﻿using CliWrap;
using Siemens.PanelManager.HubModel;
using System.IO;
using System.Text;

namespace Siemens.PanelManager.Monitor.Cmd
{
    static class IpTablesCmd
    {
        public static async Task<string[]> GetInputFilter(ILogger logger)
        {
            var resultList = new List<string>();
            Action<string> getInfoFunc = (message) =>
            {
                resultList.Add(message);
            };
            Action<string> getErrorFunc = (message) =>
            {
                logger.LogInformation(message);
            };

            var result = await Cli.Wrap("sudo")
                .WithArguments(new string[] {"iptables", "-S", "INPUT" })
                .WithValidation(CommandResultValidation.None)
                .WithStandardOutputPipe(PipeTarget.ToDelegate(getInfoFunc, Encoding.UTF8))
                .WithStandardErrorPipe(PipeTarget.ToDelegate(getErrorFunc, Encoding.UTF8))
                .ExecuteAsync();
            return resultList.ToArray();
        }

        public static async Task AppendRuleIntoInput(ILogger logger, IpTablesRuleInfo ruleInfo)
        {
            Action<string> getErrorFunc = (message) =>
            {
                logger.LogInformation(message);
            };

            var args = new List<string>() { "iptables", "-A", "INPUT" };
            if (!string.IsNullOrEmpty(ruleInfo.Protocol))
            {
                args.Add("-p");
                args.Add(ruleInfo.Protocol);
            }
            if (!string.IsNullOrEmpty(ruleInfo.Dport))
            {
                args.Add("--dport");
                args.Add(ruleInfo.Dport);
            }
            if (!string.IsNullOrEmpty(ruleInfo.Sport))
            {
                args.Add("--sport");
                args.Add(ruleInfo.Sport);
            }
            if (!string.IsNullOrEmpty(ruleInfo.Target))
            {
                args.Add("-j");
                args.Add(ruleInfo.Target);
            }

            await Cli.Wrap("sudo")
                .WithArguments(args.ToArray())
                .WithValidation(CommandResultValidation.None)
                .WithStandardOutputPipe(PipeTarget.ToDelegate(getErrorFunc, Encoding.UTF8))
                .WithStandardErrorPipe(PipeTarget.ToDelegate(getErrorFunc, Encoding.UTF8))
                .ExecuteAsync();
        }

        public static async Task<string> DeleteRuleForInput(ILogger logger, IpTablesRuleInfo ruleInfo)
        {
            var message = new StringBuilder();
            var args = new List<string>() { "iptables", "-D", "INPUT" };
            if (!string.IsNullOrEmpty(ruleInfo.Protocol))
            {
                args.Add("-p");
                args.Add(ruleInfo.Protocol);
            }
            if (!string.IsNullOrEmpty(ruleInfo.Dport))
            {
                args.Add("--dport");
                args.Add(ruleInfo.Dport);
            }
            if (!string.IsNullOrEmpty(ruleInfo.Sport))
            {
                args.Add("--sport");
                args.Add(ruleInfo.Sport);
            }
            if (!string.IsNullOrEmpty(ruleInfo.Target))
            {
                args.Add("-j");
                args.Add(ruleInfo.Target);
            }
            await Cli.Wrap("sudo")
                .WithArguments(args)
                .WithValidation(CommandResultValidation.None)
                .WithStandardOutputPipe(PipeTarget.ToStringBuilder(message, Encoding.UTF8))
                .WithStandardErrorPipe(PipeTarget.ToStringBuilder(message, Encoding.UTF8))
                .ExecuteAsync();

            if(message.Length > 0)
            {
                logger.LogInformation(message.ToString());
            }
            return message.ToString();
        }
    }
}
