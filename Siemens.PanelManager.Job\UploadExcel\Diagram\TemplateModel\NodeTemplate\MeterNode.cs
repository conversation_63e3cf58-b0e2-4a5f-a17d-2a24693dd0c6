﻿using Newtonsoft.Json;
using Siemens.PanelManager.Model.Topology;

namespace Siemens.PanelManager.Job.UploadExcel.Diagram.TemplateModel.NodeTemplate
{
    /// <summary>
    /// 单线图中电表元素
    /// </summary>
    internal class MeterNode : NodeData
    {
        public override NodeType NodeType => NodeType.Meter;
        public MeterNode(string? busBarName) 
        {
            TypeCode = "P";
            Name = "Meter";
            OpenStyle = "meterP";
            CloseStyle = "assets3";
            SizeHight = 30;
            SizeWidth = 30;
            Alarm = false;
            AlarmStatus = string.Empty;
            RatedPower = "0";
            RatedCurrent = "0";
            Category = "PACNodeTemplate";
            Angle = 0;
            BusBarId = busBarName;
        }

        [JsonProperty("ratedPower", NullValueHandling = NullValueHandling.Ignore)]
        public string? RatedPower { get; set; }
        [JsonProperty("ratedCurrent", NullValueHandling = NullValueHandling.Ignore)]
        public string? RatedCurrent { get; set; }
        [JsonProperty("busBarId", NullValueHandling = NullValueHandling.Ignore)]
        public string? BusBarId { get; set; }

    }
}
