{"info": {"_postman_id": "b03db951-7afd-4df9-ad07-98cb1039c378", "name": "35使用管理员账号进入panel manager资产管理中的资产列表菜单，点击左侧树形结构回路的名称查看回路状态", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "获取所有资产详情 Copy 28", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "let Areaid = pm.response.json().data[0].id//获取Areaid\r", "pm.environment.set('Areaid',Areaid)//把id保存到全局变量中\r", "\r", "let Substationid = pm.response.json().data[0].children[2].id//获取Substationid\r", "pm.environment.set('Substationid',Substationid)//把id保存到全局变量中\r", "\r", "let Panelid = pm.response.json().data[0].children[2].children[2].id//获取Panelid\r", "pm.environment.set('Panelid',Panelid)//把id保存到全局变量中\r", "\r", "let Circuitid = pm.response.json().data[0].children[2].children[2].children[2].id//获取Circuitid\r", "pm.environment.set('Circuitid',Circuitid)//把id保存到全局变量中\r", "\r", "let Deviceid = pm.response.json().data[0].children[2].children[2].children[2].children[2].id//获取Deviceid\r", "pm.environment.set('<PERSON>ce<PERSON>',<PERSON>ceid)//把id保存到全局变量中\r", "\r", "pm.test(\"获取所有资产详情\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"id\",\"assetName\",\"assetNumber\",\"level\",\"location\",\"children\",\"type\",\"model\");\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Asset", "host": ["{{baseUrl}}"], "path": ["api", "v1", "<PERSON><PERSON>"]}}, "response": []}, {"name": "获取回路测量 Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"获取回路测量\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"status\",\"Ua\",\"Uc\",\"Ub\",\"Ia\",\"Ib\",\"Ic\",\"P\",\"Q\",\"Uab\",\"Ubc\",\"Uca\",\"S\",\"cosΦ\",\"f\",\"xColumn\",\"yColumns\");\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/status/CircuitMeasurement/{{Circuitid}}", "host": ["{{baseUrl}}"], "path": ["api", "v1", "status", "CircuitMeasurement", "{{Circuitid}}"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}]}