﻿using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.WebApi.StaticContent;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class PrefixTelResult
    {
        public PrefixTelResult(SystemStaticModel[] staticModels, MessageContext messageContext)
        {
            var country = new List<PrefixTel>();
            foreach (var model in staticModels) 
            {
                country.Add(new PrefixTel(model, messageContext));
            }
            Country = country.ToArray();
        }
        public PrefixTel[] Country { get; set; }
    }

    public class PrefixTel
    {
        public PrefixTel(SystemStaticModel staticModel, MessageContext messageContext)
        {
            Name = messageContext.GetStaticModelName(staticModel);
            Code = staticModel.Code;
        }
        public string Name { get; set; }
        public string Code { get; set; }
    }
}
