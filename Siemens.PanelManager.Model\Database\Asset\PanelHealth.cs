﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Asset
{
    [SugarTable("asset_pannel_health")]
    public class PanelHealth : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "asset_id", IsNullable = false)]
        public int AssetId { get; set; }
        [SugarColumn(ColumnName = "name", IsNullable = false, Length = 256)]
        public string Name { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "code", IsNullable = false, Length = 50)]
        public string Code { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "limit", IsNullable = false, Length = 50)]
        public string Limit { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "weight", IsNullable = false)]
        public decimal Weight { get; set; }
        [SugarColumn(ColumnName = "is_system", IsNullable = false)]
        public bool IsSystem { get; set; }
        [SugarColumn(ColumnName = "value", IsNullable = false)]
        public decimal Value { get; set; }
        [SugarColumn(ColumnName = "parent_code", IsNullable = true, Length = 50)]
        public string? ParentCode { get; set; }
    }
}
