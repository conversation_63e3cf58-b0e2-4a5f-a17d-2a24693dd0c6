﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Siemens.PanelManager.HubModel
{
    public class IpInfo
    {
        [JsonProperty("ifindex")]
        public int Index { get; set; }
        [JsonProperty("ifname")]
        public string Name { get; set; }
        [JsonProperty("flags")]
        public string[] Flags { get; set; }
        [JsonProperty("mtu")]
        public int Mtu { get; set; }
        [JsonProperty("qdisc")]
        public string Disc { get; set; }
        [JsonProperty("operstate")]
        public string State { get; set; }
        [JsonProperty("group")]
        public string Group { get; set; }
        [JsonProperty("txqlen")]
        public int Length { get; set; }
        [JsonProperty("link_type")]
        public string LinkType { get; set; }
        [JsonProperty("address")]
        public string Address { get; set; }
        [JsonProperty("broadcast")]
        public string Broadcast { get; set; }
        [JsonProperty("promiscuity")]
        public int Promiscuity { get; set; }
        [JsonProperty("min_mtu")]
        public int MinMtu { get; set; }
        [JsonProperty("max_mtu")]
        public int MaxMtu { get; set; }
        [JsonProperty("num_tx_queues")]
        public int TxQueues { get; set; }
        [JsonProperty("num_rx_queues")]
        public int RxQueues { get; set; }
        [JsonProperty("gso_max_size")]
        public int GsoMaxSize { get; set; }
        [JsonProperty("gso_max_segs")]
        public int GsoMaxSegs { get; set; }
        [JsonProperty("addr_info")]
        public AddressInfo[] AddressInfoes { get; set; }
    }

    public class AddressInfo
    {
        [JsonProperty("family")]
        public string Family { get; set; }
        [JsonProperty("local")]
        public string Local { get; set; }
        [JsonProperty("prefixlen")]
        public int PrefixLength { get; set; }
        [JsonProperty("scope")]
        public string Scope { get; set; }
        [JsonProperty("label")]
        public string Label { get; set; }
        [JsonProperty("valid_life_time")]
        public long ValidTime { get; set; }
        [JsonProperty("preferred_life_time")]
        public long PreferredTime { get; set; }
    }
}
