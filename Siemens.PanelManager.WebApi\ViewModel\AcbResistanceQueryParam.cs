﻿using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class AcbResistanceQueryParam
    {
        [FromQuery(Name = "circuitId")]
        public int CircuitId { get; set; }
        /// <summary>
        /// Day:0,Month:1,Year:2,Custom:3
        /// </summary>
        [FromQuery(Name = "dateType")]
        public AcbChartDateType DateType { get; set; }

        [FromQuery(Name = "chartType")]
        public AcbChartType ChartType { get; set; }

        [FromQuery(Name = "startDate")]
        public string? StartDate { get; set; }

        [FromQuery(Name = "endDate")]
        public string? EndDate { get; set; }
    }

    public enum AcbChartDateType
    {
        [Description("日")]
        Day,
        [Description("月")]
        Month,
        [Description("年")]
        Year,
        [Description("自定义")]
        Custom,
    }

    public enum AcbChartType
    {
        [Description("平均值")]
        Avg,
        [Description("实时值")]
        Real,
    }
}
