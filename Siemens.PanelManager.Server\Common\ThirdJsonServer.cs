﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.UDC;
using SqlSugar;

namespace Siemens.PanelManager.Server.Common
{
    /// <summary>
    /// 第三方Json解析服务
    /// </summary>
    public class ThirdJsonServer
    {
        private readonly SqlSugarScope _db;

        private readonly ILogger _log;

        /// <summary>
        ///  构造函数初始化
        /// </summary>
        /// <param name="provider"></param>
        /// <param name="log"></param>
        public ThirdJsonServer(IServiceProvider provider, ILogger<ThirdJsonServer> log)
        {
            _db = provider.GetService<SqlSugarScope>()!;
            _log = log;
        }

        /// <summary>
        ///  从json数据中获取点位信息
        /// </summary>
        /// <returns></returns>
        public async Task<List<AssetInfoByThirdDto>> GetAllDataPoints()
        {
            var tempData = new List<AssetInfoByThirdDto>();

            try
            {
                var assetInfos = await _db.Queryable<AssetInfo>()
                   .LeftJoin<ThirdModelConfig>((t1, t2) => t1.ThirdPartCode == t2.Code)
                   .Select((t1, t2) => new
                   {
                       AssetId = t1.Id,
                       t2.Code
                   }).ToListAsync();

                if (assetInfos != null && assetInfos.Any())
                {
                    // 获取第三方json
                    var thirdConfigs = await _db.Queryable<ThirdModelConfig>()
                                        .Where(p => assetInfos.Select(t => t.Code)
                                        .Distinct().Contains(p.Code))
                                        .Select(p => new
                                        {
                                            p.Code,
                                            p.JsonData
                                        }).ToListAsync();

                    if (thirdConfigs != null && thirdConfigs.Any())
                    {
                        foreach (var config in thirdConfigs)
                        {
                            var exEntity = new AssetInfoByThirdDto();

                            var assetIds = assetInfos.Where(p => p.Code == config.Code).Select(p => p.AssetId).ToList();

                            // 集合资产id
                            exEntity.AssetIds = assetIds;

                            // 获取二级制点位信息
                            var universalDeviceConfigs = await _db.Queryable<UniversalDeviceConfig>()
                                                         .Where(p => assetIds.Contains(p.AssetId)).ToListAsync();

                            var jsonData = JsonConvert.DeserializeObject<ThreePartModelTemplate>(config.JsonData ?? "");

                            if (jsonData != null && jsonData.Treeview != null && jsonData.Treeview.Any())
                            {
                                foreach (var item in jsonData.Treeview)
                                {
                                    //获取子集集合
                                    if (item.SubGroups != null && item.SubGroups.Any())
                                    {
                                        foreach (var _item in item.SubGroups)
                                        {
                                            if (_item.Properties != null && _item.Properties.Any())
                                            {
                                                foreach (var secondItem in _item.Properties)
                                                {
                                                    exEntity.thirdJsonDto.Add(new ThirdJsonDto()
                                                    {
                                                        Code = secondItem.PropertyName ?? "",
                                                        Name = universalDeviceConfigs.FirstOrDefault(p => p.PropertyEnName == secondItem.PropertyName)
                                                        ?.PropertyCnName ?? secondItem.DescriptionInEnglish ?? "",
                                                        UdcCode = secondItem.PropertyName ?? "",
                                                        Unit = secondItem.Unit ?? "",
                                                        GroupName = "Measurement",
                                                        ParentName = "Measurement"
                                                    });
                                                }
                                            }
                                        }
                                    }

                                    if (item.Properties != null && item.Properties.Any())
                                    {
                                        foreach (var _item in item.Properties)
                                        {
                                            exEntity.thirdJsonDto.Add(new ThirdJsonDto()
                                            {
                                                Code = _item.PropertyName ?? "",
                                                Name = universalDeviceConfigs.FirstOrDefault(p => p.PropertyEnName == _item.PropertyName)
                                                       ?.PropertyCnName ?? _item.DescriptionInEnglish ?? "",
                                                UdcCode = _item.PropertyName ?? "",
                                                Unit = _item.Unit ?? "",
                                                GroupName = "Measurement",
                                                ParentName = "Measurement"
                                            });
                                        }
                                    }
                                }
                            }

                            tempData.Add(exEntity);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _log.LogError("ThirdJsonServer_GetAllDataPoints:" + ex.Message);
            }

            return tempData;
        }
    }

    public class AssetInfoByThirdDto
    {
        /// <summary>
        /// 资产id集合
        /// </summary>
        public List<int> AssetIds { get; set; } = new List<int>();

        /// <summary>
        /// Json属性集合
        /// </summary>
        public List<ThirdJsonDto> thirdJsonDto { get; set; } = new List<ThirdJsonDto>();
    }

    /// <summary>
    /// 第三方json属性
    /// </summary>
    public class ThirdJsonDto
    {
        /// <summary>
        /// Code
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// udcCode
        /// </summary>
        public string? UdcCode { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? Unit { get; set; }

        /// <summary>
        /// 分组名称
        /// </summary>
        public string? GroupName { get; set; }

        /// <summary>
        /// 父级名称
        /// </summary>
        public string? ParentName { get; set; }

    }
}
