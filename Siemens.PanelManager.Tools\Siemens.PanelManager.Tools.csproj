﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWPF>true</UseWPF>
    <Platforms>AnyCPU;x64</Platforms>
    <ApplicationIcon>sie-favicon_intranet.ico</ApplicationIcon>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="sie-favicon_intranet.ico" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Siemens.PanelManager.HubModel\Siemens.PanelManager.HubModel.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="UI\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="log4net" Version="2.0.15" />
    <PackageReference Include="Microsoft.Web.WebView2" Version="1.0.1722.45" />
    <PackageReference Include="TouchSocket" Version="1.3.0" />
  </ItemGroup>

  <ItemGroup>
    <None Update="log4net.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="settings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="sie-favicon_intranet.ico">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
