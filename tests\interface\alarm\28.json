{"info": {"_postman_id": "ca0e9740-9aad-408c-8b3d-0377b5436179", "name": "28使用管理员账号进入panel manager告警管理中的告警配置菜单，点击操作字段下的编辑按钮编辑规则", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 13", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "获取全部告警规则 Copy 2", "event": [{"listen": "test", "script": {"exec": ["let ID1 = pm.response.json().items[0].id\r", "pm.environment.set(\"ID1\", ID1);\r", "console.log(ID1)"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/AlarmRule?page=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["api", "v1", "AlarmRule"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}]}}, "response": []}, {"name": "更新告警规则Copy Copy 3", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n            \"id\": {{ID1}},\r\n            \"name\": \"QWEQW1\",\r\n            \"detail\": \"threshold range\",\r\n            \"type\": \"\",\r\n            \"model\": \"\",\r\n            \"targetValue\": \"断路器\",\r\n            \"rule\": \"电压Ua > 12  \",\r\n            \"level\": 0,\r\n            \"targetType\": \"Device\",\r\n            \"isEnable\": false,\r\n            \"sections\": [\r\n                {\r\n                    \"point\": \"电压Ua\",\r\n                    \"compare\": \">\",\r\n                    \"dataValue\": \"12\",\r\n                    \"andOr\": \"\"\r\n                }\r\n            ]\r\n        }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/AlarmRule/{{ID1}}", "host": ["{{baseUrl}}"], "path": ["api", "v1", "AlarmRule", "{{ID1}}"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 1000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", "pm.test(\"code码为20000\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData[\"code\"]).to.eql(20000);", "});", ""]}}]}