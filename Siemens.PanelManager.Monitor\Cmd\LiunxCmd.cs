﻿using CliWrap;
using Newtonsoft.Json;
using Siemens.PanelManager.HubModel;
using System;
using System.Text;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.Monitor.Cmd
{
    static class LiunxCmd
    {
        public static async Task<MonitorModel> Top(ILogger logger)
        { 
            var message = new StringBuilder();
            var error = new StringBuilder();
            var result = await Cli.Wrap("top")
                .WithArguments(new string[] {"-i", "-n", "1", "-b" })
                .WithValidation(CommandResultValidation.None)
                .WithStandardOutputPipe(PipeTarget.ToStringBuilder(message, Encoding.UTF8))
                .WithStandardErrorPipe(PipeTarget.ToStringBuilder(error, Encoding.UTF8))
                .ExecuteAsync();

            logger.LogDebug($"Cmd top run {result.RunTime}");

            var monitor = new MonitorModel();
            monitor.LogTime = DateTime.Now;
            using(var stringStream = new StringReader(message.ToString())) 
            {
                var next = true;
                do
                {
                    var str = stringStream.ReadLine();
                    if (!string.IsNullOrEmpty(str))
                    {
                        #region 获取运行时间
                        var index = str.IndexOf(" up ");
                        if (index >= 0)
                        {
                            var timeSpan = new TimeSpan();
                            var decollator = str.IndexOf(',', index);
                            var data = str.Substring(index + 4, decollator - index - 4);
                            var match = Regex.Match(data, "^([\\d]+) days$");
                            if (match.Success)
                            {
                                var days = int.Parse(match.Groups[1].Value);
                                timeSpan.Add(TimeSpan.FromDays(days));
                                int lastIndex = decollator + 1;
                                decollator = str.IndexOf(',', lastIndex);
                                data = str.Substring(lastIndex, decollator - lastIndex);
                            }
                            match = Regex.Match(data, "^([\\d]+) min$");
                            if (match.Success)
                            {
                                var mins = int.Parse(match.Groups[1].Value);
                                timeSpan.Add(TimeSpan.FromMinutes(mins));
                            }
                            else
                            {
                                match = Regex.Match(data, "([\\d]{1,2}):([\\d]{1,2})");
                                if (match.Success)
                                {
                                    var hours = int.Parse(match.Groups[1].Value);
                                    timeSpan.Add(TimeSpan.FromHours(hours));
                                    var mins = int.Parse(match.Groups[2].Value);
                                    timeSpan.Add(TimeSpan.FromMinutes(mins));
                                }
                            }

                            monitor.RunTime = (long)timeSpan.TotalMinutes;
                            continue;
                        }
                        #endregion

                        #region 获取CPU使用率
                        index = str.IndexOf("%Cpu(s):");
                        if (index >= 0)
                        {
                            var match = Regex.Match(str, "([\\d|.]+) us,");

                            if (match.Success)
                            {
                                var cpuUse = decimal.Parse(match.Groups[1].Value);
                                monitor.CPU = cpuUse;
                            }

                            continue;
                        }
                        #endregion

                        #region 获取内存使用率
                        index = str.IndexOf("Mem :");
                        if (index >= 0)
                        {
                            var unit = str.Substring(0, index);
                            if (!string.IsNullOrEmpty(unit))
                            {
                                monitor.MemUnit = unit.Trim();
                            }
                            var match = Regex.Match(str, " ([\\d|.]+) total,");

                            if (match.Success)
                            {
                                var total = decimal.Parse(match.Groups[1].Value);
                                monitor.MemTotal = total;
                            }

                            match = Regex.Match(str, " ([\\d|.]+) used,");

                            if (match.Success)
                            {
                                var used = decimal.Parse(match.Groups[1].Value);
                                monitor.MemUsed = used;
                            }
                            continue;
                        }
                        #endregion

                    }
                    else
                    {
                        next = false;
                    }
                } while (next);
            }
            return monitor;
        }

        public static void Cat() 
        {

        }

        public static async Task<string[]> GetUsbInfo(ILogger logger)
        {
            var usbInfoes = new List<string>();
            Action<string> getInfoFunc = (msg)=>usbInfoes.Add(msg);
            Action<string> getErrorFunc = (message) =>
            {
                logger.LogInformation(message);
            };
            var result = await Cli.Wrap("lsusb")
                .WithValidation(CommandResultValidation.None)
                .WithStandardOutputPipe(PipeTarget.ToDelegate(getInfoFunc, Encoding.UTF8))
                .WithStandardErrorPipe(PipeTarget.ToDelegate(getErrorFunc, Encoding.UTF8))
                .ExecuteAsync();
            return usbInfoes.ToArray();
        }

        public static async Task<IpInfo[]?> GetIpInfo(ILogger logger)
        {
            var resultStringBuilder = new StringBuilder();
            Action<string> getErrorFunc = (message) =>
            {
                logger.LogInformation(message);
            };
            var result = await Cli.Wrap("ip")
                .WithValidation(CommandResultValidation.None)
                .WithArguments(new string[] { "-d", "-j", "address" })
                .WithStandardOutputPipe(PipeTarget.ToStringBuilder(resultStringBuilder, Encoding.UTF8))
                .WithStandardErrorPipe(PipeTarget.ToDelegate(getErrorFunc, Encoding.UTF8))
                .ExecuteAsync();

            return JsonConvert.DeserializeObject<IpInfo[]>(resultStringBuilder.ToString());
        }
    }
}
