﻿using Siemens.PanelManager.HubModel;
using Siemens.PanelManager.Monitor.Cmd;

namespace Siemens.PanelManager.Monitor.Function
{
    static class MonitorFunc
    {
        public static async Task<MonitorModel> GetMonitorModel(ILogger logger)
        {
            var monitorModel = await LiunxCmd.Top(logger);
            var statsList = await DockerCmd.Stats(logger);
            monitorModel.DockerProcessStats = statsList;
            return monitorModel;
        }
    }
}
