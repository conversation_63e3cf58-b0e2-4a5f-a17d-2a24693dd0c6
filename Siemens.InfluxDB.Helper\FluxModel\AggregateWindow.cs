﻿using Siemens.InfluxDB.Helper.Enum;
using System.Text;

namespace Siemens.InfluxDB.Helper.FluxModel
{
    internal class AggregateWindow
    {
        public uint IntervalNum { get; set; } = 1;
        public TimeInterval IntervalLevel { get; set; } = TimeInterval.Minute;
        public string FunctionName { get; set; } = "mean";
        /// <summary>
        /// TODO 保证返回的数据数量是一致的
        /// </summary>
        public bool CreateEmpty { get; private set; } = true;

        public void AppendFlux(StringBuilder flux, 
            Limit? limit, 
            Sort? sort,
            FirstModel? first,
            LastModel? last)
        {
            var everyStr = string.Empty;
            switch (IntervalLevel)
            {
                case TimeInterval.Second:
                    everyStr = $"{IntervalNum}s";
                    break;
                case TimeInterval.Minute:
                    everyStr = $"{IntervalNum}m";
                    break;
                case TimeInterval.Hour:
                    everyStr = $"{IntervalNum}h";
                    break;
                case TimeInterval.Day:
                    everyStr = $"{IntervalNum}d";
                    break;
                case TimeInterval.Month:
                    everyStr = $"{IntervalNum}mo";
                    break;
                case TimeInterval.Year:
                    everyStr = $"{IntervalNum}y";
                    break;
                default:break;
            }

            flux.AppendLine($"|> aggregateWindow(every :{everyStr}, fn:{FunctionName}, createEmpty:{(CreateEmpty ? "true" : "false")})");

            if (sort != null)
            {
                sort.AppendFlux(flux);
            }

            if (limit != null)
            {
                limit.AppendFlux(flux);
            }

            if (first != null)
            {
                first.AppendFlux(flux);
            }

            if (last != null)
            {
                last.AppendFlux(flux);
            }

            flux.AppendLine($"|> yield(name: \"{FunctionName}\")");
        }
    }

    
}
