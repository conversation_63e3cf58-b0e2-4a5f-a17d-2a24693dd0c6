﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    /// <summary>
    /// 定位类型选择
    /// </summary>
    public class UserCheckPointResult
    {
        /// <summary>
        /// 父级中文名称
        /// </summary>
        public string? CnParentName { get; set; }

        /// <summary>
        /// 父级英文名称
        /// </summary>
        public string? EnParentName { get; set; }

        /// <summary>
        /// 子集集合
        /// </summary>
        public List<UserCheckPointEntity>? UserCheckPoints { get; set; } = new List<UserCheckPointEntity>();

    }

    public class UserCheckPointEntity
    {   
        /// <summary>
        /// 主键id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 点位英文名称
        /// </summary>
        public string? PropertyEnName { get; set; }

        /// <summary>
        /// 点位中文名称
        /// </summary>
        public string? PropertyCnName { get; set; }

        /// <summary>
        /// 定位对应的值
        /// </summary>
        public string? Value { get; set; }

        /// <summary>
        /// 点位单位
        /// </summary>
        public string? Unit { get; set; }

        /// <summary>
        /// 是否二级制
        /// </summary>
        public bool IsBit { get; set; } = false;

        /// <summary>
        /// 二进制对应的值
        /// </summary>
        public Dictionary<string, BitDataDto>? BitDatas { get; set; } = new Dictionary<string, BitDataDto>();
    }

    public class BitDataDto
    {
        /// <summary>
        /// 点位名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 点位标识
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 定位对应的值
        /// </summary>
        public string? Value { get; set; }
    }

    // json点位返回数据
    public class UserCheckPointDto
    {
        /// <summary>
        /// 定位类型
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 子集
        /// </summary>
        public PvssTypeDto? PvssType { get; set; }
    }

    public class PvssTypeDto
    {
        /// <summary>
        /// 属性集合
        /// </summary>
        [JsonProperty("DPES")]
        public List<DpesDto>? Dpes { get; set; }
    }

    //属性
    public class DpesDto
    {
        /// <summary>
        /// 名字
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        public DataType? PvssType { get; set; }

        /// <summary>
        /// 点位描述
        /// </summary>
        public List<Description>? Description { get; set; }

        /// <summary>
        /// 常用属性
        /// </summary>
        public GmsType? GmsType { get; set; }
    }

    /// <summary>
    /// 中英文描述
    /// </summary>
    public class Description
    {
        /// <summary>
        /// 类型描述
        /// </summary>
        public string? Culture { get; set; }

        /// <summary>
        /// 中文注释
        /// </summary>
        public string? Text { get; set; }
    }

    /// <summary>
    /// 数据类型
    /// </summary>
    public class DataType
    {
        public string? PvssType { get; set; }
    }

    /// <summary>
    /// GmsType
    /// </summary>
    public class GmsType
    {
        public Attributes? Attributes { get; set; }
    }

    public class Attributes
    {
        public bool? Valid { get; set; }

        public UnitText? UnitText { get; set; }

        public Properties? Properties { get; set; }
    }

    public class UnitText
    {
        public string? Unit { get; set; }

        public string? TextGroup { get; set; }
    }

    public class Properties
    {
        /// <summary>
        /// 最小值
        /// </summary>
        public double Min { get; set; }

        /// <summary>
        /// 最大值
        /// </summary>
        public double Max { get; set; }

        /// <summary>
        /// 当前值
        /// </summary>
        public double Res { get; set; }
    }
}
