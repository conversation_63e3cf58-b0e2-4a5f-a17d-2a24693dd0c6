{"info": {"_postman_id": "e4f8b227-7a29-4b5e-a3f0-89a260bab282", "name": "05.2使用超级管理员账号进入panel manager组态编辑中的图形编辑页面，点击创建新单线图输入51位字符图纸code", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "添加拓扑图 Copy 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为40301\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(40301);\r", "});\r", "\r", "pm.test(\"无效的图纸码\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"无效的图纸码\");\r", "});\r", "\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "        {\r\n            \"code\": \"hkn12hkn12hkn12hkn12hkn12hkn12hkn12hkn12hkn12hkn121\",\r\n            \"discription\": \"\",\r\n            \"name\": \"test03\",\r\n            \"owner\": \"hkn\",\r\n            \"time\": \"2023-02-16 15:15:43\",\r\n            \"topology\": {\r\n                \"class\": \"GraphLinksModel\",\r\n                \"copiesKey\": false,\r\n                \"linkKeyProperty\": \"key\",\r\n                \"nodeDataArray\": [\r\n                    {\r\n                        \"type\": \"L1\",\r\n                        \"name\": \"Busbar\",\r\n                        \"geo\": \"busBar\",\r\n                        \"size\": \"70 70\",\r\n                        \"category\": \"busLineNodeTemplate\",\r\n                        \"key\": -1,\r\n                        \"location\": \"0 0\",\r\n                        \"isSelected\": false\r\n                    },\r\n                    {\r\n                        \"key\": 0,\r\n                        \"index\": 0,\r\n                        \"type\": \"A\",\r\n                        \"name\": \"breaker\",\r\n                        \"geo\": \"circle\",\r\n                        \"size\": \"8 8\",\r\n                        \"loc\": \"-1100 -302.1999971354169\",\r\n                        \"flag\": \"busPoint\",\r\n                        \"figure\": \"Ellipse\",\r\n                        \"fill\": \"black\",\r\n                        \"isSelected\": false,\r\n                        \"category\": \"breakerTemplate\"\r\n                    },\r\n                    {\r\n                        \"key\": 1,\r\n                        \"index\": 0,\r\n                        \"type\": \"A\",\r\n                        \"name\": \"breaker\",\r\n                        \"geo\": \"circle\",\r\n                        \"size\": \"8 8\",\r\n                        \"loc\": \"500 -302.1999971354169\",\r\n                        \"flag\": \"busPoint\",\r\n                        \"figure\": \"Ellipse\",\r\n                        \"fill\": \"black\",\r\n                        \"isSelected\": false,\r\n                        \"category\": \"breakerTemplate\"\r\n                    },\r\n                    {\r\n                        \"type\": \"S\",\r\n                        \"name\": \"电源\",\r\n                        \"geo2\": \"statusLightA\",\r\n                        \"geo\": \"statusLightA\",\r\n                        \"size\": \"40 40\",\r\n                        \"sourceType\": \"transformer\",\r\n                        \"category\": \"sourceNodeTemplate\",\r\n                        \"key\": -4,\r\n                        \"location\": \"0 0\",\r\n                        \"isSelected\": false,\r\n                        \"loc\": \"-1100 50\"\r\n                    },\r\n                    {\r\n                        \"type\": \"P\",\r\n                        \"name\": \"表\",\r\n                        \"geo2\": \"meterP\",\r\n                        \"assetId\": \"0\",\r\n                        \"assetName\": \"\",\r\n                        \"subname\": \"1LP1\",\r\n                        \"geo\": \"assets3\",\r\n                        \"ratedPower\": \"\",\r\n                        \"ratedCurrent\": \"\",\r\n                        \"alarm\": false,\r\n                        \"alarmStatus\": \"\",\r\n                        \"size\": \"30 30\",\r\n                        \"angle\": 0,\r\n                        \"category\": \"PACNodeTemplate\",\r\n                        \"key\": -5,\r\n                        \"location\": \"0 198.5\",\r\n                        \"isSelected\": false,\r\n                        \"loc\": \"-1100 -50\"\r\n                    },\r\n                    {\r\n                        \"type\": \"B\",\r\n                        \"name\": \"断路器\",\r\n                        \"geo2\": \"swich25\",\r\n                        \"model\": \"\",\r\n                        \"assetId\": \"0\",\r\n                        \"modelId\": 1,\r\n                        \"assetName\": \"\",\r\n                        \"geo\": \"swich25\",\r\n                        \"size\": \"90 90\",\r\n                        \"alarm\": false,\r\n                        \"alarmStatus\": \"\",\r\n                        \"swichStatus\": false,\r\n                        \"colorStatus\": false,\r\n                        \"angle\": 270,\r\n                        \"category\": \"swichNodeTemplate\",\r\n                        \"key\": -3,\r\n                        \"location\": \"0 132.5\",\r\n                        \"isSelected\": false,\r\n                        \"loc\": \"-1100 -200\"\r\n                    }\r\n                ],\r\n                \"linkDataArray\": [\r\n                    {\r\n                        \"from\": 0,\r\n                        \"to\": 1,\r\n                        \"key\": 0,\r\n                        \"line_type_id\": 1,\r\n                        \"length\": 0,\r\n                        \"selectable\": false,\r\n                        \"standard\": \"125mm*8mm的铜排\",\r\n                        \"resistance_per_meter\": \"0.0000116660\",\r\n                        \"reactance_per_meter\": \"0.0001575\",\r\n                        \"color\": \"#000\",\r\n                        \"category\": \"busBarlink\",\r\n                        \"points\": [\r\n                            -1096,\r\n                            -302.1999971354169,\r\n                            496,\r\n                            -302.1999971354169\r\n                        ]\r\n                    },\r\n                    {\r\n                        \"from\": -4,\r\n                        \"to\": -5,\r\n                        \"line_type_id\": 1,\r\n                        \"length\": 0,\r\n                        \"standard\": \"125mm*8mm的铜排\",\r\n                        \"resistance_per_meter\": \"0.0000116660\",\r\n                        \"reactance_per_meter\": \"0.0001575\",\r\n                        \"current\": \"0.0\",\r\n                        \"lineloss\": \"0.0\",\r\n                        \"aging\": \"0.0\",\r\n                        \"warning\": \"0\",\r\n                        \"color\": \"#000\",\r\n                        \"points\": [\r\n                            {\r\n                                \"class\": \"go.Point\",\r\n                                \"x\": -1100,\r\n                                \"y\": 29.5\r\n                            },\r\n                            {\r\n                                \"class\": \"go.Point\",\r\n                                \"x\": -1100,\r\n                                \"y\": -5.763755035400408\r\n                            }\r\n                        ],\r\n                        \"key\": -2\r\n                    },\r\n                    {\r\n                        \"from\": -3,\r\n                        \"to\": -5,\r\n                        \"line_type_id\": 1,\r\n                        \"length\": 0,\r\n                        \"standard\": \"125mm*8mm的铜排\",\r\n                        \"resistance_per_meter\": \"0.0000116660\",\r\n                        \"reactance_per_meter\": \"0.0001575\",\r\n                        \"current\": \"0.0\",\r\n                        \"lineloss\": \"0.0\",\r\n                        \"aging\": \"0.0\",\r\n                        \"warning\": \"0\",\r\n                        \"color\": \"#000\",\r\n                        \"points\": [\r\n                            {\r\n                                \"class\": \"go.Point\",\r\n                                \"x\": -1100,\r\n                                \"y\": -160\r\n                            },\r\n                            {\r\n                                \"class\": \"go.Point\",\r\n                                \"x\": -1100,\r\n                                \"y\": -94.23624496459962\r\n                            }\r\n                        ],\r\n                        \"key\": -3\r\n                    },\r\n                    {\r\n                        \"from\": -3,\r\n                        \"to\": 0,\r\n                        \"line_type_id\": 1,\r\n                        \"length\": 0,\r\n                        \"standard\": \"125mm*8mm的铜排\",\r\n                        \"resistance_per_meter\": \"0.0000116660\",\r\n                        \"reactance_per_meter\": \"0.0001575\",\r\n                        \"current\": \"0.0\",\r\n                        \"lineloss\": \"0.0\",\r\n                        \"aging\": \"0.0\",\r\n                        \"warning\": \"0\",\r\n                        \"color\": \"#000\",\r\n                        \"points\": [\r\n                            {\r\n                                \"class\": \"go.Point\",\r\n                                \"x\": -1100,\r\n                                \"y\": -240\r\n                            },\r\n                            {\r\n                                \"class\": \"go.Point\",\r\n                                \"x\": -1100,\r\n                                \"y\": -298.1999971354169\r\n                            }\r\n                        ],\r\n                        \"key\": -4\r\n                    }\r\n                ]\r\n            }\r\n        }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/sld", "host": ["{{baseUrl}}"], "path": ["api", "v1", "sld"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 1000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});"]}}]}