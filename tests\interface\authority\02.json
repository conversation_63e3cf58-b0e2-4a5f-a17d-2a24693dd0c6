{"info": {"_postman_id": "d7541f07-b02e-4aa3-9af6-be66ba0c19a6", "name": "进入panel manager首页，输入正确的账户错误的密码登入panel manager", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "用户登录(失败)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"账号名或密码错误\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"message\"]).to.eql(\"账号名或密码错误\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"hkn666@\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/login", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "login"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}], "variable": [{"key": "usernameindex", "value": 184, "type": "string"}, {"key": "username", "value": "user-184", "type": "string"}, {"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1lIjoiaGtuNzc3IiwiaHR0cDovL3NjaGVtYXMueG1sc29hcC5vcmcvd3MvMjAwNS8wNS9pZGVudGl0eS9jbGFpbXMvbmFtZWlkZW50aWZpZXIiOiI2YWRhNWU5Zi02MzhmLTQxNzgtOGZhOS1kM2E2MDZjZmUwNjgiLCJTeW5jRGV2aWNlIjoiW10iLCJuYmYiOjE2NzcxMzgzOTUsImV4cCI6MTY3NzEzODM5NiwiaXNzIjoiU2llbWVuc0lzc3VlciIsImF1ZCI6IldlYkFwcEF1ZGllbmNlIn0.4KZ3IlrxAwd9LtF63J1Sjl2KkHbqGVuCrhfj7K6-Ljg", "type": "string"}, {"key": "userId", "value": 103, "type": "string"}, {"key": "user2Id", "value": 99, "type": "string"}, {"key": "user3Id", "value": 95, "type": "string"}, {"key": "roleId", "value": 1, "type": "string"}, {"key": "BASE_URL", "value": "http://localhost:5000"}]}