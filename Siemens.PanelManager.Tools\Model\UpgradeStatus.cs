﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Tools.Model
{
    internal class UpgradeStatus
    {
        public string[] Steps { get; set; } = new string[0];
        /// <summary>
        /// 0 默认（未进行）
        /// 1 正在升级中
        /// 2 升级完成
        /// 99 升级失败
        /// </summary>
        public int Status { get; set; }

        private Dictionary<int, string> _stepStatus = new Dictionary<int, string>();
        public void InitStepStatus()
        {
            for(var i = 1; i<= Steps.Length; i++) 
            {
                _stepStatus.TryAdd(i, string.Empty);
            }
        }

        public string GetStepStatus(int stepId)
        {
            if (_stepStatus.ContainsKey(stepId))
            {
                return _stepStatus[stepId];
            }
            return string.Empty;
        }

        public void SetStepStatus(int stepId, string status)
        {
            if (_stepStatus.Keys.Count == 0)
            {
                InitStepStatus();
            }

            if (_stepStatus.ContainsKey(stepId))
            {
                _stepStatus[stepId] = status;
            }
        }

        public void ClearStepStatus()
        {
            for(var i = 1; i <= Steps.Length; i++) 
            {
                if (_stepStatus.ContainsKey(i))
                {
                    _stepStatus[i] = string.Empty;
                }
                else
                {
                    _stepStatus.Add(i, string.Empty);
                }
            }
        }

        public IReadOnlyDictionary<int, string> CurrentStatus
        {
            get
            {
                return _stepStatus;
            }
        }

        public UpgradeStatus GetNewUpgradeStatus() 
        {
            return new UpgradeStatus()
            {
                Steps = Steps,
            };
        }
    }
}
