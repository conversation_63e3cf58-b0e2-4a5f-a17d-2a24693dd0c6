﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Tools.Model
{
    internal class MonitorCurrentStatus
    {
        [JsonProperty(PropertyName = "CPU")]
        public string CPU { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "mem")]
        public string Memory { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "runTime")]
        public string RunTime { get; set; } = string.Empty;
    }
}
