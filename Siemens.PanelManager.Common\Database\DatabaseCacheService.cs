﻿using Siemens.PanelManager.Common.Cache;
using SqlSugar;

namespace Siemens.PanelManager.Common.Database
{
    internal class DatabaseCacheService : ICacheService
    {
        public void Add<V>(string key, V value)
        {
            if (value == null) return;
            CacheHelper.Set<V>(key, value);
        }

        public void Add<V>(string key, V value, int cacheDurationInSeconds)
        {
            CacheHelper.Set<V>(key, value, TimeSpan.FromSeconds(cacheDurationInSeconds));
        }

        public bool ContainsKey<V>(string key)
        {
            return CacheHelper.ContainsKey(key);
        }

        public V? Get<V>(string key)
        {
            return CacheHelper.Get<V>(key);
        }

        public IEnumerable<string> GetAllKey<V>()
        {
            return CacheHelper.GetSimpleKeys();
        }

        public V GetOrCreate<V>(string cacheKey, Func<V> create, int cacheDurationInSeconds = int.MaxValue)
        {
            if (cacheDurationInSeconds == int.MaxValue)
            {
                return CacheHelper.GetOrCreate(cacheKey, create, true);
            }
            return CacheHelper.GetOrCreate(cacheKey, create, true, TimeSpan.FromSeconds(cacheDurationInSeconds));
        }

        public void Remove<V>(string key)
        {
            CacheHelper.Remove(key);
        }
    }
}
