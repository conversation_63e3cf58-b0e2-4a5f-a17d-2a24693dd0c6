﻿using Microsoft.AspNetCore.SignalR;
using Siemens.PanelManager.HubModel;
using Siemens.PanelManager.HubModel.Client;
using Siemens.PanelManager.Monitor.Function;
using Siemens.PanelManager.Monitor.StaticData;
using Siemens.PanelManager.Monitor.Workers;
using System;
using System.Collections.Generic;
using System.Data;
using System.Security.Cryptography;
using System.Text;

namespace Siemens.PanelManager.Monitor.Hubs
{
    public class MonitorHub : Hub
    {
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        private const string PUBLICKEY = "-----BEGIN RSA PUBLIC KEY-----\r\nMIIBCgKCAQEAvAHhQHAGn4BpKaA+BLjfkk+n+Oc/3pz7DfFBgvoyzy44J+m2AHf+\r\nKbW91tF9uyBEOJSx7Ua09g0aPKZkIiHQsHxhI6GnaChhniYi/rZl796PqnIsEk4H\r\nmP9QlxBaCD1Nje74iPynovr9HWWz++fhatybSDIQiE9F8MgSATWXqMqRbXJynM/d\r\nV0Ph+fYAHJAgT8rjRX/flHXs6SK64s69jNuk1pFQwW2Ldcb7q5t030+vKS4phCCA\r\nXppNEpK0OW17h8yYBEc0xuJzJtTVZz+LVCd4ZE0NRGy0C1ed/tjVf0cFTGz4R5KI\r\nv41vCVJqlUJohuGFpHYV7n5EjV+R/ddUEwIDAQAB\r\n-----END RSA PUBLIC KEY-----";
        private ILogger<MonitorHub> _logger;
        private IServiceProvider _provider;

        public MonitorHub(ILogger<MonitorHub> logger, IServiceProvider provider)
        {
            _logger = logger;
            _provider = provider;
        }

        public override Task OnConnectedAsync()
        {
            ConnectManager.UnknowConnect.Queue.Enqueue(Context);
            return base.OnConnectedAsync();
        }

        public override Task OnDisconnectedAsync(Exception? exception)
        {
            ConnectManager.ConnectedList.TryRemove(Context.ConnectionId, out _);
            _logger.LogDebug($"Disconnect {Context.ConnectionId}");
            return base.OnDisconnectedAsync(exception);
        }

        public bool Login(string code, string randronCode)
        {
            try
            {
                RSACryptoServiceProvider p = new RSACryptoServiceProvider();
                p.ImportFromPem(new ReadOnlySpan<char>(PRIVATEKEY.ToArray()));

                var decryptData = Encoding.UTF8.GetString(p.Decrypt(Convert.FromBase64String(code), false));
                var result = false;
                if (!string.IsNullOrEmpty(decryptData))
                {
                    var codes = decryptData.Split('|');

                    if (codes.Length > 3)
                    {
                        result = true;
                        if (!randronCode.Equals(codes[3]))
                        {
                            result = false;
                        }
                        if (!DateTime.TryParseExact(codes[2], "yyyyMMddHHmmss", null, System.Globalization.DateTimeStyles.None, out DateTime tokenTime)
                            || Math.Abs((int)(DateTime.Now - tokenTime).TotalMinutes) > 15m)
                        {
                            result = false;
                        }

                        if (result)
                        {
                            Context.Items.TryAdd("UseName", codes[0]);
                            Context.Items.TryAdd("MonitorHasLogin", true);
                            ConnectManager.ConnectedList.TryAdd(Context.ConnectionId, Context);
                        }
                    }
                }
                return result;
            }
            catch
            {
                return false;
            }
        }

        #region Monitor
        public async Task StartMonitor()
        {
            if (Context.Items.TryGetValue("MonitorHasLogin", out object? value) && value != null && value is bool hasLogin && hasLogin)
            {
                await Groups.AddToGroupAsync(Context.ConnectionId, Constants.Group_MonitorClient);
            }
        }
        public async Task StopMonitor()
        {
            if (Context.Items.TryGetValue("MonitorHasLogin", out object? value) && value != null && value is bool hasLogin && hasLogin)
            {
                await Groups.RemoveFromGroupAsync(Context.ConnectionId, Constants.Group_MonitorClient);
            }
        }
        public async Task<MonitorModel?> GetMonitor()
        {
            if (Context.Items.TryGetValue("MonitorHasLogin", out object? value) && value != null && value is bool hasLogin && hasLogin)
            {
                try
                {
                    var model = await MonitorFunc.GetMonitorModel(_logger);
                    return model;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "获取监控信息失败");
                }
            }
            return null;
        }

        public MonitorModel[] GetMonitorHistory()
        {
            if (Context.Items.TryGetValue("MonitorHasLogin", out object? value) && value != null && value is bool hasLogin && hasLogin)
            {
                return MonitorTimeCache.GetAll();
            }
            return new MonitorModel[0];
        }
        #endregion

        #region Upgrade
        public async Task<string> BeginUpgrade()
        {
            if (Context.Items.TryGetValue("MonitorHasLogin", out object? value) && value != null && value is bool hasLogin && hasLogin)
            {
                try
                {
                    var status = _provider.GetRequiredService<UpgradeWorkerStatus>();
                    var clientProxy = Clients.Client(Context.ConnectionId);
                    var tranId = status.StartUpgrade(clientProxy);
                    if (!string.IsNullOrEmpty(tranId))
                    {
                        Context.Items.TryAdd("UpgradeTranId", tranId);
                        var worker = _provider.GetService<UpgradeWorker>();
                        if (worker != null)
                        {
                            var source = new CancellationTokenSource();
                            await worker.StartAsync(source.Token);

                            return status.JobId;
                        }
                        return string.Empty;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "BeginUpgrade Fail");
                }
            }
            return string.Empty;
        }

        public bool CancelUpgrade()
        {
            if (Context.Items.TryGetValue("MonitorHasLogin", out object? value)
                && value != null
                && value is bool hasLogin
                && hasLogin
                && Context.Items.TryGetValue("UpgradeTranId", out value)
                && value != null
                && value is string tranId)
            {
                var status = _provider.GetRequiredService<UpgradeWorkerStatus>();
                Context.Items.Remove("UpgradeTranId");
                return status.Cancel(tranId);
            }
            return false;
        }

        public string[] GetUpgradeStep() 
        {
            return new string[] 
            {
                "上传文件",
                "验证文件",
                "清理文件夹",
                "清理镜像",
                "复制到指定文件夹",
                "启动服务",
                "清除临时文件"
            };
        }
        #endregion

        #region Update Port
        /// <summary>
        /// TODO 当前端口为hard code
        /// 后端Api : 5000
        /// postgres-sql : 5432
        /// influx-db : 8086
        /// </summary>
        /// <param name="funcName"></param>
        /// <returns></returns>
        public async Task UpdatePort(string funcName)
        {
            if (Context.Items.TryGetValue("MonitorHasLogin", out object? value) && value != null && value is bool hasLogin && hasLogin)
            {
                var manager = _provider.GetRequiredService<IpTableManager>();
                funcName = funcName.ToLower();
                switch (funcName)
                {
                    case "back-api":
                        {
                            int port = 5000;
                            var ipTableRule = new IpTablesRuleInfo();
                            ipTableRule.Dport = port.ToString();
                            ipTableRule.Target = "ACCEPT";
                            ipTableRule.ChainName = "INPUT";
                            ipTableRule.Protocol = "tcp";
                            var configRule = manager.ConfigRules.FirstOrDefault(r => r.Dport == ipTableRule.Dport && r.ChainName == ipTableRule.ChainName && r.Protocol == ipTableRule.Protocol);

                            if (configRule != null) 
                            {
                                await IpTableManagerFunc.DeleteInputRule(_logger, configRule);
                            }
                            await IpTableManagerFunc.AppendInputRule(_logger, ipTableRule);
                            // 默认一个小时过期
                            manager.InputCmd(ipTableRule, DateTime.Now.AddHours(1));
                            break;
                        }
                    case "postgres-sql":
                        {
                            int port = 5432;
                            var ipTableRule = new IpTablesRuleInfo();
                            ipTableRule.Dport = port.ToString();
                            ipTableRule.Target = "ACCEPT";
                            ipTableRule.ChainName = "INPUT";
                            ipTableRule.Protocol = "tcp";
                            var configRule = manager.ConfigRules.FirstOrDefault(r => r.Dport == ipTableRule.Dport && r.ChainName == ipTableRule.ChainName && r.Protocol == ipTableRule.Protocol);

                            if (configRule != null)
                            {
                                await IpTableManagerFunc.DeleteInputRule(_logger, configRule);
                            }
                            await IpTableManagerFunc.AppendInputRule(_logger, ipTableRule);
                            // 默认一个小时过期
                            manager.InputCmd(ipTableRule, DateTime.Now.AddHours(1));
                            break;
                        }
                    case "influx-db":
                        {
                            int port = 8086;
                            var ipTableRule = new IpTablesRuleInfo();
                            ipTableRule.Dport = port.ToString();
                            ipTableRule.Target = "ACCEPT";
                            ipTableRule.ChainName = "INPUT";
                            ipTableRule.Protocol = "tcp";
                            var configRule = manager.ConfigRules.FirstOrDefault(r => r.Dport == ipTableRule.Dport && r.ChainName == ipTableRule.ChainName && r.Protocol == ipTableRule.Protocol);

                            if (configRule != null)
                            {
                                await IpTableManagerFunc.DeleteInputRule(_logger, configRule);
                            }
                            await IpTableManagerFunc.AppendInputRule(_logger, ipTableRule);
                            // 默认一个小时过期
                            manager.InputCmd(ipTableRule, DateTime.Now.AddHours(1));
                            break;
                        }
                    default: break;
                }
            }
            #endregion
        }
    }
}
