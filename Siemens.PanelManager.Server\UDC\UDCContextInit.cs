﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.Model.UDC;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Server.UDC
{
    public class UDCContextInit
    {
        private const string MainObjectID = "MainObjectID";
        private const string UDCContext = "UDCContext";
        private IServiceProvider _provider;
        private ILogger _logger;
        public UDCContextInit(IServiceProvider provider, ILogger<UDCContextInit> logger)
        {
            _provider = provider;
            _logger = logger;
        }

        public async Task<UDCContext> GetUDCContext() 
        {
            string mainObjectId = string.Empty;

            using (var sqlClient = _provider.GetRequiredService<ISqlSugarClient>())
            {
                var configItem = await sqlClient.Queryable<SystemStaticModel>().FirstAsync(s=>s.Name == MainObjectID && s.Type == UDCContext);

                if (configItem == null)
                {
                    try
                    {
                        var refObj = _provider.GetService<IUDCApiRef>();
                        if (refObj != null)
                        {
                            var items = await refObj.GetItems();
                            var item = items?.Embedded.Items.FirstOrDefault(d => d.TypeName.Contains("powercenter", StringComparison.OrdinalIgnoreCase));
                            if (item != null)
                            {
                                mainObjectId = item.Id;
                                await sqlClient.Insertable(new SystemStaticModel
                                {
                                    Code = mainObjectId,
                                    Name = MainObjectID,
                                    Type = UDCContext,
                                    CreatedBy = "System",
                                    CreatedTime = DateTime.Now,
                                    UpdatedBy = "System",
                                    UpdatedTime = DateTime.Now,
                                }).ExecuteCommandAsync();
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "获取UDCContext失败");
                    }
                }
                else
                {
                    mainObjectId = configItem.Code;
                }
            }

            var configuration = _provider.GetRequiredService<IConfiguration>();

            var context = new UDCContext
            {
                PocObjectId = mainObjectId,
                UdcApiPath = configuration.GetValue<string>("UdcApiPath") ?? string.Empty,
                UdcDataPath = configuration.GetValue<string>("UdcDataPath") ?? string.Empty,
            };

            var cache = _provider.GetRequiredService<SiemensCache>();

            if (!string.IsNullOrEmpty(mainObjectId))
            {
                cache.Set(UDCContext, context);
            }
            else
            {
                cache.Set(UDCContext, context, TimeSpan.FromSeconds(5));
            }
            return context;
        }
    }
}
