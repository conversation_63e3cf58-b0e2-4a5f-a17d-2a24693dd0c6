﻿using Akka.Actor;
using Akka.DependencyInjection;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.DependencyInjection;
using Siemens.PanelManager.DeviceDataFlow.ActorRefs;
using Siemens.PanelManager.DeviceDataFlow.DataFlowActors;
using Siemens.PanelManager.DeviceDataFlow.MessageBus;
using Siemens.PanelManager.DeviceDataFlow.StaticData;
using Siemens.PanelManager.DeviceDataFlow.UDC;
using Siemens.PanelManager.Interface.ActorRefs;
using SqlSugar.DistributedSystem.Snowflake;

namespace Siemens.PanelManager.DeviceDataFlow
{
    public class ServiceInjection : IServiceInjection
    {
        public void AddService(IServiceCollection services)
        {
            services.AddHostedService<ActorServer>();
            services.AddTransient<IAssetDataProxyRef>((p) =>
            {
                var manager = ActorManager.GetActorManagerNoException();
                if (manager == null) return null;

                return new AssetDataProxyRef(manager.AssetProxyRef, manager.DataPointRef);
            });
            services.AddTransient<IMessageReceiver>((p) =>
            {
                var actorManager = ActorManager.GetActorManagerNoException();
                if (actorManager == null)
                {
                    return null;
                }

                return actorManager.MessageReceiver;
            });
            services.AddSingleton<IMessageRegister>((p) =>
            {
                return MessageBusContext.MessageRegister;
            });
            services.AddTransient<IAssetManagerRef>((p) =>
            {
                var manager = ActorManager.GetActorManagerNoException();
                if (manager == null) return null;
                var cache = p.GetRequiredService<SiemensCache>();
                var loggerFactory = p.GetRequiredService<ILoggerFactory>();
                var logger = loggerFactory.CreateLogger<AssetManagerRef>();
                return new AssetManagerRef(manager.AssetProxyRef, cache, logger);
            });

            services.AddSingleton<IAssetChangeRegister>((p) =>
            {
                return MessageBusContext.AssetChangeRegister;
            });

            services.AddSingleton<IAlarmRef>((p) =>
            {
                return new AlarmRef(ActorManager.GetActorManager().AlarmRef, ActorManager.GetActorManagerAlways().AlarmSaveRef);
            });

            services.AddSingleton<ITopologyDataChangeRef>(p =>
            {
                var am = ActorManager.GetActorManagerAlways();
                return new TopologyDataChangeRef(am.TopologyRef, am.TopologyActionRef);
            });

            services.AddTransient<IUDCApiRef>(p =>
            {
                var actorManager = ActorManager.GetActorManagerAlways();
                return new UDCApiRef(actorManager.UDCApiRef);
            });

            services.AddSingleton<IInfluxDBRef>((p) =>
            {
                var actorManager = ActorManager.GetActorManagerAlways();
                var queue = new InfluxDBQueue();
                var props = DependencyResolver.For(actorManager.ActorSystem).Props<InfluxDBActor>(queue);
                var influxRefObj = actorManager.ActorSystem.ActorOf(props);
                influxRefObj.Tell(0);
                return new InfluxDBRef(queue);
            });
        }
    }
}
