﻿using Siemens.PanelManager.Model.Database.Asset;

namespace Siemens.PanelManager.Model.DataFlow
{
    public class AssetChangeData
    {
        public int AssetId { get; set; }
        public string AssetName { get; set; } = string.Empty;
        public string AssetType { get; set; } = string.Empty;
        public string AssetModel { get; set; } = string.Empty;
        public DateTime ChangeTime { get; set; } = DateTime.Now;
        public AssetLevel AssetLevel { get; set; }
        public string[]? ChangeDataCode { get; set; }
        public Dictionary<string, string> ChangeDatas { get; set; } = new Dictionary<string, string>();
    }
}
