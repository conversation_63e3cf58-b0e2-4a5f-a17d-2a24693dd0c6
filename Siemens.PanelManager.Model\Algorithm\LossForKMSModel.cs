﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Algorithm
{
    public class LossForKMSModel
    {
        [JsonProperty(PropertyName = "times")]
        public string[] times { get; set; } 
        [JsonProperty(PropertyName = "values")]
        public LossForKMSModelVlaues values { get; set; }

    }
    public class LossForKMSModelVlaues
    {
        [JsonProperty(PropertyName = "line")]
        public decimal[] line { get; set; }
        [JsonProperty(PropertyName = "lossP")]
        public decimal[] lossP { get; set; }
        [JsonProperty(PropertyName = "refer")]
        public decimal[] refer { get; set; }
        [JsonProperty(PropertyName = "referLow")]
        public decimal[] referLow { get; set; }
        [JsonProperty(PropertyName = "referUpper")]
        public decimal[] referUpper { get; set; }
    }
}
