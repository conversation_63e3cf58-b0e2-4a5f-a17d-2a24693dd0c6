version: "2.0"

services:
  back-dev:
    image: cr.siemens.com/si-lp-cea-rd-plm-project/edge/panel-manager/panel-manager-standard-product/server/panel-server:dev
    restart: always
    hostname: back-dev
    links:
      - pgsql-dev
    ports:
      - "5080:5000"
    depends_on:
      - pgsql-dev
    volumes:
      - /panel-manger/serverlog-dev:/app/logs
      - /panel-manger/user-upload-dev:/app/wwwroot/uploadfiles/personal

  pgsql-dev:
    image: cr.siemens.com/edge_group/panel_manager_prototype/deploy/postgres:12.11
    restart: always
    hostname: pgsql-dev
    ports:
      - "5480:5432"
    deploy:
      mode: replicated
      replicas: 1
      endpoint_mode: dnsrr
    environment:
      POSTGRES_PASSWORD: postgres
    volumes:
      - /panel-manger/pgdata2-dev:/var/lib/postgresql/data

