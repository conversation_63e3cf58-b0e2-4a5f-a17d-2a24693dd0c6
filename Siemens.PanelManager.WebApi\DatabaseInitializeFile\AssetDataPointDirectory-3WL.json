[{"AssetLevel": 50, "AssetModel": "3WL", "Name": "StatusValues", "ParentName": "", "LanguageKey": "StatusValues", "Sort": 1}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "Diagnostic", "ParentName": "StatusValues", "LanguageKey": "Diagnostic", "Sort": 2}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "CircuitBreaker", "ParentName": "Diagnostic", "LanguageKey": "CircuitBreaker", "Sort": 3}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "Status", "ParentName": "CircuitBreaker", "LanguageKey": "Status", "Sort": 4}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "Trips", "ParentName": "CircuitBreaker", "LanguageKey": "Trips", "Sort": 5}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "LastUnacknowledgedTrippingOperationOfTheTripUnit", "ParentName": "Trips", "LanguageKey": "LastUnacknowledgedTrippingOperationOfTheTripUnit", "Sort": 6}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "TrippingOperationsByMeteringFunction", "ParentName": "Trips", "LanguageKey": "TrippingOperationsByMeteringFunction", "Sort": 7}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "Warnings", "ParentName": "", "LanguageKey": "Warnings", "Sort": 8}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "<PERSON><PERSON><PERSON>old<PERSON><PERSON>nings", "ParentName": "CircuitBreaker", "LanguageKey": "<PERSON><PERSON><PERSON>old<PERSON><PERSON>nings", "Sort": 9}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "Statistics_Maintenance", "ParentName": "StatusValues", "LanguageKey": "Statistics_Maintenance", "Sort": 10}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "TotalOfDeactivated", "ParentName": "Statistics_Maintenance", "LanguageKey": "TotalOfDeactivated", "Sort": 11}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "CommunicationModule", "ParentName": "StatusValues", "LanguageKey": "CommunicationModule", "Sort": 12}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "CommunicationBits", "ParentName": "CommunicationModule", "LanguageKey": "CommunicationBits", "Sort": 13}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "VoltageL-N", "ParentName": "", "LanguageKey": "VoltageL-N", "Sort": 14}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "InstantaneousValuesL-N", "ParentName": "VoltageL-N", "LanguageKey": "InstantaneousValuesL-N", "Sort": 15}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "VoltageL-N_ActualInstantaneousMeasurementValues", "ParentName": "InstantaneousValuesL-N", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 16}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "VoltageL-N_GreatestMeasuredValues", "ParentName": "InstantaneousValuesL-N", "LanguageKey": "GreatestMeasuredValues", "Sort": 17}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "VoltageL-N_LowestMeasuredValues", "ParentName": "InstantaneousValuesL-N", "LanguageKey": "LowestMeasuredValues", "Sort": 18}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "VoltageL-L", "ParentName": "", "LanguageKey": "VoltageL-L", "Sort": 19}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "InstantaneousValuesL-L", "ParentName": "VoltageL-L", "LanguageKey": "InstantaneousValuesL-L", "Sort": 20}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "VoltageL-L_ActualInstantaneousMeasurementValues", "ParentName": "InstantaneousValuesL-L", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 21}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "VoltageL-L_GreatestMeasuredValues", "ParentName": "InstantaneousValuesL-L", "LanguageKey": "GreatestMeasuredValues", "Sort": 22}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "VoltageL-L_LowestMeasuredValues", "ParentName": "InstantaneousValuesL-L", "LanguageKey": "LowestMeasuredValues", "Sort": 23}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "Current", "ParentName": "", "LanguageKey": "Current", "Sort": 24}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "InstantaneousValues", "ParentName": "Current", "LanguageKey": "InstantaneousValues", "Sort": 25}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "Current_ActualInstantaneousMeasurementValues", "ParentName": "InstantaneousValues", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 26}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "Current_GreatestMeasuredValues", "ParentName": "InstantaneousValues", "LanguageKey": "GreatestMeasuredValues", "Sort": 27}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "Current_LowestMeasuredValues", "ParentName": "InstantaneousValues", "LanguageKey": "LowestMeasuredValues", "Sort": 28}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "MeanValues", "ParentName": "Current", "LanguageKey": "MeanValues", "Sort": 29}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "MeanValues_ActualMeasurementValues", "ParentName": "MeanValues", "LanguageKey": "ActualMeasurementValues", "Sort": 30}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "MeanValues_MaximumValuesInMeasuringInterval", "ParentName": "MeanValues", "LanguageKey": "MaximumValuesInMeasuringInterval", "Sort": 31}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "MeanValues_MinimumValuesInMeasuringInterval", "ParentName": "MeanValues", "LanguageKey": "MinimumValuesInMeasuringInterval", "Sort": 32}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "Power", "ParentName": "", "LanguageKey": "Power", "Sort": 33}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ActivePower", "ParentName": "Power", "LanguageKey": "ActivePower", "Sort": 34}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ActivePower_InstantaneousValues", "ParentName": "ActivePower", "LanguageKey": "InstantaneousValues", "Sort": 35}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ActivePower_ActualInstantaneousMeasurementValues", "ParentName": "ActivePower_InstantaneousValues", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 36}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ActivePower_GreatestMeasuredValues", "ParentName": "ActivePower_InstantaneousValues", "LanguageKey": "GreatestMeasuredValues", "Sort": 37}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ActivePower_LowestMeasuredValues", "ParentName": "ActivePower_InstantaneousValues", "LanguageKey": "LowestMeasuredValues", "Sort": 38}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ActivePower_MeanValues", "ParentName": "ActivePower", "LanguageKey": "MeanValues", "Sort": 39}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ActivePower_MeanValues_ActualInstantaneousMeasurementValues", "ParentName": "ActivePower_MeanValues", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 40}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ReactivePower", "ParentName": "Power", "LanguageKey": "ReactivePower", "Sort": 41}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ReactivePower_InstantaneousValues", "ParentName": "ReactivePower", "LanguageKey": "InstantaneousValues", "Sort": 42}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ReactivePower_ActualInstantaneousMeasurementValues", "ParentName": "ReactivePower_InstantaneousValues", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 43}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ReactivePower_GreatestMeasuredValues", "ParentName": "ReactivePower_InstantaneousValues", "LanguageKey": "GreatestMeasuredValues", "Sort": 44}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ReactivePower_LowestMeasuredValues", "ParentName": "ReactivePower_InstantaneousValues", "LanguageKey": "LowestMeasuredValues", "Sort": 45}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ReactivePower_MeanValues", "ParentName": "ReactivePower", "LanguageKey": "MeanValues", "Sort": 46}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ReactivePower_MeanValues_ActualInstantaneousMeasurementValues", "ParentName": "ReactivePower_MeanValues", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 47}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ParentName": "Power", "LanguageKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Sort": 48}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ApparentPower_InstantaneousValues", "ParentName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LanguageKey": "InstantaneousValues", "Sort": 49}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ApparentPower_ActualInstantaneousMeasurementValues", "ParentName": "ApparentPower_InstantaneousValues", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 50}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ApparentPower_GreatestMeasuredValues", "ParentName": "ApparentPower_InstantaneousValues", "LanguageKey": "GreatestMeasuredValues", "Sort": 51}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ApparentPower_LowestMeasuredValues", "ParentName": "ApparentPower_InstantaneousValues", "LanguageKey": "LowestMeasuredValues", "Sort": 52}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ApparentPower_MeanValues", "ParentName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LanguageKey": "MeanValues", "Sort": 53}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ApparentPower_MeanValues_ActualInstantaneousMeasurementValues", "ParentName": "ApparentPower_MeanValues", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 54}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "PowerFactor", "ParentName": "Power", "LanguageKey": "PowerFactor", "Sort": 55}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "PowerFactor_InstantaneousValues", "ParentName": "PowerFactor", "LanguageKey": "InstantaneousValues", "Sort": 56}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "PowerFactor_ActualInstantaneousMeasurementValues", "ParentName": "PowerFactor_InstantaneousValues", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 57}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "PowerFactor_GreatestMeasuredValues", "ParentName": "PowerFactor_InstantaneousValues", "LanguageKey": "GreatestMeasuredValues", "Sort": 58}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "PowerFactor_LowestMeasuredValues", "ParentName": "PowerFactor_InstantaneousValues", "LanguageKey": "LowestMeasuredValues", "Sort": 59}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "Energy", "ParentName": "", "LanguageKey": "Energy", "Sort": 60}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ActiveEnergy", "ParentName": "Energy", "LanguageKey": "ActiveEnergy", "Sort": 61}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ActiveEnergy_NormalDirection", "ParentName": "ActiveEnergy", "LanguageKey": "NormalDirection", "Sort": 62}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ActiveEnergy_ReverseDirection", "ParentName": "ActiveEnergy", "LanguageKey": "ReverseDirection", "Sort": 63}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ReactiveEnergy", "ParentName": "Energy", "LanguageKey": "ReactiveEnergy", "Sort": 64}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ReactiveEnergy_NormalDirection", "ParentName": "ReactiveEnergy", "LanguageKey": "NormalDirection", "Sort": 65}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ReactiveEnergy_ReverseDirection", "ParentName": "ReactiveEnergy", "LanguageKey": "ReverseDirection", "Sort": 66}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "FrequencyValues", "ParentName": "", "LanguageKey": "FrequencyValues", "Sort": 67}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "InstantaneousValuesFrequency", "ParentName": "FrequencyValues", "LanguageKey": "InstantaneousValuesFrequency", "Sort": 68}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "FrequencyValues_ActualInstantaneousMeasurementValues", "ParentName": "InstantaneousValuesFrequency", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 69}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "FrequencyValues_GreatestMeasuredValues", "ParentName": "InstantaneousValuesFrequency", "LanguageKey": "GreatestMeasuredValues", "Sort": 70}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "FrequencyValues_LowestMeasuredValues", "ParentName": "InstantaneousValuesFrequency", "LanguageKey": "LowestMeasuredValues", "Sort": 71}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "ThreePhaseSystem", "ParentName": "", "LanguageKey": "ThreePhaseSystem", "Sort": 72}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "HarmonicDistortion", "ParentName": "", "LanguageKey": "HarmonicDistortion", "Sort": 73}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "THDVoltage", "ParentName": "HarmonicDistortion", "LanguageKey": "THDVoltage", "Sort": 74}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "THDVoltage_InstantaneousValues", "ParentName": "THDVoltage", "LanguageKey": "InstantaneousValues", "Sort": 75}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "THDVoltage_ActualInstantaneousMeasurementValues", "ParentName": "THDVoltage_InstantaneousValues", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 76}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "THDVoltage_GreatestMeasuredValues", "ParentName": "THDVoltage_InstantaneousValues", "LanguageKey": "GreatestMeasuredValues", "Sort": 77}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "THDVoltage_LowestMeasuredValues", "ParentName": "THDVoltage_InstantaneousValues", "LanguageKey": "LowestMeasuredValues", "Sort": 78}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "THDCurrent", "ParentName": "HarmonicDistortion", "LanguageKey": "THDCurrent", "Sort": 79}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "THDCurrent_InstantaneousValues", "ParentName": "THDCurrent", "LanguageKey": "InstantaneousValues", "Sort": 80}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "THDCurrent_ActualInstantaneousMeasurementValues", "ParentName": "THDCurrent_InstantaneousValues", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 81}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "THDCurrent_GreatestMeasuredValues", "ParentName": "THDCurrent_InstantaneousValues", "LanguageKey": "GreatestMeasuredValues", "Sort": 82}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "THDCurrent_LowestMeasuredValues", "ParentName": "THDCurrent_InstantaneousValues", "LanguageKey": "LowestMeasuredValues", "Sort": 83}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "HarmonicVoltageL-N", "ParentName": "", "LanguageKey": "HarmonicVoltageL-N", "Sort": 84}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "HarmonicCurrent", "ParentName": "", "LanguageKey": "HarmonicCurrent", "Sort": 85}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "Temperatures", "ParentName": "", "LanguageKey": "Temperatures", "Sort": 86}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "Temperatures_InstantaneousValues", "ParentName": "Temperatures", "LanguageKey": "InstantaneousValues", "Sort": 87}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "Temperatures_ActualInstantaneousMeasurementValues", "ParentName": "Temperatures_InstantaneousValues", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 88}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "Temperatures_GreatestMeasuredValues", "ParentName": "Temperatures_InstantaneousValues", "LanguageKey": "GreatestMeasuredValues", "Sort": 89}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "Temperatures_LowestMeasuredValues", "ParentName": "Temperatures_InstantaneousValues", "LanguageKey": "LowestMeasuredValues", "Sort": 90}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "Others", "ParentName": "", "LanguageKey": "Others", "Sort": 91}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "THDVoltageL-N", "ParentName": "HarmonicDistortion", "LanguageKey": "THDVoltageL-N", "Sort": 92}, {"AssetLevel": 50, "AssetModel": "3WL", "Name": "THDVoltageL-N_ActualInstantaneousMeasurementValues", "ParentName": "THDVoltageL-N", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 93}]