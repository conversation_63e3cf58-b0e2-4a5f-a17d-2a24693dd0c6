﻿using Microsoft.Extensions.DependencyInjection;
using Siemens.PanelManager.Common.DependencyInjection;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.Interface.Extend;
using Siemens.PanelManager.Interface.Job;
using Siemens.PanelManager.Job.Alarm;
using Siemens.PanelManager.Job.ClearFile;
using Siemens.PanelManager.Job.DeviceScan;
using Siemens.PanelManager.Job.Energy;
using Siemens.PanelManager.Job.ExportAlarm;
using Siemens.PanelManager.Job.MaintenanceReport;
using Siemens.PanelManager.Job.ModbusDeviceWorker;
using Siemens.PanelManager.Job.Mqtt;
using Siemens.PanelManager.Job.SyncJob;
using Siemens.PanelManager.Job.TopologyJob;
using Siemens.PanelManager.Job.UDCJob;
using Siemens.PanelManager.Job.UploadExcel;

namespace Siemens.PanelManager.Job
{
    public class ServiceInjection : IServiceInjection
    {
        public void AddService(IServiceCollection services)
        {
            services.AddTransient<JobBase, UploadExcelJob>();
            services.AddTransient<JobBase, ClearLogFileJob>();
            services.AddTransient<JobBase, ClearTempFileJob>();
            services.AddTransient<JobBase, ClearUploadFileJob>();
            services.AddTransient<JobBase, ExportAlarmJob>();
            services.AddTransient<JobBase, GetDeviceMessagesJob>();
            services.AddTransient<JobBase, CabinetAlarmJob>();
            services.AddTransient<JobBase, AlarmCountJob>();
            services.AddTransient<JobBase, ExternalMqttJob>();
            services.AddTransient<JobBase, DeviceHarmonicJob>();
            //services.AddTransient<JobBase, AssetStatusLogSaveJob>();
            services.AddTransient<JobBase, UdcSqliteSyncArchiveDataJob>();
            services.AddTransient<JobBase, UdcSqliteSyncMessageJob>();
            services.AddTransient<JobBase, DeviceMaintainSyncJob>();
            services.AddTransient<JobBase, TopologyManangerJob>();
            services.AddTransient<JobBase, DeviceScanJob>();
            services.AddTransient<JobBase, EnergySummarySyncJob>();
            services.AddTransient<JobBase, MaintenanceReportJob>();
            services.AddTransient<IDeviceConenctStateWorkerManager, DeviceConenctStateWorkerManager>();
            services.AddSingleton<ExternalMqttAction>();

            services.AddHostedService<DeviceConnectStateWorker>();

            services.AddSingleton<PanelModbusWorker>();
            services.AddHostedService(sp => sp.GetRequiredService<PanelModbusWorker>());
            services.AddHostedService<ExternalMqttServer>();
            services.AddHostedService<AlarmWorker>();


            services.AddTransient<IExtendAction>((p) =>
            {
                return p.GetRequiredService<ExternalMqttAction>();
            });
        }
    }
}
