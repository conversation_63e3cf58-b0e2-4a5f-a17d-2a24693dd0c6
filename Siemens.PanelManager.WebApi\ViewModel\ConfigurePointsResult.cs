﻿using Siemens.PanelManager.Common.Tree;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    /// <summary>
    /// 配置点位返回值结构体
    /// </summary>
    public class ConfigurePointsDto : ITreeNode<ConfigurePointsDto>
    {
        /// <summary>
        /// 主键id
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 显示名字
        /// </summary>
        public string? Label { get; set; }

        /// <summary>
        /// 是否点位
        /// </summary>
        public bool IsPoint { get; set; }

        /// <summary>
        /// 是否选中(0:未选中,1:选中)
        /// </summary>
        public int IsCheck { get; set; }

        /// <summary>
        /// Code
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 点位上层文件夹信息
        /// </summary>
        public string? Directory { get; set; }

        /// <summary>
        /// 主id
        /// </summary>
        public string? Rid { get; set; }

        /// <summary>
        /// 父id
        /// </summary>
        public string? Pid { get; set; }

        /// <summary>
        /// AssetModel
        /// </summary>
        public string? AssetModel { get; set; }

        /// <summary>
        /// 子项内容
        /// </summary>
        public List<ConfigurePointsDto>? Children { get; set; }
        /// <summary>
        /// Has Mark
        /// </summary>
        public bool HasMark { get; set; }
    }

    /// <summary>
    /// 配置点位返回值结构体
    /// </summary>
    public class ConfigurePoints
    {
        /// <summary>
        /// 主键id
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// assetId
        /// </summary>
        public int AssetId { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 显示名字
        /// </summary>
        public string? Label { get; set; }

        /// <summary>
        /// 是否点位
        /// </summary>
        public bool IsPoint { get; set; }

        /// <summary>
        /// 是否选中(0:未选中,1:选中)
        /// </summary>
        public int IsCheck { get; set; }

        /// <summary>
        /// Code
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 点位类型
        /// </summary>
        public string? AssetModel { get; set; }

        /// <summary>
        /// 时间周期
        /// </summary>
        public int SamplingPeriod { get; set; }

        /// <summary>
        /// 点位上层文件夹信息
        /// </summary>
        public string? Directory { get; set; }

    }

    /// <summary>
    /// 构建分组点位上下级层级结构
    /// </summary>
    public class ConfigurePointsResult : ConfigurePoints
    {
        public List<ConfigurePoints> Children = new List<ConfigurePoints>();
    }

}
