﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.SyncService.UdcModels;
using SqlSugar;

namespace Siemens.PanelManager.SyncService.Sqlite.SyncHandle
{
    public class SlavesSync : SyncEntity, ISyncArchiveDataHandle
    {
        private readonly ILogger<SlavesSync> _logger;

        public SlavesSync(ILogger<SlavesSync> logger,
            IDbContextFactory<UdcContext> contextFactory,
            IConfiguration configuration,
            ISqlSugarClient client,
            IAssetDataProxyRef assetDataProxyRef) : base(contextFactory, configuration, client, assetDataProxyRef)
        {
            _logger = logger;

            ConfigName = nameof(SlaveMessages);
        }

        public async Task SyncWorkAsync()
        {
            try
            {
                await Task.Delay(100);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.ToString());
                throw;
            }
        }
    }
}
