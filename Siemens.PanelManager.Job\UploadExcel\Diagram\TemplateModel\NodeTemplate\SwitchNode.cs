﻿using Newtonsoft.Json;
using Siemens.PanelManager.Model.Database.Topology;
using Siemens.PanelManager.Model.Topology;

namespace Siemens.PanelManager.Job.UploadExcel.Diagram.TemplateModel.NodeTemplate
{
    /// <summary>
    /// 单线图中断路器元素
    /// </summary>
    internal class SwitchNode : NodeData
    {
        public override NodeType NodeType => NodeType.Switch;
        public SwitchNode(string? busBarName)
        {
            TypeCode = "B";
            Name = "Swich";
            OpenStyle = "swich27";
            CloseStyle = "swich27";
            SizeHight = 90;
            SizeWidth = 90;
            SwichStatus = "0";
            Alarm = false;
            AlarmStatus = string.Empty;
            ColorStatus = false;
            SourceType = "transformer";
            Category = "swichNodeTemplate";
            BusBarId = busBarName;
        }

        [JsonProperty("positionStatus", NullValueHandling = NullValueHandling.Ignore)]
        public string? BreakerPosition { get; set; }
        [JsonProperty("circuitId", NullValueHandling = NullValueHandling.Ignore)]
        public int? CircuitId { get; set; }

        [JsonProperty("circuitName", NullValueHandling = NullValueHandling.Ignore)]
        public string? CircuitName { get; set; }
        [JsonProperty("circuitModel", NullValueHandling = NullValueHandling.Ignore)]
        public string? CircuitModel { get; set; }

        [JsonProperty("busBarId", NullValueHandling = NullValueHandling.Ignore)]
        public string? BusBarId { get; set; }

        [JsonProperty("deviceType", NullValueHandling = NullValueHandling.Ignore)]
        public string? DeviceType { get; set; }

        public override void AddRules(List<TopologyRuleInfo> ruleInfos)
        {
            if (!Key.HasValue) return;
            if (!AssetId.HasValue) return;

            var ruleIdMap = new Dictionary<string, string>();
            var id = Guid.NewGuid().ToString();
            ruleInfos.Add(new TopologyRuleInfo()
            {
                AssetId = AssetId,
                DataPoint = "Switch",
                RuleCode = id,
                TargetIdentify = Key?.ToString() ?? string.Empty,
                TargetProperty = "swichStatus",
                FormatFunction = JsonConvert.SerializeObject(new
                {
                    FunctionName = "ChangeSwitch"
                })
            });
            ruleIdMap.Add("swichStatus", id);

            if ("ACB".Equals(DeviceType))
            {
                id = Guid.NewGuid().ToString();
                ruleInfos.Add(new TopologyRuleInfo()
                {
                    AssetId = AssetId,
                    DataPoint = "BreakerPosition",
                    RuleCode = id,
                    TargetIdentify = Key?.ToString() ?? string.Empty,
                    TargetProperty = "positionStatus",
                    FormatFunction = JsonConvert.SerializeObject(new
                    {
                        FunctionName = "ChangeBreakerPosition"
                    })
                });
                ruleIdMap.Add("positionStatus", id);
            }

            RuleIdMap = ruleIdMap;
        }
    }
}
