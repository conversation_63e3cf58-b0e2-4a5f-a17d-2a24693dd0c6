﻿using Siemens.PanelManager.Monitor.Cmd;
using System.Net;

namespace Siemens.PanelManager.Monitor.Function
{
    public class ChangeIPFunc
    {
        public static async Task<string[]> GetX2p1Config(ILogger logger)
        {
            var ipAddress = await ChangeIPCmd.GetX2p1Config(logger);
            return ipAddress;
        }

        public static async Task SetX2p1Config(List<string> ipInfos, ILogger logger)
        {
            await ChangeIPCmd.SetX2p1Config(ipInfos, logger);
            await ChangeIPCmd.RestartNetwork(logger);
        }

        public static async Task Reboot(ILogger logger)
        {
            await ChangeIPCmd.Reboot(logger);
        }
    }
}
