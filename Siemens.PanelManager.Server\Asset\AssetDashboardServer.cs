﻿using Akka.Util;
using Akka.Util.Internal;
using InfluxDB.Client;
using InfluxDB.Client.Core.Flux.Domain;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Siemens.PanelManager.Common;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Model.Chart;
using Siemens.PanelManager.Model.Config;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Server.Asset.Model;
using SqlSugar;
using System;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.Server.Asset
{
    public class AssetDashboardServer
    {
        private readonly ILogger _logger;
        private readonly IServiceProvider _provider;
        public AssetDashboardServer(ILogger<AssetDashboardServer> logger, IServiceProvider provider)
        {
            _logger = logger;
            _provider = provider;
        }
        public async Task<IChart?> GetDashboard(string name, IMessageContext messageContext, Dictionary<string, string>? filter = null, ISqlSugarClient? sqlClient = null)
        {
            if (sqlClient == null)
            {
                sqlClient = _provider.GetRequiredService<SqlSugarScope>();
            }

            name = name.ToUpper();  
            var dashboardConfig = await sqlClient.Queryable<AssetDashboardConfig>().Where(d => d.ConfigName == name).FirstAsync();
            if (dashboardConfig != null)
            {
                return await GetDashboard(dashboardConfig, messageContext, filter, sqlClient);
            }

            return null;
        }
        public async Task<List<FluxTable>> GetHealthData(string startTime, string endTime,int topologyId, ISqlSugarClient? sqlClient = null)
        {
            IConfiguration configuration = _provider.GetRequiredService<IConfiguration>();
            var session = configuration.GetSection(InfluxDBConfig.Influxdb);
            var result = new List<FluxTable>();
            if (session != null && session.Exists())
            {
                var influxDbConfig = session.Get<InfluxDBConfig>();
                if (influxDbConfig == null)
                {
                    _logger.LogError("SyncDataToInfluxdbWorker init: Not found Influxdb config.");
                    return result;
                }
                using var influxClient = new InfluxDBClient(influxDbConfig.Url, influxDbConfig.UserName, influxDbConfig.Password);
                var queryApi = influxClient.GetQueryApi();
                if (sqlClient == null) { return result; }
                var dashboardConfig = await sqlClient.Queryable<AssetDashboardConfig>().Where(d => d.ConfigName == "HEALTH_SUBSTATION").FirstAsync();
                var fluxSql = dashboardConfig.Sql;
                
                fluxSql = fluxSql
                    .Replace("[[DBName]]", influxDbConfig.Bucket)
                    .Replace("[[TableName]]", "y_evaluation")
                    .Replace("[[topologyId]]", topologyId.ToString())
                    .Replace("[[StartDate]]", startTime)
                    .Replace("[[EndDate]]", endTime);
                ;
                result = await queryApi.QueryAsync(fluxSql, influxDbConfig.OrgName ?? influxDbConfig.OrgId);
            }
            return result;
        }
        public async Task<IChart?> GetDashboard(AssetDashboardConfig config, IMessageContext messageContext, Dictionary<string, string>? filter = null, ISqlSugarClient? sqlClient = null)
        {
            if (config == null)
            {
                return null;
            }

            switch (config.DbType)
            {
                case "pgsql":
                    {
                        if (sqlClient == null)
                        {
                            sqlClient = _provider.GetRequiredService<SqlSugarScope>();
                        }

                        return await GetChartBySql(config, sqlClient, messageContext, filter);
                    }
                case "influxdb":
                    {
                        IConfiguration configuration = _provider.GetRequiredService<IConfiguration>();
                        var session = configuration.GetSection(InfluxDBConfig.Influxdb);

                        if (session != null && session.Exists())
                        {
                            var influxDbConfig = session.Get<InfluxDBConfig>();
                            if (influxDbConfig == null)
                            {
                                _logger.LogError("SyncDataToInfluxdbWorker init: Not found Influxdb config.");
                                return null;
                            }
                            if (filter == null)
                            {
                                filter = new Dictionary<string, string>();
                            }
                            return await GetChartByInfluxDb(config, influxDbConfig, messageContext, filter);
                        }
                    }
                    break;
                default: break;
            }

            return null;
        }

        private async Task<IChart?> GetChartBySql(AssetDashboardConfig config, ISqlSugarClient client, IMessageContext messageContext, Dictionary<string, string>? filter)
        { 
            if (string.IsNullOrEmpty(config.Sql)) return null;
            JObject extendObj;
            if (string.IsNullOrEmpty(config.Extend))
            {
                extendObj = new JObject();
            }
            else
            {
                try
                {
                    extendObj = JObject.Parse(config.Extend);
                }
                catch
                {
                    extendObj = new JObject();
                }
            }
            switch (config.DashboardType)
            {
                case "Line":
                    {
                        var sql = config.Sql;
                        if (filter != null)
                        {
                            sql = ChangeSqlStr(sql, filter);
                        }
                        var lineList = await client.Ado.SqlQueryAsync<LineChartInfo>(sql);

                        JToken? value;
                        var staticX = new string[0];
                        if (extendObj.TryGetValue("X", out value) && value != null)
                        {
                            try
                            {
                                staticX = value.Values<string>().Where(v => !string.IsNullOrEmpty(v)).OfType<string>().ToArray() ?? staticX;
                            }
                            catch
                            {

                            }
                        }

                        var count = lineList.Count >= staticX.Length ? lineList.Count : staticX.Length;

                        var xList = new string[count];
                        var y1List = new decimal[count];
                        var y2List = new decimal[count];
                        var y3List = new decimal[count];
                        var y4List = new decimal[count];
                        var y5List = new decimal[count];
                        var y6List = new decimal[count];
                        var y7List = new decimal[count];
                        var y8List = new decimal[count];
                        var y9List = new decimal[count];
                        var xTitleList = new string?[count];
                        var lineChart = new LineChartModel();
                        lineChart.X = xList;
                        lineChart.Y1 = y1List;


                        if (extendObj.TryGetValue("Standard", out value) && value != null)
                        {
                            try
                            {
                                lineChart.Standard = value.Value<decimal>();
                            }
                            catch
                            {

                            }
                        }

                        if (extendObj.TryGetValue("XColumn", out value) && value != null)
                        {
                            try
                            {
                                lineChart.XColumn = value.Value<string>();
                            }
                            catch
                            {

                            }
                        }

                        if (extendObj.TryGetValue("YColumns", out value) && value != null)
                        {
                            try
                            {
                                lineChart.YColumns = value.Values<string>().Where(t => !string.IsNullOrEmpty(t)).OfType<string>().Select(y => messageContext.GetString($"Overview_{y}") ?? y).ToList();
                                if (lineChart.YColumns.Count >= 2)
                                {
                                    lineChart.Y2 = y2List;
                                }

                                if (lineChart.YColumns.Count >= 3)
                                {
                                    lineChart.Y3 = y3List;
                                }
                                if (lineChart.YColumns.Count >= 4)
                                {
                                    lineChart.Y4 = y4List;
                                }
                                if (lineChart.YColumns.Count >= 5)
                                {
                                    lineChart.Y5 = y5List;
                                }
                                if (lineChart.YColumns.Count >= 6)
                                {
                                    lineChart.Y6 = y6List;
                                }
                                if (lineChart.YColumns.Count >= 7)
                                {
                                    lineChart.Y7 = y7List;
                                }
                                if (lineChart.YColumns.Count >= 8)
                                {
                                    lineChart.Y8 = y8List;
                                }
                                if (lineChart.YColumns.Count >= 9)
                                {
                                    lineChart.Y9 = y9List;
                                }
                            }
                            catch
                            {

                            }
                        }

                        Func<string, string> typeNameMappings = (s) => s;
                        if (extendObj.TryGetValue("TypeNameMappings", out value) && value != null)
                        {
                            try
                            {
                                var obj = value.ToObject<TypeNameMappings>();
                                if (obj != null)
                                {
                                    if (!string.IsNullOrEmpty(obj.StaticKey))
                                    {
                                        typeNameMappings = (s) => messageContext.GetString($"{obj.StaticKey}_{s}") ?? s;
                                    }
                                }
                            }
                            catch
                            {

                            }
                        }

                        int i = 0;
                        for (; i < lineList.Count; i++)
                        {
                            var lineInfo = lineList[i];
                            if (i == 0)
                            {
                                if (lineInfo.Y2.HasValue)
                                {
                                    lineChart.Y2 = y2List;
                                }
                                if (lineInfo.Y3.HasValue)
                                {
                                    lineChart.Y3 = y3List;
                                }
                                if (lineInfo.Y4.HasValue)
                                {
                                    lineChart.Y4 = y4List;
                                }
                                if (lineInfo.Y5.HasValue)
                                {
                                    lineChart.Y5 = y5List;
                                }
                                if (lineInfo.Y6.HasValue)
                                {
                                    lineChart.Y6 = y6List;
                                }
                                if (lineInfo.Y7.HasValue)
                                {
                                    lineChart.Y7 = y7List;
                                }
                                if (lineInfo.Y8.HasValue)
                                {
                                    lineChart.Y8 = y8List;
                                }
                                if (lineInfo.Y9.HasValue)
                                {
                                    lineChart.Y9 = y9List;
                                }
                            }
                            xList[i] = typeNameMappings(lineInfo.X ?? string.Empty);
                            y1List[i] = lineInfo.Y1 ?? 0m;
                            y2List[i] = lineInfo.Y2 ?? 0m;
                            y3List[i] = lineInfo.Y3 ?? 0m;
                            y4List[i] = lineInfo.Y4 ?? 0m;
                            y5List[i] = lineInfo.Y5 ?? 0m;
                            y6List[i] = lineInfo.Y6 ?? 0m;
                            y7List[i] = lineInfo.Y7 ?? 0m;
                            y8List[i] = lineInfo.Y8 ?? 0m;
                            y9List[i] = lineInfo.Y9 ?? 0m;
                            xTitleList[i] = lineInfo.XTitles;
                        }

                        if (staticX.Length > 0)
                        {
                            foreach (var item in staticX)
                            {
                                if (string.IsNullOrEmpty(item)) continue;
                                var x = item;

                                x = typeNameMappings(x);
                                if (!xList.Contains(x))
                                {
                                    xList[i] = x;
                                    y1List[i] = 0m;
                                    y2List[i] = 0m;
                                    y3List[i] = 0m;
                                    y4List[i] = 0m;
                                    y5List[i] = 0m;
                                    y6List[i] = 0m;
                                    y7List[i] = 0m;
                                    y8List[i] = 0m;
                                    y9List[i] = 0m;
                                    i++;
                                }
                            }
                        }

                        var needAdd = false;
                        foreach (var x in xTitleList)
                        {
                            if (!string.IsNullOrEmpty(x))
                            {
                                needAdd = true;
                                break;
                            }
                        }

                        if (needAdd)
                        {
                            lineChart.XTitles = xTitleList;
                        }
                        return lineChart;
                    }
                case "Pie":
                    {
                        var pieList = await client.Ado.SqlQueryAsync<PieInfo>(config.Sql);
                        var pieChart = new PieChartModel();
                        JToken? value;
                        if (extendObj.TryGetValue("TypeList", out value) && value != null)
                        {
                            try
                            {
                                pieChart.TypeList = value.Values<string>().Where(t => !string.IsNullOrEmpty(t)).OfType<string>().ToArray();
                            }
                            catch
                            {

                            }
                        }
                        Func<string, string> typeNameMappings = (s) => s;
                        if (extendObj.TryGetValue("TypeNameMappings", out value) && value != null)
                        {
                            try
                            {
                                var obj = value.ToObject<TypeNameMappings>();
                                if (obj != null)
                                {
                                    if (!string.IsNullOrEmpty(obj.StaticKey))
                                    {
                                        typeNameMappings = (s) => messageContext.GetString($"{obj.StaticKey}_{s}") ?? s;
                                    }
                                }
                            }
                            catch
                            {

                            }
                        }
                        pieChart.Summary = pieList?.ToArray() ?? new PieInfo[0];
                        if (pieList == null) break;
                        var sum = pieList.Sum(t => t.Value.HasValue ? t.Value.Value : 0m);
                        decimal pSum = 0m;

                        for (var i = 0; i < pieList.Count; i++)
                        {
                            var pirInfo = pieList[i];
                            if (!pirInfo.Value.HasValue)
                            {
                                pirInfo.Value = 0m;
                                pirInfo.Percentage = 0m;
                                continue;
                            }
                            var p = Math.Round(pirInfo.Value.Value / sum, 4);

                            if (pSum + p > 1)
                            {
                                p = 1 - pSum;
                                pSum = 1;
                            }
                            else
                            {
                                if (i == pieList.Count - 1)
                                {
                                    p = 1 - pSum;
                                }
                                pSum += p;
                            }

                            pirInfo.Percentage = p * 100;
                            pirInfo.Code = pirInfo.Name ?? string.Empty;
                            pirInfo.Name = typeNameMappings(pirInfo.Name ?? string.Empty);

                        }
                        return pieChart;
                    }
                default: break;
            }
            return null;
        }

        private string ChangeSqlStr(string sql, Dictionary<string, string> filter) 
        {
            foreach (var kv in filter)
            {
                sql = sql.Replace($"[[{kv.Key}]]", kv.Value);
            }
            return sql; 
        }

        /// <summary>
        /// 当前时序数据库只支持时间轴
        /// </summary>
        /// <param name="config"></param>
        /// <param name="settings"></param>
        /// <param name="messageContext"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        private async Task<IChart?> GetChartByInfluxDb(AssetDashboardConfig config, InfluxDBConfig settings, IMessageContext messageContext, Dictionary<string, string> filter)
        {
            if(filter.TryGetValue("Extend", out string? extend))
            {
                //这里有问题
                if (config.Extend!= null){
                    config.Extend = config.Extend.Replace("[[Extend]]", extend);
                }
            }
            if (config.DashboardType != "Line" || string.IsNullOrEmpty(config.Extend)) return null;
            var extendObj = JsonConvert.DeserializeObject<InfluxDBExtendModel>(config.Extend);
            if (extendObj == null) return null;

            var influxClient = new InfluxDBClient(settings.Url, settings.UserName, settings.Password);

            var queryApi = influxClient.GetQueryApi();
            filter.TryAdd("DBName", settings.Bucket);
            int useFullTime = 0;
            var sql = QueryStrChangeByParamsForInfluxDb(config.Sql, filter, ref useFullTime);
            var result = await queryApi.QueryAsync(sql, settings.OrgId ?? settings.OrgName);

            var dataList = new Dictionary<string, List<FluxRecord>>();
            int recordCount = 0;
            foreach (var table in result)
            {
                var firstRecord = table.Records.FirstOrDefault();
                if (firstRecord == null) continue;
                var fieldName = firstRecord.GetValueByKey("_field")?.ToString() ?? string.Empty;

                //var records = new List<FluxRecord>();
                //if (filter.ContainsKey("AssetIdFilter"))
                //{
                //    string assetIdFilters = filter["AssetIdFilter"];
                //    var assetIdList = assetIdFilters.Split(',');
                //    for (var i = 0; i < table.Records.Count - 1; i++)
                //    {
                //        var assetid = table.Records[i].GetValueByKey("assetid")?.ToString();
                //        if (assetIdList.Contains(assetid))
                //        {
                //            records.Add(table.Records[i]);
                //            //table.Records.RemoveAt(i);
                //        }
                //    }
                //}

                if (extendObj.YColumns.Any(yc => yc.Equals(fieldName, StringComparison.OrdinalIgnoreCase)))
                {
                    recordCount = table.Records.Count;
                    dataList.Add(fieldName, table.Records);
                }

            }

            if(recordCount > 1) 
            {
                recordCount--;
            }

            var lineChart = new LineChartModel
            {
                X = new string[recordCount]
            };

            var timeFormat = string.Empty;
            switch (useFullTime)
            {
                case 2:
                    timeFormat = "yyyy-MM-dd HH:mm";
                    break;
                case 3:
                    timeFormat = "yyyy-MM-dd";
                    break;
                case 1:
                default:
                    timeFormat = "HH:mm:ss";
                    break;
            }
            var time = string.Empty;
            var loaclTimeZone = TimeZoneInfo.Local;
            if (recordCount > 0)
            {
                for (var i = 0; i < recordCount; i++)
                {
                    int inputCount = i;

                    for (var yIndex = 0; yIndex < extendObj.YColumns.Length; yIndex++)
                    {
                        var fieldName = extendObj.YColumns[yIndex];
                        if (!dataList.ContainsKey(fieldName.ToLower())) continue;
                        var records = dataList[fieldName.ToLower()];
                        if (string.IsNullOrEmpty(time))
                        {
                            var utcTime = records[i].GetTimeInDateTime();

                            if (utcTime != null)
                            {
                                var currentTime = TimeZoneInfo.ConvertTimeFromUtc(utcTime.Value, loaclTimeZone);
                                time = currentTime.ToString(timeFormat);
                            }
                        }
                        var value = Convert.ToDecimal(records[i].GetValueByKey("_value"));
                        switch (yIndex)
                        {
                            case 0:
                                if (i == 0)
                                {
                                    lineChart.Y1 = new decimal[recordCount];
                                }
                                lineChart.Y1![inputCount] += value;
                                break;
                            case 1:
                                if (i == 0)
                                {
                                    lineChart.Y2 = new decimal[recordCount];
                                }
                                lineChart.Y2![inputCount] += value;
                                break;
                            case 2:
                                if (i == 0)
                                {
                                    lineChart.Y3 = new decimal[recordCount];
                                }
                                lineChart.Y3![inputCount] += value;
                                break;
                            case 3:
                                if (i == 0)
                                {
                                    lineChart.Y4 = new decimal[recordCount];
                                }
                                lineChart.Y4![inputCount] += value;
                                break;
                            case 4:
                                if (i == 0)
                                {
                                    lineChart.Y5 = new decimal[recordCount];
                                }
                                lineChart.Y5![inputCount] += value;
                                break;
                            case 5:
                                if (i == 0)
                                {
                                    lineChart.Y6 = new decimal[recordCount];
                                }
                                lineChart.Y6![inputCount] += value;
                                break;
                            case 6:
                                if (i == 0)
                                {
                                    lineChart.Y7 = new decimal[recordCount];
                                }
                                lineChart.Y7![inputCount] += value;
                                break;
                            case 7:
                                if (i == 0)
                                {
                                    lineChart.Y8 = new decimal[recordCount];
                                }
                                lineChart.Y8![inputCount] += value;
                                break;
                            case 8:
                                if (i == 0)
                                {
                                    lineChart.Y9 = new decimal[recordCount];
                                }
                                lineChart.Y9![inputCount] += value;
                                break;
                            default: break;
                        }
                    }

                    if (string.IsNullOrEmpty(lineChart.X[inputCount]))
                    {
                        lineChart.X[inputCount] = time;
                    }
                    time = string.Empty;
                }

                if (extendObj.YColumnNameGroup != null && extendObj.YColumnNameGroup.Length > 0)
                {
                    lineChart.YColumns = new List<string>();
                    foreach (var nameGroup in extendObj.YColumnNameGroup)
                    {
                        lineChart.YColumns.Add(messageContext.GetString(nameGroup) ?? nameGroup);
                    }
                }
            }
            return lineChart;
        }

        private string QueryStrChangeByParamsForInfluxDb(string sql, Dictionary<string, string> filter, ref int useFullTime)
        {
            var paramDatas = new Dictionary<string, string>();

            #region start-end
            {
                if (filter.ContainsKey("ChartDateType"))
                {
                    if (int.TryParse(filter["ChartDateType"], out var chartType)) 
                    {
                        switch (chartType)
                        {
                            case 0:
                                {
                                    if (filter.TryGetValue("StartDate", out var startDateStr))
                                    {
                                        var m = Regex.Match(startDateStr, "^([\\d]{4})-([\\d]{1,2})-([\\d]{1,2})");
                                        if (m.Success)
                                        {
                                            var year = int.Parse(m.Groups[1].Value);
                                            var month = int.Parse(m.Groups[2].Value);
                                            var days = int.Parse(m.Groups[3].Value);
                                            var startDate = new DateTime(year, month, days);
                                            var endDate = startDate.AddDays(1).AddSeconds(-1);
                                            if (endDate > DateTime.Now)
                                            {
                                                endDate = DateTime.Now;
                                            }
                                            paramDatas.AddOrSet("StartDate", startDate.GetTimestampForSec().ToString());
                                            paramDatas.AddOrSet("EndDate", endDate.GetTimestampForSec().ToString());

                                            paramDatas.AddOrSet("TableName", "archivedatarealtime");
                                            paramDatas.AddOrSet("Cycle", "15m");
                                            useFullTime = 1;
                                        }
                                    }
                                }
                                break;
                            case 1:
                                {
                                    if (filter.TryGetValue("StartDate", out var startDateStr))
                                    {
                                        var m = Regex.Match(startDateStr, "^([\\d]{4})-([\\d]{1,2})");
                                        if (m.Success)
                                        {
                                            var year = int.Parse(m.Groups[1].Value);
                                            var month = int.Parse(m.Groups[2].Value);
                                            var startDate = new DateTime(year, month, 1);
                                            var endDate = startDate.AddMonths(1).AddSeconds(-1);
                                            paramDatas.AddOrSet("StartDate", startDate.GetTimestampForSec().ToString());
                                            paramDatas.AddOrSet("EndDate", endDate.GetTimestampForSec().ToString());

                                            paramDatas.AddOrSet("TableName", "archivedataquarter");
                                            paramDatas.AddOrSet("Cycle", "3h");
                                            useFullTime = 2;
                                        }
                                    }
                                }
                                break;
                            case 2:
                                {
                                    if (filter.TryGetValue("StartDate", out var startDateStr))
                                    {
                                        var m = Regex.Match(startDateStr, "^([\\d]{4})");
                                        if (m.Success)
                                        {
                                            var year = int.Parse(m.Groups[1].Value);
                                            var startDate = new DateTime(year, 1, 1);
                                            var endDate = startDate.AddYears(1).AddSeconds(-1);
                                            paramDatas.AddOrSet("StartDate", startDate.GetTimestampForSec().ToString());
                                            paramDatas.AddOrSet("EndDate", endDate.GetTimestampForSec().ToString());

                                            paramDatas.AddOrSet("TableName", "archivedataquarter");
                                            paramDatas.AddOrSet("Cycle", "1d");
                                            useFullTime = 3;
                                        }
                                    }
                                }
                                break;
                            case 4:
                                {
                                    var startDate = DateTime.UtcNow.AddHours(-24);
                                    var endDate = DateTime.UtcNow;
                                    paramDatas.AddOrSet("StartDate", startDate.GetTimestampForSec().ToString());
                                    paramDatas.AddOrSet("EndDate", endDate.GetTimestampForSec().ToString());

                                    paramDatas.AddOrSet("TableName", "archivedataquarter");
                                    paramDatas.AddOrSet("Cycle", "15m");
                                    useFullTime = 1;
                                }
                                break;
                            // 自定义开始结束，间隔为五分钟
                            case 5:
                                {
                                    if (filter.TryGetValue("StartDate", out var startDateStr)
                                        && DateTime.TryParseExact(startDateStr, "yyyy-MM-dd HH:mm:ss", null, System.Globalization.DateTimeStyles.None, out DateTime startDate))
                                    {
                                        paramDatas.AddOrSet("StartDate", startDate.GetTimestampForSec().ToString());
                                    }

                                    if (filter.TryGetValue("EndDate", out var endDateStr)
                                        && DateTime.TryParseExact(endDateStr, "yyyy-MM-dd HH:mm:ss", null, System.Globalization.DateTimeStyles.None, out DateTime endDate))
                                    {
                                        paramDatas.AddOrSet("EndDate", endDate.GetTimestampForSec().ToString());
                                    }

                                    paramDatas.AddOrSet("TableName", "archivedatarealtime");
                                    paramDatas.AddOrSet("Cycle", "5m");
                                    useFullTime = 1;
                                    break;
                                }
                            default:
                                {
                                    if (filter.TryGetValue("StartDate", out var startDateStr)
                                        && DateTime.TryParseExact(startDateStr, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out DateTime startDate))
                                    {
                                        paramDatas.AddOrSet("StartDate", startDate.GetTimestampForSec().ToString());
                                    }
                                    if (filter.TryGetValue("EndDate", out var endDateStr)
                                        && DateTime.TryParseExact(endDateStr, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out DateTime endDate))
                                    {
                                        endDate = endDate.ToUniversalTime().AddDays(1).AddSeconds(-1);
                                        paramDatas.AddOrSet("EndDate", endDate.GetTimestampForSec().ToString());
                                    }

                                    paramDatas.AddOrSet("TableName", "archivedataquarter");
                                    paramDatas.AddOrSet("Cycle", "1d");
                                    useFullTime = 3;
                                }
                                break;
                        }
                    }

                    filter.Remove("ChartDateType");
                    filter.Remove("StartDate");
                    filter.Remove("EndDate");
                }
            }
            #endregion

            foreach (var kv in filter)
            {
                if (!paramDatas.ContainsKey(kv.Key))
                {
                    paramDatas.Add(kv.Key, kv.Value);
                }
            }

            foreach (var kv in paramDatas)
            {
                sql = sql.Replace($"[[{kv.Key}]]", kv.Value);
            }

            return sql;
        }

        public async Task<AssetDashboardConfig[]> GetDashboardConfigsAsync(string[] nameList, ISqlSugarClient? sqlClient = null)
        {
            if (nameList == null || nameList.Length == 0) return new AssetDashboardConfig[0];
            if (sqlClient == null)
            {
                sqlClient = _provider.GetRequiredService<SqlSugarScope>();
            }

            var newNameList = new string[nameList.Length];
            for(var i =0;i< nameList.Length;i++) 
            {
                newNameList[i] = nameList[i].ToUpper();
            }

            return await sqlClient.Queryable<AssetDashboardConfig>().Where(d => newNameList.Contains(d.ConfigName)).ToArrayAsync();
        }
    }
}
