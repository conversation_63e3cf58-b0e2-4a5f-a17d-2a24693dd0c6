﻿using InfluxDB.Client.Core;
using Siemens.InfluxDB.Helper.Interface;

namespace Siemens.PanelManager.Model.InfluxDBModel
{
    [Measurement("pm_asset_status_log")]
    public class AssetStatusLogData : IInfluxData
    {
        [Column(IsTimestamp = true)]
        public DateTime Time { get; set; }
        [Column("asset_id", IsTag = true)]
        public string AssetId { get; set; } = string.Empty;
        [Column("asset_type", IsTag = true)]
        public string AssetType { get; set; } = string.Empty;
        [Column("asset_level", IsTag = true)]
        public string AssetLevel { get; set; } = string.Empty;
        [Column("ua")]
        public decimal? Ua { get; set; }
        [Column("ub")]
        public decimal? Ub { get; set; }
        [Column("uc")]
        public decimal? Uc { get; set; }
        [Column("uab")]
        public decimal? Uab { get; set; }
        [Column("ubc")]
        public decimal? Ubc { get; set; }
        [Column("uca")]
        public decimal? Uca { get; set; }
        [Column("la")]
        public decimal? La { get; set; }
        [Column("lb")]
        public decimal? Lb { get; set; }
        [Column("lc")]
        public decimal? Lc { get; set; }
        [Column("p")]
        public decimal? P { get; set; }
        [Column("q")]
        public decimal? Q { get; set; }
        [Column("s")]
        public decimal? S { get; set; }
        [Column("cos_phi")]
        public decimal? CosPhi { get; set; }
        [Column("f")]
        public decimal? F { get; set; }
        [Column("thd_ua")]
        public decimal? THD_Ua { get; set; }
        [Column("thd_ub")]
        public decimal? THD_Ub { get; set; }
        [Column("thd_uc")]
        public decimal? THD_Uc { get; set; }
        [Column("thd_la")]
        public decimal? THD_La { get; set; }
        [Column("thd_lb")]
        public decimal? THD_Lb { get; set; }
        [Column("thd_lc")]
        public decimal? THD_Lc { get; set; }
        [Column("forward_active_power")]
        public decimal? ForwardActivePower { get; set; }
        [Column("forward_reactive_power")]
        public decimal? ForwardReactivePower { get; set; }
        [Column("reverse_active_power")]
        public decimal? ReverseActivePower { get; set; }
        [Column("reverse_reactive_power")]
        public decimal? ReverseReactivePower { get; set; }
        [Column("health_score")]
        public decimal? HealthScore { get; set; }
        [Column("temperature")]
        public decimal? Temperature { get; set; }
        [Column("remaining_life")]
        public decimal? RemainingLife { get; set; }
        [Column("wear_rate")]
        public decimal? WearRate { get; set; }
        [Column("have_alarm")]
        public bool? HaveAlarm { get; set; }
    }
}
