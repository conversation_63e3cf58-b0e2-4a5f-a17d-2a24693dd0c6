{"info": {"_postman_id": "723268db-0545-496e-b56f-ab3ed986f76c", "name": "D4使用超级管理员账号进入panel manager组态编辑中的3D图编辑页面，选择任意一张图纸设为首页第一张图", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 7", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "查看3D图列表 Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"展示所有3D图表\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"id\");\r", "    pm.expect(pm.response.text()).to.include(\"name\");\r", "    pm.expect(pm.response.text()).to.include(\"code\");\r", "    pm.expect(pm.response.text()).to.include(\"description\");\r", "    pm.expect(pm.response.text()).to.include(\"time\");\r", "    pm.expect(pm.response.text()).to.include(\"owner\");\r", "    pm.expect(pm.response.text()).to.include(\"dashboardFirst\");\r", "});\r", " let D1 = pm.response.json().data[0].id\r", " pm.environment.set(\"D1\",D1)"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v2/topoplogy/3D", "host": ["{{baseUrl}}"], "path": ["api", "v2", "topoplogy", "3D"]}}, "response": []}, {"name": "设置3D图为首页第一张", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"dashboardFirst\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v2/topoplogy/3D/dashboard/{{D1}}", "host": ["{{baseUrl}}"], "path": ["api", "v2", "topoplogy", "3D", "dashboard", "{{D1}}"]}}, "response": []}, {"name": "获取首页3D图", "event": [{"listen": "test", "script": {"exec": ["// 获取已存在的环境变量\r", "var myVar = pm.environment.get(\"D1\");\r", "\r", "pm.test(\"首页为设置的3D图\", function () {\r", "    var jsonData = pm.response.json().data;\r", "    pm.expect(jsonData[\"id\"]).to.eql(myVar);\r", "});\r", ";"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v2/topoplogy/3D/dashboard", "host": ["{{baseUrl}}"], "path": ["api", "v2", "topoplogy", "3D", "dashboard"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}]}