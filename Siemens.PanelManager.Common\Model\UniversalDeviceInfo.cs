﻿using Siemens.PanelManager.Model.Common;

namespace Siemens.PanelManager.Common.Model
{
    /// <summary>
    /// 通用数据结构定义
    /// </summary>
    public class UniversalDeviceInfo
    {
        /// <summary>
        /// 私有构造函数
        /// </summary>
        private UniversalDeviceInfo() { }

        /// <summary>
        /// 实例化
        /// </summary>
        private static readonly UniversalDeviceInfo SIntance = new UniversalDeviceInfo();

        /// <summary>
        /// 外部访问器
        /// </summary>
        public static UniversalDeviceInfo Instance
        {
            get { return SIntance; }
        }

        /// <summary>
        /// 父级名称集合
        /// </summary>
        public readonly Dictionary<string, string> _dicPoint = new Dictionary<string, string>()
        {
            {"Voltage","电压"},
            {"Current","电流"},
            {"Power","功率"},
            {"Power Period","功率周期"},
            {"Power Factor","功率因数"},
            {"Frequency","频率"},
            {"THD","谐波"},
            {"Counter","电能"},
            {"Cos Phi","功率因数"},
            {"Flicker","闪变"},
            {"Voltage Harmonics","电压谐波"},
            {"Current Harmonics","电流谐波"},
            {"Others","其他"},
            {"State","状态"}
        };

        /// <summary>
        /// 第三方设备属性
        /// </summary>
        public readonly Dictionary<string, string> _dicProperty = new Dictionary<string, string>()
        {
            {"active_energy_import","总有功电能(kWh)"},
            {"current_L1","A相电流(A)"},
            {"current_L2","B相电流(A)"},
            {"current_L3","C相电流(A)"},
            {"power_factor","总功率因数(-)"},
            {"active_power","有功功率(kW)"}
        };

        /// <summary>
        /// 不是第三方设备属性
        /// </summary>
        public readonly Dictionary<string, string> _dicByProperty = new Dictionary<string, string>()
        {
            {"activeenergy","active_energy_import"},
            {"forwardactivepower_tariff1","active_energy_import"},
            {"forwardactivepower_tariff2","active_energy_import"},
            {"ia","current_l1"},
            {"ib","current_l2"},
            {"ic","current_l3"},
            {"powfactor","power_factor"},
            {"p","active_power"}
            //{"q","active_power_import"},
            //{"s","active_power_import"}
        };

        /// <summary>
        /// 定义设备是否发送数据的集合结构
        /// </summary>
        public readonly Dictionary<string, string> _dicData = new Dictionary<string, string>();

        /// <summary>
        /// 定义一个第三方二进制设备的集合(1)
        /// </summary>
        public readonly List<BitPointDto> bitPointList = new List<BitPointDto>();

        /// <summary>
        /// 设备类型集合
        /// </summary>
        public readonly Dictionary<string, (string, string)> _deviceTypeMappings = new Dictionary<string, (string, string)>()
        {
            {"42",("Meter", "PAC1020")},
            {"31",("Meter", "PAC3120")},
            {"30",("Meter", "PAC3220") },
            {"5",("Meter", "PAC4200") },
            {"8",("MCCB", "3VA") },
            {"6",("ACB", "3WL") },
            {"40",("ACB", "3WA") },
            {"4",("Meter", "PAC3200") },
            {"10",("Gateway", "COM800") }
        };

        /// <summary>
        /// 特殊字段时间得处理
        /// </summary>
        public readonly Dictionary<string, string> _subStationTags = new Dictionary<string, string>()
        {
            {"SubStation_Loss","SubStation_Loss"},
            {"SubStation_Percentage","SubStation_Percentage"},
            {"SubStation_Safety_Scope","SubStation_Safety_Scope"}
        };

    }
}
