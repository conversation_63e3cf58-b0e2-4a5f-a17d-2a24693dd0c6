﻿using SqlSugar;

namespace Siemens.PanelManager.Model.Database.Asset
{
    [SugarTable("asset_data_point_directory")]
    public class AssetDataPointDirectory: LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public long Id { get; set; }

        [Uniqueness]
        [SugarColumn(ColumnName = "asset_model", Length = 50, IsNullable = true)]
        public string? AssetModel { get; set; }

        [SugarColumn(ColumnName = "asset_level", IsNullable = true)]
        public AssetLevel? AssetLevel { get; set; }

        [Uniqueness]
        [SugarColumn(ColumnName = "name", IsNullable = true, Length = 256)]
        public string Name { get; set; } = string.Empty;

        [Uniqueness]
        [SugarColumn(ColumnName = "parent_name", IsNullable = true, Length = 256)]
        public string ParentName { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "language_key", IsNullable = true, Length = 256)]
        public string LanguageKey { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "sort", IsNullable = true)]
        public int? Sort { get; set; }
    }
}
