﻿using Microsoft.AspNetCore.Mvc;
using System.Xml.Linq;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class ChartParam
    {
        /// <summary>
        /// Day:0,Month:1,Year:2,Custom:3,24H:4
        /// </summary>
        [FromQuery(Name = "dateType")]
        public ChartDateType? DateType { get; set; }
        [FromQuery(Name = "startDate")]
        public string? StartDate { get; set; }
        [FromQuery(Name = "endDate")]
        public string? EndDate { get; set; }
        [FromQuery(Name = "chartCodes")]
        public string ChartCodes { get; set; } = string.Empty;
        [FromQuery(Name = "searchType")]
        public string SearchType { get; set; } = string.Empty;

    }
}
