import { Modal } from "@siemens/ix-react";
import React, { useRef } from "react";
import loadingLogo from "./loading.svg";
import "./loadingMark.css";
import { showModal } from "@siemens/ix-react";
import MessageModal from "./MessageModal";

export default function LoadingModal(prop) {
  const modalRef = useRef("modalRef");
  if (typeof prop.finishFuction !== "function") {
    return (
      <MessageModal
        title="loading加载失败"
        message="缺少finish方法"
      ></MessageModal>
    );
  }
  let timeout = 10;
  if (!!prop.timeout) {
    timeout = prop.timeout;
  }
  let runCount = 0;
  const interval = setInterval(() => {
    const hasFinish = prop.finishFuction();
    if (hasFinish) {
      clearInterval(interval);
      modalRef.current?.close(true);
    }
    runCount++;
    if (runCount === timeout) {
      clearInterval(interval);
      modalRef.current?.close(false);
      setTimeout(() => {
        showModal({
          icon: "alarm",
          iconColor: "color-alarm",
          content: (
            <MessageModal title="loading" message="超时了-"></MessageModal>
          ),
        });
      }, 1000);
    }
  }, 1000);
  return (
    <Modal ref={modalRef}>
      <div className="modal-body">
        <div className="loading-body">
          <img src={loadingLogo} className="loading-logo" alt="logo" />
          <div className="loading-message">
            <span className="text-l-title loading-title">{prop.title}</span>
          </div>
        </div>
      </div>
    </Modal>
  );
}
