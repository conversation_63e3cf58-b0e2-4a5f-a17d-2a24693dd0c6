﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class NetworkDiagramResult
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public int? AssetId { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? AssetName { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? AssetType { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? AssetModel { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? AssetIpAddress { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? ObjectId { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? UdcNetworkInterface { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? IpAddress { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public int? Port { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? Netmask { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? Gateway { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? MacAddress { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? PlantIdentifier { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? OrderNumber { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? FirmwareVersion { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? BootloaderVersion { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? ItemId { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? InterfaceName { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public bool IsGateway { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public int? UnitId { get; set; }

        /// <summary>
        /// 导入状态：0(已导入)；1(未导入)；2(未发现)
        /// </summary>
        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public int? ImportStatus { get; set; }

        /// <summary>
        /// 网络状态：0(离线)；1(在线)
        /// </summary>
        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public int? NetStatus { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public List<NetworkDiagramResult> Childs { get; set; } = new List<NetworkDiagramResult>();

        [JsonIgnore]
        public bool DeviceIsGateway { get; set; }
    }
}
