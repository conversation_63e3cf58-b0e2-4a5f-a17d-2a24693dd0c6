﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Quartz;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.DeviceDataFlow.ActorRefs;
using Siemens.PanelManager.DeviceDataFlow.Model;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Server.DataPoint;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.GetDeviceStatusByUDCApi
{
    [DisallowConcurrentExecution]
    internal class GetDeviceStatusByUdcApiJob : JobBase
    {
        public override string Name => "GetDeviceStatusByUdcApiJob";
        private readonly ILogger _logger;
        private readonly IServiceProvider _provider;
        private readonly DataPointServer _dataPointServer;

        public GetDeviceStatusByUdcApiJob(ILogger<GetDeviceStatusByUdcApiJob> logger,
            IServiceProvider provider,
            DataPointServer dataPointServer)
        {
            _logger = logger;
            _provider = provider;
        }

        public override async Task Execute()
        {
            var client = new HttpClient();
            client.Timeout = TimeSpan.FromSeconds(30);
            using (var sqlClient = _provider.GetRequiredService<ISqlSugarClient>()) 
            {
                var assets = await sqlClient.Queryable<AssetInfo>()
                    .Where(a => a.AssetLevel == AssetLevel.Device && a.ObjectId != null)
                    .ToArrayAsync();

                var items = await _dataPointServer.GetDeviceItems(client);

                foreach (var asset in assets)
                {
                    var item = items.FirstOrDefault(i => i.Id == asset.ObjectId);
                    if (item != null) 
                    {
                        var dataPoints = _dataPointServer.GetDataPointInfos(asset.AssetLevel, asset.AssetType, asset.AssetModel);
                        var m
                        var response = await client.GetAsync($"{baseUrl}/api/v1/items/{asset.ObjectId}/datapoints");

                        if (response == null || response.StatusCode != System.Net.HttpStatusCode.OK)
                        {
                            string message = "返回为空";
                            if (response != null)
                            {
                                message = await response.Content.ReadAsStringAsync();
                            }
                            _logger.LogInformation($"{asset.ObjectId} Api获取失败: {message}");
                            continue;
                        }

                        var dataStr = await response.Content.ReadAsStringAsync();
                        var datas = JsonConvert.DeserializeObject<DataPointRespose>(dataStr);
                        if (datas == null) continue;
                        if (datas.ObjectId != asset.ObjectId) continue;

                        if (datas.Embedded != null && datas.Embedded.Items != null)
                        {
                            var inputData = new AssetInputData();
                            inputData.ObjectId = asset.ObjectId;
                            foreach (var item in datas.Embedded.Items)
                            {
                                inputData.Datas.Add(item.InternalName, item.Value);
                            }

                            var assetInputRef = _provider.GetRequiredService<AssetDataProxyRef>();
                            assetInputRef.InputData(inputData);
                        }


                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"{asset.ObjectId} Api获取失败");
                    }
                } 
            }
        }
    }
}

