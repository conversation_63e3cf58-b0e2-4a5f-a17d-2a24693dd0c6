﻿using Microsoft.Extensions.Configuration;
using System.Security.Cryptography;
using System.Text;

namespace Siemens.PanelManager.Common.General
{
    public class LocalSecurityFunc
    {
        private readonly IConfiguration _configuration;
        public LocalSecurityFunc(IConfiguration configuration) 
        {
            _configuration = configuration;
        }

        public string GetSecurityKey(string key) 
        {
            var privateKey = _configuration.GetValue<string>("LocalPrivateKey");
            if (string.IsNullOrEmpty(privateKey)) return string.Empty;

            RSA rsa = RSA.Create();
            rsa.ImportRSAPrivateKey(Convert.FromBase64String(privateKey), out _);
            var signData = rsa.SignData(Encoding.UTF8.GetBytes($"Siemens-{key}"), HashAlgorithmName.SHA512, RSASignaturePadding.Pkcs1);
            return Convert.ToBase64String(signData);
        }
    }
}
