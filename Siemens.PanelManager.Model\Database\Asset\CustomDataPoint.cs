﻿using CsvHelper.Configuration.Attributes;
using Newtonsoft.Json;
using SQLitePCL;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Asset
{
    [SugarTable("asset_custom_data_point")]
    public class CustomDataPoint : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [SugarColumn(ColumnName = "target_asset_id", IsNullable = false)]
        public int TargetAssetId { get; set; }

        [SugarColumn(ColumnName = "target_data_point_name", IsNullable = false, Length = 256)]
        public string TargetDataPointName { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "real_asset_id", IsNullable = false)]
        public int RealAssetId { get; set; }

        [SugarColumn(ColumnName = "real_data_point_name", IsNullable = false, Length = 256)]
        public string RealDataPointName { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "value_mapping_json", IsNullable = false, Length = 2048)]
        public string ValueMappingStr { get; set; } = string.Empty;

        private ReadOnlyDictionary<string, string>? _valueMapping = null;
        [SugarColumn(IsIgnore = true)]
        public ReadOnlyDictionary<string, string> ValueMapping
        {
            get
            {
                if (_valueMapping == null)
                {
                    _valueMapping = JsonConvert.DeserializeObject<ReadOnlyDictionary<string, string>>(ValueMappingStr);
                }

                if (_valueMapping == null)
                {
                    _valueMapping = ReadOnlyDictionary<string, string>.Empty;
                }

                return _valueMapping;
            }
        }
    }
}
