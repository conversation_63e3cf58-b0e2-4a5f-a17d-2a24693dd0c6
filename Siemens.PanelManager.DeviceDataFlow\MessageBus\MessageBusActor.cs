﻿using Akka.Actor;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.DeviceDataFlow.StaticData;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.DataFlow;
using Siemens.PanelManager.Model.UDC;
using Siemens.PanelManager.Server.AssetDataPoint;
using Siemens.PanelManager.Server.DataPoint;
using SqlSugar;
using System.Diagnostics;
using System.Globalization;

namespace Siemens.PanelManager.DeviceDataFlow.MessageBus
{
    internal class MessageBusActor : ReceiveActor
    {
        private readonly IServiceProvider _provider;
        private readonly DataPointServer _dataPointServer;
        private readonly AssetDataPointInfoServer _assetDataPointInfoServer;
        private readonly SiemensCache _cache;
        private readonly ILogger _logger;
        private ISqlSugarClient _client;
        private StreamWriter? _sw;
        private int _length = 0;
        private Dictionary<string, Func<string, string>> _funcList = new Dictionary<string, Func<string, string>>();

        public MessageBusActor(ILogger<MessageBusActor> logger, SiemensCache cache, IServiceProvider provider, DataPointServer server, SqlSugarScope sqlClient, AssetDataPointInfoServer assetDataPointInfoServer)
        {
            _client = sqlClient;
            _assetDataPointInfoServer = assetDataPointInfoServer;
            _logger = logger;
            _cache = cache;
            _provider = provider;
            _dataPointServer = server;
            ReceiveAsync<DeviceDataPointsResult>(ChangeDataPointsDataToAssetInput);
            _funcList.Add("TempMotorLogic", TempMotorLogic);
            _funcList.Add("KelvinToCelsius", KelvinToCelsius);
            CountManagerTools.InitLogger(logger);
        }

        private async Task ChangeDataPointsDataToAssetInput(DeviceDataPointsResult pointsResult)
        {
            var assetInfo = _cache.Get<AssetSimpleInfo>($"AssetObjectId:{pointsResult.ItemId}");
            if (assetInfo == null) return;

            var logger = AkkaRuntimeLoggerManager.GetLogger("MessageBusActor");
            var k = logger?.Start();
            var count = CountManagerTools.GetCount("MessageBusActor");
            count.Count++;

            #region 写日志
            {
                var config = _provider.GetRequiredService<IConfiguration>();
                var needSaveLog = config.GetValue("NeedSaveDataPointsLog", false);
                if (needSaveLog)
                {
                    if (_sw == null)
                    {
                        var fileName = Path.Combine(Directory.GetCurrentDirectory(), "logs", $"dataPoints_{DateTime.Now.ToString("MMddHHmmss")}.log");
                        _sw = new StreamWriter(fileName);
                    }
                    if (_length >= 1000)
                    {
                        _length = 0;
                        _sw.Close();
                        var fileName = Path.Combine(Directory.GetCurrentDirectory(), "logs", $"dataPoints_{DateTime.Now.ToString("MMddHHmmss")}.log");
                        _sw = new StreamWriter(fileName);
                    }

                    _sw.WriteLine(JsonConvert.SerializeObject(pointsResult));
                    _length++;
                }
            }
            #endregion

            #region 海尔临时日志

            #endregion

            var time = DateTime.Now;
            if (DateTime.TryParse(pointsResult.TimeStamp, out var time1))
            {
                time = time1;
            }

            var inputData = new AssetInputData
            {
                AssetId = assetInfo.AssetId,
                AssetName = assetInfo.AssetName,
                InputTime = time,
                ObjectId = assetInfo.ObjectId,
                Datas = new Dictionary<string, string>()
            };

            if ("UDC".Equals(pointsResult.From))
            {
                inputData.DataSources = "UDC";
            }

            List<AssetDataPointInfo> dataPoints;

            ////通用设备中的其他设备
            //if ("GENERALDEVICE".Equals(assetInfo.AssetType, StringComparison.OrdinalIgnoreCase)
            //    && "OTHER".Equals(assetInfo.AssetModel, StringComparison.OrdinalIgnoreCase))
            //{
            //    dataPoints = await _dataPointServer.GetOtherDeviceDataPoints(assetInfo.AssetId);
            //} // 通用设备
            //else if ("GENERALDEVICE".Equals(assetInfo.AssetType, StringComparison.OrdinalIgnoreCase)
            //         && "GENERALDEVICE".Equals(assetInfo.AssetModel, StringComparison.OrdinalIgnoreCase))
            //{
            //    dataPoints = _assetDataPointInfoServer.GetCacheDataPoints(assetInfo.AssetId);
            //}
            //else
            //{
            //    dataPoints = await _dataPointServer.GetDataPointInfos(AssetLevel.Device, assetInfo.AssetType, assetInfo.AssetModel);
            //}

            if ("Other".Equals(assetInfo.AssetModel, StringComparison.OrdinalIgnoreCase)
                || "GeneralDevice".Equals(assetInfo.AssetType, StringComparison.OrdinalIgnoreCase)
                || ("Modbus".Equals(assetInfo.AssetModel, StringComparison.OrdinalIgnoreCase)
                && "Gateway".Equals(assetInfo.AssetType, StringComparison.OrdinalIgnoreCase)))
            {
                foreach (var item in pointsResult.Embedded.Items)
                {
                    if (string.IsNullOrEmpty(item.InternalName)) continue;
                    var value = item.Value;
                    inputData.Datas.TryAdd(item.InternalName, value);
                }
            }
            else // 其他设备
            {
                dataPoints = await _dataPointServer.GetDataPointInfos(AssetLevel.Device, assetInfo.AssetType, assetInfo.AssetModel);
                if (dataPoints.Count > 0)
                {
                    foreach (var item in pointsResult.Embedded.Items)
                    {
                        if (string.IsNullOrEmpty(item.InternalName)) continue;
                        var point = dataPoints.FirstOrDefault(d => item.InternalName.Equals(d.UdcCode, StringComparison.OrdinalIgnoreCase));
                        if (point == null) continue;
                        var value = item.Value;
                        var funcName = point.FuncName;
                        if (!string.IsNullOrEmpty(funcName) && _funcList.TryGetValue(funcName, out var func))
                        {
                            value = func(value);
                        }
                        inputData.Datas.TryAdd(point.Code, value);
                    }
                }
            }

            MessageBusContext.MessageRegister.OutputMessage(inputData);
            if (k.HasValue)
            {
                logger?.Stop(k.Value);
            }
        }

        /// <summary>
        /// 测温添加
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private string TempMotorLogic(string value)
        {
            if (!"32767".Equals(value))
            {
                if (decimal.TryParse(value, out decimal v))
                {
                    return (v / 10m).ToString(CultureInfo.InvariantCulture);
                }
            }
            return value;
        }

        /// <summary>
        /// 开氏度转摄氏度
        /// </summary>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        private string KelvinToCelsius(string value)
        {
            if (decimal.TryParse(value, out decimal v))
            {
                return (v - 273.15m).ToString(CultureInfo.CurrentCulture);
            }

            return value;
        }
    }
}
