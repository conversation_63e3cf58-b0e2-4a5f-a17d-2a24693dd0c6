import { IxButton } from "@siemens/ix-react";
import { useNavigate } from "react-router-dom";
import "./Error.css";

export default function ErrorUI() {
  const navigate = useNavigate();
  return (
    <div className="error">
      <div className="error-header">
        <span className="text-h2">请求错误</span>
        <IxButton onClick={() => navigate("/")}>返回首页</IxButton>
      </div>
    </div>
  );
}
