﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.Database.UdcSyncConfig;
using Siemens.PanelManager.SyncService.UdcModels;
using SqlSugar;

namespace Siemens.PanelManager.SyncService.Sqlite
{
    public class SyncEntity
    {
        protected readonly IDbContextFactory<UdcContext> _contextFactory;
        protected readonly IConfiguration _configuration;
        private ISqlSugarClient _client;
        protected readonly IAssetDataProxyRef _assetDataProxyRef;

        protected readonly int _perQueryCount;
        public string InfluxdbUrl { protected get; init; }
        public string InfluxdbUserName { protected get; init; }
        public string InfluxdbPassword { protected get; init; }
        public string InfluxdbOrgName { protected get; init; }
        public string InfluxdbBucket { protected get; init; }

        public string? ConfigName { get; init; }

        public SyncEntity(IDbContextFactory<UdcContext> contextFactory,
            IConfiguration configuration,
            ISqlSugarClient client,
            IAssetDataProxyRef assetDataProxyRef)
        {
            _contextFactory = contextFactory;
            _configuration = configuration;
            _client = client;
            _assetDataProxyRef = assetDataProxyRef;

            InfluxdbUrl = _configuration["Influxdb:Url"];
            InfluxdbUserName = _configuration["Influxdb:UserName"];
            InfluxdbPassword = _configuration["Influxdb:Password"];
            InfluxdbOrgName = _configuration["Influxdb:OrgName"];
            InfluxdbBucket = _configuration["Influxdb:Bucket"];

            if (int.TryParse(configuration["PerQueryCount"], out var tempQueryCount))
            {
                _perQueryCount = tempQueryCount;
            }
            else
            {
                _perQueryCount = 5000;
            }
        }

        public long GetLastTimestamp(long minusRedundancy = 0)
        {
            string lastTime = string.Empty;
            long lastTimestamp = -1;

            var udcSyncConfig = _client.Queryable<UdcSyncConfig>().Single(a => a.Name == ConfigName);

            if (udcSyncConfig != null)
            {
                lastTime = udcSyncConfig.Value;
            }

            if (!string.IsNullOrWhiteSpace(lastTime))
            {
                long.TryParse(lastTime, out lastTimestamp);
            }

            return lastTimestamp + 1 - minusRedundancy;
        }

        public void SaveLastTimestamp(long lastTimestamp)
        {
            string operationUser = "udc sync job";

            var udcSyncConfig = _client.Queryable<UdcSyncConfig>().Single(a => a.Name == ConfigName);
            if (udcSyncConfig != null)
            {
                udcSyncConfig.Value = lastTimestamp.ToString();
                udcSyncConfig.UpdatedBy = operationUser;
                udcSyncConfig.UpdatedTime = DateTime.Now;
                _client.Updateable(udcSyncConfig).ExecuteCommand();
            }
            else
            {
                _client.Insertable(new UdcSyncConfig
                {
                    Name = ConfigName ?? string.Empty,
                    Value = lastTimestamp.ToString(),
                    CreatedBy = operationUser,
                    CreatedTime = DateTime.Now,
                }).ExecuteCommand();
            }
        }
    }


}
