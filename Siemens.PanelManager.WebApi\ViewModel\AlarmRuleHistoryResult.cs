﻿using Siemens.PanelManager.Common;
using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.WebApi.StaticContent;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class AlarmRuleHistoryResult
    {
        public AlarmRuleHistoryResult(AlarmRuleChangeHistory history, MessageContext messageContext) 
        {
            AlarmName = history.RuleName;
            AlarmLevel = (int)history.Severity;
            var key = $"Alarm_RuleOpt_{history.Operation.ToString()}";
            OptType = messageContext.GetString(key) ?? key;
            Time = history.Timestamp.GetDateTimeBySec();
            Rule = history.RuleInfo;
            UpdatedBy = history.UpdatedBy;
        }
        public string AlarmName { get; set; }
        public int AlarmLevel { get; set; }
        public string OptType { get; set; }
        public DateTime Time { get; set; }
        public string? Rule { get; set; }
        public string UpdatedBy { get; set; }
    }
}
