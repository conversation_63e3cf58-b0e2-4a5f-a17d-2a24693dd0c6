﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.Model.UDC
{
    public class DeviceItemsResult : UdcListResulBase<DeviceItemInfo>
    {
    }

    public class DeviceItemInfo : IUdcData
    {
        [JsonProperty(PropertyName = "id")]
        public string Id { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "type_name")]
        public string TypeName { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "name")]
        public string Name { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "plant_id")]
        public string PlantId { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "address")]
        public string Address { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "mbgw_address")]
        public string MbgwAddress { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "archiving")]
        public DeviceStatusInfo? Archiving { get; set; }
        [JsonProperty(PropertyName = "monitoring")]
        public DeviceStatusInfo? Monitoring { get; set; }
        [JsonProperty(PropertyName = "parent_id")]
        public string ParentId { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "child_ids")]
        public string[] ChildIds { get; set; } = new string[0];
    }

    public class DeviceStatusInfo
    {
        [JsonProperty(PropertyName = "state")]
        public string State { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "messages")]
        public string[] Messages { get; set; } = new string[0];
        [JsonProperty(PropertyName = "breaker", NullValueHandling = NullValueHandling.Ignore)]
        public BreakerStatusInfo? Breaker { get; set; }
    }

    public class BreakerStatusInfo
    {
        [JsonProperty(PropertyName = "state")]
        public string Status { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "messages")]
        public string[] Messages { get; set; } = new string[0];
    }
}
