﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class TopologySaveReceipt
    {
        /// <summary>
        /// 操作结果
        /// 1:      数据更新成功
        /// 10:     数据被更新
        /// 11:     数据被删除
        /// 99:     数据更新失败
        /// </summary>
        public int ResultCode { get; set; }
        /// <summary>
        /// 图纸Id
        /// </summary>
        public int TopologyId { get; set; }
        /// <summary>
        /// 回执号
        /// </summary>
        public string ReceiptCode {  get; set; } = Guid.NewGuid().ToString();
        /// <summary>
        /// 最后更新人
        /// </summary>
        public string? LastUpdatedBy {  get; set; }
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? LastUpdatedDate { get; set; }
        /// <summary>
        /// 版本标识号
        /// </summary>
        public string Flag {  get; set; } = string.Empty;
        public DateTime Time { get; set; } = DateTime.Now;
    }

    public class TopologySaveReceiptParam
    {
        /// <summary>
        /// 用户选择
        /// 1:      覆盖
        /// 10:     放弃
        /// </summary>
        [FromQuery(Name = "opt")]
        public int Operation {  get; set; }
        /// <summary>
        /// 回执号
        /// </summary>
        [FromRoute(Name = "code")]
        public string ReceiptCode { set; get; } = string.Empty;
    }

    internal class TopologyReceiptData
    {
        public TopologyReceiptData(JObject obj, string flag, int userId, string topologyType, int topologyId, int resultCode, string sessionId) 
        {
            TopologyId = topologyId;
            TopologyType = topologyType;
            UserId = userId;
            ResultCode = resultCode;
            Data = obj;
            Flag = flag;
            SessionId = sessionId;
        }
        public int TopologyId { get; set; }
        public string TopologyType { get; set; }
        public int UserId { get; set; }
        public string SessionId { get; set; }
        public JObject Data { get; set; }
        /// <summary>
        /// 操作结果
        /// 10:     数据被更新
        /// 11:     数据被删除
        /// </summary>
        public int ResultCode { set; get; }
        public string Flag {  get; set; }
    }

    public class nodeDatasDto
    {
        [JsonProperty("key")]
        public int Key { get; set; }

        /// <summary>
        /// 字段名称
        /// </summary>
        [JsonProperty("electricalType")]
        public string? ElectricalType { get; set; }

        /// <summary>
        /// 字段名称
        /// </summary>
        [JsonProperty("assetId")]
        public int AssetId { get; set; }
    }
}
