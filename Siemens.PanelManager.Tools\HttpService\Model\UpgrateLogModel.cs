﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Tools.HttpService.Model
{
    internal class UpgrateLogModel
    {
        [JsonProperty(PropertyName = "message")]
        public string Message { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "lastIndex")]
        public long LastIndex { get; set; }
    }
}
