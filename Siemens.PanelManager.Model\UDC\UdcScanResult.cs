﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.Model.UDC
{
    public class UdcScanResult
    {
        [JsonProperty(PropertyName = "type_name")]
        public string? TypeName { get; set; }

        [JsonProperty(PropertyName = "type_display_name")]
        public string? TypeDisplayName { get; set; }

        [JsonProperty(PropertyName = "ip_address")]
        public string? IpAddress { get; set; }

        [JsonProperty(PropertyName = "port")]
        public int? Port { get; set; }

        [JsonProperty(PropertyName = "netmask")]
        public string? Netmask { get; set; }

        [JsonProperty(PropertyName = "gateway")]
        public string? Gateway { get; set; }

        [JsonProperty(PropertyName = "mac_address")]
        public string? MacAddress { get; set; }

        [JsonProperty(PropertyName = "plant_identifier")]
        public string? PlantIdentifier { get; set; }

        [JsonProperty(PropertyName = "order_number")]
        public string? OrderNumber { get; set; }

        [JsonProperty(PropertyName = "firmware_version")]
        public string? FirmwareVersion { get; set; }

        [JsonProperty(PropertyName = "bootloader_version")]
        public string? BootloaderVersion { get; set; }

        [JsonProperty(PropertyName = "item_id")]
        public string? ItemId { get; set; }

        [JsonProperty(PropertyName = "is_gateway")]
        public bool IsGateway { get; set; }

        [JsonProperty(PropertyName = "unit_id")]
        public int? UnitId { get; set; }
    }

}
