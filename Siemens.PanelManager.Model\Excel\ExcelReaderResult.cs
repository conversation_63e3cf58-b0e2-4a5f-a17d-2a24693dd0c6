﻿namespace Siemens.PanelManager.Model.Excel
{
    public class ExcelReaderResult
    {
        public ExcelReaderResult(string sheetName, int page, IDictionary<string, object>[] datas) 
        {
            SheetName = sheetName;
            Page = page;
            Count = datas.Length;
            Datas = datas;
        }

        public string SheetName { get; private set; }
        public int Page { get; private set; }
        public int Count { get; private set; }
        public IDictionary<string, object>[] Datas { get; private set; }
    }
}
