﻿using Akka.Actor;
using Akka.Util.Internal;
using InfluxDB.Client.Api.Domain;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Common.Model;
using Siemens.PanelManager.DeviceDataFlow.MessageBus;
using Siemens.PanelManager.DeviceDataFlow.StaticData;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.Alarm;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Common;
using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.DataFlow;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.Server.AssetDataPoint;
using Siemens.PanelManager.Server.Common;
using Siemens.PanelManager.Server.DataPoint;
using SqlSugar;
using SqlSugar.Extensions;
using System.Collections.Generic;
using System.Drawing.Text;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.DeviceDataFlow.DataFlowActors
{
    internal class DeviceActor : AssetActorBase
    {
        private readonly IAlarmRef _saveAlarmRef;

        public DeviceActor(ILogger<DeviceActor> logger, IServiceProvider provider, SiemensCache cache, AssetSimpleInfo simpleInfo)
            : base(simpleInfo, provider, logger, cache)
        {
            _saveAlarmRef = Provider.GetRequiredService<IAlarmRef>();
            LoggerName = "DeviceActor";
            NeedLog = false;
        }

        private const string SwitchDataPointCode = "switch_state";
        private const string BreakerPositionDataPointCode = "breaker_connection_state";

        private bool? _isTempMotor = null;
        private bool IsTempMotor
        {
            get
            {
                if (_isTempMotor == null)
                {
                    var simpleInfo = AssetSimpleInfo;
                    _isTempMotor = "TempMeasurement".Equals(simpleInfo.AssetType) && "SiemensTempMeasurement".Equals(simpleInfo.AssetModel);
                }
                return _isTempMotor ?? false;
            }
        }
        private bool? _isOtherTempMotor = null;
        private bool IsOtherTempMotor
        {
            get
            {
                if (_isOtherTempMotor == null)
                {
                    var simpleInfo = AssetSimpleInfo;
                    _isOtherTempMotor = "TempMeasurement".Equals(simpleInfo.AssetType) && "Other".Equals(simpleInfo.AssetModel);
                }
                return _isOtherTempMotor ?? false;
            }
        }

        private bool? _isBreaker = null;
        private bool IsBreaker
        {
            get
            {
                if (_isBreaker == null)
                {
                    var breakerTypes = new string[] { "ACB", "MCCB", "MCB" };
                    _isBreaker = breakerTypes.Contains(AssetSimpleInfo.AssetType);
                }

                return _isBreaker ?? false;
            }
        }

        private bool? _isCOM800 = null;
        private bool IsCOM800
        {
            get
            {
                if (_isCOM800 == null)
                {
                    _isCOM800 = AssetSimpleInfo.AssetModel == "COM800";
                }

                return _isCOM800 ?? false;
            }
        }

        //通用设备
        private bool? _isGeneralEquipment = null;
        private bool IsGeneralEquipment
        {
            get
            {
                if (_isGeneralEquipment == null)
                {
                    //_isGeneralEquipment = AssetSimpleInfo.AssetModel == "GeneralDevice" && AssetSimpleInfo.AssetType == "GeneralDevice";

                    _isGeneralEquipment = "Other".Equals(AssetSimpleInfo.AssetModel, StringComparison.OrdinalIgnoreCase)
                        || "GeneralDevice".Equals(AssetSimpleInfo.AssetType, StringComparison.OrdinalIgnoreCase)
                        || ("Modbus".Equals(AssetSimpleInfo.AssetModel, StringComparison.OrdinalIgnoreCase) 
                        && "Gateway".Equals(AssetSimpleInfo.AssetType, StringComparison.OrdinalIgnoreCase));
                }

                return _isGeneralEquipment ?? false;
            }
        }


        private bool hasLoadFunc = false;

        private Func<string, string, Dictionary<string, string>, bool> _logicFunc = (k, v, input) => { return false; };

        /// <summary>
        /// 输入数据入口
        /// </summary>
        /// <param name="inputData"></param>
        /// <returns></returns>
        protected override async Task InputDataFunc(AssetInputData inputData)
        {
            var logger = AkkaRuntimeLoggerManager.GetLogger(LoggerName);
            try
            {
                var k = logger?.Start();
                var changeData = new AssetChangeData()
                {
                    AssetId = AssetSimpleInfo.AssetId,
                    AssetLevel = AssetSimpleInfo.AssetLevel,
                    AssetName = AssetSimpleInfo.AssetName,
                    AssetModel = AssetSimpleInfo.AssetModel,
                    AssetType = AssetSimpleInfo.AssetType,
                };

                TemperatureMonitorInfo[] temperatures = new TemperatureMonitorInfo[0];

                if (IsTempMotor)
                {
                    temperatures = await GetMonitorList();
                    foreach (var inputValue in inputData.Datas)
                    {
                        var match = Regex.Match(inputValue.Key, "^Sensor_([\\d]+)$");
                        if (match.Success)
                        {
                            var no = int.Parse(match.Groups[1].Value);
                            var monitor = temperatures.FirstOrDefault(t => t.ModbusPoint == 40001 + no);
                            if (monitor != null && !"32767".Equals(inputValue.Value)
                                && monitor.BindAssetId.HasValue
                                && !string.IsNullOrEmpty(monitor.DataPoint))
                            {
                                var newInputData = new AssetInputData
                                {
                                    AssetId = monitor.BindAssetId,
                                    InputTime = inputData.InputTime,
                                    ObjectId = "NaN",
                                    Datas = new Dictionary<string, string>()
                                    {
                                        [monitor.DataPoint] = inputValue.Value
                                    }
                                };

                                MessageBusContext.MessageRegister.OutputMessage(newInputData);
                            }
                        }
                    }
                }
                if (IsOtherTempMotor)
                {
                    temperatures = await GetMonitorList();
                    var needRemoveData = new List<string>();
                    //foreach (var dataInput in inputData.Datas)
                    //{
                    //    //第三方测温的温度范围是-50到125，不在这个范围的数据舍弃
                    //    if (decimal.TryParse(dataInput.Value, out decimal temp))
                    //    {
                    //        if (temp > 125 || temp < -50)
                    //        {
                    //            needRemoveData.Add(dataInput.Key);
                    //        }
                    //    }
                    //}
                    var keys = inputData.Datas.Keys.ToList();
                    for (int i = 0; i < keys.Count; i++)
                    {
                        var key = keys[i];
                        if (decimal.TryParse(inputData.Datas[key], out decimal temp))
                        {
                            inputData.Datas[key] = Math.Round(temp, 1).ToString();
                            //第三方测温的温度范围是-50到125，不在这个范围的数据舍弃
                            if (temp > 125 || temp < -50)
                            {
                                needRemoveData.Add(key);
                            }
                        }
                    }
                    foreach (var key in needRemoveData)
                    {
                        inputData.Datas.Remove(key);
                    }
                }

                if (k.HasValue)
                {
                    logger?.MarkByFilter(k.Value, "TempMotor", 1000000L);
                }
                if (IsCOM800)
                {
                    var com800Date = inputData.Datas.GetValueOrDefault("T");
                    if (!string.IsNullOrEmpty(com800Date) && DateTime.TryParse(com800Date, out var date))
                    {
                        inputData.Datas["T"] = date.ToString("yyyy-MM-dd HH:mm:ss");
                    }

                    var opHour = inputData.Datas.GetValueOrDefault("Op");

                    if (!string.IsNullOrEmpty(opHour) && int.TryParse(opHour, out var tempHour))
                    {
                        inputData.Datas["Op"] = (tempHour / 3600).ToString("N0");
                    }
                }

                if (k.HasValue)
                {
                    logger?.MarkByFilter(k.Value, "COM800", 1000000L);
                }

                //通用设备中二进制处理
                if (IsGeneralEquipment)
                {
                    var dpServer = Provider.GetRequiredService<AssetDataPointInfoServer>();
                    var alarmLogProxy = Provider.GetRequiredService<AlarmLogServer>();

                    var bitConfigs = alarmLogProxy.GetCacheBitConfigs(inputData.AssetId);

                    if (k.HasValue)
                    {
                        logger?.MarkByFilter(k.Value, "GeneralEquipment-[CacheBitConfigs]", 1000000L);
                    }

                    // 获取json数据中的点位信息
                    var dataPoints = dpServer.GetCacheDataPoints(inputData.AssetId ?? 0);

                    if (k.HasValue)
                    {
                        logger?.MarkByFilter(k.Value, "GeneralEquipment-[CacheDataPoints]", 1000000L);
                    }

                    if (bitConfigs == null)
                    {
                        bitConfigs = new List<BitPointDto>();
                    }

                    if (dataPoints == null)
                    {
                        dataPoints = new List<AssetDataPointInfo>();
                    }

                    var bitPoints = bitConfigs.Select(p => p?.PropertyEnName ?? string.Empty).Distinct().ToList();

                    var bitDataPoints = dataPoints.Where(p => p.IsBit).ToList();

                    if (bitDataPoints.Any())
                    {
                        foreach (var item in bitDataPoints)
                        {
                            if (inputData.Datas.ContainsKey(item.Code))
                            {
                                // 科学计数法处理
                                decimal val = Decimal.Parse(inputData.Datas[item.Code], System.Globalization.NumberStyles.Float);

                                var bitData = StringFunction.TenToBit(Convert.ToInt32(val));
                                // 十进制转二进制
                                inputData.Datas[item.Code] = bitData;

                                var subBitConfigs = bitConfigs.Where(b => b.PropertyEnName == item.Code).ToList();
                                var bits = StringFunction.BitToList(bitData);
                                foreach (var subBitConfig in subBitConfigs)
                                {
                                    var code = subBitConfig.BitCode ?? subBitConfig.BitName ?? string.Empty;
                                    if (string.IsNullOrEmpty(code)) continue;
                                    if (subBitConfig.BitNumber < bits.Count)
                                    {
                                        var value = bits[subBitConfig.BitNumber];
                                        if (string.IsNullOrEmpty(value)) continue;
                                        inputData.Datas.AddOrSet(code, value);
                                    }
                                }
                            }
                        }
                    }

                    if (k.HasValue)
                    {
                        logger?.MarkByFilter(k.Value, "GeneralEquipment-[BitDataPoints]", 1000000L);
                    }

                    #region 筛选需要生产告警内容
                    bitConfigs = bitConfigs.Where(b => b.EventType != -1).ToList();
                    #endregion

                    if (bitConfigs.Any())
                    {
                        var dicBitList = new Dictionary<string, List<string?>>();

                        foreach (var item in bitPoints)
                        {
                            if (!string.IsNullOrWhiteSpace(item) && inputData.Datas.TryGetValue(item, out var bitStr))
                            {
                                dicBitList.TryAdd(item, StringFunction.BitToList(bitStr));
                            }
                        }

                        foreach (var item in bitConfigs)
                        {
                            if (dicBitList.TryGetValue(item.PropertyEnName ?? string.Empty, out List<string?>? bits))
                            {
                                item.BitValue = bits![item.BitNumber];
                                var bitCode = item.BitCode ?? item.BitName ?? string.Empty;

                                if (!string.IsNullOrWhiteSpace(bitCode) && !string.IsNullOrEmpty(item.BitValue))
                                {
                                    if (inputData.Datas.TryGetValue(bitCode, out var val))
                                    {
                                        inputData.Datas[bitCode] = item.BitValue;
                                    }
                                    else
                                    {
                                        inputData.Datas.Add(bitCode, item.BitValue);
                                    }
                                }

                                //判断历史数据是否存在
                                var bitPoint = UniversalDeviceInfo.Instance.bitPointList
                                                .FirstOrDefault(p => p.AssetId == item.AssetId
                                                && p.PropertyEnName == item.PropertyEnName
                                                && p.BitName == item.BitName
                                                && p.BitNumber == item.BitNumber
                                                && p.BitValue == item.BitValue);

                                if (bitPoint == null)
                                {
                                    UniversalDeviceInfo.Instance.bitPointList.Add(new BitPointDto
                                    {
                                        AssetId = item.AssetId,
                                        PropertyEnName = item.PropertyEnName,
                                        BitName = item.BitName,
                                        BitNumber = item.BitNumber,
                                        BitValue = item.BitValue,
                                        EventType = item.EventType,
                                        AlarmLevel = item.AlarmLevel,
                                        Time = DateTime.Now
                                    });
                                }

                                var bitZeroPoint = UniversalDeviceInfo.Instance.bitPointList.FirstOrDefault(p => p.AssetId == inputData.AssetId
                                && p.PropertyEnName == item.PropertyEnName
                                && p.BitName == item.BitName
                                && p.BitNumber == item.BitNumber
                                && p.BitValue == "0");

                                var bitOnePoint = UniversalDeviceInfo.Instance.bitPointList.FirstOrDefault(p => p.AssetId == inputData.AssetId
                                && p.PropertyEnName == item.PropertyEnName
                                && p.BitName == item.BitName
                                && p.BitNumber == item.BitNumber
                                && p.BitValue == "1");

                                var bitPointEntity = new BitPointDto();

                                string? msg = "";

                                // 1 > 0
                                if (bitZeroPoint != null && bitOnePoint != null && bitOnePoint.Time >= bitZeroPoint.Time)
                                {
                                    bitPointEntity = bitZeroPoint;
                                    msg = bitPointEntity.BitName + "(0->1)动作";
                                }
                                // 0 > 1
                                else if (bitZeroPoint != null && bitOnePoint != null && bitZeroPoint.Time >= bitOnePoint.Time)
                                {
                                    bitPointEntity = bitOnePoint;
                                    msg = bitPointEntity.BitName + "(1->0)返回";
                                }
                                else
                                {
                                    bitPointEntity = null;
                                }
                                if (bitPointEntity != null)
                                {
                                    var entity = new AlarmLog()
                                    {
                                        AssetId = inputData.AssetId,
                                        SubstationName = AssetSimpleInfo.SubstationSimpleInfo?.AssetName,
                                        PanelName = AssetSimpleInfo.PanelSimpleInfo?.AssetName,
                                        CircuitName = AssetSimpleInfo.CircuitSimpleInfo?.AssetName,
                                        DeviceName = AssetSimpleInfo.AssetName,
                                        CreatedBy = "System",
                                        CreatedTime = DateTime.Now,
                                        UpdatedBy = "System",
                                        UpdatedTime = DateTime.Now,
                                        Severity = (AlarmSeverity)bitPointEntity.AlarmLevel,
                                        Status = (AlarmSeverity)bitPointEntity.AlarmLevel == AlarmSeverity.High ? AlarmLogStatus.New : AlarmLogStatus.None
                                    };

                                    switch (bitPointEntity.EventType)
                                    {
                                        // 消息
                                        case 9:
                                            entity.EventType = AlarmEventType.DeviceLog;
                                            entity.Message = msg;
                                            break;

                                        // 告警
                                        case 10:
                                            entity.EventType = AlarmEventType.Alarm;
                                            entity.Message = msg;
                                            break;

                                        default:
                                            break;
                                    }


                                    // 批量添加
                                    _saveAlarmRef.AppendAlarmLog(entity);

                                    // 资源释放
                                    UniversalDeviceInfo.Instance.bitPointList.Remove(bitPointEntity);
                                }
                            }
                        }
                    }

                    if (IsBreaker)
                    {
                        if (inputData.Datas.ContainsKey(SwitchDataPointCode))
                        {
                            var switchValue = inputData.Datas[SwitchDataPointCode];
                            if (switchValue == "0")
                            {
                                inputData.Datas.AddOrSet("Switch", "1");
                            }
                            else if (switchValue == "1")
                            {
                                inputData.Datas.AddOrSet("Switch", "2");
                            }
                        }

                        if ("ACB".Equals(AssetSimpleInfo.AssetType))
                        {
                            if (inputData.Datas.ContainsKey(BreakerPositionDataPointCode))
                            {
                                var switchValue = inputData.Datas[BreakerPositionDataPointCode];
                                if (switchValue == "0")
                                {
                                    inputData.Datas.AddOrSet("BreakerPosition", "0");
                                }
                                else if (switchValue == "1")
                                {
                                    inputData.Datas.AddOrSet("BreakerPosition", "1");
                                }
                            }
                            else
                            {
                                var breakerPositionDataPoints = new string[] { BreakerPositionDataPointCode };

                                if (!dataPoints.Any(dp => breakerPositionDataPoints.Contains(dp.Code)))
                                {
                                    inputData.Datas.AddOrSet("BreakerPosition", "1");
                                }
                            }
                        }
                    }
                }

                if (k.HasValue)
                {
                    logger?.MarkByFilter(k.Value, "GeneralEquipment-[BitAlarm]", 1000000L);
                }

                var datas = await ChangeDataPoint(inputData.Datas);
                var cacheKey = string.Format(Constant.AssetCurrentDataCacheKey, AssetSimpleInfo.AssetId);
                var currentStatus = Cache.GetHashData(cacheKey, datas.Keys.ToArray());
                var changeTriped = false;

                var changeDataCodes = new List<string>();

                foreach (var data in datas)
                {
                    if (currentStatus.ContainsKey(data.Key))
                    {
                        if (string.IsNullOrEmpty(data.Value))
                        {
                            changeDataCodes.Add(data.Key);
                        }
                        else if (currentStatus[data.Key] != data.Value)
                        {
                            if ("HasTriped".Equals(data.Key))
                            {
                                changeTriped = true;
                            }

                            changeDataCodes.Add(data.Key);
                        }
                    }
                    else
                    {
                        changeDataCodes.Add(data.Key);
                    }

                    changeData.ChangeDatas.Add(data.Key, data.Value);
                }

                if (IsBreaker && "UDC".Equals(inputData.DataSources))
                {
                    var dps = await GetBreakerCustomDataPoints();
                    if (dps != null && dps.Length > 0)
                    {
                        if (dps.Any(d => d.TargetDataPointName == "Switch"))
                        {
                            datas.Remove("Switch");
                            inputData.Datas.Remove("Switch");
                            changeData.ChangeDatas.Remove("Switch");
                        }

                        if (dps.Any(d => d.TargetDataPointName == "BreakerPosition"))
                        {
                            datas.Remove("BreakerPosition");
                            inputData.Datas.Remove("BreakerPosition");
                            changeData.ChangeDatas.Remove("BreakerPosition");
                        }
                    }
                }

                Cache.SetHashData(cacheKey, new Dictionary<string, string>(datas), 120);

                if (datas.Any(d => d.Key == "HaveAlarm"))
                {
                    Cache.SetHashData(cacheKey, new Dictionary<string, string>() { ["HaveAlarm"] = datas["HaveAlarm"] });
                }

                if (k.HasValue)
                {
                    logger?.MarkByFilter(k.Value, "ChangeData", 1000000L);
                }
                changeData.ChangeDataCode = changeDataCodes.ToArray();

                var proxy = Provider.GetRequiredService<IAssetDataProxyRef>();
                var actorManager = ActorManager.GetActorManagerNoException();

                if (actorManager != null)
                {
                    actorManager.DataPointRef.Tell(changeData);
                }

                UpdateCustomDataPoint(proxy, changeData);

                if (IsTempMotor)
                {
                    var list = changeData.ChangeDatas.ToArray();
                    foreach (var changeItem in list)
                    {
                        var match = Regex.Match(changeItem.Key, "^Sensor_([\\d]+)$");
                        if (match.Success)
                        {
                            var no = int.Parse(match.Groups[1].Value);
                            var monitor = temperatures.FirstOrDefault(t => t.ModbusPoint == 40001 + no);

                            if (monitor != null && monitor.BindAssetId.HasValue && !string.IsNullOrEmpty(monitor.DataPoint))
                            {
                                var newChangeData = new AssetChangeData()
                                {
                                    AssetId = monitor.BindAssetId ?? 0,
                                    ChangeDatas = new Dictionary<string, string>() { [monitor.DataPoint] = changeItem.Value },
                                    ChangeTime = DateTime.Now,
                                };

                                proxy.DataChanged(newChangeData);
                            }

                            continue;
                        }

                        match = Regex.Match(changeItem.Key, "^Sensor_([\\d]+)_connect$");
                        if (match.Success)
                        {
                            var no = match.Groups[1].Value;
                            if (changeItem.Value == "0")
                            {
                                changeData.ChangeDatas.Add($"Sensor_{no}_offline_time", DateTime.Now.ToString("yyyymmddHHMMss"));
                            }
                        }
                    }
                }
                else if (IsBreaker)
                {
                    if (AssetSimpleInfo.CircuitSimpleInfo != null && AssetSimpleInfo.AssetId != AssetSimpleInfo.CircuitSimpleInfo.AssetId)
                    {
                        var data = new Dictionary<string, string>();
                        if (changeData.ChangeDatas.ContainsKey("Switch"))
                        {
                            data.Add("Switch", changeData.ChangeDatas["Switch"]);
                        }

                        if (changeData.ChangeDatas.ContainsKey("HasTriped"))
                        {
                            data.Add("HasTriped", changeData.ChangeDatas["HasTriped"]);
                        }

                        var newChangeData = new AssetInputData
                        {
                            AssetId = AssetSimpleInfo.CircuitSimpleInfo.AssetId,
                            InputTime = changeData.ChangeTime,
                            Datas = data
                        };

                        proxy.InputData(newChangeData);
                    }
                }

                if (AssetSimpleInfo.CircuitSimpleInfo != null)
                {
                    var newInputData = new AssetInputData()
                    {
                        ParentId = AssetSimpleInfo.CircuitSimpleInfo.AssetId,
                        AssetId = AssetSimpleInfo.AssetId,
                        Datas = new Dictionary<string, string>(changeData.ChangeDatas)
                    };

                    proxy.InputData(newInputData);
                }

                if (AssetSimpleInfo.SubstationSimpleInfo != null)
                {
                    var changeDatas = new Dictionary<string, string>();
                    foreach (var key in changeDataCodes)
                    {
                        changeDatas.TryAdd(key, changeData.ChangeDatas[key]);
                    }

                    if (changeDatas.Count > 0)
                    {
                        var newInputData = new AssetInputData()
                        {
                            ParentId = AssetSimpleInfo.SubstationSimpleInfo.AssetId,
                            AssetId = AssetSimpleInfo.AssetId,
                            Datas = new Dictionary<string, string>(changeDatas)
                        };

                        proxy.InputData(newInputData);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Input Data: {JsonConvert.SerializeObject(inputData)}");
            }
        }

        /// <summary>
        /// 修改点位数据
        /// 通过 FuncName 对点位数据进行加工
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        private async Task<Dictionary<string, string>> ChangeDataPoint(Dictionary<string, string> data)
        {
            if (!hasLoadFunc)
            {
                var service = Provider.GetRequiredService<DataPointServer>();
                var dataPoints = await service.GetDataPointInfos(AssetLevel.Device, AssetSimpleInfo.AssetType, AssetSimpleInfo.AssetModel);
                if (IsTempMotor)
                {
                    _logicFunc += TempMotorLogic;
                }

                var extendFuncs = dataPoints.Where(d => !string.IsNullOrEmpty(d.Extend)).Select(d => JObject.Parse(d.Extend ?? string.Empty)).ToArray();
                var funcNames = new List<string>();
                foreach (var extendFunc in extendFuncs)
                {
                    if (extendFunc.ContainsKey("FumcName"))
                    {
                        var funcName = extendFunc.GetValue("FumcName")?.Value<string>() ?? string.Empty;
                        if (!funcNames.Contains(funcName.ToLower()))
                        {
                            funcNames.Add(funcName.ToLower());
                            switch (funcName)
                            {
                                case "TotalEnergy":
                                    _logicFunc += TotalEnergy;
                                    break;
                                case "GetBreakerPosition":
                                    _logicFunc += GetBreakerPosition;
                                    break;
                                case "SetTriped":
                                    _logicFunc += SetTriped;
                                    break;
                                case "ChangeSwitch":
                                    _logicFunc += ChangeSwitch;
                                    break;
                                default: break;
                            }
                        }
                    }
                }

                hasLoadFunc = true;
            }

            var newData = new Dictionary<string, string>();
            foreach (var kvp in data)
            {
                var result = _logicFunc(kvp.Key, kvp.Value, newData);
                if (!result)
                {
                    newData.TryAdd(kvp.Key, kvp.Value);
                }
            }

            return newData;
        }

        #region 点位逻辑方法
        /// <summary>
        /// 断路器是否 已经脱扣
        /// </summary>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        private bool SetTriped(string key, string value, Dictionary<string, string> data)
        {
            switch (AssetSimpleInfo.AssetModel)
            {
                case "3WL":
                    {
                        if ("Switch".Equals(key))
                        {
                            switch (value)
                            {
                                case "1":
                                case "2":
                                    data.TryAdd("HasTriped", "0");
                                    data.TryAdd(key, value);
                                    break;
                                case "3":
                                case "4":
                                    data.TryAdd("HasTriped", "1");
                                    data.TryAdd(key, "1");
                                    break;
                                case "0":
                                default:
                                    data.TryAdd(key, value);
                                    break;
                            }

                            return true;
                        }
                        break;
                    }
                case "3WA":
                    {
                        if ("HasTriped".Equals(key))
                        {
                            switch (value)
                            {
                                case "1":
                                    data.TryAdd("HasTriped", "0");
                                    break;
                                case "0":
                                    data.TryAdd("HasTriped", "1");
                                    break;
                                default:
                                    data.TryAdd(key, value);
                                    break;
                            }

                            return true;
                        }
                    }
                    break;
                
                default: break;
            }
            return false;
        }

        /// <summary>
        /// 修改断路器开关状态
        /// </summary>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        private bool ChangeSwitch(string key, string value, Dictionary<string, string> data)
        {
            switch (AssetSimpleInfo.AssetModel)
            {
                case "3WA":
                    {
                        if ("Switch".Equals(key))
                        {
                            switch (value)
                            {
                                case "0":
                                    data.AddOrSet(key, "1");
                                    return true;
                                case "1":
                                    data.AddOrSet(key, "2");
                                    return true;
                                default:
                                    data.AddOrSet(key, "0");
                                    break;
                            }
                        }
                    }
                    break;
                default: break;
            }
            return false;
        }

        /// <summary>
        /// 电能总和
        /// </summary>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        private bool TotalEnergy(string key, string value, Dictionary<string, string> data)
        {
            var match = Regex.Match(key, "^(ForwardActivePower|ForwardReactivePower|ReverseActivePower|ReverseReactivePower)_(Tariff1|Tariff2)$");
            if (match.Success)
            {
                decimal.TryParse(value, out var currentValue);
                if (data.TryAdd(key, value))
                {
                    var cacheKey = string.Format(Constant.AssetCurrentDataCacheKey, AssetSimpleInfo.AssetId);

                    var currentTraiff = match.Groups[2].Value;
                    var code = match.Groups[1].Value;
                    var antherTraiff = "Tariff1".Equals(currentTraiff) ? "Tariff2" : "Tariff1";
                    var antherValue = 0m;
                    var totalValue = currentValue;
                    if (data.TryGetValue($"{code}_{antherTraiff}", out var result))
                    {
                        decimal.TryParse(result, out antherValue);
                    }
                    else
                    {
                        var currently = Cache.GetHashAllData(cacheKey);
                        if (currently != null && currently.TryGetValue($"{code}_{antherTraiff}", out result))
                        {
                            decimal.TryParse(result, out antherValue);
                        }
                    }
                    totalValue += antherValue;
                    data.AddOrSet(code, totalValue.ToString());
                }

                return true;
            }
            return false;
        }

        /// <summary>
        /// 温控点位
        /// </summary>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <param name="inputData"></param>
        /// <returns></returns>
        private bool TempMotorLogic(string key, string value, Dictionary<string, string> inputData)
        {
            var match = Regex.Match(key, "^Sensor_([\\d]+)$");
            if (match.Success)
            {
                var no = match.Groups[1].Value;
                if (decimal.TryParse(value, out var v))
                {
                    if (v == 32767m)
                    {
                        inputData.AddOrSet($"Sensor_{no}_connect", "0");
                        inputData.AddOrSet($"Sensor_{no}", "NA");
                    }
                    else
                    {
                        inputData.AddOrSet($"Sensor_{no}_connect", "1");
                        inputData.AddOrSet($"Sensor_{no}", v.ToString());
                    }
                }
                return true;
            }
            return false;
        }

        /// <summary>
        /// 断路器位置
        /// </summary>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <param name="inputData"></param>
        /// <returns></returns>
        private bool GetBreakerPosition(string key, string value, Dictionary<string, string> inputData)
        {
            switch (AssetSimpleInfo.AssetModel)
            {
                case "3WA":
                    {
                        if ("TestPosition".Equals(key))
                        {
                            if ("1".Equals(value))
                            {
                                inputData.AddOrSet("BreakerPosition", "2");
                            }

                            inputData.TryAdd(key, value);
                            return true;
                        }

                        if ("OperationPosition".Equals(key))
                        {
                            if ("1".Equals(value))
                            {
                                inputData.AddOrSet("BreakerPosition", "1");
                            }
                            else if ("0".Equals(value))
                            {
                                inputData.AddOrSet("BreakerPosition", "0");
                            }
                            else
                            {
                                inputData.TryAdd("BreakerPosition", "3");
                            }

                            inputData.TryAdd(key, value);
                            return true;
                        }
                        break;
                    }
                case "3WL":
                    {
                        if ("BreakerPosition".Equals(key))
                        {
                            switch (value)
                            {
                                case "0":
                                    inputData.TryAdd("TestPosition", "0");
                                    inputData.TryAdd("OperationPosition", "0");
                                    break;
                                case "1":
                                    inputData.TryAdd("TestPosition", "0");
                                    inputData.TryAdd("OperationPosition", "1");
                                    break;
                                case "2":
                                    inputData.TryAdd("TestPosition", "1");
                                    inputData.TryAdd("OperationPosition", "0");
                                    break;
                                case "3":
                                    inputData.TryAdd("TestPosition", "0");
                                    inputData.TryAdd("OperationPosition", "3");
                                    break;
                                default: break;
                            }
                            inputData.TryAdd(key, value);
                            return true;
                        }
                    }
                    break;
                default: break;
            }

            return false;
        }
        #endregion

        /// <summary>
        /// 获取温控的点位绑定
        /// </summary>
        /// <returns></returns>
        private async Task<TemperatureMonitorInfo[]> GetMonitorList()
        {
            TemperatureMonitorInfo[] temperatureMonitorInfos;
            var client = Provider.GetRequiredService<SqlSugarScope>();

            try
            {
                temperatureMonitorInfos = await client.Queryable<TemperatureMonitorInfo>()
                    .Where(t => t.BindAssetId.HasValue && t.AssetId == AssetSimpleInfo.AssetId).ToArrayAsync();
            }
            catch (Exception ex)
            {
                temperatureMonitorInfos = Array.Empty<TemperatureMonitorInfo>();
            }

            return temperatureMonitorInfos;
        }

        /// <summary>
        /// 接收修改数据的入口
        /// </summary>
        /// <param name="changeData"></param>
        /// <returns></returns>
        protected override Task ChangeDataFunc(AssetChangeData changeData)
        {
            var cacheKey = string.Format(Constant.AssetCurrentDataCacheKey, AssetSimpleInfo.AssetId);
            Cache.SetHashData(cacheKey, new Dictionary<string, string>(changeData.ChangeDatas));

            var alarmRef = Provider.GetRequiredService<IAlarmRef>();
            var topologyRef = Provider.GetRequiredService<ITopologyDataChangeRef>();

            topologyRef.DataChange(changeData);

            if (!changeData.ChangeDatas.ContainsKey("HaveAlarm"))
            {
                if (changeData.ChangeDatas.Keys.Count > 0)
                {
                    alarmRef.AssetValueChange(changeData);
                }
            }

            return Task.CompletedTask;
        }

        private void UpdateCustomDataPoint(IAssetDataProxyRef assetDataProxy, AssetChangeData changeData)
        {
            var configs = Cache.Get<CustomDataPoint[]>($"CustomDataPoint-{changeData.AssetId}");

            if (configs == null)
            {
                configs = GetConfigBySql(changeData.AssetId);
            }

            if (configs.Any(c => changeData.ChangeDatas.ContainsKey(c.RealDataPointName)))
            {
                var assetDatas = new Dictionary<int, AssetChangeData>();

                foreach (var data in changeData.ChangeDatas)
                {
                    var tempConfigs = configs.Where(c => c.RealDataPointName == data.Key).ToArray();

                    foreach (var config in tempConfigs)
                    {
                        if (assetDatas.TryGetValue(config.TargetAssetId, out var newChangeData) && newChangeData != null)
                        {
                            newChangeData.ChangeDatas.AddOrSet(config.TargetDataPointName, GetValue(data.Value, config));
                        }
                        else
                        {
                            var inputData = new AssetChangeData()
                            {
                                AssetId = config.TargetAssetId,
                                ChangeDatas = new Dictionary<string, string>()
                                {
                                    [config.TargetDataPointName] = GetValue(data.Value, config)
                                },
                                ChangeTime = changeData.ChangeTime,
                            };
                            assetDatas.AddOrSet(config.TargetAssetId, inputData);
                        }
                    }
                }

                foreach (var data in assetDatas)
                {
                    if (data.Key != changeData.AssetId)
                    {
                        assetDataProxy.DataChanged(data.Value);
                    }
                }
            }
        }

        private CustomDataPoint[] GetConfigBySql(int assetId)
        {
            try
            {
                CustomDataPoint[] configs;
                var sqlClient = Provider.GetRequiredService<SqlSugarScope>();
                var customDataPointConfig = sqlClient.Queryable<CustomDataPoint>().Where(d => d.RealAssetId == assetId).ToArray();
                if (customDataPointConfig == null)
                {
                    customDataPointConfig = Array.Empty<CustomDataPoint>();
                }

                var r = new Random();
                Cache.Set($"CustomDataPoint-{assetId}", customDataPointConfig, TimeSpan.FromSeconds(r.Next(1200, 3000)));
                configs = customDataPointConfig;
                return configs;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"CustomDataPoint-{assetId}");
                return Array.Empty<CustomDataPoint>();
            }
        }

        private string GetValue(string value, CustomDataPoint customDataPoint)
        {
            if (customDataPoint.ValueMapping.ContainsKey(value))
            {
                return customDataPoint.ValueMapping[value];
            }

            return value;
        }

        private Task<CustomDataPoint[]> GetBreakerCustomDataPoints()
        {
            var customDataPoints = Cache.GetOrCreateAsync($"BreakerCustomDataPoint-{AssetSimpleInfo.AssetId}",
                async () =>
                {
                    try
                    {
                        using var client = Provider.GetRequiredService<ISqlSugarClient>();
                        return await client.Queryable<CustomDataPoint>().Where(c => c.TargetAssetId == AssetSimpleInfo.AssetId).ToArrayAsync();
                    }
                    catch (Exception ex)
                    {
                        Logger.LogError(ex, "");
                        return Array.Empty<CustomDataPoint>();
                    }
                },
                TimeSpan.FromHours(1));

            return customDataPoints;
        }
    }
}
