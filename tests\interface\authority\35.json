{"info": {"_postman_id": "b5bddfb0-9476-4e97-a589-e95c4a814b80", "name": "用户登入成功后进入个人中心界面，编辑个人信息修改新密码输入内容与旧密码相同", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "24354515"}, "item": [{"name": "用户登录(访客账户)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = pm.response.json().data.token//获取token\r", "pm.collectionVariables.set('token',token)//把token保存到全局变量中\r", "console.log (token)\r", "\r", "pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"test1\",\r\n  \"password\": \"test123@\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/login", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "修改密码(与旧密码一致)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"新旧密码不可以是一致的\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"新旧密码不可以是一致的\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"newPassword\": \"test123@\",\r\n  \"oldPassword\": \"test123@\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/My/profile/updatePwd", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "My", "profile", "updatePwd"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}], "variable": [{"key": "usernameindex", "value": 213, "type": "string"}, {"key": "username", "value": "user-213", "type": "string"}, {"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1lIjoidGVzdCIsImh0dHA6Ly9zY2hlbWFzLnhtbHNvYXAub3JnL3dzLzIwMDUvMDUvaWRlbnRpdHkvY2xhaW1zL25hbWVpZGVudGlmaWVyIjoiNDU4NDI2ZjEtYjJjNy00ZDJjLTk0NmQtZDNlYjk1Y2M5MmQzIiwiU3luY0RldmljZSI6IltdIiwibmJmIjoxNjc3MTQwODQ2LCJleHAiOjE2NzcxNDA4NDcsImlzcyI6IlNpZW1lbnNJc3N1ZXIiLCJhdWQiOiJXZWJBcHBBdWRpZW5jZSJ9.0zRKUC5RcEwD5H4X7f6iWajeYfNcOSp16QD8Lh3BEyE", "type": "string"}, {"key": "userId", "value": 115, "type": "string"}, {"key": "user2Id", "value": 99, "type": "string"}, {"key": "user3Id", "value": 95, "type": "string"}, {"key": "roleId", "value": 1, "type": "string"}, {"key": "BASE_URL", "value": "http://localhost:5000"}]}