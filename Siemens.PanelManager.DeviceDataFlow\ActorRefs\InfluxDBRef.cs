﻿using Akka.Actor;
using InfluxDB.Client.Writes;
using Siemens.PanelManager.DeviceDataFlow.StaticData;
using Siemens.PanelManager.Interface.ActorRefs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.DeviceDataFlow.ActorRefs
{
    public class InfluxDBRef : IInfluxDBRef
    {
        private InfluxDBQueue _queue;

        internal InfluxDBRef(InfluxDBQueue queue) 
        {
            _queue = queue;
        }

        public void AppendData(PointData pointData)
        {
            _queue.Append(pointData);
        }
    }
}
