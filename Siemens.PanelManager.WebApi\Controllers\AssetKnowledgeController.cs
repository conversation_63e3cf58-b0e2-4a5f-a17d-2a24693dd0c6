﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class AssetKnowledgeController : SiemensApiControllerBase
    {
        private readonly ISqlSugarClient _client;

        public AssetKnowledgeController(IServiceProvider provider,
            SiemensCache cache, SqlSugarScope client)
            : base(provider, cache)
        {
            _client = client;
        }

        [HttpGet("Search")]
        [SwaggerOperation(Summary = "Swagger_AssetKnowledge_Search", Description = "Swagger_AssetKnowledge_Search_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<SearchBase<KnowledgeResult>> Search([FromQuery] KnowledgeSearchParam knowledgeSearchParam)
        {
            if (string.IsNullOrWhiteSpace(knowledgeSearchParam.KeyString) || knowledgeSearchParam.KeyString.Length < 3)
            {
                return new SearchBase<KnowledgeResult>()
                {
                    Message = MessageContext.ErrorParam,
                    Code = 40000,
                };
            }

            RefAsync<int> totalCount = new RefAsync<int>();

            knowledgeSearchParam.KeyString = knowledgeSearchParam.KeyString.ToUpper();
            var knowledgeList = await _client.Queryable<AssetKnowledge>()
                .Where(a => (!SqlFunc.IsNullOrEmpty(a.AssetModel) && SqlFunc.Contains(knowledgeSearchParam.KeyString, SqlFunc.ToUpper(a.AssetModel)))
                || SqlFunc.Contains(SqlFunc.ToUpper(a.Breakdown), knowledgeSearchParam.KeyString)
                || SqlFunc.Contains(SqlFunc.ToUpper(a.PossibleCause), knowledgeSearchParam.KeyString)
                || SqlFunc.Contains(SqlFunc.ToUpper(a.Measure), knowledgeSearchParam.KeyString)
                || SqlFunc.Contains(SqlFunc.ToUpper(a.BreakdownComment), knowledgeSearchParam.KeyString)
                || SqlFunc.Contains(SqlFunc.ToUpper(a.PossibleCauseComment), knowledgeSearchParam.KeyString)
                || SqlFunc.Contains(SqlFunc.ToUpper(a.MeasureComment), knowledgeSearchParam.KeyString))
                .Select(a => new KnowledgeResult
                {
                    AssetModel = a.AssetModel,
                    AssetType = a.AssetType,
                    Breakdown = a.Breakdown,
                    PossibleCause = a.PossibleCause,
                    Measure = a.Measure,
                    BreakdownComment = a.BreakdownComment,
                    PossibleCauseComment = a.PossibleCauseComment,
                    MeasureComment = a.MeasureComment,
                })
                .ToPageListAsync(knowledgeSearchParam.Page, knowledgeSearchParam.PageSize, totalCount);


            return new SearchBase<KnowledgeResult>()
            {
                Code = 20000,
                Page = knowledgeSearchParam.Page,
                TotalCount = totalCount.Value,
                Items = knowledgeList
            };
        }
    }
}
