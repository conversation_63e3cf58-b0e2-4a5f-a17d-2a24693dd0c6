import React, { Component } from "react";
import { createHashRouter, RouterProvider } from "react-router-dom";
import App from "./App";
import { Home, UpgradeUI, SettingUI } from "./containers";

export default class MyRouter extends Component {
  render() {
    const myRouter = createHashRouter([
      {
        path: "/",
        element: <App headerName="主页" child={<Home />} />,
        loader: () => "",
      },
      {
        path: "/home",
        element: <App headerName="主页" child={<Home />} />,
        loader: () => "",
      },
      {
        path: "/upload",
        element: <App headerName="升级管理" child={<UpgradeUI />} />,
        loader: () => "",
      },
      {
        path: "/settings",
        element: <App headerName="配置" isSetting="1" child={<SettingUI />} />,
        loader: () => "",
      },
      {
        path: "/*",
        element: <App headerName="页面不存在" />,
        loader: () => "",
      },
    ]);

    return <RouterProvider router={myRouter} />;
  }
}
