﻿namespace Siemens.PanelManager.SyncService.UdcModels
{
    public partial class ArchiveDataFormat1
    {
        public string ObjectId { get; set; } = null!;
        public long Archive { get; set; }
        public long TimestampInS { get; set; }
        public string? VLnAggregationValueL1n { get; set; }
        public string? VLnAggregationValueL2n { get; set; }
        public string? VLnAggregationValueL3n { get; set; }
        public string? VLnAggregationValueAvg { get; set; }
        public string? VLnAggregationGreatestL1n { get; set; }
        public string? VLnAggregationGreatestL2n { get; set; }
        public string? VLnAggregationGreatestL3n { get; set; }
        public string? VLnAggregationGreatestAvg { get; set; }
        public string? VLnAggregationLowestL1n { get; set; }
        public string? VLnAggregationLowestL2n { get; set; }
        public string? VLnAggregationLowestL3n { get; set; }
        public string? VLnAggregationLowestAvg { get; set; }
        public string? VLlAggregationValueL1l2 { get; set; }
        public string? VLlAggregationValueL2l3 { get; set; }
        public string? VLlAggregationValueL3l1 { get; set; }
        public string? VLlAggregationValueAvg { get; set; }
        public string? VLlAggregationGreatestL1l2 { get; set; }
        public string? VLlAggregationGreatestL2l3 { get; set; }
        public string? VLlAggregationGreatestL3l1 { get; set; }
        public string? VLlAggregationGreatestAvg { get; set; }
        public string? VLlAggregationLowestL1l2 { get; set; }
        public string? VLlAggregationLowestL2l3 { get; set; }
        public string? VLlAggregationLowestL3l1 { get; set; }
        public string? VLlAggregationLowestAvg { get; set; }
        public string? IAggregationValueL1 { get; set; }
        public string? IAggregationValueL2 { get; set; }
        public string? IAggregationValueL3 { get; set; }
        public string? IAggregationValueAvg { get; set; }
        public string? IAggregationValueLn { get; set; }
        public string? IAggregationGreatestL1 { get; set; }
        public string? IAggregationGreatestL2 { get; set; }
        public string? IAggregationGreatestL3 { get; set; }
        public string? IAggregationGreatestAvg { get; set; }
        public string? IAggregationGreatestLn { get; set; }
        public string? IAggregationLowestL1 { get; set; }
        public string? IAggregationLowestL2 { get; set; }
        public string? IAggregationLowestL3 { get; set; }
        public string? IAggregationLowestAvg { get; set; }
        public string? IAggregationLowestLn { get; set; }
        public string? PowerAggregationWValueL1 { get; set; }
        public string? PowerAggregationWValueL2 { get; set; }
        public string? PowerAggregationWValueL3 { get; set; }
        public string? PowerAggregationWValueSum { get; set; }
        public string? PowerAggregationWGreatestL1 { get; set; }
        public string? PowerAggregationWGreatestL2 { get; set; }
        public string? PowerAggregationWGreatestL3 { get; set; }
        public string? PowerAggregationWGreatestSum { get; set; }
        public string? PowerAggregationWLowestL1 { get; set; }
        public string? PowerAggregationWLowestL2 { get; set; }
        public string? PowerAggregationWLowestL3 { get; set; }
        public string? PowerAggregationWLowestSum { get; set; }
        public string? PowerAggregationVarQnValueL1 { get; set; }
        public string? PowerAggregationVarQnValueL2 { get; set; }
        public string? PowerAggregationVarQnValueL3 { get; set; }
        public string? PowerAggregationVarQnValueSum { get; set; }
        public string? PowerAggregationVarQnGreatestL1 { get; set; }
        public string? PowerAggregationVarQnGreatestL2 { get; set; }
        public string? PowerAggregationVarQnGreatestL3 { get; set; }
        public string? PowerAggregationVarQnGreatestSum { get; set; }
        public string? PowerAggregationVarQnLowestL1 { get; set; }
        public string? PowerAggregationVarQnLowestL2 { get; set; }
        public string? PowerAggregationVarQnLowestL3 { get; set; }
        public string? PowerAggregationVarQnLowestSum { get; set; }
        public string? PowerAggregationVarQ1ValueL1 { get; set; }
        public string? PowerAggregationVarQ1ValueL2 { get; set; }
        public string? PowerAggregationVarQ1ValueL3 { get; set; }
        public string? PowerAggregationVarQ1ValueSum { get; set; }
        public string? PowerAggregationVarQ1GreatestL1 { get; set; }
        public string? PowerAggregationVarQ1GreatestL2 { get; set; }
        public string? PowerAggregationVarQ1GreatestL3 { get; set; }
        public string? PowerAggregationVarQ1GreatestSum { get; set; }
        public string? PowerAggregationVarQ1LowestL1 { get; set; }
        public string? PowerAggregationVarQ1LowestL2 { get; set; }
        public string? PowerAggregationVarQ1LowestL3 { get; set; }
        public string? PowerAggregationVarQ1LowestSum { get; set; }
        public string? PowerAggregationVarQtotValueL1 { get; set; }
        public string? PowerAggregationVarQtotValueL2 { get; set; }
        public string? PowerAggregationVarQtotValueL3 { get; set; }
        public string? PowerAggregationVarQtotValueSum { get; set; }
        public string? PowerAggregationVarQtotGreatestL1 { get; set; }
        public string? PowerAggregationVarQtotGreatestL2 { get; set; }
        public string? PowerAggregationVarQtotGreatestL3 { get; set; }
        public string? PowerAggregationVarQtotGreatestSum { get; set; }
        public string? PowerAggregationVarQtotLowestL1 { get; set; }
        public string? PowerAggregationVarQtotLowestL2 { get; set; }
        public string? PowerAggregationVarQtotLowestL3 { get; set; }
        public string? PowerAggregationVarQtotLowestSum { get; set; }
        public string? PowerAggregationVaValueL1 { get; set; }
        public string? PowerAggregationVaValueL2 { get; set; }
        public string? PowerAggregationVaValueL3 { get; set; }
        public string? PowerAggregationVaValueSum { get; set; }
        public string? PowerAggregationVaGreatestL1 { get; set; }
        public string? PowerAggregationVaGreatestL2 { get; set; }
        public string? PowerAggregationVaGreatestL3 { get; set; }
        public string? PowerAggregationVaGreatestSum { get; set; }
        public string? PowerAggregationVaLowestL1 { get; set; }
        public string? PowerAggregationVaLowestL2 { get; set; }
        public string? PowerAggregationVaLowestL3 { get; set; }
        public string? PowerAggregationVaLowestSum { get; set; }
        public string? PowerAggregationFactorValueL1 { get; set; }
        public string? PowerAggregationFactorValueL2 { get; set; }
        public string? PowerAggregationFactorValueL3 { get; set; }
        public string? PowerAggregationFactorValueSum { get; set; }
        public string? PowerAggregationFactorGreatestL1 { get; set; }
        public string? PowerAggregationFactorGreatestL2 { get; set; }
        public string? PowerAggregationFactorGreatestL3 { get; set; }
        public string? PowerAggregationFactorGreatestSum { get; set; }
        public string? PowerAggregationFactorLowestL1 { get; set; }
        public string? PowerAggregationFactorLowestL2 { get; set; }
        public string? PowerAggregationFactorLowestL3 { get; set; }
        public string? PowerAggregationFactorLowestSum { get; set; }
        public string? FrequencyAggregationInstValueCommon { get; set; }
        public string? FrequencyAggregationInstGreatestCommon { get; set; }
        public string? FrequencyAggregationInstLowestCommon { get; set; }
        public string? PhaseshiftAggregationPhaseangleValueL1 { get; set; }
        public string? PhaseshiftAggregationPhaseangleValueL2 { get; set; }
        public string? PhaseshiftAggregationPhaseangleValueL3 { get; set; }
        public string? PhaseshiftAggregationPhaseangleGreatestL1 { get; set; }
        public string? PhaseshiftAggregationPhaseangleGreatestL2 { get; set; }
        public string? PhaseshiftAggregationPhaseangleGreatestL3 { get; set; }
        public string? PhaseshiftAggregationPhaseangleLowestL1 { get; set; }
        public string? PhaseshiftAggregationPhaseangleLowestL2 { get; set; }
        public string? PhaseshiftAggregationPhaseangleLowestL3 { get; set; }
        public string? PhaseshiftAggregationCosphiValueL1 { get; set; }
        public string? PhaseshiftAggregationCosphiValueL2 { get; set; }
        public string? PhaseshiftAggregationCosphiValueL3 { get; set; }
        public string? PhaseshiftAggregationCosphiGreatestL1 { get; set; }
        public string? PhaseshiftAggregationCosphiGreatestL2 { get; set; }
        public string? PhaseshiftAggregationCosphiGreatestL3 { get; set; }
        public string? PhaseshiftAggregationCosphiLowestL1 { get; set; }
        public string? PhaseshiftAggregationCosphiLowestL2 { get; set; }
        public string? PhaseshiftAggregationCosphiLowestL3 { get; set; }
        public string? ThreephasesystemAggregationValueSysanglel1l1 { get; set; }
        public string? ThreephasesystemAggregationValueSysanglel1l2 { get; set; }
        public string? ThreephasesystemAggregationValueSysanglel1l3 { get; set; }
        public string? ThreephasesystemAggregationValueVAmplbal { get; set; }
        public string? ThreephasesystemAggregationValueVBal { get; set; }
        public string? ThreephasesystemAggregationValueIAmplbal { get; set; }
        public string? ThreephasesystemAggregationValueIBal { get; set; }
        public string? ThreephasesystemAggregationGreatestSysanglel1l1 { get; set; }
        public string? ThreephasesystemAggregationGreatestSysanglel1l2 { get; set; }
        public string? ThreephasesystemAggregationGreatestSysanglel1l3 { get; set; }
        public string? ThreephasesystemAggregationGreatestVAmplbal { get; set; }
        public string? ThreephasesystemAggregationGreatestVBal { get; set; }
        public string? ThreephasesystemAggregationGreatestIAmplbal { get; set; }
        public string? ThreephasesystemAggregationGreatestIBal { get; set; }
        public string? ThreephasesystemAggregationLowestSysanglel1l1 { get; set; }
        public string? ThreephasesystemAggregationLowestSysanglel1l2 { get; set; }
        public string? ThreephasesystemAggregationLowestSysanglel1l3 { get; set; }
        public string? ThreephasesystemAggregationLowestVAmplbal { get; set; }
        public string? ThreephasesystemAggregationLowestVBal { get; set; }
        public string? ThreephasesystemAggregationLowestIAmplbal { get; set; }
        public string? ThreephasesystemAggregationLowestIBal { get; set; }
        public string? ThdAggregationVLnInstValueL1n { get; set; }
        public string? ThdAggregationVLnInstValueL2n { get; set; }
        public string? ThdAggregationVLnInstValueL3n { get; set; }
        public string? ThdAggregationVLnInstGreatestL1n { get; set; }
        public string? ThdAggregationVLnInstGreatestL2n { get; set; }
        public string? ThdAggregationVLnInstGreatestL3n { get; set; }
        public string? ThdAggregationVLnInstLowestL1n { get; set; }
        public string? ThdAggregationVLnInstLowestL2n { get; set; }
        public string? ThdAggregationVLnInstLowestL3n { get; set; }
        public string? ThdAggregationVLlInstValueL1l2 { get; set; }
        public string? ThdAggregationVLlInstValueL2l3 { get; set; }
        public string? ThdAggregationVLlInstValueL3l1 { get; set; }
        public string? ThdAggregationVLlInstGreatestL1l2 { get; set; }
        public string? ThdAggregationVLlInstGreatestL2l3 { get; set; }
        public string? ThdAggregationVLlInstGreatestL3l1 { get; set; }
        public string? ThdAggregationVLlInstLowestL1l2 { get; set; }
        public string? ThdAggregationVLlInstLowestL2l3 { get; set; }
        public string? ThdAggregationVLlInstLowestL3l1 { get; set; }
        public string? ThdAggregationIInstValueL1 { get; set; }
        public string? ThdAggregationIInstValueL2 { get; set; }
        public string? ThdAggregationIInstValueL3 { get; set; }
        public string? ThdAggregationIInstGreatestL1 { get; set; }
        public string? ThdAggregationIInstGreatestL2 { get; set; }
        public string? ThdAggregationIInstGreatestL3 { get; set; }
        public string? ThdAggregationIInstLowestL1 { get; set; }
        public string? ThdAggregationIInstLowestL2 { get; set; }
        public string? ThdAggregationIInstLowestL3 { get; set; }
        public string? ThdAggregationDistortionIInstValueL1 { get; set; }
        public string? ThdAggregationDistortionIInstValueL2 { get; set; }
        public string? ThdAggregationDistortionIInstValueL3 { get; set; }
        public string? ThdAggregationDistortionIInstGreatestL1 { get; set; }
        public string? ThdAggregationDistortionIInstGreatestL2 { get; set; }
        public string? ThdAggregationDistortionIInstGreatestL3 { get; set; }
        public string? ThdAggregationDistortionIInstLowestL1 { get; set; }
        public string? ThdAggregationDistortionIInstLowestL2 { get; set; }
        public string? ThdAggregationDistortionIInstLowestL3 { get; set; }
        public string? EnergyWhImportOnpeaktariffSum { get; set; }
        public string? EnergyWhExportOnpeaktariffSum { get; set; }
        public long CounterUniversal1 { get; set; }
        public long CounterUniversal2 { get; set; }
        public string? EnergyVarhExportOffpeaktariffSum { get; set; }
        public string? EnergyVarhExportOnpeaktariffSum { get; set; }
        public string? EnergyVarhImportOffpeaktariffSum { get; set; }
        public string? EnergyVarhImportOnpeaktariffSum { get; set; }
        public string? EnergyWhExportOffpeaktariffSum { get; set; }
        public string? EnergyWhImportOffpeaktariffSum { get; set; }
        public string? M1AggregationGreatestMode4 { get; set; }
        public string? M1AggregationGreatestMode5020ma { get; set; }
        public string? M1AggregationGreatestMode5420ma { get; set; }
        public string? M1AggregationGreatestMode5Centralgroundingpointcurrent { get; set; }
        public string? M1AggregationGreatestMode6020ma { get; set; }
        public string? M1AggregationGreatestMode6420ma { get; set; }
        public string? M1AggregationGreatestMode6Centralgroundingpointcurrent { get; set; }
        public string? M1AggregationLowestMode4 { get; set; }
        public string? M1AggregationLowestMode5020ma { get; set; }
        public string? M1AggregationLowestMode5420ma { get; set; }
        public string? M1AggregationLowestMode5Centralgroundingpointcurrent { get; set; }
        public string? M1AggregationLowestMode6020ma { get; set; }
        public string? M1AggregationLowestMode6420ma { get; set; }
        public string? M1AggregationLowestMode6Centralgroundingpointcurrent { get; set; }
        public string? M1AggregationValueMode4 { get; set; }
        public string? M1AggregationValueMode5020ma { get; set; }
        public string? M1AggregationValueMode5420ma { get; set; }
        public string? M1AggregationValueMode5Centralgroundingpointcurrent { get; set; }
        public string? M1AggregationValueMode6020ma { get; set; }
        public string? M1AggregationValueMode6420ma { get; set; }
        public string? M1AggregationValueMode6Centralgroundingpointcurrent { get; set; }
        public string? M2AggregationGreatestMode4 { get; set; }
        public string? M2AggregationGreatestMode5Adc2020ma { get; set; }
        public string? M2AggregationGreatestMode5Adc2420ma { get; set; }
        public string? M2AggregationGreatestMode5adc2Residualcurrent { get; set; }
        public string? M2AggregationGreatestMode6Adc3 { get; set; }
        public string? M2AggregationGreatestMode6Adc3020ma { get; set; }
        public string? M2AggregationGreatestMode6Adc3420ma { get; set; }
        public string? M2AggregationLowestMode4 { get; set; }
        public string? M2AggregationLowestMode5Adc2020ma { get; set; }
        public string? M2AggregationLowestMode5Adc2420ma { get; set; }
        public string? M2AggregationLowestMode5adc2Residualcurrent { get; set; }
        public string? M2AggregationLowestMode6Adc3 { get; set; }
        public string? M2AggregationLowestMode6Adc3020ma { get; set; }
        public string? M2AggregationLowestMode6Adc3420ma { get; set; }
        public string? M2AggregationValueMode4 { get; set; }
        public string? M2AggregationValueMode5Adc2020ma { get; set; }
        public string? M2AggregationValueMode5Adc2420ma { get; set; }
        public string? M2AggregationValueMode5adc2Residualcurrent { get; set; }
        public string? M2AggregationValueMode6Adc3 { get; set; }
        public string? M2AggregationValueMode6Adc3020ma { get; set; }
        public string? M2AggregationValueMode6Adc3420ma { get; set; }
        public long Counter1 { get; set; }
        public long Counter2 { get; set; }
        public long Counter3 { get; set; }
        public long Counter4 { get; set; }
        public long Counter5 { get; set; }
        public long Counter6 { get; set; }
        public long Counter7 { get; set; }
        public long Counter8 { get; set; }
        public long Counter9 { get; set; }
        public long Counter10 { get; set; }
    }
}
