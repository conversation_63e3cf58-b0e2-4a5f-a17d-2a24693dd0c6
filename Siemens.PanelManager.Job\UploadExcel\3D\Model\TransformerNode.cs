﻿using Newtonsoft.Json;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Topology3D;
using Siemens.PanelManager.Model.Topoplogy3D;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.UploadExcel._3D.Model
{
    internal class TransformerNode : ICabinetNode
    {
        private const int MinHeight = 2200;
        private const int MaxHeight = 2500;
        private const int MinWidth = 1600;
        private const int MaxWidth = 2200;
        private const int Offset = 200;

        public override string NodeType => "shell";
        public TransformerNode(TransformerModel model)
        {
            AssetInfo = model.AssetInfo;
            Type = NodeType;
            Id = Guid.NewGuid().ToString();
            Name = model.AssetInfo.AssetName;

            var height = MinHeight;
            if(model.Height > MaxHeight)
            {
                height = MaxHeight;
            }
            else if (model.Height < MinHeight)
            {
                height = MinHeight;
            }
            else
            {
                height = model.Height;
            }

            var width = MinWidth;
            if (model.Width > MaxWidth)
            {
                width = MaxWidth;
            }
            else if (model.Width < MinWidth)
            {
                width = MinWidth;
            }
            else
            {
                width = model.Width;
            }

            Size.Height = height;
            Size.Width = width;
            Size.Depth = 1200;

            UserData = new TransformerUserData()
            {
                BusbarStructure = model.BusbarStructure,
                AssetId = model.AssetInfo.Id,
                Type = NodeType,
                PanelType = "Transformer",
                CircuitList = new List<CircuitItem>()
                {
                    new CircuitItem
                    {
                        AssetType = "TransformerLeftExtend",
                        Width = "1/4",
                        AssetStructure = "Other",
                        Height = height - Offset
                    },
                    new CircuitItem
                    {
                        AssetType = "TransformerLeft",
                        Width = "1/4",
                        AssetStructure = "Other",
                        Height = height - Offset
                    },
                    new CircuitItem
                    {
                        AssetType = "TransformerRight",
                        Width = "1/4",
                        AssetStructure = "Other",
                        Height = height - Offset
                    },
                    new CircuitItem
                    {
                        AssetType = "TransformerRightExtend",
                        Width = "1/4",
                        AssetStructure = "Other",
                        Height = height - Offset
                    }
                }
            };
        }

        private TransformerNode()
        {
        }

        public override SingalCabinetNode GetSingalNode()
        {
            return new SingalCabinetNode(Id, NodeType, Position, Size);
        }

        public override ICabinetNode GetNew()
        {
            return new TransformerNode()
            {
                Id = Id,
                Name = Name,
                Type = NodeType,
                Position = new Position(),
                Rotation = Rotation,
                Size = Size,
                UserData = UserData
            };
        }
    }
}
