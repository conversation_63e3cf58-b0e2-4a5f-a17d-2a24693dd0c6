﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.Model.Database.Auth;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.WebApi.Models;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;
using System.Text;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class UsersController : SiemensApiControllerBase
    {
        private ILogger<UsersController> _log;
        private ISqlSugarClient _client;
        private IServiceProvider _provider;
        private AlarmExtendServer _alarmExtendServer => _provider.GetRequiredService<AlarmExtendServer>();
        public UsersController(SqlSugarScope client, ILogger<UsersController> log, SiemensCache cache, IServiceProvider provider) : base(provider, cache)
        {
            _client = client;
            _log = log;
            _provider = provider;
        }

        [HttpGet("info")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_Users_UserInfo", Description = "Swagger_Users_UserInfo_Desc")]
        public async Task<ResponseBase<UserInfoResult>> UserInfo()
        {
            var userSession = UserSession;
            if (userSession == null) 
            {
                return new ResponseBase<UserInfoResult>()
                {
                    Code = 40100,
                    Message = MessageContext.TokenTimeout
                };
            }
            var userId = userSession.UserId;
            var user = await _client.Queryable<User>().Where(u => u.Id == userId).FirstAsync();
            if(user == null)
            {
                return new ResponseBase<UserInfoResult>()
                {
                    Code = 40400,
                    Message = MessageContext.UserNotExists
                };
            }
            var roles = await _client.Queryable<Role>().InnerJoin<UserRoleMapping>((r, urm) => r.Id == urm.RoleId).Where((r, urm) => urm.UserId == userId).ToArrayAsync();
            var roleIds = roles.Select(r => r.Id).ToArray();
            var pages = await _client.Queryable<Page>().InnerJoin<RolePageMapping>((p, rpm) => p.Id == rpm.PageId).Where((p, rpm) => roleIds.Contains(rpm.RoleId)).ToArrayAsync();

            return new ResponseBase<UserInfoResult>
            {
                Code = 20000,
                Data = new UserInfoResult(user, roles, pages, MessageContext)
            };
        }

        [HttpPost()]
        [Authorize(policy: Permission.UserCreate)]
        [SwaggerOperation(Summary = "Swagger_Users_CreateUser", Description = "Swagger_Users_CreateUser_Desc")]
        public async Task<ResponseBase<CreateUserResult>> CreateUser(CreateUserParam param)
        {
            if (param == null 
                || string.IsNullOrEmpty(param.UserName) 
                || string.IsNullOrEmpty(param.PersonName)
                || param.Roles == null
                || param.Roles.Length <= 0)
            {
                return new ResponseBase<CreateUserResult>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var q = _client.Queryable<User>().Where(u=>u.IsDeleted).Select(u=>new 
            {
                u.UserName,
                u.Id
            }).ToList();

            var loginName = param.UserName.ToLower();

            if (_client.Queryable<User>().Any(u => u.LoginName == loginName && !u.IsDeleted))
            {
                return new ResponseBase<CreateUserResult>()
                {
                    Code = 50100,
                    Message = MessageContext.UserNameIsExists
                };
            }

            var password = CreatePassword();
            var code = password.GetPasswordCode();

            var newUser = new User()
            {
                UserName = param.PersonName,
                EmailAddress = param.Email,
                MobileNumber = param.Tel,
                IsDeleted = false,
                LoginName = param.UserName.ToLower(),
                PrefixMobileNumber = param.PrefixTel,
                PasswordHash = code,
                CreatedBy = UserName,
                CreatedTime = DateTime.Now,
                UpdatedBy = UserName,
                UpdatedTime = DateTime.Now,
            };
            var message = new StringBuilder();
            if (!newUser.CheckUserInfo(message, this))
            {
                return new ResponseBase<CreateUserResult>()
                {
                    Code = 40301,
                    Message = message.ToString()
                };
            }
            if (param.Roles != null && param.Roles.Length > 0)
            {
                var roleIds = param.Roles.Select(r => r.Id).ToArray();
                if(!(await roleIds.CheckRoleCount(_client.Queryable<UserRoleMapping>(), _client.Queryable<Role>())))
                {
                    return new ResponseBase<CreateUserResult>()
                    {
                        Code = 40303,
                        Message = MessageContext.RoleCountNotEnough
                    };
                }
            }

            try
            {
                _client.Ado.BeginTran();
                
                var userId = await _client.Insertable(newUser).ExecuteReturnIdentityAsync();

                if (param.Roles != null && param.Roles.Length > 0)
                {
                    var userRoleMappings = new List<UserRoleMapping>();
                    foreach (var role in param.Roles)
                    {
                        if (role.Id > 0)
                        {
                            var userRoleMapping = new UserRoleMapping()
                            {
                                RoleId = role.Id,
                                UserId = userId,
                                CreatedBy = UserName,
                                CreatedTime = DateTime.Now,
                                UpdatedBy = UserName,
                                UpdatedTime = DateTime.Now,
                            };
                            userRoleMappings.Add(userRoleMapping);
                        }
                    }

                    await _client.Insertable(userRoleMappings).ExecuteCommandAsync();
                }
                await _alarmExtendServer.InsertOperationLog(UserName, "AddUser", Model.Database.Alarm.AlarmSeverity.Middle, _client, newUser.UserName);
                _client.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "创建用户失败");
                return new ResponseBase<CreateUserResult>()
                {
                    Code = 50000,
                    Message = MessageContext.ServerException,
                };

            }

            return new ResponseBase<CreateUserResult>()
            {
                Code = 20000,
                Data = new CreateUserResult()
                {
                    Pwd = password
                }
            };
        }

        [HttpPut("{userId}")]
        [Authorize(policy: Permission.UserUpdate)]
        [SwaggerOperation(Summary = "Swagger_Users_UpdateUser", Description = "Swagger_Users_UpdateUser_Desc")]
        public async Task<ResponseBase<string>> UpdateUser(int userId, UpdateUserParam param)
        {
            if (param == null 
                || string.IsNullOrEmpty(param.PersonName)
                || param.Id <=0
                || param.Id != userId)
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam,
                };
            }

            var oldUser = await _client.Queryable<User>().Where(u => u.Id == param.Id).FirstAsync();

            if (oldUser == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.UserNotExists,
                };
            }

            oldUser.UserName = param.PersonName;
            oldUser.EmailAddress = param.Email;
            oldUser.PrefixMobileNumber = param.PrefixTel;
            oldUser.MobileNumber = param.Tel;
            oldUser.UpdatedBy = UserName;
            oldUser.UpdatedTime = DateTime.Now;
            var message = new StringBuilder();
            if (!oldUser.CheckUserInfo(message, this))
            {
                return new ResponseBase<string>()
                {
                    Code = 40301,
                    Message = message.ToString()
                };
            }

            List<UserRoleMapping>? userRoleMappings = null;
            List<UserRoleMapping>? needInsertList = null;

            if (param.Roles != null)
            {
                userRoleMappings = _client.Queryable<UserRoleMapping>().Where(urm => urm.UserId == oldUser.Id).ToList();
                needInsertList = new List<UserRoleMapping>();
                foreach (var role in param.Roles)
                {
                    var m = userRoleMappings.FirstOrDefault(r => r.RoleId == role.Id);
                    if (m != null)
                    {
                        userRoleMappings.Remove(m);
                    }
                    else
                    {
                        if (!(await role.Id.CheckRoleCount(_client.Queryable<UserRoleMapping>(), _client.Queryable<Role>())))
                        {
                            return new ResponseBase<string>()
                            {
                                Code = 40303,
                                Message = MessageContext.RoleCountNotEnough
                            };
                        }

                        needInsertList.Add(new UserRoleMapping()
                        {
                            RoleId = role.Id,
                            UserId = oldUser.Id,
                            CreatedBy = UserName,
                            CreatedTime = DateTime.Now,
                            UpdatedBy = UserName,
                            UpdatedTime = DateTime.Now
                        });
                    }
                }
            }

            try
            {
                _client.Ado.BeginTran();
                await _client.Updateable(oldUser).ExecuteCommandAsync();
                if (needInsertList != null && needInsertList.Count > 0)
                {
                    await _client.Insertable(needInsertList).ExecuteCommandAsync();
                }
                if (userRoleMappings != null && userRoleMappings.Count > 0)
                {
                    var needDeleteIdList = userRoleMappings.Select(urm => urm.Id).ToArray();
                    await _client.Deleteable<UserRoleMapping>().Where(urm => needDeleteIdList.Contains(urm.Id)).ExecuteCommandAsync();
                }
                await _alarmExtendServer.InsertOperationLog(UserName, "UpdateUser", Model.Database.Alarm.AlarmSeverity.Middle, _client, oldUser.UserName);
                _client.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "更新用户失败");
                return new ResponseBase<string>()
                {
                    Code = 50000,
                    Message = MessageContext.ErrorParam
                };

            }

            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = MessageContext.Success
            };
        }

        [HttpGet("{userId}/role")]
        [Authorize(policy: Permission.UserRead)]
        [SwaggerOperation(Summary = "Swagger_Users_GetUserRoles", Description = "Swagger_Users_GetUserRoles_Desc")]
        public async Task<ResponseBase<UserRoleResult>> GetUserRole(int userId)
        {
            if (userId <= 0)
            {
                return new ResponseBase<UserRoleResult>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            if (!await _client.Queryable<User>().AnyAsync(u => u.Id == userId && !u.IsDeleted))
            {
                return new ResponseBase<UserRoleResult>()
                {
                    Code = 40400,
                    Message = MessageContext.UserNotExists
                };
            }

            var roles = await _client.Queryable<Role>()
                .WithCache("Role:All", (int)TimeSpan.FromHours(1).TotalSeconds)
                .ToArrayAsync();
            var userRoleMappings = await _client.Queryable<UserRoleMapping>().Where(urm => urm.UserId == userId).ToArrayAsync();

            return new ResponseBase<UserRoleResult>
            {
                Code = 20000,
                Data = new UserRoleResult(roles, userRoleMappings, MessageContext)
            };
        }

        [HttpPost("{userId}/role")]
        [Authorize(policy: Permission.UserUpdate)]
        [SwaggerOperation(Summary = "Swagger_Users_UpdateUserRoles", Description = "Swagger_Users_UpdateUserRoles_Desc")]
        public async Task<ResponseBase<string>> UpdateUserRole(int userId, 
            UpdateUserRoleParam param)
        {
            if (param == null
                || param.Roles == null
                || param.UserId <= 0
                || param.UserId != userId)
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }
            var user = await _client.Queryable<User>().FirstAsync(u => u.Id == userId);
            if (user == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.UserNotExists,
                };
            }
            var mappings = await _client.Queryable<UserRoleMapping>()
                .Where(urm => urm.UserId == userId)
                .ToArrayAsync();

            
            var insertRows = new List<UserRoleMapping>();
            foreach (var r in param.Roles)
            {
                if (r.Id > 0 && !mappings.Any(m => m.RoleId == r.Id))
                {
                    if (!(await r.Id.CheckRoleCount(_client.Queryable<UserRoleMapping>(), _client.Queryable<Role>())))
                    {
                        return new ResponseBase<string>()
                        {
                            Code = 40303,
                            Message = MessageContext.RoleCountNotEnough
                        };
                    }

                    insertRows.Add(new UserRoleMapping()
                    {
                        UserId = userId,
                        RoleId = r.Id,
                        CreatedBy = UserName,
                        CreatedTime = DateTime.Now,
                        UpdatedBy = UserName,
                        UpdatedTime = DateTime.Now,
                    });
                }
            }

            var needDeleteIdList = mappings.Where(m => !param.Roles.Any(r => r.Id == m.RoleId)).Select(m => m.Id).ToArray();
            try
            {
                _client.Ado.BeginTran();
                await _client.Insertable(insertRows).ExecuteCommandAsync();
                await _client.Deleteable<UserRoleMapping>().Where(urm => needDeleteIdList.Contains(urm.Id)).ExecuteCommandAsync();
                await _alarmExtendServer.InsertOperationLog(UserName, "UpdateUser", Model.Database.Alarm.AlarmSeverity.Middle, _client, user.UserName);
                _client.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "保存用户角色失败");

                return new ResponseBase<string>
                {
                    Code = 50000,
                    Message = MessageContext.ServerException
                };
            }


            return new ResponseBase<string>
            {
                Code = 20000,
                Data = MessageContext.Success
            };
        }

        [HttpGet]
        [Authorize(policy: Permission.UserRead)]
        [SwaggerOperation(Summary = "Swagger_Users_GetUsers", Description = "Swagger_Users_GetUsers_Desc")]
        public async Task<ResponseBase<AllUserResult>> GetAllUsers()
        {
            var users = await _client.Queryable<User>().Where(u=>!u.IsDeleted).ToArrayAsync();
            var userRoleMappings = await _client.Queryable<UserRoleMapping>().ToArrayAsync();
            var roles = await _client.Queryable<Role>()
                .WithCache("Role:All", (int)TimeSpan.FromHours(1).TotalSeconds)
                .ToArrayAsync();
            return new ResponseBase<AllUserResult>
            {
                Code = 20000,
                Data = new AllUserResult(users, userRoleMappings, roles, MessageContext)
            };
        }

        [HttpDelete("{userId}")]
        [Authorize(policy: Permission.UserDelete)]
        [SwaggerOperation(Summary = "Swagger_Users_DeleteUser", Description = "Swagger_Users_DeleteUser_Desc")]
        public async Task<ResponseBase<string>> DeleteUser(int userId)
        {
            if (userId <= 0)
            {
                return new ResponseBase<string>
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }
            var user = await _client.Queryable<User>().Where(u => u.Id == userId && !u.IsDeleted).FirstAsync();
            if (user == null)
            {
                return new ResponseBase<string>
                {
                    Code = 40400,
                    Message= MessageContext.UserNotExists
                };
            }

            user.IsDeleted = true;
            user.UpdatedBy = UserName;
            user.UpdatedTime = DateTime.Now;
            try
            {
                _client.Ado.BeginTran();
                await _client.Updateable(user).ExecuteCommandAsync();
                await _client.Deleteable<UserRoleMapping>().Where(urm => urm.UserId == user.Id).ExecuteCommandAsync();
                await _alarmExtendServer.InsertOperationLog(UserName, "DeleteUser", Model.Database.Alarm.AlarmSeverity.Middle, _client, user.UserName);
                _client.Ado.CommitTran();
            }
            catch(Exception ex) 
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "删除用户失败");
                return new ResponseBase<string>()
                {
                    Code = 50000,
                    Message = MessageContext.ServerException
                };
            }
            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = MessageContext.Success
            };
        }

        [HttpGet("{userId}/resetpwd")]
        [Authorize(policy: Permission.UserUpdate)]
        [SwaggerOperation(Summary = "Swagger_Users_ResetPassword", Description = "Swagger_Users_ResetPassword_Desc")]
        public async Task<ResponseBase<CreateUserResult>> ResetPassword(int userId)
        {
            if (userId <= 0)
            {
                return new ResponseBase<CreateUserResult>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var user = await _client.Queryable<User>().Where(u=>u.Id == userId && !u.IsDeleted).FirstAsync();
            if(user == null) 
            {
                return new ResponseBase<CreateUserResult>()
                {
                    Code = 40400,
                    Message = MessageContext.UserNotExists
                };
            }

            var password = CreatePassword();
            var code = password.GetPasswordCode();

            user.PasswordHash = code;
            user.UpdatedBy = UserName;
            user.UpdatedTime = DateTime.Now;
            await _client.Updateable(user).ExecuteCommandAsync();
            await _alarmExtendServer.InsertOperationLog(UserName, "ResetPassword", Model.Database.Alarm.AlarmSeverity.Middle, _client, user.UserName);

            return new ResponseBase<CreateUserResult>()
            {
                Code = 20000,
                Data = new CreateUserResult()
                {
                    Pwd = password,
                }
            };
        }

        [HttpPost("batch")]
        [Authorize(policy: Permission.UserDelete)]
        [SwaggerOperation(Summary = "Swagger_Users_BatchDeleteUsers", Description = "Swagger_Users_BatchDeleteUsers_Desc")]
        public async Task<ResponseBase<string>> BatchDeleteUser(BatchDeleteUserParam param)
        {
            if (param == null 
                || "delete".Equals(param.Type, StringComparison.OrdinalIgnoreCase) 
                || param.Ids == null 
                || param.Ids.Length <= 0)
            {
                return new ResponseBase<string>
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }
            var ids = param.Ids;
            var users = await _client.Queryable<User>().Where(u => ids.Contains(u.Id) && !u.IsDeleted).ToArrayAsync();

            var userNames = new StringBuilder();
            foreach (var user in users)
            {
                if(userNames.Length > 0) 
                {
                    userNames.Append(',');
                }
                userNames.Append(user.UserName);
                user.IsDeleted = true;
                user.UpdatedBy = UserName;
                user.UpdatedTime = DateTime.Now;
            }
            try
            {
                _client.Ado.BeginTran();
                await _client.Updateable(users).ExecuteCommandAsync();
                await _client.Deleteable<UserRoleMapping>().Where(urm => ids.Contains(urm.UserId)).ExecuteCommandAsync();
                await _alarmExtendServer.InsertOperationLog(UserName, "DeleteUser", Model.Database.Alarm.AlarmSeverity.Middle, _client, userNames.ToString());
                _client.Ado.CommitTran();
            }
            catch (Exception ex) 
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "批量删除失败");
                return new ResponseBase<string>()
                {
                    Code = 50000,
                    Message = MessageContext.ServerException
                };
            }
            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = MessageContext.Success
            };
        }

        [HttpPut("SignIn")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_Users_SignIn", Description = "Swagger_Users_SignIn_Desc")]
        public async Task<ResponseBase<bool>> SignIn()
        {
            if (UserSession != null)
            {
                var dateTime = Cache.Get<DateTime?>($"SignIn-{UserSession.UserId}");

                if (dateTime.HasValue)
                {
                    return new ResponseBase<bool>()
                    {
                        Code = 20000,
                        Data = false,
                        Message = "一个小时只能签到一次"
                    };
                }

                var alarmLogService = _provider.GetRequiredService<AlarmExtendServer>();
                await alarmLogService.SignIn(UserName, UserSession.UserId, _client);
                Cache.Set($"SignIn-{UserSession.UserId}", DateTime.Now, TimeSpan.FromHours(1));

                return new ResponseBase<bool>()
                {
                    Code = 20000,
                    Data = true,
                };
            }

            return new ResponseBase<bool>()
            {
                Code = 40300,
                Data = false,
                Message = MessageContext.ErrorParam
            };
        }

        [HttpGet("SignInCount")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_Users_GetSignInCount", Description = "Swagger_Users_GetSignInCount_Desc")]
        public async Task<ResponseBase<SignInInfo>> GetSignInCount()
        {
            if (UserSession != null)
            {
                var count = await _client.Queryable<AlarmLog>()
                        .Where(a => a.EventType == AlarmEventType.OperationLog && a.AssetId == UserSession.UserId)
                        .CountAsync();

                var alarm = await _client.Queryable<AlarmLog>()
                        .Where(a => a.EventType == AlarmEventType.OperationLog && a.AssetId == UserSession.UserId)
                        .OrderByDescending(a => a.Id)
                        .FirstAsync();

                DateTime? lastSignInTime = null;
                if (alarm != null)
                {
                    lastSignInTime = alarm.CreatedTime;
                }

                return new ResponseBase<SignInInfo>()
                {
                    Code = 20000,
                    Data = new SignInInfo
                    {
                        SignInCount = count,
                        LastSignInTime = lastSignInTime
                    }
                };
            }

            return new ResponseBase<SignInInfo>()
            {
                Code = 20000,
                Message = MessageContext.ErrorParam
            };
        }

        [HttpGet("PrefixTel")]
        [SwaggerOperation(Summary = "Swagger_Users_PrefixTel", Description = "Swagger_Users_PrefixTel_Desc")]
        public async Task<ResponseBase<PrefixTelResult>> GetPrefixTel()
        {
            var str = "PrefixMobileNumber".ToUpper();
            var staticModels = await _client.Queryable<SystemStaticModel>().Where(a => a.Type == str)
                .OrderBy(s => s.Sort, OrderByType.Desc)
                .WithCache($"SystemStaticModel:{str}", (int)TimeSpan.FromHours(1).TotalSeconds)
                .ToArrayAsync();

            return new ResponseBase<PrefixTelResult>()
            {
                Code = 20000,
                Data = new PrefixTelResult(staticModels, MessageContext)
            };
        }

        #region Password Char List
        private static readonly char[] PasswordCharList = new char[]
        {
            'a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z',
            'A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',
            '1','2','3','4','5','6','7','8','9','0','!','@','$','-','_','+','=','<','>','?','{','}',':',';','|','&','#'
        };
        #endregion
        private string CreatePassword()
        {
            int length = 10;
            Random r = new Random();
            var passwordStringBuilder = new StringBuilder(length);
            for (var i = 0; i < length; i++)
            {
                var index = r.Next(0, PasswordCharList.Length - 1);
                passwordStringBuilder.Append(PasswordCharList[index]);
            }

            return passwordStringBuilder.ToString();
        }
    }
}
