﻿using Akka.Actor;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.DeviceDataFlow.MessageBus;
using Siemens.PanelManager.DeviceDataFlow.StaticData;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.Alarm;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.DataFlow;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.Server.Common;
using Siemens.PanelManager.Server.DataPoint;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.DeviceDataFlow.DataFlowActors
{
    internal class TransformerActor : AssetActorBase
    {
        private readonly DataPointServer _dataPointServer;

        public TransformerActor(ILogger<TransformerActor> logger, IServiceProvider provider, AssetSimpleInfo simpleInfo, SiemensCache cache)
            : base(simpleInfo, provider, logger, cache)
        {
            _dataPointServer = provider.GetRequiredService<DataPointServer>();
            LoggerName = "TransformerActor";
        }
        protected override async Task ChangeDataFunc(AssetChangeData changeData)
        {
            if (changeData.ChangeDatas.TryGetValue("HaveAlarm", out _))
            {
                changeData.ChangeDatas.Remove("HaveAlarm");
            }
            if (changeData.ChangeDatas.Count == 0) return;

            await UpdateData(changeData.AssetId, changeData.ChangeDatas, changeData.ChangeTime);
        }
        protected override async Task InputDataFunc(AssetInputData inputData)
        {
            if (inputData.Datas.TryGetValue("HaveAlarm", out _))
            {
                inputData.Datas.Remove("HaveAlarm");
            }
            if (inputData.Datas.Count == 0) return;

            await UpdateData(inputData.ParentId ?? inputData.AssetId ?? 0, inputData.Datas, inputData.InputTime);
        }
        /// <summary>
        /// 更新数据
        /// </summary>
        /// <param name="assetId"></param>
        /// <param name="changeDatas"></param>
        /// <returns></returns>
        private async Task UpdateData(int assetId, Dictionary<string, string> changeDatas, DateTime time)
        {
            var data = new Dictionary<string, string>();
            var dataPoints = await _dataPointServer.GetDataPointInfos(AssetLevel.Transformer);

            var configs = await _dataPointServer.GetSubstationDataPointConfigDetailsByCache(AssetSimpleInfo.AssetId);
            var keys = changeDatas.Keys.ToList();

            if (configs.Any(c => keys.Contains(c.DataPointCode)))
            {
                var hitConfigs = configs.Where(c => keys.Contains(c.DataPointCode)).ToList();
                foreach (var hitConfig in hitConfigs)
                {
                    data.TryAdd(hitConfig.DataPointCode, changeDatas[hitConfig.DataPointCode]);
                }
            }

            if (dataPoints.Any(d => keys.Contains(d.Code)))
            {
                var hitDataPoints = dataPoints.Where(d => keys.Contains(d.Code)).ToList();
                foreach (var hitDataPoint in hitDataPoints)
                {
                    data.TryAdd(hitDataPoint.Code, changeDatas[hitDataPoint.Code]);
                }
            }

            if (data.Any())
            {
                var transformerCacheKey = string.Format(Constant.AssetCurrentDataCacheKey, assetId);
                Cache.SetHashData(transformerCacheKey, new Dictionary<string, string>(data));

                var changeData = new AssetChangeData()
                {
                    AssetId = assetId,
                    AssetLevel = AssetSimpleInfo.AssetLevel,
                    AssetName = AssetSimpleInfo.AssetName,
                    AssetModel = AssetSimpleInfo.AssetModel,
                    AssetType = AssetSimpleInfo.AssetType,
                    ChangeTime = time,
                    ChangeDatas = data
                };
                var actorManager = ActorManager.GetActorManagerNoException();

                if (actorManager != null)
                {
                    actorManager.DataPointRef.Tell(changeData);
                }
            }

        }
    }
}
