{"info": {"_postman_id": "08bcf88d-7fad-4a6b-99c6-d03e9a60e432", "name": "使用管理员账号进入panel manager用户管理菜单，添加用户手机号时输入12位数字", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "24354515"}, "item": [{"name": "用户登录(超级管理员)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = pm.response.json().data.token//获取token\r", "pm.collectionVariables.set('token',token)//把token保存到全局变量中\r", "console.log (token)\r", "\r", "pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/login", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "添加用户信息(循环)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"手机号格式错误\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"message\"]).to.eql(\"手机号格式错误\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"userName\": \"{{username}}\",\r\n    \"personName\": \"h111\",\r\n    \"tel\": \"123123123123\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"roles\": [\r\n        {\r\n            \"id\": 5\r\n        }\r\n    ]\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/users", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "users"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}], "variable": [{"key": "usernameindex", "value": 197, "type": "string"}, {"key": "username", "value": "user-197", "type": "string"}, {"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1lIjoiaGtuNzc3IiwiaHR0cDovL3NjaGVtYXMueG1sc29hcC5vcmcvd3MvMjAwNS8wNS9pZGVudGl0eS9jbGFpbXMvbmFtZWlkZW50aWZpZXIiOiI4ZDBhYjBkZS0yNTEwLTQ1YjMtOWY5Zi04OWRlNWMzNTJmNzkiLCJTeW5jRGV2aWNlIjoiW10iLCJuYmYiOjE2NzcxMzk2MTgsImV4cCI6MTY3NzEzOTYxOSwiaXNzIjoiU2llbWVuc0lzc3VlciIsImF1ZCI6IldlYkFwcEF1ZGllbmNlIn0.tsH_YsVUKcz2pXR4Hur-fuYUNI3_Vp_1O1R1IDl6er8", "type": "string"}, {"key": "userId", "value": 103, "type": "string"}, {"key": "user2Id", "value": 99, "type": "string"}, {"key": "user3Id", "value": 95, "type": "string"}, {"key": "roleId", "value": 1, "type": "string"}, {"key": "BASE_URL", "value": "http://localhost:5000"}]}