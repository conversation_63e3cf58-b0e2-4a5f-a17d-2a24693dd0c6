﻿namespace Siemens.PanelManager.WebApi.ViewModel
{
    /// <summary>
    /// 资产数据点挂载模型
    /// </summary>
    public class CustomDataPointViewModel
    {
        /// <summary>
        /// 主键
        /// </summary>
        public int Id { get; set; }
        /// <summary>
        /// 真实数据源资产名
        /// </summary>
        public string RealAssetName { get; set; } = string.Empty;
        /// <summary>
        /// 真实数据源资产类型
        /// </summary>
        public string RealAssetType { get; set; } = string.Empty;
        /// <summary>
        /// 真实数据源资产型号
        /// </summary>
        public string RealAssetModel { get; set; } = string.Empty;

        /// <summary>
        /// 真实资产 Id
        /// </summary>
        public int RealAssetId { get; set; }
        /// <summary>
        /// 真实资产数据点位
        /// </summary>
        public string RealDataPoint { get; set; } = string.Empty;
        /// <summary>
        /// 值对应关系
        /// </summary>
        public Dictionary<string, string> ValueMapping { get; set; } = new Dictionary<string, string>();
        /// <summary>
        /// 被挂载资产 Id
        /// </summary>
        public int TargetAssetId { get; set; }
        /// <summary>
        /// 被挂载的数据点位
        /// </summary>
        public string TargetDataPoint { get; set; } = string.Empty;
        public bool IsBit { get; set;} = false;
        public string PropertyEnName { get; set; } = string.Empty;
        public string PropertyCnName { get; set; } = string.Empty;
        public string Unit { get; set; } = string.Empty;


    }
}
