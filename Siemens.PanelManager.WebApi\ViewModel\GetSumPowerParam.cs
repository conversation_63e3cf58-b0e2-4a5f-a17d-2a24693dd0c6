﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Siemens.PanelManager.Model.Database.Asset;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class GetSumPowerParam
    {
        [FromRoute(Name = "assetId")]
        public int AssetId { get; set; }
        [FromQuery(Name = "ChartDateType")]
        public ChartDateType ChartDateType { get; set; } = ChartDateType.Get24Hour;
    }

    /// <summary>
    /// 入参
    /// </summary>
    public class PowerCalculationParam
    {  
        /// <summary>
        /// 资产id
        /// </summary>
        public int AssetId { get; set; }

        [FromQuery(Name = "measurementType")]
        public string? MeasurementType { get; set; }

        [FromQuery(Name = "loop")]
        public string? Loop { get; set; }

        [FromQuery(Name = "device")]
        public string? Device { get; set; }

        [BindNever]
        public string MeasurementFinal
        {
            get
            {
                try
                {
                    if (string.IsNullOrWhiteSpace(MeasurementType) || MeasurementType.ToUpper() == "ALL")
                    {
                        return string.Empty;
                    }

                    if (int.TryParse(MeasurementType, out var tempValue))
                    {
                        return ((MeasurementType)tempValue).ToString();
                    }

                    return string.Empty;
                }
                catch
                {
                    return string.Empty;
                }
            }
        }

        [BindNever]
        public int CircuitId
        {
            get
            {
                try
                {
                    if (string.IsNullOrWhiteSpace(Loop) || Loop.ToUpper() == "ALL")
                    {
                        return 0;
                    }

                    if (int.TryParse(Loop, out var tempValue))
                    {
                        return tempValue;
                    }

                    return 0;
                }
                catch
                {
                    return 0;
                }
            }
        }
    }

    /// <summary>
    /// 出参
    /// </summary>
    public class PowerCalculationResutl
    {
        /// <summary>
        /// 有功功率
        /// </summary>
        public decimal? ActivePower { get; set; } = 0;

        /// <summary>
        /// 无功功率
        /// </summary>
        public decimal? ReactivePower { get; set; } = 0;

        /// <summary>
        /// 当日电能
        /// </summary>
        public decimal? DailyByElectricity { get; set; } = 0;
    }


}
