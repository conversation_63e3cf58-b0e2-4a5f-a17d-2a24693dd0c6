﻿using Newtonsoft.Json;
using Siemens.PanelManager.Model.Topology;

namespace Siemens.PanelManager.Job.UploadExcel.Diagram.TemplateModel.NodeTemplate
{
    /// <summary>
    /// 单线图中标题元素
    /// 自动生成用于 单线图图纸名
    /// </summary>
    internal class TitleNode : NodeData
    {
        public override NodeType NodeType => NodeType.Title;
        public TitleNode() 
        {
            Name = "标题";
            TypeCode = "Diagram Title";
            CloseStyle = "comment";
            SizeHight = 40;
            SizeWidth = 40;
            FontColor = "#000";
            FontSize = "42pt";
            Category = "diagramTitleTemplate";
        }

        [JsonProperty("fontColor", NullValueHandling = NullValueHandling.Ignore)]
        public string FontColor { get; set; }
        [JsonProperty("fontSize", NullValueHandling = NullValueHandling.Ignore)]
        public string FontSize { get; set; }
        [JsonProperty("text")]
        public string Text { get; set; } = string.Empty;
    }
}
