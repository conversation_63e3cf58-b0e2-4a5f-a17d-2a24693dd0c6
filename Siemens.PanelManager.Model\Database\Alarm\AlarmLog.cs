﻿using Newtonsoft.Json;
using SqlSugar;

namespace Siemens.PanelManager.Model.Database.Alarm
{
    [SugarTable("alarm_log")]
    public class AlarmLog : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsNullable = false, IsIdentity = true)]
        public long Id { get; set; }
        [SugarColumn(ColumnName = "rule_id", IsNullable = true)]
        public int? RuleId { get; set; }
        [SugarColumn(ColumnName = "event_type", IsNullable = false)]
        public AlarmEventType EventType { get; set; }
        [SugarColumn(ColumnName = "status", IsNullable = false)]
        public AlarmLogStatus Status { get; set; }
        [SugarColumn(ColumnName = "severity", IsNullable = false)]
        public AlarmSeverity Severity { get; set; }
        [SugarColumn(ColumnName = "message", IsNullable = false, Length = 1024)]
        public string Message { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "asset_id", IsNullable = true)]
        public int? AssetId { get; set; }
        [SugarColumn(ColumnName = "device_name", IsNullable = true, Length = 256)]
        public string? DeviceName { get; set; }
        [SugarColumn(ColumnName = "circuit_name", IsNullable = true, Length = 256)]
        public string? CircuitName { get; set; }
        [SugarColumn(ColumnName = "panel_name", IsNullable = true, Length = 256)]
        public string? PanelName { get; set; }
        [SugarColumn(ColumnName = "substation_name", IsNullable = true, Length = 256)]
        public string? SubstationName { get; set; }
        [SugarColumn(ColumnName = "asset_status", IsNullable = true, ColumnDataType = "varchar(10240)")]
        public string? AssetStatusStr { get; set; }
        [SugarColumn(ColumnName = "asset_settings", IsNullable = true, ColumnDataType = "varchar(10240)")]
        public string? AssetSettingsStr { get; set; }

        [SugarColumn(ColumnName = "extend", IsNullable = true, Length = 256)]
        public string? Extend { get; set; }

        [SugarColumn(IsIgnore = true)]
        public Dictionary<string, string>? AssetStatus
        {
            get
            {
                if (string.IsNullOrEmpty(AssetStatusStr)) return null;
                try
                {
                    return JsonConvert.DeserializeObject<Dictionary<string, string>>(AssetStatusStr);
                }
                catch
                {
                    return null;
                }
            }
            set
            {
            }
        }
        [SugarColumn(IsIgnore = true)]
        public Dictionary<string, string>? AssetSettings
        {
            get
            {
                if (string.IsNullOrEmpty(AssetSettingsStr)) return null;
                try
                {
                    return JsonConvert.DeserializeObject<Dictionary<string, string>>(AssetSettingsStr);
                }
                catch
                {
                    return null;
                }
            }
            set
            {
            }
        }

        [SugarColumn(IsIgnore = true)]
        public string StatusStr
        {
            get
            {
                return Status switch
                {
                    AlarmLogStatus.New => "New",
                    AlarmLogStatus.InProcess => "InProcess",
                    AlarmLogStatus.Finish => "Finish",
                    AlarmLogStatus.Error => "Error",
                    _ => string.Empty
                };
            }
        }

        [SugarColumn(IsIgnore = true)]
        public string SeverityStr
        {
            get
            {
                return Severity switch
                {
                    AlarmSeverity.Low => "Low",
                    AlarmSeverity.High => "High",
                    AlarmSeverity.Middle => "Middle",
                    _ => string.Empty
                };
            }
        }

        [SugarColumn(IsIgnore = true)]
        public string EventTypeStr
        {
            get
            {
                return EventType switch
                {
                    AlarmEventType.Alarm => "Alarm",
                    AlarmEventType.UdcAlarm => "UDC Alarm",
                    AlarmEventType.BreakerTrip => "Breaker Trip",
                    AlarmEventType.OperationLog => "Operation Log",
                    AlarmEventType.DeviceLog => "Device Log",
                    AlarmEventType.CommunicationAlarm => "Communication Alarm",
                    _ => string.Empty
                };
            }
        }
    }

    public enum AlarmEventType : int
    {
        All = -99,
        Alarm = 0,
        UdcAlarm = 10,
        BreakerTrip = 11,
        OperationLog = 2,
        DeviceLog = 9,
        CommunicationAlarm = 8
    }

    public enum AlarmLogStatus : int
    {
        New = 0,
        InProcess = 10,
        Finish = 20,
        Error = 99,
        Ignore = -1,
        None = -2,
    }
}
