import React, { useState } from "react";
import { getProcessList } from "../../api/homeApi";

export default function ProcessList() {
  let [processes, setData] = useState(null);
  if (processes === null) {
    getProcessList(setData);
    processes = [];
  } else {
    setTimeout(() => {
      getProcessList(setData);
    }, 10000);
  }

  const trList = processes?.map((i) => (
    <tr>
      <td>{i.pid}</td>
      <td>{i.id}</td>
      <td>{i.name}</td>
      <td>{i.CPU}</td>
      <td>{i.mem}</td>
    </tr>
  ));
  return (
    <table className="table table-striped">
      <thead>
        <th scope="col">程序Id</th>
        <th scope="col">镜像Id</th>
        <th scope="col">程序名</th>
        <th scope="col">CPU</th>
        <th scope="col">Mem</th>
      </thead>
      <tbody>{trList}</tbody>
    </table>
  );
}
