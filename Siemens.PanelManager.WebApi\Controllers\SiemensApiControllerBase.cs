﻿using Microsoft.AspNetCore.Mvc;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.WebApi.Models;
using Siemens.PanelManager.WebApi.StaticContent;

namespace Siemens.PanelManager.WebApi.Controllers
{
    public abstract class SiemensApiControllerBase : ControllerBase
    {
        private IMessageContextFactory _messageManager => _provider.GetRequiredService<IMessageContextFactory>();
        private IServiceProvider _provider;
        internal IServiceProvider Provider
        {
            get
            {
                return _provider;
            }
        }

        private SiemensCache _cache;
        internal SiemensCache Cache
        {
            get
            {
                return _cache;
            }
        }

        public SiemensApiControllerBase(
            IServiceProvider provider,
            SiemensCache cache) 
        {
            _cache = cache;
            _provider = provider;
        }

        private string? _headLanguage;

        private MessageContext? messageContext;

        internal MessageContext MessageContext
        {
            get
            {
                if (messageContext == null)
                {
                    messageContext = (MessageContext)_messageManager.GetMessageContext(UserLanguage);
                    _cache.Set<IMessageContext>("MessageByContext",messageContext);
                }

                return messageContext;
            }
        }

        private bool hasLoad;
        private UserSessionInfo? _userSession;
        internal UserSessionInfo? UserSession
        {
            get
            {
                if (!hasLoad)
                {
                    hasLoad = true;
                    {
                        var sessionId = this.User.GetSessionId();
                        if (!string.IsNullOrEmpty(sessionId))
                        {
                            _userSession = _cache.Get<UserSessionInfo>($"UserSession:{sessionId}");

                        }
                    }
                }

                return _userSession;
            }
        }

        internal string UserName
        {
            get 
            {
                return User.Identity?.Name ?? string.Empty;
            }
        }

        internal string UserLanguage
        {
            get 
            {
                if (_headLanguage == null)
                {
                    _headLanguage = string.Empty;
                    if (Request.Headers.TryGetValue("Language", out var languageValue))
                    {
                        var language = languageValue.ToString();
                        switch (language)
                        {
                            case "zh":
                                _headLanguage = "zh-cn";
                                break;
                            default:
                                _headLanguage = language.ToLower();
                                break;
                        }
                    }
                }

                if (string.IsNullOrEmpty(_headLanguage))
                {
                    return UserSession?.Language ?? "zh-cn";
                }

                return _headLanguage;
            }
        }
    }
}
