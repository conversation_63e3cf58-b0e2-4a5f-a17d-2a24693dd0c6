﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Model.Database.Topoplogy;
using Siemens.PanelManager.Model.Topology;
using Siemens.PanelManager.Server.Topoplogy;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;
using System.Diagnostics.CodeAnalysis;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class TopologyDraftController : SiemensApiControllerBase
    {
        private IServiceProvider _provider;
        private SiemensCache _cache;
        public TopologyDraftController(IServiceProvider provider, SiemensCache cache)
            : base(provider, cache)
        {
            _cache = cache;
            _provider = provider;
        }

        [HttpGet("{code}/check")]
        [SwaggerOperation(Summary = "Swagger_TopologyDraft_Get", Description = "Swagger_TopologyDraft_Get_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<TopologyDraftSimpleInfo>> CheckDraftInfo(string code)
        {
            code = GetCode(code);
            if (string.IsNullOrEmpty(code))
            {
                return new ResponseBase<TopologyDraftSimpleInfo>
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            var userId = UserSession?.UserId ?? -1;
            using var client = _provider.GetRequiredService<ISqlSugarClient>();
            var draft = await client.Queryable<TopologyDraftInfo>()
                .Where(t => t.UserId == userId && t.TopologyType == code)
                .Select(t => new TopologyDraftSimpleInfo
                {
                    DraftCode = t.TopologyAction == TopologyAction.New ? 10 : 11,
                    TopologyId = t.TopologyId,
                    TopologyName = t.TopologyName,
                    DraftId = t.Id,

                    DraftTime = t.UpdatedTime
                })
                .FirstAsync();

            if (draft == null)
            {
                return new ResponseBase<TopologyDraftSimpleInfo>
                {
                    Code = 20000,
                    Data = new TopologyDraftSimpleInfo
                    {
                        DraftCode = 0
                    }
                };
            }

            return new ResponseBase<TopologyDraftSimpleInfo>
            {
                Code = 20000,
                Data = draft
            };
        }

        [HttpDelete("{code}")]
        [SwaggerOperation(Summary = "Swagger_TopologyDraft_Delete", Description = "Swagger_TopologyDraft_Delete_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<bool>> Remove(string code)
        {
            code = GetCode(code);
            if (string.IsNullOrEmpty(code))
            {
                return new ResponseBase<bool>
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            var svc = _provider.GetRequiredService<TopologyDraftService>();
            await svc.DeleteDraftByUserId(UserSession?.UserId ?? 0, code, remove: true);
            return new ResponseBase<bool>
            {
                Code = 20000,
                Data = true
            };
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="code"></param>
        /// <param name="opt">10: 新建 11: 更新</param>
        /// <param name="flag"></param>
        /// <param name="topologyId"></param>
        /// <param name="topologyName"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPut("{code}")]
        [SwaggerOperation(Summary = "Swagger_TopologyDraft_Save", Description = "Swagger_TopologyDraft_Save_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<bool>> SaveDraft(
            [FromRoute] string code,
            [FromQuery] int opt,
            [FromQuery][AllowNull] string? flag,
            [FromQuery][AllowNull] int? topologyId,
            [FromQuery][AllowNull] string? topologyName,
            [FromBody] JObject data)
        {
            code = GetCode(code);
            if (string.IsNullOrEmpty(code))
            {
                return new ResponseBase<bool>
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            var userId = UserSession?.UserId ?? 0;
            var sessionId = UserSession?.Id ?? string.Empty;

            if (opt == 11 && (!topologyId.HasValue || topologyId < 0 || string.IsNullOrEmpty(flag)))
            { 
                return new ResponseBase<bool>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }

            var draftCacheInfo = _cache.Get<TopologyDraftCacheInfo>(string.Format(TopologyDraftCacheInfo.DraftCacheKey, code, userId));
            if (draftCacheInfo != null && draftCacheInfo.IsActive && !draftCacheInfo.SessionId.Equals(sessionId))
            {
                return new ResponseBase<bool>
                {
                    Code = 20000,
                    Data = false
                };
            }

            if (draftCacheInfo != null
                && !draftCacheInfo.IsActive
                && topologyId.HasValue
                && draftCacheInfo.TopologyId.HasValue
                && draftCacheInfo.TopologyId == topologyId
                && !string.IsNullOrEmpty(flag)
                && !string.IsNullOrEmpty(draftCacheInfo.TopologyFlag)
                && flag.Equals(draftCacheInfo.TopologyFlag))
            {
                return new ResponseBase<bool>
                {
                    Code = 20000,
                    Data = true
                };
            }

            _cache.Set(string.Format(TopologyDraftCacheInfo.DraftCacheKey, code, userId), new TopologyDraftCacheInfo
            {
                IsActive = true,
                SessionId = sessionId,
                TopologyId = topologyId,
                TopologyFlag = flag,
            }, TimeSpan.FromMinutes(3));

            using var client = _provider.GetRequiredService<ISqlSugarClient>();
            var storageable = await client.Storageable(new TopologyDraftInfo
            {
                CreatedBy = UserName,
                CreatedTime = DateTime.Now,
                TopologyAction = (TopologyAction)opt,
                TopologyFlag = flag,
                TopologyId = topologyId,
                TopologyName = topologyName ?? string.Empty,
                TopologyType = code,
                UserId = userId,
                Data = JsonConvert.SerializeObject(data),
                UpdatedBy = UserName,
                UpdatedTime = DateTime.Now,
            }).WhereColumns(t => new { t.UserId, t.TopologyType }).ToStorageAsync();

            await storageable.AsUpdateable.IgnoreColumns(t => new { t.CreatedBy, t.CreatedTime }).ExecuteCommandAsync();
            await storageable.AsInsertable.ExecuteCommandAsync();

            return new ResponseBase<bool>
            {
                Code = 20000,
                Data = true
            };
        }

        [HttpGet("{code}/DraftInfo")]
        [SwaggerOperation(Summary = "Swagger_TopologyDraft_GetDraftInfo", Description = "Swagger_TopologyDraft_GetDraftInfo_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<JObject>> GetDraftInfo(string code)
        {
            code = GetCode(code);
            if (string.IsNullOrEmpty(code))
            {
                return new ResponseBase<JObject>
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            var userId = UserSession?.UserId ?? 0;

            using var client = _provider.GetRequiredService<ISqlSugarClient>();
            var draft = await client.Queryable<TopologyDraftInfo>()
                .Where(t => t.UserId == userId && t.TopologyType == code)
                .FirstAsync();

            if (string.IsNullOrEmpty(draft.Data))
            {
                return new ResponseBase<JObject>()
                {
                    Code = 20000,
                    Data = new JObject()
                };
            }

            var draftInfo = JObject.Parse(draft.Data);
            draftInfo["flag"] = draft.TopologyFlag;
            return new ResponseBase<JObject>
            {
                Code = 20000,
                Data = draftInfo
            };
        }

        private string GetCode(string code) 
        {
            switch (code.ToLower())
            {
                case "topology": return "topology";
                case "3d": return "3D";
                default: break;
            }
            return string.Empty;
        }
    }
}
