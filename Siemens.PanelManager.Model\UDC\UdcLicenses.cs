﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.Model.UDC
{
    public class UdcLicenses : UdcListResulBase<IdItem>
    {
        [JsonProperty("order_number")]
        public string OrderNumber { get; set; } = string.Empty;
        [JsonProperty("serial_number")]
        public string SerialNumber { get; set; } = string.Empty;
    }

    public class IdItem : IUdcData
    {
        [JsonProperty("id")]
        public string Id { get; set; } = string.Empty;
        [JsonProperty("state")]
        public string State { get; set; } = string.Empty;
    }
}
