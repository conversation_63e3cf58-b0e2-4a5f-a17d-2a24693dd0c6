﻿using Akka.Actor;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.DeviceDataFlow.Model;

namespace Siemens.PanelManager.DeviceDataFlow.DataFlowActors
{
    internal class CallbackActor : ReceiveActor
    {
        private readonly ILogger<CallbackActor> _logger;
        
        public CallbackActor(ILogger<CallbackActor> logger) 
        {
            _logger = logger;
            ReceviceFunc();
        }
        protected override bool AroundReceive(Receive receive, object message)
        {
            try
            {
                return base.AroundReceive(receive, message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "CallbackActor Failed");
                return true;
            }
        }

        private void ReceviceFunc()
        {
            Receive<AssetCallbackParam>(CallbackFunc);
        }

        private void CallbackFunc(AssetCallbackParam param)
        {
            try
            {
                param.Action(param.ChangeData);
            }
            catch (Exception ex) 
            {
                _logger.LogError(ex, "方法调用失败");
            }
        }
    }
}
