﻿using Newtonsoft.Json;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Topology3D;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.UploadExcel._3D.Model
{
    internal abstract class ICabinetNode : NodeBase3D
    {
        [JsonIgnore]
        public AssetInfo AssetInfo { get; protected set; }

        [JsonIgnore]
        public GroupNode? GroupNode { get; set; }

        public abstract SingalCabinetNode GetSingalNode();

        public abstract ICabinetNode GetNew();
    }
}
