﻿

namespace Siemens.PanelManager.Common.IO
{
    /// <summary>
    /// 文件帮助类
    /// </summary>
    public class FileHelper : IDisposable
    {

        private bool _alreadyDispose;

        /// <summary>
        /// 析构函数
        /// </summary>
        ~FileHelper()
        {
            Dispose();
        }

        protected virtual void Dispose(bool isDisposing)
        {
            if (_alreadyDispose) return;
            _alreadyDispose = true;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 文件流程转化文件
        /// </summary>
        /// <param name="stream"></param>
        /// <param name="filePath"></param>
        public static void StreamToFile(Stream stream, string filePath)
        {
            // 把 Stream 转换成 byte[]
            byte[] bytes = new byte[stream.Length];
            int read = stream.Read(bytes, 0, bytes.Length);

            // 设置当前流的位置为流的开始
            Console.WriteLine(read);
            stream.Seek(0, SeekOrigin.Begin);

            // 把 byte[] 写入文件
            FileStream fs = new FileStream(filePath, FileMode.Create);
            BinaryWriter bw = new BinaryWriter(fs);

            bw.Write(bytes);
            bw.Close();
            fs.Close();
        }

        /// <summary>
        /// 二进制转化为文件
        /// </summary>
        /// <param name="bytes"></param>
        /// <param name="filePath"></param>
        public static void ByteToFile(Byte[] bytes, string filePath)
        {
            // 把 byte[] 写入文件
            FileStream fs = new FileStream(filePath, FileMode.Create);
            BinaryWriter bw = new BinaryWriter(fs);

            bw.Write(bytes);
            bw.Close();
            fs.Close();
        }

        /// <summary>
        /// 二进制转换为文件
        /// </summary>
        /// <param name="bytes"></param>
        /// <param name="filePath"></param>
        /// <returns></returns>
        public static string ByteByFile(byte[] bytes, string filePath)
        {
            File.WriteAllBytes(filePath, bytes);
            return filePath;
        }

        /// <summary>
        /// 根据当前路径获取文件的绝对路径
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        public static string FilePath(string path)
        {
            var currentPath = Directory.GetCurrentDirectory();
            path = Path.Combine(currentPath, "wwwroot") + path;
            return path;
        }

        /// <summary>
        /// 字符串转Stream
        /// </summary>
        /// <param name="jsonText"></param>
        /// <returns></returns>
        public static Stream JsonToStream(string jsonText)
        {
            var stream = new MemoryStream();
            var writer = new StreamWriter(stream);
            writer.Write(jsonText);
            writer.Flush();
            stream.Position = 0;
            return stream;
        }

        /// <summary>
        /// steam to bite
        /// </summary>
        /// <param name="stream"></param>
        /// <returns></returns>
        public static byte[] StreamToBytes(Stream stream)
        {
            byte[] byteFile;
            List<byte> bytes = new List<byte>();
            int temp = stream.ReadByte();

            while (temp != -1)
            {
                bytes.Add((byte)temp);
                temp = stream.ReadByte();
            }

            byteFile = bytes.ToArray();

            return byteFile;
        }

        /// <summary>
        /// 将 byte[]转成 Stream 
        /// </summary>
        /// <param name="bytes"></param>
        /// <returns></returns>
        public static Stream BytesToStream(byte[] bytes)
        {
            Stream stream = new MemoryStream(bytes);

            return stream;
        }

        /// <summary>
        /// 文件转stream
        /// </summary>
        /// <param name="filePath"></param>
        /// <returns></returns>
        public static Stream FileToStream(string filePath)
        {
            // 打开文件
            FileStream fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read);

            byte[] bytes = new byte[fileStream.Length];

            fileStream.Read(bytes, 0, bytes.Length);
            // 把 byte[] 转换成 Stream
            Stream stream = new MemoryStream(bytes);

            fileStream.Close();

            return stream;
        }

        /// <summary>
        /// 字符串转stream
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static Stream GetStrByStream(string str)
        {
            
            // 初始化一个在内存中的流对象：MemoryStream
            var stream = new MemoryStream();

            // 初始化一个写入流对象
            var writer = new StreamWriter(stream);

            writer.Write(str);

            writer.Flush();

            stream.Position = 0;

            return stream;
        }
    }
}
