﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class LossDiagnosisKPI
    {
        [JsonProperty("currentDate")]
        public LossDiagnosisKPIItem Current { get; set; } = new LossDiagnosisKPIItem();
        [JsonProperty("yearDate")]
        public LossDiagnosisKPIItem Year { get; set; } = new LossDiagnosisKPIItem();
        [JsonProperty("monthDate")]
        public LossDiagnosisKPIItem Month { get; set; } = new LossDiagnosisKPIItem();
        [JsonProperty("sum")]
        public LossDiagnosisKPIItem Sum { get; set; } = new LossDiagnosisKPIItem();
    }

    public class LossDiagnosisKPIItem
    {
        [JsonProperty("loss", NullValueHandling = NullValueHandling.Include)]
        public decimal? Loss { get; set; }
        [JsonProperty("loss_unit")]
        public string LossUnit => "KWH";
        [JsonProperty("loss_equivalent_electricity", NullValueHandling = NullValueHandling.Include)]
        public decimal? Electricity { get; set; }
        [JsonProperty("loss_equivalent_electricity_unit")]
        public string ElectricityUnit => "CNY";
        [JsonProperty("loss_pct", NullValueHandling = NullValueHandling.Include)]
        public decimal? Percentage { get; set; }
        [JsonProperty("loss_pct_unit")]
        public string PercentageUnit => "%";
        [JsonProperty("system_efficiency", NullValueHandling = NullValueHandling.Include)]
        public decimal? SystemEfficiency { get; set; }
        [JsonProperty("system_efficiency_unit")]
        public string SystemEfficiencyUnit => "%";
    }
}
