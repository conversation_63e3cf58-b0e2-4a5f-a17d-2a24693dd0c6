{"info": {"_postman_id": "96eac4ea-e3fb-42a6-9030-a7cdf65ecb26", "name": "06使用管理员账号进入panel manager资产管理中的资产列表菜单，点击左上角的+按钮添加设备", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "添加设备", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"生成资产id\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"assetId\");\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"type\": \"add\",\r\n  \"assetData\": {\r\n    \"assetName\": \"Device01\",\r\n    \"person\": \"h123\",\r\n    \"tel\": \"1231231\",\r\n    \"description\": \"hkn@\",\r\n    \"level\": \"Device\",\r\n    \"type\": \"MCCB\",\r\n    \"model\": \"3VA\",\r\n    \"mlfb\": \"string\",\r\n    \"factory\": \"string\",\r\n    \"location\": \"123@\",\r\n    \"pointData\": \"string\",\r\n    \"installDate\": \"2023-01-02T12:23:36.685\",\r\n    \"useScene\": \"string\",\r\n    \"meterType\": \"关口计量\",\r\n    \"circuitName\": \"string\",\r\n    \"drawing\": [\r\n      {\r\n        \"id\": 0,\r\n        \"name\": \"string\",\r\n        \"url\": \"string\"\r\n      }\r\n    ],\r\n    \"img\": [\r\n      {\r\n        \"id\": 0,\r\n        \"name\": \"string\",\r\n        \"url\": \"string\"\r\n      }\r\n    ],\r\n    \"topology\": {\r\n      \"additionalProp1\": [\r\n        \"string\"\r\n      ],\r\n      \"additionalProp2\": [\r\n        \"string\"\r\n      ],\r\n      \"additionalProp3\": [\r\n        \"string\"\r\n      ]\r\n    }\r\n  }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/Asset", "host": ["{{baseUrl}}"], "path": ["api", "v1", "<PERSON><PERSON>"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}]}