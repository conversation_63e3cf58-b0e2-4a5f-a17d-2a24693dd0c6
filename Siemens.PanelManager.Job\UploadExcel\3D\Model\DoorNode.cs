﻿using Siemens.PanelManager.Model.Topology3D;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.UploadExcel._3D.Model
{
    /// <summary>
    /// 门的3D模型
    /// </summary>
    internal class DoorNode : NodeBase3D
    {
        public DoorNode() 
            : base()
        {
            Name = "门";
            Size.Height = 2000;
            Size.Width = 800;
            Size.Depth = 100;
        }

        public override string NodeType => "door";
    }
}
