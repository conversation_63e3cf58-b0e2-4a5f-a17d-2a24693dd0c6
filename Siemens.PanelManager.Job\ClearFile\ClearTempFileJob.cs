﻿using Microsoft.Extensions.Logging;
using Quartz;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.Server.Common;

namespace Siemens.PanelManager.Job.ClearFile
{
    [DisallowConcurrentExecution]
    public class ClearTempFileJob : JobBase
    {
        private ILogger<ClearTempFileJob> _logger;
        private TempFileManager _tempFileManager;
        public ClearTempFileJob(ILogger<ClearTempFileJob> logger, TempFileManager tempFileManager)
        {
            _logger = logger;
            _tempFileManager = tempFileManager;
        }
        public override string Name => "ClearTempFile";

        public override Task Execute()
        {
            var minutes = 3 * 60;
            if (ContextData.ContainsKey("ExpireTime"))
            {
                var config = ContextData["ExpireTime"];
                int newTime;
                if (int.TryParse(config, out newTime))
                {
                    minutes = newTime;
                }
            }

            var endDate = DateTime.Now.AddMinutes(0 - minutes);

            #region 清理临时文件夹
            var files = _tempFileManager.GetTempFile(endDate: endDate);
            foreach (var f in files)
            {
                try
                {
                    f.Delete();
                }
                catch (Exception ex)
                {
                    _logger.LogError($"临时文件({f.Name})删除失败", ex);
                }
            }
            #endregion
            return Task.CompletedTask;
        }
    }
}
