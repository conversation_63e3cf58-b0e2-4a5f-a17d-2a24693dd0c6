﻿using SqlSugar;

namespace Siemens.PanelManager.Model.Database.Asset
{
    /// <summary>
    /// 通用设备配置表
    /// </summary>
    [SugarTable("universal_device_config", TableDescription = "通用设备配置表")]
    public class UniversalDeviceConfig : LogicDataBase
    {
        /// <summary>
        /// 主键id
        /// </summary>
        [SugarColumn(ColumnDescription = "主键id", ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// AssetInfo表的主键id
        /// </summary>
        [SugarColumn(ColumnDescription = "AssetInfo表的主键id", ColumnName = "asset_id", IsNullable = true)]
        public int AssetId { get; set; }

        /// <summary>
        /// ThirdModelConfig表的Code
        /// </summary>
        [SugarColumn(ColumnDescription = "ThirdModelConfig表的Code", ColumnName = "third_part_device_code", IsNullable = true, Length = 64)]
        public string? ThirdPartCode { get; set; }

        /// <summary>
        /// 点位英文名称
        /// </summary>
        [SugarColumn(ColumnDescription = "点位英文名称", ColumnName = "property_en_name", IsNullable = false, Length = 128)]
        public string? PropertyEnName { get; set; }

        /// <summary>
        /// 点位中文名称
        /// </summary>
        [SugarColumn(ColumnDescription = "点位中文名称", ColumnName = "property_cn_name", IsNullable = false, Length = 128)]
        public string? PropertyCnName { get; set; }

        /// <summary>
        /// 是否二进制位
        /// </summary>
        [SugarColumn(ColumnDescription = "是否二进制位", ColumnName = "is_bit", IsNullable = false)]
        public bool IsBit { get; set; } = false;

        /// <summary>
        /// 系数
        /// </summary>
        [SugarColumn(ColumnDescription = "系数", ColumnName = "coefficient", IsNullable = true)]
        public string? Coefficient { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [SugarColumn(ColumnDescription = "单位", ColumnName = "unit", IsNullable = true, Length = 50)]
        public string? Unit { get; set; }

        /// <summary>
        /// 分组信息（来自Json文件）
        /// </summary>
        [SugarColumn(ColumnDescription = "分组信息（来自Json文件）", ColumnName = "group_name", IsNullable = true, Length = 256)]
        public string? GroupName { get; set; }

        [SugarColumn(IsIgnore = true)]
        public string? Register { get; set; }

    }
}
