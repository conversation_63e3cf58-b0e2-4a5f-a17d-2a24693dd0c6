#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["Siemens.PanelManager.WebApi/Siemens.PanelManager.WebApi.csproj", "Siemens.PanelManager.WebApi/"]
RUN dotnet restore "Siemens.PanelManager.WebApi/Siemens.PanelManager.WebApi.csproj"
COPY . .
WORKDIR "/src/Siemens.PanelManager.WebApi"
RUN dotnet build "Siemens.PanelManager.WebApi.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Siemens.PanelManager.WebApi.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
ENV TZ Asia/Shanghai
COPY --from=publish /app/publish .
RUN chmod 777 DatabaseInitializeFile
RUN chmod 777 Language
ENTRYPOINT ["dotnet", "Siemens.PanelManager.WebApi.dll","--urls","http://0.0.0.0:5000"]