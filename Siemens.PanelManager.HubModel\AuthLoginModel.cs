﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.HubModel
{
    public class AuthLoginModel
    {
        public AuthLoginModel(string userName, string password, string publicKey)
        {
            RSACryptoServiceProvider provider = new RSACryptoServiceProvider();
            RSAParameters rsaParams = new RSAParameters();
            var publicKeyArray = Convert.FromBase64String(publicKey);
            rsaParams.Modulus = publicKeyArray;
            rsaParams.Exponent = publicKeyArray; // For simplicity, assume both Exponent and Modulus are same as Base64 encoded string length should be even number at this point due to correct input validation in the main method above
            provider.ImportParameters(rsaParams);
            UserName = userName;
            var code = $"{userName}|{password}|{DateTime.Now.ToString("yyyyMMddHHmmss")}|{RandomCode}";
            var codeBytes = Encoding.UTF8.GetBytes(code);
            var bytes = provider.Encrypt(codeBytes, false);
            EncryptCode = Convert.ToBase64String(bytes);
        }

        public string UserName { get; private set;} =string.Empty;
        public string EncryptCode { get; private set;} =string.Empty;
        public string RandomCode { get; private set; } = Guid.NewGuid().ToString();
    }
}
