﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.Excel;
using Siemens.PanelManager.Model.Chart;
using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.Model.Database.WorkOrder;
using Siemens.PanelManager.Model.WorkOrder;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class WorkOrderController : SiemensApiControllerBase
    {
        private static readonly string WORK_ORDER_CODE_BEGIN = "MWC";
        private ILogger<WorkOrderController> _log;
        private ISqlSugarClient _client;
        private SiemensExcelHelper _excelHelper;

        public WorkOrderController(SiemensCache cache,
            SqlSugarScope client,
            IServiceProvider provider,
            ILogger<WorkOrderController> logger,
            SiemensExcelHelper siemensExcelHelper)
            : base(provider, cache)
        {
            _log = logger;
            _client = client;
            _excelHelper = siemensExcelHelper;
        }

        [HttpGet("{id:int}")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_WorkOrder_GetWorkOrder", Description = "Swagger_WorkOrder_GetWorkOrder_Desc")]
        public async Task<ResponseBase<WorkOrderModel>> GetWorkOrderById(int id)
        {
            if (id <= 0)
            {
                return new ResponseBase<WorkOrderModel>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var workOrderInfo = await _client.Queryable<WorkOrderInfo>().InSingleAsync(id);
            if (workOrderInfo == null)
            {
                return new ResponseBase<WorkOrderModel>()
                {
                    Code = 40400,
                };
            }

            int.TryParse(workOrderInfo.Device, out var assetId);

            workOrderInfo.Contents = await _client.Queryable<WorkOrderContent>()
                .Where(a => a.WorkOrderId == workOrderInfo.Id).OrderBy(a => a.Id).ToListAsync();

            var fileIds = new List<int>();
            var fileManagers = new List<FileManager>();
            var fileInfos = new List<FileInfoModel>();
            if (workOrderInfo.Contents != null)
            {
                foreach (var item in workOrderInfo.Contents)
                {
                    if (item.AttachmentIds != null && item.AttachmentIds.Length != 0)
                    {
                        fileIds.AddRange(item.AttachmentIds.ToList());
                    }
                }

                if (fileIds.Count != 0)
                {
                    fileManagers = await _client.Queryable<FileManager>()
                        .Where(a => SqlFunc.ContainsArray(fileIds, a.Id))
                        .ToListAsync();

                    foreach (var fileId in fileIds)
                    {
                        var file = fileManagers.First(f => f.Id == fileId);
                        if (file != null)
                        {
                            fileInfos.Add(new FileInfoModel(file));
                        }
                    }
                }
            }

            var asset = await _client.Queryable<AssetInfo>().InSingleAsync(assetId);
            if (asset != null)
            {
                workOrderInfo.DeviceName = asset.AssetName;
            }

            var workOrderModel = new WorkOrderModel(workOrderInfo, MessageContext, fileInfos);

            return new ResponseBase<WorkOrderModel>()
            {
                Code = 20000,
                Data = workOrderModel
            };
        }

        [HttpGet("getWorkOrderList")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_WorkOrder_GetWorkOrderList", Description = "Swagger_WorkOrder_GetWorkOrderList_Desc")]
        public async Task<SearchBase<WorkOrderModel>> GetWorkOrderList([FromQuery] WorkOrderQueryParam queryParam)
        {
            if (queryParam.PageSize <= 0 || queryParam.Page <= 0)
            {
                return new SearchBase<WorkOrderModel>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            RefAsync<int> totalCount = new RefAsync<int>();

            var pageIds = await _client.Queryable<WorkOrderInfo, WorkOrderContent>((w, c) => new JoinQueryInfos(JoinType.Left, w.Id == c.WorkOrderId))
                .WhereIF(!string.IsNullOrWhiteSpace(queryParam.WorkOrderCode), (w, c) => SqlFunc.Contains(w.WorkOrderCode, queryParam.WorkOrderCode))
                .WhereIF(!string.IsNullOrWhiteSpace(queryParam.WorkOrderName), (w, c) => SqlFunc.Contains(w.WorkOrderName, queryParam.WorkOrderName))
                .WhereIF((!string.IsNullOrWhiteSpace(queryParam.WorkOrderType) && queryParam.WorkOrderType.ToUpper() != "ALL"), (w, c) => w.WorkOrderType == queryParam.WorkOrderType)
                .WhereIF((!string.IsNullOrWhiteSpace(queryParam.AssetType) && queryParam.AssetType.ToUpper() != "ALL"), (w, c) => w.AssetType == queryParam.AssetType)
                .WhereIF((!string.IsNullOrWhiteSpace(queryParam.Device) && queryParam.Device.ToUpper() != "ALL"), (w, c) => w.Device == queryParam.Device)
                .WhereIF((!string.IsNullOrWhiteSpace(queryParam.Status) && queryParam.Status.ToUpper() != "ALL"), (w, c) => w.Status == queryParam.Status)
                .WhereIF(!string.IsNullOrWhiteSpace(queryParam.Measure), (w, c) => SqlFunc.Contains(c.Measure, queryParam.Measure))
                .WhereIF(!string.IsNullOrWhiteSpace(queryParam.Content), (w, c) => SqlFunc.Contains(c.Content, queryParam.Content))
                .OrderByDescending((w, c) => w.CreatedTime)
                .GroupBy((w, c) => w.Id)
                .Select((w, c) => w.Id).ToPageListAsync(queryParam.Page, queryParam.PageSize, totalCount);

            var pageData = await _client.Queryable<WorkOrderInfo, AssetInfo>((w, a) => new JoinQueryInfos(JoinType.Left, w.Device == a.Id.ToString()))
                .Where((w, a) => SqlFunc.ContainsArray(pageIds, w.Id)).OrderByDescending((w, A) => w.CreatedTime).Select((w, a) => new WorkOrderInfo
                {
                    Id = w.Id,
                    WorkOrderCode = w.WorkOrderCode,
                    WorkOrderName = w.WorkOrderName,
                    WorkOrderType = w.WorkOrderType,
                    ProcessingTime = w.ProcessingTime,
                    ProcessingEndTime = w.ProcessingEndTime,
                    AssetType = w.AssetType,
                    Device = w.Device,
                    DeviceName = a.AssetName,
                    Status = w.Status,
                    CreatedTime = w.CreatedTime,
                    CreatedBy = w.CreatedBy,
                    UpdatedBy = w.UpdatedBy,
                    UpdatedTime = w.UpdatedTime,
                }).ToListAsync();

            var workOrderContents = await _client.Queryable<WorkOrderContent>().Where(a => SqlFunc.ContainsArray(pageIds, a.WorkOrderId)).OrderBy(a => a.Id).ToListAsync();

            pageData?.ForEach(p =>
            {
                p.Contents = workOrderContents.Where(a => a.WorkOrderId == p.Id).ToList();
            });

            var pageDataResult = new List<WorkOrderModel>();
            foreach (var item in pageData!)
            {
                pageDataResult.Add(new WorkOrderModel(item, MessageContext));
            }

            return new SearchBase<WorkOrderModel>()
            {
                Page = queryParam.Page,
                TotalCount = totalCount.Value,
                Code = 20000,
                Items = pageDataResult
            };
        }

        [HttpPost]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_WorkOrder_AddWorkOrder", Description = "Swagger_WorkOrder_AddWorkOrder_Desc")]
        public async Task<ResponseBase<WorkOrderNewResult>> AddWorkOrder(WorkOrderNewParam workOrderNewParam)
        {
            if (workOrderNewParam == null
                || string.IsNullOrEmpty(workOrderNewParam.WorkOrderType)
                || string.IsNullOrEmpty(workOrderNewParam.WorkOrderName)
                || string.IsNullOrEmpty(workOrderNewParam.AssetType)
                || workOrderNewParam.ProcessingTime == DateTime.MinValue
                || workOrderNewParam.ProcessingEndTime == DateTime.MinValue
                || workOrderNewParam.Contents == null
                || workOrderNewParam.Contents.Count == 0
                || string.IsNullOrWhiteSpace(workOrderNewParam.Contents[0].Content))
            {
                return new ResponseBase<WorkOrderNewResult>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var modelCodes = await _client.Queryable<SystemStaticModel>()
                .Where(a => a.Type == "WORK_ORDER_TYPE")
                .WithCache("AddWorkOrder_SystemStaticModel", (int)TimeSpan.FromHours(1).TotalSeconds)
                .Select(a => a.Code)
                .ToArrayAsync();

            if (!modelCodes.Contains(workOrderNewParam.WorkOrderType))
            {
                return new ResponseBase<WorkOrderNewResult>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var preCode = await _client.Queryable<WorkOrderInfo>()
                .Where(a => SqlFunc.DateIsSame(a.CreatedTime, DateTime.Now))
                .OrderByDescending(a => a.Id)
                .FirstAsync();

            var workOrderInfo = new WorkOrderInfo
            {
                WorkOrderType = workOrderNewParam.WorkOrderType,
                Device = workOrderNewParam.Device,
                WorkOrderCode = GenerateWorkOrderCode(preCode?.WorkOrderCode ?? string.Empty),
                WorkOrderName = workOrderNewParam.WorkOrderName,
                AssetType = workOrderNewParam.AssetType,
                ProcessingTime = workOrderNewParam.ProcessingTime,
                ProcessingEndTime = workOrderNewParam.ProcessingEndTime,
                Status = string.IsNullOrWhiteSpace(workOrderNewParam.Contents[0].Measure) ? WorkOrderStatus.Pending.ToString() : WorkOrderStatus.Processing.ToString(),
                CreatedBy = (UserSession?.UserId ?? 0).ToString(),
                CreatedTime = DateTime.Now,
            };

            int insertWorkOrderId = 0;

            try
            {
                _client.Ado.BeginTran();

                insertWorkOrderId = await _client.Insertable(workOrderInfo).ExecuteReturnIdentityAsync();

                List<WorkOrderContent> workOrderContents = workOrderNewParam.Contents.Select(x => new WorkOrderContent
                {
                    WorkOrderId = insertWorkOrderId,
                    AddTime = DateTime.Now,
                    Content = x.Content,
                    Measure = x.Measure,
                    Tag = x.Tag,
                    ReserveId = x.ReserveId,
                    AttachmentIds = x.Attachment?.Select(x => x.Id).ToArray() ?? [],
                    CreatedBy = (UserSession?.UserId ?? 0).ToString(),
                    CreatedTime = DateTime.Now,
                }).ToList();

                await _client.Insertable(workOrderContents).ExecuteCommandAsync();

                _client.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "add new work order error");
                return new ResponseBase<WorkOrderNewResult>()
                {
                    Code = 50000,
                    Message = MessageContext.ServerException,
                };
            }

            return new ResponseBase<WorkOrderNewResult>()
            {
                Code = 20000,
                Data = new WorkOrderNewResult()
                {
                    Id = insertWorkOrderId
                }
            };
        }

        [HttpPut]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_WorkOrder_UpdateWorkOrder", Description = "Swagger_WorkOrder_UpdateWorkOrder_Desc")]
        public async Task<ResponseBase<string>> UpdateWorkOrder(WorkOrderUpdateParam workOrderUpdateParam)
        {
            if (workOrderUpdateParam == null
                || workOrderUpdateParam.Id <= 0
                || string.IsNullOrEmpty(workOrderUpdateParam.WorkOrderType)
                || string.IsNullOrEmpty(workOrderUpdateParam.AssetType)
                || workOrderUpdateParam.ProcessingTime == DateTime.MinValue
                || workOrderUpdateParam.ProcessingTime == DateTime.MinValue
                || workOrderUpdateParam.Contents == null
                || workOrderUpdateParam.Contents.Count == 0
                || string.IsNullOrWhiteSpace(workOrderUpdateParam.Contents[0].Content))
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            int.TryParse(workOrderUpdateParam.Device, out var assetId);

            var modelCodes = await _client.Queryable<SystemStaticModel>()
                .Where(a => a.Type == "WORK_ORDER_TYPE")
                .WithCache("AddWorkOrder_SystemStaticModel", (int)TimeSpan.FromHours(1).TotalSeconds)
                .Select(a => a.Code)
                .ToArrayAsync();

            if (!modelCodes.Contains(workOrderUpdateParam.WorkOrderType))
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var currentWorkOrder = await _client.Queryable<WorkOrderInfo>().InSingleAsync(workOrderUpdateParam.Id);
            if (currentWorkOrder == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                };
            }

            List<WorkOrderContent> workOrderContents = new List<WorkOrderContent>();
            WorkOrderContent data;

            foreach (var item in workOrderUpdateParam.Contents)
            {
                data = new WorkOrderContent();
                data.Id = item.Id;
                data.WorkOrderId = currentWorkOrder.Id;
                data.Content = item.Content;
                data.Measure = item.Measure;

                if (item.Id > 0)
                {
                    data.UpdatedBy = (UserSession?.UserId ?? 0).ToString();
                    data.UpdatedTime = DateTime.Now;
                }
                else
                {
                    data.AddTime = DateTime.Now;
                    data.CreatedBy = (UserSession?.UserId ?? 0).ToString();
                    data.CreatedTime = DateTime.Now;
                }

                data.AttachmentIds = item.Attachment?.Select(x => x.Id).ToArray() ?? [];

                workOrderContents.Add(data);
            }

            var needAddContents = workOrderContents.Where(a => a.Id <= 0).ToList();
            var needUpdateContents = workOrderContents.Where(a => a.Id > 0).ToList();


            try
            {
                _client.Ado.BeginTran();

                currentWorkOrder.AssetType = workOrderUpdateParam.AssetType;
                currentWorkOrder.WorkOrderType = workOrderUpdateParam.WorkOrderType;
                currentWorkOrder.Device = workOrderUpdateParam.Device;
                currentWorkOrder.ProcessingTime = workOrderUpdateParam.ProcessingTime;
                currentWorkOrder.ProcessingEndTime = workOrderUpdateParam.ProcessingEndTime;
                if (currentWorkOrder.Status == WorkOrderStatus.Pending.ToString()
                    || currentWorkOrder.Status == WorkOrderStatus.Completed.ToString())
                {
                    currentWorkOrder.Status = WorkOrderStatus.Processing.ToString();
                }
                currentWorkOrder.UpdatedTime = DateTime.Now;
                currentWorkOrder.UpdatedBy = (UserSession?.UserId ?? 0).ToString();

                await _client.Updateable(currentWorkOrder)
                    .IgnoreColumns(a => new { a.CreatedBy, a.CreatedTime, a.WorkOrderName, a.WorkOrderCode })
                    .ExecuteCommandAsync();

                if (needAddContents.Any())
                {
                    await _client.Insertable(needAddContents).ExecuteCommandAsync();
                }

                if (needUpdateContents.Any())
                {
                    await _client.Updateable(needUpdateContents!)
                        .UpdateColumns(a => new { a.Measure, a.Content, a.AttachmentIdsStr, a.UpdatedBy, a.UpdatedTime })
                        .WhereColumns(a => new { a.Id })
                        .ExecuteCommandAsync();
                }

                _client.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "update work order error");
                return new ResponseBase<string>()
                {
                    Code = 50000,
                    Message = MessageContext.ServerException
                };
            }

            return new ResponseBase<string>()
            {
                Code = 20000,
                Message = MessageContext.Success
            };
        }

        [HttpDelete("{workOrderId:int}")]
        [SwaggerOperation(Summary = "Swagger_WorkOrder_Delete", Description = "Swagger_WorkOrder_Delete_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> Delete(int workOrderId)
        {
            if (workOrderId <= 0)
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }
            var workOrder = await _client.Queryable<WorkOrderInfo>().InSingleAsync(workOrderId);
            if (workOrder == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                };
            }

            try
            {
                _client.Ado.BeginTran();
                await _client.Deleteable(workOrder).ExecuteCommandAsync();
                _client.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "delete work order error");
                return new ResponseBase<string>()
                {
                    Code = 50000,
                    Message = MessageContext.ServerException
                };
            }

            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = MessageContext.Success
            };
        }

        [HttpDelete("batch")]
        [SwaggerOperation(Summary = "Swagger_WorkOrder_Delete_Batch", Description = "Swagger_WorkOrder_Delete_Batch_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> DeleteBatch(int[] workOrderIds)
        {
            if (workOrderIds == null || workOrderIds.Length < 0)
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            try
            {
                _client.Ado.BeginTran();
                await _client.Deleteable<WorkOrderInfo>().Where(a => workOrderIds.Contains(a.Id)).ExecuteCommandAsync();
                _client.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "delete batch work order error");
                return new ResponseBase<string>()
                {
                    Code = 50000,
                    Message = MessageContext.ServerException
                };
            }

            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = MessageContext.Success
            };
        }

        [HttpPost("Complete/{workOrderId:int}")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> CompleteWorkOrder(int workOrderId)
        {
            if (workOrderId <= 0)
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }
            var workOrder = await _client.Queryable<WorkOrderInfo>().InSingleAsync(workOrderId);
            if (workOrder == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                };
            }

            try
            {
                workOrder.Status = WorkOrderStatus.Completed.ToString();

                _client.Ado.BeginTran();
                await _client.Updateable(workOrder).ExecuteCommandAsync();
                _client.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "update work order status error");
                return new ResponseBase<string>()
                {
                    Code = 50000,
                    Message = MessageContext.ServerException
                };
            }

            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = MessageContext.Success
            };
        }

        [HttpGet("export")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_WorkOrder_Export", Description = "Swagger_WorkOrder_Export_Desc")]
        public async Task<IActionResult> ExportWorkOrder([FromQuery] WorkOrderQueryParam queryParam)
        {
            var pageIds = await _client.Queryable<WorkOrderInfo, WorkOrderContent>((w, c) => new JoinQueryInfos(JoinType.Left, w.Id == c.WorkOrderId))
                .WhereIF(!string.IsNullOrWhiteSpace(queryParam.WorkOrderCode), (w, c) => SqlFunc.Contains(w.WorkOrderCode, queryParam.WorkOrderCode))
                .WhereIF(!string.IsNullOrWhiteSpace(queryParam.WorkOrderName), (w, c) => SqlFunc.Contains(w.WorkOrderName, queryParam.WorkOrderName))
                .WhereIF((!string.IsNullOrWhiteSpace(queryParam.WorkOrderType) && queryParam.WorkOrderType.ToUpper() != "ALL"), (w, c) => w.WorkOrderType == queryParam.WorkOrderType)
                .WhereIF((!string.IsNullOrWhiteSpace(queryParam.AssetType) && queryParam.AssetType.ToUpper() != "ALL"), (w, c) => w.AssetType == queryParam.AssetType)
                .WhereIF((!string.IsNullOrWhiteSpace(queryParam.Device) && queryParam.Device.ToUpper() != "ALL"), (w, c) => w.Device == queryParam.Device)
                .WhereIF((!string.IsNullOrWhiteSpace(queryParam.Status) && queryParam.Status.ToUpper() != "ALL"), (w, c) => w.Status == queryParam.Status)
                .WhereIF(!string.IsNullOrWhiteSpace(queryParam.Measure), (w, c) => SqlFunc.Contains(c.Measure, queryParam.Measure))
                .WhereIF(!string.IsNullOrWhiteSpace(queryParam.Content), (w, c) => SqlFunc.Contains(c.Content, queryParam.Content))
                .OrderByDescending((w, c) => w.CreatedTime)
                .GroupBy((w, c) => w.Id)
                .Select((w, c) => w.Id)
                .ToListAsync();

            var pageData = await _client.Queryable<WorkOrderInfo, AssetInfo>((w, a) => new JoinQueryInfos(JoinType.Left, w.Device == a.Id.ToString()))
                .Where((w, a) => SqlFunc.ContainsArray(pageIds, w.Id)).OrderByDescending((w, A) => w.CreatedTime).Select((w, a) => new WorkOrderInfo
                {
                    Id = w.Id,
                    WorkOrderCode = w.WorkOrderCode,
                    WorkOrderName = w.WorkOrderName,
                    WorkOrderType = w.WorkOrderType,
                    ProcessingTime = w.ProcessingTime,
                    ProcessingEndTime = w.ProcessingEndTime,
                    AssetType = w.AssetType,
                    Device = w.Device,
                    DeviceName = a.AssetName,
                    Status = w.Status,
                    CreatedTime = w.CreatedTime,
                    CreatedBy = w.CreatedBy,
                    UpdatedBy = w.UpdatedBy,
                    UpdatedTime = w.UpdatedTime,
                }).ToListAsync();

            var pageDataResult = new List<WorkOrderModel>();
            foreach (var item in pageData)
            {
                pageDataResult.Add(new WorkOrderModel(item, MessageContext));
            }

            var data = new Dictionary<string, object>()
            {
                ["Data"] = pageDataResult
            };

            var ms = new MemoryStream();
            await _excelHelper.SaveExcelAsync(ms, data, UserLanguage, "WorkOrderTemplate");
            ms.Seek(0, SeekOrigin.Begin);
            return File(ms, "application/octet-stream", fileDownloadName: "WorkOrder.xlsx", enableRangeProcessing: true);
        }

        [HttpGet("snapshot/detail")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_WorkOrder_GetMaintenanceSnapshotDetail", Description = "Swagger_WorkOrder_GetMaintenanceSnapshotDetail_Desc")]
        public async Task<ResponseBase<MaintenanceReportDetailResult>> GetMaintenanceSnapshotDetail([FromQuery] string? reportId, int subId = 0)
        {
            MaintenanceReportSnapshot maintenanceReport;
            string subName = "";
            if (subId > 0)
            {
                var subInfo = await _client.Queryable<AssetInfo>().Where(a => a.Id == subId).FirstAsync();
                if (subInfo != null)
                {
                    subName = subInfo.AssetName;
                }
            }

            if (!string.IsNullOrWhiteSpace(reportId))
            {
                int.TryParse(reportId, out var tempReportId);
                if (tempReportId > 0)
                {
                    maintenanceReport = await _client.Queryable<MaintenanceReportSnapshot>().Where(a => a.Id == tempReportId)
                        .WhereIF(!string.IsNullOrEmpty(subName), a => a.SubstationName == subName).FirstAsync();
                }
                else
                {
                    maintenanceReport = await _client.Queryable<MaintenanceReportSnapshot>().WhereIF(!string.IsNullOrEmpty(subName), a => a.SubstationName == subName).OrderByDescending(a => a.Id).FirstAsync();
                }
            }
            else
            {
                maintenanceReport = await _client.Queryable<MaintenanceReportSnapshot>().WhereIF(!string.IsNullOrEmpty(subName), a => a.SubstationName == subName).OrderByDescending(a => a.Id).FirstAsync();
            }

            if (maintenanceReport == null)
            {
                return new ResponseBase<MaintenanceReportDetailResult>()
                {
                    Code = 20000,
                    Data = new MaintenanceReportDetailResult(),
                };
            }

            MaintenanceReportDetailResult detailResult = new MaintenanceReportDetailResult();

            detailResult.Id = maintenanceReport.Id;
            detailResult.CustomerName = maintenanceReport.CustomerName;
            detailResult.SubstationName = maintenanceReport.SubstationName;
            detailResult.ReportName = maintenanceReport.ReportName;
            detailResult.GeneratedTime = maintenanceReport.GeneratedTime.ToString("yyyy-MM-dd HH:mm:ss");

            if (!string.IsNullOrEmpty(maintenanceReport.AlarmOverview))
            {
                try
                {
                    detailResult.AlarmOverview = JsonConvert.DeserializeObject<AlarmCurrentInfoResult>(maintenanceReport.AlarmOverview);
                }
                catch (Exception ex)
                {
                    _log.LogError(ex, "maintenanceReport alarmOverview convert error");
                }
            }

            if (!string.IsNullOrEmpty(maintenanceReport.IndicatorOverview))
            {
                try
                {
                    detailResult.IndicatorOverview = JsonConvert.DeserializeObject<List<IndicatorType>>(maintenanceReport.IndicatorOverview) ?? new List<IndicatorType>();
                }
                catch (Exception ex)
                {
                    _log.LogError(ex, "maintenanceReport IndicatorOverview convert error");
                }
            }

            if (!string.IsNullOrEmpty(maintenanceReport.StationExceptionList))
            {
                try
                {
                    detailResult.StationExceptionList = JsonConvert.DeserializeObject<List<AssetAbnormalMessage>>(maintenanceReport.StationExceptionList) ?? new List<AssetAbnormalMessage>();
                }
                catch (Exception ex)
                {
                    _log.LogError(ex, "maintenanceReport StationExceptionList convert error");
                }
            }

            if (!string.IsNullOrEmpty(maintenanceReport.CabinetExceptionList))
            {
                try
                {
                    detailResult.CabinetExceptionList = JsonConvert.DeserializeObject<List<AssetAbnormal>>(maintenanceReport.CabinetExceptionList) ?? new List<AssetAbnormal>();
                }
                catch (Exception ex)
                {
                    _log.LogError(ex, "maintenanceReport CabinetExceptionList convert error");
                }
            }

            if (!string.IsNullOrEmpty(maintenanceReport.CircuitExceptionList))
            {
                try
                {
                    detailResult.CircuitExceptionList = JsonConvert.DeserializeObject<List<AssetAbnormal>>(maintenanceReport.CircuitExceptionList) ?? new List<AssetAbnormal>();
                }
                catch (Exception ex)
                {
                    _log.LogError(ex, "maintenanceReport CircuitExceptionList convert error");
                }
            }

            if (!string.IsNullOrEmpty(maintenanceReport.DeviceExceptionList))
            {
                try
                {
                    detailResult.DeviceExceptionList = JsonConvert.DeserializeObject<List<AssetAbnormal>>(maintenanceReport.DeviceExceptionList) ?? new List<AssetAbnormal>();
                }
                catch (Exception ex)
                {
                    _log.LogError(ex, "maintenanceReport DeviceExceptionList convert error");
                }
            }

            if (!string.IsNullOrEmpty(maintenanceReport.ReplacementPartChart))
            {
                try
                {
                    var replacementParts = JsonConvert.DeserializeObject<List<PieInfo>>(maintenanceReport.ReplacementPartChart) ?? new List<PieInfo>();
                    foreach (var part in replacementParts)
                    {
                        part.Name = MessageContext.GetString($"ReplacementPart_{part.Code}") ?? part.Name;
                    }
                    detailResult.ReplacementPartChart = replacementParts;
                }
                catch (Exception ex)
                {
                    _log.LogError(ex, "maintenanceReport ReplacementPartChart convert error");
                }
            }

            if (!string.IsNullOrEmpty(maintenanceReport.PanelHealthChart))
            {
                try
                {
                    var panelHealths = JsonConvert.DeserializeObject<List<PieInfo>>(maintenanceReport.PanelHealthChart) ?? new List<PieInfo>();
                    foreach (var health in panelHealths)
                    {
                        health.Name = MessageContext.GetString($"PanelHealth_{health.Code}") ?? health.Name;
                    }
                    detailResult.PanelHealthChart = panelHealths;
                }
                catch (Exception ex)
                {
                    _log.LogError(ex, "maintenanceReport PanelHealthChart convert error");
                }
            }

            if (!string.IsNullOrEmpty(maintenanceReport.BreakerHealthChart))
            {
                try
                {
                    var breakerHealths = JsonConvert.DeserializeObject<List<PieInfo>>(maintenanceReport.BreakerHealthChart) ?? new List<PieInfo>();
                    foreach (var health in breakerHealths)
                    {
                        health.Name = MessageContext.GetString($"BreakerHealth_{health.Code}") ?? health.Name;
                    }
                    detailResult.BreakerHealthChart = breakerHealths;
                }
                catch (Exception ex)
                {
                    _log.LogError(ex, "maintenanceReport BreakerHealthChart convert error");
                }
            }

            return new ResponseBase<MaintenanceReportDetailResult>()
            {
                Code = 20000,
                Data = detailResult
            };
        }

        [HttpGet("snapshot/alarm")]
        [SwaggerOperation(Summary = "Swagger_WorkOrder_GetMaintenanceSnapshotAlarm", Description = "Swagger_WorkOrder_GetMaintenanceSnapshotAlarm_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<SearchBase<AlarmLogListItemResult>> GetMaintenanceSnapshotAlarm([FromQuery] SnapshotAlarmQueryParam alarmQueryParam)
        {
            if (alarmQueryParam.PageSize <= 0 || alarmQueryParam.Page <= 0)
            {
                return new SearchBase<AlarmLogListItemResult>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }
            string subName = "";
            if (alarmQueryParam.subId > 0)
            {
                var subInfo = await _client.Queryable<AssetInfo>().Where(a => a.Id == alarmQueryParam.subId).FirstAsync();
                if (subInfo != null)
                {
                    subName = subInfo.AssetName;
                }
            }

            long lastAlarmId = 0;
            if (!string.IsNullOrWhiteSpace(alarmQueryParam.ReportId))
            {
                int.TryParse(alarmQueryParam.ReportId, out var tempReportId);
                if (tempReportId > 0)
                {
                    var tempReport = await _client.Queryable<MaintenanceReportSnapshot>().Where(a => a.Id == tempReportId).FirstAsync();
                    lastAlarmId = tempReport?.LastAlarmId ?? 0;
                }
                else
                {
                    var tempReport = await _client.Queryable<MaintenanceReportSnapshot>().OrderByDescending(a => a.Id).FirstAsync();
                    lastAlarmId = tempReport?.LastAlarmId ?? 0;
                }
            }
            else
            {
                var tempReport = await _client.Queryable<MaintenanceReportSnapshot>().OrderByDescending(a => a.Id).FirstAsync();
                lastAlarmId = tempReport?.LastAlarmId ?? 0;
            }

            if (lastAlarmId <= 0)
            {
                return new SearchBase<AlarmLogListItemResult>()
                {
                    Page = alarmQueryParam.Page,
                    TotalCount = 0,
                    Code = 20000,
                    Items = new List<AlarmLogListItemResult>()
                };
            }

            var query = _client.Queryable<AlarmLog>().WhereIF(lastAlarmId > 0, a => a.Id <= lastAlarmId);
            var queryStartTime = await _client.Queryable<AlarmQueryConfig>().Where(a => a.QueryType == AlarmQueryType.QueryStartTime).FirstAsync();
            if (queryStartTime != null && !string.IsNullOrEmpty(queryStartTime.QueryValue))
            {
                DateTime startDate;
                var success = DateTime.TryParseExact(queryStartTime.QueryValue, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.RoundtripKind, out startDate);
                if (success)
                {
                    query = query.Where(l => l.CreatedTime >= startDate);
                }

            }
            AlarmEventType alarmEventType = AlarmEventType.All;
            if (!string.IsNullOrEmpty(alarmQueryParam.EventType))
            {
                var eventTypes = alarmQueryParam.EventType.Split(",").Select(e =>
                {
                    if (int.TryParse(e, out var intValue))
                    {
                        return intValue;
                    }
                    return -1;
                }).ToArray();
                query = query.Where(l => eventTypes.Contains((int)l.EventType));
            }

            if (alarmQueryParam.AlarmStatus.HasValue && alarmEventType != AlarmEventType.OperationLog)
            {
                query = query.Where(l => l.Status == (AlarmLogStatus)alarmQueryParam.AlarmStatus.Value);
            }

            RefAsync<int> totalNumber = new RefAsync<int>();
            var dbItems = await query.OrderByDescending(l => l.CreatedTime).ToPageListAsync(alarmQueryParam.Page, alarmQueryParam.PageSize, totalNumber);
            var ruleId = dbItems.Select(l => l.RuleId).ToArray();
            var rules = await _client.Queryable<AlarmRule>().Where(a => ruleId.Contains(a.Id)).ToArrayAsync();
            var result = new List<AlarmLogListItemResult>();
            foreach (var item in dbItems)
            {
                var rule = rules.FirstOrDefault(r => r.Id == item.Id);
                result.Add(new AlarmLogListItemResult(item, rule));
            }

            return new SearchBase<AlarmLogListItemResult>()
            {
                Page = alarmQueryParam.Page,
                TotalCount = totalNumber.Value,
                Code = 20000,
                Items = result
            };
        }

        [HttpGet("snapshot/table")]
        [SwaggerOperation(Summary = "Swagger_WorkOrder_GetMaintenanceSnapshotTableData", Description = "Swagger_WorkOrder_GetMaintenanceSnapshotTableData_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<SearchBase<SnapshotTableResult>> GetMaintenanceSnapshotTableData([FromQuery] SnapshotTableQueryParam snapshotTableQuery)
        {
            if (snapshotTableQuery.PageSize <= 0 || snapshotTableQuery.Page <= 0)
            {
                return new SearchBase<SnapshotTableResult>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            MaintenanceReportSnapshot maintenanceReport;
            if (!string.IsNullOrWhiteSpace(snapshotTableQuery.ReportId))
            {
                int.TryParse(snapshotTableQuery.ReportId, out var tempReportId);
                if (tempReportId > 0)
                {
                    maintenanceReport = await _client.Queryable<MaintenanceReportSnapshot>().Where(a => a.Id == tempReportId).FirstAsync();
                }
                else
                {
                    maintenanceReport = await _client.Queryable<MaintenanceReportSnapshot>().OrderByDescending(a => a.Id).FirstAsync();
                }
            }
            else
            {
                maintenanceReport = await _client.Queryable<MaintenanceReportSnapshot>().OrderByDescending(a => a.Id).FirstAsync();
            }

            if (maintenanceReport == null)
            {
                return new SearchBase<SnapshotTableResult>()
                {
                    Code = 20000,
                    Items = new List<SnapshotTableResult>(),
                    TotalCount = 0,
                    Page = 1
                };
            }


            RefAsync<int> totalNumber = new RefAsync<int>();
            var dbItems = new List<ReportSnapshotDetail>();

            // 断路器不健康状态特殊处理
            if (snapshotTableQuery.ReportType == SnapshotReportType.Breaker)
            {
                // 存在不健康的断路器
                //if (!maintenanceReport.IndicatorOverview.Contains("normal"))
                //{
                dbItems = await _client.Queryable<ReportSnapshotDetail>()
                .Where(a => a.ReportType == SnapshotReportType.Breaker)
                .Where(a => a.ReportId == maintenanceReport.Id)
                .Where(a => !string.IsNullOrEmpty(a.IndicatorInfo) && a.IndicatorInfo != "normal")
                .ToPageListAsync(snapshotTableQuery.Page, snapshotTableQuery.PageSize, totalNumber);
                //}
            }
            else
            {
                dbItems = await _client.Queryable<ReportSnapshotDetail>()
               .Where(a => a.ReportType == snapshotTableQuery.ReportType)
               .Where(a => a.ReportId == maintenanceReport.Id)
               .ToPageListAsync(snapshotTableQuery.Page, snapshotTableQuery.PageSize, totalNumber);
            }

            var tempData = dbItems.Select(a => new SnapshotTableResult
            {
                AssetId = a.AssetId,
                AssetName = a.AssetName,
                AssetNumber = a.AssetNumber,
                BuyNo = a.BuyNo,
                Position = a.Position,
                IndicatorInfo = a.IndicatorInfo
            }).ToList();

            return new SearchBase<SnapshotTableResult>()
            {
                Code = 20000,
                Items = tempData,
                TotalCount = totalNumber.Value,
                Page = snapshotTableQuery.Page
            };
        }

        private string GenerateWorkOrderCode(string preWorkOrderCode)
        {
            string userIdFormatValue = (UserSession?.UserId ?? 0).ToString("X").PadLeft(6, '0');

            string initCode = $"{WORK_ORDER_CODE_BEGIN}-{DateTime.Now.ToString("yyyyMMdd")}-{userIdFormatValue}-0001";
            if (string.IsNullOrEmpty(preWorkOrderCode))
            {
                return initCode;
            }

            var preCodes = preWorkOrderCode.Split('-');
            if (preCodes.Length != 4)
            {
                return initCode;
            }

            if (int.TryParse(preCodes[3], out int codeNumber))
            {
                return $"{WORK_ORDER_CODE_BEGIN}-{DateTime.Now.ToString("yyyyMMdd")}-{userIdFormatValue}-{(codeNumber + 1).ToString().PadLeft(4, '0')}";
            }

            return initCode;
        }
    }
}
