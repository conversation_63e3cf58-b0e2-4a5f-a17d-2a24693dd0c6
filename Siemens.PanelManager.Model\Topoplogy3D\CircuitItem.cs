﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.Model.Topology3D
{
    public class CircuitItem
    {
        [JsonProperty(propertyName: "uuid", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public string Id { get; set; } = Guid.NewGuid().ToString();
        [JsonProperty(propertyName: "assetId")]
        public int? AssetId { get; set; }
        [JsonProperty(propertyName: "assetName")]
        public string AssetName { get; set; } = string.Empty;
        [JsonProperty(propertyName: "circuitName")]
        public string CircuitName { get; set; } = string.Empty;
        [JsonProperty(propertyName: "assetNumber")]
        public string AssetNumber { get; set; } = string.Empty;
        [JsonProperty(propertyName: "assetType")]
        public string AssetType { get; set; } = string.Empty;
        [JsonProperty(propertyName: "deviceType")]
        public string DeviceType { get; set; } = string.Empty;
        [JsonProperty(propertyName: "useScene")]
        public string UseScene { get; set; } = string.Empty;
        [JsonProperty(propertyName: "description")]
        public string Description { get; set; } = string.Empty;
        [JsonProperty(propertyName: "assetStructure", NullValueHandling = NullValueHandling.Ignore)]
        public string? AssetStructure { get; set; }
        [JsonProperty(propertyName: "width")]
        public string Width { get; set; } = "1";
        [JsonProperty(propertyName: "height")]
        public decimal Height { get; set; } = 300m;
    }
}
