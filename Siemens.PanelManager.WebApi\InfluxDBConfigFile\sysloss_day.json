{"name": "sysloss_day", "status": "active", "flux": "import \"date\"\n\noption task = {\n    name: \"sysloss_day\",\n    every: 1d,\n}\n\nfrom(bucket: \"panel\")\n    |> range(start: -1d)\n    |> filter(fn: (r) => r[\"_measurement\"] == \"sysloss_hour\")\n    |> sum()\n    |> map(fn: (r) => ({r with _measurement: \"sysloss_day\", _time: date.truncate(t: now(), unit: 1d)}))\n    |> to(bucket: \"panel\", tagColumns: [\"topology_id\"])", "every": "1d"}