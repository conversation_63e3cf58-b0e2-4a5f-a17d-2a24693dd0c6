﻿using SqlSugar;

namespace Siemens.PanelManager.Model.Database.Alarm
{
    [SugarTable("alarm_log_change_history")]
    public class AlarmLogChangeLog: IPanelDataTable
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsNullable = false, IsIdentity = true)]
        public long Id { get; set; }
        [SugarColumn(ColumnName = "log_id", IsNullable = false)]
        public long LogId { get; set; }
        [SugarColumn(ColumnName = "source", IsNullable = false)]
        public AlarmChangeSource Source { get; set; }
        [SugarColumn(ColumnName = "remark", IsNullable = true, ColumnDataType = "varchar(10240)")]
        public string? Remark { get; set; }
        [SugarColumn(ColumnName = "change_time", IsNullable = false)]
        public DateTime ChangeTime { get; set; }
        [SugarColumn(ColumnName = "change_by", IsNullable = false, Length = 256)]
        public string ChangedBy { get; set; } = string.Empty;

    }

    public enum AlarmChangeSource : int
    {
        Person = 0,
        WorkOrder = 100,
    }
}
