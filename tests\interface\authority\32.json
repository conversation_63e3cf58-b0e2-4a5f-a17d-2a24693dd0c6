{"info": {"_postman_id": "0b8354b1-c1ed-405a-9e77-3633fe06babe", "name": "用户登入成功后进入个人中心界面，编辑个人信息修改新密码为数字+特殊字符", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "24354515"}, "item": [{"name": "用户登录(访客账户)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = pm.response.json().data.token//获取token\r", "pm.collectionVariables.set('token',token)//把token保存到全局变量中\r", "console.log (token)\r", "\r", "pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"test1\",\r\n  \"password\": \"test123@\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/login", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "修改密码(为数字+特殊字符)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"新密码格式错误\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"新密码格式错误\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"newPassword\": \"123123@#￥\",\r\n  \"oldPassword\": \"q123456@\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/My/profile/updatePwd", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "My", "profile", "updatePwd"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}], "variable": [{"key": "usernameindex", "value": 213, "type": "string"}, {"key": "username", "value": "user-213", "type": "string"}, {"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1lIjoidGVzdCIsImh0dHA6Ly9zY2hlbWFzLnhtbHNvYXAub3JnL3dzLzIwMDUvMDUvaWRlbnRpdHkvY2xhaW1zL25hbWVpZGVudGlmaWVyIjoiNjc4NDJhZWUtZWIzMC00ZTE0LWIwZmUtYmMzZGFjYTRjNDRiIiwiU3luY0RldmljZSI6IltdIiwibmJmIjoxNjc3MTQwNzc2LCJleHAiOjE2NzcxNDA3NzcsImlzcyI6IlNpZW1lbnNJc3N1ZXIiLCJhdWQiOiJXZWJBcHBBdWRpZW5jZSJ9.xKg3TlfGzGMcdNtp3V5XUJRcLHcxZZaMFUfHzVAm3-U", "type": "string"}, {"key": "userId", "value": 115, "type": "string"}, {"key": "user2Id", "value": 99, "type": "string"}, {"key": "user3Id", "value": 95, "type": "string"}, {"key": "roleId", "value": 1, "type": "string"}, {"key": "BASE_URL", "value": "http://localhost:5000"}]}