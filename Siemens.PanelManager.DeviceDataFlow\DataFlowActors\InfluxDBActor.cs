﻿using Akka.Actor;
using InfluxDB.Client;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.DeviceDataFlow.StaticData;
using Siemens.PanelManager.Model.Config;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.DeviceDataFlow.DataFlowActors
{
    internal class InfluxDBActor : ReceiveActor
    {
        private readonly ILogger<InfluxDBActor> _logger;
        private readonly IServiceProvider _provider;
        private InfluxDBClient? _client;
        private WriteApiAsync? _writeApi;
        private InfluxDBConfig? _config;
        private InfluxDBQueue _queue;

        public InfluxDBActor(ILogger<InfluxDBActor> logger,
            IServiceProvider provider,
            InfluxDBQueue queue)
        {
            _logger = logger;
            _provider = provider;
            _queue = queue;

            ReceiveAsync<int>(WriteData);
        }

        private async Task WriteData(int arg)
        {
            if (arg == 0)
            {
                await Task.Delay(100);
            }

            arg = 1;
            if (_writeApi == null)
            {
                InitInfluxDb();
            }

            if (_writeApi == null || _config == null)
            {
                _logger.LogDebug("InfluxDBActor WriteData: InfluxDBClient is null.");

                await Task.Delay(1000);
                Self.Tell(arg);
                return;
            }

            try
            {
                var datas = _queue.Read();
                if (datas.Length == 0)
                {
                    _logger.LogDebug("InfluxDBActor WriteData: no data.");
                    await Task.Delay(100);
                }
                else
                {
                    _logger.LogDebug("InfluxDBActor WriteData: begin write.");
                    await _writeApi.WritePointsAsync(datas, _config.Bucket, _config.OrgName);
                    _logger.LogDebug("InfluxDBActor WriteData: finish write.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "InfluxDBActor WriteData error.");
                _client = null;
                _writeApi = null;
                await Task.Delay(500);
            }
            Self.Tell(arg);
        }

        private void InitInfluxDb()
        {
            if (_config == null)
            {
                var config = _provider.GetRequiredService<IConfiguration>();
                _config = config.GetSection(InfluxDBConfig.Influxdb).Get<InfluxDBConfig>();
            }

            if (_config == null) return;
            if (_client == null)
            {
                _client = new InfluxDBClient(_config.Url, _config.UserName, _config.Password);
            }

            if (_client == null) return;
            _writeApi = _client.GetWriteApiAsync();
        }
    }
}
