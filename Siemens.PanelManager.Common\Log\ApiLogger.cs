﻿using Microsoft.Extensions.Logging;

namespace Siemens.PanelManager.Common.Log
{
    public class ApiLogger
    {
        private readonly ILogger _logger;
        public ApiLogger()
        {
            _logger = new SiemensLog("ApiLogger");
        }

        public void LogInfo(string path, string action, string method)
        {
            _logger.LogDebug($"{path} {method} {action}");
        }
    }
}
