﻿using SqlSugar;

namespace Siemens.PanelManager.Model.Database.Asset
{
    [SugarTable("asset_message_config")]
    public class AssetMessagesConfig : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "asset_id", IsNullable = false)]
        public int AssetId { get; set; }
        [SugarColumn(ColumnName = "oid", IsNullable = true, Length = 50)]
        public string? Oid { get; set; }
        [SugarColumn(ColumnName = "trip_oid", IsNullable = true, Length = 50)]
        public string? TripOid { get; set; }
    }
}
