﻿using InfluxDB.Client;
using InfluxDB.Client.Api.Domain;
using Siemens.InfluxDB.Helper.Database;
using Siemens.InfluxDB.Helper.Enum;
using Siemens.InfluxDB.Helper.ExpressionFunction;
using Siemens.InfluxDB.Helper.FluxModel;
using Siemens.InfluxDB.Helper.Interface;
using System.Linq.Expressions;
using System.Text;
using IInfluxDBClient = InfluxDB.Client.IInfluxDBClient;

namespace Siemens.InfluxDB.Helper.Client
{
    public class QueryableClient<T> : Interface.IQueryable<T>
        where T : IInfluxData
    {
        private IQueryApi _queryApi;
        private Bucket _bucket;
        private Organization _organization;
        private DataHelper<T> _dataHelper;

        private Expression<Func<T, bool>>? _currentExpressions;
        private ExpressionForWhere? _expressionForWhere;

        private string _key = Guid.NewGuid().ToString();

        internal QueryableClient(IInfluxDBClient client, Organization organization, Bucket bucket) 
        {
            _queryApi = client.GetQueryApi();
            _bucket = bucket;
            _organization = organization;
            _dataHelper = new DataHelper<T>(_bucket.Name);
            
        }
        public void Dispose()
        {
            
        }

        #region query

        public Interface.IQueryable<T> GroupByTime(uint interval, TimeInterval intervalLevel)
        {
            _dataHelper.AggregateWindow = new AggregateWindow()
            {
                IntervalLevel = intervalLevel,
                IntervalNum = interval,
            };
            return this;
        }

        public Interface.IQueryable<T> Limit(uint size, uint offset)
        {
            _dataHelper.Limit = new Limit()
            {
                Count = size,
                Offset = offset
            };
            _dataHelper.Last = null;
            _dataHelper.First = null;
            return this;
        }

        public Interface.IQueryable<T> WhereAnd(Expression<Func<T, bool>> predicate)
        {
            _key = Guid.NewGuid().ToString();
            if (_currentExpressions == null)
            {
                _currentExpressions = predicate;
            }
            else
            {
                var parameter = System.Linq.Expressions.Expression.Parameter(typeof(T));
                var body = System.Linq.Expressions.Expression.And(_currentExpressions.Body, predicate.Body);
                _currentExpressions = System.Linq.Expressions.Expression.Lambda<Func<T, bool>>(body, parameter);
            }

            return this;
        }

        public Interface.IQueryable<T> WhereOr(Expression<Func<T, bool>> predicate)
        {
            _key = Guid.NewGuid().ToString();
            if (_currentExpressions == null)
            {
                _currentExpressions = predicate;
            }
            else
            {
                var parameter = System.Linq.Expressions.Expression.Parameter(typeof(T));
                var body = System.Linq.Expressions.Expression.Or(_currentExpressions.Body, predicate.Body);
                _currentExpressions = System.Linq.Expressions.Expression.Lambda<Func<T, bool>>(body, parameter);
            }
            return this;
        }

        public string ToFlux()
        {
            UpdateWhereFilter();
            var fluxStr = new StringBuilder(100);
            _dataHelper.From.AppendFlux(fluxStr);
            _dataHelper.Range.AppendFlux(fluxStr);
            _dataHelper.Filter.AppendFlux(fluxStr);
            var function = GroupFunctionEnum.Default;
            if (_dataHelper.AggregateWindow == null)
            {
                _dataHelper.Sort.AppendFlux(fluxStr);
                _dataHelper.Limit?.AppendFlux(fluxStr);
                _dataHelper.First?.AppendFlux(fluxStr);
                _dataHelper.Last?.AppendFlux(fluxStr);
            }
            else
            {
                if (_dataHelper.AggregateWindow != null)
                {
                    function = GroupFunctionEnum.Mean;
                    _dataHelper.AggregateWindow.FunctionName = function.GetGroupFunctionName();
                    _dataHelper.AggregateWindow.AppendFlux(fluxStr, _dataHelper.Limit, _dataHelper.Sort, _dataHelper.First, _dataHelper.Last);
                }
            }

            return fluxStr.ToString();
        }
        #endregion

        public async Task<IList<T>> ToListAsync()
        {
            var function = GroupFunctionEnum.Default;
            if (_dataHelper.AggregateWindow != null)
            {
                function = GroupFunctionEnum.Mean;
            }

            var searchResultHelper = new SearchResultHelper<T>();
            foreach(var field in _dataHelper.Fields) 
            {
                searchResultHelper.SetColumnFunc(field.Key, function, field.Value);
            }
            var data = await _queryApi.QueryAsync(ToFlux(), _organization.Id);
            return searchResultHelper.GetValues(data);
        }

        public Interface.IQueryable<T> First()
        {
            _dataHelper.Limit = null;
            _dataHelper.Last = null;
            _dataHelper.First = new FirstModel();
            return this;
        }

        public Interface.IQueryable<T> Last()
        {
            _dataHelper.Limit = null;
            _dataHelper.Last = new LastModel();
            _dataHelper.First = null;
            return this;
        }

        public ISelectable<R> Select<R>(Expression<Func<T, R>> predicate) where R : IInfluxData
        {
            var searchResultHelper = new SearchResultHelper<R>();
            var analyse = new ExpreesionForSelect();
            var result = analyse.AnalyseExpreesionForSelect(predicate);
            if(result == null 
                || result.ColumnInfos.Count <=0
                ||(result.GroupFunctions .Count > 1 && result.GroupFunctions.Contains(GroupFunctionEnum.Default))) 
            {
                throw new NotSupportedException("Select使用的表达式不支持");
            }

            foreach(var column in result.ColumnInfos) 
            {
                var dbName = _dataHelper.GetColumnName(column.TargetName);
                if (dbName == null) 
                {
                    throw new NotSupportedException("禁止查询非数据库字段");
                }
                searchResultHelper.SetColumnFunc(column.ColumnName, column.Function, dbName);
            }

            return new SelectableClient<R>(this, _dataHelper, searchResultHelper, _queryApi, _organization, _bucket, result.GroupFunctions);
        }

        public string ToFluxNotIncludeGroup()
        {
            UpdateWhereFilter();
            var fluxStr = new StringBuilder(100);
            _dataHelper.From.AppendFlux(fluxStr);
            _dataHelper.Range.AppendFlux(fluxStr);
            
            _dataHelper.Filter.AppendFlux(fluxStr);

            return fluxStr.ToString();
        }

        private void UpdateWhereFilter()
        {
            if (_expressionForWhere == null)
            {
                _expressionForWhere = new ExpressionForWhere(_dataHelper);
            }
            if (_currentExpressions != null && _dataHelper.Filter.NeedRecalculateFilter(_key))
            {
                var flux = _expressionForWhere.WhereExpression(_currentExpressions);
                _dataHelper.Filter.SetFilterFlux(flux, _key);
            }
        }
    }
}

