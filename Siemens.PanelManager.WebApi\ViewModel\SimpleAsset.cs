﻿using Newtonsoft.Json;
using Siemens.PanelManager.Model.Database.Asset;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class SimpleAsset
    {
        public int Id { get; set; }
        public string? AssetName { get; set; }
        [JsonIgnore]
        public AssetLevel? AssetLevel { get; set; }
        public string? Level
        {
            get
            {
                if (AssetLevel == null) return null;
                return AssetLevel.ToString();
            }
        }

        public string? AssetType { get; set; }
        public string? AssetModel { get; set; }
        public List<SimpleAsset>? Children { get; set; }
        public string? HasConnect { get; set; }
        public int Sort
        {
            get 
            {
                switch (AssetType)
                {
                    case "Incoming": return 1;
                    case "BusCoupler":return 2;
                }
                return 99;
            }
        }
    }
}
