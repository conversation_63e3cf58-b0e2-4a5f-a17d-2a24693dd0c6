﻿using SqlSugar;

namespace Siemens.PanelManager.Model.Database.Asset
{
    [SugarTable("asset_third_model")]
    public class ThirdModelConfig : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "asset_type", IsNullable = false, Length = 50)]
        public string AssetType { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "name", IsNullable = false, Length = 50)]
        public string Name { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "name_lower", IsNullable = true, Length = 50)]
        public string NameLower { get; set; } = string.Empty;
        [Uniqueness]
        [SugarColumn(ColumnName = "code", IsNullable = false, Length = 50)]
        public string Code { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "json_data", IsNullable = false, ColumnDataType = "varchar(10485760)")]
        public string? JsonData { get; set; } = string.Empty;

        /// <summary>
        ///  第三方设备名称
        /// </summary>
        [SugarColumn(ColumnDescription = "第三方设备名称", ColumnName = "third_device_name", IsNullable = true, Length = 128)]
        public string? ThirdDeviceName { get; set; }

        [SugarColumn(ColumnDescription = "分批读取点位数量", ColumnName = "read_point_count", IsNullable = true)]
        public int? ReadPointCount { get; set; }

        [SugarColumn(ColumnDescription = "每次点位获取后的延迟等待时间", ColumnName = "delay_time", IsNullable = true)]
        public int? DelayTime { get; set; }
    }
}
