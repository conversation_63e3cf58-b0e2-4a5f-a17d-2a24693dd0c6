﻿using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Topology;
using System.Diagnostics.CodeAnalysis;

namespace Siemens.PanelManager.Job.UploadExcel.Diagram.TemplateModel
{
    /// <summary>
    /// 回路模板基类
    /// </summary>
    internal abstract class CircuitTemplateModelBase
    {
        protected string? BusBarName {  get; private set; }
        public CircuitTemplateModelBase(string? busBarName, CircuitModel model, Dictionary<string, GroupKeyMappingInfo> idTable)
        {
            BusBarName = busBarName;
            Model = model;
            IdTable = idTable;
        }

        #region Virtual
        /// <summary>
        /// 图形方向 
        /// 0 = 横向排布 (默认)
        /// 1 = 纵向排布
        /// </summary>
        protected virtual int Direction { get; set; } = 0;
        protected virtual Dictionary<string, GroupKeyMappingInfo> IdTable { get; private set; }
        protected virtual CircuitModel Model { get;private set; }
        /// <summary>
        /// 需要调整好Key的先后顺序，连线使用的是 1 -> 2 -> 3
        /// 有特殊需求自行overview
        /// </summary>
        /// <param name="nodes">点的集合</param>
        /// <param name="lineDatas">线的队列不能未Null</param>
        protected virtual void LinkLine([DisallowNull] IList<NodeData> nodes, [DisallowNull] IList<LineData> lineDatas)
        {
            var keys = nodes.Where(d => d.Key != null && d.NodeType != NodeType.Label && d.NodeType != NodeType.DataPoint).Select(d => d.Key).OrderBy(d => d).ToArray();
            LineData? lastLine = null;
            foreach (var key in keys)
            {
                if (key == null) continue;
                if (lastLine != null)
                {
                    lastLine.To = key.Value;
                    lineDatas.Add(lastLine);
                }
                lastLine = new LineData()
                {
                    Key = GetNewId(),
                    From = key.Value
                };
            }

            if (lastLine != null)
            {
                lastLine.To = -1;
                lastLine.IsConnector = true;
                lineDatas.Add(lastLine);
            }
        }

        public virtual int X { get; protected set; }
        public virtual int Y { get; protected set; }

        /// <summary>
        /// 修改图形位置
        /// </summary>
        /// <param name="nodes"></param>
        protected virtual void ChangeNodeLocationByDirection(IList<NodeData> nodes)
        {
            if(nodes ==null || nodes.Count ==0) return;
            var half = (int)Math.Floor(nodes.Count / 2m);
            
            if (Direction == 1)
            {
                var orderNodes = nodes.OrderBy(d => d.LocationY).ToArray();
                for (var i = 0; i < half; i++)
                {
                    var tempY = orderNodes[i].LocationY;
                    orderNodes[i].LocationY = orderNodes[orderNodes.Length - 1 - i].LocationY;
                    orderNodes[orderNodes.Length - 1 - i].LocationY = tempY;
                }
            }
        }

        /// <summary>
        /// 回路加载完成，将idTables添加到Group中
        /// </summary>
        public virtual void FinishLoading()
        {
            var id = Model.AssetInfo.Id.ToString();
            var list = new List<int>();
            var mappingInfo = new GroupKeyMappingInfo()
            {
                Level = AssetLevel.Circuit.ToString(),
                List = list
            };
            foreach (var n in NodeDatas)
            {
                if (!n.Key.HasValue) continue;
                list.Add(n.Key.Value);
            }

            if (!IdTable.TryAdd(id, mappingInfo))
            {
                IdTable[id] = mappingInfo;
            }
        }

        /// <summary>
        /// 设置通过初始 Key 更新当前点和线对应的 Key值
        /// </summary>
        /// <param name="beforeKey">初始Key</param>
        /// <returns>最大的Key值</returns>
        public virtual int SetKey(int beforeKey)
        {
            _maxId += beforeKey;
            int maxKey = beforeKey;
            foreach (var node in NodeDatas)
            {
                if (!node.Key.HasValue) continue;
                node.Key += beforeKey;
                if (node.Key > maxKey)
                {
                    maxKey = node.Key.Value;
                }
            }

            foreach (var line in LineDatas)
            {
                if (line.To > 0)
                {
                    line.To += beforeKey;
                }

                if (line.From > 0)
                {
                    line.From += beforeKey;
                }

                if (line.Key.HasValue)
                {
                    line.Key += beforeKey;
                    if (line.Key > maxKey)
                    {
                        maxKey = line.Key.Value;
                    }
                }
            }
            return maxKey;
        }

        private int _maxId = 0;
        /// <summary>
        /// 创建Id, 在当前模板中自增长
        /// </summary>
        /// <returns></returns>
        public virtual int GetNewId()
        {
            return ++_maxId;
        }
        /// <summary>
        /// 获取最大的Id的值
        /// </summary>
        /// <returns></returns>
        public virtual int GetCurrentId()
        {
            return _maxId;
        }

        /// <summary>
        /// 设置base 的x和y，并且更新当前模板中所有node的location
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        public virtual void SetLoction(int x, int y)
        {
            X = x;
            Y = y;
            foreach (var node in NodeDatas)
            {
                SetNodeLocation(node);
            }
        }
        
        /// <summary>
        /// 旋转节点
        /// </summary>
        /// <param name="nodes"></param>
        protected virtual void RotationNodes(IList<NodeData> nodes)
        {
            foreach (var node in nodes)
            {
                if (node.TypeCode == "B") 
                {
                    node.Angle = SwitchAngle;
                }
                else if(node.TypeCode == "L") 
                {
                    node.Angle = OutletAngle;
                }
            }
        }

        /// <summary>
        /// 给node进行location 设置
        /// </summary>
        /// <param name="node"></param>
        protected virtual void SetNodeLocation(NodeData node)
        {
            node.LocationCurrentX = node.LocationX + X;
            node.LocationCurrentY = node.LocationY + Y;
        }

        protected virtual int SwitchAngle
        {
            get
            {
                switch (Direction)
                {
                    case 1: return 90;
                    case 0: return 0;
                    default: return 0;
                }
            }
        }

        protected virtual int OutletAngle
        {
            get
            {
                switch (Direction)
                {
                    case 1: return 90;
                    case 0: return 0;
                    default: return 0;
                }
            }
        }
        #endregion

        #region Abstact
        #region Function
        /// <summary>
        /// 旋转事件
        /// </summary>
        /// <param name="action">-1 90度旋转, 0 强制横排, 1 强制纵排</param>
        public abstract void Rotation(int action = -1);
        #endregion

        #region Property
        /// <summary>
        /// 内部的元素集合
        /// </summary>
        public abstract NodeData[] NodeDatas { get; }
        /// <summary>
        /// 内部的连线集合
        /// </summary>
        public abstract LineData[] LineDatas { get; }
        /// <summary>
        /// 对外连接的点位
        /// </summary>
        public abstract LineData[] Connectors { get; }
        public abstract int Width { get; }
        public abstract int Height { get; }
        #endregion
        #endregion
    }
}
