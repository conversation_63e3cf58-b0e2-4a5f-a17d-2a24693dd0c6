{"info": {"_postman_id": "95654e18-953b-4a88-8b36-e997a3326424", "name": "使用访客账号更新角色名和描述", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "用户登录(访客账户)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = pm.response.json().data.token//获取token\r", "pm.collectionVariables.set('token',token)//把token保存到全局变量中\r", "console.log (token)\r", "\r", "pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"test1\",\r\n  \"password\": \"test123@\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/login", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "更新角色名和描述(没有权限)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"没有操作权限\", function () {\r", "    pm.response.to.have.status(403);\r", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"roleId\": 1,\r\n  \"name\": \"Root\",\r\n  \"desc\": \"\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/Roles", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Roles"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}], "variable": [{"key": "usernameindex", "value": 213, "type": "string"}, {"key": "username", "value": "user-213", "type": "string"}, {"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1lIjoidGVzdCIsImh0dHA6Ly9zY2hlbWFzLnhtbHNvYXAub3JnL3dzLzIwMDUvMDUvaWRlbnRpdHkvY2xhaW1zL25hbWVpZGVudGlmaWVyIjoiY2U0YzEwMDktMTY4Yy00YWExLWJhMzktNGQ2NmM3OGMzMDIyIiwiU3luY0RldmljZSI6IltdIiwibmJmIjoxNjc3MTQxMzA3LCJleHAiOjE2NzcxNDEzMDgsImlzcyI6IlNpZW1lbnNJc3N1ZXIiLCJhdWQiOiJXZWJBcHBBdWRpZW5jZSJ9.mK8aralG5gA6bhUrJUfj5pp6fBuM3ADwG-i4UWri3lE", "type": "string"}, {"key": "userId", "value": 114, "type": "string"}, {"key": "user2Id", "value": 113, "type": "string"}, {"key": "user3Id", "value": 112, "type": "string"}, {"key": "roleId", "value": 1, "type": "string"}, {"key": "user4Id", "value": 111, "type": "string"}, {"key": "BASE_URL", "value": "http://localhost:5000"}]}