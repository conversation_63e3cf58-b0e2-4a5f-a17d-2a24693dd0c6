﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Auth
{
    [SugarTable("auth_user_role")]
    public class UserRoleMapping : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "user_id", IsNullable = false)]
        public int UserId { get; set; }
        [SugarColumn(ColumnName = "role_id", IsNullable = false)]
        public int RoleId { get; set; }
    }
}
