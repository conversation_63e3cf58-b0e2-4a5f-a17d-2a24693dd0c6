﻿using Akka.Actor;
using Siemens.PanelManager.Model.DataFlow;

namespace Siemens.PanelManager.DeviceDataFlow.ActorRefs
{
    internal class AssetStatusSaveRef
    {
        private readonly IActorRef _ref;
        internal AssetStatusSaveRef(IActorRef refObj)
        {
            _ref = refObj;
        }

        public void SaveChangeData(AssetChangeData changeData)
        {
            _ref.Tell(changeData);
        }

        public void Save()
        {
            _ref.Tell("Save");
        }
    }
}
