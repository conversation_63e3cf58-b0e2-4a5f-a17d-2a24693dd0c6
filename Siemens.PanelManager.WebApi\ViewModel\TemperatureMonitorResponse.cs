﻿using SqlSugar;
using System.Security.Principal;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class TemperatureMonitorResponse
    {
        public int Id { get; set; }
        public int AssetId { get; set; }
        public string? DeviceName { get; set; }
        public string Location { get; set; } = string.Empty;
        public string DeviceId { get; set; } = string.Empty;
        public decimal? AlarmTemperature { get; set; }
        public int? ModbusPoint { get; set; }
        public int? BindAssetId { get; set; }
        public string BindAssetName { get; set; } = string.Empty;
        public string DataPoint { get; set; } = string.Empty;
        public string DataPointDesc { get; set; } = string.Empty;
        public string DataPointName { get; set; } = string.Empty;
        public string AlarmSeverity { get; set;} = string.Empty;
        
    }
}
