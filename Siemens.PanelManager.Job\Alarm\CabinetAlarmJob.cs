﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Siemens.PanelManager.Common.Job;
using SqlSugar;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.Topology;
using Siemens.PanelManager.Model.Database.Alarm;
using Newtonsoft.Json;
using Siemens.PanelManager.Model.Topology3D;
using Siemens.PanelManager.Common.Cache;
using TouchSocket.Sockets;
using System.Collections.Generic;
using NPOI.SS.Formula.Functions;
using Newtonsoft.Json.Linq;
using System.Text.Json.Nodes;

namespace Siemens.PanelManager.Job.Alarm
{
    public class CabinetAlarmJob : JobBase
    {
        public override string Name { get; } = "CabinetAlarmJob";
        private readonly IConfiguration _configuration;
        private readonly IServiceProvider _provider;
        private readonly ILogger _logger;

        public CabinetAlarmJob(ILogger<CabinetAlarmJob> logger,
            IConfiguration configuration,
            IServiceProvider provider)
        {
            _logger = logger;
            _configuration = configuration;
            _provider = provider;
        }
        public override async Task Execute()
        {
            string topoplogType = "3D".ToUpper();
            var sqlClient = _provider.GetRequiredService<SqlSugarScope>();
            var assets = await sqlClient.Queryable<AssetInfo>().Where(a=>a.AssetLevel==AssetLevel.Panel|| a.AssetLevel == AssetLevel.Transformer).ToListAsync();
            Dictionary<string, Dictionary<string, string>> valuePairs = new Dictionary<string, Dictionary<string, string>>();
            DateTime startDate= DateTime.Now.AddMonths(-1);
            var queryStartTime = await sqlClient.Queryable<AlarmQueryConfig>().Where(a => a.QueryType == AlarmQueryType.QueryStartTime).FirstAsync();
            if (queryStartTime != null && !string.IsNullOrEmpty(queryStartTime.QueryValue))
            {
                var success = DateTime.TryParseExact(queryStartTime.QueryValue, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.RoundtripKind, out startDate);
            }
            var alarmQueryParam = await sqlClient.Queryable<AlarmQueryConfig>().Where(a => a.QueryType == AlarmQueryType.PanelAlaram && a.QueryValue == "1")
                       .Select(a => a.ParamCode).ToListAsync();
            foreach (var asset in assets) {
                var topoplogyInfo = await sqlClient.Queryable<TopologyInfo, TopologyIdManager>((t, idMapping) => new JoinQueryInfos(JoinType.Left, t.Id == idMapping.Key && idMapping.Type == "3D-Dashboard:Main"))
                      .Where((t, idMapping) => t.Type == topoplogType&&t.Id==asset.Topology3DId)
                      .Select((t, idMapping) => new TopologyInfo()
                      {
                          Id = t.Id,
                          Name = t.Name,
                          Code = t.Code,
                          Data = t.Data,
                      })
                      .OrderByDescending(t => t.Id)
                      .FirstAsync();
                if (topoplogyInfo != null) {
                    var uuid = "";
                    try
                    {
                        var topoInfo = JArray.Parse(topoplogyInfo.Data);
                        //var topoInfo = JsonConvert.DeserializeObject<NodeBase3DGeneric<CabinetUserData>[]>(topoplogyInfo.Data);
                        foreach (JObject item in topoInfo)
                        {
                            if(item["type"]?.ToString() != "ground")
                            {
                                uuid = item["uuid"]?.ToString()??"";
                                List<AlarmSeverity> severitys = new List<AlarmSeverity>();
                                int maxPriority = 0;
                                foreach (var param in alarmQueryParam)
                                {
                                    if (param == "panelAlaramLow")
                                    {
                                        severitys.Add(AlarmSeverity.Low);
                                    }
                                    else if (param == "panelAlaramMid")
                                    {
                                        severitys.Add(AlarmSeverity.Middle);
                                    }
                                    else
                                    {
                                        severitys.Add(AlarmSeverity.High);
                                    }
                                }
                                var alarm = await sqlClient.Queryable<AlarmLog>().Where(a =>
                                                a.CreatedTime > startDate &&
                                                (a.EventType == AlarmEventType.UdcAlarm || a.EventType == AlarmEventType.Alarm) &&
                                                (a.Status == AlarmLogStatus.New || a.Status == AlarmLogStatus.InProcess) &&
                                                a.PanelName == asset.AssetName &&
                                                severitys.Contains(a.Severity))
                                                    .ToListAsync();
                                Dictionary<string, string> dic = new Dictionary<string, string>();

                                if (alarm != null && alarm.Count > 0)
                                {
                                    if (alarm.Where(a => a.Severity == AlarmSeverity.High).Count() > 0)
                                    {
                                        maxPriority = (int)AlarmSeverity.High;
                                    }
                                    else if (alarm.Where(a => a.Severity == AlarmSeverity.Middle).Count() > 0)
                                    {
                                        maxPriority = (int)AlarmSeverity.Middle;
                                    }
                                    dic.Add("isWarning", "1");
                                    dic.Add("assetId", asset.Id.ToString());
                                    dic.Add("warningLevel", maxPriority.ToString());
                                    valuePairs.Add(uuid.ToString(), dic);
                                }
                                else
                                {
                                    dic.Add("isWarning", "0");
                                    dic.Add("assetId", asset.Id.ToString());
                                    dic.Add("warningLevel", maxPriority.ToString());
                                    valuePairs.Add(uuid.ToString(), dic);
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "告警处理问题");
                        continue;
                    }
                }
            }
            var cache = _provider.GetRequiredService<SiemensCache>();
            var topologyCacheKey = "TopologyAlarmCurrently";
            cache.Set(topologyCacheKey, valuePairs);
        }
    }
}


