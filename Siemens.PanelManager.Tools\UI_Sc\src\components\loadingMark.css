.loading-body{
    height: 50px;
    display: grid;
    grid-template-columns: 50px 30px auto;
    grid-template-areas: "logo . message";
  }
  
  .loading-logo {
    grid-area: logo;
    height: 50px;
  }
  
  .loading-message{
    grid-area: message;
    display: table;
  }
  
  .loading-title{
      display: table-cell;
      vertical-align: middle;
  }
  
  @media (prefers-reduced-motion: no-preference) {
      .loading-logo {
        animation: loading-logo-spin infinite 5s linear;
      }
    }
  
    @keyframes loading-logo-spin {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
    