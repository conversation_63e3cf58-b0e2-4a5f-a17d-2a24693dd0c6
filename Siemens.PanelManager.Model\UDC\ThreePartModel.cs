﻿using Newtonsoft.Json;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.Model.UDC
{
    #region 
    public class ThreePartModelTemplate
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Version { get; set; } = string.Empty;
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public ObjectModel? ObjectModel { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public AddressProfile[]? AddressProfile { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public DeviceDefinition? DeviceDefinition { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, DeviceConfiguration[]>? DefaultConfiguration { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public Treeview[]? Treeview { get; set; }

        private static readonly string[] DataPoints = new string[]
        {
            "active_power",
            "active_power_l1",
            "active_power_l2",
            "active_power_l3",
            "collective_reactive_power",
            "reactive_power_l1",
            "reactive_power_l2",
            "reactive_power_l3",
            "apparent_power",
            "apparent_power_l1",
            "apparent_power_l2",
            "apparent_power_l3",
            "active_energy_import",
            "active_energy_export",
            "active_energy_import_l1_tot",
            "active_energy_import_l2_tot",
            "active_energy_import_l3_tot",
            "reactive_energy_import",
            "reactive_energy_export",
            "reactive_energy_export_l1_tot",
            "reactive_energy_export_l2_tot",
            "reactive_energy_export_l3_tot",
            "apparent_energy_import",
            "apparent_energy_export_tariff_1",
            "apparent_energy_l1_tot",
            "apparent_energy_l2_tot",
            "apparent_energy_l3_tot",
        };
        private static readonly Regex _unitRegex = new Regex(@"^(?<multiple>[k|M]{0,1})(?<unit>W|VA|var|Wh|VAh|varh)$", RegexOptions.IgnoreCase);

        private List<PropertyInfo>? _otherPropertyInfoes = null;
        private Treeview? _valueGroup = null;

        private List<PropertyInfo> _subPropertyInfoes = new List<PropertyInfo>();
        private List<PropertyInfo> _all = new List<PropertyInfo>();

        /// <summary>
        /// 将 kW kVA kvar kWh kvarh kVAh 变更为 W/VA/var/Wh/varh/VAh
        /// 当 数字点位 补充父属性
        /// </summary>
        public void ChangeJson()
        {
            if (Treeview != null && Treeview.Length > 0)
            {
                foreach (var treeview in Treeview)
                {
                    if (treeview == null)
                        continue;
                    TraversalTree(treeview);
                }

                if (_subPropertyInfoes.Count > 0)
                {
                    foreach (var subProperty in _subPropertyInfoes)
                    {
                        if (subProperty == null || !int.TryParse(subProperty.Register, out _) || !int.TryParse(subProperty.SubIndex, out _))
                            continue;

                        var parent = _all.FirstOrDefault(p => p.Register == subProperty.Register);

                        if (parent == null)
                        {
                            int i = GetLastIndex();

                            var newProperty = new PropertyInfo()
                            {
                                SelectedDataTypeIndex = 1,
                                SelectedTransformationTypeIndex = 1,
                                SelectedFunctionCodeIndex = subProperty.SelectedFunctionCodeIndex,
                                IsStandardDPE = true,
                                SelectedDataType = subProperty.SelectedDataType,
                                SelectedFunctionCode = subProperty.SelectedFunctionCode,
                                SelectedTransformationType = subProperty.SelectedTransformationType,
                                GroupName = "Others",
                                VoltageHarmonicsPhaseInfo = subProperty.VoltageHarmonicsPhaseInfo,
                                Register = subProperty.Register,
                                SelectedVoltageHarmonicPhase = subProperty.SelectedVoltageHarmonicPhase,
                                IsCurrentHarmonicPHInfoVisible = 2,
                                IsVoltageHarmonicPHInfoVisible = 2,
                                DisplaySelectedItemIndex = 0,
                                ArchiveSelectedItemIndex = 0,
                                ActiveSelectedItemIndex = 0,
                                SelectedCurrentHarmonicPhase = subProperty.SelectedCurrentHarmonicPhase,
                                CurrentHarmonicSelectedItemIndex = 0,
                                IsMaxHarmonicChecked = false,
                                IsHarmonicSectionVisible = 2,
                                NumberOfHarmonics = 0,
                                PropertyName = $"state_binary_inputs_slot_{i}",
                                OriginalPropertyName = $"state_binary_inputs_slot_{i}",
                                DescriptionInEnglish = $"State Binary Inputs Slot {i}",
                                DescriptionInGerman = $"Status Binäreingänge Steckplatz {i}",
                                Unit = "",
                                Display = "OFF",
                                Factor = "1",
                                Archive = "OFF",
                                Active = "TRUE",
                                IsDigitalInputChecked = false,
                                IsDigitalInputCheckBoxVisible = 2,
                                SubIndex = "0",
                                WriteValueText = "0"
                            };

                            _all.Add(newProperty);

                            if (_otherPropertyInfoes != null)
                            {
                                _otherPropertyInfoes.Add(newProperty);
                            }
                            else
                            {
                                if (_valueGroup != null)
                                {
                                    var _otherPropertyInfoes = new List<PropertyInfo>()
                                    {
                                        newProperty
                                    };

                                    _valueGroup.SubGroups!.Add(new Treeview()
                                    {
                                        Name = "Others",
                                        Properties = _otherPropertyInfoes
                                    });
                                }
                            }
                        }
                    }
                }
            }
        }

        private int GetLastIndex()
        {
            var i = 1;
            while (true)
            {
                if (_all.Any(p => $"state_binary_inputs_slot_{i}".Equals(p.PropertyName, StringComparison.OrdinalIgnoreCase)))
                {
                    i++;
                }
                else
                {
                    break;
                }
            }
            return i;
        }

        private void TraversalTree(Treeview treeview)
        {
            if (treeview.Properties != null)
            {
                if ("Others".Equals(treeview.Name))
                {
                    _otherPropertyInfoes = treeview.Properties;
                }

                foreach (var property in treeview.Properties)
                {
                    if (property == null)
                        continue;
                    ChangeUnit(property);

                    if (0 == property.IsDigitalInputCheckBoxVisible
                        && property.IsDigitalInputChecked == true
                        && int.TryParse(property.Register, out _)
                        && int.TryParse(property.SubIndex, out _))
                    {
                        _subPropertyInfoes.Add(property);
                    }
                    else
                    {
                        _all.Add(property);
                    }
                }
            }

            if (treeview.SubGroups != null)
            {
                if ("Value".Equals(treeview.Name))
                {
                    _valueGroup = treeview;
                }

                foreach (var subGroup in treeview.SubGroups)
                {
                    if (subGroup == null)
                        continue;
                    TraversalTree(subGroup);
                }
            }
        }

        private void ChangeUnit(PropertyInfo property)
        {
            if (property == null || string.IsNullOrEmpty(property.Unit) || !DataPoints.Any(d => d.Equals(property.PropertyName, StringComparison.OrdinalIgnoreCase)))
                return;

            if (!string.IsNullOrEmpty(property.Unit))
            {
                var match = _unitRegex.Match(property.Unit);
                if (match.Success)
                {
                    decimal multiple = 1M;
                    switch (match.Groups["multiple"].Value)
                    {
                        case "k":
                        case "K":
                            multiple = 1000M;
                            break;
                        case "m":
                        case "M":
                            multiple = 1000000M;
                            break;
                        default: break;
                    }

                    if (string.IsNullOrEmpty(property.Factor) || !decimal.TryParse(property.Factor, out var factor))
                    {
                        property.Factor = multiple.ToString();
                    }
                    else
                    {
                        factor *= multiple;
                        property.Factor = factor.ToString();
                    }

                    property.Unit = match.Groups["unit"].Value;
                }
            }

        }
    }

    public class Treeview
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public List<Treeview>? SubGroups { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Name { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public List<PropertyInfo>? Properties { get; set; }
    }

    public class PropertyInfo
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? SelectedDataTypeIndex { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? SelectedTransformationTypeIndex { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? SelectedFunctionCodeIndex { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? IsStandardDPE { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public AttributeModel? SelectedDataType { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public AttributeModel? SelectedFunctionCode { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public AttributeModel? SelectedTransformationType { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? GroupName { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? SubGroupName { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string[]? VoltageHarmonicsPhaseInfo { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? SelectedVoltageHarmonicPhase { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IsCurrentHarmonicPHInfoVisible { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IsVoltageHarmonicPHInfoVisible { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? DisplaySelectedItemIndex { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? ArchiveSelectedItemIndex { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? ActiveSelectedItemIndex { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? SelectedCurrentHarmonicPhase { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? CurrentHarmonicSelectedItemIndex { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? IsMaxHarmonicChecked { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IsHarmonicSectionVisible { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? NumberOfHarmonics { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? PropertyName { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? OriginalPropertyName { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? DescriptionInEnglish { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? DescriptionInGerman { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? Unit { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Display { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Factor { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Archive { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Active { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Register { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? IsDigitalInputChecked { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? IsDigitalInputCheckBoxVisible { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? SubIndex { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? WriteValueText { get; set; }
    }

    public class AttributeModel
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? DataType { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Functioncode { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? TransformationDataType { get; set; }
    }

    public class DefaultValue
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Name { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Default { get; set; }
    }

    public class DescriptionInfo
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Culture { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Text { get; set; }
    }

    public class IconInfo
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Library { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Name { get; set; }
    }

    #region DeviceDefinition
    public class DeviceDefinition
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public FeaturesModel? Features { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string[]? DigitalInputs { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string[]? DigitalOutputs { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string[]? Commands { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string[]? AlarmConfigurationNonEditable { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string[]? ArchiveConfigurationNonEditable { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string[]? DisplayConfigurationNonEditable { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string[]? AddressConfigurationNonEditable { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string[]? powerDemandElements { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string[]? powerPeriodPostfixes { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public TagetInfo[]? Counters { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string[]? DefaultMeasurementPoints { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string[]? DefaultTrendGroupProperties { get; set; }
    }
    #region Features
    public class FeaturesModel
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? TariffSynchronization { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? RTUCommunicationOnly { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? DigitalInputs { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? MeasuringPeriodSynchronization { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? PowerPeriod { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? Offset { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? Webpage { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? LittleEndianRegister { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? Harmonics { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? DigitalOutputs { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? Commands { get; set; }

    }
    #endregion
    #region Taget Info
    public class TagetInfo
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Target { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Source { get; set; }
    }
    #endregion
    #endregion

    #region AddressProfile
    public class AddressProfile
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public DefaultValue? Lib { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public DefaultValue? OM { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public DefaultValue? AddrProf { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public AddressProperty[]? Properties { get; set; }
    }

    #region AddressProperty
    public class AddressProperty
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Name { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Type { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Dir { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Offset { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? FuncCode { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? TransType { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? SubIndex { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? AbsOffset { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Visible { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? LowLevelComp { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? PollGr { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public MsgConv? MsgConv { get; set; }
    }
    #endregion

    #region MsgConv
    public class MsgConv
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? MinRaw { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? MaxRaw { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? MinEngg { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? MaxEngg { get; set; }
    }
    #endregion
    #endregion

    #region Object Model
    public class ObjectModel
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public DPTData? DPTData { get; set; }
    }
    #region DPTData
    public class DPTData
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public Dpts[]? DPTS { get; set; }
    }
    #endregion
    #region DPTS
    public class Dpts
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Name { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? ManagedType { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? DefaultProp { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? Validation { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? GenericCreate { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? GenericDelete { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string[]? ParentTypes { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public Classification? Classification { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public DPES[]? DPES { get; set; }

    }
    #endregion
    #region Classification
    public class Classification
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? Valid { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? Disc { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? SubDisc { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? Type { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? SubType { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public PvssTypeModel[]? DPES { get; set; }
    }
    #endregion
    #region Dpes
    public class DPES
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Name { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public PvssTypeModel? PvssType { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? VL { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? AL { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? DL0 { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? DL1 { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? DL2 { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? DL3 { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? Persist { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? GroupId { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public DescriptionInfo[]? Description { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public PvssDisplay? Display { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public GmsTypeModel? GmsType { get; set; }
    }
    #endregion
    #region PvssType
    public class PvssTypeModel
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? PvssType { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public DPES[]? DPES { get; set; }
    }
    #endregion
    #region PvssDisplay
    public class PvssDisplay
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? Valid { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public IconInfo? Icon { set; get; }
    }
    #endregion

    #region GmsType
    public class GmsTypeModel
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? GmsType { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public GmsAttributeModel? Attributes { set; get; }
    }

    #region Attributes
    public class GmsAttributeModel
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool? Valid { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? TextGroup { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public UnitText? UnitText { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public GmsProperty? Properties { set; get; }
    }

    public class UnitText
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? Unit { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? TextGroup { get; set; }
    }

    public class GmsProperty
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public double? Min { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public double? Max { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? Res { set; get; }
    }
    #endregion
    #endregion
    #endregion

    #region DefaultConfiguration
    public class DeviceConfiguration
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, DashboardConfigItem[]>? DashboardConfiguration { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public TrendConfiguration? TrendConfiguration { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public object? EnergyConfiguration { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public GeneralConfigItem? GeneralConfiguration { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? DeviceType { get; set; }
    }

    public class GeneralConfigItem
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public MeasurementPointItem[]? MeasurementPoints { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? UnitsAndFactors { get; set; }
    }

    public class DashboardConfigItem
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public GaugeConfig? GaugeConfig { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public BarchartConfig? BarchartConfig { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? ControlType { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? MeasurementPointName { set; get; }
    }

    public class GaugeConfig
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Color1 { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Color2 { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Color3 { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? MinimumLimit { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Limit1 { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Limit2 { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? MaximumLimit { set; get; }
    }

    public class BarchartConfig
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Duration { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? Interval { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? IsComparable { set; get; }
    }


    public class TrendConfiguration
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public MeasurementPointItem[]? TrendElements { set; get; }
    }

    public class MeasurementPointItem
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? MeasurementPoint { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? MeasurementPointName { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? MeasurementPointValue { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public AlarmConfig? AlarmConfiguration { set; get; }
    }

    public class AlarmConfig
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? AlarmKind { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public DiscreteConfigItem[]? DiscreteConfigs { get; set; }
    }

    public class DiscreteConfigItem
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? AlarmClassSelectedItem { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? ValueRangeSelectedItem { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int? DataPointElementType { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? LowerValueRangeText { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? HigherValueRangeText { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? EventText { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? NormalText { set; get; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string? BoolType { set; get; }
    }
    #endregion
    #endregion
}
