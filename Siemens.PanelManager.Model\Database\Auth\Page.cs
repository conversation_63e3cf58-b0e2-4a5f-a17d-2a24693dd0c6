﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Auth
{
    [SugarTable("auth_page")]
    public class Page : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "page_name", IsNullable = false, Length = 256)]
        public string PageName { get; set; } = string.Empty;
        [UniquenessAttribute]
        [SugarColumn(ColumnName = "page_code", IsNullable = false, Length = 256)]
        public string PageCode { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "page_description", IsNullable = true, Length = 256)]
        public string? PageDescription { get; set; }
        [SugarColumn(ColumnName = "page_level", IsNullable = false)]
        public PageLevel PageLevel { get; set; }
        [SugarColumn(ColumnName = "is_system_config", IsNullable = false)]
        public bool IsSystemConfig { get; set; } = false;
        [SugarColumn(ColumnName = "order_no", IsNullable = true)]
        public int OrderNo { get; set; }


        #region 便捷区分层级关系特殊逻辑
        private int[]? _levelIds = null;
        [SugarColumn(IsIgnore = true)]
        public int[] LevelId
        {
            get
            {
                if (_levelIds == null)
                {
                    _levelIds = new int[3];
                    _levelIds[0] = Id / 100000;
                    _levelIds[1] = (Id % 100000) / 100;
                    _levelIds[2] = Id % 100;
                }
                return _levelIds;
            }
        }

        #endregion

    }

    public enum PageLevel : int
    {
        EnumKey = 10,
        PageKey = 20,
        ButtonKey = 50,
    }
}
