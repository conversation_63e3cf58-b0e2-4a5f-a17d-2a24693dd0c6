﻿using Akka.Actor;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Siemens.PanelManager.Model.UDC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.DeviceDataFlow.UDC
{
    internal class UDCApiActor : ReceiveActor
    {
        private HttpClient? _client;
        private HttpClient? _clientForUpload;
        private ILogger _logger;
        private IServiceProvider _provider;
        private string _culture;
        public UDCApiActor(IServiceProvider provider, IConfiguration configuration, ILogger<UDCApiActor> logger) 
        {
            _culture = "zh-Hans";
            var language = configuration.GetValue<string>("DefaultLanguage");
            switch (language)
            {
                case "en": _culture = "en-US"; break;
                default: break;
            }
            _provider = provider;
            _logger = logger;
            var baseUrl = configuration.GetValue<string>("UdcApiPath");
            if(!string.IsNullOrEmpty(baseUrl))
            {
                _client = InitHttpClient(baseUrl, TimeSpan.FromSeconds(10));
            }

            ReceiveFunc();
        }

        private HttpClient InitHttpClient(string baseUrl, TimeSpan timeSpan)
        {
            return new HttpClient()
            {
                BaseAddress = new Uri(baseUrl),
                Timeout = timeSpan
            };
        }

        private void ReceiveFunc()
        {
            ReceiveAsync<GetDataPointsParam>(GetDataPoints);
            ReceiveAsync<GetItemsParam>(GetItems);
            ReceiveAsync<ImportProjectParam>(ImportProject);
            ReceiveAsync<GetMessageParams>(GetMessage);
            ReceiveAsync<GetBreakerTripsParam>(GetBreakerTrips);
            ReceiveAsync<GetDeviceMessageParams>(GetDeviceMessages);
            ReceiveAsync<GetUdcLicensesParams>(GetUdcLicenses);
        }

        private async Task GetBreakerTrips(GetBreakerTripsParam param)
        {
            try
            {
                if (param == null || string.IsNullOrEmpty(param.ObjectId) || param.PageCount <= 0)
                    throw new AggregateException("GetDataPoints参数错误");
                InitHttpClient();

                var urlPath = $"/api/v1/items/{param.ObjectId}/trips?count={param.PageCount}&page=1&culture={_culture}";
                if (!string.IsNullOrEmpty(param.Oid))
                {
                    urlPath += $"&end_oid={param.Oid}";
                }
                //_logger.LogInformation(urlPath);
                var response = await _client!.GetStringAsync(urlPath);
                _logger.LogDebug(response);
                var data = JsonConvert.DeserializeObject<UdcMessageModelListResult>(response);

                Sender.Tell(data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetBreakerTrips 失败");
                Sender.Tell(ex);
            }
        }

        private async Task GetDataPoints(GetDataPointsParam param)
        {
            try 
            {
                if (param == null || string.IsNullOrEmpty(param.ObjectId) || param.InternalNames.Length == 0)
                    throw new AggregateException("GetDataPoints参数错误");
                InitHttpClient();

                var names = string.Join('&', param.InternalNames.Select(x => $"internal_name={x}").ToArray());
                var urlPath = $"/api/v1/items/{param.ObjectId}/datapoints?{names}";
                // _logger.LogInformation(urlPath);
                var response = await _client!.GetStringAsync(urlPath);

                _logger.LogDebug(response);

                var data = JsonConvert.DeserializeObject<DeviceDataPointsResult>(response);

                Sender.Tell(data);
            }
            catch(Exception ex) 
            {
                _logger.LogError(ex, "GetDataPoints 失败");
                Sender.Tell(ex);
            }
        }

        private async Task GetItems(GetItemsParam param)
        {
            try
            {
                InitHttpClient();
                var urlPath = $"/api/v1/items";
                var response = await _client!.GetStringAsync(urlPath);
                var data = JsonConvert.DeserializeObject<DeviceItemsResult>(response);
                Sender.Tell(data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetItems 失败");
                Sender.Tell(ex);
            }
        }

        private async Task ImportProject(ImportProjectParam param)
        {
            try
            {
                if (param == null || param.Stream == null) 
                {
                    throw new AggregateException("ImportProject参数错误");
                }
                InitHttpClient2();

                using var content = new StreamContent(param.Stream);
                var urlPath = "/api/v1/items/1/commands/_?internal_name=ImportProject&password=undefined&culture=en-us";
                _logger.LogDebug(urlPath);
   
                var response = await _clientForUpload!.PostAsync(urlPath, content);
                response.EnsureSuccessStatusCode();
                Sender.Tell(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ImportProject 失败");
                Sender.Tell(ex);
            }
        }

        private async Task GetMessage(GetMessageParams param)
        {
            try
            {
                if (param == null || string.IsNullOrEmpty(param.ObjectId) || param.Oid <= 0)
                {
                    throw new AggregateException("GetMessage参数错误");
                }
                InitHttpClient();

                var urlPath = $"/api/v1/items/{param.ObjectId}/messages/{param.Oid}?culture={_culture}";
                // _logger.LogInformation(urlPath);
                var response = await _client!.GetStringAsync(urlPath);
                _logger.LogDebug(response);
                var result = JsonConvert.DeserializeObject<UdcMessageModel>(response);
                Sender.Tell(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetMessage 失败");
                Sender.Tell(ex);
            }
        }

        private async Task GetDeviceMessages(GetDeviceMessageParams param)
        {
            try
            {
                if (param == null || string.IsNullOrEmpty(param.ObjectId) || param.Count <= 0)
                {
                    throw new AggregateException("GetDeviceMessages参数错误");
                }
                InitHttpClient();

                var urlPath = $"/api/v1/items/{param.ObjectId}/messages?count={param.Count}&culture={_culture}";
                if (!string.IsNullOrEmpty(param.Oid))
                {
                    urlPath += $"&end_oid={param.Oid}";
                }
                //_logger.LogInformation(urlPath);
                var response = await _client!.GetStringAsync(urlPath);
                _logger.LogDebug(response);
                var result = JsonConvert.DeserializeObject<UdcMessageModelListResult>(response);
                Sender.Tell(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"GetDeviceMessages 失败。参数 {param.ObjectId},{param.Count},{_culture}");
                Sender.Tell(ex);
            }
        }

        private void InitHttpClient()
        {
            if (_client == null)
            {
                var config = _provider.GetRequiredService<IConfiguration>();
                var baseUrl = config.GetValue<string>("UdcApiPath");
                if (!string.IsNullOrEmpty(baseUrl))
                {
                    _client = InitHttpClient(baseUrl, TimeSpan.FromSeconds(10));
                }
                else
                {
                    throw new AggregateException("UDC Api Url没有配置");
                }
            }
        }

        private void InitHttpClient2()
        {
            if (_clientForUpload == null)
            {
                var config = _provider.GetRequiredService<IConfiguration>();
                var baseUrl = config.GetValue<string>("UdcApiPath");
                if (!string.IsNullOrEmpty(baseUrl))
                {
                    _clientForUpload = InitHttpClient(baseUrl, TimeSpan.FromMinutes(5));
                }
                else
                {
                    throw new AggregateException("UDC Api Url没有配置");
                }
            }
        }

        private async Task GetUdcLicenses(GetUdcLicensesParams param)
        {
            try
            {
                InitHttpClient();
                var urlPath = "/api/v1/licenses";
                var response = await _client!.GetStringAsync(urlPath);
                var result = JsonConvert.DeserializeObject<UdcLicenses>(response);
                Sender.Tell(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetUdcLicenses 失败");
                Sender.Tell(ex);
            }
        }
    }
}
