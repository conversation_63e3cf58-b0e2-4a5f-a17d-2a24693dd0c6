﻿using Siemens.PanelManager.Model.Database;
using Siemens.PanelManager.Model.Database.Asset;
using SqlSugar;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;

namespace Siemens.PanelManager.Common.Model
{
    internal class DatabaseModelInfo
    {
        public DatabaseModelInfo(Type type, string tableName)
        {
            DataType = type;
             TableName = tableName;
            var properties = type.GetProperties();
            var uniquenessColumns = new List<string>();
            var upperColumns = new List<string>();
            foreach (var property in properties)
            {
                 var attr = property.GetCustomAttribute<UniquenessAttribute>();
                if (attr != null)
                {
                    uniquenessColumns.Add(property.Name);
                }
            }
            UniquenessColumns = uniquenessColumns.ToArray();
            UpperColumns = upperColumns.ToArray();
        }

        public System.Type DataType { get; private set; }
        public string[] UniquenessColumns { get; private set; }
        public string[] UpperColumns { get; private set; }
        public string? TableName { get; private set; }

        public Dictionary<string, object> GetUniquenessValue([DisallowNull] object data)
        {
            var result = new Dictionary<string, object>();
            if(data.GetType() == DataType) 
            {
                foreach (var uniquenessColumn in UniquenessColumns)
                {
                    var p = DataType.GetProperty(uniquenessColumn);
                    if (p == null) continue;
                    var name = uniquenessColumn;
                    var attr = p.GetCustomAttribute<SugarColumn>();
                    if (attr != null) 
                    {
                        name = attr.ColumnName;
                    }

                    var value = p.GetValue(data, null);

                    if (value != null)
                    {
                        var upperAttr = p.GetCustomAttribute<UpperAttibute>();
                        if (upperAttr != null && value is string strValue)
                        {
                            result.Add(name, strValue.ToUpper());
                        }
                        else if (value is AssetLevel level)
                        {
                            result.Add(name, (int)level);
                        }
                        else
                        {
                            result.Add(name, value);
                        }
                    }
                }
            }
            return result;
        }

        public async Task<object?> CheckIsExists([DisallowNull] object data, [DisallowNull] ISqlSugarClient client)
        {
            if (data.GetType() == DataType)
            {
                if (!string.IsNullOrEmpty(TableName)) 
                {
                    var uniquessValues = GetUniquenessValue(data);
                    if (uniquessValues.Count != 0)
                    {
                        var item = await client.Queryable(TableName, string.Empty).WhereColumns(new List<Dictionary<string, object>>() { uniquessValues }).FirstAsync();
                        return item;
                    }
                }
            }
            return null;
        }

        public async Task InsertValues([DisallowNull] object value, [DisallowNull] ISqlSugarClient client)
        {
            if (value.GetType() == DataType)
            {
                var datas = new Dictionary<string, object?>();
                var properties = DataType.GetProperties();
                foreach (var property in properties)
                {
                    var attr = property.GetCustomAttribute<SugarColumn>();
                    if (attr == null) continue;
                    if (attr.IsIgnore) continue;
                    if (attr.IsIdentity) continue;
                    var name = property.Name;
                    if (!string.IsNullOrEmpty(attr.ColumnName))
                    {
                        name = attr.ColumnName;
                    }
                    var pValue = property.GetValue(value);
                    var upperAttr = property.GetCustomAttribute<UpperAttibute>();
                    if (upperAttr != null && pValue is string strValue)
                    {
                        pValue = strValue.ToUpper();
                    }

                    if (name == "created_by" || name == "updated_by")
                    {
                        pValue = Statup.UpdatedBy;
                    }

                    if (name == "created_time" || name == "updated_time")
                    {
                        pValue = DateTime.Now;
                    }
                    datas.TryAdd(name, pValue);
                }
                await client.Insertable(datas).AsType(DataType).ExecuteCommandAsync();
            }
        }

        public async Task UpdateValue([DisallowNull] object newValue, [DisallowNull] dynamic oldValue, [DisallowNull] ISqlSugarClient client)
        {
            if (newValue.GetType() != DataType) return;
            var datas = new Dictionary<string, object?>();
            var properties = DataType.GetProperties();
            var oldValueDic = (IDictionary<string, object?>)oldValue;
            var pks = new List<string>();
            foreach (var property in properties)
            {
                var attr = property.GetCustomAttribute<SugarColumn>();
                if (attr == null) continue;
                if (attr.IsIgnore) continue;

                var name = property.Name;
                if (!string.IsNullOrEmpty(attr.ColumnName))
                {
                    name = attr.ColumnName;
                }

                object? pValue;
                if (attr.IsPrimaryKey)
                {
                    pValue = oldValueDic[name];
                    pks.Add(name);
                }
                else
                {
                    pValue = property.GetValue(newValue);
                }

                var upperAttr = property.GetCustomAttribute<UpperAttibute>();
                if (upperAttr != null && pValue is string strValue)
                {
                    pValue = strValue.ToUpper();
                }

                if (name == "created_by" || name == "created_time")
                {
                    continue;
                }

                if (name == "updated_by")
                {
                    pValue = Statup.UpdatedBy;
                }

                if (name == "updated_time")
                {
                    pValue = DateTime.Now;
                }
                datas.TryAdd(name, pValue);
            }
            await client.Updateable(datas).AsType(DataType).WhereColumns(pks.ToArray()).ExecuteCommandAsync();
        }
    }
}
