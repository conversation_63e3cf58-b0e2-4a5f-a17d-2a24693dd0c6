[{"QueryType": 0, "ParamCode": "highReminder", "QueryValue": "1"}, {"QueryType": 0, "ParamCode": "midReminder", "QueryValue": "0"}, {"QueryType": 0, "ParamCode": "lowReminder", "QueryValue": "0"}, {"QueryType": 1, "ParamCode": "panelAlaramHigh", "QueryValue": "1"}, {"QueryType": 1, "ParamCode": "panelAlaramMid", "QueryValue": "0"}, {"QueryType": 1, "ParamCode": "panelAlaramLow", "QueryValue": "0"}, {"QueryType": 2, "ParamCode": "substationName", "QueryValue": ""}, {"QueryType": 2, "ParamCode": "panelName", "QueryValue": ""}, {"QueryType": 2, "ParamCode": "circuitName", "QueryValue": ""}, {"QueryType": 2, "ParamCode": "deviceName", "QueryValue": ""}, {"QueryType": 2, "ParamCode": "eventType", "QueryValue": "[0,10]"}, {"QueryType": 2, "ParamCode": "alarmLevel", "QueryValue": "[20]"}, {"QueryType": 2, "ParamCode": "alarmStatus", "QueryValue": "[0]"}, {"QueryType": 2, "ParamCode": "alarmRuleId", "QueryValue": "[]"}, {"QueryType": 2, "ParamCode": "user", "QueryValue": ""}, {"QueryType": 3, "ParamCode": "alarmQueryStartTime", "QueryValue": ""}]