﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using NPOI.POIFS.Properties;
using NPOI.SS.Formula.Functions;
using Siemens.PanelManager.Model.Database.Asset;
using SqlSugar;
using System.Collections.Concurrent;
using System.Text.Json;
using TouchSocket.Core;

namespace Siemens.PanelManager.Job.ModbusDeviceWorker
{
    public class PanelModbusWorker : BackgroundService
    {
        private readonly ILogger<PanelModbusWorker> _logger;
        private readonly IServiceProvider _serviceProvider;
        private ConcurrentDictionary<string, List<PanelModbusFunction>> _modbusFunctionConfigs = new();
        private List<PanelModbusClient> _panelModbusClients = new();
        private CancellationTokenSource? _source;

        private Dictionary<string, ushort> _registerCountConfig = new Dictionary<string, ushort>()
        {
            {"string",4},
            {"boolean",1 },
            {"float",2 },
            {"int16",1 },
            {"int32",2 },
            {"int64",4 },
            {"uint16",1 },
            {"uint32",2 },
            {"uint64",4 }
        };

        public PanelModbusWorker(ILogger<PanelModbusWorker> logger, IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
        }

        public override Task StartAsync(CancellationToken cancellationToken)
        {
            if (_source == null || _source.IsCancellationRequested)
            {
                _source = new CancellationTokenSource();
                _logger.LogInformation($"{DateTime.Now},PanelModbusWorker starting.");
                return base.StartAsync(_source.Token);
            }
            return Task.CompletedTask;
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            if (_source != null)
            {
                _logger.LogInformation($"{DateTime.Now},PanelModbusWorker stopping.");

                _source.Cancel();
                await base.StopAsync(_source.Token);
            }
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {

            await InitDataAndModbusClient();

            while (true)
            {
                if (stoppingToken.IsCancellationRequested)
                {
                    try
                    {
                        if (_panelModbusClients.Any())
                        {
                            foreach (var modbusClient in _panelModbusClients)
                            {
                                modbusClient.StopMonitor();
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"PanelModbusWorker ExecuteAsync stop error.");
                    }
                    return;
                }

                await Task.Delay(2000);
            }
        }

        private async Task InitDataAndModbusClient()
        {
            _modbusFunctionConfigs.Clear();
            _panelModbusClients.Clear();
            PanelModbusClientStatus.GetInstance().ClearAllStatus();
            PanelModbusClientStatus.GetInstance().ClearDataPointCustomFactor();

            var sqlClient = _serviceProvider.GetRequiredService<SqlSugarScope>();
            var thirdPartCodes = await sqlClient
                .Queryable<AssetInfo>()
                .Where(a => !SqlFunc.IsNullOrEmpty(a.ThirdPartCode))
                .Select(a => a.ThirdPartCode)
                .Distinct()
                .ToListAsync();

            var thirdPartModels = await sqlClient.Queryable<ThirdModelConfig>()
                .Where(a => SqlFunc.ContainsArray(thirdPartCodes, a.Code))
                .ToListAsync();

            var universalDeviceConfigs = await sqlClient
               .Queryable<UniversalDeviceConfig>()
               .Where(a => !SqlFunc.IsNullOrEmpty(a.Coefficient))
               .ToListAsync();

            if (universalDeviceConfigs != null && universalDeviceConfigs.Any())
            {
                foreach (var itemConfig in universalDeviceConfigs)
                {
                    if (decimal.TryParse(itemConfig.Coefficient, out var tempCoefficient) && tempCoefficient != 1m)
                    {
                        PanelModbusClientStatus.GetInstance().SetDataPointCustomFactor(itemConfig.AssetId, itemConfig.PropertyEnName, tempCoefficient);
                    }
                }
            }

            if (thirdPartModels != null && thirdPartModels.Any())
            {
                foreach (var thirdModel in thirdPartModels)
                {
                    if (!_modbusFunctionConfigs.ContainsKey(thirdModel.Code))
                    {
                        try
                        {
                            InitDataPoints(thirdModel.Code, thirdModel.JsonData);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"InitDataPoints error,ThirdModelCode[{thirdModel.Code}]");
                        }
                    }
                }
            }

            var modbusDevices = await sqlClient.Queryable<AssetInfo>()
                 .Where(a => (a.AssetModel == "Other") || (a.AssetType == "GeneralDevice") || ("Modbus".Equals(a.AssetModel) && "Gateway".Equals(a.AssetType)))
                 .Where(a => !SqlFunc.IsNullOrEmpty(a.IPAddress) && !SqlFunc.IsNullOrEmpty(a.ObjectId))
                 .ToListAsync();

            if (modbusDevices.Any())
            {
                #region Old
                //{
                //    var modbusOrGateways = modbusDevices.Where(a => !string.IsNullOrEmpty(a.IPAddress) && !a.IPAddress.Contains("/"));
                //    PanelModbusClientConfig modbusConfig;
                //    foreach (var modbusOrGateway in modbusOrGateways)
                //    {
                //        if (!string.IsNullOrEmpty(modbusOrGateway.IPAddress))
                //        {
                //            var ipAddressInfoes = modbusOrGateway.IPAddress.Split("/");

                //            int port = string.IsNullOrEmpty(modbusOrGateway.Port) ? 502 : Convert.ToInt32(modbusOrGateway.Port);
                //            modbusConfig = new PanelModbusClientConfig()
                //            {
                //                IPAddress = ipAddressInfoes[0],
                //                Port = port,
                //                SlaveId = 1,
                //                ObjectId = modbusOrGateway.ObjectId,
                //                AssetId = modbusOrGateway.Id,
                //                AssetName = modbusOrGateway.AssetName,
                //                ThirdPartCode = modbusOrGateway.ThirdPartCode,
                //                PanelModbusSlaves = new List<PanelModbusSlave>()
                //            };

                //            var currentChilds = modbusDevices
                //                .Where(a => !string.IsNullOrEmpty(a.IPAddress) && a.IPAddress.Contains(ipAddress) && a.IPAddress.Contains("/") && a.Port == modbusOrGateway.Port);

                //            if (currentChilds.Any())
                //            {
                //                foreach (var child in currentChilds)
                //                {
                //                    var ipInfos = child.IPAddress.Split("/");
                //                    if (ipInfos != null && ipInfos.Length == 2)
                //                    {
                //                        modbusConfig.PanelModbusSlaves.Add(new PanelModbusSlave
                //                        {
                //                            SlaveId = int.Parse(ipInfos[1]),
                //                            AssetId = child.Id,
                //                            AssetName = child.AssetName,
                //                            ThirdPartCode = child.ThirdPartCode,
                //                            ObjectId = child.ObjectId
                //                        });
                //                    }
                //                }
                //            }

                //            _panelModbusClients.Add(new PanelModbusClient(modbusConfig, _modbusFunctionConfigs, _serviceProvider));
                //        }
                //    }
                //}
                #endregion

                #region New
                {
                    var ipAddresses = modbusDevices.Select(m => m.IpAddressStr).Distinct().ToList();

                    foreach (var ipAddress in ipAddresses)
                    {
                        if (string.IsNullOrEmpty(ipAddress))
                        {
                            continue;
                        }

                        var currentModbusDevices = modbusDevices.Where(a => a.IpAddressStr == ipAddress).ToList();
                        if (!currentModbusDevices.Any())
                        {
                            continue;
                        }

                        var ports = currentModbusDevices.Select(m => m.Port).Distinct().ToList();
                        foreach (var p in ports)
                        {
                            if (int.TryParse(p, out int port) && port > 0)
                            {
                                var gateway = currentModbusDevices.FirstOrDefault(a => a.Port == p && a.AssetType == "Gateway");

                                var modbusConfig = new PanelModbusClientConfig
                                {
                                    IPAddress = ipAddress,
                                    Port = port,
                                    PanelModbusSlaves = new List<PanelModbusSlave>()
                                };

                                if (gateway == null)
                                {
                                    modbusConfig.SlaveId = -1;
                                    modbusConfig.AssetId = -1;
                                    modbusConfig.AssetName = string.Empty;
                                    modbusConfig.ObjectId = string.Empty;
                                }
                                else
                                {
                                    modbusConfig.SlaveId = gateway.SlaveId ?? -1;
                                    modbusConfig.AssetId = gateway.Id;
                                    modbusConfig.AssetName = gateway.AssetName;
                                    modbusConfig.ObjectId = gateway.ObjectId ?? string.Empty;
                                }

                                var devices = currentModbusDevices.Where(a => a.Port == p && a.AssetType != "Gateway").ToList();
                                foreach (var d in devices)
                                {
                                    modbusConfig.PanelModbusSlaves.Add(new PanelModbusSlave
                                    {
                                        AssetId = d.Id,
                                        AssetName = d.AssetName,
                                        ObjectId = d.ObjectId ?? string.Empty,
                                        SlaveId = d.SlaveId ?? -1,
                                        ThirdPartCode = d.ThirdPartCode
                                    });
                                }

                                _panelModbusClients.Add(new PanelModbusClient(modbusConfig, _modbusFunctionConfigs, _serviceProvider));
                            }
                        }
                    }
                }
                #endregion

                if (_panelModbusClients.Any())
                {
                    foreach (var modbusClient in _panelModbusClients)
                    {
                        modbusClient.StartMonitor();
                    }
                }
            }
        }

        private void InitDataPoints(string? code, string? jsonContent)
        {
            if (string.IsNullOrEmpty(code) || string.IsNullOrEmpty(jsonContent))
            {
                return;
            }

            try
            {
                List<PanelModbusFunction> panelModbusFunctions = new();
                var panelModbusDataPoints = new List<PanelModbusDataPoint>();
                var document = JsonDocument.Parse(jsonContent);
                JsonElement root = document.RootElement;

                var isBigEndian = true;

                // DeviceDefinition/Features/LittleEndianRegister
                if (root.TryGetProperty("DeviceDefinition", out var deviceDefinition))
                {
                    if (deviceDefinition.TryGetProperty("Features", out var features))
                    {
                        if (features.TryGetProperty("LittleEndianRegister", out var littleEndian))
                        {
                            isBigEndian = !littleEndian.GetBoolean();
                        }
                    }
                }

                var treeViews = root.GetProperty("Treeview").EnumerateArray();

                foreach (var treeView in treeViews)
                {
                    if (treeView.TryGetProperty("SubGroups", out var sGroup))
                    {
                        if (sGroup.ValueKind == JsonValueKind.Array)
                        {
                            foreach (var sub in sGroup.EnumerateArray())
                            {
                                if (sub.TryGetProperty("Properties", out var properties))
                                {
                                    AddModbusDataPoint(panelModbusDataPoints, properties);
                                }
                            }
                        }
                        else
                        {
                            if (treeView.TryGetProperty("Properties", out var properties))
                            {
                                AddModbusDataPoint(panelModbusDataPoints, properties);
                            }
                        }
                    }
                }

                if (panelModbusDataPoints.Any())
                {
                    for (int f = 1; f <= 4; f++)
                    {
                        var tempPoints = panelModbusDataPoints.Where(a => a.FunctionCode == $"FC{f}").OrderBy(a => a.Register).ToList();
                        var tempReadSpans = new List<PanelModbusReadSpan>();

                        if (tempPoints.Any())
                        {
                            var tempCount = tempPoints.Count;
                            var tempStartIndex = 0;

                            for (int i = 0; i < tempCount; i++)
                            {
                                var currentObj = tempPoints[tempStartIndex];

                                var endIndex = i + 1 >= tempCount ? tempCount - 1 : i + 1;
                                var nextObj = tempPoints[endIndex];

                                // 一次读10个寄存器
                                if ((nextObj.Register + nextObj.RegisterCount - currentObj.Register) > 11)
                                {
                                    tempReadSpans.Add(new PanelModbusReadSpan
                                    {
                                        PanelModbusDataPoints = tempPoints.Skip(tempStartIndex).Take(i - tempStartIndex + 1).ToList(),
                                        ReadRegisterCount = tempPoints[i].Register + tempPoints[i].RegisterCount - currentObj.Register
                                    });

                                    tempStartIndex = i + 1;
                                }
                                else
                                {
                                    if (tempStartIndex == endIndex || i == tempCount - 1)
                                    {
                                        tempReadSpans.Add(new PanelModbusReadSpan
                                        {
                                            PanelModbusDataPoints = tempPoints.Skip(tempStartIndex).Take(i - tempStartIndex + 1).ToList(),
                                            ReadRegisterCount = tempPoints[i].Register + tempPoints[i].RegisterCount - currentObj.Register
                                        });
                                    }
                                }
                            }
                        }

                        panelModbusFunctions.Add(new PanelModbusFunction
                        {
                            FunctionCode = $"FC{f}",
                            PanelModbusReadSpans = tempReadSpans,
                            IsBigEndian = isBigEndian
                        });
                    }
                }

                if (panelModbusFunctions.Any())
                {
                    _modbusFunctionConfigs.TryAdd(code, panelModbusFunctions);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "PanelModbusWorker IninDataPoints Error.");
            }
        }

        private void AddModbusDataPoint(List<PanelModbusDataPoint> panelModbusDataPoints, JsonElement properties)
        {
            PanelModbusDataPoint pMPoint;
            if (properties.ValueKind == JsonValueKind.Array && properties.GetArrayLength() > 0)
            {
                foreach (var property in properties.EnumerateArray())
                {
                    try
                    {
                        if (property.GetProperty("IsDigitalInputCheckBoxVisible").ValueKind != JsonValueKind.Null
                            && property.GetProperty("IsDigitalInputChecked").ValueKind != JsonValueKind.Null)
                        {
                            if (0 == property.GetProperty("IsDigitalInputCheckBoxVisible").GetInt32()
                                && property.GetProperty("IsDigitalInputChecked").GetBoolean())
                            {
                                continue;
                            }
                        }

                        pMPoint = new PanelModbusDataPoint();

                        pMPoint.TransformationType = property.GetProperty("SelectedTransformationType").GetProperty("TransformationDataType").GetString();
                        pMPoint.FunctionCode = property.GetProperty("SelectedFunctionCode").GetProperty("Functioncode").GetString()?.Substring(0, 3);
                        pMPoint.PropertyName = property.GetProperty("PropertyName").GetString();
                        //pMPoint.OriginalPropertyName = property.GetProperty("OriginalPropertyName").GetString();
                        pMPoint.DescriptionInEnglish = property.GetProperty("DescriptionInEnglish").GetString();
                        pMPoint.DescriptionInGerman = property.GetProperty("DescriptionInGerman").GetString();
                        pMPoint.Unit = property.GetProperty("Unit").GetString();

                        if (!string.IsNullOrWhiteSpace(property.GetProperty("Factor").GetString()))
                        {
                            pMPoint.Factor = Convert.ToSingle(property.GetProperty("Factor").GetString());
                        }

                        if (!string.IsNullOrWhiteSpace(property.GetProperty("Register").GetString()))
                        {
                            var tempData = property.GetProperty("Register").GetString().Split("@@@");
                            pMPoint.Register = Convert.ToUInt16(tempData[0]);

                            if (tempData.Length == 2)
                            {
                                if (float.TryParse(tempData[1], out var tempIntercept))
                                {
                                    pMPoint.Intercept = tempIntercept;
                                }
                                else
                                {
                                    pMPoint.ParseMode = tempData[1];
                                }
                            }

                            if (tempData.Length == 3)
                            {
                                pMPoint.ParseMode = tempData[1];

                                if (float.TryParse(tempData[2], out var tempIntercept))
                                {
                                    pMPoint.Intercept = tempIntercept;
                                }
                            }
                        }

                        pMPoint.RegisterCount = _registerCountConfig.GetValueOrDefault(pMPoint.TransformationType);

                        panelModbusDataPoints.Add(pMPoint);

                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "PanelModbusWorker AddModbusDataPoint Error.");
                    }
                }
            }
        }
    }
}
