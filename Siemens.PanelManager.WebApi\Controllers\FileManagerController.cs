﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.WebApi.Models;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class FileManagerController : SiemensApiControllerBase
    {
        private ILogger<AssetController> _log;
        private ISqlSugarClient _client;
        private SiemensCache _cache;
        private IServiceProvider _provider;
        private AlarmExtendServer _alarmExtendServer => _provider.GetRequiredService<AlarmExtendServer>();
        public FileManagerController(SqlSugarScope client,
            SiemensCache cache,
            ILogger<AssetController> log,
            IServiceProvider provider)
            : base(provider, cache)
        {
            _client = client;
            _log = log;
            _cache = cache;
            _provider = provider;
        }


        [HttpPost("uploadFile")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_FileManager_UploadFile", Description = "Swagger_FileManager_UploadFile_Desc")]
        public async Task<ResponseBase<FileInfoModel>> UploadFile([FromForm] IFormCollection form)
        {
            var files = form.Files;
            var f = files.FirstOrDefault();
            if (f == null)
            {
                return new ResponseBase<FileInfoModel>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var m = Regex.IsMatch(f.FileName, "^(.*)\\.([\\w]*)$");
            if (m)
            {
                FileManager? fileManager = await FileManagerFunc.CreateFileManager(f, UserName, _log);
                if (fileManager != null && !string.IsNullOrEmpty(fileManager.Url))
                {
                    var fileId = await _client.Insertable(fileManager).ExecuteReturnIdentityAsync();
                    await _alarmExtendServer.InsertOperationLog(UserName, "UploadFile", Model.Database.Alarm.AlarmSeverity.Low, _client, f.FileName);
                    fileManager.Id = fileId;
                    return new ResponseBase<FileInfoModel>()
                    {
                        Code = 20000,
                        Data = new FileInfoModel(fileManager)
                    };
                }

                return new ResponseBase<FileInfoModel>()
                {
                    Code = 50000,
                    Message = MessageContext.ServerException
                };
            }

            return new ResponseBase<FileInfoModel>()
            {
                Code = 40300,
                Message = MessageContext.ErrorParam
            };
        }

        [HttpGet("{fileName}")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_FileManager_GetFile", Description = "Swagger_FileManager_GetFile_Desc")]
        public async Task<ResponseBase<FileInfoModel[]>> GetFileByName(string fileName, int isSystem = 1)
        {
            if (string.IsNullOrEmpty(fileName))
            {
                return new ResponseBase<FileInfoModel[]>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var code = fileName.ToUpper();
            var fileManagers = await _client.Queryable<FileManager>()
                .Where(f => f.Code == code
                && f.IsSystemFile == (isSystem != 0) 
                && (f.Language == null || f.Language == UserLanguage))
                .ToArrayAsync();

            var fileInfoModels = fileManagers.Select(f => new FileInfoModel(f)).ToArray();

            return new ResponseBase<FileInfoModel[]>()
            {
                Code = 20000,
                Data = fileInfoModels
            };
        }


        [HttpGet()]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_FileManager_GetAllFiles", Description = "Swagger_FileManager_GetAllFiles_Desc")]
        public async Task<ResponseBase<FileInfoModel[]>> GetFiles()
        {
            int isSystem = 1;
            var fileManagers = await _client.Queryable<FileManager>()
                .Where(f => f.IsSystemFile == (isSystem != 0))
                .ToArrayAsync();

            var fileInfoModels = fileManagers.Select(f => new FileInfoModel(f)).ToArray();

            return new ResponseBase<FileInfoModel[]>()
            {
                Code = 20000,
                Data = fileInfoModels
            };
        }

        [HttpGet("temporary/{tempId}")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_FileManager_GetTemporaryFile", Description = "Swagger_FileManager_GetTemporaryFile_Desc")]
        public IActionResult GetTemporaryFile(string tempId)
        {
            if (string.IsNullOrEmpty(tempId))
            {
                return NotFound();
            }
            var model = _cache.Get<TemporaryFileModel>($"TemporaryFile:{tempId}");
            if (model == null || string.IsNullOrEmpty(model.FilePath) || string.IsNullOrEmpty(model.FileName))
            {
                return NotFound();
            }
            var fs = new FileStream(model.FilePath,FileMode.Open, FileAccess.Read);
            return File(fs, "application/octet-stream", model.FileName);
        }
    }
}
