{"info": {"_postman_id": "f97ab494-f9ad-4182-b72f-62c215b969cc", "name": "29使用管理员账号进入panel manager告警管理中的告警配置菜单，点击操作字段下的更新日志按钮查看更新日志", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 14", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "获取全部告警规则 Copy 3", "event": [{"listen": "test", "script": {"exec": ["let ID1 = pm.response.json().items[0].id\r", "pm.environment.set(\"ID1\", ID1);\r", "console.log(ID1)\r", "pm.test(\"包含规则ID，告警名称，告警规则，告警级别，是否启用\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"id\");\r", "    pm.expect(pm.response.text()).to.include(\"name\");\r", "    pm.expect(pm.response.text()).to.include(\"rule\");\r", "    pm.expect(pm.response.text()).to.include(\"level\");\r", "    pm.expect(pm.response.text()).to.include(\"isEnable\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/AlarmRule?page=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["api", "v1", "AlarmRule"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}]}}, "response": []}, {"name": "查看告警规则日志Copy Copy 4", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"包含告警名称、告警等级、操作类型、操作时间、操作人\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"alarmName\");\r", "    pm.expect(pm.response.text()).to.include(\"alarmLevel\");\r", "    pm.expect(pm.response.text()).to.include(\"optType\");\r", "    pm.expect(pm.response.text()).to.include(\"time\");\r", "    pm.expect(pm.response.text()).to.include(\"updatedBy\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/AlarmRule/history/{{ID1}}?page=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["api", "v1", "AlarmRule", "history", "{{ID1}}"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 1000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", "pm.test(\"code码为20000\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData[\"code\"]).to.eql(20000);", "});", ""]}}]}