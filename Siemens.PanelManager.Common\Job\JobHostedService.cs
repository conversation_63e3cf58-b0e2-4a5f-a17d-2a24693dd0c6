﻿using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Quartz;

namespace Siemens.PanelManager.Common.Job
{
    public class JobHostedService : IHostedService
    {
        private ILogger<JobHostedService> _logger;
        private ISchedulerFactory _schedulerFactory;
        private IServiceProvider _provider;
        public JobHostedService(ILogger<JobHostedService> logger, ISchedulerFactory schedulerFactory, IServiceProvider provider)
        {
            _logger = logger;
            _schedulerFactory = schedulerFactory;
            _provider = provider;
        }

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Job server is starting");
            var scheduler = await _schedulerFactory.GetScheduler();
            await JobStaticManager.StartJobServer(scheduler, _provider, _logger);
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Job server is stoping");
            return Task.CompletedTask;
        }
    }
}
