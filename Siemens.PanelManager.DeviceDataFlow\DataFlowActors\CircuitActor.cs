﻿using Akka.Actor;
using Akka.Util.Internal;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Quartz.Util;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.DeviceDataFlow.Model;
using Siemens.PanelManager.DeviceDataFlow.StaticData;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.Alarm;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.DataFlow;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.Server.DataPoint;
using SqlSugar;

namespace Siemens.PanelManager.DeviceDataFlow.DataFlowActors
{
    internal class CircuitActor : AssetActorBase
    {
        private readonly DataPointServer _service;
        public CircuitActor(ILogger<CircuitActor> logger, IServiceProvider provider, AssetSimpleInfo simpleInfo, SiemensCache cache, DataPointServer server) 
            : base(simpleInfo, provider, logger, cache)
        {
            _service = server;
            LoggerName = "CircuitActor";
            NeedLog = true;
        }
       
        private Dictionary<string, string> mapping = new Dictionary<string, string>()
        {
            {"F", "frequency"},
            {"Ia", "current_l1"},
            {"Ib", "current_l2"},
            {"Ic", "current_l3"},
            {"Ua", "voltage_ph_n_l1"},
            {"Ub", "voltage_ph_n_l2"},
            {"Uc", "voltage_ph_n_l3"},
            {"P", "active_power"},
            {"Q", "collective_reactive_power"},
            {"Pa", "active_power_l1"},
            {"Pb", "active_power_l2"},
            {"Pc", "active_power_l3"},
            {"Qa", "reactive_power_l1"},
            {"Qb", "reactive_power_l2"},
            {"Qc", "reactive_power_l3"},
            {"Uab", "voltage_ph_ph_l1_l2"},
            {"Ubc", "voltage_ph_ph_l2_l3"},
            {"Uca", "voltage_ph_ph_l3_l1"},
            {"THD_Ia", "thd_current_l1"},
            {"THD_Ib", "thd_current_l2"},
            {"THD_Ic", "thd_current_l3"},
            {"THD_Ua", "thd_voltage_l1"},
            {"THD_Ub", "thd_voltage_l2"},
            {"THD_Uc", "thd_voltage_l3"},
            {"PowFactor","power_factor" },
            {"S","apparent_power" }
        };
        private Dictionary<string, string> mapping2 = new Dictionary<string, string>()
        {
            {"frequency","F"},
            {"current_l1","Ia"},
            {"current_l2","Ib"},
            {"current_l3","Ic"},
            {"voltage_ph_n_l1","Ua" },
            {"voltage_ph_n_l2","Ub" },
            {"voltage_ph_n_l3","Uc" },
            {"active_power","P"},
            {"collective_reactive_power","Q"},
            {"active_power_l1","Pa"},
            {"active_power_l2","Pb"},
            {"active_power_l3","Pc"},
            {"reactive_power_l1","Qa"},
            {"reactive_power_l2","Qb"},
            {"reactive_power_l3","Qc"},
            {"voltage_ph_ph_l1_l2","Uab"},
            {"voltage_ph_ph_l2_l3","Ubc"},
            {"voltage_ph_ph_l3_l1","Uca"},
            {"thd_current_l1","THD_Ia"},
            {"thd_current_l2","THD_Ib"},
            {"thd_current_l3","THD_Ic"},
            {"thd_voltage_l1","THD_Ua"},
            {"thd_voltage_l2","THD_Ub"},
            {"thd_voltage_l3","THD_Uc"},
            {"power_factor" ,"PowFactor"},
            {"apparent_power","S" }
        };

        protected override async Task InputDataFunc(AssetInputData inputData)
        {
            var simpleInfo = AssetSimpleInfo;
            if (simpleInfo == null) return;
            if (!(inputData.AssetId == simpleInfo.AssetId
                || (inputData.ParentId == simpleInfo.AssetId
                && inputData.AssetId == simpleInfo.SourceAssetId)))
            {
                if (inputData.Datas.TryGetValue("HaveAlarm", out _))
                {
                    UpdateAlarmStatus(inputData.InputTime);
                }

                return;
            }

            if (inputData.Datas.TryGetValue("HaveAlarm", out _))
            {
                UpdateAlarmStatus(inputData.InputTime);
                inputData.Datas.Remove("HaveAlarm");
            }

            if (inputData.Datas.Count == 0) return;

            await SaveDataFunc(inputData.Datas, simpleInfo, inputData.InputTime, false);
        }

        private async Task SaveDataFunc(Dictionary<string, string> originalDatas, AssetSimpleInfo simpleInfo, DateTime time, bool isChangeData)
        {
            var cacheKey = string.Format(Constant.AssetCurrentDataCacheKey, simpleInfo.AssetId);
            var hasChanged = false;
            var changeData = new AssetChangeData()
            {
                AssetId = AssetSimpleInfo.AssetId,
                AssetLevel = AssetSimpleInfo.AssetLevel,
                AssetName = AssetSimpleInfo.AssetName,
                AssetModel = AssetSimpleInfo.AssetModel,
                AssetType = AssetSimpleInfo.AssetType,
                ChangeTime = time,
            };
            var needSaveDatas = new Dictionary<string, Dictionary<string, string>>();
            var datas = await ClearDataPoint(originalDatas, needSaveDatas);

            var currentStatus = Cache.GetHashData(cacheKey, datas.Keys.ToArray());
            foreach (var data in datas)
            {
                if (currentStatus.ContainsKey(data.Key))
                {
                    if (currentStatus[data.Key] != data.Value)
                    {
                        hasChanged = true;
                        changeData.ChangeDatas.Add(data.Key, data.Value);
                    }
                }
                else
                {
                    hasChanged = true;
                    changeData.ChangeDatas.Add(data.Key, data.Value);
                }
            }

            var alarmServer = Provider.GetService<AlarmLogServer>();

            //回路告警信息
            //if (alarmServer != null)
            //{
            //    var alarmInfo = alarmServer!.GetAlarmInfo(new AlarmInfoParam() { CircuitName = changeData.AssetName, AssetId = changeData.AssetId });

            //    if (alarmInfo != null)
            //    {
            //        changeData.ChangeDatas.Add("Alarm_Severity", alarmInfo.AlarmSeverity.ToString());
            //        changeData.ChangeDatas.Add("Alarm_Status", alarmInfo.AlarmStatus.ToString());
            //        changeData.ChangeDatas.Add("Alarm_Path", alarmInfo.AlarmPath ?? "");
            //        changeData.ChangeDatas.Add("Alarm_Info", alarmInfo.AlarmInfo ?? "");
            //        changeData.ChangeDatas.Add("Alarm_Time", alarmInfo.AlarmTime ?? "");
            //    }
            //}
            Cache.SetHashData(cacheKey, new Dictionary<string, string>(changeData.ChangeDatas), 120);
            if (hasChanged)
            {
                var actorManager = ActorManager.GetActorManagerNoException();

                if (actorManager != null)
                {
                    actorManager.DataPointRef.Tell(changeData);
                }

                if (simpleInfo.PanelSimpleInfo != null)
                {
                    var proxyRef = Provider.GetRequiredService<IAssetDataProxyRef>();
                    var panelInputData = new AssetInputData()
                    {
                        ParentId = simpleInfo.PanelSimpleInfo.AssetId,
                        AssetId = simpleInfo.AssetId,
                        Datas = changeData.ChangeDatas,
                        InputTime = changeData.ChangeTime
                    };
                    proxyRef.InputData(panelInputData);
                }

                if (needSaveDatas.Count >= 0)
                {
                    foreach (var kv in needSaveDatas)
                    {
                        ActorManager.GetActorManager().AssetStatusSaveRef.Tell(new AssetSaveParam
                        {
                            AssetId = AssetSimpleInfo.AssetId,
                            ObjectId = AssetSimpleInfo.ObjectId ?? string.Empty,
                            Time = changeData.ChangeTime,
                            Datas = kv.Value,
                            TableName = kv.Key
                        });
                    }
                }
            }
        }
        protected override async Task ChangeDataFunc(AssetChangeData changeData)
        {
            var simpleInfo = AssetSimpleInfo;
            if (simpleInfo == null) return;
            if (changeData.AssetId != simpleInfo.AssetId) return;

            if (changeData.ChangeDatas.TryGetValue("HaveAlarm", out _))
            {
                UpdateAlarmStatus(changeData.ChangeTime);
                changeData.ChangeDatas.Remove("HaveAlarm");
            }

            if (changeData.ChangeDatas.Count == 0) return;

            await SaveDataFunc(changeData.ChangeDatas, simpleInfo, changeData.ChangeTime, true);
        }

        private async Task<Dictionary<string, string>> ClearDataPoint(Dictionary<string,string> datas,
            Dictionary<string, Dictionary<string, string>> needSaveDatas)
        {
            var dataPoints = await _service.GetDataPointInfos(AssetSimpleInfo.AssetLevel, AssetSimpleInfo.AssetType, AssetSimpleInfo.AssetModel);

            var result = new Dictionary<string, string>();
            foreach (var kv in datas)
            {
                mapping2.TryGetValue(kv.Key.ToLower(), out var map);

                var dataPoint = dataPoints.FirstOrDefault(d => d.Code == kv.Key || d.Code == map);
                if (dataPoint != null)
                {
                    var key = kv.Key;
                    if (dataPoint.Code == map)
                    {
                        key = map;
                    }
                    if (!string.IsNullOrEmpty(dataPoint.SaveToTable))
                    {
                        if (!needSaveDatas.TryGetValue(dataPoint.SaveToTable, out var needSaves))
                        {
                            needSaves = new Dictionary<string, string>();
                            needSaveDatas.TryAdd(dataPoint.SaveToTable, needSaves);
                        }
                        needSaves.AddOrSet(key, kv.Value);
                    }
                    result.AddOrSet(key, kv.Value);
                }
            }
            return result;
        }

        private void UpdateAlarmStatus(DateTime time)
        {
            var alarmLogService = Provider.GetRequiredService<AlarmLogServer>();

            var countModel = alarmLogService.GetAlarmCount(PanelManager.Model.Database.Asset.AssetLevel.Circuit, AssetSimpleInfo.AssetName);
            if (countModel != null)
            {
                var cacheKey = string.Format(Constant.AssetCurrentDataCacheKey, AssetSimpleInfo.AssetId);
                var haveAlarm = (countModel.HighCount + countModel.MiddleCount + countModel.LowCount) > 0 ? "1" : "0";
                Cache.SetHashData(cacheKey, new Dictionary<string, string>
                {
                    ["HaveAlarm"] = haveAlarm,
                    ["HighCount"] = countModel.HighCount.ToString(),
                    ["MiddleCount"] = countModel.MiddleCount.ToString(),
                    ["LowCount"] = countModel.LowCount.ToString(),
                });

                if (AssetSimpleInfo.PanelSimpleInfo != null)
                {
                    var proxyRef = Provider.GetRequiredService<IAssetDataProxyRef>();
                    var panelInputData = new AssetInputData()
                    {
                        ParentId = AssetSimpleInfo.PanelSimpleInfo.AssetId,
                        AssetId = AssetSimpleInfo.AssetId,
                        Datas = new Dictionary<string, string> 
                        {
                            ["HaveAlarm"] = haveAlarm
                        },
                        InputTime = time
                    };
                    proxyRef.InputData(panelInputData);
                }
            }
        }
    }
}
