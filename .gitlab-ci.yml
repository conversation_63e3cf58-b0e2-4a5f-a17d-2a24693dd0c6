variables:
  NO_PROXY: code.siemens.com,cr.siemens.com,*.siemens.*
  no_proxy: code.siemens.com,cr.siemens.com,*.siemens.*
  IMAGE_NAME: cr.siemens.com/si-lp-cea-rd-plm-project/edge/panel-manager/panel-manager-standard-product/server/panel-server
  RELEASE_IMAGE_NAME: panel-api

stages:
  - build
  - deploy
  # - test
  # - undeploy
  # - push

generate_server:
  stage: build
  tags:
    - pms-api
  only:
    - dev
  script:
    - docker build -f ./Siemens.PanelManager.WebApi/Dockerfile -t $IMAGE_NAME:dev .

generate_main_server:
  stage: build
  tags:
    - pms-api
  only:
    - main
  script:
    - docker build -f ./Siemens.PanelManager.WebApi/Dockerfile_Main -t $RELEASE_IMAGE_NAME:latest .

generate_test_server:
  stage: build
  tags:
    - pms-api
  only:
    - dev
  script:
    - docker build -f ./Siemens.PanelManager.WebApi/Dockerfile_Test -t $IMAGE_NAME:test .

generate_other_server:
  stage: build
  tags:
    - pms-api
  rules:
    - if: $CI_COMMIT_BRANCH != "master" && $CI_COMMIT_BRANCH != "main" && $CI_COMMIT_BRANCH != "dev" && $CI_COMMIT_BRANCH != "test"
  script:
    - docker build -f ./Siemens.PanelManager.WebApi/Dockerfile -t $IMAGE_NAME:temp .

deploy_server:
  stage: deploy
  tags:
    - pms-api
  only:
    - dev
  script:
    - docker compose -f /app/custom_config/docker-compose_dev.yml down
    - docker compose -f /app/custom_config/docker-compose_dev.yml up -d

deploy_main_server:
  stage: deploy
  tags:
    - pms-api
  only:
    - main
  script:
    - docker compose -f /app/custom_config/panelmain/docker-compose_main.yml down
    - docker compose -f /app/custom_config/panelmain/docker-compose_main.yml up -d

# coverage_interface_test:
#   stage: test
#   tags:
#     - pms-api
#   only:
#     - dev
#   script:
#     - docker compose -f /repo/deploy/test/docker-compose_panel_test.yml up -d
#     - sleep 15
#     - rm -rf ./newman
#     - set +e
#     - for file in `ls ./tests/interface/authority/*.json`; do newman run $file -r htmlextra --reporter-html-export authority.html;done
#     - for file in `ls ./tests/interface/asset/*.json`; do newman run $file -e ./tests/interface/asset/assert.postman_environment.json -r htmlextra --reporter-html-export asset.html;done 
#     - for file in `ls ./tests/interface/topology/*.json`; do newman run $file -e ./tests/interface/topology/topology.postman_environment.json -r htmlextra --reporter-html-export topology.html;done
#     - for file in `ls ./tests/interface/alarm/*.json`; do newman run $file -e ./tests/interface/alarm/alarm.postman_environment.json -r htmlextra --reporter-html-export alarm.html;done
#     - for file in `ls ./tests/interface/Analysis/*.json`; do newman run $file -e ./tests/interface/Analysis/Analysis.postman_environment.json -r htmlextra --reporter-html-export Analysis.html;done
#     - for file in `ls ./tests/interface/workorder/*.json`; do newman run $file -e ./tests/interface/workorder/workorder.postman_environment.json -r htmlextra --reporter-html-export workorder.html;done
#     - for file in `ls ./tests/interface/3D/*.json`; do newman run $file -e ./tests/interface/3D/3D.postman_environment.json -r htmlextra --reporter-html-export alarm.html;done
#     - set -e
#     - gensummary -p ./newman
#   artifacts:
#     when: always
#     expire_in: 1 month
#     paths:
#       - ./newman

# undeploy_test_server:
#   stage: undeploy
#   when: always
#   tags:
#     - pms-api
#   only:
#     - dev
#   script:
#     - cd /repo/deploy/test/
#     - docker compose -f docker-compose_panel_test.yml down

# push_dev_and_test_server:
#   stage: push
#   when: always
#   tags:
#     - pms-api
#   only:
#     - dev
#   script:
#     - docker login cr.siemens.com
#     - docker image push $IMAGE_NAME:dev
#     - docker image push $IMAGE_NAME:test

# push_main_server:
#   stage: push
#   when: always
#   tags:
#     - pms-api
#   only:
#     - main
#   script:
#     - docker login cr.siemens.com
#     - docker image push $IMAGE_NAME:latest
