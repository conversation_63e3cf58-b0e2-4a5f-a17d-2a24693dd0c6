﻿Error_Common_Param=Invalid parameter
Error_Json_Param=Please upload the correct JSON file
Error_Common_NotExists=Not exists
Error_Common_TokenTimeout=The token has expired
Error_Common_IncorrectFileFormat=The file format is incorrect
Error_Common_ServerException=Server malfunction, please contact Siemens after-sales service
Error_Asset_Not_InJson=If the device JSON is not configured in batches, configure the device JSON first

Error_Auth_UserPassword=The account name or password is incorrect
Error_Auth_NoExistByAdmin=The current user does not have administrator privileges

Error_My_SamePassword=The old and new passwords cannot be the same
Error_My_InvaildPassword=Incorrect password
Error_My_InvaildPasswordFormat=The format of the new password is incorrect
Error_My_InvaildOldPassword=Old password error

Error_User_NotExists=The user doesn't exist
Error_User_InvaildUserNameFormat=The account name is incorrect
Error_User_InvaildNameFormat=The name is incorrect
Error_User_InvaildMobileFormat=The mobile phone number is incorrect
Error_User_InvaildEmailFormat=The email address is incorrect
Error_User_UserNameIsExists=The account already exists

Error_Asset_NotExists=Nonexistence of assets
Error_Asset_InvaildLevel=Asset level error
Error_Asset_AssetNumberIsExists=Binary point description already exists
Error_Asset_AssetNameIsExists=The asset name already exists
Error_Asset_BitPointIsExists=There is a data point description with the same name, please modify it to another name
Error_Asset_AssetNameVerifyError=The asset name is too long or does not meet requirements
Error_Asset_PrincipalVerifyError=The name of the person in charge is too long or does not meet the requirements
Error_Asset_LocationVerifyError=The address is too long or does not meet the requirements
Error_Asset_DescriptionVerifyError=The description is too long or does not meet the requirements
Error_Asset_AssetMakerVerifyError=The manufacturer is too long or does not meet the requirements
Error_Asset_TelVerifyError=The phone is too long or contains special characters
Error_Asset_FileNotExists=file does not exist
Error_Asset_TopologyFail=Create topoplogy failed
Error_Asset_TopologySuccess=Create topoplogy success
Error_Asset_Topology3DFail=Create 3D topoplogy failed
Error_Asset_Topology3DSuccess=Create 3D topoplogy success
Error_Asset_Topology3D_NotExists=Device Drawing Not Exists
Error_Asset_FileVersionWrong=The uploaded asset template version is incompatible (the minimum supported version number is 1.0.20230515)
Error_Asset_SubstationMissParent=The substation parent information is lost
Error_Asset_PanelMissParent=The panel parent information is lost
Error_Asset_CircuitMissParent=The circuit parent information is lost
Error_Asset_DeviceMissParent=The device parent information is lost
Error_Asset_DeviceModelIncorrect=The device model is incorrect
Error_Asset_InvalidInstallTime=The install time is invalid
Error_Asset_Ttpfdoesnotexist=The third-party file does not exist

Swagger_Asset_GetUserCheckGeneralDevicePoint=User selects data points
Swagger_Asset_GetUserCheckGeneralDevicePoint_Desc=User selects data points
Swagger_Asset_GetPointConfigs=Obtain point configuration information based on asset ID
Swagger_Asset_GetPointConfigs_Desc=Obtain point configuration information based on asset ID
Swagger_Asset_AddPointConfigs=Save point configuration
Swagger_Asset_AddPointConfigs_Desc=Save point configuration
Swagger_Asset_GetAnalyzingDataPoints=Get binary point properties
Swagger_Asset_GetAnalyzingDataPoints_Desc=Get binary point properties
Swagger_Asset_GetBitPoints=Obtain the description of secondary system points based on binary attribute IDs
Swagger_Asset_GetBitPoints_Desc=Obtain the description of secondary system points based on binary attribute IDs
Swagger_Asset_SaveBitPoints=Save binary point description information
Swagger_Asset_SaveBitPoints_Desc=Save binary point description information
Swagger_Asset_GetChartDatas=Obtain charts based on point attributes
Swagger_Asset_GetChartDatas_Desc=Obtain charts based on point attributes
Swagger_Asset_GetTimedChartDatas=Obtain a single point attribute chart
Swagger_Asset_GetTimedChartDatas_Desc=Obtain a single point attribute chart
Swagger_Asset_GetGeneralDevices=Obtain third-party universal device information
Swagger_Asset_GetGeneralDevices_Desc=Obtain third-party universal device information
Swagger_Asset_BatchSpecifyByAsset=Batch specifying JSON files for general devices
Swagger_Asset_BatchSpecifyByAsset_Desc=Batch specifying JSON files for general devices
Swagger_AssetStatus_GetPowerCalculation=Obtain the active power, reactive power, and electrical energy data of the distribution cabinet
Swagger_AssetStatus_GetPowerCalculation_Desc=Obtain the active power, reactive power, and electrical energy data of the distribution cabinet

Error_Role_NotExists=The role doesn't exist
Error_Role_CountNotEnough=The number of roles is insufficient

Error_File_NotExists=file does not exist

Error_Job_NotExists=Task does not exist

Error_Asset_MissDevice=The device parent information is incorrect
Error_Asset_MissCircuit=The circuit parent information is incorrect
Error_Asset_MissPanel=The panel parent information is incorrect
Error_Asset_MissSubstation=The substation parent information is incorrect
Error_Topology_MissInfo=Missing busbar 
Error_Topology_MissCircuit=Missing circuit
Error_Topology_InvaildName=Invaild topoplogy name
Error_Topology_InvaildCode=Invaild drawing code
Error_Topology_UniqueCode=The drawing code already exists
Error_Topology_MissingCode=The drawing code cannot be empty
Error_Topology_MissDevice=Missing breaker
Error_Topology_ReuseRowNo=The row number is repeated
Error_Topology_MissReceiptCode=The receipt number has expired or is invalid
Error_DataPoint_ShowName=The ShowName is repeated

Error_PanelHealth_PanelHealthCodeIsExists=The health check rule already exists
Error_Electricity_Verify_Time=TimeRange verify failure
Error_Electricity_Verify_Season=SeansonRange verify failure
Error_Electricity_Verify_Step=Step charge Range verify failure

Success=success
Error_ThirdModel_TypeIsExists= Third Model Type IsExists
Error_TempMonitor_Verify_DataPoint=Data point description is not repeatable ：

Swagger_Asset_SelectAll=Query details about all assets
Swagger_Asset_SelectAll_Desc=Query details about all assets
Swagger_Asset_Select=Obtain details about a single asset
Swagger_Asset_Select_Desc=Obtain details about a single asset
Swagger_Asset_Insert=Adding an asset
Swagger_Asset_Insert_Desc=Adding an asset
Swagger_Asset_Update=Updating an asset
Swagger_Asset_Update_Desc=Updating an asset
Swagger_Asset_Delete=Deleting an asset
Swagger_Asset_Delete_Desc=Deleting an asset
Swagger_Asset_Copy=Copying assets
Swagger_Asset_Copy_Desc=Copying assets
Swagger_Asset_Move=asset transformation
Swagger_Asset_Move_Desc=Asset transfer interface
Swagger_Asset_GetStaticModel=Get static data interface
Swagger_Asset_GetStaticModel_Desc=Get static data interface
Swagger_Asset_UploadExcel=Import asset data
Swagger_Asset_UploadExcel_Desc=Import asset data. The imported file is an Excel file and needs to be imported according to the template (TODO does not have template verification)
Swagger_Asset_DownloadExcel=Export asset data
Swagger_Asset_DownloadExcel_Desc=The asset data is exported as an Excel file
Swagger_Asset_Search=Asset inquiry 
Swagger_Asset_Search_Desc=Asset inquiry interface
Swagger_Asset_GetOverviewList=Get the asset profile list
Swagger_Asset_GetOverviewList_Desc=Get the asset profile list

Swagger_AssetStatus_CircuitTemp=Get circuit temperature
Swagger_AssetStatus_CircuitTemp_Desc=Get circuit temperature interface
Swagger_AssetStatus_CircuitMeasurement=Get circuit Measurement
Swagger_AssetStatus_CircuitMeasurement_Desc=Get circuit Measurement interface

Swagger_Auth_Login=User login
Swagger_Auth_Login_Desc=User login
Swagger_Auth_Logout=User logout
Swagger_Auth_Logout_Desc=User logout
Swagger_Auth_IsUserByPrivileges=Determine if the current user has administrator privileges
Swagger_Auth_IsUserByPrivileges_Desc=Determine if the current user has administrator privileges

Swagger_FileManager_UploadFile=Uploading a file 
Swagger_FileManager_UploadFile_Desc=Uploading a file
Swagger_FileManager_GetFile=Obtain file
Swagger_FileManager_GetFile_Desc=Get the file by the file name
Swagger_FileManager_GetAllFiles=Get file list
Swagger_FileManager_GetAllFiles_Desc=Gets the file list interface


Swagger_My_UpdatePassword=Change password
Swagger_My_UpdatePassword_Desc=Change password
Swagger_My_UpdateProfileInfo=Modifying personal information
Swagger_My_UpdateProfileInfo_Desc=Modifying personal information

Swagger_Roles_UpdateRole=Updating an role
Swagger_Roles_UpdateRole_Desc=Updating an role
Swagger_Roles_GetAllRoles=Getting the role list
Swagger_Roles_GetAllRoles_Desc=Getting the role list (total gas)
Swagger_Roles_GetRolePermissions=Obtain the role permission list
Swagger_Roles_GetRolePermissions_Desc=Obtain the role permission list (Contains all permissions, use IsSelect to determine whether to own)
Swagger_Roles_UpdateRolePermissions=Updating the role permission list
Swagger_Roles_UpdateRolePermissions_Desc=Updating the role permission list (The default contains "My" and "Profile")

Swagger_Users_UserInfo=Obtaining user information
Swagger_Users_UserInfo_Desc=Obtaining user information (Obtained by token)
Swagger_Users_CreateUser=Creating a user
Swagger_Users_CreateUser_Desc=Creating a user
Swagger_Users_UpdateUser=Updating a user
Swagger_Users_UpdateUser_Desc=Updating a user
Swagger_Users_GetUserRoles=Obtain the user role list
Swagger_Users_GetUserRoles_Desc=Obtain the user role list
Swagger_Users_UpdateUserRoles=Updating the user role list
Swagger_Users_UpdateUserRoles_Desc=Updating the user role list
Swagger_Users_GetUsers=Getting the user list
Swagger_Users_GetUsers_Desc=Getting the user list (total gas)
Swagger_Users_DeleteUser=Deleting a user
Swagger_Users_DeleteUser_Desc=Deleting a user (Soft delete)
Swagger_Users_ResetPassword=Reset password
Swagger_Users_ResetPassword_Desc=Reset password
Swagger_Users_BatchDeleteUsers=Deleting users in batch
Swagger_Users_BatchDeleteUsers_Desc=Deleting users in batch (Soft delete)
Swagger_Users_PrefixTel=Obtain the phone number prefix
Swagger_Users_PrefixTel_Desc=Obtain the phone number prefix

Swagger_Topology_Update=Update the topology view
Swagger_Topology_Update_Desc=Update the topology view
Swagger_Topology_Add=Add a topology view
Swagger_Topology_Add_Desc=Add a topology view
Swagger_Topology_Delete=Delete the topology view
Swagger_Topology_Delete_Desc=Delete the topology view
Swagger_Topology_Get=Search the topology by Id
Swagger_Topology_Get_Desc=Search the topology by Id
Swagger_Topology_GetAll=Finds all topologies
Swagger_Topology_GetAll_Desc=Finds all topologies

Swagger_Alam_GetCurrentInfo=Get an overview of current alarm information
Swagger_Alam_GetCurrentInfo_Desc=Get an overview of current alarm information
Swagger_Alam_GetAll=Get the alarm log list
Swagger_Alam_GetAll_Desc=Get the alarm log list
Swagger_Alam_Get=Get the alarm log
Swagger_Alam_Get_Desc=Get the alarm log
Swagger_Alam_BatchChangeLogStatus=Batch modify log status
Swagger_Alam_BatchChangeLogStatus_Desc=Batch modify log status
Swagger_AlamRule_GetHistroy=Get alarm rule modification logs
Swagger_AlamRule_GetHistroy_Desc=Get alarm rule modification logs
Swagger_AlamRule_SelectAll=Get all alarm rules
Swagger_AlamRule_SelectAll_Desc=Get all alarm rules
Swagger_AlamRule_Add=Adding an alarm rule
Swagger_AlamRule_Add_Desc=Adding an alarm rule
Swagger_AlamRule_Select=Get an alarm rule
Swagger_AlamRule_Select_Desc=Get an alarm rule
Swagger_AlamRule_Delete=Delete an alarm rule
Swagger_AlamRule_Delete_Desc=Delete an alarm rule
Swagger_AlamRule_BatchDelete=Delete alarm rules in batches
Swagger_AlamRule_BatchDelete_Desc=Delete alarm rules in batches
Swagger_PanelHealth_GetPanelHealth=Get the switchgear health status
Swagger_PanelHealth_GetPanelHealth_Desc=Get the switchgear health status
Swagger_PanelHealth_RecheckResult=Get the recheck result
Swagger_PanelHealth_RecheckResult_Desc=Get the recheck result
Swagger_PanelHealth_Recheck=Start rechecking
Swagger_PanelHealth_Recheck_Desc=Start rechecking
Swagger_PanelHealth_GetPanelHealthList=Get the health check rule list
Swagger_PanelHealth_GetPanelHealthList_Desc=Get the health check rule list
Swagger_PanelHealth_Add=Adding a health check rule
Swagger_PanelHealth_Add_Desc=Adding a health check rule
Swagger_PanelHealth_Update=Updating a health check rule
Swagger_PanelHealth_Update_Desc=Updating a health check rule
Swagger_PanelHealth_Delete=Deleting a health check rule
Swagger_PanelHealth_Delete_Desc=Deleting a health check rule
Swagger_Topology_UpdateDashboard=Set the display list of the home page of the single line chart (deprecated)
Swagger_Topology_UpdateDashboard_Desc=Set the display list of the home page of the single line chart (deprecated)
Swagger_Topology_UpdateMainDashboard=Set the home page display of the single line diagram
Swagger_Topology_UpdateMainDashboard_Desc=Set the home page display of the single line diagram
Swagger_Topology_GetMainDashboard=Get the single-line diagram of the home page
Swagger_Topology_GetMainDashboard_Desc=Get the single-line diagram of the home page
Swagger_Topology_GetDashboardList=Get Home page display list (deprecated)
Swagger_Topology_GetDashboardList_Desc=Get Home page display list (deprecated)
Swagger_Topology_Working=User status is synchronized when the single-line chart is updated
Swagger_Topology_Working_Desc=User status is synchronized when the single-line chart is updated
Swagger_SystemHealth_GetHealthData=Get the health data of the switchboard
Swagger_SystemHealth_GetHealthData_Desc=Get the health data of the switchboard
Swagger_SystemHealth_GetCabinetHealthData=Get the health data of the cabinet
Swagger_SystemHealth_GetCabinetHealthData_Desc=Get the health data of the cabinet
Swagger_SystemHealth_ExportHealthResult=Export the health report of the power distribution room
Swagger_SystemHealth_ExportHealthResult_Desc=Export the health report of the power distribution room
Swagger_SystemHealth_CabinetHealthStatistics=Get health statistics of the distribution cabinet in the distribution room
Swagger_SystemHealth_CabinetHealthStatistics_Desc=Get health statistics of the distribution cabinet in the distribution room
Swagger_LossDiagnosis_GetChartData=Get loss analysis data
Swagger_LossDiagnosis_GetChartData_Desc=Get loss analysis data
Swagger_LossDiagnosis_GetSysLossRefer=Get user-defined reference loss
Swagger_LossDiagnosis_GetSysLossRefer_Desc=Get user-defined reference loss
Swagger_LossDiagnosis_GetGetlineImpedance=Obtain the impedance information of the line
Swagger_LossDiagnosis_GetGetlineImpedance_Desc=Obtain the impedance information of the line
Swagger_LossDiagnosis_AddLineImpedance=Added the impedance information of the line
Swagger_LossDiagnosis_AddLineImpedance_Desc=Added the impedance information of the line
Swagger_LossDiagnosis_CheckTopoConnectedness=Check Topo Connectedness
Swagger_LossDiagnosis_CheckTopoBreakerNode=Check whether the circuit breaker is configured with rated power and rated current
Swagger_LossDiagnosis_CheckTopoBreakerNode_Desc=Check whether the circuit breaker is configured with rated power and rated current
Swagger_LossDiagnosis_CheckCAIPredict=Check if you can make AI predictions
Swagger_LossDiagnosis_CheckCAIPredict_Desc=Check if you can make AI predictions
Swagger_LossDiagnosis_GetNtlResult=Get unknown loss location
Swagger_LossDiagnosis_GetNtlResult_Desc=Get unknown loss location
Swagger_LossDiagnosis_GetKpi=Loss statistics
Swagger_LossDiagnosis_GetKpi_Desc=Loss statistics

Swagger_AlamRule_Update=Update an alarm rule
Swagger_AlamRule_Update_Desc=Update an alarm rule

Swagger_Mqtt_MqttClientStartOrStop=Start/stop mqtt
Swagger_Mqtt_MqttClientStartOrStop_Desc=Start/stop mqtt
Swagger_Mqtt_GetMqttConnectedStatus=Get mqtt status
Swagger_Mqtt_GetMqttConnectedStatus_Desc=Get mqtt status
Swagger_Mqtt_GetMqttConfig=Query mqtt configuration information
Swagger_Mqtt_GetMqttConfig_Desc=Query mqtt configuration information
Swagger_Mqtt_ModifyMqttConfig=Update mqtt configuration information
Swagger_Mqtt_ModifyMqttConfig_Desc=Update mqtt configuration information
Swagger_Mqtt_PingTestMqttConfig=Test mqtt service connection
Swagger_Mqtt_PingTestMqttConfig_Desc=Test mqtt service connection
Swagger_Mqtt_UploadTlsFiles=Upload Tls certificate
Swagger_Mqtt_UploadTlsFiles_Desc=Upload Tls certificate
Swagger_Mqtt_GetPointConfigs=Query point configuration information
Swagger_Mqtt_GetPointConfigs_Desc=Query point configuration information
Swagger_Mqtt_GetMqttDataPointConfigs=Query grouping point configuration information
Swagger_Mqtt_GetMqttDataPointConfigs_Desc=Query grouping point configuration information
Swagger_Mqtt_ModifyGroupPointConfig=Renaming Group Names
Swagger_Mqtt_ModifyGroupPointConfig_Desc_Desc=Renaming Group Names
Swagger_Mqtt_DelMqttDataPointConfig=Delete grouping information
Swagger_Mqtt_DelMqttDataPointConfig_Desc=Delete grouping information
Swagger_Mqtt_CopyToPoint=Copy grouping points
Swagger_Mqtt_CopyToPoint_Desc=Copy grouping points
Swagger_Mqtt_AddMqttDataPointConfig=Save grouping point information
Swagger_Mqtt_AddMqttDataPointConfig_Desc=Save grouping point information
Swagger_Mqtt_GetSynchronizeAssetinfo=Get synchronized asset list
Swagger_Mqtt_GetSynchronizeAssetinfo_Desc=Get synchronized asset list
Swagger_Mqtt_AddGroupConfig=Add grouping
Swagger_Mqtt_AddGroupConfig_Desc=Add grouping
Swagger_Mqtt_MqttReset=Reset of Mqtt Advanced Configuration
Swagger_Mqtt_MqttReset_Desc=Reset of Mqtt Advanced Configuration
Swagger_Mqtt_DownMqttConfig=Export Mqtt configuration
Swagger_Mqtt_DownMqttConfig_Desc=Export Mqtt configuration

Mqtt_Info_Sinitmqttc=Successfully initialized mqtt configuration
Mqtt_Err_Ftinitmcemsg=Failed to initialize mqtt configuration, error message
Mqtt_Err_Tconfcanbempty=The configuration file cannot be empty
Mqtt_Info_Successstartmqtt=Successfully started mqtt
Mqtt_Err_Ftosmqttwemsg=Failed to start mqtt with exception message
Mqtt_Info_Successstopmqtt=Successfully stopped mqtt
Mqtt_Err_Stopmqttfwemsg=Stopping mqtt failed with exception message
Mqtt_Err_Currennisstartstate=Currently not in self starting state
Mqtt_Info_Online=On line
Mqtt_Info_Brokenline=Broken line
Mqtt_Err_Failtomqttstatuswemsg=Failed to obtain mqtt status with exception message
Mqtt_Info_Successpdata=Successfully published data
Mqtt_Err_Tmqttshbdpreconnect=The mqtt service has been disconnected, please reconnect
Mqtt_Err_Failtopdataemsgis=Failed to publish data, exception message is
Mqtt_Info_QueryWasSuccessful=query was successful
Mqtt_Info_UpdateSuccessful=Update successful
Mqtt_Err_Puafwtspemorkey=Please upload a file with the suffix. pem or. key
Mqtt_Info_Successfullyuploadedcertificate=Successfully uploaded certificate
Mqtt_Info_Successfullydeleted=Successfully deleted certificate
Mqtt_Err_Idcannotbeorempty=ID cannot be 0 or empty
Mqtt_Err_Groupnamecannotbeempty=Group name cannot be empty
Mqtt_Err_Tiagnwtsnpmitadname=There is a group name with the same name, please modify it to a different name
Mqtt_Err_AssetIdcannotbeempty=AssetId cannot be empty
Mqtt_Info_SaveSuccessful=Save successful
Mqtt_Err_Thegroupnameisduplicated=The group name is duplicated
Mqtt_Err_Groupingdoesnotexist=Grouping does not exist
Mqtt_info_Deletesuccessful=Delete successful
Mqtt_Err_Tatitcaroainacbr=The asset types in the copied and replaced objects are inconsistent and cannot be replaced
Mqtt_info_Pastesuccessful=Paste successful
Mqtt_info_Resetsuccessful=Reset successful
Mqtt_info_Distributionroom=Distribution room
Mqtt_info_Switchgear=Switchgear
Mqtt_info_Loop=Loop
Mqtt_info_Equipment=Equipment
Mqtt_Err_BrokerserviceIPcannotbeempty=Broker service IP cannot be empty
Mqtt_Err_Theportnumbercannotbeempty=The port number is empty or invaild
Mqtt_Err_ClientIdcannotbeempty=ClientId cannot be empty
Mqtt_Err_Themecannotbeempty=Theme cannot be empty
Mqtt_info_PleaseuploadTLSPomFilefile=Please upload TLSPomFile file
Mqtt_info_PleaseuploadClientPrivateFilefile=Please upload ClientPrivateFile file
Mqtt_info_PleaseuploadClientPemFilefile=Please upload ClientPemFile file
Mqtt_Err_Errorwhileconnectingwithhost=Please check if the Borker address and port number are correct and if the network is unobstructed
Mqtt_Err_Specifiedargumentwasoutoftherangeofvalidvalues=The port number is incorrect and exceeds the range of the port number
Mqtt_Err_Exceptionwhilereadingfromstream=Network not working
Mqtt_Err_Theoperationhastimedout=Please check if the network is unobstructed and if the Borker address and port number are correct
Mqtt_Err_Connectingwithmqttserverfailed=Please check if the username, password, and theme are correct and special characters cannot be entered
Mqtt_Err_Eofor0bytes=Please check if the Borker address and port number are correct
Mqtt_Err_RemoteCertificateValidationCallback=Please enable mqtt to ignore the security mode of client certificates
Mqtt_Err_Nosuchfile=The SSL/TLS certificate file does not exist
Mqtt_Err_Authenticationfailed=Connection failed, please check if the SSL/TLS certificate is correct
Mqtt_Err_Thedecryptionoperationfailedseeinnerexception=Authentication error, decryption failed
Mqtt_Err_Inputstringwasnotinacorrectformat=The format of the input string is incorrect
Mqtt_Err_Tscanftsfile=The system cannot find the specified SSL/TLS certificate file
Mqtt_Err_ExceptionwhileconnectingDbType=Please check if the connection string of the database is correct and if the network is unobstructed
Mqtt_Err_Tkeycdonotcapemtcisotkdnmtcer=The key contents do not contain a PEM, the content is malformed, or the key does not match the certificate
Mqtt_Err_Cermorincerconformat=Certificate mismatch or incorrect certificate content format
Mqtt_Err_Pchkiftvtapnmath=Please check if the verification type and port number match
Mqtt_Err_MQTTconnectcanceled=The MQTT service connection has timed out. Please check if the MQTT service or the firewall is functioning properly
Mqtt_Group_Name_Measurement=Measurement
Mqtt_Group_Name_Alarm=Alarm
Mqtt_Group_Name_Status=Status

Swagger_MeterRead_AddAttributes=Add cache attribute
Swagger_MeterRead_AddAttributes_Desc=Add cache attribute
Swagger_MeterRead_GetAttributes=Retrieve cache properties
Swagger_MeterRead_GetAttributes_Desc=Retrieve cache properties
Swagger_MeterRead_GetAllAttributes=Retrieve meter reading attributes
Swagger_MeterRead_GetAllAttributes_Desc=Retrieve meter reading attributes
Swagger_MeterRead_GetAllSubstation=Obtain all distribution rooms
Swagger_MeterRead_GetAllSubstation_Desc=Obtain all distribution rooms
Swagger_MeterRead_GetTabChartData=Obtain meter reading table data
Swagger_MeterRead_GetTabChartData_Desc=Obtain meter reading table data
Swagger_MeterRead_GetRealTabChartData=Obtain real-time data from meter reading tables
Swagger_MeterRead_GetRealTabChartData_Desc=Obtain real-time data from meter reading tables
Swagger_MeterRead_DownExcel=Export meter reading table to Excel
Swagger_MeterRead_DownExcel_Desc=Export meter reading table to Excel

MeterRead_Err_AttributeByEmpty=The attribute collection cannot be empty

ThirdPartDevice_Prompt_udcCode=There are no predefined ones, please complete the predefined ones in the JSON file first

Swagger_Asset_GetThirdDeviceName=Obtain the name of a third-party device
Swagger_Asset_GetThirdDeviceName_Desc=Obtain the name of a third-party device

Download_Asset_FileName=Assets overview
Download_Alarm_FileName=Alarm logs

Power_Consumption_File_Name=Power Consumption
Power_File_Name=Power
Peak_Flat_Valley_File_Name=Peak Flat Valley
Structure_File_Name=Structure
Top_Ten_File_Name=Top Ten

StaticModel_PREFIXMOBILENUMBER_0086=the mainland
StaticModel_PREFIXMOBILENUMBER_00852=Hong Kong 
StaticModel_PREFIXMOBILENUMBER_00853=Macao 
StaticModel_PREFIXMOBILENUMBER_00886=Taiwan
StaticModel_PREFIXMOBILENUMBER_001=USA/Canada
StaticModel_PREFIXMOBILENUMBER_007=Russia/Kazakhstan
StaticModel_PREFIXMOBILENUMBER_0020=Egypt
StaticModel_PREFIXMOBILENUMBER_0027=South Africa
StaticModel_PREFIXMOBILENUMBER_0041=Switzerland
StaticModel_PREFIXMOBILENUMBER_0049=Germany
StaticModel_PREFIXMOBILENUMBER_0051=Peru
StaticModel_PREFIXMOBILENUMBER_0052=Mexico
StaticModel_PREFIXMOBILENUMBER_0053=Cuba
StaticModel_PREFIXMOBILENUMBER_0054=Argentina
StaticModel_PREFIXMOBILENUMBER_0055=Brazil
StaticModel_PREFIXMOBILENUMBER_0056=Chile
StaticModel_PREFIXMOBILENUMBER_0057=Columbia
StaticModel_PREFIXMOBILENUMBER_0058=Venezuela
StaticModel_PREFIXMOBILENUMBER_0060=Malaysia
StaticModel_PREFIXMOBILENUMBER_0061=Australian
StaticModel_PREFIXMOBILENUMBER_0062=Indonesia
StaticModel_PREFIXMOBILENUMBER_0063=the Philippines
StaticModel_PREFIXMOBILENUMBER_0064=New Zealand
StaticModel_PREFIXMOBILENUMBER_0065=Singapore
StaticModel_PREFIXMOBILENUMBER_0066=Thailand
StaticModel_PREFIXMOBILENUMBER_0081=Japan
StaticModel_PREFIXMOBILENUMBER_0082=South Korea
StaticModel_PREFIXMOBILENUMBER_0084=Vietnam
StaticModel_PREFIXMOBILENUMBER_0090=turkey
StaticModel_PREFIXMOBILENUMBER_0091=India
StaticModel_PREFIXMOBILENUMBER_0092=Pakistan
StaticModel_PREFIXMOBILENUMBER_0093=Afghanistan
StaticModel_PREFIXMOBILENUMBER_0094=Sri Lanka
StaticModel_PREFIXMOBILENUMBER_0095=Burma
StaticModel_PREFIXMOBILENUMBER_0098=Iran
StaticModel_PREFIXMOBILENUMBER_00212=Morocco
StaticModel_PREFIXMOBILENUMBER_00213=Algeria
StaticModel_PREFIXMOBILENUMBER_00216=Tunisia
StaticModel_PREFIXMOBILENUMBER_00218=Libya
StaticModel_PREFIXMOBILENUMBER_00220=Gambia
StaticModel_PREFIXMOBILENUMBER_00221=Senegal
StaticModel_PREFIXMOBILENUMBER_00223=Mali
StaticModel_PREFIXMOBILENUMBER_00224=Guinea
StaticModel_PREFIXMOBILENUMBER_00225=Cote d'Ivoire
StaticModel_PREFIXMOBILENUMBER_00226=burkina faso
StaticModel_PREFIXMOBILENUMBER_00227=the Niger
StaticModel_PREFIXMOBILENUMBER_00228=Togo
StaticModel_PREFIXMOBILENUMBER_00229=Benin
StaticModel_PREFIXMOBILENUMBER_00230=Mauritius
StaticModel_PREFIXMOBILENUMBER_00231=Liberia
StaticModel_PREFIXMOBILENUMBER_00232=sierra leone
StaticModel_PREFIXMOBILENUMBER_00233=Ghana
StaticModel_PREFIXMOBILENUMBER_00234=Nigeria
StaticModel_PREFIXMOBILENUMBER_00235=Chad
StaticModel_PREFIXMOBILENUMBER_00236=Central African Republic
StaticModel_PREFIXMOBILENUMBER_00237=Cameroon
StaticModel_PREFIXMOBILENUMBER_00239=Sao Tome and Principe
StaticModel_PREFIXMOBILENUMBER_00241=Gabon
StaticModel_PREFIXMOBILENUMBER_00242=Congo
StaticModel_PREFIXMOBILENUMBER_00243=Zaire
StaticModel_PREFIXMOBILENUMBER_00244=Angola
StaticModel_PREFIXMOBILENUMBER_00247=ascension
StaticModel_PREFIXMOBILENUMBER_00248=Seychelles
StaticModel_PREFIXMOBILENUMBER_00249=Sudan
StaticModel_PREFIXMOBILENUMBER_00250=Rwanda
StaticModel_PREFIXMOBILENUMBER_00251=Ethiopia
StaticModel_PREFIXMOBILENUMBER_00252=Somalia
StaticModel_PREFIXMOBILENUMBER_00253=Djibouti
StaticModel_PREFIXMOBILENUMBER_00254=Kenya
StaticModel_PREFIXMOBILENUMBER_00255=Tanzania
StaticModel_PREFIXMOBILENUMBER_00256=Uganda
StaticModel_PREFIXMOBILENUMBER_00257=Burundi
StaticModel_PREFIXMOBILENUMBER_00258=Mozambique
StaticModel_PREFIXMOBILENUMBER_00260=Zambia
StaticModel_PREFIXMOBILENUMBER_00261=Madagascar
StaticModel_PREFIXMOBILENUMBER_00262=Reunion
StaticModel_PREFIXMOBILENUMBER_00263=Zimbabwe
StaticModel_PREFIXMOBILENUMBER_00264=Namibia
StaticModel_PREFIXMOBILENUMBER_00265=Malawi
StaticModel_PREFIXMOBILENUMBER_00266=Lesotho
StaticModel_PREFIXMOBILENUMBER_00267=Botswana
StaticModel_PREFIXMOBILENUMBER_00268=Eswatini
StaticModel_PREFIXMOBILENUMBER_00350=Gibraltar
StaticModel_PREFIXMOBILENUMBER_00355=Albania
StaticModel_PREFIXMOBILENUMBER_00373=Moldova
StaticModel_PREFIXMOBILENUMBER_00374=Armenia
StaticModel_PREFIXMOBILENUMBER_00375=Belarus
StaticModel_PREFIXMOBILENUMBER_00376=Andorra
StaticModel_PREFIXMOBILENUMBER_00377=Monaco
StaticModel_PREFIXMOBILENUMBER_00378=San Marino
StaticModel_PREFIXMOBILENUMBER_00380=Ukraine
StaticModel_PREFIXMOBILENUMBER_00501=Belize
StaticModel_PREFIXMOBILENUMBER_00502=Guatemala
StaticModel_PREFIXMOBILENUMBER_00503=el salvador
StaticModel_PREFIXMOBILENUMBER_00504=Honduras
StaticModel_PREFIXMOBILENUMBER_00505=Nicaragua
StaticModel_PREFIXMOBILENUMBER_00506=Costa Rica
StaticModel_PREFIXMOBILENUMBER_00507=Panama
StaticModel_PREFIXMOBILENUMBER_00509=Haiti
StaticModel_PREFIXMOBILENUMBER_00591=Bolivia
StaticModel_PREFIXMOBILENUMBER_00592=Guyana
StaticModel_PREFIXMOBILENUMBER_00593=Ecuador
StaticModel_PREFIXMOBILENUMBER_00594=French Guiana
StaticModel_PREFIXMOBILENUMBER_00595=Paraguay
StaticModel_PREFIXMOBILENUMBER_00596=Martinique
StaticModel_PREFIXMOBILENUMBER_00597=Surinam
StaticModel_PREFIXMOBILENUMBER_00598=Uruguay
StaticModel_PREFIXMOBILENUMBER_00599=the netherlands antilles
StaticModel_PREFIXMOBILENUMBER_00673=Brunei
StaticModel_PREFIXMOBILENUMBER_00674=Nauru
StaticModel_PREFIXMOBILENUMBER_00675=Papua New Guinea
StaticModel_PREFIXMOBILENUMBER_00676=Tonga
StaticModel_PREFIXMOBILENUMBER_00677=Solomon Islands
StaticModel_PREFIXMOBILENUMBER_00679=Fiji
StaticModel_PREFIXMOBILENUMBER_00682=cook islands
StaticModel_PREFIXMOBILENUMBER_00684=Eastern Samoa
StaticModel_PREFIXMOBILENUMBER_00685=Western Samoa
StaticModel_PREFIXMOBILENUMBER_00689=PF(French Polynesia)
StaticModel_PREFIXMOBILENUMBER_00850=North Korea
StaticModel_PREFIXMOBILENUMBER_00855=Cambodia
StaticModel_PREFIXMOBILENUMBER_00856=Laos
StaticModel_PREFIXMOBILENUMBER_00880=Bangladesh
StaticModel_PREFIXMOBILENUMBER_00960=Maldives
StaticModel_PREFIXMOBILENUMBER_00961=Lebanon
StaticModel_PREFIXMOBILENUMBER_00962=Jordan
StaticModel_PREFIXMOBILENUMBER_00963=Syria
StaticModel_PREFIXMOBILENUMBER_00964=Iraq
StaticModel_PREFIXMOBILENUMBER_00965=Kuwait
StaticModel_PREFIXMOBILENUMBER_00966=Saudi Arabia
StaticModel_PREFIXMOBILENUMBER_00967=Yemen
StaticModel_PREFIXMOBILENUMBER_00968=Oman
StaticModel_PREFIXMOBILENUMBER_00971=The United Arab Emirates
StaticModel_PREFIXMOBILENUMBER_00972=Israel
StaticModel_PREFIXMOBILENUMBER_00973=Bahrain
StaticModel_PREFIXMOBILENUMBER_00974=Katar
StaticModel_PREFIXMOBILENUMBER_00976=Mongolia
StaticModel_PREFIXMOBILENUMBER_00977=Nepal
StaticModel_PREFIXMOBILENUMBER_00992=Tajikistan
StaticModel_PREFIXMOBILENUMBER_00993=Turkmenistan
StaticModel_PREFIXMOBILENUMBER_00994=Azerbaijan
StaticModel_PREFIXMOBILENUMBER_00995=Georgia
StaticModel_PREFIXMOBILENUMBER_00996=Kyrgyzstan
StaticModel_PREFIXMOBILENUMBER_00998=Uzbekistan
StaticModel_PREFIXMOBILENUMBER_001242=Bahamas
StaticModel_PREFIXMOBILENUMBER_001246=Barbados
StaticModel_PREFIXMOBILENUMBER_001264=Anguilla
StaticModel_PREFIXMOBILENUMBER_001268=Antigua and Barbuda
StaticModel_PREFIXMOBILENUMBER_001345=Cayman Islands
StaticModel_PREFIXMOBILENUMBER_001441=Bermuda
StaticModel_PREFIXMOBILENUMBER_001664=montserrat
StaticModel_PREFIXMOBILENUMBER_001670=Mariana
StaticModel_PREFIXMOBILENUMBER_001671=Guam
StaticModel_PREFIXMOBILENUMBER_001758=Saint Lucia
StaticModel_PREFIXMOBILENUMBER_001784=St.Vincent
StaticModel_PREFIXMOBILENUMBER_001787=Puerto Rico
StaticModel_PREFIXMOBILENUMBER_001809=Trinidad and Tobago/Grenada
StaticModel_PREFIXMOBILENUMBER_001849=Dominican Republic
StaticModel_PREFIXMOBILENUMBER_001876=Jamaica
StaticModel_DEVICETYPE_MCCB=MCCB
StaticModel_DEVICEMODEL_3VA=3VA
StaticModel_DEVICEMODEL_3VA2=3VA2
StaticModel_DEVICEMODEL_Other=Other
StaticModel_DEVICETYPE_ACB=ACB
StaticModel_DEVICEMODEL_3WA=3WA
StaticModel_DEVICEMODEL_3WL=3WL
StaticModel_DEVICEMODEL_3WT=3WT
StaticModel_DEVICETYPE_MCB=MCB
StaticModel_DEVICEMODEL_5SL=5SL
StaticModel_DEVICEMODEL_5SV=5SV
StaticModel_DEVICEMODEL_5ST=5ST
StaticModel_DEVICETYPE_MotorProtector=Motor protector
StaticModel_DEVICEMODEL_3UF=3UF
StaticModel_DEVICEMODEL_3UE=3UE
StaticModel_DEVICETYPE_SoftStarter=Soft starter
StaticModel_DEVICEMODEL_3RW=3RW
StaticModel_DEVICETYPE_Relay=Relay
StaticModel_DEVICEMODEL_7SJ68=7SJ68
StaticModel_DEVICEMODEL_7SJ62=7SJ62
StaticModel_DEVICEMODEL_7SJ63=7SJ63
StaticModel_DEVICETYPE_ATSE=ATSE
StaticModel_DEVICEMODEL_3KC2=3KC2
StaticModel_DEVICEMODEL_3KC8=3KC8
StaticModel_DEVICETYPE_Gateway=Gateway
StaticModel_DEVICEMODEL_POC1000=POC1000
StaticModel_DEVICETYPE_PLC=PLC
StaticModel_DEVICEMODEL_PLC1200=PLC1200
StaticModel_DEVICETYPE_Meter=Meter
StaticModel_DEVICEMODEL_PAC1020=PAC1020
StaticModel_DEVICEMODEL_PAC3120=PAC3120
StaticModel_DEVICEMODEL_PAC3200=PAC3200
StaticModel_DEVICEMODEL_PAC3220=PAC3220
StaticModel_DEVICEMODEL_PAC4220=PAC4220
StaticModel_DEVICEMODEL_PAC4200=PAC4200
StaticModel_DEVICEMODEL_PAC5220=PAC5220
StaticModel_DEVICEMODEL_PAC1200=PAC1200
StaticModel_DEVICEMODEL_PAC1600=PAC1600
StaticModel_DEVICEMODEL_P35=P35
StaticModel_DEVICEMODEL_P36=P36
StaticModel_DEVICETYPE_GeneralDevice=General device
StaticModel_DEVICEMODEL_GeneralMeter=General meter
StaticModel_DEVICEMODEL_GeneralDevice=General device
StaticModel_DEVICETYPE_TempMeasurement=Temperature Monitoring System
StaticModel_DEVICEMODEL_SiemensTempMeasurement=7KT71
StaticModel_USESCENE_Lighting=Lighting
StaticModel_USESCENE_Dynamical=Dynamical
StaticModel_USESCENE_Process=Process
StaticModel_USESCENE_BusCoupler=Bus coupler
StaticModel_USESCENE_Emergency=Emergency
StaticModel_USESCENE_Spare=Spare
StaticModel_USESCENE_Other=Other
StaticModel_CIRCUITTYPE_=Unknown
StaticModel_CIRCUITTYPE_Feeder=Feeder-Frame
StaticModel_CIRCUITTYPE_FeederMCCB=Feeder-MCCB
StaticModel_CIRCUITTYPE_Motor=Motor
StaticModel_CIRCUITTYPE_Incoming=Incoming
StaticModel_CIRCUITTYPE_BusCoupler=Bus coupler
StaticModel_CIRCUITTYPE_Spare=Spare
StaticModel_CIRCUITTYPE_Thermorelay=Thermorelay
StaticModel_CIRCUITTYPE_ActivePowerFilter=Active power filter
StaticModel_CIRCUITTYPE_Capacitance=Capacitance compensation
StaticModel_CIRCUITTYPE_Other=Other
StaticModel_PANELTYPE_=Unknown
StaticModel_PANELTYPE_IncomingCabinet=Incoming Panel
StaticModel_PANELTYPE_OutletCabinet=Feeder Panel
StaticModel_PANELTYPE_BusbarCabinet=Bus Coupler Panel
StaticModel_PANELTYPE_SupplementCabinet=Supplement Panel
StaticModel_PANELTYPE_Other=Other
StaticModel_PANELMODEL_=Unknown
StaticModel_PANELMODEL_Sivecon8PT=SIVACON 8PT
StaticModel_PANELMODEL_SiveconS8=SIVACON S8
StaticModel_PANELMODEL_Other=Other
StaticModel_TRANSFORMERTYPE_OilTransformer=Oil Transformer
StaticModel_TRANSFORMERTYPE_DryTransformer=Dry Transformer
StaticModel_NOMINALVOLTAGE_400V=400V
StaticModel_WORK_ORDER_STATUS_Pending=Pending
StaticModel_WORK_ORDER_STATUS_Processing=Processing
StaticModel_WORK_ORDER_STATUS_Completed=Completed
StaticModel_WORK_ORDER_STATUS_Overdue=Overdue
StaticModel_WORK_ORDER_MEASURE_Check=Check
StaticModel_WORK_ORDER_MEASURE_Maintain=Maintain
StaticModel_WORK_ORDER_MEASURE_Repair=Repair
StaticModel_WORK_ORDER_TYPE_General=General
StaticModel_WORK_ORDER_TYPE_Alarm=Alarm

StaticModel_METERTYPE_Gateway=Gateway measurement
StaticModel_METERTYPE_Terminal=Terminal metering

Page_100000=System Management
Page_100100=UsersManagement
Page_100200=RolesManagement
Page_100300=Data Backup
Page_100400=About
Page_200000=Alarm Management
Page_200100=Alarm List
Page_200200=Alarm Detail
Page_200300=Alarm Configuration
Page_300000=My
Page_300100=Profile
Page_400000=Intelligent Analysis
Page_400100=Health Management
Page_400200=Loss Analysis
Page_500000=System Topology
Page_500100=Topology Edit
Page_600000=Configuration Editor
Page_600100=Graphic Editor
Page_600200=Single Line Diagram Editor
Page_600300=EditThreeDModel
Page_600400=ThreeDModelList
Page_700000=Energy Efficiency
Page_700100=Energy Management
Page_700300=EnergyEfficiency Analysis
Page_700400=Power Quality
Page_800000=Maintenance Management
Page_800100=Maintenance Work order
Page_800200=Maintenance Report
Page_900000=Asset
Page_900100=Asset Overview
Page_900200=Asset List
Page_900300=Device Scan
Page_1000000=System Overview
Page_1000100=Desktop
Page_1000200=Single Line Diagram
Page_1000300=Network Diagram
Page_400300=Protection Setting Value
Page_1100000=System Config
Page_1100100=Settings

Overview_HighRisk=High risk
Overview_MiddleRisk=Middle risk
Overview_UrgentDispatch=Urgent dispatch
Overview_NotUrgentDispatch=Not urgent dispatch
Overview_Normal=Normal
Overview_Warning=Warning
Overview_Hint=Hint
Overview_Repair=Repair
Overview_A-PhaseTemperature=A-phase temperature
Overview_B-PhaseTemperature=B-phase temperature
Overview_C-PhaseTemperature=C-phase temperature
Overview_N-PhaseTemperature=N-phase temperature
Overview_A-PhaseTemperature2=A-phase temperature2
Overview_B-PhaseTemperature2=B-phase temperature2
Overview_C-PhaseTemperature2=C-phase temperature2
Overview_N-PhaseTemperature2=N-phase temperature2
Overview_Excellent=Excellent
Overview_MaxTemperature=The highest temperature of the panel
Overview_MaxElectricity=The highest current of the panel
Overview_RemainingLife=Remaining life of the circuit in the panel
Overview_AlarmCount=Number of alarms in the panel
Overview_HighLevelAlarmCount=Number of high alarms of the panel
Overview_MiddleLevelAlarmCount=Number of middle alarms of the panel
Overview_LowLevelAlarmCount=Number of low alarms of the panel

PanelHealth_excellent=Excellent
PanelHealth_good=Good
PanelHealth_medium=Medium
PanelHealth_bad=Bad
PanelHealth_unknown=Unknown
PanelHealth_abnormalMsg=Under Risk
PanelHealth_normal=normal
PanelHealth_abnormal=abnormal

ReplacementPart_Delay=Lack of part
ReplacementPart_Urgent=Urgent document or dispatch

BreakerHealth_normal=Normal
BreakerHealth_attention=Attention
BreakerHealth_maintain=Maintain
BreakerHealth_rushRepair=Rush to repair
BreakerHealth_unknown=Unknown

Role_Root=Root
Role_Para=Para
Role_Operatorall=Operatorall
Role_Operator=Operator
Role_Guest=Guest

Alarm_RuleOpt_Add=Add
Alarm_RuleOpt_Update=Update
Alarm_RuleOpt_Enable=Enable
Alarm_RuleOpt_Disable=Disable
Alarm_RuleOpt_Delete=Delete
Alarm_RuleOpt_Current=Currently

Alarm_EventType_Alarm=Alarm
Alarm_EventType_BreakerTrip=Breaker trip info
Alarm_EventType_UdcAlarm=Device alarm
Alarm_EventType_DeviceLog=Device log
Alarm_Status_New=New
Alarm_Status_InProcess=InProcess
Alarm_Status_Finish=Finish
Alarm_Status_Error=Error
Alarm_Status_Ignore=Ignore
Alarm_Severity_Low=Low
Alarm_Severity_Middle=Middle
Alarm_Severity_High=High
Alarm_ChangeSource_Person=Manual
Alarm_ChangeSource_WorkOrder=Work order
Alarm_Temperature= Temperature alarm

Asset_Busbar1=Bus bar I
Asset_Busbar2=Bus bar II
Asset_Busbar3=Bus bar III
Asset_Busbar4=Bus bar IV
Asset_Busbar5=Bus bar V
Asset_Busbar6=Bus bar VI

OperationLogAction_Login=Login
OperationLogAction_Logout=Logout
OperationLogAction_UpdateUser=Update user {0}
OperationLogAction_DeleteUser=Delete user {0}
OperationLogAction_AddUser=Add user {0}
OperationLogAction_UpdateRole=Update role {0}
OperationLogAction_AddAsset=Add asset {0}
OperationLogAction_UpdateAsset=Update asset {0}
OperationLogAction_DeleteAsset=Delete asset {0}
OperationLogAction_CopyAsset=Copy asset
OperationLogAction_MoveAsset=Move asset
OperationLogAction_UploadAssetFile=Upload asset excel
OperationLogAction_UploadFile=Upload file {0}
OperationLogAction_ResetPassword=Reset {0}'s password
OperationLogAction_ChangeMyPassword=Change my password
OperationLogAction_ChangeMyProfile=Change my profile
OperationLogAction_AddPanelHealth=Add panel({0}) health check item: {1}
OperationLogAction_UpdatePanelHealth=Update panel({0}) health check item: {1}
OperationLogAction_DeletePanelHealth=Delete panel({0}) health check item: {1}
OperationLogAction_RecheckPanelHealth=Panel({0}) recheck health
OperationLogAction_AddTopology=Add topology {0}
OperationLogAction_UpdateTopology=Update topology {0}
OperationLogAction_DeleteTopology=Delete topology {0}
OperationLogAction_AddTopologyAsset=Add area image
OperationLogAction_UpdateTopologyAsset=Update area image
OperationLogAction_DeleteTopologyAsset=Delete area image
OperationLogAction_AddTopology3D=Add 3D topology {0}
OperationLogAction_UpdateTopology3D=Update 3D topology {0}
OperationLogAction_DeleteTopology3D=Delete 3D topology {0}
OperationLogAction_DeleteAlarmRule=Delete alarm rule {0}
OperationLogAction_AddAlarmRule=Add alarm rule {0}
OperationLogAction_UpdateAlarmRule=Update alarm rule {0}
OperationLogAction_EnableAlarmRule=Enable alarm rule {0}
OperationLogAction_DisableAlarmRule=Disable alarm rule {0}

OperationLogAction_SignIn=Sign in

OperationLogAction_DeleteSubstationDataPointConfig=Delete the environmental data configuration of the {0}
OperationLogAction_AddSubstationDataPointConfig=Add the environmental data configuration of the {0}
OperationLogAction_UpdateSubstationDataPointConfig=Update the environmental data configuration of the {0}

OperationLogAction_SignIn=Sign in

DataPoint_APhaseTemp1=A-phase incoming line temperature
DataPoint_APhaseTemp2=A-phase outgoing line temperature
DataPoint_BPhaseTemp1=B-phase incoming line temperature
DataPoint_BPhaseTemp2=B-phase outgoing line temperature
DataPoint_CPhaseTemp1=C-phase incoming line temperature
DataPoint_CPhaseTemp2=C-phase outgoing line temperature
DataPoint_F=Frequency
DataPoint_I_Avg=Current mean
DataPoint_Ia=The current of phase A
DataPoint_Ib=The current of phase B
DataPoint_Ic=The current of phase C
DataPoint_NPhaseTemp1=N-phase incoming line temperature
DataPoint_NPhaseTemp2=N-phase outgoing line temperature
DataPoint_P=Active power
DataPoint_PowFactor=Power factor
DataPoint_PowFactor_A=The power factor of phase A
DataPoint_PowFactor_B=The power factor of phase B
DataPoint_PowFactor_C=The power factor of phase C
DataPoint_Q=Reactive power
DataPoint_S=Apparent power
DataPoint_Switch=On-off state
DataPoint_THD_Ia=The THD current of phase A
DataPoint_THD_Ib=The THD current of phase B
DataPoint_THD_Ic=The THD current of phase C
DataPoint_THD_Ua=The THD voltage of phase A
DataPoint_THD_Ub=The THD voltage of phase B
DataPoint_THD_Uc=The THD voltage of phase A
DataPoint_Total_P=Total active power
DataPoint_Total_Q=Total reactive power
DataPoint_Total_S=Total apparent power
DataPoint_Ua=The voltage of phase A
DataPoint_Uab=The voltage of line AB
DataPoint_Ub=The voltage of phase B
DataPoint_Ubc=The voltage of line BC
DataPoint_Uc=The voltage of phase C
DataPoint_Uca=The voltage of line CA
DataPoint_HealthScore=Health score
DataPoint_Health_Status=Health Status
DataPoint_HealthLevel=Health Level
DataPoint_SpringCharged=Spring charged
DataPoint_SwitchReady=Ready for switching on
DataPoint_ElectricalSwitchCycles=Electrical switching cycles
DataPoint_MechanicalSwitchCycles=Mechanical switching cycles
DataPoint_BreakerTemp=Temperature in the circuit breaker
DataPoint_ComTemp=Temperature at the circuit breaker
DataPoint_ForwardActivePower=Total imported active energy
DataPoint_ForwardReactivePower=Total imported reactive energy
DataPoint_ReverseActivePower=Total exported active energy
DataPoint_ReverseReactivePower=Total exported reactive energy
DataPoint_OperatingHours=Operating time counter of the circuit breaker
DataPoint_MainContantStatus=Status of the main contacts
DataPoint_LTTrips=Number of LT trips
DataPoint_STTrips=Number of ST / dST trips
DataPoint_AllTrips=Number of all trips
DataPoint_RemainingLife=Remaining life
DataPoint_ContactWearRate=Main contact health
DataPoint_LT_ONOFF=LT tripping ON/OFF
DataPoint_LT_IR=LT current setting value I<sub>r</sub> (A)
DataPoint_LT_TR=LT tripping time t<sub>r</sub> at 6 x I<sub>r</sub> (ms)
DataPoint_LT_PARA_CURVE=Characteristic LT curve
DataPoint_LT_ONOFF_REMOTE=LT tripping ON/OFF for e.SET
DataPoint_LT_IR_REMOTE=LT current setting value I<sub>r</sub> e.SET (A)
DataPoint_LT_PARA_CURVE_REMOTE=Characteristic LT curve e.SET
DataPoint_LT_TAU=Cooling time constant (s)
DataPoint_LTN_ONOFF=LT N tripping ON/OFF
DataPoint_LTN_IN=LT N current setting value I<sub>N</sub> (A)
DataPoint_PAL_IN=PAL current setting value I<sub>N PAL </sub> (A)
DataPoint_ST_ONOFF=ST tripping ON/OFF
DataPoint_ST_ISD=ST current setting value I<sub>sd</sub> (A)
DataPoint_ST_TSD=ST tripping time t<sub>sd</sub> (ms)
DataPoint_ST_I2t_ONOFF=Characteristic ST curve
DataPoint_ST_ISD_REF_I2TSD=ST reference point I<sub>ST ref</sub> (Ir)
DataPoint_ST_ONOFF_REMOTE=ST tripping ON/OFF for e.SET
DataPoint_ST_ISD_REMOTE=ST current setting value I<sub>sd</sub> e.SET (A)
DataPoint_ST_TSD_REMOTE=ST tripping time t<sub>sd</sub> e.SET (ms)
DataPoint_INST_ONOFF=INST tripping ON/OFF
DataPoint_INST_II=INST current setting value I<sub>i</sub> (A)
DataPoint_GF_ONOFF=GF tripping ON/OFF
DataPoint_GF_INTERMITTENT_ONOFF=GF intermittent detection
DataPoint_GF_STD_PARA_CURVE=GFs characteristic GFs curve
DataPoint_GF_STD_IG_DIRECT=GFs current setting value I<sub>g</sub> (A)
DataPoint_GF_STD_TG_DIRECT=GFs tripping time t<sub>g</sub>  (ms)
DataPoint_GF_ALARM_ONOFF=GF alarm ON/OFF
DataPoint_GF_IG_DIRECT_ALARM=GF alarm current setting value
DataPoint_GFTrips=Number of GF trips
DataPoint_NTrips=Number of N trips
DataPoint_LT_THERMAL_MEM_ONOFF=Thermal memory
DataPoint_GF_IG=GF current setting value
DataPoint_GF_TG=GF tripping time
DataPoint_GF_PARA_CURVE=Characteristic GF curve
DataPoint_GF_TYPE=GF method
DataPoint_HaveAlarm=Have alarms
DataPoint_IsConnected=Connection state
DataPoint_LT_TR_REMOTE=LT tripping time t<sub>r</sub> at 6 x I<sub>r</sub> e.SET (ms)
DataPoint_THD_U=The THD voltage
DataPoint_THD_I=The THD current
DataPoint_Sensor_1=Temperature sensor 1 temperature
DataPoint_Sensor_2=Temperature sensor 2 temperature
DataPoint_Sensor_3=Temperature sensor 3 temperature
DataPoint_Sensor_4=Temperature sensor 4 temperature
DataPoint_Sensor_5=Temperature sensor 5 temperature
DataPoint_Sensor_6=Temperature sensor 6 temperature
DataPoint_Sensor_7=Temperature sensor 7 temperature
DataPoint_Sensor_8=Temperature sensor 8 temperature
DataPoint_Sensor_9=Temperature sensor 9 temperature
DataPoint_Sensor_10=Temperature sensor 10 temperature
DataPoint_Sensor_11=Temperature sensor 11 temperature
DataPoint_Sensor_12=Temperature sensor 12 temperature
DataPoint_Sensor_13=Temperature sensor 13 temperature
DataPoint_Sensor_14=Temperature sensor 14 temperature
DataPoint_Sensor_15=Temperature sensor 15 temperature
DataPoint_Sensor_16=Temperature sensor 16 temperature
DataPoint_Sensor_17=Temperature sensor 17 temperature
DataPoint_Sensor_18=Temperature sensor 18 temperature
DataPoint_Sensor_19=Temperature sensor 19 temperature
DataPoint_Sensor_20=Temperature sensor 20 temperature
DataPoint_Sensor_21=Temperature sensor 21 temperature
DataPoint_Sensor_22=Temperature sensor 22 temperature
DataPoint_Sensor_23=Temperature sensor 23 temperature
DataPoint_Sensor_24=Temperature sensor 24 temperature
DataPoint_Sensor_25=Temperature sensor 25 temperature
DataPoint_Sensor_26=Temperature sensor 26 temperature
DataPoint_Sensor_27=Temperature sensor 27 temperature
DataPoint_Sensor_28=Temperature sensor 28 temperature
DataPoint_Sensor_29=Temperature sensor 29 temperature
DataPoint_Sensor_30=Temperature sensor 30 temperature
DataPoint_Sensor_31=Temperature sensor 31 temperature
DataPoint_Sensor_32=Temperature sensor 32 temperature
DataPoint_Sensor_33=Temperature sensor 33 temperature
DataPoint_Sensor_34=Temperature sensor 34 temperature
DataPoint_Sensor_35=Temperature sensor 35 temperature
DataPoint_Sensor_36=Temperature sensor 36 temperature
DataPoint_Sensor_37=Temperature sensor 37 temperature
DataPoint_Sensor_38=Temperature sensor 38 temperature
DataPoint_Sensor_39=Temperature sensor 39 temperature
DataPoint_Sensor_40=Temperature sensor 40 temperature
DataPoint_Sensor_41=Temperature sensor 41 temperature
DataPoint_Sensor_42=Temperature sensor 42 temperature
DataPoint_Sensor_43=Temperature sensor 43 temperature
DataPoint_Sensor_44=Temperature sensor 44 temperature
DataPoint_Sensor_45=Temperature sensor 45 temperature
DataPoint_Sensor_46=Temperature sensor 46 temperature
DataPoint_Sensor_47=Temperature sensor 47 temperature
DataPoint_Sensor_48=Temperature sensor 48 temperature
DataPoint_Sensor_49=Temperature sensor 49 temperature
DataPoint_Sensor_50=Temperature sensor 50 temperature
DataPoint_Sensor_51=Temperature sensor 51 temperature
DataPoint_Sensor_52=Temperature sensor 52 temperature
DataPoint_Sensor_53=Temperature sensor 53 temperature
DataPoint_Sensor_54=Temperature sensor 54 temperature
DataPoint_Sensor_55=Temperature sensor 55 temperature
DataPoint_Sensor_56=Temperature sensor 56 temperature
DataPoint_Sensor_57=Temperature sensor 57 temperature
DataPoint_Sensor_58=Temperature sensor 58 temperature
DataPoint_Sensor_59=Temperature sensor 59 temperature
DataPoint_Sensor_60=Temperature sensor 60 temperature
DataPoint_Sensor_61=Temperature sensor 61 temperature
DataPoint_Sensor_62=Temperature sensor 62 temperature
DataPoint_Sensor_63=Temperature sensor 63 temperature
DataPoint_Sensor_64=Temperature sensor 64 temperature
DataPoint_Sensor_65=Temperature sensor 65 temperature
DataPoint_Sensor_66=Temperature sensor 66 temperature
DataPoint_Sensor_67=Temperature sensor 67 temperature
DataPoint_Sensor_68=Temperature sensor 68 temperature
DataPoint_Sensor_69=Temperature sensor 69 temperature
DataPoint_Sensor_70=Temperature sensor 70 temperature
DataPoint_Sensor_71=Temperature sensor 71 temperature
DataPoint_Sensor_72=Temperature sensor 72 temperature
DataPoint_Sensor_73=Temperature sensor 73 temperature
DataPoint_Sensor_74=Temperature sensor 74 temperature
DataPoint_Sensor_75=Temperature sensor 75 temperature
DataPoint_Sensor_76=Temperature sensor 76 temperature
DataPoint_Sensor_77=Temperature sensor 77 temperature
DataPoint_Sensor_78=Temperature sensor 78 temperature
DataPoint_Sensor_79=Temperature sensor 79 temperature
DataPoint_Sensor_80=Temperature sensor 80 temperature
DataPoint_Sensor_81=Temperature sensor 81 temperature
DataPoint_Sensor_82=Temperature sensor 82 temperature
DataPoint_Sensor_83=Temperature sensor 83 temperature
DataPoint_Sensor_84=Temperature sensor 84 temperature
DataPoint_Sensor_85=Temperature sensor 85 temperature
DataPoint_Sensor_86=Temperature sensor 86 temperature
DataPoint_Sensor_87=Temperature sensor 87 temperature
DataPoint_Sensor_88=Temperature sensor 88 temperature
DataPoint_Sensor_89=Temperature sensor 89 temperature
DataPoint_Sensor_90=Temperature sensor 90 temperature
DataPoint_Sensor_91=Temperature sensor 91 temperature
DataPoint_Sensor_92=Temperature sensor 92 temperature
DataPoint_Sensor_93=Temperature sensor 93 temperature
DataPoint_Sensor_94=Temperature sensor 94 temperature
DataPoint_Sensor_95=Temperature sensor 95 temperature
DataPoint_Sensor_96=Temperature sensor 96 temperature
DataPoint_Sensor_97=Temperature sensor 97 temperature
DataPoint_Sensor_98=Temperature sensor 98 temperature
DataPoint_Sensor_99=Temperature sensor 99 temperature
DataPoint_Sensor_100=Temperature sensor 100 temperature
DataPoint_Sensor_1_connect=Temperature sensor 1 connection status
DataPoint_Sensor_2_connect=Temperature sensor 2 connection status
DataPoint_Sensor_3_connect=Temperature sensor 3 connection status
DataPoint_Sensor_4_connect=Temperature sensor 4 connection status
DataPoint_Sensor_5_connect=Temperature sensor 5 connection status
DataPoint_Sensor_6_connect=Temperature sensor 6 connection status
DataPoint_Sensor_7_connect=Temperature sensor 7 connection status
DataPoint_Sensor_8_connect=Temperature sensor 8 connection status
DataPoint_Sensor_9_connect=Temperature sensor 9 connection status
DataPoint_Sensor_10_connect=Temperature sensor 10 connection status
DataPoint_Sensor_11_connect=Temperature sensor 11 connection status
DataPoint_Sensor_12_connect=Temperature sensor 12 connection status
DataPoint_Sensor_13_connect=Temperature sensor 13 connection status
DataPoint_Sensor_14_connect=Temperature sensor 14 connection status
DataPoint_Sensor_15_connect=Temperature sensor 15 connection status
DataPoint_Sensor_16_connect=Temperature sensor 16 connection status
DataPoint_Sensor_17_connect=Temperature sensor 17 connection status
DataPoint_Sensor_18_connect=Temperature sensor 18 connection status
DataPoint_Sensor_19_connect=Temperature sensor 19 connection status
DataPoint_Sensor_20_connect=Temperature sensor 20 connection status
DataPoint_Sensor_21_connect=Temperature sensor 21 connection status
DataPoint_Sensor_22_connect=Temperature sensor 22 connection status
DataPoint_Sensor_23_connect=Temperature sensor 23 connection status
DataPoint_Sensor_24_connect=Temperature sensor 24 connection status
DataPoint_Sensor_25_connect=Temperature sensor 25 connection status
DataPoint_Sensor_26_connect=Temperature sensor 26 connection status
DataPoint_Sensor_27_connect=Temperature sensor 27 connection status
DataPoint_Sensor_28_connect=Temperature sensor 28 connection status
DataPoint_Sensor_29_connect=Temperature sensor 29 connection status
DataPoint_Sensor_30_connect=Temperature sensor 30 connection status
DataPoint_Sensor_31_connect=Temperature sensor 31 connection status
DataPoint_Sensor_32_connect=Temperature sensor 32 connection status
DataPoint_Sensor_33_connect=Temperature sensor 33 connection status
DataPoint_Sensor_34_connect=Temperature sensor 34 connection status
DataPoint_Sensor_35_connect=Temperature sensor 35 connection status
DataPoint_Sensor_36_connect=Temperature sensor 36 connection status
DataPoint_Sensor_37_connect=Temperature sensor 37 connection status
DataPoint_Sensor_38_connect=Temperature sensor 38 connection status
DataPoint_Sensor_39_connect=Temperature sensor 39 connection status
DataPoint_Sensor_40_connect=Temperature sensor 40 connection status
DataPoint_Sensor_41_connect=Temperature sensor 41 connection status
DataPoint_Sensor_42_connect=Temperature sensor 42 connection status
DataPoint_Sensor_43_connect=Temperature sensor 43 connection status
DataPoint_Sensor_44_connect=Temperature sensor 44 connection status
DataPoint_Sensor_45_connect=Temperature sensor 45 connection status
DataPoint_Sensor_46_connect=Temperature sensor 46 connection status
DataPoint_Sensor_47_connect=Temperature sensor 47 connection status
DataPoint_Sensor_48_connect=Temperature sensor 48 connection status
DataPoint_Sensor_49_connect=Temperature sensor 49 connection status
DataPoint_Sensor_50_connect=Temperature sensor 50 connection status
DataPoint_Sensor_51_connect=Temperature sensor 51 connection status
DataPoint_Sensor_52_connect=Temperature sensor 52 connection status
DataPoint_Sensor_53_connect=Temperature sensor 53 connection status
DataPoint_Sensor_54_connect=Temperature sensor 54 connection status
DataPoint_Sensor_55_connect=Temperature sensor 55 connection status
DataPoint_Sensor_56_connect=Temperature sensor 56 connection status
DataPoint_Sensor_57_connect=Temperature sensor 57 connection status
DataPoint_Sensor_58_connect=Temperature sensor 58 connection status
DataPoint_Sensor_59_connect=Temperature sensor 59 connection status
DataPoint_Sensor_60_connect=Temperature sensor 60 connection status
DataPoint_Sensor_61_connect=Temperature sensor 61 connection status
DataPoint_Sensor_62_connect=Temperature sensor 62 connection status
DataPoint_Sensor_63_connect=Temperature sensor 63 connection status
DataPoint_Sensor_64_connect=Temperature sensor 64 connection status
DataPoint_Sensor_65_connect=Temperature sensor 65 connection status
DataPoint_Sensor_66_connect=Temperature sensor 66 connection status
DataPoint_Sensor_67_connect=Temperature sensor 67 connection status
DataPoint_Sensor_68_connect=Temperature sensor 68 connection status
DataPoint_Sensor_69_connect=Temperature sensor 69 connection status
DataPoint_Sensor_70_connect=Temperature sensor 70 connection status
DataPoint_Sensor_71_connect=Temperature sensor 71 connection status
DataPoint_Sensor_72_connect=Temperature sensor 72 connection status
DataPoint_Sensor_73_connect=Temperature sensor 73 connection status
DataPoint_Sensor_74_connect=Temperature sensor 74 connection status
DataPoint_Sensor_75_connect=Temperature sensor 75 connection status
DataPoint_Sensor_76_connect=Temperature sensor 76 connection status
DataPoint_Sensor_77_connect=Temperature sensor 77 connection status
DataPoint_Sensor_78_connect=Temperature sensor 78 connection status
DataPoint_Sensor_79_connect=Temperature sensor 79 connection status
DataPoint_Sensor_80_connect=Temperature sensor 80 connection status
DataPoint_Sensor_81_connect=Temperature sensor 81 connection status
DataPoint_Sensor_82_connect=Temperature sensor 82 connection status
DataPoint_Sensor_83_connect=Temperature sensor 83 connection status
DataPoint_Sensor_84_connect=Temperature sensor 84 connection status
DataPoint_Sensor_85_connect=Temperature sensor 85 connection status
DataPoint_Sensor_86_connect=Temperature sensor 86 connection status
DataPoint_Sensor_87_connect=Temperature sensor 87 connection status
DataPoint_Sensor_88_connect=Temperature sensor 88 connection status
DataPoint_Sensor_89_connect=Temperature sensor 89 connection status
DataPoint_Sensor_90_connect=Temperature sensor 90 connection status
DataPoint_Sensor_91_connect=Temperature sensor 91 connection status
DataPoint_Sensor_92_connect=Temperature sensor 92 connection status
DataPoint_Sensor_93_connect=Temperature sensor 93 connection status
DataPoint_Sensor_94_connect=Temperature sensor 94 connection status
DataPoint_Sensor_95_connect=Temperature sensor 95 connection status
DataPoint_Sensor_96_connect=Temperature sensor 96 connection status
DataPoint_Sensor_97_connect=Temperature sensor 97 connection status
DataPoint_Sensor_98_connect=Temperature sensor 98 connection status
DataPoint_Sensor_99_connect=Temperature sensor 99 connection status
DataPoint_Sensor_100_connect=Temperature sensor 100 connection status
DataPoint_Sensor_1_offline_time=Temperature sensor 1 offline time
DataPoint_Sensor_2_offline_time=Temperature sensor 2 offline time
DataPoint_Sensor_3_offline_time=Temperature sensor 3 offline time
DataPoint_Sensor_4_offline_time=Temperature sensor 4 offline time
DataPoint_Sensor_5_offline_time=Temperature sensor 5 offline time
DataPoint_Sensor_6_offline_time=Temperature sensor 6 offline time
DataPoint_Sensor_7_offline_time=Temperature sensor 7 offline time
DataPoint_Sensor_8_offline_time=Temperature sensor 8 offline time
DataPoint_Sensor_9_offline_time=Temperature sensor 9 offline time
DataPoint_Sensor_10_offline_time=Temperature sensor 10 offline time
DataPoint_Sensor_11_offline_time=Temperature sensor 11 offline time
DataPoint_Sensor_12_offline_time=Temperature sensor 12 offline time
DataPoint_Sensor_13_offline_time=Temperature sensor 13 offline time
DataPoint_Sensor_14_offline_time=Temperature sensor 14 offline time
DataPoint_Sensor_15_offline_time=Temperature sensor 15 offline time
DataPoint_Sensor_16_offline_time=Temperature sensor 16 offline time
DataPoint_Sensor_17_offline_time=Temperature sensor 17 offline time
DataPoint_Sensor_18_offline_time=Temperature sensor 18 offline time
DataPoint_Sensor_19_offline_time=Temperature sensor 19 offline time
DataPoint_Sensor_20_offline_time=Temperature sensor 20 offline time
DataPoint_Sensor_21_offline_time=Temperature sensor 21 offline time
DataPoint_Sensor_22_offline_time=Temperature sensor 22 offline time
DataPoint_Sensor_23_offline_time=Temperature sensor 23 offline time
DataPoint_Sensor_24_offline_time=Temperature sensor 24 offline time
DataPoint_Sensor_25_offline_time=Temperature sensor 25 offline time
DataPoint_Sensor_26_offline_time=Temperature sensor 26 offline time
DataPoint_Sensor_27_offline_time=Temperature sensor 27 offline time
DataPoint_Sensor_28_offline_time=Temperature sensor 28 offline time
DataPoint_Sensor_29_offline_time=Temperature sensor 29 offline time
DataPoint_Sensor_30_offline_time=Temperature sensor 30 offline time
DataPoint_Sensor_31_offline_time=Temperature sensor 31 offline time
DataPoint_Sensor_32_offline_time=Temperature sensor 32 offline time
DataPoint_Sensor_33_offline_time=Temperature sensor 33 offline time
DataPoint_Sensor_34_offline_time=Temperature sensor 34 offline time
DataPoint_Sensor_35_offline_time=Temperature sensor 35 offline time
DataPoint_Sensor_36_offline_time=Temperature sensor 36 offline time
DataPoint_Sensor_37_offline_time=Temperature sensor 37 offline time
DataPoint_Sensor_38_offline_time=Temperature sensor 38 offline time
DataPoint_Sensor_39_offline_time=Temperature sensor 39 offline time
DataPoint_Sensor_40_offline_time=Temperature sensor 40 offline time
DataPoint_Sensor_41_offline_time=Temperature sensor 41 offline time
DataPoint_Sensor_42_offline_time=Temperature sensor 42 offline time
DataPoint_Sensor_43_offline_time=Temperature sensor 43 offline time
DataPoint_Sensor_44_offline_time=Temperature sensor 44 offline time
DataPoint_Sensor_45_offline_time=Temperature sensor 45 offline time
DataPoint_Sensor_46_offline_time=Temperature sensor 46 offline time
DataPoint_Sensor_47_offline_time=Temperature sensor 47 offline time
DataPoint_Sensor_48_offline_time=Temperature sensor 48 offline time
DataPoint_Sensor_49_offline_time=Temperature sensor 49 offline time
DataPoint_Sensor_50_offline_time=Temperature sensor 50 offline time
DataPoint_Sensor_51_offline_time=Temperature sensor 51 offline time
DataPoint_Sensor_52_offline_time=Temperature sensor 52 offline time
DataPoint_Sensor_53_offline_time=Temperature sensor 53 offline time
DataPoint_Sensor_54_offline_time=Temperature sensor 54 offline time
DataPoint_Sensor_55_offline_time=Temperature sensor 55 offline time
DataPoint_Sensor_56_offline_time=Temperature sensor 56 offline time
DataPoint_Sensor_57_offline_time=Temperature sensor 57 offline time
DataPoint_Sensor_58_offline_time=Temperature sensor 58 offline time
DataPoint_Sensor_59_offline_time=Temperature sensor 59 offline time
DataPoint_Sensor_60_offline_time=Temperature sensor 60 offline time
DataPoint_Sensor_61_offline_time=Temperature sensor 61 offline time
DataPoint_Sensor_62_offline_time=Temperature sensor 62 offline time
DataPoint_Sensor_63_offline_time=Temperature sensor 63 offline time
DataPoint_Sensor_64_offline_time=Temperature sensor 64 offline time
DataPoint_Sensor_65_offline_time=Temperature sensor 65 offline time
DataPoint_Sensor_66_offline_time=Temperature sensor 66 offline time
DataPoint_Sensor_67_offline_time=Temperature sensor 67 offline time
DataPoint_Sensor_68_offline_time=Temperature sensor 68 offline time
DataPoint_Sensor_69_offline_time=Temperature sensor 69 offline time
DataPoint_Sensor_70_offline_time=Temperature sensor 70 offline time
DataPoint_Sensor_71_offline_time=Temperature sensor 71 offline time
DataPoint_Sensor_72_offline_time=Temperature sensor 72 offline time
DataPoint_Sensor_73_offline_time=Temperature sensor 73 offline time
DataPoint_Sensor_74_offline_time=Temperature sensor 74 offline time
DataPoint_Sensor_75_offline_time=Temperature sensor 75 offline time
DataPoint_Sensor_76_offline_time=Temperature sensor 76 offline time
DataPoint_Sensor_77_offline_time=Temperature sensor 77 offline time
DataPoint_Sensor_78_offline_time=Temperature sensor 78 offline time
DataPoint_Sensor_79_offline_time=Temperature sensor 79 offline time
DataPoint_Sensor_80_offline_time=Temperature sensor 80 offline time
DataPoint_Sensor_81_offline_time=Temperature sensor 81 offline time
DataPoint_Sensor_82_offline_time=Temperature sensor 82 offline time
DataPoint_Sensor_83_offline_time=Temperature sensor 83 offline time
DataPoint_Sensor_84_offline_time=Temperature sensor 84 offline time
DataPoint_Sensor_85_offline_time=Temperature sensor 85 offline time
DataPoint_Sensor_86_offline_time=Temperature sensor 86 offline time
DataPoint_Sensor_87_offline_time=Temperature sensor 87 offline time
DataPoint_Sensor_88_offline_time=Temperature sensor 88 offline time
DataPoint_Sensor_89_offline_time=Temperature sensor 89 offline time
DataPoint_Sensor_90_offline_time=Temperature sensor 90 offline time
DataPoint_Sensor_91_offline_time=Temperature sensor 91 offline time
DataPoint_Sensor_92_offline_time=Temperature sensor 92 offline time
DataPoint_Sensor_93_offline_time=Temperature sensor 93 offline time
DataPoint_Sensor_94_offline_time=Temperature sensor 94 offline time
DataPoint_Sensor_95_offline_time=Temperature sensor 95 offline time
DataPoint_Sensor_96_offline_time=Temperature sensor 96 offline time
DataPoint_Sensor_97_offline_time=Temperature sensor 97 offline time
DataPoint_Sensor_98_offline_time=Temperature sensor 98 offline time
DataPoint_Sensor_99_offline_time=Temperature sensor 99 offline time
DataPoint_Sensor_100_offline_time=Temperature sensor 100 offline time
DataPoint_In=The current of phase N
DataPoint_DO_0.0=Digital output 0.0
DataPoint_DO_0.1=Digital output 0.1
DataPoint_DO_4.0=Digital output 4.0
DataPoint_DO_4.1=Digital output 4.1
DataPoint_DO_8.0=Digital output 8.0
DataPoint_DO_8.1=Digital output 8.1
DataPoint_DI_0.0=Digital input 0.0
DataPoint_DI_0.1=Digital input 0.1
DataPoint_DI_4.0=Digital input 4.0
DataPoint_DI_4.1=Digital input 4.1
DataPoint_DI_4.2=Digital input 4.2
DataPoint_DI_4.3=Digital input 4.3
DataPoint_DI_8.0=Digital input 8.0
DataPoint_DI_8.1=Digital input 8.1
DataPoint_DI_8.2=Digital input 8.2
DataPoint_DI_8.3=Digital input 8.3
DataPoint_LimitMonitoring_0=Limit monitoring 0
DataPoint_LimitMonitoring_1=Limit monitoring 1
DataPoint_LimitMonitoring_2=Limit monitoring 2
DataPoint_LimitMonitoring_3=Limit monitoring 3
DataPoint_LimitMonitoring_4=Limit monitoring 4
DataPoint_LimitMonitoring_5=Limit monitoring 5
DataPoint_LimitMonitoring_6=Limit monitoring 6
DataPoint_LimitMonitoring_7=Limit monitoring 7
DataPoint_LimitMonitoring_8=Limit monitoring 8
DataPoint_LimitMonitoring_9=Limit monitoring 9
DataPoint_LimitMonitoring_10=Limit monitoring 10
DataPoint_LimitMonitoring_11=Limit monitoring 11
DataPoint_LogicResult=Logic combination result
DataPoint_LogicFunction_1=Output logic function block 1
DataPoint_LogicFunction_2=Output logic function block 2
DataPoint_LogicFunction_3=Output logic function block 3
DataPoint_LogicFunction_4=Output logic function block 4
DataPoint_ActualTariff=Actual tariff
DataPoint_Slot_1=Expansion module slot 1
DataPoint_Slot_2=Expansion module slot 2
DataPoint_UseVoltageTransformer=Voltage transformer
DataPoint_PrimaryVoltage=Primary voltage
DataPoint_SecondaryVoltage=Secondary voltage
DataPoint_PrimaryCurrent=Primary current
DataPoint_SecondaryCurrent=Secondary current
DataPoint_HarwareWriteProtectionStatus=Write protection
DataPoint_ActiveEnergy=Active energy
DataPoint_ReactiveEnergy=Reactive energy
DataPoint_AConnectPoint_1_1=Phase A horizontal and vertical connection points #1_1
DataPoint_BConnectPoint_1_1=Phase B horizontal and vertical connection points #1_1
DataPoint_CConnectPoint_1_1=Phase C horizontal and vertical connection points #1_1
DataPoint_NConnectPoint_1_1=Phase N horizontal and vertical connection points #1_1
DataPoint_AConnectPoint_1_2=Phase A horizontal and vertical connection points #1_2
DataPoint_BConnectPoint_1_2=Phase B horizontal and vertical connection points #1_2
DataPoint_CConnectPoint_1_2=Phase C horizontal and vertical connection points #1_2
DataPoint_NConnectPoint_1_2=Phase N horizontal and vertical connection points #1_2
DataPoint_AConnectPoint_1_3=Phase A horizontal and vertical connection points #1_3
DataPoint_BConnectPoint_1_3=Phase B horizontal and vertical connection points #1_3
DataPoint_CConnectPoint_1_3=Phase C horizontal and vertical connection points #1_3
DataPoint_NConnectPoint_1_3=Phase N horizontal and vertical connection points #1_3
DataPoint_AConnectPoint_1_4=Phase A horizontal and vertical connection points #1_4
DataPoint_BConnectPoint_1_4=Phase B horizontal and vertical connection points #1_4
DataPoint_CConnectPoint_1_4=Phase C horizontal and vertical connection points #1_4
DataPoint_NConnectPoint_1_4=Phase N horizontal and vertical connection points #1_4
DataPoint_AConnectPoint_2_1=Phase A horizontal and vertical connection points #2_1
DataPoint_BConnectPoint_2_1=Phase B horizontal and vertical connection points #2_1
DataPoint_CConnectPoint_2_1=Phase C horizontal and vertical connection points #2_1
DataPoint_NConnectPoint_2_1=Phase N horizontal and vertical connection points #2_1
DataPoint_AConnectPoint_2_2=Phase A horizontal and vertical connection points #2_2
DataPoint_BConnectPoint_2_2=Phase B horizontal and vertical connection points #2_2
DataPoint_CConnectPoint_2_2=Phase C horizontal and vertical connection points #2_2
DataPoint_NConnectPoint_2_2=Phase N horizontal and vertical connection points #2_2
DataPoint_AConnectPoint_2_3=Phase A horizontal and vertical connection points #2_3
DataPoint_BConnectPoint_2_3=Phase B horizontal and vertical connection points #2_3
DataPoint_CConnectPoint_2_3=Phase C horizontal and vertical connection points #2_3
DataPoint_NConnectPoint_2_3=Phase N horizontal and vertical connection points #2_3
DataPoint_AConnectPoint_2_4=Phase A horizontal and vertical connection points #2_4
DataPoint_BConnectPoint_2_4=Phase B horizontal and vertical connection points #2_4
DataPoint_CConnectPoint_2_4=Phase C horizontal and vertical connection points #2_4
DataPoint_NConnectPoint_2_4=Phase N horizontal and vertical connection points #2_4
DataPoint_BreakerPosition=Position of circuit-breaker
DataPoint_TestPosition=Position of test
DataPoint_OperationPosition=Position of operation
DataPoint_HasTriped=Has triped
DataPoint_I_Agv=3 Phase Average Current
DataPoint_Pa=Active Power {L1}
DataPoint_Pb=Active Power {L2}
DataPoint_Pc=Active Power {L3}
DataPoint_Qa=Reactive Power {L1}
DataPoint_Qb=Reactive Power {L2}
DataPoint_Qc=Reactive Power {L3}
DataPoint_MLFB=MLFB
DataPoint_FirmwareRevision_1=Device firmware version 1
DataPoint_FirmwareRevision_2=Device firmware version 2
DataPoint_IdentNumber=Ident number
DataPoint_FirmwareRevision_3=Device firmware version 3
DataPoint_FirmwareRevision_4=Device firmware version 4
DataPoint_BreakerOrderNo=Breaker order No
DataPoint_LT_PHASE_LOSS_SENSITIV_ONOFF=Phase failure detection
DataPoint_PAL_ONOFF=PAL overload pre-alarm
DataPoint_PAL_IR=PAL current setting value I<sub>r PAL</sub>  (A)
DataPoint_PAL_TR=PAL delay time t<sub>r PAL</sub> (ms)
DataPoint_ST_I2t_ON_OFF=Characteristic ST curve
DataPoint_ST_I2t_ON_OFF_REMOTE=ST tripping ON/OFF for e.SET
DataPoint_ST_INTERMITTENT_ONOFF=ST intermittent detection
DataPoint_DST_ONOFF=dST tripping ON/OFF
DataPoint_DST_ISD_FW=dST current setting value I<sub>sd FW</sub> (A)
DataPoint_DST_ISD_REV=dST current setting value I<sub>sd REV</sub> (A)
DataPoint_DST_TSD_FW=dST tripping time t<sub>sd FW</sub> (ms)
DataPoint_DST_TSD_REV=dST tripping time t<sub>sd REV</sub> (ms)
DataPoint_INST_ONOFF_REMOTE=INST tripping ON/OFF for e.SET
DataPoint_INST_II_REMOTE=INST current setting value I<sub>i</sub> e.SET (A)
DataPoint_GF_PROTECTION_ONOFF=GF tripping ON/OFF
DataPoint_GF_STD_IG_RESIDUAL=GFs current setting value I<sub>g</sub> (A)
DataPoint_GF_STD_TG_RESIDUAL=GFs tripping time t<sub>g</sub>  (ms)
DataPoint_GF_STD_IG_DM_REF=GFs current setting value I<sub>g</sub> (A)
DataPoint_GF_STD_TG_DM_REF=GFs tripping time t<sub>g</sub>  (ms)
DataPoint_GF_STD_IG_DM_UREF=GFs current setting value I<sub>g</sub> (A)
DataPoint_GF_STD_TG_DM_UREF=GFs tripping time t<sub>g</sub>  (ms)
DataPoint_GF_STD_IG_HIZ_REF_SEC=GFs current setting value I<sub>g</sub> (mA)
DataPoint_GF_STD_TG_HIZ_REF=GFs tripping time t<sub>g</sub>  (ms)
DataPoint_GF_STD_IG_HIZ_UREF=GFs tripping time t<sub>g</sub>  (A)
DataPoint_GF_STD_TG_HIZ_UREF=GFs current setting value I<sub>g</sub> (ms)
DataPoint_GF_ALARM_IG_RESIDUAL=GF alarm current setting value I<sub>g alarm</sub> (A)
DataPoint_GF_ALARM_IG_DIRECT=GF alarm current setting value I<sub>g alarm</sub> (A)
DataPoint_GF_ALARM_IG_DM_UREF=GF alarm current setting value I<sub>g alarm</sub> (A)
DataPoint_GF_ALARM_IG_HIZ_UREF=GF alarm current setting value I<sub>g alarm</sub> (A)
DataPoint_GF_ALARM_TG=GFs alarm time t<sub>g</sub> alarm (ms)
DataPoint_RP_ONOFF=RP tripping ON/OFF
DataPoint_RP_PICKUP=RP setting value P<sub>RP</sub> (%)
DataPoint_RP_DELAY=RP tripping time t<sub>RP</sub> (ms)
DataPoint_SumI2t_A=Sum I²t L1
DataPoint_SumI2t_B=Sum I²t L2
DataPoint_SumI2t_C=Sum I²t L3
DataPoint_SumI2t_N=Sum I²t Ln
DataPoint_INSTTrips=Number of INST trips
DataPoint_SerialNumber=Serial number
DataPoint_ForwardActivePower_Tariff1=Total imported active energy tarff 1
DataPoint_ForwardReactivePower_Tariff1=Total imported reactive energy tarff 1
DataPoint_ReverseActivePower_Tariff1=Total exported active energy tarff 1
DataPoint_ReverseReactivePower_Tariff1=Total exported reactive energy tarff 1
DataPoint_ForwardActivePower_Tariff2=Total imported active energy tarff 2
DataPoint_ForwardReactivePower_Tariff2=Total imported reactive energy tarff 2
DataPoint_ReverseActivePower_Tariff2=Total exported active energy tarff 2
DataPoint_ReverseReactivePower_Tariff2=Total exported reactive energy tarff 2
DataPoint_AConnector_Left1=Phase A left side connector #1
DataPoint_BConnector_Left1=Phase B left side connector #1
DataPoint_CConnector_Left1=Phase C left side connector #1
DataPoint_NConnector_Left1=Phase N left side connector #1
DataPoint_AConnector_Right1=Phase A right side connector #1
DataPoint_BConnector_Right1=Phase B right side connector #1
DataPoint_CConnector_Right1=Phase C right side connector #1
DataPoint_NConnector_Right1=Phase N right side connector #1
DataPoint_AConnector_Left2=Phase A left side connector #2
DataPoint_BConnector_Left2=Phase B left side connector #2
DataPoint_CConnector_Left2=Phase C left side connector #2
DataPoint_NConnector_Left2=Phase N left side connector #2
DataPoint_AConnector_Right2=Phase A right side connector #2
DataPoint_BConnector_Right2=Phase B left side connector #2
DataPoint_CConnector_Right2=Phase C left side connector #2
DataPoint_NConnector_Right2=Phase N left side connector #2
DataPoint_SubstationTemp=Substation temperature
DataPoint_SubstationHumidness=Substation humidness
DataPoint_Harmonic_Ua_1=1th Harmonic Voltage
DataPoint_Harmonic_Ub_1=1th Harmonic Voltage
DataPoint_Harmonic_Uc_1=1th Harmonic Voltage
DataPoint_Harmonic_Ua_2=2th Harmonic Voltage
DataPoint_Harmonic_Ub_2=2th Harmonic Voltage
DataPoint_Harmonic_Uc_2=2th Harmonic Voltage
DataPoint_Harmonic_Ua_3=3th Harmonic Voltage
DataPoint_Harmonic_Ub_3=3th Harmonic Voltage
DataPoint_Harmonic_Uc_3=3th Harmonic Voltage
DataPoint_Harmonic_Ua_4=4th Harmonic Voltage
DataPoint_Harmonic_Ub_4=4th Harmonic Voltage
DataPoint_Harmonic_Uc_4=4th Harmonic Voltage
DataPoint_Harmonic_Ua_5=5th Harmonic Voltage
DataPoint_Harmonic_Ub_5=5th Harmonic Voltage
DataPoint_Harmonic_Uc_5=5th Harmonic Voltage
DataPoint_Harmonic_Ua_6=6th Harmonic Voltage
DataPoint_Harmonic_Ub_6=6th Harmonic Voltage
DataPoint_Harmonic_Uc_6=6th Harmonic Voltage
DataPoint_Harmonic_Ua_7=7th Harmonic Voltage
DataPoint_Harmonic_Ub_7=7th Harmonic Voltage
DataPoint_Harmonic_Uc_7=7th Harmonic Voltage
DataPoint_Harmonic_Ua_8=8th Harmonic Voltage
DataPoint_Harmonic_Ub_8=8th Harmonic Voltage
DataPoint_Harmonic_Uc_8=8th Harmonic Voltage
DataPoint_Harmonic_Ua_9=9th Harmonic Voltage
DataPoint_Harmonic_Ub_9=9th Harmonic Voltage
DataPoint_Harmonic_Uc_9=9th Harmonic Voltage
DataPoint_Harmonic_Ua_10=10th Harmonic Voltage
DataPoint_Harmonic_Ub_10=10th Harmonic Voltage
DataPoint_Harmonic_Uc_10=10th Harmonic Voltage
DataPoint_Harmonic_Ua_11=11th Harmonic Voltage
DataPoint_Harmonic_Ub_11=11th Harmonic Voltage
DataPoint_Harmonic_Uc_11=11th Harmonic Voltage
DataPoint_Harmonic_Ua_12=12th Harmonic Voltage
DataPoint_Harmonic_Ub_12=12th Harmonic Voltage
DataPoint_Harmonic_Uc_12=12th Harmonic Voltage
DataPoint_Harmonic_Ua_13=13th Harmonic Voltage
DataPoint_Harmonic_Ub_13=13th Harmonic Voltage
DataPoint_Harmonic_Uc_13=13th Harmonic Voltage
DataPoint_Harmonic_Ua_14=14th Harmonic Voltage
DataPoint_Harmonic_Ub_14=14th Harmonic Voltage
DataPoint_Harmonic_Uc_14=14th Harmonic Voltage
DataPoint_Harmonic_Ua_15=15th Harmonic Voltage
DataPoint_Harmonic_Ub_15=15th Harmonic Voltage
DataPoint_Harmonic_Uc_15=15th Harmonic Voltage
DataPoint_Harmonic_Ua_16=16th Harmonic Voltage
DataPoint_Harmonic_Ub_16=16th Harmonic Voltage
DataPoint_Harmonic_Uc_16=16th Harmonic Voltage
DataPoint_Harmonic_Ua_17=17th Harmonic Voltage
DataPoint_Harmonic_Ub_17=17th Harmonic Voltage
DataPoint_Harmonic_Uc_17=17th Harmonic Voltage
DataPoint_Harmonic_Ua_18=18th Harmonic Voltage
DataPoint_Harmonic_Ub_18=18th Harmonic Voltage
DataPoint_Harmonic_Uc_18=18th Harmonic Voltage
DataPoint_Harmonic_Ua_19=19th Harmonic Voltage
DataPoint_Harmonic_Ub_19=19th Harmonic Voltage
DataPoint_Harmonic_Uc_19=19th Harmonic Voltage
DataPoint_Harmonic_Ua_20=20th Harmonic Voltage
DataPoint_Harmonic_Ub_20=20th Harmonic Voltage
DataPoint_Harmonic_Uc_20=20th Harmonic Voltage
DataPoint_Harmonic_Ua_21=21th Harmonic Voltage
DataPoint_Harmonic_Ub_21=21th Harmonic Voltage
DataPoint_Harmonic_Uc_21=21th Harmonic Voltage
DataPoint_Harmonic_Ua_22=22th Harmonic Voltage
DataPoint_Harmonic_Ub_22=22th Harmonic Voltage
DataPoint_Harmonic_Uc_22=22th Harmonic Voltage
DataPoint_Harmonic_Ua_23=23th Harmonic Voltage
DataPoint_Harmonic_Ub_23=23th Harmonic Voltage
DataPoint_Harmonic_Uc_23=23th Harmonic Voltage
DataPoint_Harmonic_Ua_24=24th Harmonic Voltage
DataPoint_Harmonic_Ub_24=24th Harmonic Voltage
DataPoint_Harmonic_Uc_24=24th Harmonic Voltage
DataPoint_Harmonic_Ua_25=25th Harmonic Voltage
DataPoint_Harmonic_Ub_25=25th Harmonic Voltage
DataPoint_Harmonic_Uc_25=25th Harmonic Voltage
DataPoint_Harmonic_Ua_26=26th Harmonic Voltage
DataPoint_Harmonic_Ub_26=26th Harmonic Voltage
DataPoint_Harmonic_Uc_26=26th Harmonic Voltage
DataPoint_Harmonic_Ua_27=27th Harmonic Voltage
DataPoint_Harmonic_Ub_27=27th Harmonic Voltage
DataPoint_Harmonic_Uc_27=27th Harmonic Voltage
DataPoint_Harmonic_Ua_28=28th Harmonic Voltage
DataPoint_Harmonic_Ub_28=28th Harmonic Voltage
DataPoint_Harmonic_Uc_28=28th Harmonic Voltage
DataPoint_Harmonic_Ua_29=29th Harmonic Voltage
DataPoint_Harmonic_Ub_29=29th Harmonic Voltage
DataPoint_Harmonic_Uc_29=29th Harmonic Voltage
DataPoint_Harmonic_Ua_30=30th Harmonic Voltage
DataPoint_Harmonic_Ub_30=30th Harmonic Voltage
DataPoint_Harmonic_Uc_30=30th Harmonic Voltage
DataPoint_Harmonic_Ua_31=31th Harmonic Voltage
DataPoint_Harmonic_Ub_31=31th Harmonic Voltage
DataPoint_Harmonic_Uc_31=31th Harmonic Voltage
DataPoint_Harmonic_Ua_32=32th Harmonic Voltage
DataPoint_Harmonic_Ub_32=32th Harmonic Voltage
DataPoint_Harmonic_Uc_32=32th Harmonic Voltage
DataPoint_Harmonic_Ua_33=33th Harmonic Voltage
DataPoint_Harmonic_Ub_33=33th Harmonic Voltage
DataPoint_Harmonic_Uc_33=33th Harmonic Voltage
DataPoint_Harmonic_Ua_34=34th Harmonic Voltage
DataPoint_Harmonic_Ub_34=34th Harmonic Voltage
DataPoint_Harmonic_Uc_34=34th Harmonic Voltage
DataPoint_Harmonic_Ua_35=35th Harmonic Voltage
DataPoint_Harmonic_Ub_35=35th Harmonic Voltage
DataPoint_Harmonic_Uc_35=35th Harmonic Voltage
DataPoint_Harmonic_Ua_36=36th Harmonic Voltage
DataPoint_Harmonic_Ub_36=36th Harmonic Voltage
DataPoint_Harmonic_Uc_36=36th Harmonic Voltage
DataPoint_Harmonic_Ua_37=37th Harmonic Voltage
DataPoint_Harmonic_Ub_37=37th Harmonic Voltage
DataPoint_Harmonic_Uc_37=37th Harmonic Voltage
DataPoint_Harmonic_Ua_38=38th Harmonic Voltage
DataPoint_Harmonic_Ub_38=38th Harmonic Voltage
DataPoint_Harmonic_Uc_38=38th Harmonic Voltage
DataPoint_Harmonic_Ua_39=39th Harmonic Voltage
DataPoint_Harmonic_Ub_39=39th Harmonic Voltage
DataPoint_Harmonic_Uc_39=39th Harmonic Voltage
DataPoint_Harmonic_Ua_40=40th Harmonic Voltage
DataPoint_Harmonic_Ub_40=40th Harmonic Voltage
DataPoint_Harmonic_Uc_40=40th Harmonic Voltage
DataPoint_Harmonic_Ua_41=41th Harmonic Voltage
DataPoint_Harmonic_Ub_41=41th Harmonic Voltage
DataPoint_Harmonic_Uc_41=41th Harmonic Voltage
DataPoint_Harmonic_Ua_42=42th Harmonic Voltage
DataPoint_Harmonic_Ub_42=42th Harmonic Voltage
DataPoint_Harmonic_Uc_42=42th Harmonic Voltage
DataPoint_Harmonic_Ua_43=43th Harmonic Voltage
DataPoint_Harmonic_Ub_43=43th Harmonic Voltage
DataPoint_Harmonic_Uc_43=43th Harmonic Voltage
DataPoint_Harmonic_Ua_44=44th Harmonic Voltage
DataPoint_Harmonic_Ub_44=44th Harmonic Voltage
DataPoint_Harmonic_Uc_44=44th Harmonic Voltage
DataPoint_Harmonic_Ua_45=45th Harmonic Voltage
DataPoint_Harmonic_Ub_45=45th Harmonic Voltage
DataPoint_Harmonic_Uc_45=45th Harmonic Voltage
DataPoint_Harmonic_Ua_46=46th Harmonic Voltage
DataPoint_Harmonic_Ub_46=46th Harmonic Voltage
DataPoint_Harmonic_Uc_46=46th Harmonic Voltage
DataPoint_Harmonic_Ua_47=47th Harmonic Voltage
DataPoint_Harmonic_Ub_47=47th Harmonic Voltage
DataPoint_Harmonic_Uc_47=47th Harmonic Voltage
DataPoint_Harmonic_Ua_48=48th Harmonic Voltage
DataPoint_Harmonic_Ub_48=48th Harmonic Voltage
DataPoint_Harmonic_Uc_48=48th Harmonic Voltage
DataPoint_Harmonic_Ua_49=49th Harmonic Voltage
DataPoint_Harmonic_Ub_49=49th Harmonic Voltage
DataPoint_Harmonic_Uc_49=49th Harmonic Voltage
DataPoint_Harmonic_Ua_50=50th Harmonic Voltage
DataPoint_Harmonic_Ub_50=50th Harmonic Voltage
DataPoint_Harmonic_Uc_50=50th Harmonic Voltage
DataPoint_Harmonic_Ua_51=51th Harmonic Voltage
DataPoint_Harmonic_Ub_51=51th Harmonic Voltage
DataPoint_Harmonic_Uc_51=51th Harmonic Voltage
DataPoint_Harmonic_Ua_52=52th Harmonic Voltage
DataPoint_Harmonic_Ub_52=52th Harmonic Voltage
DataPoint_Harmonic_Uc_52=52th Harmonic Voltage
DataPoint_Harmonic_Ua_53=53th Harmonic Voltage
DataPoint_Harmonic_Ub_53=53th Harmonic Voltage
DataPoint_Harmonic_Uc_53=53th Harmonic Voltage
DataPoint_Harmonic_Ua_54=54th Harmonic Voltage
DataPoint_Harmonic_Ub_54=54th Harmonic Voltage
DataPoint_Harmonic_Uc_54=54th Harmonic Voltage
DataPoint_Harmonic_Ua_55=55th Harmonic Voltage
DataPoint_Harmonic_Ub_55=55th Harmonic Voltage
DataPoint_Harmonic_Uc_55=55th Harmonic Voltage
DataPoint_Harmonic_Ua_56=56th Harmonic Voltage
DataPoint_Harmonic_Ub_56=56th Harmonic Voltage
DataPoint_Harmonic_Uc_56=56th Harmonic Voltage
DataPoint_Harmonic_Ua_57=57th Harmonic Voltage
DataPoint_Harmonic_Ub_57=57th Harmonic Voltage
DataPoint_Harmonic_Uc_57=57th Harmonic Voltage
DataPoint_Harmonic_Ua_58=58th Harmonic Voltage
DataPoint_Harmonic_Ub_58=58th Harmonic Voltage
DataPoint_Harmonic_Uc_58=58th Harmonic Voltage
DataPoint_Harmonic_Ua_59=59th Harmonic Voltage
DataPoint_Harmonic_Ub_59=59th Harmonic Voltage
DataPoint_Harmonic_Uc_59=59th Harmonic Voltage
DataPoint_Harmonic_Ua_60=60th Harmonic Voltage
DataPoint_Harmonic_Ub_60=60th Harmonic Voltage
DataPoint_Harmonic_Uc_60=60th Harmonic Voltage
DataPoint_Harmonic_Ua_61=61th Harmonic Voltage
DataPoint_Harmonic_Ub_61=61th Harmonic Voltage
DataPoint_Harmonic_Uc_61=61th Harmonic Voltage
DataPoint_Harmonic_Ua_62=62th Harmonic Voltage
DataPoint_Harmonic_Ub_62=62th Harmonic Voltage
DataPoint_Harmonic_Uc_62=62th Harmonic Voltage
DataPoint_Harmonic_Ua_63=63th Harmonic Voltage
DataPoint_Harmonic_Ub_63=63th Harmonic Voltage
DataPoint_Harmonic_Uc_63=63th Harmonic Voltage
DataPoint_Harmonic_Ua_64=64th Harmonic Voltage
DataPoint_Harmonic_Ub_64=64th Harmonic Voltage
DataPoint_Harmonic_Uc_64=64th Harmonic Voltage
DataPoint_T=System time
DataPoint_Op=Operating hours counter
DataPoint_WriteP=Write protection
DataPoint_StateSet=Configuration is set
DataPoint_StateOk=Configuration is valid
DataPoint_Deserter=At least one known breaker is not found
DataPoint_Stowaway=At least one unknown breaker is connected
DataPoint_TooManyB=Too many breakers are connected
DataPoint_Slot1=Module slot 1
DataPoint_Boot=Bootloader active
DataPoint_SNTPSync=SNTP server synchronization telegram
DataPoint_DateInfo=Device date and time
DataPoint_Alarm_Severity=alarm level
DataPoint_Alarm_Status=Alarm status
DataPoint_Alarm_Path=Alarm Path
DataPoint_Alarm_Info=Alarm information
DataPoint_Alarm_Time=Alarm time
DataPoint_Msg_Severity=Message level
DataPoint_Msg_Status=Message status
DataPoint_Msg_Path=Message Path
DataPoint_Msg_Info=Message information
DataPoint_Msg_Time=Message time
DataPoint_Trip_Severity=Release level
DataPoint_Trip_Status=Release status
DataPoint_Trip_Path=Release path
DataPoint_Trip_Info=Release information
DataPoint_Trip_Time=Tripping time 
DataPoint_INST_II_ARC=Instantaneous protection (arc flash mode)
DataPoint_MP_TcTp_SELECTOR=Tc/TP selection
DataPoint_MP_Tc=Release level Tc
DataPoint_MP_Tp=Release time Tp
DataPoint_GF_IG_ARC=Grounding fault (arc flash mode)
DataPoint_GF_I2t_ON_OFF=I²t characteristic grounding fault on/off
DataPoint_GF_ALARM_IG=Ground fault alarm IgA
DataPoint_Capacity_Analysis=Capacity analysis time-series data
DataPoint_Load_Rate=Load rate time-series data
DataPoint_CircuitNextBreakerName=Name of lower level circuit breaker
DataPoint_CircuitNextBreakerStatus=Lower level circuit breaker alarm status
DataPoint_CircuitNextMeterName=Lower level meter name
DataPoint_CircuitNextMeterStatus=Lower level electricity meter alarm status
DataPoint_Health_Grade=Health level
DataPoint_Abnormal_Count=Number of abnormal indicators
DataPoint_Alarm_Status_Count=Statistics of the number of alarm messages
DataPoint_Max_Temperature=Maximum temperature measurement index value (℃)
DataPoint_Max_Temperature_Status=Maximum temperature state
DataPoint_Max_Electricity=Maximum current measurement index value (A)
DataPoint_Max_Electricity_Status=Maximum current state
DataPoint_Remaining_Life_Percentage=Remaining life index value of circuit breaker (%)
DataPoint_Remaining_Life_Status=Remaining life state of the short-circuit device
DataPoint_Panel_Custom_Indicators=Remaining life state of the short-circuit device
DataPoint_Total_ActiveEnergy=Same day delivery of electricity
DataPoint_SubStation_Loss=Time series data of power distribution room losses(KWh)
DataPoint_SubStation_Percentage=Time series data of power distribution room losses(%)
DataPoint_SubStation_Safety_Scope=Time series data of power distribution room losses(safe range)
DataPoint_SubStation_Day_Energy_ loss=Accumulated energy loss of the distribution room on the same day
DataPoint_SubStation_Month_Energy_ loss=Accumulated energy loss in the distribution room for the current month
DataPoint_SubStation_Year_Energy_ loss=Accumulated energy loss of the distribution room in the current year
DataPoint_SubStation_Day_Cost_ loss=Accumulated cost loss of the distribution room on the same day
DataPoint_SubStation_Month_Cost_ loss=Accumulated cost loss of the distribution room in the current month
DataPoint_SubStation_Year_Cost_ loss=Accumulated cost loss of the distribution room in the current year
DataPoint_SubStation_Day_Efficiency=The average efficiency of the distribution room system on the same day
DataPoint_SubStation_Month_Efficiency=The average efficiency of the distribution room system in the current month
DataPoint_SubStation_Year_Efficiency=The average efficiency of the distribution room system in the current year
DataPoint_RatedVoltage=Rated Voltage

DataPoint_TR_High_Ua=Phase A voltage on the high voltage side
DataPoint_TR_High_Ub=Phase B voltage on the high voltage side
DataPoint_TR_High_Uc=Phase C voltage on the high voltage side
DataPoint_TR_Low_Ua=Phase A voltage on the low voltage side
DataPoint_TR_Low_Ub=Phase B voltage on the low voltage side
DataPoint_TR_Low_Uc=Phase C voltage on the low voltage side
DataPoint_TR_High_Ia=High voltage side A-phase current
DataPoint_TR_High_Ib=High voltage side B-phase current
DataPoint_TR_High_Ic=High voltage side C-phase current
DataPoint_TR_Low_Ia=Low voltage side A phase current
DataPoint_TR_Low_Ib=Low voltage side B phase current
DataPoint_TR_Low_Ic=Low voltage side C phase current
DataPoint_temperature_tr_phaseA=Transformer phase A temperature
DataPoint_temperature_tr_phaseB=Transformer phase B temperature
DataPoint_temperature_tr_phaseC=Transformer phase C temperature

DataPoint_Cv=Current Supply voltage Dataserver
DataPoint_Sv=Current Supply voltage Com
DataPoint_MaxCv=Maximum Supply voltage Dataserver
DataPoint_MaxSv=Maximum Supply voltage Com
DataPoint_MinCv=Minimum Supply voltage Dataserver
DataPoint_MinSv=Minimum Supply voltage Com

Directory_Status=Status
Directory_State3VA-line=State 3VA-line
Directory_LocalDeviceStatus=Local Device Status
Directory_GlobalDeviceState=Global Device State

Directory_3VAState=3VA State
Directory_ProtectionFunctionState=Protection function state
Directory_MeteringFunctionState=Metering function state
Directory_AlarmState=Alarm state
Directory_TripCounters=Trip Counters
Directory_MaintenanceInformation=Maintenance Information
Directory_EFB_MMBState=EFB/MMB State
Directory_EFB_MMBThresholdActivationStates=EFB/MMB Threshold activation states
Directory_Current=Current
Directory_InstantaneousValues=Instantaneous Values
Directory_ActualInstantaneousMeasurementValues=Actual Instantaneous Measurement Values
Directory_ActivePower_InstantaneousValues=Active Power Instantaneous Values
Directory_ReactivePower_InstantaneousValues=Reactive Power Instantaneous Values
Directory_VoltageL-N=Voltage L-N
Directory_InstantaneousValuesL-N=Instantaneous Values L-N
Directory_VoltageL-L=Voltage L-L
Directory_InstantaneousValuesL-L=Instantaneous Values L-L
Directory_Power=Power
Directory_ActivePower=Active Power
Directory_ReactivePower=Reactive Power
Directory_MeasuringMethodVARtot=Measuring Method VARtot
Directory_ApparentPower=Apparent Power
Directory_PowerFactor=Power Factor
Directory_Energy=Energy
Directory_ActiveEnergy=Active Energy
Directory_Import=Import
Directory_Export=Export
Directory_ReactiveEnergy=Reactive Energy
Directory_ApparentEnergy=Apparent Energy
Directory_FrequencyValues=Frequency Values
Directory_InstantaneousValuesFrequency=Instantaneous Values Frequency
Directory_HarmonicDistortion=Harmonic Distortion
Directory_THDCurrent=THD Current
Directory_InstantaneousValuesTHDCurrent=Instantaneous Values THD Current
Directory_THDVoltageL-N=THD Voltage L-N
Directory_InstantaneousValuesTHDVoltageL-N=Instantaneous Values THD Voltage L-N
Directory_THDVoltageL-L=THD Voltage L-L
Directory_InstantaneousValuesTHDVoltageL-L=Instantaneous Values THD Voltage L-L
Directory_Temperatures=Temperatures
Directory_StatusValues=Status values
Directory_Das_EnableFrom=DAS+ enabled from
Directory_ParameterSetBActivatedBy=Parameter set B activated by
Directory_ETUStatus=ETU status
Directory_Warnings=Warnings
Directory_Alarms=Alarms
Directory_ETUErrors=ETU errors
Directory_System=System
Directory_RotarySwitchPositions=Rotary switch positions
Directory_AvailableModules=Available modules
Directory_Maintenance=Maintenance
Directory_Statistics=Statistics
Directory_Limits=Limits
Directory_StateDigitalInputs_outputs=State digital inputs/outputs
Directory_IOM230_1=IOM230 #1
Directory_IOM230_2=IOM230 #2
Directory_IOM230_3=IOM230 #3
Directory_IOM230_4=IOM230 #4
Directory_IOM230_5=IOM230 #5
Directory_IOM350_1=IOM350 #1
Directory_IOM350_2=IOM350 #2
Directory_IOM350_3=IOM350 #3
Directory_IOM350_4=IOM350 #4
Directory_IOM350_5=IOM350 #5
Directory_BSSModule=BSS module
Directory_ZSIModule=ZSI module
Directory_CommunicationModule=Communication modules
Directory_Test=Test
Directory_InstantaneousMeasuredValues=Instantaneous measured values
Directory_GreatestMeasuredValues=Greatest measured values
Directory_LowestMeasuredValues=Lowest measured values
Directory_MeasuringMethodVAR1=Measuring method VAR1
Directory_Cos=Cosφ
Directory_ThreePhaseSystem=Three-phase system
Directory_Unbalance=Unbalance
Directory_FormFactor=Form factor
Directory_CrestFactor=Crest factor
Directory_THDVoltage=THD voltage
Directory_Harmonic=Harmonic
Directory_HarmonicCurrent=Harmonic Current
Directory_DirectHarmonicCurrent=Direct Harmonic Current
Directory_HarmonicVoltageL-N=Harmonic Voltage L-N
Directory_DirectHarmonicVoltage=Direct Harmonic Voltage
Directory_HarmonicVoltage1=1st Harmonic Voltage
Directory_HarmonicVoltage2=2nd Harmonic Voltage
Directory_HarmonicVoltage3=3rd Harmonic Voltage
Directory_HarmonicVoltage4=4th Harmonic Voltage
Directory_HarmonicVoltage5=5th Harmonic Voltage
Directory_HarmonicVoltage6=6th Harmonic Voltage
Directory_HarmonicVoltage7=7th Harmonic Voltage
Directory_HarmonicVoltage8=8th Harmonic Voltage
Directory_HarmonicVoltage9=9th Harmonic Voltage
Directory_HarmonicVoltage10=10th Harmonic Voltage
Directory_HarmonicVoltage11=11th Harmonic Voltage
Directory_HarmonicVoltage12=12th Harmonic Voltage
Directory_HarmonicVoltage13=13th Harmonic Voltage
Directory_HarmonicVoltage14=14th Harmonic Voltage
Directory_HarmonicVoltage15=15th Harmonic Voltage
Directory_HarmonicVoltage16=16th Harmonic Voltage
Directory_HarmonicVoltage17=17th Harmonic Voltage
Directory_HarmonicVoltage18=18th Harmonic Voltage
Directory_HarmonicVoltage19=19th Harmonic Voltage
Directory_HarmonicVoltage20=20th Harmonic Voltage
Directory_HarmonicVoltage21=21st Harmonic Voltage
Directory_HarmonicVoltage22=22nd Harmonic Voltage
Directory_HarmonicVoltage23=23rd Harmonic Voltage
Directory_HarmonicVoltage24=24th Harmonic Voltage
Directory_HarmonicVoltage25=25th Harmonic Voltage
Directory_HarmonicVoltage26=26th Harmonic Voltage
Directory_HarmonicVoltage27=27th Harmonic Voltage
Directory_HarmonicVoltage28=28th Harmonic Voltage
Directory_HarmonicVoltage29=29th Harmonic Voltage
Directory_HarmonicVoltage30=30th Harmonic Voltage
Directory_HarmonicVoltage31=31st Harmonic Voltage
Directory_Diagnostic=Diagnostic
Directory_CircuitBreaker=Circuit breaker
Directory_Trips=Trips
Directory_LastUnacknowledgedTrippingOperationOfTheTripUnit=Last unacknowledged tripping operation of the trip unit
Directory_TrippingOperationsByMeteringFunction=Tripping operations by metering function
Directory_ThresholdWarnings=Threshold warnings
Directory_Statistics_Maintenance=Statistics / maintenance
Directory_TotalOfDeactivated=Total of deactivated I²t values L1, L2, L3, N
Directory_CommunicationBits=Communication bits
Directory_MeanValues=Mean values
Directory_ActualMeasurementValues=Actual Measurement Values
Directory_MaximumValuesInMeasuringInterval=Maximum values in measuring interval
Directory_MinimumValuesInMeasuringInterval=Minimum values in measuring interval
Directory_NormalDirection=Normal direction
Directory_ReverseDirection=Reverse direction
Directory_RMS=RMS
Directory_ActiveEnergy_Wh=Active Energy (Wh)
Directory_ReactiveEnergy_Varh=Reactive Energy (Varh)
Directory_GlobalDeviceDiagnostics=Global Device Diagnostics
Directory_GlobalDeviceStatus=Global Device Status
Directory_InputStatus=Input Status
Directory_OutputStatus=Output Status
Directory_AverageValues1=Average values 1
Directory_AverageValues2=Average values 2
Directory_InstantaneousValuesActivePower=Instantaneous Values Active Power
Directory_MeanValuesActivePower=Mean Values Active Power
Directory_MaximumValuesInMeasuringPeriod=Maximum Values in Measuring Period
Directory_MinimumValuesInMeasuringPeriod=Minimum Values in Measuring Period
Directory_CumulatedActivePower=Cumulated Active Power
Directory_CumulatedActivePowerImport=Cumulated Active Power Import
Directory_CumulatedActivePowerExport=Cumulated Active Power Export
Directory_MeasuringMethodQ1=Measuring Method Q1
Directory_MeanValuesReactivePower=Mean Values Reactive Power
Directory_CumulatedReactivePower=Cumulated Reactive Power
Directory_CumulatedReactivePowerImport=Cumulated Reactive Power Import
Directory_CumulatedReactivePowerExport=Cumulated Reactive Power Export
Directory_LoadManagement=Load Management
Directory_CommonDataPowerMeasurementOfTheLastPeriod=Common Data Power Measurement of the last Period
Directory_DisplacementPowerFactor=Displacement Power Factor
Directory_ActiveEnergyImportTariff1=Active Energy Import Tariff 1
Directory_ActiveEnergyImportTariff2=Active Energy Import Tariff 2
Directory_ActiveEnergyExportTariff1=Active Energy Export Tariff 1
Directory_ActiveEnergyExportTariff2=Active Energy Export Tariff 2
Directory_TotalReactiveEnergyImportTariff1=Total Reactive Energy Import Tariff 1
Directory_TotalReactiveEnergyImportTariff2=Total Reactive Energy Import Tariff 2
Directory_TotalReactiveEnergyExportTariff1=Total Reactive Energy Export Tariff 1
Directory_TotalReactiveEnergyExportTariff2=Total Reactive Energy Export Tariff 2
Directory_ApparentEnergyTariff1=Apparent Energy Tariff 1
Directory_ApparentEnergyTariff2=Apparent Energy Tariff 2
Directory_Non-resettableActiveEnergyCounter=Non-resettable Active Energy Counter
Directory_Counter=Counter
Directory_CostManagement=Cost Management
Directory_Device=Device
Directory_LimitMonitoring=Limit Monitoring
Directory_DateTime=Date/time
Directory_DeviceLED=DeviceLED
Directory_MeasuringMethodVARn=Measuring Method VARn
Directory_GlobalValues=Global Values
Directory_VoltageL-N_SlidingWindowDemand=Sliding Window Demand
Directory_VoltageL-L_SlidingWindowDemand=Sliding Window Demand L-L
Directory_SlidingWindowDemand=Sliding Window Demand
Directory_MeanValuesActivePowerImport=Mean Values Active Power Import
Directory_MeanValuesActivePowerExport=Mean Values Active Power Export
Directory_ActualMeasurementValuesActivePowerImport=Actual Measurement Values Active Power Import
Directory_ActualMeasurementValuesActivePowerExport=Actual Measurement Values Active Power Export
Directory_SlidingAverageOfInstantaneousValues=Sliding Average of Instantaneous Values
Directory_ActualMeasurementValuesReactivePowerExport=Actual Measurement Values Reactive Power Export
Directory_ActualMeasurementValuesReactivePowerImport=Actual Measurement Values Reactive Power Import
Directory_MeanValuesReactivePowerImport=Mean Values Reactive Power Import
Directory_MeanValuesReactivePowerExport=Mean Values Reactive Power Export
Directory_MeanValuesApparentPower=Mean Values Apparent Power
Directory_CumulatedApparentPower=Cumulated Apparent Power
Directory_ActualMeasurementValuesApparentPower=Actual Measurement Values Apparent Power
Directory_LoadManagementActivePower=Load Management Active Power
Directory_LoadManagementActivePowerExport=Load Management Active Power Export
Directory_LoadManagementActivePowerImport=Load Management Active Power Import
Directory_LoadManagementActivePowerMaximum=Load Management Active Power Maximum
Directory_LoadManagementActivePowerMinimum=Load Management Active Power Minimum
Directory_LoadManagementReactivePower=Load Management Reactive Power
Directory_LoadManagementReactivePowerExport=Load Management Reactive Power Export
Directory_LoadManagementReactivePowerImport=Load Management Reactive Power Import
Directory_LoadManagementReactivePowerMaximum=Load Management Reactive Power Maximum
Directory_LoadManagementReactivePowerMinimum=Load Management Reactive Power Minimum
Directory_Tariff1=Tariff 1
Directory_Tariff2=Tariff 2
Directory_PhaseShift=Phase Shift
Directory_PhaseAngle=Phase Angle
Directory_Counters=Counters
Directory_CurrentDistortion=Current Distortion
Directory_InstantaneousValuesCurrentDistortion=Instantaneous Values Current Distortion
Directory_HarmonicVoltageL-N1=1st Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N1=Instantaneous Values of the 1st Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N1=Actual Measured Values of the 1st Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N1=Greatest Measured Values of the 1st Harmonic Voltage L-N
Directory_HarmonicVoltageL-N2=2nd Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N2=Instantaneous Values of the 2nd Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N2=Actual Measured Values of the 2nd Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N2=Greatest Measured Values of the 2nd Harmonic Voltage L-N
Directory_HarmonicVoltageL-N3=3rd Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N3=Instantaneous Values of the 3rd Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N3=Actual Measured Values of the 3rd Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N3=Greatest Measured Values of the 3rd Harmonic Voltage L-N
Directory_HarmonicVoltageL-N4=4th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N4=Instantaneous Values of the 4th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N4=Actual Measured Values of the 4th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N4=Greatest Measured Values of the 4th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N5=5th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N5=Instantaneous Values of the 5th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N5=Actual Measured Values of the 5th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N5=Greatest Measured Values of the 5th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N6=6th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N6=Instantaneous Values of the 6th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N6=Actual Measured Values of the 6th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N6=Greatest Measured Values of the 6th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N7=7th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N7=Instantaneous Values of the 7th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N7=Actual Measured Values of the 7th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N7=Greatest Measured Values of the 7th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N8=8th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N8=Instantaneous Values of the 8th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N8=Actual Measured Values of the 8th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N8=Greatest Measured Values of the 8th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N9=9th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N9=Instantaneous Values of the 9th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N9=Actual Measured Values of the 9th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N9=Greatest Measured Values of the 9th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N10=10th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N10=Instantaneous Values of the 10th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N10=Actual Measured Values of the 10th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N10=Greatest Measured Values of the 10th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N11=11th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N11=Instantaneous Values of the 11th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N11=Actual Measured Values of the 11th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N11=Greatest Measured Values of the 11th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N12=12th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N12=Instantaneous Values of the 12th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N12=Actual Measured Values of the 12th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N12=Greatest Measured Values of the 12th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N13=13th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N13=Instantaneous Values of the 13th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N13=Actual Measured Values of the 13th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N13=Greatest Measured Values of the 13th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N14=14th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N14=Instantaneous Values of the 14th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N14=Actual Measured Values of the 14th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N14=Greatest Measured Values of the 14th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N15=15th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N15=Instantaneous Values of the 15th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N15=Actual Measured Values of the 15th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N15=Greatest Measured Values of the 15th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N16=16th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N16=Instantaneous Values of the 16th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N16=Actual Measured Values of the 16th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N16=Greatest Measured Values of the 16th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N17=17th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N17=Instantaneous Values of the 17th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N17=Actual Measured Values of the 17th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N17=Greatest Measured Values of the 17th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N18=18th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N18=Instantaneous Values of the 18th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N18=Actual Measured Values of the 18th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N18=Greatest Measured Values of the 18th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N19=19th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N19=Instantaneous Values of the 19th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N19=Actual Measured Values of the 19th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N19=Greatest Measured Values of the 19th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N20=20th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N20=Instantaneous Values of the 20th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N20=Actual Measured Values of the 20th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N20=Greatest Measured Values of the 20th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N21=21st Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N21=Instantaneous Values of the 21st Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N21=Actual Measured Values of the 21st Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N21=Greatest Measured Values of the 21st Harmonic Voltage L-N
Directory_HarmonicVoltageL-N22=22nd Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N22=Instantaneous Values of the 22nd Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N22=Actual Measured Values of the 22nd Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N22=Greatest Measured Values of the 22nd Harmonic Voltage L-N
Directory_HarmonicVoltageL-N23=23rd Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N23=Instantaneous Values of the 23rd Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N23=Actual Measured Values of the 23rd Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N23=Greatest Measured Values of the 23rd Harmonic Voltage L-N
Directory_HarmonicVoltageL-N24=24th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N24=Instantaneous Values of the 24th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N24=Actual Measured Values of the 24th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N24=Greatest Measured Values of the 24th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N25=25th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N25=Instantaneous Values of the 25th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N25=Actual Measured Values of the 25th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N25=Greatest Measured Values of the 25th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N26=26th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N26=Instantaneous Values of the 26th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N26=Actual Measured Values of the 26th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N26=Greatest Measured Values of the 26th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N27=27th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N27=Instantaneous Values of the 27th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N27=Actual Measured Values of the 27th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N27=Greatest Measured Values of the 27th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N28=28th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N28=Instantaneous Values of the 28th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N28=Actual Measured Values of the 28th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N28=Greatest Measured Values of the 28th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N29=29th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N29=Instantaneous Values of the 29th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N29=Actual Measured Values of the 29th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N29=Greatest Measured Values of the 29th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N30=30th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N30=Instantaneous Values of the 30th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N30=Actual Measured Values of the 30th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N30=Greatest Measured Values of the 30th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N31=31st Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N31=Instantaneous Values of the 31st Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N31=Actual Measured Values of the 31st Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N31=Greatest Measured Values of the 31st Harmonic Voltage L-N
Directory_HarmonicVoltageL-N32=32nd Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N32=Instantaneous Values of the 32nd Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N32=Actual Measured Values of the 32nd Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N32=Greatest Measured Values of the 32nd Harmonic Voltage L-N
Directory_HarmonicVoltageL-N33=33rd Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N33=Instantaneous Values of the 33rd Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N33=Actual Measured Values of the 33rd Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N33=Greatest Measured Values of the 33rd Harmonic Voltage L-N
Directory_HarmonicVoltageL-N34=34th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N34=Instantaneous Values of the 34th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N34=Actual Measured Values of the 34th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N34=Greatest Measured Values of the 34th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N35=35th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N35=Instantaneous Values of the 35th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N35=Actual Measured Values of the 35th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N35=Greatest Measured Values of the 35th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N36=36th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N36=Instantaneous Values of the 36th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N36=Actual Measured Values of the 36th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N36=Greatest Measured Values of the 36th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N37=37th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N37=Instantaneous Values of the 37th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N37=Actual Measured Values of the 37th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N37=Greatest Measured Values of the 37th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N38=38th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N38=Instantaneous Values of the 38th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N38=Actual Measured Values of the 38th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N38=Greatest Measured Values of the 38th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N39=39th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N39=Instantaneous Values of the 39th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N39=Actual Measured Values of the 39th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N39=Greatest Measured Values of the 39th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N40=40th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N40=Instantaneous Values of the 40th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N40=Actual Measured Values of the 40th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N40=Greatest Measured Values of the 40th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N41=41st Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N41=Instantaneous Values of the 41st Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N41=Actual Measured Values of the 41st Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N41=Greatest Measured Values of the 41st Harmonic Voltage L-N
Directory_HarmonicVoltageL-N42=42nd Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N42=Instantaneous Values of the 42nd Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N42=Actual Measured Values of the 42nd Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N42=Greatest Measured Values of the 42nd Harmonic Voltage L-N
Directory_HarmonicVoltageL-N43=43rd Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N43=Instantaneous Values of the 43rd Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N43=Actual Measured Values of the 43rd Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N43=Greatest Measured Values of the 43rd Harmonic Voltage L-N
Directory_HarmonicVoltageL-N44=44th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N44=Instantaneous Values of the 44th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N44=Actual Measured Values of the 44th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N44=Greatest Measured Values of the 44th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N45=45th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N45=Instantaneous Values of the 45th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N45=Actual Measured Values of the 45th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N45=Greatest Measured Values of the 45th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N46=46th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N46=Instantaneous Values of the 46th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N46=Actual Measured Values of the 46th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N46=Greatest Measured Values of the 46th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N47=47th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N47=Instantaneous Values of the 47th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N47=Actual Measured Values of the 47th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N47=Greatest Measured Values of the 47th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N48=48th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N48=Instantaneous Values of the 48th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N48=Actual Measured Values of the 48th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N48=Greatest Measured Values of the 48th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N49=49th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N49=Instantaneous Values of the 49th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N49=Actual Measured Values of the 49th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N49=Greatest Measured Values of the 49th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N50=50th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N50=Instantaneous Values of the 50th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N50=Actual Measured Values of the 50th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N50=Greatest Measured Values of the 50th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N51=51st Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N51=Instantaneous Values of the 51st Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N51=Actual Measured Values of the 51st Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N51=Greatest Measured Values of the 51st Harmonic Voltage L-N
Directory_HarmonicVoltageL-N52=52nd Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N52=Instantaneous Values of the 52nd Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N52=Actual Measured Values of the 52nd Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N52=Greatest Measured Values of the 52nd Harmonic Voltage L-N
Directory_HarmonicVoltageL-N53=53rd Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N53=Instantaneous Values of the 53rd Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N53=Actual Measured Values of the 53rd Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N53=Greatest Measured Values of the 53rd Harmonic Voltage L-N
Directory_HarmonicVoltageL-N54=54th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N54=Instantaneous Values of the 54th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N54=Actual Measured Values of the 54th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N54=Greatest Measured Values of the 54th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N55=55th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N55=Instantaneous Values of the 55th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N55=Actual Measured Values of the 55th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N55=Greatest Measured Values of the 55th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N56=56th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N56=Instantaneous Values of the 56th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N56=Actual Measured Values of the 56th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N56=Greatest Measured Values of the 56th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N57=57th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N57=Instantaneous Values of the 57th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N57=Actual Measured Values of the 57th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N57=Greatest Measured Values of the 57th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N58=58th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N58=Instantaneous Values of the 58th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N58=Actual Measured Values of the 58th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N58=Greatest Measured Values of the 58th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N59=59th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N59=Instantaneous Values of the 59th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N59=Actual Measured Values of the 59th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N59=Greatest Measured Values of the 59th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N60=60th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N60=Instantaneous Values of the 60th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N60=Actual Measured Values of the 60th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N60=Greatest Measured Values of the 60th Harmonic Voltage L-N
Directory_HarmonicVoltageL-N61=61st Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N61=Instantaneous Values of the 61st Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N61=Actual Measured Values of the 61st Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N61=Greatest Measured Values of the 61st Harmonic Voltage L-N
Directory_HarmonicVoltageL-N62=62nd Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N62=Instantaneous Values of the 62nd Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N62=Actual Measured Values of the 62nd Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N62=Greatest Measured Values of the 62nd Harmonic Voltage L-N
Directory_HarmonicVoltageL-N63=63rd Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N63=Instantaneous Values of the 63rd Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N63=Actual Measured Values of the 63rd Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N63=Greatest Measured Values of the 63rd Harmonic Voltage L-N
Directory_HarmonicVoltageL-N64=64th Harmonic Voltage L-N
Directory_InstantaneousValuesOfTheHarmonicVoltageL-N64=Instantaneous Values of the 64th Harmonic Voltage L-N
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-N64=Actual Measured Values of the 64th Harmonic Voltage L-N
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-N64=Greatest Measured Values of the 64th Harmonic Voltage L-N
Directory_HarmonicVoltageL-L=Harmonic Voltage L-L
Directory_HarmonicVoltageL-L1=1st Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L1=Instantaneous Values of the 1st Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L1=Actual Measured Values of the 1st Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L1=Greatest Measured Values of the 1st Harmonic Voltage L-L
Directory_HarmonicVoltageL-L2=2nd Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L2=Instantaneous Values of the 2nd Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L2=Actual Measured Values of the 2nd Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L2=Greatest Measured Values of the 2nd Harmonic Voltage L-L
Directory_HarmonicVoltageL-L3=3rd Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L3=Instantaneous Values of the 3rd Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L3=Actual Measured Values of the 3rd Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L3=Greatest Measured Values of the 3rd Harmonic Voltage L-L
Directory_HarmonicVoltageL-L4=4th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L4=Instantaneous Values of the 4th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L4=Actual Measured Values of the 4th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L4=Greatest Measured Values of the 4th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L5=5th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L5=Instantaneous Values of the 5th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L5=Actual Measured Values of the 5th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L5=Greatest Measured Values of the 5th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L6=6th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L6=Instantaneous Values of the 6th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L6=Actual Measured Values of the 6th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L6=Greatest Measured Values of the 6th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L7=7th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L7=Instantaneous Values of the 7th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L7=Actual Measured Values of the 7th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L7=Greatest Measured Values of the 7th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L8=8th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L8=Instantaneous Values of the 8th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L8=Actual Measured Values of the 8th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L8=Greatest Measured Values of the 8th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L9=9th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L9=Instantaneous Values of the 9th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L9=Actual Measured Values of the 9th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L9=Greatest Measured Values of the 9th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L10=10th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L10=Instantaneous Values of the 10th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L10=Actual Measured Values of the 10th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L10=Greatest Measured Values of the 10th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L11=11th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L11=Instantaneous Values of the 11th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L11=Actual Measured Values of the 11th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L11=Greatest Measured Values of the 11th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L12=12th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L12=Instantaneous Values of the 12th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L12=Actual Measured Values of the 12th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L12=Greatest Measured Values of the 12th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L13=13th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L13=Instantaneous Values of the 13th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L13=Actual Measured Values of the 13th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L13=Greatest Measured Values of the 13th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L14=14th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L14=Instantaneous Values of the 14th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L14=Actual Measured Values of the 14th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L14=Greatest Measured Values of the 14th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L15=15th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L15=Instantaneous Values of the 15th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L15=Actual Measured Values of the 15th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L15=Greatest Measured Values of the 15th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L16=16th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L16=Instantaneous Values of the 16th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L16=Actual Measured Values of the 16th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L16=Greatest Measured Values of the 16th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L17=17th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L17=Instantaneous Values of the 17th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L17=Actual Measured Values of the 17th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L17=Greatest Measured Values of the 17th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L18=18th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L18=Instantaneous Values of the 18th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L18=Actual Measured Values of the 18th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L18=Greatest Measured Values of the 18th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L19=19th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L19=Instantaneous Values of the 19th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L19=Actual Measured Values of the 19th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L19=Greatest Measured Values of the 19th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L20=20th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L20=Instantaneous Values of the 20th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L20=Actual Measured Values of the 20th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L20=Greatest Measured Values of the 20th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L21=21st Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L21=Instantaneous Values of the 21st Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L21=Actual Measured Values of the 21st Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L21=Greatest Measured Values of the 21st Harmonic Voltage L-L
Directory_HarmonicVoltageL-L22=22nd Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L22=Instantaneous Values of the 22nd Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L22=Actual Measured Values of the 22nd Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L22=Greatest Measured Values of the 22nd Harmonic Voltage L-L
Directory_HarmonicVoltageL-L23=23rd Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L23=Instantaneous Values of the 23rd Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L23=Actual Measured Values of the 23rd Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L23=Greatest Measured Values of the 23rd Harmonic Voltage L-L
Directory_HarmonicVoltageL-L24=24th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L24=Instantaneous Values of the 24th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L24=Actual Measured Values of the 24th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L24=Greatest Measured Values of the 24th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L25=25th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L25=Instantaneous Values of the 25th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L25=Actual Measured Values of the 25th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L25=Greatest Measured Values of the 25th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L26=26th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L26=Instantaneous Values of the 26th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L26=Actual Measured Values of the 26th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L26=Greatest Measured Values of the 26th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L27=27th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L27=Instantaneous Values of the 27th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L27=Actual Measured Values of the 27th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L27=Greatest Measured Values of the 27th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L28=28th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L28=Instantaneous Values of the 28th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L28=Actual Measured Values of the 28th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L28=Greatest Measured Values of the 28th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L29=29th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L29=Instantaneous Values of the 29th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L29=Actual Measured Values of the 29th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L29=Greatest Measured Values of the 29th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L30=30th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L30=Instantaneous Values of the 30th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L30=Actual Measured Values of the 30th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L30=Greatest Measured Values of the 30th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L31=31st Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L31=Instantaneous Values of the 31st Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L31=Actual Measured Values of the 31st Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L31=Greatest Measured Values of the 31st Harmonic Voltage L-L
Directory_HarmonicVoltageL-L32=32nd Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L32=Instantaneous Values of the 32nd Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L32=Actual Measured Values of the 32nd Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L32=Greatest Measured Values of the 32nd Harmonic Voltage L-L
Directory_HarmonicVoltageL-L33=33rd Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L33=Instantaneous Values of the 33rd Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L33=Actual Measured Values of the 33rd Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L33=Greatest Measured Values of the 33rd Harmonic Voltage L-L
Directory_HarmonicVoltageL-L34=34th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L34=Instantaneous Values of the 34th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L34=Actual Measured Values of the 34th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L34=Greatest Measured Values of the 34th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L35=35th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L35=Instantaneous Values of the 35th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L35=Actual Measured Values of the 35th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L35=Greatest Measured Values of the 35th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L36=36th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L36=Instantaneous Values of the 36th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L36=Actual Measured Values of the 36th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L36=Greatest Measured Values of the 36th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L37=37th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L37=Instantaneous Values of the 37th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L37=Actual Measured Values of the 37th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L37=Greatest Measured Values of the 37th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L38=38th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L38=Instantaneous Values of the 38th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L38=Actual Measured Values of the 38th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L38=Greatest Measured Values of the 38th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L39=39th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L39=Instantaneous Values of the 39th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L39=Actual Measured Values of the 39th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L39=Greatest Measured Values of the 39th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L40=40th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L40=Instantaneous Values of the 40th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L40=Actual Measured Values of the 40th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L40=Greatest Measured Values of the 40th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L41=41st Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L41=Instantaneous Values of the 41st Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L41=Actual Measured Values of the 41st Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L41=Greatest Measured Values of the 41st Harmonic Voltage L-L
Directory_HarmonicVoltageL-L42=42nd Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L42=Instantaneous Values of the 42nd Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L42=Actual Measured Values of the 42nd Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L42=Greatest Measured Values of the 42nd Harmonic Voltage L-L
Directory_HarmonicVoltageL-L43=43rd Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L43=Instantaneous Values of the 43rd Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L43=Actual Measured Values of the 43rd Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L43=Greatest Measured Values of the 43rd Harmonic Voltage L-L
Directory_HarmonicVoltageL-L44=44th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L44=Instantaneous Values of the 44th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L44=Actual Measured Values of the 44th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L44=Greatest Measured Values of the 44th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L45=45th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L45=Instantaneous Values of the 45th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L45=Actual Measured Values of the 45th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L45=Greatest Measured Values of the 45th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L46=46th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L46=Instantaneous Values of the 46th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L46=Actual Measured Values of the 46th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L46=Greatest Measured Values of the 46th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L47=47th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L47=Instantaneous Values of the 47th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L47=Actual Measured Values of the 47th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L47=Greatest Measured Values of the 47th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L48=48th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L48=Instantaneous Values of the 48th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L48=Actual Measured Values of the 48th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L48=Greatest Measured Values of the 48th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L49=49th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L49=Instantaneous Values of the 49th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L49=Actual Measured Values of the 49th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L49=Greatest Measured Values of the 49th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L50=50th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L50=Instantaneous Values of the 50th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L50=Actual Measured Values of the 50th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L50=Greatest Measured Values of the 50th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L51=51st Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L51=Instantaneous Values of the 51st Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L51=Actual Measured Values of the 51st Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L51=Greatest Measured Values of the 51st Harmonic Voltage L-L
Directory_HarmonicVoltageL-L52=52nd Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L52=Instantaneous Values of the 52nd Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L52=Actual Measured Values of the 52nd Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L52=Greatest Measured Values of the 52nd Harmonic Voltage L-L
Directory_HarmonicVoltageL-L53=53rd Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L53=Instantaneous Values of the 53rd Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L53=Actual Measured Values of the 53rd Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L53=Greatest Measured Values of the 53rd Harmonic Voltage L-L
Directory_HarmonicVoltageL-L54=54th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L54=Instantaneous Values of the 54th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L54=Actual Measured Values of the 54th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L54=Greatest Measured Values of the 54th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L55=55th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L55=Instantaneous Values of the 55th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L55=Actual Measured Values of the 55th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L55=Greatest Measured Values of the 55th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L56=56th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L56=Instantaneous Values of the 56th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L56=Actual Measured Values of the 56th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L56=Greatest Measured Values of the 56th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L57=57th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L57=Instantaneous Values of the 57th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L57=Actual Measured Values of the 57th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L57=Greatest Measured Values of the 57th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L58=58th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L58=Instantaneous Values of the 58th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L58=Actual Measured Values of the 58th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L58=Greatest Measured Values of the 58th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L59=59th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L59=Instantaneous Values of the 59th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L59=Actual Measured Values of the 59th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L59=Greatest Measured Values of the 59th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L60=60th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L60=Instantaneous Values of the 60th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L60=Actual Measured Values of the 60th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L60=Greatest Measured Values of the 60th Harmonic Voltage L-L
Directory_HarmonicVoltageL-L61=61st Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L61=Instantaneous Values of the 61st Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L61=Actual Measured Values of the 61st Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L61=Greatest Measured Values of the 61st Harmonic Voltage L-L
Directory_HarmonicVoltageL-L62=62nd Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L62=Instantaneous Values of the 62nd Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L62=Actual Measured Values of the 62nd Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L62=Greatest Measured Values of the 62nd Harmonic Voltage L-L
Directory_HarmonicVoltageL-L63=63rd Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L63=Instantaneous Values of the 63rd Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L63=Actual Measured Values of the 63rd Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L63=Greatest Measured Values of the 63rd Harmonic Voltage L-L
Directory_HarmonicVoltageL-L64=64th Harmonic Voltage L-L
Directory_InstantaneousValuesOfTheHarmonicVoltageL-L64=Instantaneous Values of the 64th Harmonic Voltage L-L
Directory_ActualMeasuredValuesOfTheHarmonicVoltageL-L64=Actual Measured Values of the 64th Harmonic Voltage L-L
Directory_GreatestMeasuredValuesOfTheHarmonicVoltageL-L64=Greatest Measured Values of the 64th Harmonic Voltage L-L
Directory_HarmonicCurrent1=1st Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent1=Instantaneous Values of the 1st Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent1=Actual Measured Values of the 1st Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent1=Greatest Measured Values of the 1st Harmonic Current
Directory_HarmonicCurrent2=2nd Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent2=Instantaneous Values of the 2nd Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent2=Actual Measured Values of the 2nd Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent2=Greatest Measured Values of the 2nd Harmonic Current
Directory_HarmonicCurrent3=3rd Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent3=Instantaneous Values of the 3rd Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent3=Actual Measured Values of the 3rd Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent3=Greatest Measured Values of the 3rd Harmonic Current
Directory_HarmonicCurrent4=4th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent4=Instantaneous Values of the 4th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent4=Actual Measured Values of the 4th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent4=Greatest Measured Values of the 4th Harmonic Current
Directory_HarmonicCurrent5=5th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent5=Instantaneous Values of the 5th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent5=Actual Measured Values of the 5th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent5=Greatest Measured Values of the 5th Harmonic Current
Directory_HarmonicCurrent6=6th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent6=Instantaneous Values of the 6th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent6=Actual Measured Values of the 6th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent6=Greatest Measured Values of the 6th Harmonic Current
Directory_HarmonicCurrent7=7th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent7=Instantaneous Values of the 7th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent7=Actual Measured Values of the 7th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent7=Greatest Measured Values of the 7th Harmonic Current
Directory_HarmonicCurrent8=8th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent8=Instantaneous Values of the 8th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent8=Actual Measured Values of the 8th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent8=Greatest Measured Values of the 8th Harmonic Current
Directory_HarmonicCurrent9=9th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent9=Instantaneous Values of the 9th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent9=Actual Measured Values of the 9th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent9=Greatest Measured Values of the 9th Harmonic Current
Directory_HarmonicCurrent10=10th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent10=Instantaneous Values of the 10th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent10=Actual Measured Values of the 10th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent10=Greatest Measured Values of the 10th Harmonic Current
Directory_HarmonicCurrent11=11th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent11=Instantaneous Values of the 11th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent11=Actual Measured Values of the 11th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent11=Greatest Measured Values of the 11th Harmonic Current
Directory_HarmonicCurrent12=12th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent12=Instantaneous Values of the 12th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent12=Actual Measured Values of the 12th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent12=Greatest Measured Values of the 12th Harmonic Current
Directory_HarmonicCurrent13=13th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent13=Instantaneous Values of the 13th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent13=Actual Measured Values of the 13th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent13=Greatest Measured Values of the 13th Harmonic Current
Directory_HarmonicCurrent14=14th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent14=Instantaneous Values of the 14th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent14=Actual Measured Values of the 14th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent14=Greatest Measured Values of the 14th Harmonic Current
Directory_HarmonicCurrent15=15th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent15=Instantaneous Values of the 15th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent15=Actual Measured Values of the 15th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent15=Greatest Measured Values of the 15th Harmonic Current
Directory_HarmonicCurrent16=16th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent16=Instantaneous Values of the 16th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent16=Actual Measured Values of the 16th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent16=Greatest Measured Values of the 16th Harmonic Current
Directory_HarmonicCurrent17=17th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent17=Instantaneous Values of the 17th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent17=Actual Measured Values of the 17th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent17=Greatest Measured Values of the 17th Harmonic Current
Directory_HarmonicCurrent18=18th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent18=Instantaneous Values of the 18th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent18=Actual Measured Values of the 18th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent18=Greatest Measured Values of the 18th Harmonic Current
Directory_HarmonicCurrent19=19th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent19=Instantaneous Values of the 19th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent19=Actual Measured Values of the 19th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent19=Greatest Measured Values of the 19th Harmonic Current
Directory_HarmonicCurrent20=20th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent20=Instantaneous Values of the 20th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent20=Actual Measured Values of the 20th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent20=Greatest Measured Values of the 20th Harmonic Current
Directory_HarmonicCurrent21=21st Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent21=Instantaneous Values of the 21st Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent21=Actual Measured Values of the 21st Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent21=Greatest Measured Values of the 21st Harmonic Current
Directory_HarmonicCurrent22=22nd Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent22=Instantaneous Values of the 22nd Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent22=Actual Measured Values of the 22nd Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent22=Greatest Measured Values of the 22nd Harmonic Current
Directory_HarmonicCurrent23=23rd Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent23=Instantaneous Values of the 23rd Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent23=Actual Measured Values of the 23rd Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent23=Greatest Measured Values of the 23rd Harmonic Current
Directory_HarmonicCurrent24=24th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent24=Instantaneous Values of the 24th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent24=Actual Measured Values of the 24th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent24=Greatest Measured Values of the 24th Harmonic Current
Directory_HarmonicCurrent25=25th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent25=Instantaneous Values of the 25th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent25=Actual Measured Values of the 25th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent25=Greatest Measured Values of the 25th Harmonic Current
Directory_HarmonicCurrent26=26th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent26=Instantaneous Values of the 26th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent26=Actual Measured Values of the 26th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent26=Greatest Measured Values of the 26th Harmonic Current
Directory_HarmonicCurrent27=27th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent27=Instantaneous Values of the 27th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent27=Actual Measured Values of the 27th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent27=Greatest Measured Values of the 27th Harmonic Current
Directory_HarmonicCurrent28=28th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent28=Instantaneous Values of the 28th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent28=Actual Measured Values of the 28th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent28=Greatest Measured Values of the 28th Harmonic Current
Directory_HarmonicCurrent29=29th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent29=Instantaneous Values of the 29th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent29=Actual Measured Values of the 29th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent29=Greatest Measured Values of the 29th Harmonic Current
Directory_HarmonicCurrent30=30th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent30=Instantaneous Values of the 30th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent30=Actual Measured Values of the 30th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent30=Greatest Measured Values of the 30th Harmonic Current
Directory_HarmonicCurrent31=31st Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent31=Instantaneous Values of the 31st Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent31=Actual Measured Values of the 31st Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent31=Greatest Measured Values of the 31st Harmonic Current
Directory_HarmonicCurrent32=32nd Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent32=Instantaneous Values of the 32nd Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent32=Actual Measured Values of the 32nd Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent32=Greatest Measured Values of the 32nd Harmonic Current
Directory_HarmonicCurrent33=33rd Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent33=Instantaneous Values of the 33rd Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent33=Actual Measured Values of the 33rd Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent33=Greatest Measured Values of the 33rd Harmonic Current
Directory_HarmonicCurrent34=34th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent34=Instantaneous Values of the 34th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent34=Actual Measured Values of the 34th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent34=Greatest Measured Values of the 34th Harmonic Current
Directory_HarmonicCurrent35=35th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent35=Instantaneous Values of the 35th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent35=Actual Measured Values of the 35th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent35=Greatest Measured Values of the 35th Harmonic Current
Directory_HarmonicCurrent36=36th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent36=Instantaneous Values of the 36th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent36=Actual Measured Values of the 36th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent36=Greatest Measured Values of the 36th Harmonic Current
Directory_HarmonicCurrent37=37th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent37=Instantaneous Values of the 37th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent37=Actual Measured Values of the 37th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent37=Greatest Measured Values of the 37th Harmonic Current
Directory_HarmonicCurrent38=38th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent38=Instantaneous Values of the 38th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent38=Actual Measured Values of the 38th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent38=Greatest Measured Values of the 38th Harmonic Current
Directory_HarmonicCurrent39=39th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent39=Instantaneous Values of the 39th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent39=Actual Measured Values of the 39th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent39=Greatest Measured Values of the 39th Harmonic Current
Directory_HarmonicCurrent40=40th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent40=Instantaneous Values of the 40th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent40=Actual Measured Values of the 40th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent40=Greatest Measured Values of the 40th Harmonic Current
Directory_HarmonicCurrent41=41st Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent41=Instantaneous Values of the 41st Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent41=Actual Measured Values of the 41st Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent41=Greatest Measured Values of the 41st Harmonic Current
Directory_HarmonicCurrent42=42nd Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent42=Instantaneous Values of the 42nd Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent42=Actual Measured Values of the 42nd Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent42=Greatest Measured Values of the 42nd Harmonic Current
Directory_HarmonicCurrent43=43rd Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent43=Instantaneous Values of the 43rd Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent43=Actual Measured Values of the 43rd Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent43=Greatest Measured Values of the 43rd Harmonic Current
Directory_HarmonicCurrent44=44th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent44=Instantaneous Values of the 44th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent44=Actual Measured Values of the 44th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent44=Greatest Measured Values of the 44th Harmonic Current
Directory_HarmonicCurrent45=45th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent45=Instantaneous Values of the 45th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent45=Actual Measured Values of the 45th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent45=Greatest Measured Values of the 45th Harmonic Current
Directory_HarmonicCurrent46=46th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent46=Instantaneous Values of the 46th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent46=Actual Measured Values of the 46th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent46=Greatest Measured Values of the 46th Harmonic Current
Directory_HarmonicCurrent47=47th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent47=Instantaneous Values of the 47th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent47=Actual Measured Values of the 47th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent47=Greatest Measured Values of the 47th Harmonic Current
Directory_HarmonicCurrent48=48th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent48=Instantaneous Values of the 48th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent48=Actual Measured Values of the 48th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent48=Greatest Measured Values of the 48th Harmonic Current
Directory_HarmonicCurrent49=49th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent49=Instantaneous Values of the 49th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent49=Actual Measured Values of the 49th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent49=Greatest Measured Values of the 49th Harmonic Current
Directory_HarmonicCurrent50=50th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent50=Instantaneous Values of the 50th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent50=Actual Measured Values of the 50th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent50=Greatest Measured Values of the 50th Harmonic Current
Directory_HarmonicCurrent51=51st Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent51=Instantaneous Values of the 51st Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent51=Actual Measured Values of the 51st Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent51=Greatest Measured Values of the 51st Harmonic Current
Directory_HarmonicCurrent52=52nd Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent52=Instantaneous Values of the 52nd Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent52=Actual Measured Values of the 52nd Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent52=Greatest Measured Values of the 52nd Harmonic Current
Directory_HarmonicCurrent53=53rd Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent53=Instantaneous Values of the 53rd Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent53=Actual Measured Values of the 53rd Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent53=Greatest Measured Values of the 53rd Harmonic Current
Directory_HarmonicCurrent54=54th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent54=Instantaneous Values of the 54th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent54=Actual Measured Values of the 54th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent54=Greatest Measured Values of the 54th Harmonic Current
Directory_HarmonicCurrent55=55th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent55=Instantaneous Values of the 55th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent55=Actual Measured Values of the 55th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent55=Greatest Measured Values of the 55th Harmonic Current
Directory_HarmonicCurrent56=56th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent56=Instantaneous Values of the 56th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent56=Actual Measured Values of the 56th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent56=Greatest Measured Values of the 56th Harmonic Current
Directory_HarmonicCurrent57=57th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent57=Instantaneous Values of the 57th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent57=Actual Measured Values of the 57th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent57=Greatest Measured Values of the 57th Harmonic Current
Directory_HarmonicCurrent58=58th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent58=Instantaneous Values of the 58th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent58=Actual Measured Values of the 58th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent58=Greatest Measured Values of the 58th Harmonic Current
Directory_HarmonicCurrent59=59th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent59=Instantaneous Values of the 59th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent59=Actual Measured Values of the 59th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent59=Greatest Measured Values of the 59th Harmonic Current
Directory_HarmonicCurrent60=60th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent60=Instantaneous Values of the 60th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent60=Actual Measured Values of the 60th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent60=Greatest Measured Values of the 60th Harmonic Current
Directory_HarmonicCurrent61=61st Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent61=Instantaneous Values of the 61st Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent61=Actual Measured Values of the 61st Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent61=Greatest Measured Values of the 61st Harmonic Current
Directory_HarmonicCurrent62=62nd Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent62=Instantaneous Values of the 62nd Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent62=Actual Measured Values of the 62nd Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent62=Greatest Measured Values of the 62nd Harmonic Current
Directory_HarmonicCurrent63=63rd Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent63=Instantaneous Values of the 63rd Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent63=Actual Measured Values of the 63rd Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent63=Greatest Measured Values of the 63rd Harmonic Current
Directory_HarmonicCurrent64=64th Harmonic Current
Directory_InstantaneousValuesOfTheHarmonicCurrent64=Instantaneous Values of the 64th Harmonic Current
Directory_ActualMeasuredValuesOfTheHarmonicCurrent64=Actual Measured Values of the 64th Harmonic Current
Directory_GreatestMeasuredValuesOfTheHarmonicCurrent64=Greatest Measured Values of the 64th Harmonic Current
Directory_LocalDeviceDiagnostics=Local Device Diagnostics
Directory_DigitalInputs=Digital Inputs
Directory_DigitalOutputs=Digital Outputs
Directory_LogicFunctions=Logic Functions
Directory_Measurement=Measurement
Directory_Voltage=Voltage
Directory_Power Period=Power Period
Directory_Power Factor=Power Factor
Directory_Frequency=Frequency
Directory_THD=THD
Directory_Cos Phi=Cos Phi
Directory_Flicker=Flicker
Directory_Voltage Harmonics=Voltager Harmonics
Directory_Current Harmonics=Current Harmonics
Directory_Others=Others
Directory_State=State
Directory_BusBarTemperature=Bus temperature
Directory_HarmonicDistortion_AverageValues1_hDVoltageL-N=HarmonicDistortion AverageValues1 hDVoltageL-N
Directory_HarmonicDistortion_AverageValues2_hDVoltageL-N=HarmonicDistortion AverageValues2 hDVoltageL-N

DataPointGroupName_Voltage=Voltage
DataPointGroupName_Current=Current
DataPointGroupName_Power=Power
DataPointGroupName_Power Period=Power Period
DataPointGroupName_Power Factor=Power Factor
DataPointGroupName_Frequency=Frequency
DataPointGroupName_THD=THD
DataPointGroupName_Counter=Counter
DataPointGroupName_Cos Phi=Cos Phi
DataPointGroupName_Flicker=Flicker
DataPointGroupName_Voltage Harmonics=Voltage Harmonics
DataPointGroupName_Current Harmonics=Current Harmonics
DataPointGroupName_Temperature=Temperature
DataPointGroupName_Others=Others
DataPointGroupName_State=State