﻿using Siemens.PanelManager.HubModel;
using Siemens.PanelManager.Monitor.Cmd;

namespace Siemens.PanelManager.Monitor.Function
{
    static class CheckDeviceFunc
    {
        public static async Task<IpInfo[]> GetIpInfoes(ILogger logger)
        {
            try
            {
                var ipInfoes = await LiunxCmd.GetIpInfo(logger);
                return ipInfoes ?? new IpInfo[0];
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Get ip info fail");
                return new IpInfo[0];
            }
        }

        public static async Task<string[]> GetUsbInfoes(ILogger logger)
        {
            try
            {
                var usbInfoes = await LiunxCmd.GetUsbInfo(logger);
                return usbInfoes;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Get usb info fail");
                return new string[0];
            }
        }
    }
}
