﻿using Microsoft.Extensions.Logging;
using MQTTnet.Diagnostics;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Common.Mqtt
{
    public class MqttLogger : IMqttNetLogger
    {
        private ILogger _logger;
        public MqttLogger(ILogger logger)
        {
            _logger = logger;
        }

        public bool IsEnabled =>true;

        public void Publish(MqttNetLogLevel logLevel, string source, string message, object[] parameters, Exception exception)
        {
            switch (logLevel)
            {
                case MqttNetLogLevel.Verbose:
                    if (exception != null)
                    {
                        _logger.LogDebug(exception, GetMessage(source, message, parameters));
                    }
                    else
                    {
                        _logger.LogDebug(GetMessage(source, message, parameters));
                    }
                    break;
                case MqttNetLogLevel.Warning:
                    if (exception != null)
                    {
                        _logger.LogWarning(exception, GetMessage(source, message, parameters));
                    }
                    else
                    {
                        _logger.LogWarning(GetMessage(source, message, parameters));
                    }
                    break;
                case MqttNetLogLevel.Info:
                    if (exception != null)
                    {
                        _logger.LogInformation(exception, GetMessage(source, message, parameters));
                    }
                    else
                    {
                        _logger.LogInformation(GetMessage(source, message, parameters));
                    }
                    break;
                case MqttNetLogLevel.Error:
                    if (exception != null)
                    {
                        _logger.LogError(exception, GetMessage(source, message, parameters));
                    }
                    else
                    {
                        _logger.LogError(GetMessage(source, message, parameters));
                    }
                    break;
                default:
                    break;
            }
        }

        private string GetMessage(string source, string message, object[] parameters)
        {
            var messageBuilder = new StringBuilder((source == null ? 0 : 8 + source.Length) + (message == null ? 0 : 10 + message.Length));

            if (!string.IsNullOrEmpty(source))
            {
                messageBuilder.Append("Source: ");
                messageBuilder.AppendLine(source);
            }

            if (!string.IsNullOrEmpty(message))
            {
                messageBuilder.Append("Message: ");
                if (parameters != null)
                {
                    messageBuilder.AppendLine(string.Format(message, parameters));
                }
                else
                {
                    messageBuilder.AppendLine(message);
                }
            }

            return messageBuilder.ToString();
        }
    }
}
