[{"Code": "HaveAlarm", "Name": "HaveAlarm", "GroupName": "Status", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "Alarm", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "ParentName": "Status", "MqttGroupName": "Alarm", "MqttSamplingPeriod": 1, "IsDefaultMqtt": 1, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "IsConnected", "Name": "IsConnected", "GroupName": "Status", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "UDCConnect", "IsSystemStatus": true, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Switch", "Name": "Switch", "GroupName": "Status", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Bss_BSS_STATUS_MAIN_CONTACTS_CLOSED", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"ChangeSwitch\"}", "Sort": 9999, "ParentName": "ETUStatus", "MqttGroupName": "Status", "MqttSamplingPeriod": 1, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "SpringCharged", "Name": "SpringCharged", "GroupName": "Status", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Bss_BSS_STATUS_SPRING_CHARGED", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "ParentName": "BSSModule", "MqttGroupName": "Status", "MqttSamplingPeriod": 1, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "SwitchReady", "Name": "SwitchReady", "GroupName": "Status", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Bss_BSS_STATUS_READY_TO_CLOSE", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "ParentName": "BSSModule", "MqttGroupName": "Status", "MqttSamplingPeriod": 1, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "BreakerPosition", "Name": "BreakerPosition", "GroupName": "Status", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"GetBreakerPosition\"}", "Sort": 9999, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "TestPosition", "Name": "TestPosition", "GroupName": "Status", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Bss_BSS_STATUS_TEST", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "ParentName": "BSSModule", "MqttGroupName": "Status", "MqttSamplingPeriod": 1, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "OperationPosition", "Name": "OperationPosition", "GroupName": "Status", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Bss_BSS_STATUS_CONNECTED", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "ParentName": "BSSModule", "MqttGroupName": "Status", "MqttSamplingPeriod": 1, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "HasTriped", "Name": "HasTriped", "GroupName": "Status", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Bss_BSS_STATUS_BREAKER_TRIPPED", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"SetTriped\"}", "Sort": 9999, "ParentName": "ETUStatus", "MqttGroupName": "Status", "MqttSamplingPeriod": 1, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ua", "Name": "Ua", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_V_L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 101, "ParentName": "VoltageL-N_InstantaneousMeasuredValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ub", "Name": "Ub", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_V_L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 102, "ParentName": "VoltageL-N_InstantaneousMeasuredValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Uc", "Name": "Uc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_V_L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 103, "ParentName": "VoltageL-N_InstantaneousMeasuredValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Uab", "Name": "Uab", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_V_L12", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 104, "ParentName": "VoltageL-L_InstantaneousMeasuredValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ubc", "Name": "Ubc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_V_L23", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 105, "ParentName": "VoltageL-L_InstantaneousMeasuredValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Uca", "Name": "Uca", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_V_L31", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 106, "ParentName": "VoltageL-L_InstantaneousMeasuredValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ia", "Name": "Ia", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_I_L1", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 201, "ParentName": "Current_InstantaneousMeasuredValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ib", "Name": "Ib", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_I_L2", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 202, "ParentName": "Current_InstantaneousMeasuredValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ic", "Name": "Ic", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_I_L3", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 203, "ParentName": "Current_InstantaneousMeasuredValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "I_Agv", "Name": "I_Agv", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_I_AVG", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 204, "ParentName": "Current_InstantaneousMeasuredValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "P", "Name": "P", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_P_TOT", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 301, "ParentName": "ActivePower_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Pa", "Name": "Pa", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_P_L1", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 302, "ParentName": "ActivePower_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Pb", "Name": "Pb", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_P_L2", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 303, "ParentName": "ActivePower_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Pc", "Name": "Pc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_P_L3", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 304, "ParentName": "ActivePower_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Q", "Name": "Q", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_Q_TOT_V", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 401, "ParentName": "MeasuringMethodVARtot_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Qa", "Name": "Qa", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_Q_L1", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 402, "ParentName": "MeasuringMethodVARtot_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Qb", "Name": "Qb", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_Q_L2", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 403, "ParentName": "MeasuringMethodVARtot_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Qc", "Name": "Qc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_Q_L3", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 404, "ParentName": "MeasuringMethodVARtot_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "S", "Name": "S", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_S_TOT_V", "Unit": "VA", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 501, "ParentName": "ApparentPower_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor", "Name": "PowFactor", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_PF_TOT_V", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 601, "ParentName": "PowerFactor_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor_A", "Name": "PowFactor_A", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_PF_L1", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 602, "ParentName": "PowerFactor_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor_B", "Name": "PowFactor_B", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_PF_L2", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 603, "ParentName": "PowerFactor_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor_C", "Name": "PowFactor_C", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_PF_L3", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 604, "ParentName": "PowerFactor_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "F", "Name": "F", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_FREQ", "Unit": "Hz", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 701, "ParentName": "FrequencyValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ua", "Name": "THD_Ua", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_THDV_L1", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 701, "ParentName": "THDVoltage_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ub", "Name": "THD_Ub", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_THDV_L2", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 702, "ParentName": "THDVoltage_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Uc", "Name": "THD_Uc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_THDV_L3", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 703, "ParentName": "THDVoltage_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ia", "Name": "THD_Ia", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_THDI_L1", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 711, "ParentName": "THDCurrent_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ib", "Name": "THD_Ib", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_THDI_L2", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 712, "ParentName": "THDCurrent_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ic", "Name": "THD_Ic", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_THDI_L3", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 713, "ParentName": "THDCurrent_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ForwardActivePower", "Name": "ForwardActivePower", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_EA_SUM_IMPORT", "Unit": "Wh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 811, "ParentName": "ActiveEnergy_Import", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ForwardReactivePower", "Name": "ForwardReactivePower", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_ER_SUM_IMPORT", "Unit": "varh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 812, "ParentName": "ReactiveEnergy_Import", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ReverseActivePower", "Name": "ReverseActivePower", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_EA_SUM_EXPORT", "Unit": "Wh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 813, "ParentName": "ActiveEnergy_Export", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ReverseReactivePower", "Name": "ReverseReactivePower", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_ER_SUM_EXPORT", "Unit": "varh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 814, "ParentName": "ReactiveEnergy_Export", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "BreakerTemp", "Name": "BreakerTemp", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "[T]", "UdcCode": "Bss_TEMP_BREAKER", "Unit": "℃", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"KelvinToCelsius\"}", "Sort": 9001, "ParentName": "Temperatures", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ComTemp", "Name": "ComTemp", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "[T]", "UdcCode": "ComRoleA_TEMP_EXT", "Unit": "℃", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"KelvinToCelsius\"}", "Sort": 9002, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "OperatingHours", "Name": "OperatingHours", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_DIAG_OPERATING_TIME", "Unit": "h", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "MainContantStatus", "Name": "MainContantStatus", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_DIAG_CONTACT_STATUS", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "ParentName": "Status", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LTTrips", "Name": "LTTrips", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_DIAG_CNT_TRIP_LONG_TIME", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "ParentName": "Statistics", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "STTrips", "Name": "STTrips", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_DIAG_CNT_TRIP_SHORT_TIME", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "ParentName": "Statistics", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "AllTrips", "Name": "AllTrips", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_DIAG_CNT_TRIP_ALL", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "ParentName": "Statistics", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ElectricalSwitchCycles", "Name": "ElectricalSwitchCycles", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Bss_DIAG_OPERATING_COUNT_WITH_LOAD", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "MechanicalSwitchCycles", "Name": "MechanicalSwitchCycles", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Bss_DIAG_OPERATING_COUNT_WO_LOAD", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "HealthScore", "Name": "HealthScore", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "PanelManagerAlgorithm", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "RemainingLife", "Name": "RemainingLife", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "", "Unit": "%", "CollectMode": "PanelManagerAlgorithm", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ContactWearRate", "Name": "ContactWearRate", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "", "Unit": "%", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "RatedVoltage", "Name": "RatedVoltage", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_CONF_RATED_VOLTAGE_LL", "Unit": "V", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "MLFB", "Name": "MLFB", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_IDENT_BREAKER_ORDER_ID", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "FirmwareRevision_1", "Name": "FirmwareRevision_1", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_IDENT_SW_REVISION", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "FirmwareRevision_2", "Name": "FirmwareRevision_2", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_IDENT_FW_ETU_UCPROTECTION", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "IdentNumber", "Name": "IdentNumber", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_IDENT_BREAKER_TAG_FUNCTION", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "FirmwareRevision_3", "Name": "FirmwareRevision_3", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_IDENT_ORDER_ID", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "FirmwareRevision_4", "Name": "FirmwareRevision_4", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_IDENT_FW_REVISION", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "SerialNumber", "Name": "SerialNumber", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_IDENT_BREAKER_SERIAL_NUMBER", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LT_ONOFF", "Name": "LT_ONOFF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_LT_ONOFF_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LT_IR", "Name": "LT_IR", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_LT_IR_A", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LT_TR", "Name": "LT_TR", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_LT_TR_A", "Unit": "ms", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LT_PARA_CURVE", "Name": "LT_PARA_CURVE", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_LT_PARA_CURVE_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LT_ONOFF_REMOTE", "Name": "LT_ONOFF_REMOTE", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_LT_ONOFF_A_REMOTE", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LT_IR_REMOTE", "Name": "LT_IR_REMOTE", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_LT_IR_A_REMOTE", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LT_TR_REMOTE", "Name": "LT_TR_REMOTE", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_LT_TR_A_REMOTE", "Unit": "ms", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LT_PARA_CURVE_REMOTE", "Name": "LT_PARA_CURVE_REMOTE", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_LT_PARA_CURVE_A_REMOTE", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LT_TAU", "Name": "LT_TAU", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_LT_TAU_A", "Unit": "s", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LT_THERMAL_MEM_ONOFF", "Name": "LT_THERMAL_MEM_ONOFF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_LT_THERMAL_MEM_ONOFF_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LT_PHASE_LOSS_SENSITIV_ONOFF", "Name": "LT_PHASE_LOSS_SENSITIV_ONOFF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_LT_PHASE_LOSS_SENSITIV_ONOFF_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "PAL_ONOFF", "Name": "PAL_ONOFF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_PAL_ONOFF_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "PAL_IR", "Name": "PAL_IR", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_PAL_IR_A", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "PAL_TR", "Name": "PAL_TR", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_PAL_TR_A", "Unit": "ms", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LTN_ONOFF", "Name": "LTN_ONOFF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_LTN_ONOFF_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LTN_IN", "Name": "LTN_IN", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_LTN_IN_A", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "PAL_IN", "Name": "PAL_IN", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_PAL_IN_A", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "ST_ONOFF", "Name": "ST_ONOFF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_ST_ONOFF_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "ST_ISD", "Name": "ST_ISD", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_ST_ISD_A", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "ST_TSD", "Name": "ST_TSD", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_ST_TSD_A", "Unit": "ms", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "ST_I2t_ON_OFF", "Name": "ST_I2t_ON_OFF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_ST_I2t_ON_OFF_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "ST_ONOFF_REMOTE", "Name": "ST_ONOFF_REMOTE", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_ST_ONOFF_A_REMOTE", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "ST_ISD_REMOTE", "Name": "ST_ISD_REMOTE", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_ST_ISD_A_REMOTE", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "ST_TSD_REMOTE", "Name": "ST_TSD_REMOTE", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_ST_TSD_A_REMOTE", "Unit": "ms", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "ST_I2t_ON_OFF_REMOTE", "Name": "ST_I2t_ON_OFF_REMOTE", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_ST_I2t_ON_OFF_A_REMOTE", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "ST_ISD_REF_I2TSD", "Name": "ST_ISD_REF_I2TSD", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_ST_ISD_REF_I2TSD_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "ST_INTERMITTENT_ONOFF", "Name": "ST_INTERMITTENT_ONOFF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_ST_INTERMITTENT_ONOFF_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "DST_ONOFF", "Name": "DST_ONOFF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_DST_ONOFF_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "DST_ISD_FW", "Name": "DST_ISD_FW", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_DST_ISD_FW_A", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "DST_ISD_REV", "Name": "DST_ISD_REV", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_DST_ISD_REV_A", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "DST_TSD_FW", "Name": "DST_TSD_FW", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_DST_TSD_FW_A", "Unit": "ms", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "DST_TSD_REV", "Name": "DST_TSD_REV", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_DST_TSD_REV_A", "Unit": "ms", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "INST_ONOFF", "Name": "INST_ONOFF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_INST_ONOFF_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "INST_II", "Name": "INST_II", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_INST_II_A", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "INST_ONOFF_REMOTE", "Name": "INST_ONOFF_REMOTE", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_INST_ONOFF_A_REMOTE", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "INST_II_REMOTE", "Name": "INST_II_REMOTE", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_INST_II_A_REMOTE", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_PROTECTION_ONOFF", "Name": "GF_PROTECTION_ONOFF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_GF_PROTECTION_ONOFF_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_INTERMITTENT_ONOFF", "Name": "GF_INTERMITTENT_ONOFF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_GF_INTERMITTENT_ONOFF_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_STD_PARA_CURVE", "Name": "GF_STD_PARA_CURVE", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_GF_STD_PARA_CURVE_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_STD_IG_RESIDUAL", "Name": "GF_STD_IG_RESIDUAL", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_GF_STD_IG_RESIDUAL_A", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_STD_TG_RESIDUAL", "Name": "GF_STD_TG_RESIDUAL", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_GF_STD_TG_RESIDUAL_A", "Unit": "ms", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_STD_IG_DIRECT", "Name": "GF_STD_IG_DIRECT", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_GF_STD_IG_DIRECT_A", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_STD_TG_DIRECT", "Name": "GF_STD_TG_DIRECT", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_GF_STD_TG_DIRECT_A", "Unit": "ms", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_STD_IG_DM_REF", "Name": "GF_STD_IG_DM_REF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_GF_STD_IG_DM_REF_A", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_STD_TG_DM_REF", "Name": "GF_STD_TG_DM_REF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_GF_STD_TG_DM_REF_A", "Unit": "ms", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_STD_IG_DM_UREF", "Name": "GF_STD_IG_DM_UREF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_GF_STD_IG_DM_UREF_A", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_STD_TG_DM_UREF", "Name": "GF_STD_TG_DM_UREF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_GF_STD_TG_DM_UREF_A", "Unit": "ms", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_STD_IG_HIZ_REF_SEC", "Name": "GF_STD_IG_HIZ_REF_SEC", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_GF_STD_IG_HIZ_REF_SEC_A", "Unit": "mA", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_STD_TG_HIZ_REF", "Name": "GF_STD_TG_HIZ_REF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_GF_STD_TG_HIZ_REF_A", "Unit": "ms", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_STD_IG_HIZ_UREF", "Name": "GF_STD_IG_HIZ_UREF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_GF_STD_IG_HIZ_UREF_A", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_STD_TG_HIZ_UREF", "Name": "GF_STD_TG_HIZ_UREF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_GF_STD_TG_HIZ_UREF_A", "Unit": "ms", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_ALARM_ONOFF", "Name": "GF_ALARM_ONOFF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_GF_ALARM_ONOFF_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_ALARM_IG_RESIDUAL", "Name": "GF_ALARM_IG_RESIDUAL", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_GF_IG_RESIDUAL_ALARM_A", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_ALARM_IG_DIRECT", "Name": "GF_ALARM_IG_DIRECT", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_GF_IG_DIRECT_ALARM_A", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_ALARM_IG_DM_UREF", "Name": "GF_ALARM_IG_DM_UREF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_GF_IG_DM_UREF_ALARM_A", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_ALARM_IG_HIZ_UREF", "Name": "GF_ALARM_IG_HIZ_UREF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_GF_IG_HIZ_UREF_ALARM_A", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_ALARM_TG", "Name": "GF_ALARM_TG", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_GF_TG_ALARM_A", "Unit": "ms", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "RP_ONOFF", "Name": "RP_ONOFF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_RP_ONOFF_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "RP_PICKUP", "Name": "RP_PICKUP", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_RP_PICKUP_A", "Unit": "%", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "RP_DELAY", "Name": "RP_DELAY", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_RP_DELAY_A", "Unit": "ms", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19999, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_1", "Name": "Harmonic_Ua_1", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_01", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 132, "ParentName": "HarmonicVoltage1", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_1", "Name": "Harmonic_Ub_1", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_01", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 133, "ParentName": "HarmonicVoltage1", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_1", "Name": "Harmonic_Uc_1", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_01", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 134, "ParentName": "HarmonicVoltage1", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_2", "Name": "Harmonic_Ua_2", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_02", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 135, "ParentName": "HarmonicVoltage2", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_2", "Name": "Harmonic_Ub_2", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_02", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 136, "ParentName": "HarmonicVoltage2", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_2", "Name": "Harmonic_Uc_2", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_02", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 137, "ParentName": "HarmonicVoltage2", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_3", "Name": "Harmonic_Ua_3", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_03", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 138, "ParentName": "HarmonicVoltage3", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_3", "Name": "Harmonic_Ub_3", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_03", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 139, "ParentName": "HarmonicVoltage3", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_3", "Name": "Harmonic_Uc_3", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_03", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 140, "ParentName": "HarmonicVoltage3", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_4", "Name": "Harmonic_Ua_4", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_04", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 141, "ParentName": "HarmonicVoltage4", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_4", "Name": "Harmonic_Ub_4", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_04", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 142, "ParentName": "HarmonicVoltage4", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_4", "Name": "Harmonic_Uc_4", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_04", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 143, "ParentName": "HarmonicVoltage4", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_5", "Name": "Harmonic_Ua_5", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_05", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 144, "ParentName": "HarmonicVoltage5", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_5", "Name": "Harmonic_Ub_5", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_05", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 145, "ParentName": "HarmonicVoltage5", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_5", "Name": "Harmonic_Uc_5", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_05", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 146, "ParentName": "HarmonicVoltage5", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_6", "Name": "Harmonic_Ua_6", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_06", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 147, "ParentName": "HarmonicVoltage6", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_6", "Name": "Harmonic_Ub_6", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_06", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 148, "ParentName": "HarmonicVoltage6", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_6", "Name": "Harmonic_Uc_6", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_06", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 149, "ParentName": "HarmonicVoltage6", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_7", "Name": "Harmonic_Ua_7", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_07", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 150, "ParentName": "HarmonicVoltage7", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_7", "Name": "Harmonic_Ub_7", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_07", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 151, "ParentName": "HarmonicVoltage7", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_7", "Name": "Harmonic_Uc_7", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_07", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 152, "ParentName": "HarmonicVoltage7", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_8", "Name": "Harmonic_Ua_8", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_08", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 153, "ParentName": "HarmonicVoltage8", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_8", "Name": "Harmonic_Ub_8", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_08", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 154, "ParentName": "HarmonicVoltage8", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_8", "Name": "Harmonic_Uc_8", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_08", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 155, "ParentName": "HarmonicVoltage8", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_9", "Name": "Harmonic_Ua_9", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_09", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 156, "ParentName": "HarmonicVoltage9", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_9", "Name": "Harmonic_Ub_9", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_09", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 157, "ParentName": "HarmonicVoltage9", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_9", "Name": "Harmonic_Uc_9", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_09", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 158, "ParentName": "HarmonicVoltage9", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_10", "Name": "Harmonic_Ua_10", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_10", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 159, "ParentName": "HarmonicVoltage10", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_10", "Name": "Harmonic_Ub_10", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_10", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 160, "ParentName": "HarmonicVoltage10", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_10", "Name": "Harmonic_Uc_10", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_10", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 161, "ParentName": "HarmonicVoltage10", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_11", "Name": "Harmonic_Ua_11", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_11", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 162, "ParentName": "HarmonicVoltage11", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_11", "Name": "Harmonic_Ub_11", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_11", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 163, "ParentName": "HarmonicVoltage11", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_11", "Name": "Harmonic_Uc_11", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_11", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 164, "ParentName": "HarmonicVoltage11", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_12", "Name": "Harmonic_Ua_12", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_12", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 165, "ParentName": "HarmonicVoltage12", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_12", "Name": "Harmonic_Ub_12", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_12", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 166, "ParentName": "HarmonicVoltage12", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_12", "Name": "Harmonic_Uc_12", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_12", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 167, "ParentName": "HarmonicVoltage12", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_13", "Name": "Harmonic_Ua_13", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_13", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 168, "ParentName": "HarmonicVoltage13", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_13", "Name": "Harmonic_Ub_13", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_13", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 169, "ParentName": "HarmonicVoltage13", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_13", "Name": "Harmonic_Uc_13", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_13", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 170, "ParentName": "HarmonicVoltage13", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_14", "Name": "Harmonic_Ua_14", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_14", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 171, "ParentName": "HarmonicVoltage14", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_14", "Name": "Harmonic_Ub_14", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_14", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 172, "ParentName": "HarmonicVoltage14", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_14", "Name": "Harmonic_Uc_14", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_14", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 173, "ParentName": "HarmonicVoltage14", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_15", "Name": "Harmonic_Ua_15", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_15", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 174, "ParentName": "HarmonicVoltage15", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_15", "Name": "Harmonic_Ub_15", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_15", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 175, "ParentName": "HarmonicVoltage15", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_15", "Name": "Harmonic_Uc_15", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_15", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 176, "ParentName": "HarmonicVoltage15", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_16", "Name": "Harmonic_Ua_16", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_16", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 177, "ParentName": "HarmonicVoltage16", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_16", "Name": "Harmonic_Ub_16", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_16", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 178, "ParentName": "HarmonicVoltage16", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_16", "Name": "Harmonic_Uc_16", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_16", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 179, "ParentName": "HarmonicVoltage16", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_17", "Name": "Harmonic_Ua_17", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_17", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 180, "ParentName": "HarmonicVoltage17", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_17", "Name": "Harmonic_Ub_17", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_17", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 181, "ParentName": "HarmonicVoltage17", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_17", "Name": "Harmonic_Uc_17", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_17", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 182, "ParentName": "HarmonicVoltage17", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_18", "Name": "Harmonic_Ua_18", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_18", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 183, "ParentName": "HarmonicVoltage18", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_18", "Name": "Harmonic_Ub_18", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_18", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 184, "ParentName": "HarmonicVoltage18", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_18", "Name": "Harmonic_Uc_18", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_18", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 185, "ParentName": "HarmonicVoltage18", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_19", "Name": "Harmonic_Ua_19", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_19", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 186, "ParentName": "HarmonicVoltage19", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_19", "Name": "Harmonic_Ub_19", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_19", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 187, "ParentName": "HarmonicVoltage19", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_19", "Name": "Harmonic_Uc_19", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_19", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 188, "ParentName": "HarmonicVoltage19", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_20", "Name": "Harmonic_Ua_20", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_20", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 189, "ParentName": "HarmonicVoltage20", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_20", "Name": "Harmonic_Ub_20", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_20", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 190, "ParentName": "HarmonicVoltage20", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_20", "Name": "Harmonic_Uc_20", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_20", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 191, "ParentName": "HarmonicVoltage20", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_21", "Name": "Harmonic_Ua_21", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_21", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 192, "ParentName": "HarmonicVoltage21", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_21", "Name": "Harmonic_Ub_21", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_21", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 193, "ParentName": "HarmonicVoltage21", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_21", "Name": "Harmonic_Uc_21", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_21", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 194, "ParentName": "HarmonicVoltage21", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_22", "Name": "Harmonic_Ua_22", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_22", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 195, "ParentName": "HarmonicVoltage22", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_22", "Name": "Harmonic_Ub_22", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_22", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 196, "ParentName": "HarmonicVoltage22", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_22", "Name": "Harmonic_Uc_22", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_22", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 197, "ParentName": "HarmonicVoltage22", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_23", "Name": "Harmonic_Ua_23", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_23", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 198, "ParentName": "HarmonicVoltage23", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_23", "Name": "Harmonic_Ub_23", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_23", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 199, "ParentName": "HarmonicVoltage23", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_23", "Name": "Harmonic_Uc_23", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_23", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 200, "ParentName": "HarmonicVoltage23", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_24", "Name": "Harmonic_Ua_24", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_24", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 201, "ParentName": "HarmonicVoltage24", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_24", "Name": "Harmonic_Ub_24", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_24", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 202, "ParentName": "HarmonicVoltage24", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_24", "Name": "Harmonic_Uc_24", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_24", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 203, "ParentName": "HarmonicVoltage24", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_25", "Name": "Harmonic_Ua_25", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_25", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 204, "ParentName": "HarmonicVoltage25", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_25", "Name": "Harmonic_Ub_25", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_25", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 205, "ParentName": "HarmonicVoltage25", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_25", "Name": "Harmonic_Uc_25", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_25", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 206, "ParentName": "HarmonicVoltage25", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_26", "Name": "Harmonic_Ua_26", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_26", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 207, "ParentName": "HarmonicVoltage26", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_26", "Name": "Harmonic_Ub_26", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_26", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 208, "ParentName": "HarmonicVoltage26", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_26", "Name": "Harmonic_Uc_26", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_26", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 209, "ParentName": "HarmonicVoltage26", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_27", "Name": "Harmonic_Ua_27", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_27", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 210, "ParentName": "HarmonicVoltage27", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_27", "Name": "Harmonic_Ub_27", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_27", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 211, "ParentName": "HarmonicVoltage27", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_27", "Name": "Harmonic_Uc_27", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_27", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 212, "ParentName": "HarmonicVoltage27", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_28", "Name": "Harmonic_Ua_28", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_28", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 213, "ParentName": "HarmonicVoltage28", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_28", "Name": "Harmonic_Ub_28", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_28", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 214, "ParentName": "HarmonicVoltage28", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_28", "Name": "Harmonic_Uc_28", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_28", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 215, "ParentName": "HarmonicVoltage28", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_29", "Name": "Harmonic_Ua_29", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_29", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 216, "ParentName": "HarmonicVoltage29", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_29", "Name": "Harmonic_Ub_29", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_29", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 217, "ParentName": "HarmonicVoltage29", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_29", "Name": "Harmonic_Uc_29", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_29", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 218, "ParentName": "HarmonicVoltage29", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_30", "Name": "Harmonic_Ua_30", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_30", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 219, "ParentName": "HarmonicVoltage30", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_30", "Name": "Harmonic_Ub_30", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_30", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 220, "ParentName": "HarmonicVoltage30", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_30", "Name": "Harmonic_Uc_30", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_30", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 221, "ParentName": "HarmonicVoltage30", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_31", "Name": "Harmonic_Ua_31", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L1_Harmonic_31", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 222, "ParentName": "HarmonicVoltage31", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_31", "Name": "Harmonic_Ub_31", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L2_Harmonic_31", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 223, "ParentName": "HarmonicVoltage31", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_31", "Name": "Harmonic_Uc_31", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WA", "FilterIds": "", "UdcCode": "Etu_HARM_V_L3_Harmonic_31", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 224, "ParentName": "HarmonicVoltage31", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}]