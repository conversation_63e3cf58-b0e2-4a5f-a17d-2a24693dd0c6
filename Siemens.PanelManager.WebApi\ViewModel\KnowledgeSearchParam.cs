﻿using Microsoft.AspNetCore.Mvc;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class KnowledgeSearchParam
    {
        [FromQuery(Name = "page")]
        public int Page { get; set; } = 1;

        [FromQuery(Name = "pageSize")]
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// 字符长度不得少于3
        /// </summary>
        [FromQuery(Name = "keyString")]
        public string? KeyString { get; set; }
    }
}
