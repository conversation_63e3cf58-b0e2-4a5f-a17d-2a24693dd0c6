﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.Excel;
using Siemens.PanelManager.Model.Database.ElectricityCharge;
using Siemens.PanelManager.Model.ElectricityCharge;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.Server.ElectricityCharge;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class ElectricityChargeController : SiemensApiControllerBase
    {
        private SiemensExcelHelper _excelHelper => _provider.GetRequiredService<SiemensExcelHelper>();
        private ElectricityChargeServer _electricityCharge => _provider.GetRequiredService<ElectricityChargeServer>();
        private ILogger<PanelHealthController> _log;
        private ISqlSugarClient _client;
        private IServiceProvider _provider;
        private SiemensCache _cache;
        private AlarmExtendServer _alarmExtendServer => _provider.GetRequiredService<AlarmExtendServer>();

        public ElectricityChargeController(SqlSugarScope client,
           SiemensCache cache,
           ILogger<PanelHealthController> log,
           IServiceProvider provider)
           : base(provider, cache)
        {
            _client = client;
            _log = log;
            _provider = provider;
            _cache = cache;
        }
        [HttpGet]
        [SwaggerOperation(Summary = "Swagger_ElectricityCharge_GetElectricityCharge", Description = "Swagger_ElectricityCharge_GetElectricityCharge_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<List<BillingSchemeModel>>> GetElectricityCharge()
        {
            var billingSchemes = await _client.Queryable<BillingScheme>().OrderBy(p => p.Id).ToArrayAsync();
            var result = new List<BillingSchemeModel>();
            foreach (var billingScheme in billingSchemes)
            {
                BillingSchemeModel billing = new BillingSchemeModel();
                billing.Id = billingScheme.Id;
                billing.Name = billingScheme.billingName;
                billing.IsEnable = billingScheme.isEnable;
                result.Add(billing);
            }
            return new ResponseBase<List<BillingSchemeModel>>()
            {
                Code = 20000,
                Data = result
            };

        }
        [HttpPut("{id}/Excute")]
        [SwaggerOperation(Summary = "Swagger_ElectricityCharge_Excute", Description = "Swagger_ElectricityCharge_Excute")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> Excute(int id)
        {
            var billing = await _client.Queryable<BillingScheme>().FirstAsync(a => a.Id == id);
            if (billing != null)
            {

                var billingOld = await _client.Queryable<BillingScheme>().FirstAsync(a => a.Id != id && a.isEnable == true);
                if(billingOld != null)
                {
                    billingOld.isEnable = false;
                    _client.Ado.BeginTran();
                    await _client.Updateable(billingOld).ExecuteCommandAsync();
                    var elcListOld = await _client.Queryable<ElectricityConfig>().Where(a => a.bsid == billingOld.Id && a.isLatest).ToArrayAsync();
                    foreach (var e in elcListOld)
                    {
                        e.effectiveTimeEnd = DateTime.Now;
                        await _client.Updateable(e).ExecuteCommandAsync();
                        ElectricityConfigHistory history = new ElectricityConfigHistory
                        {
                            eid=e.Id,
                            bsid=e.bsid,
                            rateName=e.rateName,
                            rateDesc = e.rateDesc,
                            electrovalence = e.electrovalence,
                            electrovalenceType= e.electrovalenceType,
                            timeRange= e.timeRange,
                            seasonRange=e.seasonRange,
                            seasonType= e.seasonType,
                            step= e.step,
                            stepTarif= e.stepTarif,
                            effectiveTimeStart= e.effectiveTimeStart,
                            effectiveTimeEnd= e.effectiveTimeEnd,
                            CreatedTime=DateTime.Now,
                            CreatedBy= UserName,
                            UpdatedBy= UserName,
                            UpdatedTime = DateTime.Now
                        };
                        await _client.Insertable<ElectricityConfigHistory>(history).ExecuteReturnIdentityAsync();
                    }
                    billing.isEnable = true;
                    await _client.Updateable(billing).ExecuteCommandAsync();
                    var elcList = await _client.Queryable<ElectricityConfig>().Where(a => a.bsid == id && a.isLatest).ToArrayAsync();
                    foreach(var e in elcList)
                    {
                        e.effectiveTimeStart = DateTime.Now;
                        e.effectiveTimeEnd = DateTime.MaxValue;
                        await _client.Updateable(e).ExecuteCommandAsync();
                        
                    }
                    _client.Ado.CommitTran();
                }
                return new ResponseBase<string>()
                {
                    Code = 20000,
                    Message = MessageContext.Success
                };
            }
            return new ResponseBase<string>()
            {
                Code = 40400,
                Message = MessageContext.ErrorParam
            };

        }
        public static bool ValidateTimeIntervals(List<string> intervals)
        {
            int totalSeconds = 0;

            foreach (var interval in intervals)
            {
                if (!TimeSpan.TryParse(interval, out TimeSpan time))
                {
                    // 不是有效的时间格式
                    return false;
                }

                totalSeconds += (int)time.TotalSeconds;
            }

            return totalSeconds <= 86400;
        }
        public static bool VerifyStep(IEnumerable<ElectricityConfigModel> intervals)
        {
            decimal endStep = 0;
            decimal sumStep = 0;
            foreach (var item in intervals)
            {
                if (string.IsNullOrEmpty(item.stepTarif.to))
                {
                    endStep = Decimal.Parse(item.stepTarif.from);
                }
                else
                {
                    var d = Decimal.Parse(item.stepTarif.to) - Decimal.Parse(item.stepTarif.from);
                    sumStep += d;
                }
            }
            if (endStep != sumStep)
            {
                return false;
            }
            return true;
        }
        public static bool VerifySeason(IEnumerable<ElectricityConfigModel> intervals)
        {
            //verify season
            var monthList = intervals.Where(a => a.seasonType == "Month").OrderBy(a => a.id);
            HashSet<string> startMonths = new HashSet<string>();
            int sumMonthDalte = 0;
            foreach (var month in monthList)
            {
                foreach (var item in month.seasonRange)
                {
                    if (startMonths.Add(item.startMonth + "-" + item.endMonth))
                    {
                        int dalte = int.Parse(item.endMonth) - int.Parse(item.startMonth)+1;
                        sumMonthDalte += dalte;
                    }

                }
            }
            if (sumMonthDalte % 12 != 0)
            {
                return false;
            }

            return true;
        }
        public static bool VerifyTime(IEnumerable<ElectricityConfigModel> intervals)
        {
            var gourpBySeason = intervals.Where(a => a.seasonType == "Month").GroupBy(a => a.seasonType);
            List<string> times = new List<string>();

            foreach (var data in gourpBySeason)
            {
                
                Dictionary<string,int> valuePairs = new Dictionary<string,int>();
                foreach (var item in data)
                {
                    int totalHours = 0;
                    foreach (var range in item.timeRange)
                    {
                        TimeSpan time1 = TimeSpan.Parse(range.startTime);
                        TimeSpan time2 = new TimeSpan();
                        int hours = 0;
                        if (range.endTime != "24:00")
                        {
                            time2 = TimeSpan.Parse(range.endTime);

                            hours = time2.Hours - time1.Hours;
                        }
                        else
                        {
                            hours = 24 - time1.Hours;
                        }
                        totalHours += hours;
                    }
                    foreach(var season in item.seasonRange)
                    {
                        string monthStr = season.startMonth + "-" + season.endMonth;
                        if (valuePairs.ContainsKey(monthStr))
                        {
                            valuePairs[monthStr] = valuePairs[monthStr] + totalHours;
                        }
                        else{
                            valuePairs.Add(season.startMonth + "-" + season.endMonth, totalHours);
                        }
                       
                    }
                }
                foreach(var key in valuePairs.Keys)
                {
                    if (valuePairs[key] % 24 != 0)
                    {
                        return false;
                    }
                }
            }
            return true;
        }

        [HttpPut("ElectricityDetail")]
        [SwaggerOperation(Summary = "Swagger_ElectricityCharge_Save", Description = "Swagger_ElectricityCharge_Save")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> SaveElectricity(List<ElectricityConfigModel> electricityCharges)
        {
            foreach (var model in electricityCharges)
            {
                var bs = await _client.Queryable<BillingScheme>().FirstAsync(a => a.Id == model.bsId);
                if (string.IsNullOrEmpty(model.rateName) || model.electrovalence <= 0 || string.IsNullOrEmpty(model.electrovalenceType))
                {
                    return new ResponseBase<string>()
                    {
                        Code = 40300,
                        Message = MessageContext.ErrorParam
                    };
                }
                if (bs.billingCode== "TimeSharing")
                {
                    if (model.timeRange == null || model.seasonRange == null || string.IsNullOrEmpty(model.seasonType))
                    {
                        return new ResponseBase<string>()
                        {
                            Code = 40300,
                            Message = MessageContext.ErrorParam
                        };
                    }
                }
                if (bs.billingCode == "TimeStep")
                {
                    if (model.timeRange == null || model.seasonRange == null || string.IsNullOrEmpty(model.seasonType) || model.stepTarif == null)
                    {
                        return new ResponseBase<string>()
                        {
                            Code = 40300,
                            Message = MessageContext.ErrorParam
                        };
                    }
                }
                if (bs.billingCode == "Step")
                {
                    if (model.stepTarif == null)
                    {
                        return new ResponseBase<string>()
                        {
                            Code = 40300,
                            Message = MessageContext.ErrorParam
                        };
                    }
                }
            }
            try
            {
                int bsid = electricityCharges.First().bsId;
                 
                var bs = await _client.Queryable<BillingScheme>().FirstAsync(a => a.Id == bsid);
                var checkList = electricityCharges.Where(a => a.electrovalenceType == "受电" || a.electrovalenceType == ElectrovalenceType.receivePower.ToString());
                switch (bs.billingCode)
                {
                    case "TimeSharing":
                        //verify season
                        bool seasonTimeResult = VerifySeason(checkList);
                        if (!seasonTimeResult)
                        {
                            return new ResponseBase<string>()
                            {
                                Code = 40300,
                                Message = MessageContext.GetErrorValue("Electricity_Verify_Season")
                            };
                        }
                        bool timeVerifyResult = VerifyTime(checkList);
                        if (!timeVerifyResult)
                        {
                            return new ResponseBase<string>()
                            {
                                Code = 40300,
                                Message = MessageContext.GetErrorValue("Electricity_Verify_Time")
                            };
                        }
                        break;
                    case "Step":
                        if (!VerifyStep(checkList))
                        {
                            return new ResponseBase<string>()
                            {
                                Code = 40300,
                                Message = MessageContext.GetErrorValue("Electricity_Verify_Step")
                            };
                        }
                        break;
                    case "TimeStep":
                        //checkList.ForEach(a => a.stepTarifStr = JsonConvert.SerializeObject(a.stepTarif));
                        var stepGroup = checkList.GroupBy(a => JsonConvert.SerializeObject(a.stepTarif));
                        List<ElectricityConfigModel> verifyStepModel = new List<ElectricityConfigModel>();

                        foreach (var group in stepGroup)
                        {
                            List<ElectricityConfigModel> verifyTimeModel = new List<ElectricityConfigModel>();
                            verifyStepModel.Add(group.First());
                            foreach (var item in group)
                            {
                                verifyTimeModel.Add(item);
                            }
                            bool seasonResult = VerifySeason(verifyTimeModel);
                            if (!seasonResult)
                            {
                                return new ResponseBase<string>()
                                {
                                    Code = 40300,
                                    Message = MessageContext.GetErrorValue("Electricity_Verify_Season")
                                };
                            }
                            bool timeResult = VerifyTime(verifyTimeModel);
                            if (!timeResult)
                            {
                                return new ResponseBase<string>()
                                {
                                    Code = 40300,
                                    Message = MessageContext.GetErrorValue("Electricity_Verify_Time")
                                };
                            }
                        }
                        var stepReulst = VerifyStep(verifyStepModel);
                        if (!stepReulst)
                        {
                            return new ResponseBase<string>()
                            {
                                Code = 40300,
                                Message = MessageContext.GetErrorValue("Electricity_Verify_Step")
                            };
                        }
                        break;
                }

                
                _client.Ado.BeginTran();
                #region 根据id是否还存在新的列表中，删除电价配置并保存到历史表
                var oldList = await _client.Queryable<ElectricityConfig>().Where(a => a.bsid == bsid).ToArrayAsync();
                var electricityChargesIds = electricityCharges.Select(a => a.id).ToList();

                foreach (var e in oldList)
                {
                    if (bs.isEnable)
                    {
                        ElectricityConfigHistory history = new ElectricityConfigHistory
                        {
                            eid = e.Id,
                            bsid = e.bsid,
                            rateName = e.rateName,
                            rateDesc = e.rateDesc,
                            electrovalence = e.electrovalence,
                            electrovalenceType = e.electrovalenceType,
                            timeRange = e.timeRange,
                            seasonRange = e.seasonRange,
                            seasonType = e.seasonType,
                            step = e.step,
                            stepTarif = e.stepTarif,
                            effectiveTimeStart = e.effectiveTimeStart,
                            effectiveTimeEnd = DateTime.Now,
                            CreatedTime = DateTime.Now,
                            CreatedBy = UserName,
                            UpdatedBy = UserName,
                            UpdatedTime = DateTime.Now
                        };
                        await _client.Insertable<ElectricityConfigHistory>(history).ExecuteReturnIdentityAsync();
                    }
                   
                    if (!electricityChargesIds.Contains(e.Id))
                    {
                        await _client.Deleteable(e).ExecuteCommandAsync();
                        await _alarmExtendServer.InsertOperationLog(UserName, "DeleteElectricityConfig", Model.Database.Alarm.AlarmSeverity.Low, _client);

                    }
                }
                #endregion

                foreach (var electricity in electricityCharges)
                {
                    var db = await _client.Queryable<ElectricityConfig>().FirstAsync(a => a.Id == electricity.id);

                    if (db != null)
                    {
                        db.rateName = electricity.rateName;
                        db.rateDesc = electricity.rateDesc;
                        db.electrovalence = electricity.electrovalence;
                        db.electrovalenceType = electricity.electrovalenceType;
                        db.timeRange = JsonConvert.SerializeObject(electricity.timeRange);
                        db.seasonType = electricity.seasonType;
                        db.seasonRange = JsonConvert.SerializeObject(electricity.seasonRange);
                        db.step = electricity.step;
                        db.stepTarif = JsonConvert.SerializeObject(electricity.stepTarif);
                        db.UpdatedTime = DateTime.Now;
                        db.UpdatedBy = UserName;
                        //如果当前方案是启用状态，保存后需要重置生效时间
                        if (bs.isEnable)
                        {
                            db.effectiveTimeStart = DateTime.Now;
                            db.effectiveTimeEnd = DateTime.MaxValue;
                        }
                        await _client.Updateable(db).ExecuteCommandAsync();
                    }
                    else
                    {
                        int id = await _client.Insertable<ElectricityConfig>(new ElectricityConfig
                        {
                            rateName = electricity.rateName,
                            rateDesc = electricity.rateDesc,
                            seasonRange = JsonConvert.SerializeObject(electricity.seasonRange),
                            bsid = electricity.bsId,
                            electrovalence = electricity.electrovalence,
                            electrovalenceType = electricity.electrovalenceType,
                            timeRange = JsonConvert.SerializeObject(electricity.timeRange),
                            seasonType = electricity.seasonType,
                            step = electricity.step,
                            stepTarif = JsonConvert.SerializeObject(electricity.stepTarif),
                            CreatedTime = DateTime.Now,
                            UpdatedTime = DateTime.Now,
                            CreatedBy = UserName,
                            UpdatedBy = UserName,
                            //isLatest=true,
                            effectiveTimeStart = DateTime.Now,
                            effectiveTimeEnd = DateTime.MaxValue,
                        }).ExecuteReturnIdentityAsync();
                    }
                   

                }
                _client.Ado.CommitTran();

            }
           
            catch (Exception ex)
            {
                _log.LogError(ex, "电价修改保存失败");
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.ServerException
                };
            }

            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = "sucess"
            };
        }


        [HttpDelete("{id}/ElectricityDetail")]
        [SwaggerOperation(Summary = "Swagger_ElectricityCharge_Delete", Description = "Swagger_ElectricityCharge_Delete_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> DeleteElectricityDetail(int id)
        {
            var db = await _client.Queryable<ElectricityConfig>().FirstAsync(a => a.Id == id);
            if (db != null)
            {
                await _client.Deleteable(db).ExecuteCommandAsync();
                await _alarmExtendServer.InsertOperationLog(UserName, "DeleteElectricityConfig", Model.Database.Alarm.AlarmSeverity.Low, _client);
                return new ResponseBase<string>()
                {
                    Code = 20000,
                    Data = MessageContext.Success
                };

            }
            return new ResponseBase<string>()
            {
                Code = 40400,
                Message = MessageContext.ErrorParam
            };
        }
        [HttpGet("ElectricityDetail/{bsid}")]
        [SwaggerOperation(Summary = "Swagger_ElectricityCharge_GetElectricityList", Description = "Swagger_ElectricityCharge_GetElectricityList_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<List<ElectricityConfigModel>>> GetElectricityList(int bsid)
        {
            var configs = await _client.Queryable<ElectricityConfig>().Where(p => p.bsid == bsid).OrderBy(p => p.Id).ToArrayAsync();
            var result = new List<ElectricityConfigModel>();
            try
            {
                foreach (var config in configs)
                {
                    ElectricityConfigModel configModel = new ElectricityConfigModel();
                    configModel.id = config.Id;
                    configModel.bsId = config.bsid;
                    configModel.rateName = config.rateName;
                    configModel.rateDesc = config.rateDesc;
                    configModel.electrovalence = config.electrovalence;
                    configModel.electrovalenceType = config.electrovalenceType;
                    configModel.timeRange = config.timeRange == null ? null : JsonConvert.DeserializeObject<List<timeRange>>(config.timeRange);
                    configModel.seasonRange = config.seasonRange == null ? null : JsonConvert.DeserializeObject<List<seasonRange>>(config.seasonRange);
                    configModel.seasonType = config.seasonType;
                    configModel.step = config.step;
                    configModel.stepTarif = JsonConvert.DeserializeObject<stepTarif>(config.stepTarif);
                    result.Add(configModel);
                }
            }
            catch (Exception ex)
            {
                _log.LogError(ex, "电价获取失败");
                return new ResponseBase<List<ElectricityConfigModel>>()
                {
                    Code = 40000,
                    Data = null,
                    Message = "获取失败"
                };
            }

            return new ResponseBase<List<ElectricityConfigModel>>()
            {
                Code = 20000,
                Data = result
            };
        }
        /// <summary>
        /// get electricity price by datetime or total 
        /// </summary>
        /// <param name="datetime">yyyy-mm-dd hh:MM:ss</param>
        /// <param name="totalElectricity">Unit:kwh,current total</param>
        /// <returns></returns>
        [HttpGet("Price")]
        [SwaggerOperation(Summary = "Swagger_ElectricityCharge_GetElectricityPrice", Description = "Swagger_ElectricityCharge_GetElectricityPrice_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<decimal>> GetElectricityPrice(string datetime, decimal totalElectricity)
        {

            if (string.IsNullOrEmpty(datetime) || totalElectricity < 0)
            {
                return new ResponseBase<decimal>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }
            DateTime time;
            if (!DateTime.TryParse(datetime, out time))
            {
                return new ResponseBase<decimal>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }
            var dt = DateTime.Parse(datetime);
            var history = await _client.Queryable<ElectricityConfigHistory>().Where(a => a.electrovalenceType == "受电" || a.electrovalenceType == ElectrovalenceType.receivePower.ToString()).ToArrayAsync();
            var electricityList = await _client.Queryable<ElectricityConfig>().Where(a => a.electrovalenceType == "受电" || a.electrovalenceType == ElectrovalenceType.receivePower.ToString()).ToArrayAsync();
            if (electricityList.Any())
            {
                var price = await _electricityCharge.GetElectricityPrice(time, totalElectricity, electricityList, history);
                return new ResponseBase<decimal>()
                {
                    Code = 20000,
                    Data = price
                };
            }
            else
            {
                return new ResponseBase<decimal>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }
        }
    }

}
