﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Siemens.PanelManager.Common.Model;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.UDC;
using Siemens.PanelManager.Server.AssetDataPoint;
using Siemens.PanelManager.Server.DataPoint;
using SqlSugar;
using System.Text;
using System.Text.RegularExpressions;
using System.Xml;

namespace Siemens.PanelManager.Server.Asset
{
    /// <summary>
    /// 扫描公共方法服务
    /// </summary>
    public class SplxServer
    {
        private readonly AssetDataPointInfoServer _assetDataPointInfoServer;

        private readonly IServiceProvider _provider;

        private readonly DataPointServer _dataPointServer;

        private readonly SqlSugarScope _db;

        private readonly ILogger _log;

        /// <summary>
        /// 构造函数初始化
        /// </summary>
        /// <param name="provider"></param>
        /// <param name="log"></param>
        public SplxServer(IServiceProvider provider, ILogger<SplxServer> log,
            DataPointServer dataPointServer,
            AssetDataPointInfoServer assetDataPointInfoServer)
        {
            _db = provider.GetService<SqlSugarScope>()!;
            _provider = provider;
            _log = log;
            _dataPointServer = dataPointServer;
            _assetDataPointInfoServer = assetDataPointInfoServer;
        }

        /// <summary>
        /// 根据类型id获取点位信息
        /// </summary>
        /// <param name="typeId"></param>
        /// <returns></returns>
        private async Task<List<AssetDataPointInfo>> GetDataPointInfos(string typeId)
        {
            var assetDataPointInfos = new List<AssetDataPointInfo>();

            if (UniversalDeviceInfo.Instance._deviceTypeMappings.TryGetValue(typeId, out var devices))
            {
                assetDataPointInfos = await _dataPointServer.GetDataPointInfos(AssetLevel.Device, devices.Item1, devices.Item2);
            }

            return assetDataPointInfos;
        }

        /// <summary>
        /// 根据级别，大类型，小类型获取点位信息
        /// </summary>
        /// <param name="level"></param>
        /// <param name="type"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        private async Task<List<AssetDataPointInfo>> GetDataPointInfos(AssetLevel level, string type, string model)
        {
            return await _dataPointServer.GetDataPointInfos(level, type, model);
        }

        /// <summary>
        /// 随机获取rid编号
        /// </summary>
        /// <returns></returns>
        private string GetRId()
        {
            var code = new char[] { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' };
            var r = new Random();

            var sb = new StringBuilder();

            sb.Append('R');

            for (var i = 0; i < code.Length; i++)
            {
                sb.Append(code[r.Next(0, code.Length)]);
            }

            return sb.ToString();
        }

        /// <summary>
        /// 创建rels文件
        /// </summary>
        /// <param name="propertiesPath"></param>
        /// <param name="target"></param>
        /// <returns></returns>
        public string CreateRels(string propertiesPath, string target)
        {
            var relsId = GetRId();

            var relsFolder = Path.Combine(propertiesPath, "_rels");

            if (!Directory.Exists(relsFolder))
            {
                Directory.CreateDirectory(relsFolder);
            }

            var relsFile = Path.Combine(relsFolder, "Properties.xml.rels");
            XmlDocument document = new XmlDocument();
            XmlDeclaration declaration = document.CreateXmlDeclaration("1.0", "utf-8", null);
            var relationshipsXml = document.CreateElement("Relationships", "http://schemas.openxmlformats.org/package/2006/relationships");
            var relationshipXml = document.CreateElement("Relationship", null);
            var typeAttr = document.CreateAttribute("Type");
            typeAttr.InnerText = "http://powerconfig.de/package/2013/relationships/Parent";
            var targetAttr = document.CreateAttribute("Target");
            targetAttr.InnerText = target;
            var idAttr = document.CreateAttribute("Id");
            idAttr.InnerText = relsId;
            relationshipXml.Attributes.Append(idAttr);
            relationshipXml.Attributes.Append(targetAttr);
            relationshipXml.Attributes.Append(typeAttr);

            //var relationshipGatewayXml = document.CreateElement("Relationship", null);
            //var typeGatewayAttr = document.CreateAttribute("Type");
            //typeGatewayAttr.InnerText = "http://powerconfig.de/package/2013/relationships/GatewayParent";
            //var targetGatewayAttr = document.CreateAttribute("Target");
            //targetGatewayAttr.InnerText = target;
            //var idGatewayAttr = document.CreateAttribute("Id");
            //idGatewayAttr.InnerText = relsId;
            //relationshipGatewayXml.Attributes.Append(idGatewayAttr);
            //relationshipGatewayXml.Attributes.Append(targetGatewayAttr);
            //relationshipGatewayXml.Attributes.Append(typeGatewayAttr);
            //relationshipsXml.AppendChild(relationshipGatewayXml);

            relationshipsXml.AppendChild(relationshipXml);
            document.AppendChild(relationshipsXml);
            document.InsertBefore(declaration, document.DocumentElement);
            document.Save(relsFile);

            var xmlStr = string.Empty;
            using (var sr = new StreamReader(relsFile))
            {
                xmlStr = sr.ReadToEnd();
            }

            xmlStr = xmlStr.Replace("xmlns=\"\" ", "");
            using (var sw = new StreamWriter(relsFile))
            {
                sw.Write(xmlStr);
            }

            return relsId;
        }

        /// <summary>
        /// 添加子集的rels文件
        /// </summary>
        /// <param name="filePath"></param>
        /// <param name="relsIds"></param>
        public void AddChildRels(string filePath, Dictionary<string, string> relsIds)
        {
            if (relsIds.Count == 0) return;

            if (!File.Exists(filePath)) return;

            var document = new XmlDocument();
            document.Load(filePath);
            var relationshipsXml = document.DocumentElement;

            foreach (var id in relsIds)
            {
                var relationshipXml = document.CreateElement("Relationship", null);
                var typeAttr = document.CreateAttribute("Type");
                typeAttr.InnerText = "http://powerconfig.de/package/2013/relationships/Children";
                var targetAttr = document.CreateAttribute("Target");
                targetAttr.InnerText = $"/ProjectItems/{id.Key}/Properties.xml";
                var idAttr = document.CreateAttribute("Id");
                idAttr.InnerText = id.Value;

                relationshipXml.Attributes.Append(typeAttr);
                relationshipXml.Attributes.Append(targetAttr);
                relationshipXml.Attributes.Append(idAttr);
                relationshipsXml?.AppendChild(relationshipXml);
            }

            document.Save(filePath);

            var xmlStr = string.Empty;
            using (var sr = new StreamReader(filePath))
            {
                xmlStr = sr.ReadToEnd();
            }

            xmlStr = xmlStr.Replace("xmlns=\"\" ", "");
            using (var sw = new StreamWriter(filePath))
            {
                sw.Write(xmlStr);
            }
        }

        /// <summary>
        /// 创建CommunicationParameter.xml文件
        /// </summary>
        /// <param name="modbusDevice"></param>
        /// <param name="propertiesPath"></param>
        public void CreateCommunicationParameter(AssetInfo modbusDevice, string propertiesPath)
        {
            var ip = "0.0.0.0";
            var port = "502";

            //如果是modbus网关和通用设备，需要把port设置
            if (!string.IsNullOrEmpty(modbusDevice.Port)
                && (("Modbus".Equals(modbusDevice.AssetModel) && "Gateway".Equals(modbusDevice.AssetType))
                || (modbusDevice.AssetType == "GeneralDevice")))
            {
                port = modbusDevice.Port;
            }

            var modbus = "1";
            if (!string.IsNullOrEmpty(modbusDevice.IPAddress))
            {
                var ipMatch = Regex.Match(modbusDevice.IPAddress, "^(?<ip>[\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3})(?<port>:[\\d]{2,5})?(?<modbus>[\\|/][\\d]{2,5})?$");

                if (ipMatch.Success)
                {
                    ip = ipMatch.Groups["ip"].Value;

                    if (!string.IsNullOrEmpty(ipMatch.Groups["port"].Value))
                    {
                        var portStr = ipMatch.Groups["port"].Value ?? string.Empty;
                        port = portStr[1..];
                    }

                    if (!string.IsNullOrEmpty(ipMatch.Groups["modbus"].Value))
                    {
                        var modbusStr = ipMatch.Groups["modbus"].Value ?? string.Empty;
                        modbus = modbusStr[1..];
                    }
                }
            }

            var communicationParameterFile = Path.Combine(propertiesPath, "CommunicationParameter.xml");
            XmlDocument document = new XmlDocument();
            XmlDeclaration declaration = document.CreateXmlDeclaration("1.0", "utf-8", null);
            var paremeterXml = document.CreateElement("CommunicationParameters");
            document.AppendChild(paremeterXml);
            var vessionAttr = document.CreateAttribute("version");
            vessionAttr.InnerText = "1.1";
            paremeterXml.Attributes.Append(vessionAttr);

            #region TpfDeviceReference
            {
                var paremeterItemXml = document.CreateElement("TpfDeviceReference");
                paremeterItemXml.InnerText = "\r\n";
                paremeterXml.AppendChild(paremeterItemXml);
            }
            #endregion

            #region Firmware
            {
                var paremeterItemXml = document.CreateElement("Firmware");
                paremeterItemXml.InnerText = "\r\n";
                paremeterXml.AppendChild(paremeterItemXml);
            }
            #endregion

            #region FirmwareCom
            {
                var paremeterItemXml = document.CreateElement("FirmwareCom");
                paremeterItemXml.InnerText = "\r\n";
                paremeterXml.AppendChild(paremeterItemXml);
            }
            #endregion

            #region FirmwareRevision
            {
                var paremeterItemXml = document.CreateElement("FirmwareRevision");
                paremeterItemXml.InnerText = "\r\n";
                paremeterXml.AppendChild(paremeterItemXml);
            }
            #endregion

            #region FirmwareComRevision
            {
                var paremeterItemXml = document.CreateElement("FirmwareComRevision");
                paremeterItemXml.InnerText = "\r\n";
                paremeterXml.AppendChild(paremeterItemXml);
            }
            #endregion

            #region OrderNumber
            {
                var paremeterItemXml = document.CreateElement("OrderNumber");
                var data = document.CreateNode(XmlNodeType.CDATA, string.Empty, null);
                data.InnerText = string.Empty;
                paremeterItemXml.AppendChild(data);
                paremeterXml.AppendChild(paremeterItemXml);
            }
            #endregion

            #region SerialNumber
            {
                var paremeterItemXml = document.CreateElement("SerialNumber");
                var data = document.CreateNode(XmlNodeType.CDATA, string.Empty, null);
                data.InnerText = string.Empty;
                paremeterItemXml.AppendChild(data);
                paremeterXml.AppendChild(paremeterItemXml);
            }
            #endregion

            #region PlantIdentifier
            {
                var paremeterItemXml = document.CreateElement("PlantIdentifier");
                var data = document.CreateNode(XmlNodeType.CDATA, string.Empty, null);
                data.InnerText = string.Empty;
                paremeterItemXml.AppendChild(data);
                paremeterXml.AppendChild(paremeterItemXml);
            }
            #endregion

            #region LocationIdentifier
            {
                var paremeterItemXml = document.CreateElement("LocationIdentifier");
                var data = document.CreateNode(XmlNodeType.CDATA, string.Empty, null);
                data.InnerText = string.Empty;
                paremeterItemXml.AppendChild(data);
                paremeterXml.AppendChild(paremeterItemXml);
            }
            #endregion

            #region Slot1
            {
                var paremeterItemXml = document.CreateElement("Slot1");
                var value = document.CreateTextNode("0");
                paremeterItemXml.AppendChild(value);
                paremeterXml.AppendChild(paremeterItemXml);
            }
            #endregion

            #region Slot2
            {
                var paremeterItemXml = document.CreateElement("Slot2");
                var value = document.CreateTextNode("0");
                paremeterItemXml.AppendChild(value);
                paremeterXml.AppendChild(paremeterItemXml);
            }
            #endregion

            #region FirmwareSlot1
            {
                var paremeterItemXml = document.CreateElement("FirmwareSlot1");
                paremeterItemXml.InnerText = "V0.0.0";
                paremeterXml.AppendChild(paremeterItemXml);
            }
            #endregion

            #region FirmwareSlot2
            {
                var paremeterItemXml = document.CreateElement("FirmwareSlot2");
                paremeterItemXml.InnerText = "V0.0.0";
                paremeterXml.AppendChild(paremeterItemXml);
            }
            #endregion

            #region DeviceInterface
            {
                var paremeterItemXml = document.CreateElement("DeviceInterface");
                paremeterItemXml.InnerText = "0";
                paremeterXml.AppendChild(paremeterItemXml);
            }
            #endregion

            #region PCInterface
            {
                var paremeterItemXml = document.CreateElement("PCInterface");
                paremeterItemXml.InnerText = "Ethernet";
                paremeterXml.AppendChild(paremeterItemXml);
            }
            #endregion

            #region ModbusAddress
            {
                var paremeterItemXml = document.CreateElement("ModbusAddress");
                paremeterItemXml.InnerText = modbus;
                paremeterXml.AppendChild(paremeterItemXml);
            }
            #endregion

            #region ExternalIdentifier
            {
                var paremeterItemXml = document.CreateElement("ExternalIdentifier");
                paremeterItemXml.InnerText = "0";
                paremeterXml.AppendChild(paremeterItemXml);
            }
            #endregion

            #region EtuType
            {
                var paremeterItemXml = document.CreateElement("EtuType");
                paremeterItemXml.InnerText = "0";
                paremeterXml.AppendChild(paremeterItemXml);
            }
            #endregion

            #region Profibus
            {
                var paremeterItemXml = document.CreateElement("Profibus");
                var addressXml = document.CreateElement("Address");
                addressXml.InnerText = "0";
                paremeterItemXml.AppendChild(addressXml);
                paremeterXml.AppendChild(paremeterItemXml);
            }
            #endregion

            #region Rs485
            {
                var paremeterItemXml = document.CreateElement("Rs485");
                var baudRateXml = document.CreateElement("BaudRate");
                baudRateXml.InnerText = "0";
                paremeterItemXml.AppendChild(baudRateXml);
                var bitsXml = document.CreateElement("Bits");
                paremeterItemXml.AppendChild(bitsXml);
                var protocolXml = document.CreateElement("Protocol");
                protocolXml.InnerText = "0";
                paremeterItemXml.AppendChild(protocolXml);
                paremeterXml.AppendChild(paremeterItemXml);
            }
            #endregion

            #region Tcp
            {
                var paremeterItemXml = document.CreateElement("Tcp");
                var addressXml = document.CreateElement("HostAddress");
                addressXml.InnerText = ip;
                paremeterItemXml.AppendChild(addressXml);
                var portXml = document.CreateElement("Port");
                portXml.InnerText = port;
                paremeterItemXml.AppendChild(portXml);
                var gatewayPortXml = document.CreateElement("GatewayPort");
                gatewayPortXml.InnerText = "502";
                paremeterItemXml.AppendChild(gatewayPortXml);
                var gatewayXml = document.CreateElement("Gateway");
                gatewayXml.InnerText = "***************";
                paremeterItemXml.AppendChild(gatewayXml);
                var subnetMaskXml = document.CreateElement("SubnetMask");
                subnetMaskXml.InnerText = "*************";
                paremeterItemXml.AppendChild(subnetMaskXml);
                var macAddressXml = document.CreateElement("MacAddress");
                macAddressXml.InnerText = "00-00-00-00-00-00";
                paremeterItemXml.AppendChild(macAddressXml);
                var protocolXml = document.CreateElement("Protocol");
                protocolXml.InnerText = "ModbusTcp";
                paremeterItemXml.AppendChild(protocolXml);
                paremeterXml.AppendChild(paremeterItemXml);
            }
            #endregion

            document.InsertBefore(declaration, document.DocumentElement);
            document.Save(communicationParameterFile);
        }

        /// <summary>
        /// 创建Properties.xml 文件
        /// </summary>
        /// <param name="modbusDevice"></param>
        /// <param name="propertiesPath"></param>
        /// <param name="typeId"></param>
        public void CreatePropertiesData(AssetInfo modbusDevice, string propertiesPath, string typeId)
        {
            var propertiesFile = Path.Combine(propertiesPath, "Properties.xml");
            XmlDocument document = new XmlDocument();
            XmlDeclaration declaration = document.CreateXmlDeclaration("1.0", "utf-8", null);
            XmlElement projectItemXml = document.CreateElement("ProjectItem");
            document.AppendChild(projectItemXml);
            var typeIdAttr = document.CreateAttribute("typeId");
            typeIdAttr.InnerText = typeId;
            var vessionAttr = document.CreateAttribute("version");
            vessionAttr.InnerText = "1.0";
            projectItemXml.Attributes.Append(typeIdAttr);
            projectItemXml.Attributes.Append(vessionAttr);

            var propertiesXml = document.CreateElement("Properties");
            projectItemXml.AppendChild(propertiesXml);

            #region Name
            {
                var propertyXml = document.CreateElement("Property");
                propertiesXml.AppendChild(propertyXml);
                var nAttr = document.CreateAttribute("n");
                nAttr.InnerText = "Name";
                var pbAttr = document.CreateAttribute("pb");
                pbAttr.InnerText = "Visible";
                propertyXml.Attributes.Append(nAttr);
                propertyXml.Attributes.Append(pbAttr);
                var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                data.Value = modbusDevice.AssetName;
                propertyXml.AppendChild(data);
            }
            #endregion

            #region DeviceType
            {
                var propertyXml = document.CreateElement("Property");
                propertiesXml.AppendChild(propertyXml);
                var nAttr = document.CreateAttribute("n");
                nAttr.InnerText = "DeviceType";
                var pbAttr = document.CreateAttribute("pb");
                pbAttr.InnerText = "Visible";
                propertyXml.Attributes.Append(nAttr);
                propertyXml.Attributes.Append(pbAttr);
                var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                data.InnerText = modbusDevice.AssetName;
                propertyXml.AppendChild(data);
            }
            #endregion

            #region SubDeviceType
            {
                var propertyXml = document.CreateElement("Property");
                propertiesXml.AppendChild(propertyXml);
                var nAttr = document.CreateAttribute("n");
                nAttr.InnerText = "SubDeviceType";
                var pbAttr = document.CreateAttribute("pb");
                pbAttr.InnerText = "Visible";
                propertyXml.Attributes.Append(nAttr);
                propertyXml.Attributes.Append(pbAttr);
                var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                data.InnerText = string.Empty;
                propertyXml.AppendChild(data);
            }
            #endregion

            #region Description
            {
                var propertyXml = document.CreateElement("Property");
                propertiesXml.AppendChild(propertyXml);
                var nAttr = document.CreateAttribute("n");
                nAttr.InnerText = "Description";
                var pbAttr = document.CreateAttribute("pb");
                pbAttr.InnerText = "Visible";
                propertyXml.Attributes.Append(nAttr);
                propertyXml.Attributes.Append(pbAttr);
                var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                data.InnerText = string.Empty;
                propertyXml.AppendChild(data);
            }
            #endregion

            #region Firmware
            {
                var propertyXml = document.CreateElement("Property");
                propertiesXml.AppendChild(propertyXml);
                var nAttr = document.CreateAttribute("n");
                nAttr.InnerText = "Firmware";
                var pbAttr = document.CreateAttribute("pb");
                pbAttr.InnerText = "Visible";
                propertyXml.Attributes.Append(nAttr);
                propertyXml.Attributes.Append(pbAttr);
                var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                data.InnerText = "1.0";
                propertyXml.AppendChild(data);
            }
            #endregion

            #region Protocol
            {
                var propertyXml = document.CreateElement("Property");
                propertiesXml.AppendChild(propertyXml);
                var nAttr = document.CreateAttribute("n");
                nAttr.InnerText = "Protocol";
                var pbAttr = document.CreateAttribute("pb");
                pbAttr.InnerText = "Visible";
                propertyXml.Attributes.Append(nAttr);
                propertyXml.Attributes.Append(pbAttr);
                var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                data.InnerText = "0";
                propertyXml.AppendChild(data);
            }
            #endregion

            #region IPAddress
            {
                var propertyXml = document.CreateElement("Property");
                propertiesXml.AppendChild(propertyXml);
                var nAttr = document.CreateAttribute("n");
                nAttr.InnerText = "IPAddress";
                var pbAttr = document.CreateAttribute("pb");
                pbAttr.InnerText = "Visible";
                propertyXml.Attributes.Append(nAttr);
                propertyXml.Attributes.Append(pbAttr);
                var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                data.InnerText = "0.0.0.0";
                propertyXml.AppendChild(data);
            }
            #endregion

            #region NumberOfCustomCategories
            {
                var propertyXml = document.CreateElement("Property");
                propertiesXml.AppendChild(propertyXml);
                var nAttr = document.CreateAttribute("n");
                nAttr.InnerText = "NumberOfCustomCategories";
                var pbAttr = document.CreateAttribute("pb");
                pbAttr.InnerText = "Visible";
                propertyXml.Attributes.Append(nAttr);
                propertyXml.Attributes.Append(pbAttr);
                var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                data.InnerText = "0";
                propertyXml.AppendChild(data);
            }
            #endregion

            #region NumberOfCustomDataPoints
            {
                var propertyXml = document.CreateElement("Property");
                propertiesXml.AppendChild(propertyXml);
                var nAttr = document.CreateAttribute("n");
                nAttr.InnerText = "NumberOfCustomDataPoints";
                var pbAttr = document.CreateAttribute("pb");
                pbAttr.InnerText = "Visible";
                propertyXml.Attributes.Append(nAttr);
                propertyXml.Attributes.Append(pbAttr);
                var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                data.InnerText = "100";
                propertyXml.AppendChild(data);
            }
            #endregion

            #region Categories
            for (var i = 1; i <= 35; i++)
            {
                #region Category Name
                {
                    var propertyXml = document.CreateElement("Property");
                    propertiesXml.AppendChild(propertyXml);
                    var nAttr = document.CreateAttribute("n");
                    nAttr.InnerText = $"Category{i}_Name";
                    var pbAttr = document.CreateAttribute("pb");
                    pbAttr.InnerText = "Visible";
                    propertyXml.Attributes.Append(nAttr);
                    propertyXml.Attributes.Append(pbAttr);
                    var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                    data.InnerText = $"CAT{i}";
                    propertyXml.AppendChild(data);
                }
                #endregion

                #region English
                {
                    var propertyXml = document.CreateElement("Property");
                    propertiesXml.AppendChild(propertyXml);
                    var nAttr = document.CreateAttribute("n");
                    nAttr.InnerText = $"Category{i}_Language_English";
                    var pbAttr = document.CreateAttribute("pb");
                    pbAttr.InnerText = "Visible";
                    propertyXml.Attributes.Append(nAttr);
                    propertyXml.Attributes.Append(pbAttr);
                    var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                    data.InnerText = $"Category {i}";
                    propertyXml.AppendChild(data);
                }
                #endregion

                #region German
                {
                    var propertyXml = document.CreateElement("Property");
                    propertiesXml.AppendChild(propertyXml);
                    var nAttr = document.CreateAttribute("n");
                    nAttr.InnerText = $"Category{i}_Language_German";
                    var pbAttr = document.CreateAttribute("pb");
                    pbAttr.InnerText = "Visible";
                    propertyXml.Attributes.Append(nAttr);
                    propertyXml.Attributes.Append(pbAttr);
                    var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                    data.InnerText = $"Kategorie {i}";
                    propertyXml.AppendChild(data);
                }
                #endregion
            }
            #endregion

            #region DataPoints
            for (var i = 1; i <= 100; i++)
            {
                #region Data Point Name
                {
                    var propertyXml = document.CreateElement("Property");
                    propertiesXml.AppendChild(propertyXml);
                    var nAttr = document.CreateAttribute("n");
                    nAttr.InnerText = $"DataPoint{i}_Name";
                    var pbAttr = document.CreateAttribute("pb");
                    pbAttr.InnerText = "Visible";
                    propertyXml.Attributes.Append(nAttr);
                    propertyXml.Attributes.Append(pbAttr);
                    var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                    data.InnerText = $"DP{i}";
                    propertyXml.AppendChild(data);
                }
                #endregion

                #region English
                {
                    var propertyXml = document.CreateElement("Property");
                    propertiesXml.AppendChild(propertyXml);
                    var nAttr = document.CreateAttribute("n");
                    nAttr.InnerText = $"DataPoint{i}_Language_English";
                    var pbAttr = document.CreateAttribute("pb");
                    pbAttr.InnerText = "Visible";
                    propertyXml.Attributes.Append(nAttr);
                    propertyXml.Attributes.Append(pbAttr);
                    var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                    data.InnerText = $"Data point {i}";
                    propertyXml.AppendChild(data);
                }
                #endregion

                #region German
                {
                    var propertyXml = document.CreateElement("Property");
                    propertiesXml.AppendChild(propertyXml);
                    var nAttr = document.CreateAttribute("n");
                    nAttr.InnerText = $"DataPoint{i}_Language_German";
                    var pbAttr = document.CreateAttribute("pb");
                    pbAttr.InnerText = "Visible";
                    propertyXml.Attributes.Append(nAttr);
                    propertyXml.Attributes.Append(pbAttr);
                    var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                    data.InnerText = $"Datenpunkt {i}";
                    propertyXml.AppendChild(data);
                }
                #endregion

                #region UniformName
                {
                    var propertyXml = document.CreateElement("Property");
                    propertiesXml.AppendChild(propertyXml);
                    var nAttr = document.CreateAttribute("n");
                    nAttr.InnerText = $"DataPoint{i}_UniformName";
                    var pbAttr = document.CreateAttribute("pb");
                    pbAttr.InnerText = "Visible";
                    propertyXml.Attributes.Append(nAttr);
                    propertyXml.Attributes.Append(pbAttr);
                    var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                    data.InnerText = string.Empty;
                    propertyXml.AppendChild(data);
                }
                #endregion

                #region CategoryName
                {
                    var propertyXml = document.CreateElement("Property");
                    propertiesXml.AppendChild(propertyXml);
                    var nAttr = document.CreateAttribute("n");
                    nAttr.InnerText = $"DataPoint{i}_CategoryName";
                    var pbAttr = document.CreateAttribute("pb");
                    pbAttr.InnerText = "Visible";
                    propertyXml.Attributes.Append(nAttr);
                    propertyXml.Attributes.Append(pbAttr);
                    var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                    data.InnerText = string.Empty;
                    propertyXml.AppendChild(data);
                }
                #endregion

                #region RegisterAddress
                {
                    var propertyXml = document.CreateElement("Property");
                    propertiesXml.AppendChild(propertyXml);
                    var nAttr = document.CreateAttribute("n");
                    nAttr.InnerText = $"DataPoint{i}_RegisterAddress";
                    var pbAttr = document.CreateAttribute("pb");
                    pbAttr.InnerText = "Visible";
                    propertyXml.Attributes.Append(nAttr);
                    propertyXml.Attributes.Append(pbAttr);
                    var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                    data.InnerText = (2 + i).ToString();
                    propertyXml.AppendChild(data);
                }
                #endregion

                #region RegisterCount
                {
                    var propertyXml = document.CreateElement("Property");
                    propertiesXml.AppendChild(propertyXml);
                    var nAttr = document.CreateAttribute("n");
                    nAttr.InnerText = $"DataPoint{i}_RegisterCount";
                    var pbAttr = document.CreateAttribute("pb");
                    pbAttr.InnerText = "Visible";
                    propertyXml.Attributes.Append(nAttr);
                    propertyXml.Attributes.Append(pbAttr);
                    var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                    data.InnerText = "1";
                    propertyXml.AppendChild(data);
                }
                #endregion

                #region ByteOffset
                {
                    var propertyXml = document.CreateElement("Property");
                    propertiesXml.AppendChild(propertyXml);
                    var nAttr = document.CreateAttribute("n");
                    nAttr.InnerText = $"DataPoint{i}_ByteOffset";
                    var pbAttr = document.CreateAttribute("pb");
                    pbAttr.InnerText = "Visible";
                    propertyXml.Attributes.Append(nAttr);
                    propertyXml.Attributes.Append(pbAttr);
                    var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                    data.InnerText = "0";
                    propertyXml.AppendChild(data);
                }
                #endregion

                #region ByteCount
                {
                    var propertyXml = document.CreateElement("Property");
                    propertiesXml.AppendChild(propertyXml);
                    var nAttr = document.CreateAttribute("n");
                    nAttr.InnerText = $"DataPoint{i}_ByteCount";
                    var pbAttr = document.CreateAttribute("pb");
                    pbAttr.InnerText = "Visible";
                    propertyXml.Attributes.Append(nAttr);
                    propertyXml.Attributes.Append(pbAttr);
                    var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                    data.InnerText = "0";
                    propertyXml.AppendChild(data);
                }
                #endregion

                #region BitOffset
                {
                    var propertyXml = document.CreateElement("Property");
                    propertiesXml.AppendChild(propertyXml);
                    var nAttr = document.CreateAttribute("n");
                    nAttr.InnerText = $"DataPoint{i}_BitOffset";
                    var pbAttr = document.CreateAttribute("pb");
                    pbAttr.InnerText = "Visible";
                    propertyXml.Attributes.Append(nAttr);
                    propertyXml.Attributes.Append(pbAttr);
                    var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                    data.InnerText = "0";
                    propertyXml.AppendChild(data);
                }
                #endregion

                #region BitCount
                {
                    var propertyXml = document.CreateElement("Property");
                    propertiesXml.AppendChild(propertyXml);
                    var nAttr = document.CreateAttribute("n");
                    nAttr.InnerText = $"DataPoint{i}_BitCount";
                    var pbAttr = document.CreateAttribute("pb");
                    pbAttr.InnerText = "Visible";
                    propertyXml.Attributes.Append(nAttr);
                    propertyXml.Attributes.Append(pbAttr);
                    var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                    data.InnerText = "0";
                    propertyXml.AppendChild(data);
                }
                #endregion

                #region ValueFormat
                {
                    var propertyXml = document.CreateElement("Property");
                    propertiesXml.AppendChild(propertyXml);
                    var nAttr = document.CreateAttribute("n");
                    nAttr.InnerText = $"DataPoint{i}_ValueFormat";
                    var pbAttr = document.CreateAttribute("pb");
                    pbAttr.InnerText = "Visible";
                    propertyXml.Attributes.Append(nAttr);
                    propertyXml.Attributes.Append(pbAttr);
                    var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                    data.InnerText = "1";
                    propertyXml.AppendChild(data);
                }
                #endregion

                #region ScaleFactor
                {
                    var propertyXml = document.CreateElement("Property");
                    propertiesXml.AppendChild(propertyXml);
                    var nAttr = document.CreateAttribute("n");
                    nAttr.InnerText = $"DataPoint{i}_ScaleFactor";
                    var pbAttr = document.CreateAttribute("pb");
                    pbAttr.InnerText = "Visible";
                    propertyXml.Attributes.Append(nAttr);
                    propertyXml.Attributes.Append(pbAttr);
                    var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                    data.InnerText = "1";
                    propertyXml.AppendChild(data);
                }
                #endregion

                #region DisplayFormat
                {
                    var propertyXml = document.CreateElement("Property");
                    propertiesXml.AppendChild(propertyXml);
                    var nAttr = document.CreateAttribute("n");
                    nAttr.InnerText = $"DataPoint{i}_DisplayFormat";
                    var pbAttr = document.CreateAttribute("pb");
                    pbAttr.InnerText = "Visible";
                    propertyXml.Attributes.Append(nAttr);
                    propertyXml.Attributes.Append(pbAttr);
                    var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                    data.InnerText = "1";
                    propertyXml.AppendChild(data);
                }
                #endregion

                #region Unit
                {
                    var propertyXml = document.CreateElement("Property");
                    propertiesXml.AppendChild(propertyXml);
                    var nAttr = document.CreateAttribute("n");
                    nAttr.InnerText = $"DataPoint{i}_Unit";
                    var pbAttr = document.CreateAttribute("pb");
                    pbAttr.InnerText = "Visible";
                    propertyXml.Attributes.Append(nAttr);
                    propertyXml.Attributes.Append(pbAttr);
                    var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                    data.InnerText = string.Empty;
                    propertyXml.AppendChild(data);
                }
                #endregion

                #region RuntimeSettingArchiving
                {
                    var propertyXml = document.CreateElement("Property");
                    propertiesXml.AppendChild(propertyXml);
                    var nAttr = document.CreateAttribute("n");
                    nAttr.InnerText = $"DataPoint{i}_RuntimeSettingArchiving";
                    var pbAttr = document.CreateAttribute("pb");
                    pbAttr.InnerText = "Visible";
                    propertyXml.Attributes.Append(nAttr);
                    propertyXml.Attributes.Append(pbAttr);
                    var data = document.CreateNode(XmlNodeType.CDATA, modbusDevice.AssetName, null);
                    data.InnerText = "0";
                    propertyXml.AppendChild(data);
                }
                #endregion
            }
            #endregion

            document.InsertBefore(declaration, document.DocumentElement);
            document.Save(propertiesFile);
        }

        /// <summary>
        /// 创建Properties.xml文件
        /// </summary>
        /// <param name="assetId"></param>
        /// <param name="userName"></param>
        /// <param name="propertiesPath"></param>
        /// <returns></returns>
        public async Task ChangeThirdDeviceBySplxFile(int assetId, string userName, string propertiesPath)
        {
            var assetInfo = await _db.Queryable<AssetInfo>().FirstAsync(a => a.Id == assetId);

            if (assetInfo == null || string.IsNullOrEmpty(assetInfo.ThirdPartCode))
            {
                return;
            }

            var dataPointList = await _assetDataPointInfoServer.GetDataPoints(assetId);

            var thirdPartModel = await _db.Queryable<ThirdModelConfig>().FirstAsync(t => t.Code == assetInfo.ThirdPartCode);

            var template = JsonConvert.DeserializeObject<ThreePartModelTemplate>(thirdPartModel.JsonData ?? "");

            var properties = new List<PropertyInfo>();

            if (template != null && template.Treeview != null && template.Treeview.Any())
            {
                foreach (var item in template.Treeview!)
                {
                    //获取子集集合
                    if (item.SubGroups != null && item.SubGroups!.Any())
                    {
                        foreach (var _item in item.SubGroups!)
                        {
                            if (_item.Properties != null && _item.Properties.Any())
                            {
                                properties.AddRange(_item.Properties);
                            }
                        }
                    }

                    if (item.Properties != null && item.Properties.Any())
                    {
                        properties.AddRange(item.Properties);
                    }
                }
            }

            var propertiesFile = Path.Combine(propertiesPath, "Properties.xml");
            XmlDocument document = new XmlDocument();
            XmlDeclaration declaration = document.CreateXmlDeclaration("1.0", "utf-8", null);
            XmlElement projectItemXml = document.CreateElement("ProjectItem");
            document.AppendChild(projectItemXml);
            var typeIdAttr = document.CreateAttribute("typeId");
            typeIdAttr.InnerText = "41";
            var vessionAttr = document.CreateAttribute("version");
            vessionAttr.InnerText = "1.0";
            projectItemXml.Attributes.Append(typeIdAttr);
            projectItemXml.Attributes.Append(vessionAttr);

            var propertiesXml = document.CreateElement("Properties");
            projectItemXml.AppendChild(propertiesXml);

            #region Name
            {
                var propertyXml = document.CreateElement("Property");
                propertiesXml.AppendChild(propertyXml);
                var nAttr = document.CreateAttribute("n");
                nAttr.InnerText = "Name";
                var pbAttr = document.CreateAttribute("pb");
                pbAttr.InnerText = "Visible";
                propertyXml.Attributes.Append(nAttr);
                propertyXml.Attributes.Append(pbAttr);
                var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                data.Value = assetInfo.AssetName;
                propertyXml.AppendChild(data);
            }
            #endregion

            #region DeviceType
            {
                var propertyXml = document.CreateElement("Property");
                propertiesXml.AppendChild(propertyXml);
                var nAttr = document.CreateAttribute("n");
                nAttr.InnerText = "DeviceType";
                var pbAttr = document.CreateAttribute("pb");
                pbAttr.InnerText = "Visible";
                propertyXml.Attributes.Append(nAttr);
                propertyXml.Attributes.Append(pbAttr);
                var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                data.InnerText = assetInfo.AssetName;
                propertyXml.AppendChild(data);
            }
            #endregion

            #region SubDeviceType
            {
                var propertyXml = document.CreateElement("Property");
                propertiesXml.AppendChild(propertyXml);
                var nAttr = document.CreateAttribute("n");
                nAttr.InnerText = "SubDeviceType";
                var pbAttr = document.CreateAttribute("pb");
                pbAttr.InnerText = "Visible";
                propertyXml.Attributes.Append(nAttr);
                propertyXml.Attributes.Append(pbAttr);
                var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                data.InnerText = string.Empty;
                propertyXml.AppendChild(data);
            }
            #endregion

            #region Description
            {
                var propertyXml = document.CreateElement("Property");
                propertiesXml.AppendChild(propertyXml);
                var nAttr = document.CreateAttribute("n");
                nAttr.InnerText = "Description";
                var pbAttr = document.CreateAttribute("pb");
                pbAttr.InnerText = "Visible";
                propertyXml.Attributes.Append(nAttr);
                propertyXml.Attributes.Append(pbAttr);
                var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                data.InnerText = string.Empty;
                propertyXml.AppendChild(data);
            }
            #endregion

            #region Firmware
            {
                var propertyXml = document.CreateElement("Property");
                propertiesXml.AppendChild(propertyXml);
                var nAttr = document.CreateAttribute("n");
                nAttr.InnerText = "Firmware";
                var pbAttr = document.CreateAttribute("pb");
                pbAttr.InnerText = "Visible";
                propertyXml.Attributes.Append(nAttr);
                propertyXml.Attributes.Append(pbAttr);
                var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                data.InnerText = "1.0";
                propertyXml.AppendChild(data);
            }
            #endregion

            #region Protocol
            {
                var propertyXml = document.CreateElement("Property");
                propertiesXml.AppendChild(propertyXml);
                var nAttr = document.CreateAttribute("n");
                nAttr.InnerText = "Protocol";
                var pbAttr = document.CreateAttribute("pb");
                pbAttr.InnerText = "Visible";
                propertyXml.Attributes.Append(nAttr);
                propertyXml.Attributes.Append(pbAttr);
                var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                data.InnerText = "0";
                propertyXml.AppendChild(data);
            }
            #endregion

            #region IPAddress
            {
                var propertyXml = document.CreateElement("Property");
                propertiesXml.AppendChild(propertyXml);
                var nAttr = document.CreateAttribute("n");
                nAttr.InnerText = "IPAddress";
                var pbAttr = document.CreateAttribute("pb");
                pbAttr.InnerText = "Visible";
                propertyXml.Attributes.Append(nAttr);
                propertyXml.Attributes.Append(pbAttr);
                var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                data.InnerText = "0.0.0.0";
                propertyXml.AppendChild(data);
            }
            #endregion

            #region NumberOfCustomCategories
            {
                var propertyXml = document.CreateElement("Property");
                propertiesXml.AppendChild(propertyXml);
                var nAttr = document.CreateAttribute("n");
                nAttr.InnerText = "NumberOfCustomCategories";
                var pbAttr = document.CreateAttribute("pb");
                pbAttr.InnerText = "Visible";
                propertyXml.Attributes.Append(nAttr);
                propertyXml.Attributes.Append(pbAttr);
                var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                data.InnerText = "0";
                propertyXml.AppendChild(data);
            }
            #endregion

            #region NumberOfCustomDataPoints
            {
                var propertyXml = document.CreateElement("Property");
                propertiesXml.AppendChild(propertyXml);
                var nAttr = document.CreateAttribute("n");
                nAttr.InnerText = "NumberOfCustomDataPoints";
                var pbAttr = document.CreateAttribute("pb");
                pbAttr.InnerText = "Visible";
                propertyXml.Attributes.Append(nAttr);
                propertyXml.Attributes.Append(pbAttr);
                var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                data.InnerText = properties.Count.ToString();
                propertyXml.AppendChild(data);
            }
            #endregion

            #region Categories
            for (var i = 1; i <= 35; i++)
            {
                #region Category Name
                {
                    var propertyXml = document.CreateElement("Property");
                    propertiesXml.AppendChild(propertyXml);
                    var nAttr = document.CreateAttribute("n");
                    nAttr.InnerText = $"Category{i}_Name";
                    var pbAttr = document.CreateAttribute("pb");
                    pbAttr.InnerText = "Visible";
                    propertyXml.Attributes.Append(nAttr);
                    propertyXml.Attributes.Append(pbAttr);
                    var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                    data.InnerText = $"CAT{i}";
                    propertyXml.AppendChild(data);
                }
                #endregion
                #region English
                {
                    var propertyXml = document.CreateElement("Property");
                    propertiesXml.AppendChild(propertyXml);
                    var nAttr = document.CreateAttribute("n");
                    nAttr.InnerText = $"Category{i}_Language_English";
                    var pbAttr = document.CreateAttribute("pb");
                    pbAttr.InnerText = "Visible";
                    propertyXml.Attributes.Append(nAttr);
                    propertyXml.Attributes.Append(pbAttr);
                    var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                    data.InnerText = $"Category {i}";
                    propertyXml.AppendChild(data);
                }
                #endregion
                #region German
                {
                    var propertyXml = document.CreateElement("Property");
                    propertiesXml.AppendChild(propertyXml);
                    var nAttr = document.CreateAttribute("n");
                    nAttr.InnerText = $"Category{i}_Language_German";
                    var pbAttr = document.CreateAttribute("pb");
                    pbAttr.InnerText = "Visible";
                    propertyXml.Attributes.Append(nAttr);
                    propertyXml.Attributes.Append(pbAttr);
                    var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                    data.InnerText = $"Kategorie {i}";
                    propertyXml.AppendChild(data);
                }
                #endregion
            }
            #endregion

            #region DataPoints

            var generalDataPoints = await _db.Queryable<AssetGeneralDataPoints>().Where(a => a.AssetId == assetId).ToListAsync();
            var dataPointConfigs = dataPointList;

            for (var i = 1; i <= 100; i++)
            {
                string dpName = $"DP{i}";
                int registerAddress = 0;
                int registerCount = 2;
                int byteOffset = 0;
                int byteCount = 0;
                int bitOffset = 0;
                int bitCount = 0;
                int valueFormat = 0;
                decimal factor = 0m;
                int displayFormat = 0;
                string unit = string.Empty;

                #region Property
                if (properties.Count >= i)
                {
                    var p = properties[i - 1];
                    var dataPointConfig = dataPointConfigs.FirstOrDefault(d => d.UdcCode == p.PropertyName);
                    if (dataPointConfig != null)
                    {
                        var gdp = generalDataPoints.FirstOrDefault(g => g.Index == i);
                        if (gdp == null)
                        {
                            gdp = new AssetGeneralDataPoints()
                            {
                                AssetId = assetId,
                                CreatedBy = userName,
                                CreatedTime = DateTime.Now,
                                Index = i,
                            };
                            generalDataPoints.Add(gdp);
                        }
                        if ("TRUE".Equals(p.Active))
                        {
                            gdp.IsActive = true;
                            registerAddress = int.Parse(p.Register ?? "0");

                            if (p.SelectedTransformationType != null)
                            {
                                SetRegisterCountAndValueFormat(p.SelectedTransformationType, out registerCount, out valueFormat, out displayFormat);
                            }

                            if (!decimal.TryParse(p.Factor, out factor))
                            {
                                factor = 1m;
                            }
                            unit = p.Unit ?? string.Empty;
                            var dp = dataPointList.FirstOrDefault(d => $"DP{i}".Equals(d.UdcCode));

                        }
                        else
                        {
                            gdp.IsActive = false;
                        }

                        gdp.Code = dataPointConfig.Code;
                        gdp.UdcCode = dpName = dataPointConfig.Code;
                        gdp.UpdatedBy = userName;
                        gdp.UpdatedTime = DateTime.Now;
                    }

                    #region Data Point Name
                    {
                        var propertyXml = document.CreateElement("Property");
                        propertiesXml.AppendChild(propertyXml);
                        var nAttr = document.CreateAttribute("n");
                        nAttr.InnerText = $"DataPoint{i}_Name";
                        propertyXml.Attributes.Append(nAttr);

                        var pbAttr = document.CreateAttribute("pb");
                        pbAttr.InnerText = "Visible";
                        propertyXml.Attributes.Append(pbAttr);

                        var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                        data.InnerText = dpName;
                        propertyXml.AppendChild(data);
                    }
                    #endregion

                    #region English
                    {
                        var propertyXml = document.CreateElement("Property");
                        propertiesXml.AppendChild(propertyXml);
                        var nAttr = document.CreateAttribute("n");
                        nAttr.InnerText = $"DataPoint{i}_Language_English";
                        propertyXml.Attributes.Append(nAttr);

                        var pbAttr = document.CreateAttribute("pb");
                        pbAttr.InnerText = "Visible";
                        propertyXml.Attributes.Append(pbAttr);

                        var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                        data.InnerText = $"{p.PropertyName}";
                        propertyXml.AppendChild(data);
                    }
                    #endregion

                    #region German
                    {
                        var propertyXml = document.CreateElement("Property");
                        propertiesXml.AppendChild(propertyXml);
                        var nAttr = document.CreateAttribute("n");
                        nAttr.InnerText = $"DataPoint{i}_Language_German";
                        propertyXml.Attributes.Append(nAttr);

                        var pbAttr = document.CreateAttribute("pb");
                        pbAttr.InnerText = "Visible";
                        propertyXml.Attributes.Append(pbAttr);

                        var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                        data.InnerText = $"{p.DescriptionInGerman}";
                        propertyXml.AppendChild(data);
                    }
                    #endregion

                    #region UniformName
                    {
                        var propertyXml = document.CreateElement("Property");
                        propertiesXml.AppendChild(propertyXml);
                        var nAttr = document.CreateAttribute("n");
                        nAttr.InnerText = $"DataPoint{i}_UniformName";
                        propertyXml.Attributes.Append(nAttr);

                        var pbAttr = document.CreateAttribute("pb");
                        pbAttr.InnerText = "Visible";
                        propertyXml.Attributes.Append(pbAttr);

                        var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                        data.InnerText = string.Empty;
                        propertyXml.AppendChild(data);
                    }
                    #endregion

                    #region CategoryName
                    {
                        var propertyXml = document.CreateElement("Property");
                        propertiesXml.AppendChild(propertyXml);
                        var nAttr = document.CreateAttribute("n");
                        nAttr.InnerText = $"DataPoint{i}_CategoryName";
                        propertyXml.Attributes.Append(nAttr);
                        var pbAttr = document.CreateAttribute("pb");
                        pbAttr.InnerText = "Visible";
                        propertyXml.Attributes.Append(pbAttr);
                        var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                        data.InnerText = string.Empty;
                        propertyXml.AppendChild(data);
                    }
                    #endregion

                    #region RegisterAddress
                    {
                        var propertyXml = document.CreateElement("Property");
                        propertiesXml.AppendChild(propertyXml);
                        var nAttr = document.CreateAttribute("n");
                        nAttr.InnerText = $"DataPoint{i}_RegisterAddress";
                        propertyXml.Attributes.Append(nAttr);

                        var pbAttr = document.CreateAttribute("pb");
                        pbAttr.InnerText = "Visible";
                        propertyXml.Attributes.Append(pbAttr);

                        var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                        data.InnerText = registerAddress.ToString();
                        propertyXml.AppendChild(data);
                    }
                    #endregion

                    #region RegisterCount
                    {
                        var propertyXml = document.CreateElement("Property");
                        propertiesXml.AppendChild(propertyXml);
                        var nAttr = document.CreateAttribute("n");
                        nAttr.InnerText = $"DataPoint{i}_RegisterCount";
                        propertyXml.Attributes.Append(nAttr);

                        var pbAttr = document.CreateAttribute("pb");
                        pbAttr.InnerText = "Visible";
                        propertyXml.Attributes.Append(pbAttr);

                        var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                        data.InnerText = registerCount.ToString();
                        propertyXml.AppendChild(data);
                    }
                    #endregion

                    #region ByteOffset
                    {
                        var propertyXml = document.CreateElement("Property");
                        propertiesXml.AppendChild(propertyXml);
                        var nAttr = document.CreateAttribute("n");
                        nAttr.InnerText = $"DataPoint{i}_ByteOffset";
                        propertyXml.Attributes.Append(nAttr);

                        var pbAttr = document.CreateAttribute("pb");
                        pbAttr.InnerText = "Visible";
                        propertyXml.Attributes.Append(pbAttr);

                        var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                        data.InnerText = byteOffset.ToString();
                        propertyXml.AppendChild(data);
                    }
                    #endregion

                    #region ByteCount
                    {
                        var propertyXml = document.CreateElement("Property");
                        propertiesXml.AppendChild(propertyXml);
                        var nAttr = document.CreateAttribute("n");
                        nAttr.InnerText = $"DataPoint{i}_ByteCount";
                        propertyXml.Attributes.Append(nAttr);

                        var pbAttr = document.CreateAttribute("pb");
                        pbAttr.InnerText = "Visible";
                        propertyXml.Attributes.Append(pbAttr);

                        var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                        data.InnerText = byteCount.ToString();
                        propertyXml.AppendChild(data);
                    }
                    #endregion

                    #region BitOffset
                    {
                        var propertyXml = document.CreateElement("Property");
                        propertiesXml.AppendChild(propertyXml);
                        var nAttr = document.CreateAttribute("n");
                        nAttr.InnerText = $"DataPoint{i}_BitOffset";
                        propertyXml.Attributes.Append(nAttr);

                        var pbAttr = document.CreateAttribute("pb");
                        pbAttr.InnerText = "Visible";
                        propertyXml.Attributes.Append(pbAttr);

                        var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                        data.InnerText = bitOffset.ToString();
                        propertyXml.AppendChild(data);
                    }
                    #endregion

                    #region BitCount
                    {
                        var propertyXml = document.CreateElement("Property");
                        propertiesXml.AppendChild(propertyXml);
                        var nAttr = document.CreateAttribute("n");
                        nAttr.InnerText = $"DataPoint{i}_BitCount";
                        propertyXml.Attributes.Append(nAttr);

                        var pbAttr = document.CreateAttribute("pb");
                        pbAttr.InnerText = "Visible";
                        propertyXml.Attributes.Append(pbAttr);

                        var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                        data.InnerText = bitCount.ToString();
                        propertyXml.AppendChild(data);
                    }
                    #endregion

                    #region ValueFormat
                    {
                        var propertyXml = document.CreateElement("Property");
                        propertiesXml.AppendChild(propertyXml);
                        var nAttr = document.CreateAttribute("n");
                        nAttr.InnerText = $"DataPoint{i}_ValueFormat";
                        propertyXml.Attributes.Append(nAttr);

                        var pbAttr = document.CreateAttribute("pb");
                        pbAttr.InnerText = "Visible";
                        propertyXml.Attributes.Append(pbAttr);

                        var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                        data.InnerText = valueFormat.ToString();
                        propertyXml.AppendChild(data);
                    }
                    #endregion

                    #region ScaleFactor
                    {
                        var propertyXml = document.CreateElement("Property");
                        propertiesXml.AppendChild(propertyXml);
                        var nAttr = document.CreateAttribute("n");
                        nAttr.InnerText = $"DataPoint{i}_ScaleFactor";
                        propertyXml.Attributes.Append(nAttr);

                        var pbAttr = document.CreateAttribute("pb");
                        pbAttr.InnerText = "Visible";
                        propertyXml.Attributes.Append(pbAttr);

                        var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                        data.InnerText = factor.ToString();
                        propertyXml.AppendChild(data);
                    }
                    #endregion

                    #region DisplayFormat
                    {
                        var propertyXml = document.CreateElement("Property");
                        propertiesXml.AppendChild(propertyXml);
                        var nAttr = document.CreateAttribute("n");
                        nAttr.InnerText = $"DataPoint{i}_DisplayFormat";
                        propertyXml.Attributes.Append(nAttr);

                        var pbAttr = document.CreateAttribute("pb");
                        pbAttr.InnerText = "Visible";
                        propertyXml.Attributes.Append(pbAttr);

                        var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                        data.InnerText = displayFormat.ToString();
                        propertyXml.AppendChild(data);
                    }
                    #endregion

                    #region Unit
                    {
                        var propertyXml = document.CreateElement("Property");
                        propertiesXml.AppendChild(propertyXml);
                        var nAttr = document.CreateAttribute("n");
                        nAttr.InnerText = $"DataPoint{i}_Unit";
                        propertyXml.Attributes.Append(nAttr);

                        var pbAttr = document.CreateAttribute("pb");
                        pbAttr.InnerText = "Visible";
                        propertyXml.Attributes.Append(pbAttr);

                        var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                        data.InnerText = unit;
                        propertyXml.AppendChild(data);
                    }
                    #endregion

                    #region RuntimeSettingArchiving
                    {
                        var propertyXml = document.CreateElement("Property");
                        propertiesXml.AppendChild(propertyXml);
                        var nAttr = document.CreateAttribute("n");
                        nAttr.InnerText = $"DataPoint{i}_RuntimeSettingArchiving";
                        propertyXml.Attributes.Append(nAttr);

                        var pbAttr = document.CreateAttribute("pb");
                        pbAttr.InnerText = "Visible";
                        propertyXml.Attributes.Append(pbAttr);

                        var data = document.CreateNode(XmlNodeType.CDATA, assetInfo.AssetName, null);
                        data.InnerText = "0";
                        propertyXml.AppendChild(data);
                    }
                    #endregion
                }
                #endregion
            }

            await _db.Storageable(generalDataPoints).ExecuteCommandAsync();
            #endregion

            document.InsertBefore(declaration, document.DocumentElement);
            document.Save(propertiesFile);
        }

        /// <summary>
        /// 返回对应类型的注册的值
        /// </summary>
        /// <param name="model"></param>
        /// <param name="registerCount"></param>
        /// <param name="valueFormat"></param>
        /// <param name="displayFormat"></param>
        public void SetRegisterCountAndValueFormat(AttributeModel model, out int registerCount, out int valueFormat, out int displayFormat)
        {
            displayFormat = 0;
            valueFormat = 0;
            registerCount = 0;
            switch (model.TransformationDataType)
            {
                case "int16":
                    {
                        registerCount = 1;
                        valueFormat = 1;
                        displayFormat = 1;
                    }
                    break;

                case "int32":
                    {
                        registerCount = 2;
                        valueFormat = 1;
                        displayFormat = 1;
                    }
                    break;

                case "int64":
                    {
                        registerCount = 4;
                        valueFormat = 1;
                        displayFormat = 1;
                    }
                    break;

                case "uint16":
                    {
                        registerCount = 1;
                        valueFormat = 2;
                        displayFormat = 1;
                    }
                    break;

                case "uint32":
                    {
                        registerCount = 2;
                        valueFormat = 2;
                        displayFormat = 1;
                    }
                    break;

                case "uint64":
                    {
                        registerCount = 4;
                        valueFormat = 2;
                        displayFormat = 1;
                    }
                    break;

                case "float":
                    {
                        registerCount = 2;
                        valueFormat = 0;
                        displayFormat = 0;
                    }
                    break;

                case "double":
                    {
                        registerCount = 4;
                        valueFormat = 0;
                        displayFormat = 0;
                    }
                    break;

                case "string":
                    {
                        registerCount = 36;
                        valueFormat = 3;
                        displayFormat = 4;
                    }
                    break;

                case "boolean":
                    {
                        registerCount = 1;
                        valueFormat = 2;
                        displayFormat = 3;
                    }
                    break;

                case "float with timestamp":
                    {
                        registerCount = 2;
                        valueFormat = 4;
                        displayFormat = 5;
                    }
                    break;
            }
        }

        /// <summary>
        /// 创建CloudData.xml文件
        /// </summary>
        /// <param name="assetInfo"></param>
        /// <param name="unPackageTempPath"></param>
        /// <param name="typeId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task CreateCloudData(AssetInfo? assetInfo, string unPackageTempPath, string typeId, string id)
        {
            string statusGroupName = "status";
            string statusTimePeriod = "1";
            string measurementGroupName = "measurement";
            string measurementTimePeriod = "5";
            List<AssetDataPointInfo> tempDatas;
            if (assetInfo != null)
            {
                //// 温控设备
                //if (assetInfo.AssetType == "TempMeasurement" && assetInfo.AssetModel == "SiemensTempMeasurement")
                //{
                //    tempDatas = await _dataPointServer.GetDataPointInfos(AssetLevel.Device, assetInfo.AssetType ?? string.Empty, assetInfo.AssetModel ?? string.Empty);
                //}
                ////第三方设备
                //else if (assetInfo.AssetType == "GeneralDevice" && assetInfo.AssetModel == "GeneralDevice")
                //{
                //    tempDatas = await _assetDataPointInfoServer.GetDataPoints(assetInfo.Id);
                //}
                //// 其他设备
                //else
                //{
                //    tempDatas = await _dataPointServer.GetOtherDeviceDataPoints(assetInfo.Id);
                //}

                //  走第三方json设备
                if ("Other".Equals(assetInfo.AssetModel, StringComparison.OrdinalIgnoreCase)
                    || "GeneralDevice".Equals(assetInfo.AssetType, StringComparison.OrdinalIgnoreCase)
                    || ("Modbus".Equals(assetInfo.AssetModel, StringComparison.OrdinalIgnoreCase)
                    && "Gateway".Equals(assetInfo.AssetType, StringComparison.OrdinalIgnoreCase)))
                {
                    tempDatas = await _assetDataPointInfoServer.GetDataPoints(assetInfo.Id);

                }
                else // 其他设备
                {
                    tempDatas = await _dataPointServer.GetDataPointInfos(AssetLevel.Device, assetInfo.AssetType ?? string.Empty, assetInfo.AssetModel ?? string.Empty);
                }
            }
            else
            {
                tempDatas = await GetDataPointInfos(typeId);
            }

            var tempCloudXmlPath = Path.Combine(unPackageTempPath, "ProjectItems", id, "CloudData.xml");
            XmlDocument document = new XmlDocument();
            XmlDeclaration declaration = document.CreateXmlDeclaration("1.0", "utf-8", null);

            XmlElement root = document.CreateElement("CloudData");
            XmlAttribute rootVersion = document.CreateAttribute("version");
            rootVersion.InnerText = "1.0";
            root.Attributes.Append(rootVersion);
            document.AppendChild(root);

            XmlElement statusGroup = document.CreateElement("Group");
            XmlAttribute statusGroupNameXml = document.CreateAttribute("name");
            statusGroupNameXml.InnerText = statusGroupName;
            XmlAttribute statusPeriodXml = document.CreateAttribute("samplingPeriod");
            statusPeriodXml.InnerText = statusTimePeriod;
            statusGroup.Attributes.Append(statusPeriodXml);
            statusGroup.Attributes.Append(statusGroupNameXml);


            XmlElement measurementGroup = document.CreateElement("Group");
            XmlAttribute measurementGroupNameXml = document.CreateAttribute("name");
            measurementGroupNameXml.InnerText = measurementGroupName;
            XmlAttribute measurementPeriodXml = document.CreateAttribute("samplingPeriod");
            measurementPeriodXml.InnerText = measurementTimePeriod;
            measurementGroup.Attributes.Append(measurementPeriodXml);
            measurementGroup.Attributes.Append(measurementGroupNameXml);

            document.InsertBefore(declaration, document.DocumentElement);

            bool haveStatus = false;
            bool haveMeasurement = false;

            foreach (var item in tempDatas)
            {
                if (!string.IsNullOrWhiteSpace(item.UdcCode))
                {
                    switch (item.GroupName)
                    {
                        case "Status":
                        case "DigitalInputOutput":
                        case "LimitMonitoring":
                        case "LogicFunction":
                            {
                                XmlElement dataPointXml = document.CreateElement("DataPoint");
                                XmlAttribute dName = document.CreateAttribute("name");
                                dName.InnerText = item.UdcCode;

                                dataPointXml.Attributes.Append(dName);
                                statusGroup.AppendChild(dataPointXml);
                                haveStatus = true;
                            }
                            break;
                        case "Measurement":
                            {
                                XmlElement dataPointXml = document.CreateElement("DataPoint");
                                XmlAttribute dName = document.CreateAttribute("name");
                                dName.InnerText = item.UdcCode;

                                dataPointXml.Attributes.Append(dName);
                                measurementGroup.AppendChild(dataPointXml);
                                haveMeasurement = true;
                            }
                            break;
                        default: break;
                    }

                }
            }

            if (haveStatus)
            {
                root.AppendChild(statusGroup);
            }

            if (haveMeasurement)
            {
                root.AppendChild(measurementGroup);
            }

            document.Save(tempCloudXmlPath);
        }
    }
}
