[{"Code": "HaveAlarm", "Name": "HaveAlarm", "GroupName": "Status", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "Alarm", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 1, "ParentName": "Status", "MqttGroupName": "Alarm", "MqttSamplingPeriod": 1, "IsDefaultMqtt": 1, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "IsConnected", "Name": "IsConnected", "GroupName": "Status", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "UDCConnect", "IsSystemStatus": true, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 2, "ParentName": "Status", "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Switch", "Name": "Switch", "GroupName": "Status", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "CircuitBreakerState", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 3, "ParentName": "Status", "MqttGroupName": "Status", "MqttSamplingPeriod": 1, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "SpringCharged", "Name": "SpringCharged", "GroupName": "Status", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "StatusValues/Diagnositic/Diag_CB/Diag_Status/Diag_SpringCharged", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 4, "ParentName": "Status", "MqttGroupName": "Status", "MqttSamplingPeriod": 1, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "SwitchReady", "Name": "SwitchReady", "GroupName": "Status", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "StatusValues/Diagnositic/Diag_CB/Diag_Status/Diag_SwitchReadiness", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 5, "ParentName": "Status", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "BreakerPosition", "Name": "BreakerPosition", "GroupName": "Status", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "StatusValues/Diagnositic/Diag_CB/Diag_Status/Main_PositionInFrame", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "ParentName": "Status", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "TestPosition", "Name": "TestPosition", "GroupName": "Status", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Bss_BSS_STATUS_TEST", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"GetBreakerPosition\"}", "Sort": 9999, "ParentName": "Status", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "OperationPosition", "Name": "OperationPosition", "GroupName": "Status", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Bss_BSS_STATUS_CONNECTED", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"GetBreakerPosition\"}", "Sort": 9999, "ParentName": "Status", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "HasTriped", "Name": "HasTriped", "GroupName": "Status", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Bss_BSS_STATUS_BREAKER_TRIPPED", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"SetTriped\"}", "Sort": 9999, "ParentName": "Status", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ElectricalSwitchCycles", "Name": "ElectricalSwitchCycles", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "StatusValues/MaintenanceAndStatistic/Diag_CountGearLoad", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 10, "ParentName": "Statistics_Maintenance", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "MechanicalSwitchCycles", "Name": "MechanicalSwitchCycles", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "StatusValues/MaintenanceAndStatistic/Diag_CountGearControl", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 11, "ParentName": "Statistics_Maintenance", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "BreakerTemp", "Name": "BreakerTemp", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "[T]", "UdcCode": "Temperatures/Inst/Value/BSS", "Unit": "℃", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 12, "ParentName": "Temperatures_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ComTemp", "Name": "ComTemp", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "[T]", "UdcCode": "Temperatures/Inst/Value/COM16", "Unit": "℃", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 13, "ParentName": "Temperatures_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ua", "Name": "Ua", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "V_LN/Inst/Value/L1N", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 14, "ParentName": "VoltageL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ub", "Name": "Ub", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "V_LN/Inst/Value/L2N", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 15, "ParentName": "VoltageL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Uc", "Name": "Uc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "V_LN/Inst/Value/L3N", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 16, "ParentName": "VoltageL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Uab", "Name": "Uab", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "V_LL/Inst/Value/L1L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 17, "ParentName": "VoltageL-L_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ubc", "Name": "Ubc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "V_LL/Inst/Value/L2L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 18, "ParentName": "VoltageL-L_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Uca", "Name": "Uca", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "V_LL/Inst/Value/L3L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19, "ParentName": "VoltageL-L_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ia", "Name": "Ia", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "I/Inst/Value/L1", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 20, "ParentName": "Current_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ib", "Name": "Ib", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "I/Inst/Value/L2", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 21, "ParentName": "Current_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ic", "Name": "Ic", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "I/Inst/Value/L3", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 22, "ParentName": "Current_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "I_Agv", "Name": "I_Agv", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "I/Inst/Value/AVG", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 23, "ParentName": "Current_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "P", "Name": "P", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Power/W/Inst/Value/Sum", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 24, "ParentName": "ActivePower_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Pa", "Name": "Pa", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Power/W/Inst/Value/L1", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 25, "ParentName": "ActivePower_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Pb", "Name": "Pb", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Power/W/Inst/Value/L2", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 26, "ParentName": "ActivePower_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Pc", "Name": "Pc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Power/W/Inst/Value/L3", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 27, "ParentName": "ActivePower_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Q", "Name": "Q", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Power/var/Inst/Value/Sum", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 28, "ParentName": "ReactivePower_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Qa", "Name": "Qa", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Power/var/Inst/Value/L1", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 29, "ParentName": "ReactivePower_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Qb", "Name": "Qb", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Power/var/Inst/Value/L2", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 30, "ParentName": "ReactivePower_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Qc", "Name": "Qc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Power/var/Inst/Value/L3", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 31, "ParentName": "ReactivePower_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "S", "Name": "S", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Power/VA/Inst/Value/Sum", "Unit": "VA", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 32, "ParentName": "ApparentPower_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor", "Name": "PowFactor", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Power/Factor/Inst/Value/AVG", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 33, "ParentName": "PowerFactor_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor_A", "Name": "PowFactor_A", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Power/Factor/Inst/Value/L1", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 34, "ParentName": "PowerFactor_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor_B", "Name": "PowFactor_B", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Power/Factor/Inst/Value/L2", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 35, "ParentName": "PowerFactor_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor_C", "Name": "PowFactor_C", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Power/Factor/Inst/Value/L3", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 36, "ParentName": "PowerFactor_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "F", "Name": "F", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Frequency/Inst/Value/Common", "Unit": "Hz", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 37, "ParentName": "FrequencyValues_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_U", "Name": "THD_U", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "THD/V/Inst/Value/Common", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 38, "ParentName": "THDVoltage_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_I", "Name": "THD_I", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "THD/I/Inst/Value/Common", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 39, "ParentName": "THDCurrent_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ForwardActivePower", "Name": "ForwardActivePower", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Energy/Wh/Normal/kWh", "Unit": "Wh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 40, "ParentName": "ActiveEnergy_NormalDirection", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ForwardReactivePower", "Name": "ForwardReactivePower", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Energy/varh/Normal/kWh", "Unit": "varh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 41, "ParentName": "ReactiveEnergy_NormalDirection", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ReverseActivePower", "Name": "ReverseActivePower", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Energy/Wh/Reverse/kWh", "Unit": "Wh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 42, "ParentName": "ActiveEnergy_ReverseDirection", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ReverseReactivePower", "Name": "ReverseReactivePower", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Energy/varh/Reverse/kWh", "Unit": "varh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 43, "ParentName": "ReactiveEnergy_ReverseDirection", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "OperatingHours", "Name": "OperatingHours", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "StatusValues/MaintenanceAndStatistic/Diag_HourMetering", "Unit": "h", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 60, "ParentName": "Statistics_Maintenance", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "MainContantStatus", "Name": "MainContantStatus", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "StatusValues/MaintenanceAndStatistic/StatusContactErosion", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 61, "ParentName": "Statistics_Maintenance", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LTTrips", "Name": "LTTrips", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "StatusValues/MaintenanceAndStatistic/Diag_CounterOverload", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 62, "ParentName": "Statistics_Maintenance", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "STTrips", "Name": "STTrips", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "StatusValues/MaintenanceAndStatistic/Diag_CounterShortCircuit", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 63, "ParentName": "Statistics_Maintenance", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "AllTrips", "Name": "AllTrips", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "StatusValues/MaintenanceAndStatistic/Diag_CountGearTrip", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 64, "ParentName": "Statistics_Maintenance", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "HealthScore", "Name": "HealthScore", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "PanelManagerAlgorithm", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 65, "ParentName": "StatusValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "RemainingLife", "Name": "RemainingLife", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "", "Unit": "%", "CollectMode": "PanelManagerAlgorithm", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 66, "ParentName": "StatusValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ContactWearRate", "Name": "ContactWearRate", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "", "Unit": "%", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 67, "ParentName": "StatusValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LT_IR", "Name": "LT_IR", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "ParaIrA", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 68, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LT_TR", "Name": "LT_TR", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "ParatrA", "Unit": "ms", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 69, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LT_TAU", "Name": "LT_TAU", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "ParaTauA", "Unit": "%", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 70, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LTN_IN", "Name": "LTN_IN", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "ParaINA", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 71, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "ST_ISD", "Name": "ST_ISD", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "ParaIsdA", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 72, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "ST_TSD", "Name": "ST_TSD", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "ParatsdA", "Unit": "ms", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 73, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "INST_II", "Name": "INST_II", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "ParaIiA", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 74, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_IG", "Name": "GF_IG", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "ParaIgA", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 75, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_TG", "Name": "GF_TG", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "ParatgA", "Unit": "ms", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 76, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_ALARM_IG", "Name": "GF_ALARM_IG", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "ParaIg2A", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 77, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_ALARM_TG", "Name": "GF_ALARM_TG", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Paratg2A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 78, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_XTD_IG", "Name": "GF_XTD_IG", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "ParaIg2A_TripCurveEnhancement", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 79, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_XTD_TG", "Name": "GF_XTD_TG", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Paratg2A_TripCurveEnhancement", "Unit": "ms", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 80, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "MLFB", "Name": "MLFB", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "EnvCatalogBreaker", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 81, "ParentName": "StatusValues", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "BreakerOrderNo", "Name": "BreakerOrderNo", "GroupName": "Others", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "EnvCatalogTripUnit", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 82, "ParentName": "Others", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "IdentNumber", "Name": "IdentNumber", "GroupName": "Others", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "EnvEquipID", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 83, "ParentName": "Others", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "SerialNumber", "Name": "SerialNumber", "GroupName": "Others", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "EnvIdentCircuitBreaker", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 84, "ParentName": "Others", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "FirmwareRevision_1", "Name": "FirmwareRevision_1", "GroupName": "Others", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "EnvHardSoftTripUnit", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 85, "ParentName": "Others", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "FirmwareRevision_2", "Name": "FirmwareRevision_2", "GroupName": "Others", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "EnvHardSoftMSAE", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 86, "ParentName": "Others", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "FirmwareRevision_3", "Name": "FirmwareRevision_3", "GroupName": "Others", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "FWVersionCOM35", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 87, "ParentName": "Others", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "SumI2t_A", "Name": "SumI2t_A", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "StatusValues/MaintenanceAndStatistic/Diag_ContactWearSum/Diag_WearL1", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 88, "ParentName": "TotalOfDeactivated", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "SumI2t_B", "Name": "SumI2t_B", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "StatusValues/MaintenanceAndStatistic/Diag_ContactWearSum/Diag_WearL2", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 89, "ParentName": "TotalOfDeactivated", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "SumI2t_C", "Name": "SumI2t_C", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "StatusValues/MaintenanceAndStatistic/Diag_ContactWearSum/Diag_WearL3", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 90, "ParentName": "TotalOfDeactivated", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "SumI2t_N", "Name": "SumI2t_N", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "StatusValues/MaintenanceAndStatistic/Diag_ContactWearSum/Diag_WearLN", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 91, "ParentName": "TotalOfDeactivated", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_1", "Name": "Harmonic_Ua_1", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/1/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 92, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_1", "Name": "Harmonic_Ub_1", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/1/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 93, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_1", "Name": "Harmonic_Uc_1", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/1/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 94, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_2", "Name": "Harmonic_Ua_2", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/2/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 95, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_2", "Name": "Harmonic_Ub_2", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/2/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 96, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_2", "Name": "Harmonic_Uc_2", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/2/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 97, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_3", "Name": "Harmonic_Ua_3", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/3/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 98, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_3", "Name": "Harmonic_Ub_3", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/3/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 99, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_3", "Name": "Harmonic_Uc_3", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/3/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 100, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_4", "Name": "Harmonic_Ua_4", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/4/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 101, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_4", "Name": "Harmonic_Ub_4", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/4/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 102, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_4", "Name": "Harmonic_Uc_4", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/4/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 103, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_5", "Name": "Harmonic_Ua_5", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/5/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 104, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_5", "Name": "Harmonic_Ub_5", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/5/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 105, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_5", "Name": "Harmonic_Uc_5", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/5/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 106, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_6", "Name": "Harmonic_Ua_6", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/6/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 107, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_6", "Name": "Harmonic_Ub_6", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/6/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 108, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_6", "Name": "Harmonic_Uc_6", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/6/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 109, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_7", "Name": "Harmonic_Ua_7", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/7/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 110, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_7", "Name": "Harmonic_Ub_7", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/7/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 111, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_7", "Name": "Harmonic_Uc_7", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/7/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 112, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_8", "Name": "Harmonic_Ua_8", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/8/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 113, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_8", "Name": "Harmonic_Ub_8", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/8/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 114, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_8", "Name": "Harmonic_Uc_8", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/8/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 115, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_9", "Name": "Harmonic_Ua_9", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/9/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 116, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_9", "Name": "Harmonic_Ub_9", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/9/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 117, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_9", "Name": "Harmonic_Uc_9", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/9/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 118, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_10", "Name": "Harmonic_Ua_10", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/10/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 119, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_10", "Name": "Harmonic_Ub_10", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/10/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 120, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_10", "Name": "Harmonic_Uc_10", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/10/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 121, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_11", "Name": "Harmonic_Ua_11", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/11/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 122, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_11", "Name": "Harmonic_Ub_11", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/11/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 123, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_11", "Name": "Harmonic_Uc_11", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/11/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 124, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_12", "Name": "Harmonic_Ua_12", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/12/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 125, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_12", "Name": "Harmonic_Ub_12", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/12/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 126, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_12", "Name": "Harmonic_Uc_12", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/12/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 127, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_13", "Name": "Harmonic_Ua_13", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/13/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 128, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_13", "Name": "Harmonic_Ub_13", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/13/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 129, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_13", "Name": "Harmonic_Uc_13", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/13/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 130, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_14", "Name": "Harmonic_Ua_14", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/14/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 131, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_14", "Name": "Harmonic_Ub_14", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/14/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 132, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_14", "Name": "Harmonic_Uc_14", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/14/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 133, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_15", "Name": "Harmonic_Ua_15", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/15/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 134, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_15", "Name": "Harmonic_Ub_15", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/15/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 135, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_15", "Name": "Harmonic_Uc_15", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/15/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 136, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_16", "Name": "Harmonic_Ua_16", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/16/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 137, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_16", "Name": "Harmonic_Ub_16", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/16/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 138, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_16", "Name": "Harmonic_Uc_16", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/16/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 139, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_17", "Name": "Harmonic_Ua_17", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/17/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 140, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_17", "Name": "Harmonic_Ub_17", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/17/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 141, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_17", "Name": "Harmonic_Uc_17", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/17/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 142, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_18", "Name": "Harmonic_Ua_18", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/18/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 143, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_18", "Name": "Harmonic_Ub_18", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/18/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 144, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_18", "Name": "Harmonic_Uc_18", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/18/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 145, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_19", "Name": "Harmonic_Ua_19", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/19/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 146, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_19", "Name": "Harmonic_Ub_19", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/19/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 147, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_19", "Name": "Harmonic_Uc_19", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/19/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 148, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_20", "Name": "Harmonic_Ua_20", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/20/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 149, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_20", "Name": "Harmonic_Ub_20", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/20/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 150, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_20", "Name": "Harmonic_Uc_20", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/20/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 151, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_21", "Name": "Harmonic_Ua_21", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/21/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 152, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_21", "Name": "Harmonic_Ub_21", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/21/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 153, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_21", "Name": "Harmonic_Uc_21", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/21/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 154, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_22", "Name": "Harmonic_Ua_22", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/22/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 155, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_22", "Name": "Harmonic_Ub_22", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/22/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 156, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_22", "Name": "Harmonic_Uc_22", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/22/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 157, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_23", "Name": "Harmonic_Ua_23", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/23/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 158, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_23", "Name": "Harmonic_Ub_23", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/23/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 159, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_23", "Name": "Harmonic_Uc_23", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/23/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 160, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_24", "Name": "Harmonic_Ua_24", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/24/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 161, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_24", "Name": "Harmonic_Ub_24", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/24/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 162, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_24", "Name": "Harmonic_Uc_24", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/24/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 163, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_25", "Name": "Harmonic_Ua_25", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/25/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 164, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_25", "Name": "Harmonic_Ub_25", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/25/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 165, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_25", "Name": "Harmonic_Uc_25", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/25/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 166, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_26", "Name": "Harmonic_Ua_26", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/26/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 167, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_26", "Name": "Harmonic_Ub_26", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/26/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 168, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_26", "Name": "Harmonic_Uc_26", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/26/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 169, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_27", "Name": "Harmonic_Ua_27", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/27/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 170, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_27", "Name": "Harmonic_Ub_27", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/27/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 171, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_27", "Name": "Harmonic_Uc_27", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/27/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 172, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_28", "Name": "Harmonic_Ua_28", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/28/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 173, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_28", "Name": "Harmonic_Ub_28", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/28/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 174, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_28", "Name": "Harmonic_Uc_28", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/28/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 175, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_29", "Name": "Harmonic_Ua_29", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/29/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 176, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_29", "Name": "Harmonic_Ub_29", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/29/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 177, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_29", "Name": "Harmonic_Uc_29", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/29/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 178, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_30", "Name": "Harmonic_Ua_30", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/30/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 179, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_30", "Name": "Harmonic_Ub_30", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/30/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 180, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_30", "Name": "Harmonic_Uc_30", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/30/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 181, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_31", "Name": "Harmonic_Ua_31", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/31/Inst/Value/L1N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 182, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_31", "Name": "Harmonic_Ub_31", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/31/Inst/Value/L2N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 183, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_31", "Name": "Harmonic_Uc_31", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "HV_LN/31/Inst/Value/L3N#", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 184, "ParentName": "HarmonicVoltageL-N", "CanAlarmListen": false, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Trip_Severity", "Name": "Trip_Severity", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Alarm/Value/TripSeverity", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 185, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Trip_Status", "Name": "Trip_Status", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Alarm/Value/TripStatus", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 186, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Trip_Path", "Name": "Trip_Path", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Alarm/Value/TripPath", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 187, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Trip_Info", "Name": "Trip_Info", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Alarm/Value/TripInfo", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 188, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Trip_Time", "Name": "Trip_Time", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Alarm/Value/TripTime", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 189, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Severity", "Name": "Alarm_Severity", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Alarm/Value/AlarmSeverity", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 190, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Status", "Name": "Alarm_Status", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Alarm/Value/AlarmStatus", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 191, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Path", "Name": "Alarm_Path", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Alarm/Value/AlarmPath", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 192, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Info", "Name": "Alarm_Info", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Alarm/Value/AlarmInfo", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 193, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Time", "Name": "Alarm_Time", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Alarm/Value/AlarmTime", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 194, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Msg_Severity", "Name": "Msg_Severity", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Alarm/Value/MsgSeverity", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 195, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Msg_Status", "Name": "Msg_Status", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Alarm/Value/MsgStatus", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 196, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Msg_Path", "Name": "Msg_Path", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Alarm/Value/MsgPath", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 197, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Msg_Info", "Name": "Msg_Info", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Alarm/Value/MsgInfo", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 198, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Msg_Time", "Name": "Msg_Time", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Alarm/Value/MsgTime", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 199, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Capacity_Analysis", "Name": "Capacity_Analysis", "GroupName": "Others", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Capacity_Analysis", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 199, "ParentName": "Others"}, {"Code": "Load_Rate", "Name": "Load_Rate", "GroupName": "Others", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "Load_Rate", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 199, "ParentName": "Others"}, {"Code": "THD_Ua", "Name": "THD_Ua", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "THD_Ua", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 200, "ParentName": "THDVoltageL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "THD_Ub", "Name": "THD_Ub", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "THD_Ub", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 201, "ParentName": "THDVoltageL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "THD_Uc", "Name": "THD_Uc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "THD_Uc", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 202, "ParentName": "THDVoltageL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "THD_Ia", "Name": "THD_Ia", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "THD_Ia", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 203, "ParentName": "THDCurrent_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "THD_Ib", "Name": "THD_Ib", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "THD_Ib", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 204, "ParentName": "THDCurrent_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "THD_Ic", "Name": "THD_Ic", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "THD_Ic", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 205, "ParentName": "THDCurrent_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Health_Status", "Name": "Health_Status", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "PanelManagerAlgorithm", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 206, "ParentName": "StatusValues"}, {"Code": "HealthLevel", "Name": "HealthLevel", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "ACB", "AssetModel": "3WL", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "PanelManagerAlgorithm", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 207, "ParentName": "StatusValues"}]