﻿using Siemens.PanelManager.Common.Job;

namespace Siemens.PanelManager.Job
{
    public class AssetStatusLogSaveJob : JobBase
    {
        public override string Name => "AssetStatusLogSaveJob";
        private IServiceProvider _provider;

        public AssetStatusLogSaveJob(IServiceProvider provider)
        {
            _provider = provider;
        }

        public override Task Execute()
        {
            // TODO1
            //AssetStatusSaveRef refObj = _provider.GetRequiredService<AssetStatusSaveRef>();
            //refObj.Save();
            return Task.CompletedTask;
        }
    }
}
