﻿using Microsoft.AspNetCore.SignalR;
using Siemens.PanelManager.HubModel;
using System.Text;

namespace Siemens.PanelManager.Monitor.Logger
{
    public class CustomLogger : ILogger, IDisposable
    {
        private ILogger _baseLogger;
        private IClientProxy _clientProxy;
        private LogLevel _logLevel = LogLevel.Information;
        private string _tag;
        public CustomLogger(IClientProxy clientProxy, ILogger logger, string tag)
        { 
            _clientProxy = clientProxy;
            _baseLogger = logger;
            _tag = tag;
        }

        public IDisposable? BeginScope<TState>(TState state) where TState : notnull
        {
            return _baseLogger.BeginScope(state);
        }

        public void Dispose()
        {
            _clientProxy.SendAsync("LogInfo", new LogModel()
            {
                Message = "Finish",
                Tag = _tag,
                Timestamp = DateTime.Now.GetTimestampForSec()
            });
        }

        public bool IsEnabled(LogLevel logLevel)
        {
            return logLevel >= _logLevel;
        }

        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
        {
            _baseLogger.Log(logLevel, eventId, state, exception, formatter);
            var message = state?.ToString() ?? string.Empty;

            if (exception != null)
            {
                var sb = new StringBuilder();
                sb.AppendLine(message);
                sb.AppendLine(exception.Message);
                message = sb.ToString();
            }

            _clientProxy.SendAsync("LogInfo", new LogModel()
            {
                Message = message,
                Tag = _tag,
                Timestamp = DateTime.Now.GetTimestampForSec()
            });
        }
    }
}
