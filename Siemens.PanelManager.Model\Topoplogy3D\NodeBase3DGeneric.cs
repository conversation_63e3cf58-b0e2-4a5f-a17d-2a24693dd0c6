﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.Model.Topology3D
{
    public class NodeBase3DGeneric<T> where T : UserData
    {
        [JsonProperty(propertyName: "type", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public string Type { get; protected set; } = string.Empty;
        [JsonProperty(propertyName: "name", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public string? Name { get; protected set; }
        [JsonProperty(propertyName: "uuid", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public string Id { get; protected set; } = string.Empty;
        [JsonProperty(propertyName: "position", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public Position Position { get; protected set; } = new Position();
        [JsonProperty(propertyName: "rotation", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public Rotation Rotation { get; protected set; } = new Rotation();
        [JsonProperty(propertyName: "size", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public Size Size { get; protected set; } = new Size();
        [JsonProperty(propertyName: "userData", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public T? UserData { get; protected set; }
        [JsonProperty(propertyName: "nodes", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public virtual List<NodeBase3DGeneric<T>>? Nodes { get; set; }
    }
}
