﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Asset
{
    [SugarTable("asset_dashboard_config")]
    public class AssetDashboardConfig : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [UniquenessAttribute]
        [SugarColumn(ColumnName = "config_name", IsNullable = false, Length = 256)]
        public string ConfigName { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "sub_key", IsNullable = false, Length = 50)]
        public string SubKey { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "sql", IsNullable = false, ColumnDataType = "varchar(10240)")]
        public string Sql { get; set; }= string.Empty;
        [SugarColumn(ColumnName = "dashboard_type", IsNullable = false, Length = 50)]
        public string DashboardType { get; set; } = string.Empty;
        /// <summary>
        /// 数据库类型
        /// pgsql
        /// influxdb
        /// </summary>
        [SugarColumn(ColumnName = "datebase_type", IsNullable = true, Length = 50)]
        public string DbType { get; set; } = "pgsql";
        /// <summary>
        /// 参数配置
        /// </summary>
        [SugarColumn(ColumnName = "parameter_settings", IsNullable = true, Length = 256)]
        public string? ParameterSettings { get; set; }

        [SugarColumn(ColumnName = "extend", IsNullable = true, ColumnDataType = "varchar(10240)")]
        public string? Extend { get; set; }
    }
}
