﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.InfluxDB.Helper.FluxModel
{
    internal class Sort
    {
        public List<string> Columns { get; set; } = new List<string>();
        public bool IsDesc { get; set; }

        public void AppendFlux(StringBuilder flux)
        {
            if(Columns.Count == 0) return;

            flux.Append($"|> sort(columns: [");
            foreach(var column in Columns ) 
            {
                flux.Append($"\"{column.ToString()}\"");
            }
            flux.AppendLine($"], desc: {(IsDesc ? "true": "false")})");
        }
    }
}
