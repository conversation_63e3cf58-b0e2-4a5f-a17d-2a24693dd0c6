﻿using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.Model.Emun;
using Siemens.PanelManager.WebApi.StaticContent;
using System.Text;
using System.Text.RegularExpressions;


namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class AlarmRuleInfoResult
    {
        public AlarmRuleInfoResult()
        {
        }
        public AlarmRuleInfoResult(AlarmRule rule, MessageContext messageContext)
        {
            var ruleInfo = GetRuleInfo(rule, messageContext);
            InitRuleInfo(rule, ruleInfo);
        }

        private void InitRuleInfo(AlarmRule rule, string ruleInfo)
        {
            Id = rule.Id;
            Name = rule.Name;
            Detail = rule.Details;
            TargetType = rule.TargetType.ToString();
            Rule = ruleInfo;
            switch (rule.TargetType)
            {
                case AlarmTargetType.Panel:
                case AlarmTargetType.Circuit:
                case AlarmTargetType.Substation:
                case AlarmTargetType.Transformer:
                case AlarmTargetType.Device:
                    TargetValue = rule.TargetValue;
                    break;
                case AlarmTargetType.CircuitModel:
                case AlarmTargetType.PanelModel:
                case AlarmTargetType.DeviceModel:
                    {
                        var match = Regex.Match(rule.TargetValue, "^([\\w|_|-]+)\\|([\\w|_|-]*)$");
                        if (match.Success)
                        {
                            Type = match.Groups[1].Value;
                            Model = match.Groups[2].Value;
                        }
                    }
                    break;
                default: break;
            }
            Level = (int)rule.Severity;
            var sections = new List<AlarmRuleSection>();
            foreach (var section in rule.Sections)
            {
                sections.Add(new AlarmRuleSection(section));
            }
            Sections = sections.ToArray();
            IsEnable = rule.IsEnable;
        }

        public AlarmRuleInfoResult(AlarmRule rule, string ruleInfo)
        {
            InitRuleInfo(rule, ruleInfo);
        }

        private string GetRuleInfo(AlarmRule rule, MessageContext messageContext)
        {
            var stringBuilder = new StringBuilder();
            foreach (var section in rule.Sections)
            {
                stringBuilder.Append($"{messageContext.GetDataPointName(section.Point)} {section.Compare.ToCompareString()} {section.DataValue} {section.LogicalOperator.ToLogicalOperatorString()} ");
            }

            return stringBuilder.ToString();
        }

        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Detail { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Model { get; set; } = string.Empty;
        public string TargetValue { get; set; } = string.Empty;
        public string Rule { get; set; } = string.Empty;
        public int Level { get; set; }
        public string TargetType { get; set; } = string.Empty;
        public bool IsEnable { get; set; }
        public AlarmRuleSection[] Sections { get; set; } = new AlarmRuleSection[0];

        public AlarmRule GetAlarmRule()
        {
            var targetType = AlarmTargetType.Device;
            if (Enum.TryParse(typeof(AlarmTargetType), TargetType, out object? target) && target != null && target is AlarmTargetType newValue)
            {
                targetType = newValue;
            }

            var targetValue = string.Empty;
            switch (targetType)
            {
                case AlarmTargetType.Panel:
                case AlarmTargetType.Circuit:
                case AlarmTargetType.Substation:
                case AlarmTargetType.Transformer:
                case AlarmTargetType.Device:
                    {
                        targetValue = TargetValue;
                        break;
                    }
                case AlarmTargetType.CircuitModel:
                case AlarmTargetType.PanelModel:
                case AlarmTargetType.DeviceModel:
                    targetValue = $"{Type ?? string.Empty}|{Model ?? string.Empty}";
                    break;
                default: break;
            }

            var rule = new AlarmRule()
            {
                Id = Id,
                Name = Name,
                Details = Detail,
                TargetValue = targetValue,
                TargetType = targetType,
                IsEnable = IsEnable,
                Severity = (AlarmSeverity)Level,
            };
            foreach (var section in Sections)
            {
                rule.Sections.Add(section.ToRuleSection());
            }
            return rule;
        }

        public bool Validation()
        {
            var targetType = AlarmTargetType.Device;
            if (Enum.TryParse(typeof(AlarmTargetType), TargetType, out object? target) 
                && target != null 
                && target is AlarmTargetType newValue)
            {
                targetType = newValue;
            }

            switch (targetType)
            {
                case AlarmTargetType.Panel:
                case AlarmTargetType.Circuit:
                case AlarmTargetType.Substation:
                case AlarmTargetType.Device:
                case AlarmTargetType.CircuitModel:
                case AlarmTargetType.PanelModel:
                case AlarmTargetType.DeviceModel:
                case AlarmTargetType.Transformer:
                    break;
                default: return false;
            }

            bool wrongLevel = true;
            switch (Level)
            {
                case (int)AlarmSeverity.Low:
                case (int)AlarmSeverity.Middle:
                case (int)AlarmSeverity.High:
                    wrongLevel = false;
                    break;
                default:
                    wrongLevel = true;
                    break;
            }

            if (wrongLevel)
            {
                return false;
            }

            if (Sections.Length == 0)
            {
                return false;
            }

            foreach (var section in Sections)
            {
                if (section == null) continue;
                if (!section.Validation())
                {
                    return false;
                }
            }

            return true;
        }
    }

    public class AlarmRuleSection
    {
        public AlarmRuleSection()
        {

        }

        public AlarmRuleSection(RuleSection section)
        {
            Point= section.Point;
            Compare = section.Compare.ToCompareString();
            DataValue = section.DataValue;
            AndOr = section.LogicalOperator.ToLogicalOperatorString();
        }
        public string Point { get; set; }= string.Empty;
        public string Compare { get; set; } = string.Empty;
        public string DataValue { get; set; } = string.Empty;
        public string AndOr { get; set; } = string.Empty;

        public RuleSection ToRuleSection()
        {
            return new RuleSection()
            {
                Point = Point,
                Compare = Compare.ToCompare(),
                DataValue = DataValue,
                LogicalOperator = AndOr.ToLogicalOperator()
            };
        }

        public bool Validation() 
        {
            if (!Compare.IsCompare())
            {
                return false;
            }
            if (!string.IsNullOrEmpty(AndOr) && !AndOr.IsLogicalOperator())
            {
                return false;
            }
            if (string.IsNullOrEmpty(Point) && string.IsNullOrEmpty(DataValue))
            {
                return false;
            }

            return true;
        }
    }
}
