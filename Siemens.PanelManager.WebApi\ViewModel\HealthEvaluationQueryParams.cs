﻿using Akka.Util;
using System.Diagnostics.CodeAnalysis;
using System.Diagnostics.Metrics;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class HealthEvaluationQueryParams
    {
        public string searchTime { get; set; }
        public string topo { get; set; } = "";
        public HealthParams param { get; set; }
    }
    public class HealthParams
    {
        /// <summary>
        /// 同厂、同型、同期设备的故障信息
        /// </summary>
        public Indicators familyDefects { get; set; } = new Indicators();
        public Indicators enviroment { get; set; } = new Indicators();
        public Indicators meter { get; set; } = new Indicators();
        public Indicators cabinet { get; set; } = new Indicators();
        public Indicators lamp { get; set; } = new Indicators();
        public Indicators isolated { get; set; } = new Indicators();
        public Indicators protection { get; set; } = new Indicators();
        /// <summary>
        /// 接线方式
        /// </summary>
        public Indicators unwindingMethod { get; set; } = new Indicators();
        /// <summary>
        /// 选型
        /// </summary>
        public Indicators modelSelection { get; set; } = new Indicators();
        /// <summary>
        /// 互感器配置
        /// </summary>
        public Indicators transformerConfiguration { get; set; } = new Indicators();
        /// <summary>
        /// 空气开关分断能力
        /// </summary>
        public Indicators airSwitchBreakingCapacity { get; set; } = new Indicators();
        /// <summary>
        /// 配电柜外观
        /// </summary>
        public Indicators cabinetAppearance { get; set; } = new Indicators();
        /// <summary>
        /// 配电柜连接情况
        /// </summary>
        public Indicators cabinetConnection { get; set; } = new Indicators();
        /// <summary>
        /// 配电柜接地
        /// </summary>
        public Indicators cabinetGrounding { get; set; } = new Indicators();
        /// <summary>
        /// 配电柜闭锁
        /// </summary>
        public Indicators cabinetLocking { get; set; } = new Indicators();
        /// <summary>
        /// 空气开关外观
        /// </summary>
        public Indicators airSwitchAppearance { get; set; } = new Indicators();
        /// <summary>
        /// 空气开关操作情况
        /// </summary>
        public Indicators airSwitchOperation { get; set; } = new Indicators();
        /// <summary>
        /// 空气开关位置指示
        /// </summary>
        public Indicators airSwitchPosition { get; set; } = new Indicators();
        /// <summary>
        /// 接触器吸合情况
        /// </summary>
        public Indicators contactorSuctionCondition { get; set; } = new Indicators();
        /// <summary>
        /// 接触器绝缘部件
        /// </summary>
        public Indicators contactorInsulationComponents { get; set; } = new Indicators();
        /// <summary>
        /// 接触器辅助触点
        /// </summary>
        public Indicators contactorAuxiliaryContacts { get; set; } = new Indicators();
        /// <summary>
        /// 接触器灭护罩
        /// </summary>
        public Indicators contactorExtinguishingCover { get; set; } = new Indicators();
        /// <summary>
        /// 接触器合闸线圈
        /// </summary>
        public Indicators contactorClosingCoil { get; set; } = new Indicators();
        /// <summary>
        /// 避雷器外观
        /// </summary>
        public Indicators lightningArresterAppearance { get; set; } = new Indicators();
        /// <summary>
        /// 声音
        /// </summary>
        public Indicators voice { get; set; } = new Indicators();
        /// <summary>
        /// 操作试验
        /// </summary>
        public Indicators operationTest { get; set; } = new Indicators();
        /// <summary>
        /// 红外试验
        /// </summary>
        public Indicators infraredTest { get; set; } = new Indicators();
        /// <summary>
        /// 接触器动作试验
        /// </summary>
        public Indicators contactorActionTest { get; set; } = new Indicators();
        /// <summary>
        /// 避雷器试验
        /// </summary>
        public Indicators lightningArresterTest { get; set; } = new Indicators();
    }
    public class Indicators
    {
        public bool isEnable {  get; set; }=false;
        public decimal min { get; set; } = 0;
        public decimal max { get; set; } = 10;
        public decimal weight { get; set; } = 5;
        public decimal value { get; set; } = 0;
        public int[]? Limit { get; set; }
    }
    
}
