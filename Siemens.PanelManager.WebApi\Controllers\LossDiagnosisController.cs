﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Quartz.Util;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Model.Algorithm;
using Siemens.PanelManager.Model.Chart;
using Siemens.PanelManager.Model.Database.Algorithm;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.Topology;
using Siemens.PanelManager.Model.Topology;
using Siemens.PanelManager.Server.Algorithm;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class LossDiagnosisController : SiemensApiControllerBase
    {
        private LossDiagnose _lossDiagnose => _provider.GetRequiredService<LossDiagnose>();
        private ISqlSugarClient _client;
        private IServiceProvider _provider;
        private ILogger _logger;
        public LossDiagnosisController(SqlSugarScope client, IServiceProvider provider, SiemensCache cache, ILogger<LossDiagnosisController> logger)
            : base(provider, cache)
        {
            _client = client;
            _provider = provider;
            _logger = logger;
        }

        #region Mock 数据
        private readonly static Dictionary<string, Dictionary<int, decimal[]>> _mockData = new Dictionary<string, Dictionary<int, decimal[]>>()
        {
            ["LossDiagnosisForKMS"] = new Dictionary<int, decimal[]>()
            {
                [1] = new decimal[31] { 3.1m, 3.4m, 2.6m, 2.6m, 3.6m, 2.5m, 3.2m, 3.4m, 3.2m, 3.6m, 3.2m, 2.6m, 3.3m, 3.4m, 2.5m, 3.1m, 2.9m, 2.8m, 3.5m, 3.4m, 2.5m, 3.1m, 2.5m, 2.8m, 3m, 3.3m, 2.6m, 3.4m, 2.8m, 3.5m, 3.3m, },
                [2] = new decimal[31] { 8613.76m, 8865.69m, 8095.03m, 8675.6m, 8645.23m, 8839.33m, 8783.72m, 8594.01m, 8864.23m, 8476.45m, 8096.07m, 8211.2m, 8525.85m, 8201.19m, 8135.45m, 8574.2m, 8416.86m, 8178.72m, 8149.43m, 8483.94m, 8589.57m, 8172.13m, 8590.68m, 8713.91m, 8759.97m, 8360.99m, 8671.82m, 8229.85m, 8514.72m, 8938.25m, 8214.6m, },
                [3] = new decimal[31] { 1028.23m, 1624.78m, 1492.3m, 1501.95m, 1264.83m, 1609.79m, 1474.13m, 1204.17m, 1067.24m, 1899.66m, 1139.49m, 1760.07m, 1685.6m, 1506.05m, 1054.75m, 1275.81m, 1204.61m, 1410.36m, 1009.71m, 1935.52m, 1441.59m, 1782.65m, 1799.39m, 1746.86m, 1644.59m, 1457.18m, 1849.16m, 1355.59m, 1620.83m, 1124.15m, 1082.99m, },
                [4] = new decimal[24] { 94.68m, 109.23m, 104.06m, 90.86m, 90.71m, 92.36m, 109.31m, 524m, 545.53m, 525.47m, 465.13m, 498.45m, 530.79m, 556.79m, 479.28m, 548.64m, 540.83m, 486.37m, 533.02m, 462.76m, 492.01m, 522.9m, 540.02m, 103.58m, },
                [5] = new decimal[24] { 95.52m, 101.63m, 99.43m, 101.5m, 94.74m, 106.55m, 106.89m, 94.76m, 92.02m, 104.98m, 93.64m, 94.3m, 93.91m, 108.4m, 90.5m, 102.42m, 107.79m, 91.16m, 103.31m, 107.83m, 104.15m, 106.89m, 92.12m, 94.55m, },
            },
            ["LossDiagnosisForCNY"] = new Dictionary<int, decimal[]>()
            {
                [1] = new decimal[31] { 3.1m, 3.4m, 2.6m, 2.6m, 3.6m, 2.5m, 3.2m, 3.4m, 3.2m, 3.6m, 3.2m, 2.6m, 3.3m, 3.4m, 2.5m, 3.1m, 2.9m, 2.8m, 3.5m, 3.4m, 2.5m, 3.1m, 2.5m, 2.8m, 3m, 3.3m, 2.6m, 3.4m, 2.8m, 3.5m, 3.3m, },
                [2] = new decimal[31] { 8613.76m, 8865.69m, 8095.03m, 8675.6m, 8645.23m, 8839.33m, 8783.72m, 8594.01m, 8864.23m, 8476.45m, 8096.07m, 8211.2m, 8525.85m, 8201.19m, 8135.45m, 8574.2m, 8416.86m, 8178.72m, 8149.43m, 8483.94m, 8589.57m, 8172.13m, 8590.68m, 8713.91m, 8759.97m, 8360.99m, 8671.82m, 8229.85m, 8514.72m, 8938.25m, 8214.6m, },
                [3] = new decimal[31] { 1028.23m, 1624.78m, 1492.3m, 1501.95m, 1264.83m, 1609.79m, 1474.13m, 1204.17m, 1067.24m, 1899.66m, 1139.49m, 1760.07m, 1685.6m, 1506.05m, 1054.75m, 1275.81m, 1204.61m, 1410.36m, 1009.71m, 1935.52m, 1441.59m, 1782.65m, 1799.39m, 1746.86m, 1644.59m, 1457.18m, 1849.16m, 1355.59m, 1620.83m, 1124.15m, 1082.99m, },
                [4] = new decimal[24] { 94.68m, 109.23m, 104.06m, 90.86m, 90.71m, 92.36m, 109.31m, 524m, 545.53m, 525.47m, 465.13m, 498.45m, 530.79m, 556.79m, 479.28m, 548.64m, 540.83m, 486.37m, 533.02m, 462.76m, 492.01m, 522.9m, 540.02m, 103.58m, },
                [5] = new decimal[24] { 95.52m, 101.63m, 99.43m, 101.5m, 94.74m, 106.55m, 106.89m, 94.76m, 92.02m, 104.98m, 93.64m, 94.3m, 93.91m, 108.4m, 90.5m, 102.42m, 107.79m, 91.16m, 103.31m, 107.83m, 104.15m, 106.89m, 92.12m, 94.55m, },
            },
            ["TestTrainData"] = new Dictionary<int, decimal[]>()
            {
                [1] = new decimal[31] { 3.1m, 3.4m, 2.6m, 2.6m, 3.6m, 2.5m, 3.2m, 3.4m, 3.2m, 3.6m, 3.2m, 2.6m, 3.3m, 3.4m, 2.5m, 3.1m, 2.9m, 2.8m, 3.5m, 3.4m, 2.5m, 3.1m, 2.5m, 2.8m, 3m, 3.3m, 2.6m, 3.4m, 2.8m, 3.5m, 3.3m, },
                [2] = new decimal[31] { 1722.752m, 1773.138m, 1619.006m, 1735.12m, 1729.046m, 1767.866m, 1756.744m, 1718.802m, 1772.846m, 1695.29m, 1619.214m, 1642.24m, 1705.17m, 1640.238m, 1627.09m, 1714.84m, 1683.372m, 1635.744m, 1629.886m, 1696.788m, 1717.914m, 1634.426m, 1718.136m, 1742.782m, 1751.994m, 1672.198m, 1734.364m, 1645.97m, 1702.944m, 1787.65m, 1642.92m, },
                [3] = new decimal[31] { 205.646m, 324.956m, 298.46m, 300.39m, 252.966m, 321.958m, 294.826m, 240.834m, 213.448m, 379.932m, 227.898m, 352.014m, 337.12m, 301.21m, 210.95m, 255.162m, 240.922m, 282.072m, 201.942m, 387.104m, 288.318m, 356.53m, 359.878m, 349.372m, 328.918m, 291.436m, 369.832m, 271.118m, 324.166m, 224.83m, 216.598m, },
            },
            ["PattenData"] = new Dictionary<int, decimal[]>()
            {
                [1] = new decimal[24] { 26.94m, 30.105m, 29.562m, 26.127m, 27.006m, 26.829m, 31.401m, 151.302m, 155.091m, 155.703m, 131.13m, 134.586m, 155.979m, 160.929m, 138.888m, 153.45m, 155.85m, 138.903m, 155.688m, 128.679m, 146.916m, 150.282m, 146.394m, 29.961m, },
                [2] = new decimal[24] { 28.404m, 32.769m, 31.218m, 27.258m, 27.213m, 27.708m, 32.793m, 157.2m, 163.659m, 157.641m, 139.539m, 149.535m, 159.237m, 167.037m, 143.784m, 164.592m, 162.249m, 145.911m, 159.906m, 138.828m, 147.603m, 156.87m, 162.006m, 31.074m, },
                [3] = new decimal[24] { 26.013m, 29.043m, 30.42m, 29.496m, 27.549m, 25.989m, 31.419m, 158.538m, 159.099m, 150.558m, 152.052m, 151.659m, 163.119m, 149.733m, 136.017m, 172.683m, 174.924m, 151.338m, 170.343m, 143.922m, 136.623m, 157.665m, 151.047m, 29.733m, },
            }
        };

        private readonly static Dictionary<string, string[]> _yColumns = new Dictionary<string, string[]>()
        {
            ["LossDiagnosisForKMS"] = new string[] { "Loss(KMS)", "Reference loss", "Loss(%)" },
            ["LossDiagnosisForCNY"] = new string[] { "Loss(CNY)", "Reference loss", "Loss(%)" },
            ["TestTrainData"] = new string[] { "Selected day", "Reference" },
            ["PattenData"] = new string[] { "Loss(KMS)", "Reference loss by AI forcast-Max", "Reference loss by AI forcast-Min" }
        };

        private readonly static string[] _timeList = new string[] { "00:00", "01:00", "02:00", "03:00", "04:00", "05:00", "06:00", "07:00", "08:00", "09:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00", "19:00", "20:00", "21:00", "22:00", "23:00" };
        #endregion
        private async Task<LineChartModel> GetLossData(string chartCode, int assetId, string dateType, string startDate, string endDate = "",string searchType="base")
        {
            var lineModel = new LineChartModel();
            lineModel.YColumns = _yColumns[chartCode].ToList();
            Dictionary<string, object> requestBody = new Dictionary<string, object>();
            switch (chartCode)
            {
                case "LossDiagnosisForKMS":

                    requestBody.Add("topology_id", assetId.ToString());
                    requestBody.Add("searchTimeType", dateType);
                    requestBody.Add("searchType", searchType);
                    if (endDate != "undefined" && !endDate.IsNullOrWhiteSpace() && dateType == ChartDateType.Custom.GetDescription())
                    {
                        List<string> dateRange = new List<string>();
                        dateRange.Add(startDate);
                        dateRange.Add(endDate);
                        requestBody.Add("searchTime", dateRange);
                    }
                    else
                    {
                        requestBody.Add("searchTime", startDate);
                    }
                    var response = await _lossDiagnose.GetSysLossdataApi(requestBody, "AlgorithmEvaluationApiPath", "/loss/loss_rmb");
                    var result = JsonConvert.DeserializeObject<lossForCNYModel>(response);
                    if (result == null) return lineModel;
                    lineModel.X = result.times;
                    lineModel.Y1 = result.lossP;
                    lineModel.Y2 = result.line;
                    lineModel.Y3 = result.referLossP;
                    //if (searchType == "predict")
                    //{
                    //    lineModel.Y3 = result.values.refer;
                    //    lineModel.Y4 = result.values.referLow;
                    //}
                    //else
                    //{
                    //    lineModel.Y3 = result.values.refer;
                    //}
                    break;
                case "LossDiagnosisForCNY":
                    requestBody.Add("topology_id", assetId.ToString());
                    requestBody.Add("searchTimeType", dateType);
                    requestBody.Add("searchType", searchType);
                    if (endDate != "undefined" && !endDate.IsNullOrWhiteSpace() && dateType == ChartDateType.Custom.GetDescription())
                    {
                        List<string> dateRange = new List<string>();
                        dateRange.Add(startDate);
                        dateRange.Add(endDate);
                        requestBody.Add("searchTime", dateRange);
                    }
                    else
                    {
                        requestBody.Add("searchTime", startDate);
                    }
                    var responseCNY = await _lossDiagnose.GetSysLossdataApi(requestBody, "AlgorithmEvaluationApiPath", "/loss/loss_rmb");

                    var resultCNY = JsonConvert.DeserializeObject<lossForCNYModel>(responseCNY);
                    if (resultCNY == null) break;
                    lineModel.X = resultCNY.times;
                    lineModel.Y1 = resultCNY.rmb;
                    lineModel.Y2 = resultCNY.refer;
                    break;
                case "TestTrainData":
                    requestBody.Add("topology_id", assetId.ToString());
                    requestBody.Add("searchTime", startDate);
                    var responseTestTrain = await _lossDiagnose.GetSysLossdataApi(requestBody, "AlgorithmEvaluationApiPath", "loss/loss_predict/barchart");
                    var dataTestTrain = JsonConvert.DeserializeObject<TestTrainModel>(responseTestTrain);
                    if (dataTestTrain == null) break;
                    lineModel.X = dataTestTrain.assetName;
                    lineModel.XTitles = dataTestTrain.asset;
                    lineModel.Y1 = dataTestTrain.values.testing;
                    lineModel.Y2 = dataTestTrain.values.training;
                    break;
                case "PattenData":
                    requestBody.Add("topology_id", assetId.ToString());
                    requestBody.Add("searchTime", startDate);
                    var responsePattenData = await _lossDiagnose.GetSysLossdataApi(requestBody, "AlgorithmEvaluationApiPath", "loss/loss_predict/linechart");
                    var dataPattenData = JsonConvert.DeserializeObject<LossPredictModel>(responsePattenData);
                    if (dataPattenData == null) break;
                    lineModel.X = dataPattenData.times;
                    lineModel.Y1 = dataPattenData.values.lossW;
                    lineModel.Y2 = dataPattenData.values.max;
                    //lineModel.Y3 = dataPattenData.values.min;
                    lineModel.XColumn = dataPattenData.values.abnormal;
                    break;

            }

            return lineModel;
        }

        [HttpGet("{assetId}/GetChartData")]
        [SwaggerOperation(Summary = "Swagger_LossDiagnosis_GetChartData", Description = "Swagger_LossDiagnosis_GetChartData_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<Dictionary<string, IChart>>> GetChartData(int assetId, [FromQuery] ChartParam param)
        {
            if (assetId <= 0 || param == null ||
                !param.DateType.HasValue ||
                (param.DateType != ChartDateType.Day
                && param.DateType != ChartDateType.Month
                && param.DateType != ChartDateType.Year
                && param.DateType != ChartDateType.Custom) ||
                string.IsNullOrEmpty(param.ChartCodes) ||
                string.IsNullOrEmpty(param.StartDate))
            {
                return new ResponseBase<Dictionary<string, IChart>>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }

            var chartCodes = param.ChartCodes.Split(',');
            var result = new Dictionary<string, IChart>();
            bool isTimeList = param.DateType == ChartDateType.Day;
            switch (param.DateType)
            {
                case ChartDateType.Year:
                case ChartDateType.Month:
                case ChartDateType.Custom:
                    {
                        var startDate = DateTime.Today;
                        var endDate = DateTime.Today;
                        if (!param.DateType.Value.GetDateByDateType(param.StartDate, param.EndDate, ref startDate, ref endDate))
                        {
                            return new ResponseBase<Dictionary<string, IChart>>()
                            {
                                Code = 40301,
                                Message = MessageContext.ErrorParam
                            };
                        }

                        if (startDate > DateTime.Now)
                        {
                            return new ResponseBase<Dictionary<string, IChart>>()
                            {
                                Code = 40301,
                                Message = MessageContext.ErrorParam
                            };
                        }
                    }
                    break;
            }
            int topoId = 0;
            var assetInfo = await _client.Queryable<AssetInfo>().FirstAsync(a => a.Id == assetId && a.AssetLevel == AssetLevel.Substation);
            if (assetInfo != null && assetInfo.TopologyId.HasValue)
            {
                topoId = (int)assetInfo.TopologyId;
            }
            else
            {
                return new ResponseBase<Dictionary<string, IChart>>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            try
            {
                var lossData = await GetLossData(param.ChartCodes, topoId, param.DateType.GetDescription(), param.StartDate, param.EndDate ?? string.Empty, param.SearchType);
                result.Add(chartCodes[0], lossData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetChartData Error");
            }
           

            return new ResponseBase<Dictionary<string, IChart>>()
            {
                Code = 20000,
                Data = result
            };
        }

        [HttpGet("GetSysLossRefer")]
        [SwaggerOperation(Summary = "Swagger_LossDiagnosis_GetSysLossRefer", Description = "Swagger_LossDiagnosis_GetSysLossRefer_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<JObject>> GetSysLossRefer()
        {
            SystemLossConfig lossConfigInfo = await _client.Queryable<SystemLossConfig>()
                .FirstAsync();
            JObject result = JObject.FromObject(lossConfigInfo);
            return new ResponseBase<JObject>()
            {
                Code = 20000,
                Data = result
            };
        }
        [HttpPut("UpdateSysLossRefer")]
        [SwaggerOperation(Summary = "Swagger_LossDiagnosis_UpdateSysLossRefer", Description = "Swagger_LossDiagnosis_UpdateSysLossRefer_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> Update(decimal lossRefer)
        {
            try
            {
                SystemLossConfig lossConfigInfo = await _client.Queryable<SystemLossConfig>()
                .FirstAsync();
                lossConfigInfo.ReferLoss = lossRefer;
                await _client.Updateable(lossConfigInfo).ExecuteCommandAsync();
            }catch (Exception ex)
            {
                _logger.LogError(ex, "更新参考损耗失败");
                return new ResponseBase<string>()
                {
                    Code = 50000,
                    Message = MessageContext.ServerException
                };
            }
            
            return new ResponseBase<string>()
            {
                Code = 20000,
                Message = MessageContext.Success
            };
        }

        [HttpGet("{assetId}/GetNtlResult")]
        [SwaggerOperation(Summary = "Swagger_LossDiagnosis_GetNtlResult", Description = "Swagger_LossDiagnosis_GetNtlResult_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<JObject>> GetNtlResult(int assetId, string date)
        {
            if (assetId <= 0 || string.IsNullOrEmpty(date))
            {
                return new ResponseBase<JObject>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            int topoId = 0;
            var assetInfo = await _client.Queryable<AssetInfo>().FirstAsync(a => a.Id == assetId && a.AssetLevel == AssetLevel.Substation);
            if (assetInfo != null && assetInfo.TopologyId.HasValue)
            {
                topoId = (int)assetInfo.TopologyId;
            }
            else
            {
                return new ResponseBase<JObject>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            Dictionary<string, object> requestBody = new Dictionary<string, object>();
            //DateTime dateTime = DateTime.Parse(date);
            //string day = dateTime.ToString("yyyy-MM-dd");
            //int hh = dateTime.Hour;
            //int mm = dateTime.Minute;
            //int calmm = (mm / 15) * 15;
            requestBody.Add("topology_id", topoId);
            requestBody.Add("searchTime", date);
            //requestBody.Add("selectTime", hh.ToString()+":"+calmm.ToString());
            var responseUnknownLoss = await _lossDiagnose.GetSysLossdataApi(requestBody, "AlgorithmEvaluationApiPath", "/loss/ntl_result");
            var dataresponseUnknownLoss = JsonConvert.DeserializeObject<UnknownLocationModel>(responseUnknownLoss);
            var topoInfo = new JObject();
            if (dataresponseUnknownLoss != null)
            {
                var topoplogyInfo = await _client.Queryable<TopologyInfo>().FirstAsync(t => t.Id == topoId);
                if (topoplogyInfo != null)
                {
                    topoInfo = JObject.Parse(topoplogyInfo.Data);
                    var nodesToken = topoInfo.SelectToken("topology.nodeDataArray");
                    if (nodesToken != null && nodesToken is JArray nodesArray)
                    {
                        foreach (var info in nodesArray)
                        {
                            if (info is JObject node)
                            {
                                var keyToken = node.GetValue("key");
                                if (keyToken == null) continue;
                                var key = keyToken.Value<int>();
                                var ntl = dataresponseUnknownLoss.value.FirstOrDefault(n => n.node_id == key);
                                if(ntl!=null)
                                {
                                    info["level"] = ntl.level;
                                }
                            }
                        }
                    }
                }
            }
            return new ResponseBase<JObject>()
            {
                Code = 20000,
                Data = topoInfo
            };
        }

        private decimal GetMockDataByDate(DateTime date, int index, int dataIndex, Dictionary<int, decimal[]> data)
        {
            switch (dataIndex)
            {
                case 2:
                    return data[1][index];
                case 1:
                    if (date.DayOfWeek == DayOfWeek.Sunday || date.DayOfWeek == DayOfWeek.Saturday)
                    {
                        var total = data[3][index];
                        return total * data[1][index] * 0.1m;
                    }
                    {
                        var total = data[2][index];
                        return total * data[1][index] * 0.1m;
                    }
                case 3:
                    if (date.DayOfWeek == DayOfWeek.Sunday || date.DayOfWeek == DayOfWeek.Saturday)
                    {
                        var total = data[3][index];
                        return total * 0.3m;
                    }
                    {
                        var total = data[2][index];
                        return total * 0.3m;
                    }
                default: return 0m;
            }
        }

        [HttpGet("{assetId}/getKpi")]
        [SwaggerOperation(Summary = "Swagger_LossDiagnosis_GetKpi", Description = "Swagger_LossDiagnosis_GetKpi_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<LossDiagnosisKPI>> GetKpi(int assetId)
        {

            var result = new LossDiagnosisKPI();
            Dictionary<string, object> requestBody = new Dictionary<string, object>();
            requestBody.Add("currentTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            int topoId = 0;
            try
            {
                var assetInfo = await _client.Queryable<AssetInfo>().FirstAsync(a => a.Id == assetId && a.AssetLevel == AssetLevel.Substation);
                if (assetInfo != null && assetInfo.TopologyId.HasValue)
                {
                    topoId = (int)assetInfo.TopologyId;
                }
                else
                {
                    return new ResponseBase<LossDiagnosisKPI>()
                    {
                        Code = 40301,
                        Message = MessageContext.ErrorParam
                    };
                }
                requestBody.Add("topology_id", topoId.ToString());
                var response = await _lossDiagnose.GetSysLossKpiApi(requestBody);
                result = JsonConvert.DeserializeObject<LossDiagnosisKPI>(response);
            }catch (Exception ex) {
                _logger.LogError(ex, "getKpi 错误");
            }
           
            return new ResponseBase<LossDiagnosisKPI>()
            {
                Code = 20000,
                Data = result
            };
        }

        [HttpGet("GetPowerBreaker")]
        [SwaggerOperation(Summary = "Swagger_LossDiagnosis_GetPowerBreaker", Description = "Swagger_LossDiagnosis_GetPowerBreaker_Desc")]
        [Authorize(policy: Permission.Default)]
        public ResponseBase<PowerBreakerViewModel[]> GetPowerBreaker()
        {

            var result =  _client.Queryable<PowerBreaker>().Select<PowerBreakerViewModel>().ToArray();

            return new ResponseBase<PowerBreakerViewModel[]>()
            {
                Code = 20000,
                Data = result
            };
        }

        [HttpPost("AddPowerBreaker")]
        [SwaggerOperation(Summary = "Swagger_LossDiagnosis_AddPowerBreaker", Description = "Swagger_LossDiagnosis_AddPowerBreaker_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<int>> AddPowerBreaker(PowerBreakerViewModel powerBreaker)
        {
            if(powerBreaker ==null || string.IsNullOrEmpty(powerBreaker.model))
            {
                return new ResponseBase<int>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            var pb = new PowerBreaker
            {
               
                Model = powerBreaker.model,
                CreatedBy = UserName,
                UpdatedBy= UserName,
                CreatedTime= DateTime.Now,
                UpdatedTime= DateTime.Now,
                Power=powerBreaker.power,
                RatedCurrent=powerBreaker.RatedCurrent

            };
            var id = await _client.Insertable(pb).ExecuteReturnIdentityAsync();
            return new ResponseBase<int>()
            {
                Code = 20000,
                Data = id
            };
        }

        [HttpGet("GetGetlineImpedance")]
        [SwaggerOperation(Summary = "Swagger_LossDiagnosis_GetGetlineImpedance", Description = "Swagger_LossDiagnosis_GetGetlineImpedance_Desc")]
        [Authorize(policy: Permission.Default)]
        public ResponseBase<LineImpedanceViewModel[]> GetlineImpedance()
        {
            var result = _client.Queryable<LineImpedance>().Select<LineImpedanceViewModel>().ToArray();

            return new ResponseBase<LineImpedanceViewModel[]>()
            {
                Code = 20000,
                Data = result
            };
        }

        [HttpPost("AddLineImpedance")]
        [SwaggerOperation(Summary = "Swagger_LossDiagnosis_AddLineImpedance", Description = "Swagger_LossDiagnosis_AddLineImpedance_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<int>> AddLineImpedance(LineImpedanceViewModel li)
        {
            if (li == null || string.IsNullOrEmpty(li.Standard))
            {
                return new ResponseBase<int>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            var data = new LineImpedance
            {
                ResistancePerMeter = li.ResistancePerMeter,
                CreatedBy = UserName,
                UpdatedBy = UserName,
                CreatedTime = DateTime.Now,
                UpdatedTime = DateTime.Now,
                Standard = li.Standard,
                ReactancePerMeter = li.ReactancePerMeter,
                Ampacity = li.Ampacity
            };
            var id = await _client.Insertable(data).ExecuteReturnIdentityAsync();
            return new ResponseBase<int>()
            {
                Code = 20000,
                Data = id
            };
        }

        [HttpGet("CheckTopoLinkNode")]
        [SwaggerOperation(Summary = "Swagger_LossDiagnosis_CheckTopoLinkNode", Description = "Swagger_LossDiagnosis_CheckTopoLinkNode_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<JObject>> CheckTopoLinkNode(int topoId)
        {
            var topoplogyInfo = await _client.Queryable<TopologyInfo>().FirstAsync(t => t.Id == topoId);
            var topoInfo = new JObject();
            if (topoplogyInfo != null)
            {
                bool checkSucess = true;
                topoInfo = JObject.Parse(topoplogyInfo.Data);
                var linksToken = topoInfo.SelectToken("topology.linkDataArray");
                if (linksToken != null && linksToken is JArray nodesArray)
                {
                    foreach (JToken info in nodesArray)
                    {
                        if (info["line_type_id"] == null || info["length"] == null)
                        {
                            info["checkResult"] = false;
                            checkSucess = false;
                            continue;
                        }
                    }
                }
                topoInfo["checkSucess"] = checkSucess;
            }
            
            return new ResponseBase<JObject>()
            {
                Code = 20000,
                Data = topoInfo
            };
        }

        [HttpGet("CheckTopoBreakerNode")]
        [SwaggerOperation(Summary = "Swagger_LossDiagnosis_CheckTopoBreakerNode", Description = "Swagger_LossDiagnosis_CheckTopoBreakerNode_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<JObject>> CheckTopoBreakerNode(int topoId)
        {
            var topoplogyInfo = await _client.Queryable<TopologyInfo>().FirstAsync(t => t.Id == topoId);
            var topoInfo = new JObject();
            if (topoplogyInfo != null)
            {
                bool checkSucess = true;
                topoInfo = JObject.Parse(topoplogyInfo.Data);
                var nodesToken = topoInfo.SelectToken("topology.nodeDataArray");
                if (nodesToken != null && nodesToken is JArray nodesArray)
                {
                    foreach (JToken info in nodesArray)
                    {
                        if (info is JObject node)
                        {
                            if (node.TryGetValue("type", out var tokenValue) && tokenValue.ToString() == "B")
                            {
                                JToken? assetIdToken = null;
                                // check if the assetid is selected,if not, check failed,if have,check power and current
                                if (!node.TryGetValue("assetId", out assetIdToken) || assetIdToken.Value<int>() == 0)
                                {
                                    node["checkResult"] = false;
                                    checkSucess = false;
                                    continue;
                                }
                                else
                                {
                                    int assetid = assetIdToken.Value<int>();
                                    var deviceInfo = await _client.Queryable<DeviceDetails>().FirstAsync(t => t.AssetId == assetid);
                                    if (deviceInfo != null)
                                    {
                                        if (deviceInfo.RatedPower == null || deviceInfo.RatedPower == 0)
                                        {
                                            info["checkResult"] = false;
                                            checkSucess = false;
                                            continue;
                                        }
                                    }
                                    else
                                    {
                                        info["checkResult"] = false;
                                        checkSucess = false;
                                        continue;
                                    }
                                }
                            }
                        }
                    }
                }
                topoInfo["checkSucess"] = checkSucess;
            }

            return new ResponseBase<JObject>()
            {
                Code = 20000,
                Data = topoInfo
            };
        }

        [HttpGet("CheckCAIPredict")]
        [SwaggerOperation(Summary = "Swagger_LossDiagnosis_CheckCAIPredict", Description = "Swagger_LossDiagnosis_CheckCAIPredict_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<JObject>> CheckCAIPredict(int topoId)
        {
            Dictionary<string, object> requestBody = new Dictionary<string, object>();
            requestBody.Add("topology_id", topoId.ToString());
           
            try
            {
                var response = await _lossDiagnose.GetSysLossdataApi(requestBody, "AlgorithmEvaluationApiPath", "/loss/checkPredict");
                var re = JsonConvert.DeserializeObject<JObject>(response);
                return new ResponseBase<JObject>()
                {
                    Code = 20000,
                    Data = re,
                };
            }
            catch(Exception ex)
            {
                _logger.LogError(ex, "CheckCAIPredict Error");
                var d = new Dictionary<string, bool>();
                d.Add("result", false);
                return new ResponseBase<JObject>()
                {
                    Code = 20000,
                    Data = JObject.FromObject(d)
                };
            }
            
        }

        [HttpGet("CheckTopoConnectedness")]
        [SwaggerOperation(Summary = "Swagger_LossDiagnosis_CheckTopoConnectedness", Description = "Swagger_LossDiagnosis_CheckTopoConnectedness_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<bool>> CheckTopoConnectedness(int topoId)
        {
            var topoplogyInfo = await _client.Queryable<TopologyInfo>().FirstAsync(t => t.Id == topoId);
            var topoInfo = new JObject();
            bool checkSucess = true;
            if (topoplogyInfo != null)
            {
                try
                {
                    topoInfo = JObject.Parse(topoplogyInfo.Data);
                    string nodesourceKey = "";
                    Dictionary<int, TopoParserModel> topoNode = new Dictionary<int, TopoParserModel>();
                    List<int> outletNode = new List<int>();
                    var nodesToken = topoInfo.SelectToken("topology.nodeDataArray");
                    var linksToken = topoInfo.SelectToken("topology.linkDataArray");

                    if (nodesToken != null && nodesToken is JArray nodesArray && linksToken != null && linksToken is JArray linksArray)
                    {
                        foreach (JToken info in nodesArray)
                        {
                            if (info is JObject node)
                            {
                                var keyToken = node.GetValue("key");
                                var typeToken = node.GetValue("type");
                                var nameToken = node.GetValue("name");
                                var typeStr = typeToken?.Value<string>() ?? string.Empty;
                                var nameStr = nameToken?.Value<string>() ?? string.Empty;
                                if (keyToken != null)
                                {
                                    int key = keyToken.Value<int>();
                                    TopoParserModel topoParserModel = new TopoParserModel(key, typeStr, nameStr);

                                    topoNode.Add(key, topoParserModel);
                                    if (typeStr == "S")
                                    {
                                        nodesourceKey = key.ToString();
                                    }
                                    if (nameStr == "Outlet")
                                    {
                                        int outKey = key;
                                        outletNode.Add(outKey);
                                    }
                                    foreach (JToken linkinfo in linksArray)
                                    {
                                        if (linkinfo is JObject linkObj)
                                        {
                                            var fromToken = linkObj.GetValue("from");
                                            var toToken = linkObj.GetValue("to");
                                            int from = fromToken?.Value<int>() ?? -1;
                                            int to = toToken?.Value<int>() ?? -1;
                                            if (from == key)
                                            {
                                                topoParserModel.ChildNode.Add(to);
                                            }
                                            if (to == key)
                                            {
                                                topoParserModel.ChildNode.Add(from);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        foreach (int node in outletNode)
                        {
                            bool flag = false;
                            var nodeInfo = topoNode[node];
                            if (nodeInfo != null)
                            {

                                foreach (int child in nodeInfo.ChildNode)
                                {
                                    TestNode(node, child, topoNode, ref flag);
                                }
                            }
                            if (!flag)
                            {
                                checkSucess = false; break;
                            }
                        }
                    }
                }
                catch(Exception ex)
                {
                    _logger.LogError(ex.Message, "CheckTopoConnectedness Error");
                    var d = new Dictionary<string, bool>();
                    d.Add("result", false);
                    return new ResponseBase<bool>()
                    {
                        Code = 20000,
                        Data = false
                    };
                }
              
            }
            else
            {
                checkSucess = false;
            }
            return new ResponseBase<bool>()
            {
                Code = 20000,
                Data = checkSucess
            };
        }

        [HttpGet("CheckEmpiricalAnalysis")]
        [SwaggerOperation(Summary = "Swagger_LossDiagnosis_CheckEmpiricalAnalysis", Description = "Swagger_LossDiagnosis_CheckEmpiricalAnalysis_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<bool>> CheckEmpiricalAnalysis(int topoId)
        {
            if (topoId <= 0)
            {
                return new ResponseBase<bool>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }

            var isPass = false;
            var topologyInfo = await _client.Queryable<TopologyInfo>().FirstAsync(t => t.Id == topoId);

            if (topologyInfo == null)
            {
                return new ResponseBase<bool>()
                {
                    Code = 20000,
                    Data = false
                };
            }

            var assetTypes = new string[] { "ACB", "MCCB", "MCB", "Meter", "MotorProtector" };
            var canListenDevices = await _client.Queryable<AssetInfo>()
                .Where(a => a.AssetLevel == AssetLevel.Device && a.ObjectId != null && assetTypes.Contains(a.AssetType))
                .Select(a => a.Id)
                .ToArrayAsync();

            var topoInfo = JObject.Parse(topologyInfo.Data);
            var nodesToken = topoInfo.SelectToken("topology.nodeDataArray");
            if (nodesToken != null && nodesToken is JArray nodesArray)
            {
                var groupToken = topoInfo.SelectToken("group");
                if (groupToken != null && groupToken is JObject groupObj)
                {
                    var properties = groupObj.Properties();
                    isPass = true;
                    foreach (var property in properties)
                    {
                        if (property.Value is JObject propertyObj)
                        {
                            var levelToken = propertyObj.GetValue("level");
                            if (levelToken == null) continue;
                            var levelStr = levelToken.Value<string>();
                            if ("Circuit".Equals(levelStr))
                            {
                                var listToken = propertyObj.GetValue("list");
                                if (listToken == null)
                                {
                                    isPass = false;
                                    break;
                                }
                                var listArray = listToken.Value<int[]>();
                                if (listArray == null || listArray.Length == 0)
                                {
                                    isPass = false;
                                    break;
                                }

                                if (!listArray.Any(l => canListenDevices.Contains(l)))
                                {
                                    isPass = false;
                                    break;
                                }
                            }
                            else
                            {
                                isPass = false;
                                break;
                            }
                        }
                    }

                    return new ResponseBase<bool>()
                    {
                        Code = 20000,
                        Data = isPass
                    };
                }

                var linksToken = topoInfo.SelectToken("topology.linkDataArray");
                if (linksToken != null && linksToken is JArray linksArray)
                {
                    var panelPointKeys = new List<int>();
                    var linkDatas = new List<LineData>();
                    foreach (var linkToken in linksArray)
                    {
                        var linkData = linkToken.ToObject<LineData>();
                        if (linkData != null)
                        {
                            linkDatas.Add(linkData);
                            if (string.IsNullOrEmpty(linkData.BusBarId)) continue;
                            var fromId = linkData.From;
                            var toId = linkData.To;
                            if (fromId > 0)
                            {
                                panelPointKeys.Add(fromId);
                            }
                            if (toId > 0)
                            {
                                panelPointKeys.Add(toId);
                            }
                        }
                    }

                    if (panelPointKeys.Count > 0)
                    {
                        isPass = true;
                        panelPointKeys = panelPointKeys.Distinct().ToList();
                        foreach (var panelKey in panelPointKeys)
                        {
                            var r = CheckPanelDevice(panelKey, nodesArray, linkDatas, canListenDevices);
                            if (!r)
                            {
                                isPass = false;
                                break;
                            }
                        }
                    }
                }

            }
            return new ResponseBase<bool>()
            {
                Code = 20000,
                Data = isPass
            };
        }

        private void TestNode(int sourcekey, int childkey, Dictionary<int, TopoParserModel> topoNode, ref bool flag)
        {
            var model = topoNode[childkey];
            if (model.Type == "S")
            {
                flag = true;
                return;
            }
            foreach (int key in model.ChildNode)
            {
                if (sourcekey == key)
                {
                    continue;
                }
                var child = topoNode[key];
                if (model.ChildNode.Count == 1)
                {
                    break;
                }
                TestNode(childkey, key, topoNode, ref flag);
            }
        }

        private bool CheckPanelDevice(int startNodeKey, JArray nodesArray, List<LineData> lineDatas, int[] canListenDevices, int? from = null)
        {
            var isPass = false;

            var node = nodesArray.FirstOrDefault(n => startNodeKey == n.Value<int>("key"));
            if (node == null) return isPass;
            var typeStr = node.Value<string>("type");

            switch (typeStr)
            {
                case "L":
                case "S":
                    return isPass;
                case "B":
                case "P":
                    {
                        var assetId = node.Value<int>("assetId");
                        if (canListenDevices.Contains(assetId))
                            return true;
                    }
                    break;
                case "A":
                    break;
                default:break;
            }

            var lines = lineDatas.Where(l => (l.From == startNodeKey || l.To == startNodeKey) && string.IsNullOrEmpty(l.BusBarId) && (from == null || (l.From != from && l.To != from))).ToArray();

            if (lines != null && lines.Length > 0)
            {
                isPass = true;
                foreach (var line in lines)
                {
                    var startIndex = -1;
                    if (line.From == startNodeKey)
                    {
                        startIndex = line.To;
                    }
                    else if(line.To == startNodeKey) 
                    {
                        startIndex = line.From;
                    }
                    if (startIndex > 0)
                    {
                        var r = CheckPanelDevice(startIndex, nodesArray, lineDatas, canListenDevices, startNodeKey);
                        if (!r)
                        {
                            isPass = false;
                            break;
                        }
                    }
                    else
                    {
                        isPass = false;
                        break;
                    }
                }
            }

            return isPass;
        }
    }
}
