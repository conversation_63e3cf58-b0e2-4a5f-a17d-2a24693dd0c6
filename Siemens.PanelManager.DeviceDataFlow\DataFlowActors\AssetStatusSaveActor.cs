﻿using Akka.Actor;
using InfluxDB.Client;
using Microsoft.Extensions.Configuration;
using InfluxDB.Client.Api.Domain;
using InfluxDB.Client.Writes;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.DeviceDataFlow.Model;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.Config;
using Siemens.PanelManager.Server.Asset;

namespace Siemens.PanelManager.DeviceDataFlow.DataFlowActors
{
    internal class AssetStatusSaveActor : ReceiveActor
    {
        private readonly ILogger<AssetStatusSaveActor> _logger;
        private readonly IServiceProvider _provider;
        private IInfluxDBRef? _influxDBRef;
        public const string RealTimeMeasurementName = "archivedatarealtime";

        public AssetStatusSaveActor(ILogger<AssetStatusSaveActor> logger,
            IServiceProvider provider)
        {
            _logger = logger;
            _provider = provider;

            Receive<AssetSaveParam>(SaveData);
        }


        private void SaveData(AssetSaveParam saveParam)
        {
            if (_influxDBRef == null)
            {
                _influxDBRef = _provider.GetRequiredService<IInfluxDBRef>();
            }

            PointData pointData = PointData.Measurement(RealTimeMeasurementName);
            pointData = pointData
            .Tag("assetid", saveParam.AssetId.ToString())
            .Tag("objectid", saveParam.ObjectId ?? string.Empty)
            .Timestamp(saveParam.Time, WritePrecision.Ms);

            foreach (var item in saveParam.Datas)
            {
                // 转换不成功的数据不插入
                if (double.TryParse(item.Value, out var tempValue))
                {
                    pointData = pointData.Field(item.Key.ToLower(), tempValue);
                }
            }

            _influxDBRef.AppendData(pointData);
        }


        protected override bool AroundReceive(Receive receive, object message)
        {
            try
            {
                return base.AroundReceive(receive, message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AssetStatusSaveActor Failed");
                return true;
            }
        }
    }
}
