﻿using Siemens.PanelManager.Model.Database.WorkOrder;
using SqlSugar;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class SnapshotTableResult
    {
        public int? AssetId { get; set; }
        public string AssetName { get; set; } = string.Empty;
        public string AssetNumber { get; set; } = string.Empty;
        public string BuyNo { get; set; } = string.Empty;
        public string Position { get; set; } = string.Empty;
        public string IndicatorInfo { get; set; } = string.Empty;
    }
}
