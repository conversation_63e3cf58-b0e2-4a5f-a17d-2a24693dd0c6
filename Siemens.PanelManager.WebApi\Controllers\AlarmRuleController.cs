﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Siemens.PanelManager.Common;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.Model.Emun;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.Server.DataPoint;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class AlarmRuleController : SiemensApiControllerBase
    {
        private ILogger<AlarmRuleController> _log;
        private ISqlSugarClient _client;
        private IServiceProvider _provider;
        private AlarmExtendServer _alarmExtendServer;
        private SiemensCache _cache;
        private const string LastUpdatedTimeCacheKey = "Alarm:LastUpdatedTime";

        public AlarmRuleController(
            SiemensCache cache,
            SqlSugarScope client,
            IServiceProvider provider,
            ILogger<AlarmRuleController> logger,
            AlarmExtendServer alarmExtendServer)
            : base(provider, cache)
        {
            _cache = cache;
            _log = logger;
            _client = client;
            _provider = provider;
            _alarmExtendServer = alarmExtendServer;
        }

        #region Get
        [HttpGet("history/{id}")]
        [SwaggerOperation(Summary = "Swagger_AlamRule_GetHistroy", Description = "Swagger_AlamRule_GetHistroy_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<SearchBase<AlarmRuleHistoryResult>> GetHistroy(int id, [AllowNull] int page = 1, [AllowNull] int pageSize = 10)
        {
            if (id <= 0 || pageSize <= 0 || page <= 0)
            {
                return new SearchBase<AlarmRuleHistoryResult>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            if (!await _client.Queryable<AlarmRule>().AnyAsync(a => a.Id == id))
            {
                return new SearchBase<AlarmRuleHistoryResult>()
                {
                    Code = 40400,
                    Message = MessageContext.GetErrorValue("Common_NotExists")
                };
            }

            RefAsync<int> totalNumber = new RefAsync<int>();
            RefAsync<int> totalPage = new RefAsync<int>();
            var result = await _client.Queryable<AlarmRuleChangeHistory>().Where(h => h.RuleId == id).OrderByDescending(a => a.Timestamp).ToPageListAsync(page, pageSize, totalNumber, totalPage);

            var list = new List<AlarmRuleHistoryResult>();
            foreach (var item in result)
            {
                list.Add(new AlarmRuleHistoryResult(item, MessageContext));
            }

            return new SearchBase<AlarmRuleHistoryResult>()
            {
                Page = page,
                TotalCount = totalNumber.Value,
                Code = 20000,
                Items = list
            };
        }

        [HttpGet]
        [SwaggerOperation(Summary = "Swagger_AlamRule_SelectAll", Description = "Swagger_AlamRule_SelectAll_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<SearchBase<AlarmRuleInfoResult>> GetAll([AllowNull] int page = 1, [AllowNull] int pageSize = 10)
        {
            if (pageSize <= 0 || page <= 0)
            {
                return new SearchBase<AlarmRuleInfoResult>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            RefAsync<int> totalNumber = new RefAsync<int>();
            RefAsync<int> totalPage = new RefAsync<int>();
            var result = await _client.Queryable<AlarmRule>().Where(r => !r.IsDelete).OrderByDescending(a => a.Id).ToPageListAsync(page, pageSize, totalNumber, totalPage);
            var assetIds = result.Where(r => r.TargetType == AlarmTargetType.Device).Select(r =>
            {
                int id = 0;
                int.TryParse(r.TargetValue ,out id);
                return id;
            }).ToArray();

            var assetInfoes = await _client.Queryable<AssetInfo>()
                .Where(a => assetIds.Contains(a.Id))
                .ToArrayAsync();

            var dataPointServer = _provider.GetRequiredService<DataPointServer>();
            var alarmRuleServer = _provider.GetRequiredService<AlarmRuleServer>();

            var list = new List<AlarmRuleInfoResult>();
            foreach (var item in result)
            {
                if (item.TargetType == AlarmTargetType.Device && int.TryParse(item.TargetValue, out var assetId))
                {
                    var assetInfo = assetInfoes.FirstOrDefault(a => a.Id == assetId);
                    if (assetInfo != null && assetInfo.IsGeneralEquipment())
                    {
                        var dataPointList = await dataPointServer.GetDataPointInfos(assetInfo.AssetLevel, assetInfo.AssetType, assetInfo.AssetModel, assetInfo.Id);
                        string ruleInfo = alarmRuleServer.GetRuleInfo(item, dataPointList, MessageContext);
                        list.Add(new AlarmRuleInfoResult(item, ruleInfo));
                        continue;
                    }
                }

                list.Add(new AlarmRuleInfoResult(item, MessageContext));
            }

            return new SearchBase<AlarmRuleInfoResult>()
            {
                Page = page,
                TotalCount = totalNumber.Value,
                Code = 20000,
                Items = list
            };
        }

        [HttpGet("{id}")]
        [SwaggerOperation(Summary = "Swagger_AlamRule_Select", Description = "Swagger_AlamRule_Select_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<AlarmRuleInfoResult>> Get(int id)
        {
            if (id <= 0)
            {
                return new ResponseBase<AlarmRuleInfoResult>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var rule = await _client.Queryable<AlarmRule>().FirstAsync(a => a.Id == id);
            if (rule == null)
            {
                return new ResponseBase<AlarmRuleInfoResult>()
                {
                     Code = 40400,
                    Message = MessageContext.GetErrorValue("Common_NotExists")
                };
            }

            return new ResponseBase<AlarmRuleInfoResult>()
            {
                Code = 20000,
                Data = new AlarmRuleInfoResult(rule, MessageContext)
            };
        }
        #endregion

        #region Delete
        [HttpDelete("batch")]
        [SwaggerOperation(Summary = "Swagger_AlamRule_BatchDelete", Description = "Swagger_AlamRule_BatchDelete_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> BatchDelete(string ids)
        {
            if (string.IsNullOrEmpty(ids))
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var idsStr = ids.Split(',');
            var idList = new List<int>();
            foreach (var s in idsStr)
            {
                if (int.TryParse(s, out int id))
                {
                    idList.Add(id);
                }
            }

            if (idList.Count <= 0)
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var rules = await _client.Queryable<AlarmRule>().Where(a => idList.Contains(a.Id)).ToArrayAsync();
            if (rules == null || rules.Length <= 0)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.GetErrorValue("Common_NotExists")
                };
            }
            var histories = new List<AlarmRuleChangeHistory>();
            try
            {
                StringBuilder ruleNames = new StringBuilder();
                foreach (var rule in rules)
                {
                    if (ruleNames.Length > 0)
                    {
                        ruleNames.Append(',');
                    }
                    ruleNames.Append(rule.Name);
                    rule.IsDelete = true;
                    rule.UpdatedBy = UserName;
                    rule.UpdatedTime = DateTime.Now;

                    histories.Add(new AlarmRuleChangeHistory()
                    {
                        RuleId = rule.Id,
                        Timestamp = DateTime.Now.GetTimestampForSec(),
                        Operation = AlarmRuleOpt.Delete,
                        RuleName = rule.Name,
                        Severity = rule.Severity,
                        UpdatedBy = UserName,
                    });
                }
                _client.Ado.BeginTran();
                await _client.Updateable(rules).ExecuteCommandAsync();
                await _client.Insertable(histories).ExecuteCommandAsync();
                await _alarmExtendServer.InsertOperationLog(UserName, "DeleteAlarmRule", AlarmSeverity.Middle, _client, ruleNames.ToString());
                _client.Ado.CommitTran();
                return new ResponseBase<string>()
                {
                    Code = 20000,
                    Data = MessageContext.Success
                };
            }
            catch (Exception ex) 
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "删除失败");
                return new ResponseBase<string>()
                {
                    Code = 50000,
                    Message = MessageContext.ServerException
                };
            }
        }

        [HttpDelete("{id}")]
        [SwaggerOperation(Summary = "Swagger_AlamRule_Delete", Description = "Swagger_AlamRule_Delete_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> Delete(int id)
        {
            if (id <= 0)
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var rule = await _client.Queryable<AlarmRule>().FirstAsync(a => a.Id == id);
            if (rule == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.GetErrorValue("Common_NotExists")
                };
            }

            try
            {
                _client.Ado.BeginTran();
                rule.IsDelete = true;
                rule.UpdatedBy = UserName;
                rule.UpdatedTime = DateTime.Now;
                await _client.Updateable(rule).ExecuteCommandAsync();
                await _alarmExtendServer.InsertOperationLog(UserName, "DeleteAlarmRule", AlarmSeverity.Middle, _client, rule.Name);
                var history = new AlarmRuleChangeHistory()
                {
                    RuleId = rule.Id,
                    Timestamp = DateTime.Now.GetTimestampForSec(),
                    Operation = AlarmRuleOpt.Delete,
                    RuleName = rule.Name,
                    RuleInfo = rule.RuleInfo,
                    Severity = rule.Severity,
                    UpdatedBy = UserName,
                };
                await _client.Insertable(history).ExecuteCommandAsync();
                _cache.Clear(LastUpdatedTimeCacheKey);
                _client.Ado.CommitTran();
                return new ResponseBase<string>()
                {
                    Code = 20000,
                    Data = MessageContext.Success
                };
            }
            catch (Exception ex)
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "保存失败");
                return new ResponseBase<string>()
                {
                    Code = 50000,
                    Message = MessageContext.ServerException
                };
            }
        }
        #endregion

        #region Post
        [HttpPost()]
        [SwaggerOperation(Summary = "Swagger_AlamRule_Add", Description = "Swagger_AlamRule_Add_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<int>> Add(AlarmRuleInfoResult param)
        {
            if (param == null
                || string.IsNullOrEmpty(param.Detail)
                || param.Name.MustCharAndNotOnlyNumberOrSymbol(20)
                || !param.Validation())
            {
                return new ResponseBase<int>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var alarmRule = param.GetAlarmRule();

            if (!await ValidationAlarmRule(alarmRule))
            {
                return new ResponseBase<int>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            alarmRule.CreatedBy = UserName;
            alarmRule.CreatedTime = DateTime.Now;
            alarmRule.UpdatedBy = UserName;
            alarmRule.UpdatedTime = DateTime.Now;

            try
            {
                _client.Ado.BeginTran();

                int ruleId = await _client.Insertable<AlarmRule>(alarmRule).ExecuteReturnIdentityAsync();
                await _alarmExtendServer.InsertOperationLog(UserName, "AddAlarmRule", AlarmSeverity.Middle, _client, alarmRule.Name);
                var history = new AlarmRuleChangeHistory()
                {
                    RuleId = ruleId,
                    Timestamp = DateTime.Now.GetTimestampForSec(),
                    Operation = AlarmRuleOpt.Add,
                    RuleName = alarmRule.Name,
                    RuleInfo = alarmRule.RuleInfo,
                    Severity = alarmRule.Severity,
                    UpdatedBy = UserName,
                };

                await _client.Insertable(history).ExecuteCommandAsync();
                _cache.Clear(LastUpdatedTimeCacheKey);
                _client.Ado.CommitTran();

                alarmRule.Id = ruleId;

                var server = _provider.GetRequiredService<AlarmRuleServer>();
                await server.CheckAlarm(alarmRule);

                return new ResponseBase<int>()
                {
                    Code = 20000,
                    Data = ruleId
                };
            }
            catch (Exception ex)
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "保存失败");
                return new ResponseBase<int>()
                {
                    Code = 50000,
                    Message = MessageContext.ServerException,
                };
            }
        }
        #endregion

        #region Put
        [HttpPut("{id}")]
        [SwaggerOperation(Summary = "Swagger_AlamRule_Update", Description = "Swagger_AlamRule_Update_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> Update(AlarmRuleInfoResult param, int id)
        {
            if (param == null 
                || string.IsNullOrEmpty(param.Detail) 
                || id <= 0 
                || id != param.Id 
                || param.Name.MustCharAndNotOnlyNumberOrSymbol(20)
                || !param.Validation())
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }
            var exists = await _client.Queryable<AlarmRule>().FirstAsync(a => a.Id == id && !a.IsDelete);

            if (exists == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.GetErrorValue("Common_NotExists")
                };
            }

            var alarmRule = param.GetAlarmRule();
            if (!await ValidationAlarmRule(alarmRule))
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var optStr = "UpdateAlarmRule";
            var opt = AlarmRuleOpt.Update;
            if (alarmRule.IsEnable != exists.IsEnable)
            {
                if (alarmRule.IsEnable)
                {
                    opt = AlarmRuleOpt.Enable;
                    optStr = "EnableAlarmRule";
                }
                else
                {
                    opt = AlarmRuleOpt.Disable;
                    optStr = "DisableAlarmRule";
                }
            }
            var oldName = exists.Name;
            var severity = exists.Severity;
            var ruleInfo = exists.RuleInfo;

            exists.IsEnable = alarmRule.IsEnable;
            exists.TargetValue = alarmRule.TargetValue;
            exists.Name = alarmRule.Name;
            exists.Details = alarmRule.Details;
            exists.SectionStr = alarmRule.SectionStr;
            exists.Severity = alarmRule.Severity;
            exists.TargetType = alarmRule.TargetType;
            exists.UpdatedBy = UserName;
            exists.UpdatedTime = DateTime.Now;

            try
            {
                _client.Ado.BeginTran();
                await _client.Updateable(exists).ExecuteCommandAsync();
                var history = new AlarmRuleChangeHistory()
                {
                    RuleId = exists.Id,
                    Timestamp = DateTime.Now.GetTimestampForSec(),
                    Operation = opt,
                    RuleName = oldName,
                    RuleInfo = ruleInfo,
                    Severity = severity,
                    UpdatedBy = UserName,
                };

                var storage = await _client.Insertable(history).ExecuteCommandAsync();
                await _alarmExtendServer.InsertOperationLog(UserName, optStr, AlarmSeverity.Middle, _client, oldName);
                _cache.Clear(LastUpdatedTimeCacheKey);
                _client.Ado.CommitTran();

                var server = _provider.GetRequiredService<AlarmRuleServer>();
                await server.CheckAlarm(exists);

                return new ResponseBase<string>()
                {
                    Code = 20000,
                    Data = MessageContext.Success
                };
            }
            catch (Exception ex) 
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "保存失败");
                return new ResponseBase<string>()
                {
                    Code = 50000,
                    Message = MessageContext.ServerException
                };
            }

        }

        [HttpPut("{id}/enable")]
        [SwaggerOperation(Summary = "Swagger_AlamRule_Enable", Description = "Swagger_AlamRule_Enable_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> Enable(int id, int isEnable)
        {
            if (id<=0 || (isEnable != 0 && isEnable != 1))
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }
            var exists = await _client.Queryable<AlarmRule>().FirstAsync(a => a.Id == id && !a.IsDelete);

            if (exists == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.GetErrorValue("Common_NotExists")
                };
            }

            bool enable = isEnable == 1;
            string optStr = string.Empty;
            AlarmRuleOpt opt;
            if (enable != exists.IsEnable)
            {
                if (enable)
                {
                    opt = AlarmRuleOpt.Enable;
                    optStr = "EnableAlarmRule";
                }
                else
                {
                    opt = AlarmRuleOpt.Disable;
                    optStr = "EnableAlarmRule";
                }
            }
            else
            {
                return new ResponseBase<string>()
                {
                    Code = 20000,
                    Data = MessageContext.Success
                };
            }

            var oldName = exists.Name;
            var severity = exists.Severity;

            exists.IsEnable = enable;
            exists.UpdatedBy = UserName;
            exists.UpdatedTime = DateTime.Now;

            try
            {
                _client.Ado.BeginTran();
                await _client.Updateable(exists).UpdateColumns(t=>new { t.IsEnable, t.UpdatedBy, t.UpdatedTime }).ExecuteCommandAsync();
                var history = new AlarmRuleChangeHistory()
                {
                    RuleId = exists.Id,
                    Timestamp = DateTime.Now.GetTimestampForSec(),
                    Operation = opt,
                    RuleName = oldName,
                    RuleInfo = exists.RuleInfo,
                    Severity = severity,
                    UpdatedBy = UserName,
                };

                var storage = await _client.Insertable(history).ExecuteCommandAsync();
                await _alarmExtendServer.InsertOperationLog(UserName, optStr, AlarmSeverity.Middle, _client, oldName);
                _cache.Clear(LastUpdatedTimeCacheKey);
                _client.Ado.CommitTran();

                var server = _provider.GetRequiredService<AlarmRuleServer>();
                await server.CheckAlarm(exists);

                return new ResponseBase<string>()
                {
                    Code = 20000,
                    Data = MessageContext.Success
                };
            }
            catch (Exception ex)
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "保存失败");
                return new ResponseBase<string>()
                {
                    Code = 50000,
                    Message = MessageContext.ServerException
                };
            }
        }

        #endregion

        private async Task<bool> ValidationAlarmRule(AlarmRule rule)
        {
            AssetLevel level = AssetLevel.Area;
            switch (rule.TargetType)
            {
                case AlarmTargetType.PanelModel:
                case AlarmTargetType.CircuitModel:
                case AlarmTargetType.DeviceModel:
                    {
                        var match = Regex.Match(rule.TargetValue, "^([\\w|_|-]+)\\|([\\w|_|-]*)$");
                        if (match.Success)
                        {
                            var assetType = match.Groups[1].Value;
                            var assetModel = match.Groups[2].Value;

                            var typeCode = string.Empty;
                            var modelCode = string.Empty;
                            switch (rule.TargetType)
                            {
                                case AlarmTargetType.DeviceModel:
                                    {
                                        typeCode = "DEVICETYPE";
                                        modelCode = "DEVICEMODEL";
                                        break;
                                    }
                                case AlarmTargetType.PanelModel:
                                    {
                                        typeCode = "PANELTYPE";
                                        modelCode = "PANELMODEL";
                                        break;
                                    }
                                case AlarmTargetType.CircuitModel:
                                    {
                                        typeCode = "CIRCUITTYPE";
                                        modelCode = "USESCENE";
                                        break;
                                    }
                                default: break;
                            }

                            var staticCode = await _client.Queryable<SystemStaticModel>().FirstAsync(s => s.Type == typeCode && s.Code == assetType);
                            if (staticCode == null) return false;
                            if (!string.IsNullOrEmpty(assetModel))
                            {
                                if (!string.IsNullOrEmpty(modelCode))
                                {
                                    staticCode = await _client.Queryable<SystemStaticModel>().FirstAsync(s => s.Type == modelCode && s.Code == assetModel);
                                    if (staticCode == null) return false;
                                    return true;
                                }
                                return false;
                            }
                        }
                        return false;
                    }
                case AlarmTargetType.Circuit:
                case AlarmTargetType.Device:
                case AlarmTargetType.Transformer:
                case AlarmTargetType.Panel:
                case AlarmTargetType.Substation:
                    if (int.TryParse(rule.TargetValue, out var assetId))
                    {
                        return await _client.Queryable<AssetInfo>().AnyAsync(a=>a.Id == assetId);
                    }
                    return false;
            }

            switch (rule.TargetType)
            {
                case AlarmTargetType.CircuitModel:
                case AlarmTargetType.Circuit:
                    level = AssetLevel.Circuit;
                    break;
                case AlarmTargetType.DeviceModel:
                case AlarmTargetType.Device:
                    level = AssetLevel.Device;
                    break;
                case AlarmTargetType.Panel:
                case AlarmTargetType.PanelModel:
                    level = AssetLevel.Panel;
                    break;
                case AlarmTargetType.Substation:
                    level = AssetLevel.Substation;
                    break;
                case AlarmTargetType.Transformer:
                    level = AssetLevel.Transformer;
                    break;
                default:break;
            }
            if (await ValidationSections(level, rule.Sections))
            {
                return true;
            }
            return false;
        }

        private async Task<bool> ValidationSections(AssetLevel level, List<RuleSection> sections)
        {
            var pointList = sections.Select(s => s.Point).Distinct().ToList();
            var count = await _client.Queryable<AssetDataPointInfo>().CountAsync(dp => dp.AssetLevel == level && pointList.Contains(dp.Code));

            return pointList.Count == count;
        }
    }
}
