﻿using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.WebApi.StaticContent;
using System.Text;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class AssetInfoExtend
    {
        public AssetInfoExtend()
        {

        }
        public AssetInfoExtend(AssetInfo asset,
            SystemStaticModel[] deviceTypeList,
            SystemStaticModel[] deviceModelList,
            SystemStaticModel[] panelTypeList,
            SystemStaticModel[] TransformerTypeList,
            SystemStaticModel[] circuitTypeList,
            SystemStaticModel[] panelModelList,
            SystemStaticModel[] transformerModelList,
            SystemStaticModel[] useSceneList,
            SystemStaticModel[] meterTypeList,
            MessageContext messageContext)
        {
            Id = asset.Id;
            AssetName = asset.AssetName;
            AssetType = asset.AssetType ?? string.Empty;
            AssetNumber = asset.AssetNumber;
            AssetLevel = asset.AssetLevel;
            AssetModel = asset.AssetModel ?? string.Empty;
            MLFB = asset.MLFB ?? string.Empty;
            Description = asset.Description ?? string.Empty;
            Telephone = asset.Telephone ?? string.Empty;
            Principal = asset.Principal ?? string.Empty;
            AssetMaker = asset.AssetMaker ?? string.Empty;
            Location = asset.Location ?? string.Empty;
            PointData = asset.PointData ?? string.Empty;
            UseScene = asset.UseScene ?? string.Empty;
            MeterType = asset.MeterType ?? string.Empty;
            InstallDate = asset.InstallDate;
            CreatedTime = asset.CreatedTime;
            CircuitName = asset.CircuitName ?? string.Empty;
            SortNo = asset.SortNoNotNull;
            OpenMqtt = (asset.EnableMqtt ?? false) ? "1" : "0";

            #region 时间格式
            if (InstallDate != null && InstallDate.HasValue)
            {
                InstallDateStr = InstallDate.Value.ToString("yyyy/MM/dd");
            }

            if (CreatedTime != null && CreatedTime.HasValue)
            {
                CreateTimeStr = CreatedTime.Value.ToString("yyyy/MM/dd");
            }

            switch (AssetLevel)
            {
                case AssetLevel.Circuit:
                    {
                        if (!string.IsNullOrEmpty(AssetType))
                        {
                            var circuitType = circuitTypeList.FirstOrDefault(d => d.Code == AssetType);
                            if (circuitType != null)
                            {
                                AssetTypeStr = messageContext.GetStaticModelName(circuitType);
                            }
                            else
                            {
                                AssetTypeStr = AssetType;
                            }
                        }

                        if (!string.IsNullOrEmpty(UseScene))
                        {
                            var useScene = useSceneList.FirstOrDefault(d => d.Code == UseScene);
                            if (useScene != null)
                            {
                                UseSceneStr = messageContext.GetStaticModelName(useScene);
                            }
                        }

                        Width = asset.Width ?? string.Empty;
                        Height = asset.Height ?? string.Empty;
                        break;
                    }
                case AssetLevel.Device:
                    {
                        if (!string.IsNullOrEmpty(AssetType))
                        {
                            var deviceType = deviceTypeList.FirstOrDefault(d => d.Code == AssetType);
                            if (deviceType != null)
                            {
                                AssetTypeStr = messageContext.GetStaticModelName(deviceType);
                            }
                        }

                        if (!string.IsNullOrEmpty(AssetModel))
                        {
                            var deviceModel = deviceModelList.FirstOrDefault(d => d.Code == AssetModel);
                            if (deviceModel != null)
                            {
                                AssetModelStr = messageContext.GetStaticModelName(deviceModel);
                            }
                        }

                        if (!string.IsNullOrEmpty(MeterType))
                        {
                            var meterType = meterTypeList.FirstOrDefault(m => m.Code == MeterType);
                            if (meterType != null)
                            {
                                MeterTypeStr = messageContext.GetStaticModelName(meterType);
                            }
                        }

                        IP = asset.IPAddress ?? string.Empty;
                        Port = asset.Port ?? string.Empty;

                    }
                    break;
                case AssetLevel.Panel:
                    {
                        if (!string.IsNullOrEmpty(AssetType))
                        {
                            var panelType = panelTypeList.FirstOrDefault(p => p.Code == AssetType);
                            if (panelType != null)
                            {
                                AssetTypeStr = messageContext.GetStaticModelName(panelType);
                            }
                            else
                            {
                                AssetTypeStr = AssetType;
                            }
                        }

                        if (!string.IsNullOrEmpty(AssetModel))
                        {
                            var panelModel = panelModelList.FirstOrDefault(d => d.Code == AssetModel);
                            if (panelModel != null)
                            {
                                AssetModelStr = messageContext.GetStaticModelName(panelModel);
                            }
                            else
                            {
                                AssetModelStr = AssetModel;
                            }
                        }

                        RowNo = asset.RowNo ?? string.Empty;
                        Height = asset.Height ?? string.Empty;
                        Width = asset.Width ?? string.Empty;
                        if (string.IsNullOrEmpty(asset.BusBarId))
                        {
                            BarNo = string.Empty;
                        }
                        else
                        {
                            var busBarIdList = asset.BusBarId.Split(",");
                            var barNo = new StringBuilder();
                            foreach (var busBarId in busBarIdList)
                            {
                                if (int.TryParse(busBarId, out var barId))
                                {
                                    if (barNo.Length > 0)
                                    {
                                        barNo.Append(',');
                                    }
                                    barNo.Append(RomanNumberHelper.GetString(barId));
                                }
                            }
                            BarNo = barNo.ToString();
                        }
                        break;
                    }
                case AssetLevel.Transformer:
                    {
                        if (!string.IsNullOrEmpty(AssetType))
                        {
                            var panelType = TransformerTypeList.FirstOrDefault(p => p.Code == AssetType);
                            if (panelType != null)
                            {
                                AssetTypeStr = messageContext.GetStaticModelName(panelType);
                            }
                            else
                            {
                                AssetTypeStr = AssetType;
                            }
                        }

                        if (!string.IsNullOrEmpty(AssetModel))
                        {
                            var panelModel = transformerModelList.FirstOrDefault(d => d.Code == AssetModel);
                            if (panelModel != null)
                            {
                                AssetModelStr = messageContext.GetStaticModelName(panelModel);
                            }
                            else
                            {
                                AssetModelStr = AssetModel;
                            }
                        }

                        RowNo = asset.RowNo ?? string.Empty;
                        Height = asset.Height ?? string.Empty;
                        Width = asset.Width ?? string.Empty;
                        if (string.IsNullOrEmpty(asset.BusBarId))
                        {
                            BarNo = string.Empty;
                        }
                        else
                        {
                            var busBarIdList = asset.BusBarId.Split(",");
                            var barNo = new StringBuilder();
                            foreach (var busBarId in busBarIdList)
                            {
                                if (int.TryParse(busBarId, out var barId))
                                {
                                    if (barNo.Length > 0)
                                    {
                                        barNo.Append(',');
                                    }
                                    barNo.Append(RomanNumberHelper.GetString(barId));
                                }
                            }
                            BarNo = barNo.ToString();
                        }
                        break;
                    }
                default: break;
            }


            #endregion
        }

        public int Id { get; set; }
        public string AssetName { get; set; } = string.Empty;
        public string AssetNumber { get; set; } = string.Empty;
        public AssetLevel AssetLevel { get; set; }
        public string AssetModel { get; set; } = string.Empty;
        public string MLFB { get; set; } = string.Empty;
        public string AssetType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Telephone { get; set; } = string.Empty;
        public string Principal { get; set; } = string.Empty;
        public string AssetMaker { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public string PointData { get; set; } = string.Empty;
        public string UseScene { get; set; } = string.Empty;
        public string MeterType { get; set; } = string.Empty;
        public string ParentName { get; set; } = string.Empty;
        public int? ParentId { get; set; }
        public int SortNo { get; set; }
        public DateTime? InstallDate { get; set; }
        public DateTime? CreatedTime { get; set; }

        public string InstallDateStr { get; set; } = string.Empty;
        public string CreateTimeStr { get; set; } = string.Empty;
        public string AssetModelStr { get; set; } = string.Empty;
        public string AssetTypeStr { get; set; } = string.Empty;
        public string UseSceneStr { get; set; } = string.Empty;
        public string MeterTypeStr { get; set; } = string.Empty;

        public string CircuitName { get; set; } = string.Empty;
        public string OpenMqtt { get; set; } = "0";
        public string Width { get; set; } = "0";
        public string Height { get; set; } = "0";
        public string RelationPanelName { get; set; } = string.Empty;
        public string RowNo { get; set; } = "0";
        public string BarNo { get; set; } = "I";
        public string IP { get; set; } = string.Empty;
        public string Port { get; set; } = string.Empty;
        public string AssetLevelStr
        {
            get
            {
                switch (AssetLevel)
                {
                    case AssetLevel.Area:
                        return "厂区";
                    case AssetLevel.Substation:
                        return "配电房";
                    case AssetLevel.Panel:
                        return "开关柜";
                    case AssetLevel.Transformer:
                        return "变压器";
                    case AssetLevel.Circuit:
                        return "回路";
                    case AssetLevel.Device:
                        return "设备";
                }
                return string.Empty;
            }
        }
    }
}
