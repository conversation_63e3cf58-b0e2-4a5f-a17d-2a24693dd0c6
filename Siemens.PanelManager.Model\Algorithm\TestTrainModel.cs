﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Algorithm
{
    public class TestTrainModel
    {
        [JsonProperty(PropertyName = "asset")]
        public string[] asset { get; set; }

        [JsonProperty(PropertyName = "assetName")]
        public string[] assetName  { get; set; }

        [JsonProperty(PropertyName = "values")]
        public TestTrainModelVlaues values { get; set; }

    }
    public class TestTrainModelVlaues
    {
        [JsonProperty(PropertyName = "training")]
        public decimal[] training { get; set; }
        [JsonProperty(PropertyName = "testing")]
        public decimal[] testing { get; set; }
    }
}
