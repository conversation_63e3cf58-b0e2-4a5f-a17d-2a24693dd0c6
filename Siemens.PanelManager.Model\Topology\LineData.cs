﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.Model.Topology
{
    public class LineData
    {
        [JsonProperty(propertyName: "from")]
        public int From { get; set; }
        [JsonProperty(propertyName: "to")]
        public int To { get; set; }
        [JsonProperty(propertyName: "category", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public string? Category { get; set; }
        [JsonProperty(propertyName: "key", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public int? Key { get; set; }

        [JsonProperty(propertyName: "busBarId", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public string? BusBarId { get; set; }

        [JsonIgnore]
        public bool IsConnector { get; set; } = false;
    }
}
