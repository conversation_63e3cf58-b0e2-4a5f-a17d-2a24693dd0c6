﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.Model.UDC
{
    public class UdcScanProcess
    {
        [JsonProperty(PropertyName = "id")]
        public string? Id { get; set; }

        [JsonProperty(PropertyName = "type")]
        public string? Type { get; set; }

        [JsonProperty(PropertyName = "options")]
        public Options? Options { get; set; }

        [JsonProperty(PropertyName = "schedule")]
        public Schedule? Schedule { get; set; }

        [JsonProperty(PropertyName = "status")]
        public Status? Status { get; set; }

    }

    public class Options
    {
        [JsonProperty(PropertyName = "result")]
        public Result? Result { get; set; }
    }

    public class Result
    {
        [JsonProperty(PropertyName = "format")]
        public string? Format { get; set; }

        [JsonProperty(PropertyName = "zip")]
        public bool Zip { get; set; }

        [JsonProperty(PropertyName = "publish")]
        public string? Publish { get; set; }

        [JsonProperty(PropertyName = "culture")]
        public string? Culture { get; set; }

        [JsonProperty(PropertyName = "timezone")]
        public string? Timezone { get; set; }
    }

    public class Schedule
    {
        [JsonProperty(PropertyName = "type")]
        public string? Type { get; set; }
    }

    public class Status
    {
        [JsonProperty(PropertyName = "state")]
        public string? State { get; set; }

        [JsonProperty(PropertyName = "started")]
        public DateTime? Started { get; set; }

        [JsonProperty(PropertyName = "progress")]
        public float Progress { get; set; }

        [JsonProperty(PropertyName = "progress_message")]
        public string? ProgressMessage { get; set; }

        [JsonProperty(PropertyName = "result_from")]
        public DateTime? ResultFrom { get; set; }
    }
}
