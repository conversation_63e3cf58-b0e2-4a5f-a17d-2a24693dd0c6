import { IxBasicNavigation, showModal } from "@siemens/ix-react";
import { MyMenu, MessageModal, LoadingModal } from "./components";
import "./App.css";
import { useNavigate } from "react-router-dom";
import { SettingUI } from "./containers";

export default function App(props) {
  const navigate = useNavigate();
  let showSettings = false;
  if (props.isSetting !== "1") {
    let isSettings = sessionStorage.getItem("isSettings");
    let isConnected = sessionStorage.getItem("isConnected");
    if (isSettings === "0") {
      showModal({
        icon: "alarm",
        iconColor: "color-alarm",
        content: (
          <MessageModal
            title="尚未链接"
            message="需要先配置网络方可使用"
          ></MessageModal>
        ),
      });
      showSettings = true;
    } else if (isConnected === "0") {
      showModal({
        content: (
          <LoadingModal
            title="等待连接服务器"
            finishFuction={() => {
              let isConnected = sessionStorage.getItem("isConnected");
              return isConnected === "1";
            }}
          />
        ),
      }).then((m) => {
        m.onClose.listeners[0] = (r) => {
          if (r) {
            navigate(0);
          }
        };
      });
    }
  }
  return (
    <IxBasicNavigation class="Menu" hideHeader>
      <MyMenu />
      <div className="app-container">
        <div className="app-header">
          <span class="text-h2 app-header-span">
            {!showSettings ? props.headerName : "配置"}
          </span>
        </div>
        {!showSettings ? (
          <div className="app-body">{props.child}</div>
        ) : (
          <div className="app-body">
            {" "}
            <SettingUI />
          </div>
        )}
      </div>
    </IxBasicNavigation>
  );
}
