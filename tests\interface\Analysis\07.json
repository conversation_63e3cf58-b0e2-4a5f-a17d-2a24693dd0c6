{"info": {"_postman_id": "0e29b944-9493-4fdf-9f50-6f075b55fdc6", "name": "07使用管理员账号进入panel manager智慧分析中的损耗分析菜单，切换损耗or金额查看损耗电量和等量损耗金额", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 7", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "获取单个资产的详情 Copy 6", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "let s1 = pm.response.json().items[0].id\r", "pm.environment.set(\"s1\", s1);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Asset/search?levels=Substation", "host": ["{{baseUrl}}"], "path": ["api", "v1", "<PERSON><PERSON>", "search"], "query": [{"key": "levels", "value": "Substation"}]}}, "response": []}, {"name": "获取损耗电量损耗分析图表 Copy 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"成功查看损耗电量损耗分析图表\", function () {\r", "    pm.expect(pm.response.text()).to.include(\":00\");\r", "    pm.expect(pm.response.text()).to.include(\"x\");\r", "    pm.expect(pm.response.text()).to.include(\"y\");\r", "    pm.expect(pm.response.text()).to.include(\"Loss\");\r", "    pm.expect(pm.response.text()).to.include(\"Reference\");\r", "    pm.expect(pm.response.text()).to.include(\"KMS\");\r", "    pm.expect(pm.response.text()).to.include(\"%\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/LossDiagnosis/{{s1}}/GetChartData?dateType=0&chartCodes=LossDiagnosisForKMS&startDate=2023-04-20", "host": ["{{baseUrl}}"], "path": ["api", "v1", "LossDiagnosis", "{{s1}}", "GetChartData"], "query": [{"key": "dateType", "value": "0"}, {"key": "endDate", "value": "2023-04-20", "disabled": true}, {"key": "chartCodes", "value": "LossDiagnosisForKMS"}, {"key": "startDate", "value": "2023-04-20"}]}}, "response": []}, {"name": "获取损耗金额损耗分析图表 Copy 3", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"成功查看损耗金额损耗分析图表\", function () {\r", "    pm.expect(pm.response.text()).to.include(\":00\");\r", "    pm.expect(pm.response.text()).to.include(\"x\");\r", "    pm.expect(pm.response.text()).to.include(\"y\");\r", "    pm.expect(pm.response.text()).to.include(\"Loss\");\r", "    pm.expect(pm.response.text()).to.include(\"Reference\");\r", "    pm.expect(pm.response.text()).to.include(\"CNY\");\r", "    pm.expect(pm.response.text()).to.include(\"%\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/LossDiagnosis/{{s1}}/GetChartData?dateType=0&chartCodes=LossDiagnosisForCNY&startDate=2023-04-20", "host": ["{{baseUrl}}"], "path": ["api", "v1", "LossDiagnosis", "{{s1}}", "GetChartData"], "query": [{"key": "dateType", "value": "0"}, {"key": "endDate", "value": "2023-04-20", "disabled": true}, {"key": "chartCodes", "value": "LossDiagnosisForCNY"}, {"key": "startDate", "value": "2023-04-20"}]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}]}