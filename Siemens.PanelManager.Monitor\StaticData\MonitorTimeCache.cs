﻿using Siemens.PanelManager.HubModel;

namespace Siemens.PanelManager.Monitor.StaticData
{
    static class MonitorTimeCache
    {
        private static System.Collections.Concurrent.ConcurrentQueue<MonitorModel> _monitors = new System.Collections.Concurrent.ConcurrentQueue<MonitorModel>();
        private const int MonitorLength = 60;
        public static void Append(MonitorModel model)
        {
            _monitors.Enqueue(model);
            if (_monitors.Count > MonitorLength) 
            {
                _monitors.TryDequeue(out _);
            }
        }

        public static MonitorModel[] GetAll()
        {
            return _monitors.ToArray();
        }
    }
}
