{"info": {"_postman_id": "35f8a065-4358-4636-9bbc-aacbd3f125fc", "name": "W07使用超级管理员账号进入panel manager运维工单页面，输入工单编号，选择设备、工单状态、处理措施搜索工单", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 9", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "筛选查询工单", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"展示工单列表详情\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"workOrderCode\");\r", "    pm.expect(pm.response.text()).to.include(\"deviceName\");\r", "    pm.expect(pm.response.text()).to.include(\"workOrderTypeName\");\r", "    pm.expect(pm.response.text()).to.include(\"content\");\r", "    pm.expect(pm.response.text()).to.include(\"processingTime\");\r", "    pm.expect(pm.response.text()).to.include(\"measureName\");\r", "    pm.expect(pm.response.text()).to.include(\"statusName\");\r", "    pm.expect(pm.response.text()).to.include(\"createdTime\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/workOrder/getWorkOrderList?workOrderCode=MWC&device=ALL&workOrderStatus=ALL&measure=ALL&pageSize=10&page=1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "workOrder", "getWorkOrderList"], "query": [{"key": "workOrderCode", "value": "MWC"}, {"key": "device", "value": "ALL"}, {"key": "workOrderStatus", "value": "ALL"}, {"key": "measure", "value": "ALL"}, {"key": "pageSize", "value": "10"}, {"key": "page", "value": "1"}]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}]}