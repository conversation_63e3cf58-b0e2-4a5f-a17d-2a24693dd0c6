﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Quartz;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.Interface.Job;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Server.Asset;
using Siemens.PanelManager.Server.DataPoint;
using SqlSugar;

namespace Siemens.PanelManager.Job.UDCJob
{
    [DisallowConcurrentExecution]
    public class DeviceMaintainSyncJob : JobBase
    {
        public override string Name => "DeviceMaintainSyncJob";
        private readonly ILogger _logger;
        private readonly IServiceProvider _provider;

        public DeviceMaintainSyncJob(ILogger<DeviceMaintainSyncJob> logger,
            IServiceProvider provider)
        {
            _logger = logger;
            _provider = provider;
        }

        public override async Task Execute()
        {
            var service = _provider.GetRequiredService<DeviceInfoService>();
            await service.InitDeviceInfoes(_logger);

            if (DateTime.Now.Hour == 9)
            {
                var manager = _provider.GetRequiredService<IDeviceConenctStateWorkerManager>();
                await manager.StopAsync();
                await Task.Delay(10000);
                await manager.StartAsync();
            }
        }
    }
}
