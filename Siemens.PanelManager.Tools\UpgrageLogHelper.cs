﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Tools
{
    internal class UpgrageLogHelper : IDisposable
    {
        private const string Upgrage = "Upgrage";
        private string _logFileName;

        private ConcurrentQueue<string> _writeLogQueue = new ConcurrentQueue<string>();
        private CancellationTokenSource _source = new CancellationTokenSource();

        public UpgrageLogHelper()
        {
            var basePath = Path.Combine(Directory.GetCurrentDirectory(), Upgrage);
            if (!Directory.Exists(basePath)) 
            {
                Directory.CreateDirectory(basePath);
            }

            var logFileName = $"{DateTime.Now.ToString("yyyyMMddhhmmss")}";
            var filePath = Path.Combine(basePath, $"{logFileName}.log");
            if (File.Exists(filePath))
            {
                logFileName = logFileName + "(1)";
            }
            _logFileName = Path.Combine(basePath, $"{logFileName}.log");
            var fs = File.Create(_logFileName);
            fs.Close();

            Task.Run(WriteLogTask, _source.Token);
        }

        public void Dispose()
        {
            if (!_source.IsCancellationRequested)
            {
                _source.Cancel();
            }
        }

        public string ReadLog(long begin, out long next)
        {
            using (var fs = new FileStream(_logFileName, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
            {
                if (fs.Length > begin)
                {
                    fs.Position = begin;
                    next = fs.Length;

                    using (var reader = new StreamReader(fs))
                    {
                        return reader.ReadToEnd();
                    }
                }
            }
            next = begin;
            return string.Empty;
        }

        public void WriteLog(string log)
        {
            _writeLogQueue.Enqueue(log);
        }

        private async Task WriteLogTask()
        {
            while (true)
            {
                if (_source.IsCancellationRequested) break;
                using (var fs = new FileStream(_logFileName, FileMode.Open, FileAccess.Write))
                {
                    using (var sw = new StreamWriter(fs))
                    {
                        while (_writeLogQueue.TryDequeue(out var log))
                        {
                            using (var strReader = new StringReader(log))
                            {
                                int i = 0;
                                while (true)
                                {
                                    var hear = "         - ";
                                    if (i == 0)
                                    {
                                        hear = $"{DateTime.Now.ToString("HH:mm:ss")} - ";
                                    }
                                    var inputData = strReader.ReadLine();
                                    if (inputData == null) break;
                                    await sw.WriteLineAsync($"{hear}{inputData}");
                                }
                            }
                        }
                    }
                }

                await Task.Delay(300);
            }
        }

        
    }
}
