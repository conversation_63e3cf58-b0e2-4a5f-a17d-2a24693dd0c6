﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.System
{
    [SugarTable("sys_file_manager")]
    public class FileManager : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [UniquenessAttribute]
        [SugarColumn(ColumnName = "file_code", IsNullable = false)]
        public string Code { get; set; } = Guid.NewGuid().ToString();
        [SugarColumn(ColumnName = "name", IsNullable = false, Length = 512)]
        public string Name { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "url", IsNullable = false, Length = 512)]
        public string Url { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "file_type", IsNullable = false, Length = 512)]
        public string FileType { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "file_description", IsNullable = true, Length = 1024)]
        public string? FileDescription { get; set; }
        [SugarColumn(ColumnName = "is_system_file", IsNullable = false)]
        public bool IsSystemFile { get; set; } = false;
        [SugarColumn(ColumnName = "language", IsNullable = true)]
        public string? Language { get; set; }
    }
}
