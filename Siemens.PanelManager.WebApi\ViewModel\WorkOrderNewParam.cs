﻿namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class WorkOrderNewParam
    {
        public string AssetType { get; set; } = string.Empty;
        public string Device { get; set; } = string.Empty;
        public string WorkOrderType { get; set; } = string.Empty;
        public string WorkOrderName { get; set; } = string.Empty;
        public DateTime ProcessingTime { get; set; }
        public DateTime ProcessingEndTime { get; set; }
        public List<WorkOrderContentModel> Contents { get; set; } = new List<WorkOrderContentModel>();
    }

    // 工单状态枚举，只使用枚举名称
    public enum WorkOrderStatus
    {
        // 待处理
        Pending,

        //处理中
        Processing,

        // 已完成
        Completed,

        // 逾期未处理
        Overdue
    }
}
