{"info": {"_postman_id": "2b59346e-dfb7-4a40-86cd-a92b65044dc3", "name": "使用高级操作员账号进行越权操作", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "用户登录(高级操作员)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = pm.response.json().data.token//获取token\r", "pm.collectionVariables.set('token',token)//把token保存到全局变量中\r", "console.log (token)\r", "\r", "pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"test3\",\r\n  \"password\": \"test123@\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/login", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "获取用户自己的信息(成功)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"包含用户关键信息\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"userName\",\"personName\",\"prefixTel\",\"tel\",\"roles\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/api/v1/Users/<USER>", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Users", "info"]}}, "response": []}, {"name": "获取角色权限(没有权限)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"没有操作权限\", function () {\r", "    pm.response.to.have.status(403);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/api/v1/Roles/1/permission", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Roles", "1", "permission"]}}, "response": []}, {"name": "更改角色权限(没有权限)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"没有操作权限\", function () {\r", "    pm.response.to.have.status(403);\r", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"roleId\": 3,\r\n  \"permissionId\": [\r\n  1000000\r\n  ]\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/Roles/3/permission", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Roles", "3", "permission"]}}, "response": []}, {"name": "获取用户列表(没有权限)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"没有操作权限\", function () {\r", "    pm.response.to.have.status(403);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/api/v1/Users", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Users"]}}, "response": []}, {"name": "添加用户信息(没有权限)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"没有操作权限\", function () {\r", "    pm.response.to.have.status(403);\r", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"userName\": \"{{username}}\",\r\n    \"personName\": \"h111\",\r\n    \"tel\": \"15751111111\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"roles\": [\r\n        {\r\n            \"id\": 5\r\n        }\r\n    ]\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/users", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "users"]}}, "response": []}, {"name": "更新用户(没有权限)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"没有操作权限\", function () {\r", "    pm.response.to.have.status(403);\r", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Accept", "value": ""}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"id\": \"{{userId}}\",\r\n  \"personName\": \"hkn888\",\r\n  \"tel\": \"15755555\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"roles\": [\r\n    {\r\n      \"id\": 1\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/Users/<USER>", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Users", "{{userId}}"]}}, "response": []}, {"name": "获取用户角色(没有权限)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"没有操作权限\", function () {\r", "    pm.response.to.have.status(403);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/api/v1/Users/<USER>/role", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Users", "{{userId}}", "role"]}}, "response": []}, {"name": "更新用户角色(没有权限)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"没有操作权限\", function () {\r", "    pm.response.to.have.status(403);\r", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"userId\": {{userId}},\r\n  \"roles\": [\r\n    {\r\n      \"id\": 1\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/Users/<USER>/role", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Users", "{{userId}}", "role"]}}, "response": []}, {"name": "重置密码(没有权限)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"没有操作权限\", function () {\r", "    pm.response.to.have.status(403);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/api/v1/Users/<USER>/resetpwd", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Users", "{{userId}}", "resetpwd"]}}, "response": []}, {"name": "删除用户(没有权限)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"没有操作权限\", function () {\r", "    pm.response.to.have.status(403);\r", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "Accept", "value": ""}], "url": {"raw": "{{BASE_URL}}/api/v1/Users/<USER>", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Users", "{{userId}}"]}}, "response": []}, {"name": "批量删除用户(没有权限)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"没有操作权限\", function () {\r", "    pm.response.to.have.status(403);\r", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"ids\": [\r\n    {{user2Id}},{{user3Id}}\r\n  ],\r\n  \"type\": \"string\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/Users/<USER>", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Users", "batch"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}], "variable": [{"key": "usernameindex", "value": 213, "type": "string"}, {"key": "username", "value": "user-213", "type": "string"}, {"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1lIjoidGVzdCIsImh0dHA6Ly9zY2hlbWFzLnhtbHNvYXAub3JnL3dzLzIwMDUvMDUvaWRlbnRpdHkvY2xhaW1zL25hbWVpZGVudGlmaWVyIjoiZmU2MTM4NzUtOGUwMi00MmJjLWIxNTItMTlhMDdjM2U5OTdlIiwiU3luY0RldmljZSI6IltdIiwibmJmIjoxNjc3MTQxMzM5LCJleHAiOjE2NzcxNDEzNDAsImlzcyI6IlNpZW1lbnNJc3N1ZXIiLCJhdWQiOiJXZWJBcHBBdWRpZW5jZSJ9.RS21FqUpecwqwyomp9QvshWb8dD-zwTLJx6Sy8AplmU", "type": "string"}, {"key": "userId", "value": 114, "type": "string"}, {"key": "user2Id", "value": 113, "type": "string"}, {"key": "user3Id", "value": 112, "type": "string"}, {"key": "roleId", "value": 1, "type": "string"}, {"key": "user4Id", "value": 111, "type": "string"}, {"key": "BASE_URL", "value": "http://localhost:5000"}]}