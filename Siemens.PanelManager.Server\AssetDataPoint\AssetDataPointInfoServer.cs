﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.UDC;
using SqlSugar;

namespace Siemens.PanelManager.Server.AssetDataPoint
{
    /// <summary>
    /// 点位公共服务
    /// </summary>
    public class AssetDataPointInfoServer
    {
        private readonly SqlSugarScope _db;

        private readonly ILogger _log;

        private readonly SiemensCache _cache;

        /// <summary>
        ///  构造函数初始化
        /// </summary>
        /// <param name="provider"></param>
        /// <param name="log"></param>
        /// <param name="cache"></param>
        public AssetDataPointInfoServer(IServiceProvider provider, ILogger<AssetDataPointInfoServer> log, SiemensCache cache)
        {
            _db = provider.GetService<SqlSugarScope>()!;
            _log = log;
            _cache = cache;
        }

        /// <summary>
        /// 从json数据中获取点位信息
        /// </summary>
        /// <param name="assetId"></param>
        /// <returns></returns>
        public async Task<List<AssetDataPointInfo>> GetDataPoints(int assetId)
        {
            var tempData = new List<AssetDataPointInfo>();

            try
            {
                var thirdModelConfig = await _db.Queryable<ThirdModelConfig>()
                    .LeftJoin<AssetInfo>((t1, t2) => t1.Code == t2.ThirdPartCode)
                    .Where((t1, t2) => t2.Id == assetId)
                    .Select((t1, t2) => t1)
                    .FirstAsync();

                if (thirdModelConfig != null)
                {
                    // 获取二级制点位信息
                    var universalDeviceConfigs = await _db.Queryable<UniversalDeviceConfig>()
                        .Where(p => p.AssetId == assetId).ToListAsync();

                    var jsonData = JsonConvert.DeserializeObject<ThreePartModelTemplate>(thirdModelConfig.JsonData ?? "");

                    if (jsonData != null && jsonData.Treeview != null && jsonData.Treeview.Any())
                    {
                        foreach (var item in jsonData.Treeview)
                        {
                            //获取子集集合
                            if (item.SubGroups != null && item.SubGroups.Any())
                            {
                                foreach (var _item in item.SubGroups)
                                {
                                    if (_item.Properties != null && _item.Properties.Any())
                                    {
                                        foreach (var secondItem in _item.Properties)
                                        {
                                            tempData.Add(new AssetDataPointInfo()
                                            {
                                                Code = secondItem.PropertyName ?? "",
                                                Name = universalDeviceConfigs
                                                .FirstOrDefault(p => p.PropertyEnName == secondItem.PropertyName)?.PropertyCnName
                                                ?? secondItem.DescriptionInGerman ?? secondItem.DescriptionInEnglish ?? "",
                                                UdcCode = secondItem.PropertyName ?? "",
                                                Unit = secondItem.Unit ?? "",
                                                GroupName = "Measurement",
                                                ParentName = "Measurement",
                                                Extend = "{\"FumcName\":\"GeneralDeviceMotorLogic\"}",
                                                IsBit = universalDeviceConfigs
                                                    .FirstOrDefault(p => p.PropertyEnName == secondItem.PropertyName)?.IsBit ?? false
                                            });
                                        }
                                    }
                                }
                            }

                            if (item.Properties != null && item.Properties.Any())
                            {
                                foreach (var _item in item.Properties)
                                {
                                    tempData.Add(new AssetDataPointInfo()
                                    {
                                        Code = _item.PropertyName ?? "",
                                        Name = universalDeviceConfigs
                                        .FirstOrDefault(p => p.PropertyEnName == _item.PropertyName)?.PropertyCnName ?? _item.DescriptionInGerman ?? _item.DescriptionInEnglish ?? "",
                                        UdcCode = _item.PropertyName ?? "",
                                        Unit = _item.Unit ?? "",
                                        GroupName = "Measurement",
                                        ParentName = "Measurement",
                                        Extend = "{\"FumcName\":\"GeneralDeviceMotorLogic\"}",
                                        IsBit = universalDeviceConfigs
                                            .FirstOrDefault(p => p.PropertyEnName == _item.DescriptionInEnglish)?.IsBit ?? false
                                    });
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _log.LogError("AssetDataPointInfoServer_GetDataPoints:" + ex.Message);
            }

            return tempData;
        }

        /// <summary>
        /// 从json数据中获取点位信息(缓存写法)
        /// </summary>
        /// <param name="assetId"></param>
        /// <returns></returns>
        public List<AssetDataPointInfo> GetCacheDataPoints(int assetId)
        {
            var tempDatas = new List<AssetDataPointInfo>();

            try
            {
                Random r = new Random();
                int number = r.Next(60, 80);

                tempDatas = _cache.GetOrCreate<List<AssetDataPointInfo>>($"AssetDataPointInfoServer_GetCacheDataPoints_{assetId}", () =>
                {
                    var tempData = new List<AssetDataPointInfo>();

                    var thirdModelConfig = _db.Queryable<ThirdModelConfig>()
                        .LeftJoin<AssetInfo>((t1, t2) => t1.Code == t2.ThirdPartCode)
                        .Where((t1, t2) => t2.Id == assetId)
                        .Select((t1, t2) => t1)
                        .First();

                    if (thirdModelConfig != null)
                    {
                        // 获取二级制点位信息
                        var universalDeviceConfigs = _db.Queryable<UniversalDeviceConfig>()
                            .Where(p => p.AssetId == assetId).ToList();

                        var jsonData = JsonConvert.DeserializeObject<ThreePartModelTemplate>(thirdModelConfig.JsonData ?? "");

                        if (jsonData != null && jsonData.Treeview != null && jsonData.Treeview.Any())
                        {
                            foreach (var item in jsonData.Treeview)
                            {
                                //获取子集集合
                                if (item.SubGroups != null && item.SubGroups.Any())
                                {
                                    foreach (var _item in item.SubGroups)
                                    {
                                        if (_item.Properties != null && _item.Properties.Any())
                                        {
                                            foreach (var secondItem in _item.Properties)
                                            {
                                                tempData.Add(new AssetDataPointInfo()
                                                {
                                                    Code = secondItem.PropertyName ?? "",
                                                    Name = universalDeviceConfigs
                                                    .FirstOrDefault(p => p.PropertyEnName == secondItem.PropertyName)?.PropertyCnName
                                                    ?? secondItem.DescriptionInEnglish ?? "",
                                                    UdcCode = secondItem.PropertyName ?? "",
                                                    Unit = secondItem.Unit!,
                                                    GroupName = "Measurement",
                                                    ParentName = "Measurement",
                                                    Extend = "{\"FumcName\":\"GeneralDeviceMotorLogic\"}",
                                                    IsBit = universalDeviceConfigs
                                                        .FirstOrDefault(p => p.PropertyEnName == secondItem.PropertyName)?.IsBit ?? false
                                                });
                                            }
                                        }
                                    }
                                }

                                if (item.Properties != null && item.Properties.Any())
                                {
                                    foreach (var _item in item.Properties)
                                    {
                                        tempData.Add(new AssetDataPointInfo()
                                        {
                                            Code = _item.PropertyName ?? "",
                                            Name = universalDeviceConfigs
                                            .FirstOrDefault(p => p.PropertyEnName == _item.PropertyName)?.PropertyCnName ?? _item.DescriptionInEnglish ?? "",
                                            UdcCode = _item.PropertyName ?? "",
                                            Unit = _item.Unit ?? "",
                                            GroupName = "Measurement",
                                            ParentName = "Measurement",
                                            Extend = "{\"FumcName\":\"GeneralDeviceMotorLogic\"}",
                                            IsBit = universalDeviceConfigs
                                                .FirstOrDefault(p => p.PropertyEnName == _item.PropertyName)?.IsBit ?? false
                                        });
                                    }
                                }
                            }
                        }
                    }

                    return tempData;
                }
                , TimeSpan.FromMinutes(number));

            }
            catch (Exception ex)
            {
                _log.LogError("AssetDataPointInfoServer_GetCacheDataPoints:" + ex.Message);
            }

            return tempDatas;
        }

        /// <summary>
        /// 西门子设备的数据点位
        /// </summary>
        /// <param name="assetType"></param>
        /// <param name="assetModel"></param>
        /// <returns></returns>
        public async Task<List<AssetDataPointInfo>> GetAssetDataPointsForSiemens(string assetType, string assetModel)
        {
            var dataPoints = await _db.Queryable<AssetDataPointInfo>()
                                      .Where(d => d.AssetModel == assetModel
                                            && d.AssetType == assetType
                                            && (d.GroupName == "Status"
                                            || (d.GroupName == "DigitalInputOutput" && d.Code.Contains("DI"))))
                                      .ToListAsync();

            return dataPoints;
        }

        /// <summary>
        /// 第三方设备
        /// </summary>
        /// <param name="assetId"></param>
        /// <returns></returns>
        public async Task<List<BitConfig>> GetBitConfigByThirdPartDevice(int assetId)
        {
            var configs = await _db.Queryable<UniversalDeviceConfig>()
                                   .InnerJoin<BitConfig>((u, b) => u.Id == b.UniversalDeviceConfigId)
                                   .Where((u, b) => u.AssetId == assetId && u.IsBit == true)
                                   .Select((u, b) => b)
                                   .ToListAsync();

            return configs;
        }
    }
}
