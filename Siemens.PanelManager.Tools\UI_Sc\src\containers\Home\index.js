import { getMonitorList } from "../../api/homeApi";
import React, { useState } from "react";
import CurrentStatus from "./CurrentStatus";
import ProcessList from "./ProcessList";
import "./index.css";
import { loadMonitor } from "./monitorChartInit";

export default function Home() {
  const [echartData, setEcharData] = useState(null);
  if (echartData == null) {
    getMonitorList(setEcharData);
  } else {
    setTimeout(() => loadMonitor(echartData), 500);
    setTimeout(() => {
      getMonitorList(setEcharData);
    }, 10000);
  }
  return (
    <div className="home-container">
      <CurrentStatus />
      <div className="home-processes">
        <ProcessList />
      </div>
      <div className="home-echart" id="monitorChart"></div>
    </div>
  );
}
