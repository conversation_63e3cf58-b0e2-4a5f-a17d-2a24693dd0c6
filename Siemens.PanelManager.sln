﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.4.33110.190
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Siemens.PanelManager.WebApi", "Siemens.PanelManager.WebApi\Siemens.PanelManager.WebApi.csproj", "{FD1727E8-199E-4F6B-899B-EDB429292F8B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Siemens.PanelManager.Common", "Siemens.PanelManager.Common\Siemens.PanelManager.Common.csproj", "{6CACE6A5-CBD2-4877-B6A4-7354FDFE92B8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Siemens.PanelManager.Model", "Siemens.PanelManager.Model\Siemens.PanelManager.Model.csproj", "{87C5A488-CA0F-42F7-BA5D-7C7C0304A126}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Siemens.PanelManager.Job", "Siemens.PanelManager.Job\Siemens.PanelManager.Job.csproj", "{1F7CA1AB-B64E-4D06-A211-8EE6D036F3D0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Siemens.PanelManager.Server", "Siemens.PanelManager.Server\Siemens.PanelManager.Server.csproj", "{EEF59972-59A1-4CEA-9200-C465A5C45283}"
	ProjectSection(ProjectDependencies) = postProject
		{87C5A488-CA0F-42F7-BA5D-7C7C0304A126} = {87C5A488-CA0F-42F7-BA5D-7C7C0304A126}
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Siemens.InfluxDB.Helper", "Siemens.InfluxDB.Helper\Siemens.InfluxDB.Helper.csproj", "{D04D9175-F187-4A10-96AE-97D0F3DDBBDC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Siemens.PanelManager.Tools", "Siemens.PanelManager.Tools\Siemens.PanelManager.Tools.csproj", "{5CAA5C2F-AEB4-46CF-8EAA-D201E78F8720}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "AncillaryTools", "AncillaryTools", "{B89BB91B-4CF0-4163-9737-E7153B5E4A66}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Siemens.PanelManager.Monitor", "Siemens.PanelManager.Monitor\Siemens.PanelManager.Monitor.csproj", "{B6CCC1EC-7DA1-4EF8-855C-C2C0B6B33BAE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Siemens.PanelManager.DeviceDataFlow", "Siemens.PanelManager.DeviceDataFlow\Siemens.PanelManager.DeviceDataFlow.csproj", "{419669DD-6640-489D-ACE8-ECADC013A4A1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UdcSyncServices", "UdcSyncServices", "{E09649B2-0328-453C-B02F-46C4C758142D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Siemens.PanelManager.SyncService.Sqlite", "Siemens.PanelManager.SyncService.Sqlite\Siemens.PanelManager.SyncService.Sqlite.csproj", "{B10B51FB-2FF6-448B-99E0-F240806AEAFA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Siemens.PanelManager.SyncService.UdcModels", "Siemens.PanelManager.SyncService.UdcModels\Siemens.PanelManager.SyncService.UdcModels.csproj", "{7C0241ED-8D38-463D-9BF0-3919A196FFAA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Siemens.PanelManager.HubModel", "Siemens.PanelManager.HubModel\Siemens.PanelManager.HubModel.csproj", "{9A702A85-C0ED-4DC1-A74F-38AE13BA04AD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Siemens.PanelManager.Interface", "Siemens.PanelManager.Interface\Siemens.PanelManager.Interface.csproj", "{65C1B5DC-4F8E-48DA-A2B5-F23EDA938F49}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{FD1727E8-199E-4F6B-899B-EDB429292F8B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FD1727E8-199E-4F6B-899B-EDB429292F8B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FD1727E8-199E-4F6B-899B-EDB429292F8B}.Debug|x64.ActiveCfg = Debug|Any CPU
		{FD1727E8-199E-4F6B-899B-EDB429292F8B}.Debug|x64.Build.0 = Debug|Any CPU
		{FD1727E8-199E-4F6B-899B-EDB429292F8B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FD1727E8-199E-4F6B-899B-EDB429292F8B}.Release|Any CPU.Build.0 = Release|Any CPU
		{FD1727E8-199E-4F6B-899B-EDB429292F8B}.Release|x64.ActiveCfg = Release|Any CPU
		{FD1727E8-199E-4F6B-899B-EDB429292F8B}.Release|x64.Build.0 = Release|Any CPU
		{6CACE6A5-CBD2-4877-B6A4-7354FDFE92B8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6CACE6A5-CBD2-4877-B6A4-7354FDFE92B8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6CACE6A5-CBD2-4877-B6A4-7354FDFE92B8}.Debug|x64.ActiveCfg = Debug|Any CPU
		{6CACE6A5-CBD2-4877-B6A4-7354FDFE92B8}.Debug|x64.Build.0 = Debug|Any CPU
		{6CACE6A5-CBD2-4877-B6A4-7354FDFE92B8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6CACE6A5-CBD2-4877-B6A4-7354FDFE92B8}.Release|Any CPU.Build.0 = Release|Any CPU
		{6CACE6A5-CBD2-4877-B6A4-7354FDFE92B8}.Release|x64.ActiveCfg = Release|Any CPU
		{6CACE6A5-CBD2-4877-B6A4-7354FDFE92B8}.Release|x64.Build.0 = Release|Any CPU
		{87C5A488-CA0F-42F7-BA5D-7C7C0304A126}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{87C5A488-CA0F-42F7-BA5D-7C7C0304A126}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{87C5A488-CA0F-42F7-BA5D-7C7C0304A126}.Debug|x64.ActiveCfg = Debug|Any CPU
		{87C5A488-CA0F-42F7-BA5D-7C7C0304A126}.Debug|x64.Build.0 = Debug|Any CPU
		{87C5A488-CA0F-42F7-BA5D-7C7C0304A126}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{87C5A488-CA0F-42F7-BA5D-7C7C0304A126}.Release|Any CPU.Build.0 = Release|Any CPU
		{87C5A488-CA0F-42F7-BA5D-7C7C0304A126}.Release|x64.ActiveCfg = Release|Any CPU
		{87C5A488-CA0F-42F7-BA5D-7C7C0304A126}.Release|x64.Build.0 = Release|Any CPU
		{1F7CA1AB-B64E-4D06-A211-8EE6D036F3D0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1F7CA1AB-B64E-4D06-A211-8EE6D036F3D0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1F7CA1AB-B64E-4D06-A211-8EE6D036F3D0}.Debug|x64.ActiveCfg = Debug|Any CPU
		{1F7CA1AB-B64E-4D06-A211-8EE6D036F3D0}.Debug|x64.Build.0 = Debug|Any CPU
		{1F7CA1AB-B64E-4D06-A211-8EE6D036F3D0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1F7CA1AB-B64E-4D06-A211-8EE6D036F3D0}.Release|Any CPU.Build.0 = Release|Any CPU
		{1F7CA1AB-B64E-4D06-A211-8EE6D036F3D0}.Release|x64.ActiveCfg = Release|Any CPU
		{1F7CA1AB-B64E-4D06-A211-8EE6D036F3D0}.Release|x64.Build.0 = Release|Any CPU
		{EEF59972-59A1-4CEA-9200-C465A5C45283}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EEF59972-59A1-4CEA-9200-C465A5C45283}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EEF59972-59A1-4CEA-9200-C465A5C45283}.Debug|x64.ActiveCfg = Debug|Any CPU
		{EEF59972-59A1-4CEA-9200-C465A5C45283}.Debug|x64.Build.0 = Debug|Any CPU
		{EEF59972-59A1-4CEA-9200-C465A5C45283}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EEF59972-59A1-4CEA-9200-C465A5C45283}.Release|Any CPU.Build.0 = Release|Any CPU
		{EEF59972-59A1-4CEA-9200-C465A5C45283}.Release|x64.ActiveCfg = Release|Any CPU
		{EEF59972-59A1-4CEA-9200-C465A5C45283}.Release|x64.Build.0 = Release|Any CPU
		{D04D9175-F187-4A10-96AE-97D0F3DDBBDC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D04D9175-F187-4A10-96AE-97D0F3DDBBDC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D04D9175-F187-4A10-96AE-97D0F3DDBBDC}.Debug|x64.ActiveCfg = Debug|Any CPU
		{D04D9175-F187-4A10-96AE-97D0F3DDBBDC}.Debug|x64.Build.0 = Debug|Any CPU
		{D04D9175-F187-4A10-96AE-97D0F3DDBBDC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D04D9175-F187-4A10-96AE-97D0F3DDBBDC}.Release|Any CPU.Build.0 = Release|Any CPU
		{D04D9175-F187-4A10-96AE-97D0F3DDBBDC}.Release|x64.ActiveCfg = Release|Any CPU
		{D04D9175-F187-4A10-96AE-97D0F3DDBBDC}.Release|x64.Build.0 = Release|Any CPU
		{5CAA5C2F-AEB4-46CF-8EAA-D201E78F8720}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5CAA5C2F-AEB4-46CF-8EAA-D201E78F8720}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5CAA5C2F-AEB4-46CF-8EAA-D201E78F8720}.Debug|x64.ActiveCfg = Debug|x64
		{5CAA5C2F-AEB4-46CF-8EAA-D201E78F8720}.Debug|x64.Build.0 = Debug|x64
		{5CAA5C2F-AEB4-46CF-8EAA-D201E78F8720}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5CAA5C2F-AEB4-46CF-8EAA-D201E78F8720}.Release|Any CPU.Build.0 = Release|Any CPU
		{5CAA5C2F-AEB4-46CF-8EAA-D201E78F8720}.Release|x64.ActiveCfg = Release|x64
		{5CAA5C2F-AEB4-46CF-8EAA-D201E78F8720}.Release|x64.Build.0 = Release|x64
		{B6CCC1EC-7DA1-4EF8-855C-C2C0B6B33BAE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B6CCC1EC-7DA1-4EF8-855C-C2C0B6B33BAE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B6CCC1EC-7DA1-4EF8-855C-C2C0B6B33BAE}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B6CCC1EC-7DA1-4EF8-855C-C2C0B6B33BAE}.Debug|x64.Build.0 = Debug|Any CPU
		{B6CCC1EC-7DA1-4EF8-855C-C2C0B6B33BAE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B6CCC1EC-7DA1-4EF8-855C-C2C0B6B33BAE}.Release|Any CPU.Build.0 = Release|Any CPU
		{B6CCC1EC-7DA1-4EF8-855C-C2C0B6B33BAE}.Release|x64.ActiveCfg = Release|Any CPU
		{B6CCC1EC-7DA1-4EF8-855C-C2C0B6B33BAE}.Release|x64.Build.0 = Release|Any CPU
		{419669DD-6640-489D-ACE8-ECADC013A4A1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{419669DD-6640-489D-ACE8-ECADC013A4A1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{419669DD-6640-489D-ACE8-ECADC013A4A1}.Debug|x64.ActiveCfg = Debug|Any CPU
		{419669DD-6640-489D-ACE8-ECADC013A4A1}.Debug|x64.Build.0 = Debug|Any CPU
		{419669DD-6640-489D-ACE8-ECADC013A4A1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{419669DD-6640-489D-ACE8-ECADC013A4A1}.Release|Any CPU.Build.0 = Release|Any CPU
		{419669DD-6640-489D-ACE8-ECADC013A4A1}.Release|x64.ActiveCfg = Release|Any CPU
		{419669DD-6640-489D-ACE8-ECADC013A4A1}.Release|x64.Build.0 = Release|Any CPU
		{B10B51FB-2FF6-448B-99E0-F240806AEAFA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B10B51FB-2FF6-448B-99E0-F240806AEAFA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B10B51FB-2FF6-448B-99E0-F240806AEAFA}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B10B51FB-2FF6-448B-99E0-F240806AEAFA}.Debug|x64.Build.0 = Debug|Any CPU
		{B10B51FB-2FF6-448B-99E0-F240806AEAFA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B10B51FB-2FF6-448B-99E0-F240806AEAFA}.Release|Any CPU.Build.0 = Release|Any CPU
		{B10B51FB-2FF6-448B-99E0-F240806AEAFA}.Release|x64.ActiveCfg = Release|Any CPU
		{B10B51FB-2FF6-448B-99E0-F240806AEAFA}.Release|x64.Build.0 = Release|Any CPU
		{7C0241ED-8D38-463D-9BF0-3919A196FFAA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7C0241ED-8D38-463D-9BF0-3919A196FFAA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7C0241ED-8D38-463D-9BF0-3919A196FFAA}.Debug|x64.ActiveCfg = Debug|Any CPU
		{7C0241ED-8D38-463D-9BF0-3919A196FFAA}.Debug|x64.Build.0 = Debug|Any CPU
		{7C0241ED-8D38-463D-9BF0-3919A196FFAA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7C0241ED-8D38-463D-9BF0-3919A196FFAA}.Release|Any CPU.Build.0 = Release|Any CPU
		{7C0241ED-8D38-463D-9BF0-3919A196FFAA}.Release|x64.ActiveCfg = Release|Any CPU
		{7C0241ED-8D38-463D-9BF0-3919A196FFAA}.Release|x64.Build.0 = Release|Any CPU
		{9A702A85-C0ED-4DC1-A74F-38AE13BA04AD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9A702A85-C0ED-4DC1-A74F-38AE13BA04AD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9A702A85-C0ED-4DC1-A74F-38AE13BA04AD}.Debug|x64.ActiveCfg = Debug|Any CPU
		{9A702A85-C0ED-4DC1-A74F-38AE13BA04AD}.Debug|x64.Build.0 = Debug|Any CPU
		{9A702A85-C0ED-4DC1-A74F-38AE13BA04AD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9A702A85-C0ED-4DC1-A74F-38AE13BA04AD}.Release|Any CPU.Build.0 = Release|Any CPU
		{9A702A85-C0ED-4DC1-A74F-38AE13BA04AD}.Release|x64.ActiveCfg = Release|Any CPU
		{9A702A85-C0ED-4DC1-A74F-38AE13BA04AD}.Release|x64.Build.0 = Release|Any CPU
		{65C1B5DC-4F8E-48DA-A2B5-F23EDA938F49}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{65C1B5DC-4F8E-48DA-A2B5-F23EDA938F49}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{65C1B5DC-4F8E-48DA-A2B5-F23EDA938F49}.Debug|x64.ActiveCfg = Debug|Any CPU
		{65C1B5DC-4F8E-48DA-A2B5-F23EDA938F49}.Debug|x64.Build.0 = Debug|Any CPU
		{65C1B5DC-4F8E-48DA-A2B5-F23EDA938F49}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{65C1B5DC-4F8E-48DA-A2B5-F23EDA938F49}.Release|Any CPU.Build.0 = Release|Any CPU
		{65C1B5DC-4F8E-48DA-A2B5-F23EDA938F49}.Release|x64.ActiveCfg = Release|Any CPU
		{65C1B5DC-4F8E-48DA-A2B5-F23EDA938F49}.Release|x64.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{5CAA5C2F-AEB4-46CF-8EAA-D201E78F8720} = {B89BB91B-4CF0-4163-9737-E7153B5E4A66}
		{B6CCC1EC-7DA1-4EF8-855C-C2C0B6B33BAE} = {B89BB91B-4CF0-4163-9737-E7153B5E4A66}
		{B10B51FB-2FF6-448B-99E0-F240806AEAFA} = {E09649B2-0328-453C-B02F-46C4C758142D}
		{7C0241ED-8D38-463D-9BF0-3919A196FFAA} = {E09649B2-0328-453C-B02F-46C4C758142D}
		{9A702A85-C0ED-4DC1-A74F-38AE13BA04AD} = {B89BB91B-4CF0-4163-9737-E7153B5E4A66}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {0F3F4645-37AA-46A9-8DC2-BF08D8C99911}
	EndGlobalSection
EndGlobal
