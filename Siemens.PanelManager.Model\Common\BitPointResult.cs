﻿namespace Siemens.PanelManager.Model.Common
{
    /// <summary>
    /// 第三方设备点位信息
    /// </summary>
    public class BitPointDto
    {
        /// <summary>
        /// 资产id
        /// </summary>
        public int AssetId { get; set; }

        /// <summary>
        /// 点位英文名称
        /// </summary>
        public string? PropertyEnName { get; set; }


        /// <summary>
        /// 二进制点位中文名称
        /// </summary>
        public string? BitName { get; set; }

        /// <summary>
        /// bit位序号(一共16位,索引从0-15)
        /// </summary>
        public int BitNumber { get; set; }

        /// <summary>
        /// Bit位标识符
        /// </summary>
        public string? BitCode { get; set; }

        /// <summary>
        /// 二进制点位的值
        /// </summary>
        public string? BitValue { get; set; } = "0";

        /// <summary>
        /// 事件类型
        /// </summary>
        public int EventType { get; set; } = -1;

        /// <summary>
        /// 告警级别
        /// </summary>
        public int AlarmLevel { get; set; } = -1;

        /// <summary>
        /// 实时时间
        /// </summary>
        public DateTime Time { get; set; }

    }
}
