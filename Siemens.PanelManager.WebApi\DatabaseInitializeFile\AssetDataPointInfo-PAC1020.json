[{"Code": "IsConnected", "Name": "IsConnected", "GroupName": "Status", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "UDCConnect", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 1, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Ua", "Name": "Ua", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "V_LN/Inst/Value/L1N", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 2, "ParentName": "InstantaneousValuesL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ub", "Name": "Ub", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "V_LN/Inst/Value/L2N", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 3, "ParentName": "InstantaneousValuesL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Uc", "Name": "Uc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "V_LN/Inst/Value/L3N", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 4, "ParentName": "InstantaneousValuesL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Uab", "Name": "Uab", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "V_LL/Inst/Value/L1L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 5, "ParentName": "InstantaneousValuesL-L_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ubc", "Name": "Ubc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "V_LL/Inst/Value/L2L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 6, "ParentName": "InstantaneousValuesL-L_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Uca", "Name": "Uca", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "V_LL/Inst/Value/L3L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 7, "ParentName": "InstantaneousValuesL-L_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ia", "Name": "Ia", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "I/Inst/Value/L1", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 8, "ParentName": "RMS_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ib", "Name": "Ib", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "I/Inst/Value/L2", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9, "ParentName": "RMS_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ic", "Name": "Ic", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "I/Inst/Value/L3", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 10, "ParentName": "RMS_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "In", "Name": "In", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "I/Inst/Value/Neutral", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 11, "ParentName": "RMS_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "P", "Name": "P", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "Power/W/Inst/Value/Sum", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 12, "ParentName": "ActivePower_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Pa", "Name": "Pa", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "Power/W/Inst/Value/L1", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 13, "ParentName": "ActivePower_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Pb", "Name": "Pb", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "Power/W/Inst/Value/L2", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 14, "ParentName": "ActivePower_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Pc", "Name": "Pc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "Power/W/Inst/Value/L3", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 15, "ParentName": "ActivePower_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Q", "Name": "Q", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "Power/var/Q1/Inst/Value/Sum", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 16, "ParentName": "MeasuringMethodVAR1_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Qa", "Name": "Qa", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "Power/var/Q1/Inst/Value/L1", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 17, "ParentName": "MeasuringMethodVAR1_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Qb", "Name": "Qb", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "Power/var/Q1/Inst/Value/L2", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 18, "ParentName": "MeasuringMethodVAR1_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Qc", "Name": "Qc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "Power/var/Q1/Inst/Value/L3", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19, "ParentName": "MeasuringMethodVAR1_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor", "Name": "PowFactor", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "Power/Factor/Inst/Value/AVG", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 20, "ParentName": "PowerFactor_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor_A", "Name": "PowFactor_A", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "Power/Factor/Inst/Value/L1", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 21, "ParentName": "PowerFactor_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor_B", "Name": "PowFactor_B", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "Power/Factor/Inst/Value/L2", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 22, "ParentName": "PowerFactor_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor_C", "Name": "PowFactor_C", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "Power/Factor/Inst/Value/L3", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 23, "ParentName": "PowerFactor_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "F", "Name": "F", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "Frequency/Inst/Value/Common", "Unit": "Hz", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 24, "ParentName": "PowerFactor_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ActiveEnergy", "Name": "ActiveEnergy", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "Energy/Wh/ImportExportNet", "Unit": "Wh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 25, "ParentName": "ActiveEnergy_Wh", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ReactiveEnergy", "Name": "ReactiveEnergy", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "Energy/varh/ImportExportNet", "Unit": "varh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 26, "ParentName": "ReactiveEnergy_Varh", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "DO_0.0", "Name": "DO_0.0", "GroupName": "DigitalInputOutput", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "Output_Status_/Output_Status_Device/DO0.0", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 27, "ParentName": "InputStatus", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "DI_0.0", "Name": "DI_0.0", "GroupName": "DigitalInputOutput", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "Input_Status_/Input_Status_Device_DI/DI0.0", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 28, "ParentName": "OutputStatus", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "MLFB", "Name": "MLFB", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "OrderNumber", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "SerialNumber", "Name": "SerialNumber", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "SerialNumber", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "UseVoltageTransformer", "Name": "UseVoltageTransformer", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "UseVoltageTransformer", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 31, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "PrimaryVoltage", "Name": "PrimaryVoltage", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "PrimaryVoltage", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 32, "ParentName": "GlobalDeviceStatus", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "SecondaryVoltage", "Name": "SecondaryVoltage", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "SecondaryVoltage", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 33, "ParentName": "GlobalDeviceStatus", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "PrimaryCurrent", "Name": "PrimaryCurrent", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "PrimaryCurrent", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 34, "ParentName": "GlobalDeviceStatus", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "SecondaryCurrent", "Name": "SecondaryCurrent", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC1020", "FilterIds": "", "UdcCode": "SecondaryCurrent", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 35, "ParentName": "GlobalDeviceStatus", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}]