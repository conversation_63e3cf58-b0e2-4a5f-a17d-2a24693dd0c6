﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.Model.Algorithm
{
    public class HealthEvaluationModel
    {
        [JsonProperty(PropertyName = "score")]
        public decimal score { get; set; }
        [JsonProperty(PropertyName = "temperature")]
        public string temperature { get; set; }
        [JsonProperty(PropertyName = "temperatureMsg")]
        public string temperatureMsg { get; set; }
        [JsonProperty(PropertyName = "branchCurrent")]
        public decimal branchCurrent { get; set; }
        [JsonProperty(PropertyName = "branchCurrentMsg")]
        public string branchCurrentMsg { get; set; }
        [JsonProperty(PropertyName = "CBs")]
        public decimal CBs { get; set; }
        [JsonProperty(PropertyName = "CBsMsg")]
        public string CBsMsg { get; set; }
        [JsonProperty(PropertyName = "alarmFrequency")]
        public decimal alarmFrequency { get; set; }
        [JsonProperty(PropertyName = "alarmFrequencyMsg")]
        public string alarmFrequencyMsg { get; set; }
        [JsonProperty(PropertyName = "evaluationResult")]
        public evaluationResult? evaluationResult { get; set; }
        [JsonProperty(PropertyName = "alarmPerWeek")]
        public alarmPerWeek alarmPerWeek { get; set; }
        [JsonProperty(PropertyName = "alarmsPerWeekMsg")]
        public string alarmsPerWeekMsg { get; set; }
        [JsonProperty(PropertyName = "topology")]
        public topology[] topology { get; set; }
        [JsonProperty(PropertyName = "historicalResult")]
        public historicalResult historicalResult { get; set; }
        public string maxLine_id { get; set; }
        [JsonProperty(PropertyName = "report_time")]
        public string ReportTime { get; set; }
        [JsonProperty(PropertyName = "indicatorResult")]
        public Dictionary<string,IndicatorResult> IndicatorResult { get;set; }

    }
    public class IndicatorResult
    {
        [JsonProperty(PropertyName = "result")]
        public decimal result { get; set; }
        [JsonProperty(PropertyName = "msg")]
        public string msg { get; set; }
        [JsonProperty(PropertyName = "value")]
        public decimal value { get; set; }
    }
    public class evaluationResult
    {
        [JsonProperty(PropertyName = "excellent")]
        public decimal excellent { get; set; }
        [JsonProperty(PropertyName = "good")]
        public decimal good { get; set; }
        [JsonProperty(PropertyName = "Medium")]
        public decimal Medium { get; set; }
        [JsonProperty(PropertyName = "bad")]
        public decimal bad { get; set; }
        [JsonProperty(PropertyName = "result")]
        public decimal result { get; set; }

    }
    public class alarmPerWeek
    {
        [JsonProperty(PropertyName = "high")]
        public int high { get; set; }
        [JsonProperty(PropertyName = "medium")]
        public int medium { get; set; }
        [JsonProperty(PropertyName = "low")]
        public int low { get; set; }
    }
    public class topology
    {
        [JsonProperty(PropertyName = "line_id")]
        public int line_id { get; set; }
        [JsonProperty(PropertyName = "current")]
        public decimal current { get; set; }
        [JsonProperty(PropertyName = "temperature")]
        public string temperature { get; set; }
        [JsonProperty(PropertyName = "temperatureCalculate")]
        public string temperatureCalculate { get; set; }
    }
    public class historicalResult
    {
        [JsonProperty(PropertyName = "time")]
        public string[] time { get; set; }
        [JsonProperty(PropertyName = "values")]
        public decimal[] values { get; set; }
        [JsonProperty(PropertyName = "excellent")]
        public decimal excellent { get; set; }
        [JsonProperty(PropertyName = "good")]
        public decimal good { get; set; }
        [JsonProperty(PropertyName = "medium")]
        public decimal medium { get; set; }
        [JsonProperty(PropertyName = "bad")]
        public decimal bad { get; set; }
    }
    public class CabinetHealthStatistics
    {
        [JsonProperty(PropertyName = "excellent")]
        public decimal excellent { get; set; }
        [JsonProperty(PropertyName = "good")]
        public decimal good { get; set; }
        [JsonProperty(PropertyName = "medium")]
        public decimal medium { get; set; }
        [JsonProperty(PropertyName = "bad")]
        public decimal bad { get; set; }
        [JsonProperty(PropertyName = "badAssetList")]
        public List<int> badAssetList { get; set; }
        [JsonProperty(PropertyName = "total")]
        public decimal total { get; set; }
    }
}
