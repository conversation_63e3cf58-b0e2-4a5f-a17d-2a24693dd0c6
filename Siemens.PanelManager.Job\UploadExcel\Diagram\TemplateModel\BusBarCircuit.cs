﻿using Siemens.PanelManager.Job.UploadExcel.Diagram.TemplateModel.NodeTemplate;
using Siemens.PanelManager.Model.Topology;
using System.Diagnostics.Metrics;

namespace Siemens.PanelManager.Job.UploadExcel.Diagram.TemplateModel
{
    /// <summary>
    /// 单线图中母联回路
    /// </summary>
    internal class BusBarCircuit : CircuitTemplateModelBase
    {
        private List<NodeData> _nodes;
        private List<LineData> _lineDatas;
        private int _height = 0;
        private int _width = 80;

        public BusBarCircuit(string? busBarName, CircuitModel model, Dictionary<string, GroupKeyMappingInfo> idTable) 
            : this(busBarName, model, 1, idTable)
        {
        }

        public BusBarCircuit(string? busBarName, CircuitModel model, int direction, Dictionary<string, GroupKeyMappingInfo> idTable)
            : base(busBarName, model, idTable)
        {
            if (model == null)
            {
                throw new CreateTemplateException("回路缺失信息");
            }
            if (model.AssetInfo == null)
            {
                throw new CreateTemplateException("回路缺失资产信息");
            }

            if (model.SubDevices == null || model.SubDevices.Count == 0)
            {
                throw new CreateTemplateException($"回路{model.AssetInfo.AssetNumber}-{model.AssetInfo.AssetName}缺失子设备");
            }

            if (model.SubDevices == null || !model.SubDevices.Any(d => d.AssetType == "MCCB" || d.AssetType == "ACB" || d.AssetType == "MCB"))
            {
                throw new CreateTemplateException($"回路{model.AssetInfo.AssetNumber}-{model.AssetInfo.AssetName}缺失断路器");
            }

            Direction = direction;
            _nodes = new List<NodeData>();
            _lineDatas = new List<LineData>();

            // 添加起点
            var starting = new PointNode()
            {
                Key = GetNewId(),
                Name = "BusCouplerPoint0"
            };
            var starting1 = new PointNode()
            {
                Key = GetNewId(),
                Name = "BusCouplerPoint1"
            };
            var starting2 = new PointNode()
            {
                Key = GetNewId(),
                Name = "BusCouplerPoint2"
            };
            _nodes.Add(starting);
            _nodes.Add(starting1);
            _nodes.Add(starting2);
            var meterKey = GetNewId();
            var swithKey = GetNewId();
            var labelId = GetNewId();

            var breakerAsset = model.SubDevices.FirstOrDefault(d => d.AssetType == "MCCB" || d.AssetType == "ACB" || d.AssetType == "MCB");
            if (breakerAsset != null)
            {
                var breaker = new SwitchNode(busBarName)
                {
                    Model = breakerAsset.AssetModel ?? string.Empty,
                    AssetId = breakerAsset.Id,
                    AssetName = breakerAsset.AssetName ?? string.Empty,
                    DeviceType = breakerAsset.AssetType,
                    Angle = SwitchAngle,
                    CircuitId = model.AssetInfo.Id,
                    CircuitName = model.AssetInfo.CircuitName ?? string.Empty,
                    Key = swithKey,
                };
                _nodes.Add(breaker);

                if ("3WA".Equals(breakerAsset.AssetModel) || "3WL".Equals(breakerAsset.AssetModel))
                {
                    breaker.BreakerPosition = "2";
                }
            }

            //var breakerAssets = model.SubDevices.Where(d => d.AssetType == "MCCB" || d.AssetType == "ACB" || d.AssetType == "MCB" || d.AssetType == "GeneralDevice").ToList();

            //if (breakerAssets != null && breakerAssets.Any())
            //{
            //    foreach (var item in breakerAssets)
            //    {
            //        var swithKey = GetNewId();

            //        var breaker = new SwitchNode(busBarName)
            //        {
            //            Model = item.AssetModel ?? string.Empty,
            //            AssetId = item.Id,
            //            AssetName = item.AssetName ?? string.Empty,
            //            Angle = SwitchAngle,
            //            CircuitId = model.AssetInfo.Id,
            //            CircuitName = model.AssetInfo.CircuitName ?? string.Empty,
            //            Key = swithKey,
            //        };

            //        if ("3WA".Equals(item.AssetModel) || "3WL".Equals(item.AssetModel) || "GeneralDevice".Equals(item.AssetModel))
            //        {
            //            breaker.BreakerPosition = "0";
            //        }

            //        _nodes.Add(breaker);
            //    }
            //}


            var meter = model.SubDevices.FirstOrDefault(d => d.AssetType == "Meter");
            if (meter == null)
            {
                meter = model.SubDevices.FirstOrDefault(d => d.AssetType == "GeneralDevice" && d.AssetModel == "GeneralDevice");
            }

            _nodes.Add(new MeterNode(busBarName)
            {
                Model = meter?.AssetModel ?? string.Empty,
                AssetId = meter?.Id ?? 0,
                AssetName = meter?.AssetName ?? string.Empty,
                Key = meterKey
            });

            _nodes.Add(new LabelNode()
            {
                Text = model.AssetInfo.CircuitName ?? string.Empty,
                Key = labelId
            });

            #region 数据点位
            var dataPointId = GetNewId();
            _nodes.Add(new DataPointNode()
            {
                Name = model.AssetInfo.AssetName ?? string.Empty,
                ElectricalType = "APhaseCurrent",
                ElectricalName = "A 相电流",
                Key = dataPointId,
                BindAssetId = model.AssetInfo.Id,
                BindPointName = "Ia"
            });
            dataPointId = GetNewId();
            _nodes.Add(new DataPointNode()
            {
                Name = model.AssetInfo.AssetName ?? string.Empty,
                ElectricalType = "BPhaseCurrent",
                ElectricalName = "B 相电流",
                Key = dataPointId,
                BindAssetId = model.AssetInfo.Id,
                BindPointName = "Ib"
            });
            dataPointId = GetNewId();
            _nodes.Add(new DataPointNode()
            {
                Name = model.AssetInfo.AssetName ?? string.Empty,
                ElectricalType = "CPhaseCurrent",
                ElectricalName = "C 相电流",
                Key = dataPointId,
                BindAssetId = model.AssetInfo.Id,
                BindPointName = "Ic"
            });
            #endregion

            #region 计算坐标
            CalculateLocation();
            #endregion

            #region 添加线
            LinkLine(_nodes, _lineDatas);
            _lineDatas.Add(new LineData()
            {
                Key = GetNewId(),
                From = -1,
                To = starting.Key.Value,
                IsConnector = true
            });
            #endregion
        } 

        /// <summary>
        /// 计算回路中设备对应的相对位置
        /// </summary>
        private void CalculateLocation()
        {
            switch (Direction)
            {
                case 1:
                    {
                        var switchModel = _nodes.First(d => d.NodeType == NodeType.Switch);
                        switchModel.LocationX = 0;
                        switchModel.LocationY = 20;

                        var label = _nodes.First(d => d.NodeType == NodeType.Label);
                        label.LocationX = 120;
                        label.LocationY = 20;

                        #region DataPoint
                        var dataPoints = _nodes.Where(d => d.NodeType == NodeType.DataPoint).OrderBy(d=>d.Key).ToArray();
                        int y = 70;
                        foreach (var dataPoint in dataPoints)
                        {
                            dataPoint.LocationX = 120;
                            dataPoint.LocationY = y;

                            y += 40;
                        }
                        #endregion

                        var meterModel = _nodes.First(d => d.NodeType == NodeType.Meter);
                        meterModel.LocationX = 0;
                        meterModel.LocationY = 150;
                        //母联柜联线修改
                        var source0 = _nodes.Where(d => d.NodeType == NodeType.Point && d.Name == "BusCouplerPoint0").First();
                        var source1 = _nodes.Where(d => d.NodeType == NodeType.Point&&d.Name== "BusCouplerPoint1").First();
                        var source2 = _nodes.Where(d => d.NodeType == NodeType.Point && d.Name == "BusCouplerPoint2").First();
                        source2.LocationX = 0;
                        source2.LocationY = 320;
                        source1.LocationX = 600;
                        source1.LocationY = 320;
                        source0.LocationX = 600;
                        source0.LocationY = -230;
                        _width = 200;
                        _height = 320;
                    }
                    break;
                default:
                    {
                        var switchModel = _nodes.First(d => d.NodeType == NodeType.Switch);
                        switchModel.LocationX = 20;
                        switchModel.LocationY = 0;
                        var label = _nodes.First(d => d.NodeType == NodeType.Label);
                        label.LocationX = 50;
                        label.LocationY = -90;

                        #region DataPoint
                        var dataPoints = _nodes.Where(d => d.NodeType == NodeType.DataPoint).OrderBy(d => d.Key).ToArray();
                        int x = -10;
                        foreach (var dataPoint in dataPoints)
                        {
                            dataPoint.LocationX = x;
                            dataPoint.LocationY = -50;

                            x += 150;
                        }
                        #endregion

                        var meterModel = _nodes.First(d => d.NodeType == NodeType.Meter);
                        meterModel.LocationX = 120;
                        meterModel.LocationY = 0;
                        var source = _nodes.First(d => d.NodeType == NodeType.Point);
                        source.LocationX = 220;
                        source.LocationY = 0;

                        _height = 150;
                        _width = 550;
                    }
                    break;
            }
        }

        public override NodeData[] NodeDatas => _nodes.ToArray();

        public override LineData[] LineDatas => _lineDatas.ToArray();

        public override LineData[] Connectors => _lineDatas.Where(l=>l.IsConnector).OrderBy(l=>l.Key).ToArray();

        public override int Width => _width;

        public override int Height => _height;

        public override void Rotation(int action = -1)
        {
            bool needCalculate = false;
            switch (action)
            {
                // 90度翻转
                case -1:
                    {
                        if (Direction == 0)
                        {
                            Direction = 1;
                            needCalculate = true;
                        }
                        else if (Direction == 1)
                        {
                            Direction = 0;
                            needCalculate = true;
                        }
                        break;
                    }
                // 强制横排
                case 0:
                    {
                        if (Direction != 0)
                        {
                            Direction = 0;
                            needCalculate = true;
                        }
                        break;
                    }
                // 强制纵排
                case 1:
                    {
                        if (Direction != 1)
                        {
                            Direction = 1;
                            needCalculate = true;
                        }
                        break;
                    }
                default: break;
            }

            if (needCalculate)
            {
                RotationNodes(_nodes);
                CalculateLocation();
                foreach (var node in _nodes)
                {
                    SetNodeLocation(node);
                }
            }
        }
    }
}
