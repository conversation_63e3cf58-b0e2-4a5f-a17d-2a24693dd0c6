﻿using InfluxDB.Client.Api.Domain;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System.IO;

namespace Siemens.PanelManager.Server.Algorithm
{
    public class LossDiagnose
    {
        private IServiceProvider _provider;
        private HttpClient httpClient;
        public LossDiagnose(IServiceProvider provider)
        {
            _provider = provider;
            httpClient = new HttpClient();
        }

        public async Task<string> GetSysLossKpiApi(Dictionary<string,object> query)
        {
            var config = _provider.GetRequiredService<IConfiguration>();
            var url = config.GetSection("AlgorithmEvaluationApiPath").Value;
            url = url.TrimEnd('/');
            string body = JsonConvert.SerializeObject(query);
            HttpContent content = new StringContent(body);
            var dataStr = await httpClient.PostAsync($"{url}/sysloss/kpi", content);
            return await dataStr.Content.ReadAsStringAsync();
        }

        /// <summary>
        /// 获取lossdata
        /// </summary>
        /// <param name="query"></param>
        /// <param name="path"></param>
        /// <returns></returns>
        public async Task<string> GetSysLossdataApi(Dictionary<string, object> query,string module, string path)
        {
            var config = _provider.GetRequiredService<IConfiguration>();
            var url = config.GetSection(module).Value;
            if (url != null) {
                url = url.TrimEnd('/');
            }
            string body = JsonConvert.SerializeObject(query);
            HttpContent content = new StringContent(body);
            content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/json");
            var dataStr = await httpClient.PostAsync($"{url}/{path}", content);
            return await dataStr.Content.ReadAsStringAsync();
        }

        public async Task<string> GetAlgorithmVersion()
        {
            var module = "AlgorithmEvaluationApiPath";
            var config = _provider.GetRequiredService<IConfiguration>();
            var url = config.GetSection(module).Value;
            if (url != null)
            {
                url = url.TrimEnd('/');
            }
            var dataStr = await httpClient.GetAsync($"{url}/version");
            return await dataStr.Content.ReadAsStringAsync();
        }

        /// <summary>
        /// 获取healthdata
        /// </summary>
        /// <param name="query"></param>
        /// <param name="path"></param>
        /// <returns></returns>
        public async Task<string> GetCustomizHealthApi(string jsonBody, string path)
        {
            var config = _provider.GetRequiredService<IConfiguration>();
            var url = config.GetSection("AlgorithmEvaluationApiPath").Value;
            url = url.TrimEnd('/');
            HttpContent content = new StringContent(jsonBody);
            content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/json");
            var dataStr = await httpClient.PostAsync($"{url}/{path}", content);
            return await dataStr.Content.ReadAsStringAsync();
        }

        /// <summary>
        /// 获取healthdata
        /// </summary>
        /// <param name="query"></param>
        /// <param name="path"></param>
        /// <returns></returns>
        public async Task<Stream> ReportExport(Dictionary<string, object> query, string path)
        {
            var config = _provider.GetRequiredService<IConfiguration>();
            var url = config.GetSection("AlgorithmEvaluationApiPath").Value;
            if (!string.IsNullOrEmpty(url))
            {
                url = url.TrimEnd('/');
            }
            HttpContent content = null;
            string body = JsonConvert.SerializeObject(query);
            content = new StringContent(body);
            content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/json");
            var responseMessage = await httpClient.PostAsync($"{url}/{path}", content);
            var stream = responseMessage.Content.ReadAsStreamAsync().Result;
            return stream;
        }
    }
}
