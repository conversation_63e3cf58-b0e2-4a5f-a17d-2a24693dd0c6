﻿using Akka.Util.Internal;
using InfluxDB.Client.Core.Flux.Domain;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Common;
using Siemens.PanelManager.Model.Chart;
using Siemens.PanelManager.Model.Config;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Server.Asset;
using Siemens.PanelManager.Server.Asset.Model;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using InfluxDB.Client;
using Microsoft.Extensions.Configuration;
using System.Xml.Linq;
using Akka.Actor;
using Siemens.PanelManager.Model.Database.Energy;
using InfluxDB.Client.Api.Domain;
using System.Collections;
using Siemens.PanelManager.Server.ElectricityCharge;
using Siemens.PanelManager.Model.Database.ElectricityCharge;
using TouchSocket.Sockets;
using static Akka.Actor.FSMBase;
using TouchSocket.Core;
using Siemens.PanelManager.Common.Cache;
using NPOI.SS.Formula.Functions;
using Siemens.PanelManager.Model.Database.System;

namespace Siemens.PanelManager.Server.Energy
{
    public class EnergyManagementServer
    {
        private readonly ILogger<EnergyManagementServer> _logger;
        private readonly IServiceProvider _provider;
        private readonly SiemensCache _cache;
        private ISqlSugarClient _client;
        private ElectricityChargeServer _electricityCharge => _provider.GetRequiredService<ElectricityChargeServer>();
        public EnergyManagementServer(ILogger<EnergyManagementServer> logger, IServiceProvider provider, SqlSugarScope client, SiemensCache cache)
        {
            _logger = logger;
            _provider = provider;
            _client = client;
            _cache = cache;
        }

        /// <summary>
        /// 查询当天的用电量或者电费
        /// </summary>
        /// <param name="assetIds">资产id列表</param>
        /// <param name="dateTime">需要查询的日期</param>
        /// <param name="chartDataType">
        /// <list type="bullet">
        ///     <item>0: 用电量</item>
        ///     <item>1: 电费</item>
        /// </list>
        /// </param>
        /// <returns>IChart <para>see <see cref="IChart" /></para></returns>
        public async Task<IChart?> GetEnergyDayChart(List<int> assetIds, DateTime dateTime, int chartDataType = 0)
        {
            var lineChart = new LineChartModel();

            if (!assetIds.Any())
            {
                lineChart.X = new string[0];
                lineChart.Y1 = new decimal[0];
                return lineChart;
            }

            IConfiguration configuration = _provider.GetRequiredService<IConfiguration>();
            var session = configuration.GetSection(InfluxDBConfig.Influxdb);
            InfluxDBConfig influxDbConfig = null;
            if (session != null && session.Exists())
            {
                influxDbConfig = session.Get<InfluxDBConfig>();
            }

            if (influxDbConfig == null)
            {
                _logger.LogError("EnergyManagementServer init: Not found Influxdb config.");
                return null;
            }

            using var sqlClient = _provider.GetRequiredService<ISqlSugarClient>();
            var dashboardConfig = await sqlClient.Queryable<AssetDashboardConfig>().Where(d => d.ConfigName == "ENERGY_SUMMARY").FirstAsync();

            var sql = dashboardConfig.Sql;
            if (dashboardConfig != null)
            {

                var assetIdQuery = new StringBuilder();
                foreach (var id in assetIds)
                {
                    if (assetIdQuery.Length > 0)
                    {
                        assetIdQuery.Append(" or ");
                    }
                    assetIdQuery.Append("r[\"assetid\"] == \"");
                    assetIdQuery.Append(id);
                    assetIdQuery.Append('\"');
                }

                sql = sql
                    .Replace("[[DBName]]", influxDbConfig.Bucket)
                    .Replace("[[TableName]]", "archivedatarealtime")
                    .Replace("[[AssetIdList]]", assetIdQuery.ToString())
                    .Replace("[[StartDate]]", dateTime.Date.AddMinutes(-1).GetTimestampForSec().ToString())
                    .Replace("[[EndDate]]", dateTime.Date.AddDays(1).GetTimestampForSec().ToString());

                using var influxClient = new InfluxDBClient(influxDbConfig.Url, influxDbConfig.UserName, influxDbConfig.Password);

                var queryApi = influxClient.GetQueryApi();
                var result = await queryApi.QueryAsync(sql, influxDbConfig.OrgId ?? influxDbConfig.OrgName);

                if (result == null || !result.Any())
                {
                    lineChart.X = new string[0];
                    lineChart.Y1 = new decimal[0];
                    return lineChart;
                }

                var localTimeZone = TimeZoneInfo.Local;

                var dataCount = result?.FirstOrDefault()?.Records.Count ?? 0;
                var xData = new string[dataCount - 1];
                var y1Data = new decimal[dataCount - 1];

                foreach (var table in result)
                {
                    for (var i = 0; i < table.Records.Count - 1; i++)
                    {
                        var currentDate = TimeZoneInfo.ConvertTimeFromUtc(table.Records[i].GetTimeInDateTime().Value, localTimeZone);
                        var currentValue = (Convert.ToDecimal(table.Records[i + 1].GetValue()) - Convert.ToDecimal(table.Records[i].GetValue())) / 1000.0M;
                        if (string.IsNullOrWhiteSpace(xData[i]))
                        {
                            xData[i] = currentDate.ToString("HH:00");
                        }

                        if (chartDataType == 0)
                        {
                            y1Data[i] = y1Data[i] + currentValue;
                        }
                        else
                        {
                            // 苏州工业用电电价及峰平谷时间段：
                            // 峰：8:00-11:00，17:00-22:00，电价：1.0347
                            // 平：11:00-17:00，22:00-24:00，电价：0.6068
                            // 谷：0:00-8:00，电价：0.2589

                            int hour = currentDate.Hour;
                            if ((hour >= 8 && hour < 11) || (hour >= 17 && hour < 22))
                            {
                                y1Data[i] = y1Data[i] + currentValue * ElectricityPriceRule.PeakPrice;
                            }
                            else if ((hour >= 11 && hour < 17) || (hour >= 22 && hour < 24))
                            {
                                y1Data[i] = y1Data[i] + currentValue * ElectricityPriceRule.FlatPrice;
                            }
                            else
                            {
                                y1Data[i] = y1Data[i] + currentValue * ElectricityPriceRule.ValleyPrice;
                            }
                        }
                    }
                }


                lineChart.X = xData;
                lineChart.Y1 = y1Data.Select(a => Math.Round(a, 2)).ToArray();
            }

            return lineChart;
        }

        /// <summary>
        /// 查询指定时间区间内的电量与电费消耗情况，分组间隔为1小时
        /// </summary>
        /// <param name="assetIds">资产id列表</param>
        /// <param name="startTime">查询的开始时间</param>
        /// <param name="endTime">查询的开始时间</param>
        public async Task<List<EnergyIntervalSummaryModel>> GetEnergyDataByHour(List<int> assetIds, DateTime startTime, DateTime endTime, bool isSum = false)
        {
            return await GetEnergyDataByInterval(assetIds, startTime, endTime, "1h", isSum);
        }

        /// <summary>
        /// 查询指定时间区间内的电量与电费消耗情况，分组间隔为15分钟
        /// </summary>
        /// <param name="assetIds">资产id列表</param>
        /// <param name="startTime">查询的开始时间</param>
        /// <param name="endTime">查询的开始时间</param>
        public async Task<List<EnergyIntervalSummaryModel>> GetEnergyDataByQuarter(List<int> assetIds, DateTime startTime, DateTime endTime)
        {
            return await GetEnergyDataByInterval(assetIds, startTime, endTime, "15m");
        }

        /// <summary>
        /// 根据图表类型查询用电量或者电费
        /// </summary>
        /// <param name="assetIds">资产id列表</param>
        /// <param name="startTime">需要查询的开始日期</param>
        /// <param name="endTime">需要查询的结束日期</param>
        /// <param name="dateType">
        /// <list type="bullet">
        ///     <item>1: 月</item>
        ///     <item>2: 年</item>
        ///     <item>3: 自定义</item>
        /// </list>
        /// </param>
        /// <param name="compareType">
        /// <list type="bullet">
        ///     <item>0: 同比</item>
        ///     <item>1: 环比</item>
        /// </list>
        /// </param>
        /// <param name="chartDataType">
        /// <list type="bullet">
        ///     <item>0: 用电量</item>
        ///     <item>1: 电费</item>
        /// </list>
        /// </param>
        /// <returns>IChart <para>see <see cref="IChart" /></para></returns>
        public async Task<IChart?> GetEnergyAnalysisChart(List<int> assetIds, DateTime startTime, DateTime endTime, int dateType, int compareType, int chartDataType = 0)
        {
            using var sqlClient = _provider.GetRequiredService<ISqlSugarClient>();
            var lineChart = new LineChartModel();

            // 按当日统计数据
            if (dateType == 0)
            {
                if (!assetIds.Any())
                {
                    lineChart.X = new string[0];
                    lineChart.Y1 = new decimal[0];
                    return lineChart;
                }

                var dayData = await GetEnergyDataByHour(assetIds, startTime, startTime);

                if (dayData == null || !dayData.Any())
                {
                    lineChart.X = new string[0];
                    lineChart.Y1 = new decimal[0];
                    return lineChart;
                }

                var dayChartResult = dayData.GroupBy(a => a.Time).Select(a => new
                {
                    Time = a.Key,
                    Value = Math.Round(a.Sum(p => p.Value), 2),
                    Cost = Math.Round(a.Sum(p => p.Cost), 2)
                }).OrderBy(a => a.Time);

                lineChart.X = dayChartResult.Select(a => a.Time.ToString("HH:00")).ToArray();
                lineChart.Y1 = chartDataType == 0 ? dayChartResult.Select(a => a.Value).ToArray() : dayChartResult.Select(a => a.Cost).ToArray();
            }
            else if (dateType == 1 || dateType == 3) // 按月统计数据
            {
                var currentDate = DateTime.Now;
                var searchStartDate = new DateTime(startTime.Year, startTime.Month, 1);
                var searchEndDate = currentDate;

                // 同比或者环比数据的查询开始与结束时间
                var yearOrChainStartDate = DateTime.MinValue;
                var yearOrChainEndDate = currentDate;

                if (dateType == 1)
                {
                    if (startTime.Year == currentDate.Year && startTime.Month == currentDate.Month)
                    {
                        searchEndDate = currentDate;

                        yearOrChainStartDate = compareType == 0 ? searchStartDate.AddYears(-1) : searchStartDate.AddDays(-1);
                        yearOrChainEndDate = compareType == 0 ? searchEndDate.AddYears(-1) : searchEndDate.AddDays(-1);
                    }
                    else
                    {
                        searchEndDate = new DateTime(startTime.Year, startTime.Month, DateTime.DaysInMonth(startTime.Year, startTime.Month));

                        yearOrChainStartDate = compareType == 0 ? searchStartDate.AddYears(-1) : searchStartDate.AddDays(-1);
                        yearOrChainEndDate = compareType == 0 ? searchEndDate.AddYears(-1) : searchEndDate.AddDays(-1);
                    }
                }
                else
                {
                    searchStartDate = startTime.Date;
                    searchEndDate = endTime.Date.AddHours(23);
                    yearOrChainStartDate = compareType == 0 ? searchStartDate.AddYears(-1) : searchStartDate.AddDays(-1);
                    yearOrChainEndDate = compareType == 0 ? searchEndDate.AddYears(-1) : searchEndDate.AddDays(-1);
                }

                var energyData = await GetEnergyDataByHour(assetIds, searchStartDate, searchEndDate);


                if (energyData == null || !energyData.Any())
                {
                    lineChart.X = new string[0];
                    lineChart.Y1 = new decimal[0];
                    return lineChart;
                }

                var chartResult = energyData.GroupBy(a => a.Time.ToString("yyyy-MM-dd")).Select(a => new
                {
                    Time = DateTime.Parse(a.Key),
                    Value = Math.Round(a.Sum(p => p.Value), 2),
                    Cost = Math.Round(a.Sum(p => p.Cost), 2)
                }).OrderBy(a => a.Time).ToList();

                var compareEnergyData = await GetEnergyDataByHour(assetIds, yearOrChainStartDate, yearOrChainEndDate);

                var compareChartResult = compareEnergyData.GroupBy(a => a.Time.ToString("yyyy-MM-dd")).Select(a => new
                {
                    Time = DateTime.Parse(a.Key),
                    Value = Math.Round(a.Sum(p => p.Value), 2),
                    Cost = Math.Round(a.Sum(p => p.Cost), 2)
                });

                var xData = new string[chartResult.Count];
                var y1Data = new decimal[chartResult.Count];
                var y2Data = new decimal[chartResult.Count];

                for (int i = 0; i < chartResult.Count; i++)
                {
                    xData[i] = chartResult[i].Time.ToString("yyyy-MM-dd");
                    y1Data[i] = Math.Round(chartDataType == 0 ? chartResult[i].Value : chartResult[i].Cost, 2);

                    if (compareChartResult.Any())
                    {
                        var tempSearchDate = compareType == 0 ? chartResult[i].Time.AddYears(-1) : chartResult[i].Time.AddDays(-1);
                        if (chartDataType == 0)
                        {
                            y2Data[i] = Math.Round(compareChartResult.FirstOrDefault(a => a.Time == tempSearchDate)?.Value ?? .0M, 2);
                        }
                        else
                        {
                            y2Data[i] = Math.Round(compareChartResult.FirstOrDefault(a => a.Time == tempSearchDate)?.Cost ?? .0M, 2);
                        }
                    }
                }

                lineChart.X = xData;
                lineChart.Y1 = y1Data;
                lineChart.Y2 = y2Data;
            }
            else
            {
                var currentDate = DateTime.Now;
                var searchStartDate = new DateTime(startTime.Year, 1, 1);
                var totalMonthInYear = currentDate.Year == startTime.Year ? currentDate.Month : 12;
                var searchEndDate = new DateTime(startTime.Year, totalMonthInYear, DateTime.DaysInMonth(startTime.Year, totalMonthInYear));

                // 同比或者环比数据的查询开始与结束时间
                var yearOrChainStartDate = compareType == 0 ? searchStartDate.AddYears(-1) : searchStartDate.AddMonths(-1);
                var yearOrChainEndDate = compareType == 0 ? searchEndDate.AddYears(-1) : searchEndDate.AddMonths(-1);


                var energyData = await GetEnergyDataByHour(assetIds, searchStartDate, searchEndDate);


                if (energyData == null || !energyData.Any())
                {
                    lineChart.X = new string[0];
                    lineChart.Y1 = new decimal[0];
                    return lineChart;
                }

                var chartResult = energyData.GroupBy(a => a.Time.ToString("yyyy-MM")).Select(a => new
                {
                    Time = DateTime.Parse($"{a.Key}-01"),
                    Value = Math.Round(a.Sum(p => p.Value), 2),
                    Cost = Math.Round(a.Sum(p => p.Cost), 2)
                }).OrderBy(a => a.Time).ToList();

                var compareEnergyData = await GetEnergyDataByHour(assetIds, yearOrChainStartDate, yearOrChainEndDate);

                var compareChartResult = compareEnergyData.GroupBy(a => a.Time.ToString("yyyy-MM")).Select(a => new
                {
                    Time = DateTime.Parse($"{a.Key}-01"),
                    Value = Math.Round(a.Sum(p => p.Value), 2),
                    Cost = Math.Round(a.Sum(p => p.Cost), 2)
                });

                var xData = new string[chartResult.Count];
                var y1Data = new decimal[chartResult.Count];
                var y2Data = new decimal[chartResult.Count];

                for (int i = 0; i < chartResult.Count; i++)
                {
                    xData[i] = chartResult[i].Time.ToString("yyyy-MM");
                    y1Data[i] = Math.Round(chartDataType == 0 ? chartResult[i].Value : chartResult[i].Cost, 2);

                    if (compareChartResult.Any())
                    {
                        var tempSearchDate = compareType == 0 ? chartResult[i].Time.AddYears(-1) : chartResult[i].Time.AddMonths(-1);
                        if (chartDataType == 0)
                        {
                            y2Data[i] = Math.Round(compareChartResult.FirstOrDefault(a => a.Time == tempSearchDate)?.Value ?? .0M, 2);
                        }
                        else
                        {
                            y2Data[i] = Math.Round(compareChartResult.FirstOrDefault(a => a.Time == tempSearchDate)?.Cost ?? .0M, 2);
                        }
                    }
                }

                lineChart.X = xData;
                lineChart.Y1 = y1Data;
                lineChart.Y2 = y2Data;
            }

            return lineChart;
        }

        /// <summary>
        /// 根据图表类型查询用电量或者电费
        /// </summary>
        /// <param name="assetIds">资产id列表</param>
        /// <param name="startTime">需要查询的开始日期</param>
        /// <param name="endTime">需要查询的结束日期</param>
        /// <param name="dateType">
        /// <list type="bullet">
        ///     <item>1: 月</item>
        ///     <item>2: 年</item>
        ///     <item>3: 自定义</item>
        /// </list>
        /// </param>
        /// <param name="compareType">
        /// <list type="bullet">
        ///     <item>0: 同比</item>
        ///     <item>1: 环比</item>
        /// </list>
        /// </param>
        /// <param name="chartDataType">
        /// <list type="bullet">
        ///     <item>0: 用电量</item>
        ///     <item>1: 电费</item>
        /// </list>
        /// </param>
        /// <returns>IChart <para>see <see cref="IChart" /></para></returns>
        public async Task<IChart?> GetEnergyChart(List<int> assetIds, DateTime startTime, DateTime endTime, int dateType, int compareType, int chartDataType = 0)
        {
            using var sqlClient = _provider.GetRequiredService<ISqlSugarClient>();
            var lineChart = new LineChartModel();

            var searchBaseQuery = sqlClient.Queryable<EnergySummary>()
                .Where(a => SqlFunc.ContainsArray(assetIds, a.AssetId));

            // 按月统计数据
            if (dateType == 1 || dateType == 3)
            {
                var currentDate = DateTime.Now;
                var searchStartDate = new DateTime(startTime.Year, startTime.Month, 1);
                var searchEndDate = currentDate;

                // 同比或者环比数据的查询开始与结束时间
                var yearOrChainStartDate = DateTime.MinValue;
                var yearOrChainEndDate = currentDate;

                if (dateType == 1)
                {
                    if (startTime.Year == currentDate.Year && startTime.Month == currentDate.Month)
                    {
                        searchEndDate = currentDate;

                        yearOrChainStartDate = compareType == 0 ? searchStartDate.AddYears(-1) : searchStartDate.AddDays(-1);
                        yearOrChainEndDate = compareType == 0 ? searchEndDate.AddYears(-1) : searchEndDate.AddDays(-1);
                    }
                    else
                    {
                        searchEndDate = new DateTime(startTime.Year, startTime.Month, DateTime.DaysInMonth(startTime.Year, startTime.Month));

                        yearOrChainStartDate = compareType == 0 ? searchStartDate.AddYears(-1) : searchStartDate.AddDays(-1);
                        yearOrChainEndDate = compareType == 0 ? searchEndDate.AddYears(-1) : searchEndDate.AddDays(-1);
                    }
                }
                else
                {
                    searchStartDate = startTime.Date;
                    searchEndDate = endTime.Date.AddHours(23);
                    yearOrChainStartDate = compareType == 0 ? searchStartDate.AddYears(-1) : searchStartDate.AddDays(-1);
                    yearOrChainEndDate = compareType == 0 ? searchEndDate.AddYears(-1) : searchEndDate.AddDays(-1);
                }

                var result = await searchBaseQuery
                    .Where(a => a.ConsumptionDate >= searchStartDate && a.ConsumptionDate <= searchEndDate)
                    .GroupBy(a => a.ConsumptionDate)
                    .Select(a => new
                    {
                        a.ConsumptionDate,
                        Value = SqlFunc.AggregateSum(chartDataType == 0 ? (a.PeakElectricity + a.FlatElectricity + a.ValleyElectricity) / 1000.0M : (a.PeakCost + a.FlatCost + a.ValleyCost))
                    })
                    .OrderBy(a => a.ConsumptionDate)
                    .ToListAsync();

                var compareResult = await searchBaseQuery
                    .Where(a => a.ConsumptionDate >= yearOrChainStartDate && a.ConsumptionDate <= yearOrChainEndDate)
                    .GroupBy(a => a.ConsumptionDate)
                    .Select(a => new
                    {
                        a.ConsumptionDate,
                        Value = SqlFunc.AggregateSum(chartDataType == 0 ? (a.PeakElectricity + a.FlatElectricity + a.ValleyElectricity) / 1000.0M : (a.PeakCost + a.FlatCost + a.ValleyCost))
                    })
                    .OrderBy(a => a.ConsumptionDate)
                    .ToListAsync();

                var xData = new string[result.Count];
                var y1Data = new decimal[result.Count];
                var y2Data = new decimal[result.Count];

                for (int i = 0; i < result.Count; i++)
                {
                    xData[i] = result[i].ConsumptionDate.ToString("yyyy-MM-dd");
                    y1Data[i] = Math.Round(result[i].Value, 2);

                    var tempSearchDate = compareType == 0 ? result[i].ConsumptionDate.AddYears(-1) : result[i].ConsumptionDate.AddDays(-1);
                    y2Data[i] = Math.Round(compareResult.FirstOrDefault(a => a.ConsumptionDate == tempSearchDate)?.Value ?? .0M, 2);
                }

                lineChart.X = xData;
                lineChart.Y1 = y1Data;
                lineChart.Y2 = y2Data;
            }
            else
            {
                var currentDate = DateTime.Now;
                var searchStartDate = new DateTime(startTime.Year, 1, 1);
                var totalMonthInYear = currentDate.Year == startTime.Year ? currentDate.Month : 12;
                var searchEndDate = new DateTime(startTime.Year, totalMonthInYear, DateTime.DaysInMonth(startTime.Year, totalMonthInYear));

                // 同比或者环比数据的查询开始与结束时间
                var yearOrChainStartDate = compareType == 0 ? searchStartDate.AddYears(-1) : searchStartDate.AddMonths(-1);
                var yearOrChainEndDate = compareType == 0 ? searchEndDate.AddYears(-1) : searchEndDate.AddMonths(-1);

                var result = await searchBaseQuery
                    .Where(a => a.ConsumptionDate >= searchStartDate && a.ConsumptionDate <= searchEndDate)
                    .GroupBy(a => a.ConsumptionDate.ToString("yyyy-MM"))
                    .Select(a => new
                    {
                        ConsumptionDate = SqlFunc.AggregateMin(a.ConsumptionDate),
                        Value = SqlFunc.AggregateSum(chartDataType == 0 ? (a.PeakElectricity + a.FlatElectricity + a.ValleyElectricity) / 1000.0M : (a.PeakCost + a.FlatCost + a.ValleyCost))
                    })
                    .OrderBy(a => a.ConsumptionDate)
                    .ToListAsync();

                var compareResult = await searchBaseQuery
                    .Where(a => a.ConsumptionDate >= yearOrChainStartDate && a.ConsumptionDate <= yearOrChainEndDate)
                    .GroupBy(a => a.ConsumptionDate.ToString("yyyy-MM"))
                    .Select(a => new
                    {
                        ConsumptionDate = SqlFunc.AggregateMin(a.ConsumptionDate),
                        Value = SqlFunc.AggregateSum(chartDataType == 0 ? (a.PeakElectricity + a.FlatElectricity + a.ValleyElectricity) / 1000.0M : (a.PeakCost + a.FlatCost + a.ValleyCost))
                    })
                    .OrderBy(a => a.ConsumptionDate)
                    .ToListAsync();

                var xData = new string[result.Count];
                var y1Data = new decimal[result.Count];
                var y2Data = new decimal[result.Count];

                for (int i = 0; i < result.Count; i++)
                {
                    xData[i] = result[i].ConsumptionDate.ToString("yyyy-MM");
                    y1Data[i] = Math.Round(result[i].Value, 2);

                    var tempSearchDate = compareType == 0 ? result[i].ConsumptionDate.AddYears(-1) : result[i].ConsumptionDate.AddMonths(-1);
                    y2Data[i] = Math.Round(compareResult.FirstOrDefault(a => a.ConsumptionDate.ToString("yyyy-MM") == tempSearchDate.ToString("yyyy-MM"))?.Value ?? .0M, 2);
                }

                lineChart.X = xData;
                lineChart.Y1 = y1Data;
                lineChart.Y2 = y2Data;
            }

            return lineChart;
        }

        public async Task<List<(DateTime Date, decimal Value, string Field)>> GetPowerData(List<int> assetIds, DateTime startTime, DateTime endTime, List<int> powerTypes)
        {
            var lineChart = new LineChartModel();

            IConfiguration configuration = _provider.GetRequiredService<IConfiguration>();
            var session = configuration.GetSection(InfluxDBConfig.Influxdb);
            InfluxDBConfig influxDbConfig = null;
            if (session != null && session.Exists())
            {
                influxDbConfig = session.Get<InfluxDBConfig>();
            }

            if (influxDbConfig == null)
            {
                _logger.LogError("EnergyManagementServer init: Not found Influxdb config.");
                return null;
            }

            if (assetIds == null || !assetIds.Any())
            {
                return null;
            }

            List<(DateTime Date, decimal Value, string Field)> dataResult = new List<(DateTime Date, decimal Value, string Field)>();

            try
            {
                using var sqlClient = _provider.GetRequiredService<ISqlSugarClient>();
                var dashboardConfig = await sqlClient.Queryable<AssetDashboardConfig>().Where(d => d.ConfigName == "ENERGY_THREEPOWER").FirstAsync();

                var sql = dashboardConfig.Sql;
                if (dashboardConfig != null)
                {

                    var assetIdQuery = new StringBuilder();
                    foreach (var id in assetIds)
                    {
                        if (assetIdQuery.Length > 0)
                        {
                            assetIdQuery.Append(" or ");
                        }
                        assetIdQuery.Append("r[\"assetid\"] == \"");
                        assetIdQuery.Append(id);
                        assetIdQuery.Append('\"');
                    }

                    var fieldQuery = new StringBuilder();
                    string powerTypeQuery = string.Empty;
                    string mapValue = string.Empty;
                    string mapSum = string.Empty;
                    foreach (var power in powerTypes)
                    {
                        if (fieldQuery.Length > 0)
                        {
                            fieldQuery.Append(" or ");
                        }
                        fieldQuery.Append("r[\"_field\"] == \"");

                        switch (power)
                        {
                            case 0:
                                fieldQuery.Append("p\" or r[\"_field\"] == \"active_power");
                                powerTypeQuery = "\"p\"";
                                mapSum = "((if exists r.p then r.p else 0.0)  + (if exists r.active_power then r.active_power else 0.0))";
                                break;
                            case 1:
                                fieldQuery.Append("q\" or r[\"_field\"] == \"collective_reactive_power");
                                powerTypeQuery = "\"q\"";
                                mapSum = "((if exists r.q then r.q else 0.0)  + (if exists r.collective_reactive_power then r.collective_reactive_power else 0.0))";
                                break;
                            case 2:
                                fieldQuery.Append("s\" or r[\"_field\"] == \"apparent_power");
                                powerTypeQuery = "\"s\"";
                                mapSum = "((if exists r.s then r.s else 0.0)  + (if exists r.apparent_power then r.apparent_power else 0.0))";
                                break;
                            default:
                                break;

                        }
                        fieldQuery.Append('\"');
                    }

                    var coefficient = 1000;

                    var systemConfig = await sqlClient.Queryable<SystemConfig>().FirstAsync(c => c.Type == "SpecialConfig" && c.Name == "EnergyCoefficient");

                    if (systemConfig != null && int.TryParse(systemConfig.Value, out var coefficientInt))
                    {
                        coefficient = coefficientInt;
                    }

                    sql = sql
                        .Replace("[[DBName]]", influxDbConfig.Bucket)
                        .Replace("[[TableName]]", "archivedatarealtime")
                        .Replace("[[AssetIdList]]", assetIdQuery.ToString())
                        .Replace("[[FieldFilter]]", fieldQuery.ToString())
                        .Replace("[[MapSum]]", mapSum)
                        .Replace("[[Cycle]]", "15m")
                        .Replace("[[Coefficient]]", coefficient.ToString(".0"))
                        .Replace("[[powerTypeQuery]]", powerTypeQuery)
                        .Replace("[[StartDate]]", startTime.Date.AddMinutes(-1).GetTimestampForSec().ToString())
                        .Replace("[[EndDate]]", endTime.GetTimestampForSec().ToString());

                    using var influxClient = new InfluxDBClient(influxDbConfig.Url, influxDbConfig.UserName, influxDbConfig.Password);

                    var queryApi = influxClient.GetQueryApi();
                    var result = await queryApi.QueryAsync(sql, influxDbConfig.OrgId ?? influxDbConfig.OrgName);

                    var localTimeZone = TimeZoneInfo.Local;
                    foreach (var table in result)
                    {
                        foreach (var item in table.Records)
                        {
                            var currentDate = TimeZoneInfo.ConvertTimeFromUtc(item.GetTimeInDateTime().Value, localTimeZone);
                            var currentValue = Convert.ToDecimal(item.GetValue());
                            var columnName = item.GetField();

                            dataResult.Add((currentDate, currentValue, columnName));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "EnergyManagementServer GetPowerData Error.");
            }

            return dataResult;
        }

        public async Task<List<PowerExportModel>> ExportPowerData(List<int> assetIds, DateTime startTime, DateTime endTime)
        {
            var lineChart = new LineChartModel();

            IConfiguration configuration = _provider.GetRequiredService<IConfiguration>();
            var session = configuration.GetSection(InfluxDBConfig.Influxdb);
            InfluxDBConfig influxDbConfig = null;
            if (session != null && session.Exists())
            {
                influxDbConfig = session.Get<InfluxDBConfig>();
            }

            if (influxDbConfig == null)
            {
                _logger.LogError("EnergyManagementServer init: Not found Influxdb config.");
                return null;
            }

            if (assetIds == null || !assetIds.Any())
            {
                return null;
            }

            List<PowerExportModel> dataResult = new();

            try
            {
                using var sqlClient = _provider.GetRequiredService<ISqlSugarClient>();
                var dashboardConfig = await sqlClient.Queryable<AssetDashboardConfig>().Where(d => d.ConfigName == "ENERGY_THREEPOWER_EXPORT").FirstAsync();

                var sql = dashboardConfig.Sql;
                if (dashboardConfig != null)
                {

                    var assetIdQuery = new StringBuilder();
                    foreach (var id in assetIds)
                    {
                        if (assetIdQuery.Length > 0)
                        {
                            assetIdQuery.Append(" or ");
                        }
                        assetIdQuery.Append("r[\"assetid\"] == \"");
                        assetIdQuery.Append(id);
                        assetIdQuery.Append('\"');
                    }

                    var fieldQuery = new StringBuilder();
                    string powerTypeQuery = string.Empty;
                    string mapValue = string.Empty;
                    string mapSum = string.Empty;

                    var coefficient = 1000;

                    var systemConfig = await sqlClient.Queryable<SystemConfig>().FirstAsync(c => c.Type == "SpecialConfig" && c.Name == "EnergyCoefficient");

                    if (systemConfig != null && int.TryParse(systemConfig.Value, out var coefficientInt))
                    {
                        coefficient = coefficientInt;
                    }

                    if (endTime.Date == DateTime.Now.Date)
                    {
                        endTime = DateTime.Now;
                    }
                    else
                    {
                        endTime = endTime.Date.AddDays(1).AddMinutes(-1);
                    }

                    sql = sql
                        .Replace("[[DBName]]", influxDbConfig.Bucket)
                        .Replace("[[TableName]]", "archivedatarealtime")
                        .Replace("[[AssetIdList]]", assetIdQuery.ToString())
                        .Replace("[[Cycle]]", "15m")
                        .Replace("[[Coefficient]]", coefficient.ToString(".0"))
                        .Replace("[[StartDate]]", startTime.Date.AddMinutes(-1).GetTimestampForSec().ToString())
                        .Replace("[[EndDate]]", endTime.GetTimestampForSec().ToString());

                    using var influxClient = new InfluxDBClient(influxDbConfig.Url, influxDbConfig.UserName, influxDbConfig.Password);

                    var queryApi = influxClient.GetQueryApi();
                    var result = await queryApi.QueryAsync(sql, influxDbConfig.OrgId ?? influxDbConfig.OrgName);

                    var localTimeZone = TimeZoneInfo.Local;
                    PowerExportModel powerExportModel;
                    foreach (var table in result)
                    {
                        foreach (var item in table.Records)
                        {
                            powerExportModel = new PowerExportModel();

                            powerExportModel.Time = TimeZoneInfo.ConvertTimeFromUtc(item.GetTimeInDateTime().Value, localTimeZone);
                            powerExportModel.AssetId = Convert.ToInt32(item.GetValueByKey("assetid"));
                            powerExportModel.P = Convert.ToDecimal(item.GetValueByKey("total_p"));
                            powerExportModel.Q = Convert.ToDecimal(item.GetValueByKey("total_q"));
                            powerExportModel.S = Convert.ToDecimal(item.GetValueByKey("total_s"));

                            dataResult.Add(powerExportModel);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "EnergyManagementServer GetPowerData Error.");
            }

            return dataResult;
        }

        /// <summary>
        /// 按条件获取峰平谷图表
        /// </summary>
        /// <param name="startTime">查询时间</param>
        /// <param name="measurementFinal">计量类型</param>
        /// <param name="circuitId">回路Id</param>
        /// <param name="assetId">资产Id</param>
        /// <returns>List EnergySummary</returns>
        public async Task<List<EnergySummary>> GetPeakFlatValleyData(DateTime startTime, string measurementFinal, int circuitId, int assetId)
        {
            using var sqlClient = _provider.GetRequiredService<ISqlSugarClient>();

            var searchMonthDays = DateTime.DaysInMonth(startTime.Year, startTime.Month);
            var searchStartDate = new DateTime(startTime.Year, startTime.Month, 1);
            var searchEndDate = new DateTime(startTime.Year, startTime.Month, searchMonthDays).AddDays(1);

            List<int> assetRelations = new List<int>();
            if (circuitId > 0)
            {
                assetRelations = await sqlClient.Queryable<AssetRelation>()
                    .Where(a => a.ParentId == circuitId)
                    .Select(a => a.ChildId)
                    .ToListAsync();
            }

            var assetIds = await sqlClient.Queryable<AssetInfo>()
                .Where(a => a.AssetLevel == AssetLevel.Device)
                .WhereIF(!string.IsNullOrWhiteSpace(measurementFinal), a => a.MeterType == measurementFinal)
                .WhereIF(circuitId > 0, a => SqlFunc.ContainsArray(assetRelations.ToArray(), a.Id))
                .WhereIF(assetId > 0, a => a.Id == assetId)
                .Select(a => a.Id)
                .ToListAsync();

            // 查询对应区间的数据
            var summaryList = await sqlClient.Queryable<EnergySummary>()
                .Where(a => SqlFunc.ContainsArray(assetIds, a.AssetId))
                .Where(a => a.ConsumptionDate >= searchStartDate && a.ConsumptionDate < searchEndDate)
                .GroupBy(a => a.ConsumptionDate)
                .Select(a => new EnergySummary
                {
                    ConsumptionDate = a.ConsumptionDate,
                    PeakElectricity = SqlFunc.AggregateSum(a.PeakElectricity),
                    FlatElectricity = SqlFunc.AggregateSum(a.FlatElectricity),
                    ValleyElectricity = SqlFunc.AggregateSum(a.ValleyElectricity),
                    DeepValleyElectricity = SqlFunc.AggregateSum(a.DeepValleyElectricity),
                    HighPeakElectricity = SqlFunc.AggregateSum(a.HighPeakElectricity),
                    PeakCost = SqlFunc.AggregateSum(a.PeakCost),
                    FlatCost = SqlFunc.AggregateSum(a.FlatCost),
                    ValleyCost = SqlFunc.AggregateSum(a.ValleyCost),
                    HighPeakCost = SqlFunc.AggregateSum(a.HighPeakCost),
                    DeepValleyCost = SqlFunc.AggregateSum(a.DeepValleyCost)
                })
                .OrderBy(a => a.ConsumptionDate)
                .ToListAsync();

            return summaryList;
        }


        /// <summary>
        /// 按条件实时获取峰平谷图表数据
        /// </summary>
        /// <param name="startTime">查询时间</param>
        /// <param name="measurementFinal">计量类型</param>
        /// <param name="circuitId">回路Id</param>
        /// <param name="assetId">资产Id</param>
        /// <returns>List EnergySummary</returns>
        public async Task<List<EnergySummary>> GetPeakFlatValleyRealTimeData(DateTime startTime, string measurementFinal, int circuitId, int assetId)
        {
            using var sqlClient = _provider.GetRequiredService<ISqlSugarClient>();

            var searchMonthDays = DateTime.DaysInMonth(startTime.Year, startTime.Month);
            var searchStartDate = new DateTime(startTime.Year, startTime.Month, 1);
            var searchEndDate = new DateTime(startTime.Year, startTime.Month, searchMonthDays);

            List<int> assetRelations = new List<int>();
            if (circuitId > 0)
            {
                assetRelations = await sqlClient.Queryable<AssetRelation>()
                    .Where(a => a.ParentId == circuitId)
                    .Select(a => a.ChildId)
                    .ToListAsync();
            }

            var assetIds = await sqlClient.Queryable<AssetInfo>()
                .Where(a => a.AssetLevel == AssetLevel.Device)
                .WhereIF(!string.IsNullOrWhiteSpace(measurementFinal), a => a.MeterType == measurementFinal)
                .WhereIF(circuitId > 0, a => SqlFunc.ContainsArray(assetRelations.ToArray(), a.Id))
                .WhereIF(assetId > 0, a => a.Id == assetId)
                .Select(a => a.Id)
                .ToListAsync();

            List<EnergySummary> energySummaries = new List<EnergySummary>();

            var data = await GetEnergyDataByHour(assetIds, searchStartDate, searchEndDate);

            if (data == null || !data.Any())
            {
                return energySummaries;
            }

            var allTimes = data.GroupBy(a => a.Time.ToString("yyyy-MM-dd")).Select(a => DateTime.Parse(a.Key)).ToList();

            EnergySummary energySummary;
            var bs = await _client.Queryable<BillingScheme>().Where(d => d.billingCode == "TimeSharing").FirstAsync();
            var electricityList = await _client.Queryable<ElectricityConfig>().Where(a => a.bsid == bs.Id && a.electrovalenceType == "受电" || a.electrovalenceType == ElectrovalenceType.receivePower.ToString()).ToArrayAsync();
            var history = await _client.Queryable<ElectricityConfigHistory>()
                      .Where(a => a.bsid == bs.Id && a.electrovalenceType == "受电" || a.electrovalenceType == ElectrovalenceType.receivePower.ToString()).ToArrayAsync();
            allTimes.ForEach(async t =>
            {

                energySummary = new EnergySummary();
                var currentTimeData = data.Where(d => d.Time.ToString("yyyy-MM-dd") == t.ToString("yyyy-MM-dd"));
                foreach (var d in currentTimeData)
                {
                    var electricity = await _electricityCharge.GetElectricityChargeList(d.Time, electricityList, history);
                    d.desc = electricity.rateDesc;
                }
                // 苏州工业用电电价及峰平谷时间段：
                // 峰：8:00-11:00，17:00-22:00，电价：1.0347
                // 平：11:00-17:00，22:00-24:00，电价：0.6068
                // 谷：0:00-8:00，电价：0.2589

                energySummary.ConsumptionDate = t;
                var peakData = currentTimeData.Where(c => c.desc == "峰").ToList();
                //var peakData = currentTimeData.Where(c => (c.Time.Hour >= 8 && c.Time.Hour < 11) || (c.Time.Hour >= 17 && c.Time.Hour < 22)).ToList();
                energySummary.PeakElectricity = peakData?.Sum(a => a.Value) ?? .0M;
                energySummary.PeakCost = peakData?.Sum(a => a.Cost) ?? .0M;
                var flatData = currentTimeData.Where(c => c.desc == "平").ToList();
                //var flatData = currentTimeData.Where(c => (c.Time.Hour >= 11 && c.Time.Hour < 17) || (c.Time.Hour >= 22 && c.Time.Hour < 24)).ToList();
                energySummary.FlatElectricity = flatData?.Sum(a => a.Value) ?? .0M;
                energySummary.FlatCost = flatData?.Sum(a => a.Cost) ?? .0M;
                var valleyData = currentTimeData.Where(c => c.desc == "谷").ToList();
                //var valleyData = currentTimeData.Where(c => c.Time.Hour >= 0 && c.Time.Hour < 8).ToList();
                energySummary.ValleyElectricity = valleyData?.Sum(a => a.Value) ?? .0M;
                energySummary.ValleyCost = valleyData?.Sum(a => a.Cost) ?? .0M;
                var deepValleyData = currentTimeData.Where(c => c.desc == "深谷").ToList();
                //var valleyData = currentTimeData.Where(c => c.Time.Hour >= 0 && c.Time.Hour < 8).ToList();
                energySummary.DeepValleyElectricity = deepValleyData?.Sum(a => a.Value) ?? .0M;
                energySummary.DeepValleyCost = deepValleyData?.Sum(a => a.Cost) ?? .0M;
                var highpeakData = currentTimeData.Where(c => c.desc == "尖峰").ToList();
                //var valleyData = currentTimeData.Where(c => c.Time.Hour >= 0 && c.Time.Hour < 8).ToList();
                energySummary.HighPeakElectricity = highpeakData?.Sum(a => a.Value) ?? .0M;
                energySummary.HighPeakCost = highpeakData?.Sum(a => a.Cost) ?? .0M;
                energySummaries.Add(energySummary);
            });

            return energySummaries;
        }

        /// <summary>
        /// 获取消耗电量结构数据
        /// </summary>
        /// <param name="startTime">需要查询的开始日期</param>
        /// <param name="endTime">需要查询的结束日期</param>
        /// <param name="dateType">
        /// <list type="bullet">
        ///     <item>0: 日</item>
        ///     <item>1: 月</item>
        ///     <item>2: 年</item>
        ///     <item>3: 自定义</item>
        /// </list>
        /// </param>
        /// <returns>List</returns>

        public async Task<List<(string Name, decimal Value)>> GetStructureData(DateTime startTime, DateTime endTime, int dateType)
        {
            using var sqlClient = _provider.GetRequiredService<ISqlSugarClient>();
            var lineChart = new LineChartModel();

            var searchStartDate = DateTime.MinValue;
            var searchEndDate = DateTime.MinValue;

            if (dateType == 0)
            {
                searchStartDate = startTime.Date;
                searchEndDate = startTime.Date.AddDays(1);
            }
            else if (dateType == 1)
            {
                searchStartDate = new DateTime(startTime.Year, startTime.Month, 1);
                searchEndDate = new DateTime(startTime.Year, startTime.Month, DateTime.DaysInMonth(startTime.Year, startTime.Month)).AddDays(1);
            }
            else if (dateType == 2)
            {
                searchStartDate = new DateTime(startTime.Year, 1, 1);
                searchEndDate = new DateTime(startTime.Year + 1, 1, 1);
            }
            else
            {
                searchStartDate = startTime.Date;
                searchEndDate = endTime.Date.AddDays(1);
            }

            var summaryList = await sqlClient.Queryable<EnergySummary>()
                .InnerJoin<AssetInfo>((e, a) => e.AssetId == a.Id)
                .Where((e, a) => e.ConsumptionDate >= searchStartDate && e.ConsumptionDate < searchEndDate)
                .GroupBy((e, a) => e.AssetId)
                .Select((e, a) => new
                {
                    e.AssetId,
                    Electricity = SqlFunc.AggregateSum((e.PeakElectricity + e.FlatElectricity + e.ValleyElectricity) / 1000.0M),
                })
                .ToListAsync();

            var tempRelations = await sqlClient.Queryable<AssetInfo>()
                .LeftJoin<AssetRelation>((a, ar) => a.Id == ar.ParentId)
                .Where((a, ar) => a.AssetLevel == AssetLevel.Circuit)
                .Select((a, ar) => new
                {
                    a.Id,
                    a.UseScene,
                    ar.ChildId
                })
                .ToListAsync();

            var structureResult = (from u in (from t in tempRelations
                                              join s in summaryList on t.ChildId equals s.AssetId into tjoin
                                              from tj in tjoin.DefaultIfEmpty()
                                              select new
                                              {
                                                  t.UseScene,
                                                  Electricity = tj?.Electricity ?? 0.0M
                                              })
                                   group u by u.UseScene into ug
                                   select (ug.Key ?? string.Empty, Math.Round(ug.Sum(a => a.Electricity), 2))).ToList();

            return structureResult;
        }

        /// <summary>
        /// 获取实时消耗电量结构数据
        /// </summary>
        /// <param name="startTime">需要查询的开始日期</param>
        /// <param name="endTime">需要查询的结束日期</param>
        /// <param name="dateType">
        /// <list type="bullet">
        ///     <item>0: 日</item>
        ///     <item>1: 月</item>
        ///     <item>2: 年</item>
        ///     <item>3: 自定义</item>
        /// </list>
        /// </param>
        /// <returns>List</returns>
        public async Task<List<(string Name, decimal Value)>> GetStructureRealTimeData(DateTime startTime, DateTime endTime, int dateType)
        {
            using var sqlClient = _provider.GetRequiredService<ISqlSugarClient>();
            var lineChart = new LineChartModel();

            var searchStartDate = DateTime.MinValue;
            var searchEndDate = DateTime.MinValue;

            if (dateType == 0)
            {
                searchStartDate = startTime.Date;
                searchEndDate = startTime.Date;
            }
            else if (dateType == 1)
            {
                searchStartDate = new DateTime(startTime.Year, startTime.Month, 1);
                searchEndDate = new DateTime(startTime.Year, startTime.Month, DateTime.DaysInMonth(startTime.Year, startTime.Month)).AddDays(1);
            }
            else if (dateType == 2)
            {
                searchStartDate = new DateTime(startTime.Year, 1, 1);
                searchEndDate = new DateTime(startTime.Year + 1, 1, 1);
            }
            else
            {
                searchStartDate = startTime.Date;
                searchEndDate = endTime.Date;
            }

            var assetIds = await sqlClient.Queryable<AssetInfo>().Where(a => a.AssetLevel == AssetLevel.Device).Select(a => a.Id).ToListAsync();

            var data = await GetEnergyDataByHour(assetIds, searchStartDate, searchEndDate);

            if (data == null || !data.Any())
            {
                return new List<(string Name, decimal Value)>();
            }

            var summaryList = data.GroupBy(a => a.AssetId).Select(a => new
            {
                AssetId = a.Key,
                Electricity = Math.Round(a.Sum(p => p.Value), 2)
            }).ToList();

            var tempRelations = await sqlClient.Queryable<AssetInfo>()
                .LeftJoin<AssetRelation>((a, ar) => a.Id == ar.ParentId)
                .Where((a, ar) => a.AssetLevel == AssetLevel.Circuit)
                .Select((a, ar) => new
                {
                    a.Id,
                    a.UseScene,
                    ar.ChildId
                })
                .ToListAsync();

            var structureResult = (from u in (from t in tempRelations
                                              join s in summaryList on t.ChildId equals s.AssetId into tjoin
                                              from tj in tjoin.DefaultIfEmpty()
                                              select new
                                              {
                                                  t.UseScene,
                                                  Electricity = tj?.Electricity ?? 0.0M
                                              })
                                   group u by u.UseScene into ug
                                   select (ug.Key ?? string.Empty, Math.Round(ug.Sum(a => a.Electricity), 2))).ToList();

            return structureResult;
        }

        /// <summary>
        /// 获取消耗电量top10数据
        /// </summary>
        /// <param name="startTime">需要查询的开始日期</param>
        /// <param name="endTime">需要查询的结束日期</param>
        /// <param name="dateType">
        /// <list type="bullet">
        ///     <item>0: 日</item>
        ///     <item>1: 月</item>
        ///     <item>2: 年</item>
        ///     <item>3: 自定义</item>
        /// </list>
        /// </param>
        /// <returns>List</returns>

        public async Task<List<(string AssetName, decimal Value)>> GetTopTenData(DateTime startTime, DateTime endTime, int dateType)
        {
            using var sqlClient = _provider.GetRequiredService<ISqlSugarClient>();
            var lineChart = new LineChartModel();

            var searchStartDate = DateTime.MinValue;
            var searchEndDate = DateTime.MinValue;

            if (dateType == 0)
            {
                searchStartDate = startTime.Date;
                searchEndDate = startTime.Date.AddDays(1);
            }
            else if (dateType == 1)
            {
                searchStartDate = new DateTime(startTime.Year, startTime.Month, 1);
                searchEndDate = new DateTime(startTime.Year, startTime.Month, DateTime.DaysInMonth(startTime.Year, startTime.Month)).AddDays(1);
            }
            else if (dateType == 2)
            {
                searchStartDate = new DateTime(startTime.Year, 1, 1);
                searchEndDate = new DateTime(startTime.Year + 1, 1, 1);
            }
            else
            {
                searchStartDate = startTime.Date;
                searchEndDate = endTime.Date.AddDays(1);
            }
            //获取当前小时数据
            var assetIds = await _client.Queryable<AssetInfo>()
                .Where(a => a.AssetLevel == AssetLevel.Device)
                .Select(a => a.Id)
                .ToListAsync();
            var now = DateTime.Now;
            var realtimeHourStart = new DateTime(now.Year, now.Month, now.Day, now.Hour, 0, 5);
            var realtimeHourData = await GetEnergyDataByInterval(assetIds, realtimeHourStart, searchEndDate, "1h");
            Dictionary<int, decimal> keyValuePairs = new Dictionary<int, decimal>();
            foreach (var data in realtimeHourData)
            {
                if (!keyValuePairs.ContainsKey(data.AssetId))
                {
                    keyValuePairs.Add(data.AssetId, data.Value);
                }
                else
                {
                    keyValuePairs[data.AssetId] += data.Value;
                }

            }
            //将当前数据和历史job数据结合起来
            var summaryList = await sqlClient.Queryable<EnergySummary>()
                .InnerJoin<AssetInfo>((e, a) => e.AssetId == a.Id)
                .Where((e, a) => e.ConsumptionDate >= searchStartDate && e.ConsumptionDate < searchEndDate)
                .GroupBy((e, a) => e.AssetId)
                .Select((e, a) => new
                {
                    e.AssetId,
                    AssetName = SqlFunc.AggregateMax(a.AssetName),
                    Electricity = SqlFunc.AggregateSum((e.PeakElectricity + e.FlatElectricity + e.ValleyElectricity) / 1000.0M),
                })
                .MergeTable()
                .OrderByDescending(a => a.Electricity)
                .Take(10)
                .ToListAsync();
            //将summaryList存在的assetid加入到dict，防止出现不存在key
            foreach (var obj in summaryList)
            {
                if (obj == null) continue;
                if (!keyValuePairs.ContainsKey(obj.AssetId))
                {
                    keyValuePairs.Add(obj.AssetId, 0.0M);
                }
            }

            var result = summaryList.Select(s => (s.AssetName, Math.Round(s.Electricity + keyValuePairs[s.AssetId], 2))).OrderBy(a => a.Item2).ToList();

            return result;
        }

        /// <summary>
        /// 获取消耗电量top10数据
        /// </summary>
        /// <param name="startTime">需要查询的开始日期</param>
        /// <param name="endTime">需要查询的结束日期</param>
        /// <param name="dateType">
        /// <list type="bullet">
        ///     <item>0: 日</item>
        ///     <item>1: 月</item>
        ///     <item>2: 年</item>
        ///     <item>3: 自定义</item>
        /// </list>
        /// </param>
        /// <returns>List</returns>

        public async Task<List<(string AssetName, decimal Value)>> GetTopTenRealTimeData(DateTime startTime, DateTime endTime, int dateType)
        {
            using var sqlClient = _provider.GetRequiredService<ISqlSugarClient>();
            var lineChart = new LineChartModel();

            var searchStartDate = DateTime.MinValue;
            var searchEndDate = DateTime.MinValue;

            if (dateType == 0)
            {
                searchStartDate = startTime.Date;
                searchEndDate = startTime.Date.AddDays(1);
            }
            else if (dateType == 1)
            {
                searchStartDate = new DateTime(startTime.Year, startTime.Month, 1);
                searchEndDate = new DateTime(startTime.Year, startTime.Month, DateTime.DaysInMonth(startTime.Year, startTime.Month)).AddDays(1);
            }
            else if (dateType == 2)
            {
                searchStartDate = new DateTime(startTime.Year, 1, 1);
                searchEndDate = new DateTime(startTime.Year + 1, 1, 1);
            }
            else
            {
                searchStartDate = startTime.Date;
                searchEndDate = endTime.Date.AddDays(1);
            }

            var assets = await sqlClient.Queryable<AssetInfo>().Where(a => a.AssetLevel == AssetLevel.Device).Select(a => new { a.Id, a.AssetName }).ToListAsync();

            var data = await GetEnergyDataByHour(assets.Select(a => a.Id).ToList(), searchStartDate, searchEndDate);

            if (data == null || !data.Any())
            {
                return new List<(string AssetName, decimal Value)>();
            }

            var summaryList = data.GroupBy(a => a.AssetId).Select(a => new
            {
                AssetId = a.Key,
                Electricity = Math.Round(a.Sum(p => p.Value), 2)
            }).OrderByDescending(a => a.Electricity).Take(10).ToList();


            var result = summaryList.Select(s => (assets.FirstOrDefault(a => a.Id == s.AssetId)?.AssetName ?? string.Empty, Math.Round(s.Electricity))).OrderBy(a => a.Item2).ToList();

            return result;
        }
        /// <summary>
        /// 查询指定时间区间内的电能数据
        /// </summary>
        /// <param name="assetIds">资产id列表</param>
        /// <param name="startTime">查询的开始时间</param>
        /// <param name="endTime">查询的开始时间</param>
        /// <param name="interval">查询时间间隔,当前可输入"1h","15m"</param>
        public async Task<List<EnergyIntervalSummaryModel>> GetEnergyData(List<int> assetIds, DateTime startTime, DateTime endTime, string interval, bool isSum = false)
        {
            var intervalSummaryList = new List<EnergyIntervalSummaryModel>();

            if (assetIds == null || !assetIds.Any())
            {
                return intervalSummaryList;
            }

            IConfiguration configuration = _provider.GetRequiredService<IConfiguration>();
            var session = configuration.GetSection(InfluxDBConfig.Influxdb);
            InfluxDBConfig influxDbConfig = null;
            if (session != null && session.Exists())
            {
                influxDbConfig = session.Get<InfluxDBConfig>();
            }

            if (influxDbConfig == null)
            {
                _logger.LogError("EnergyManagementServer init: Not found Influxdb config.");
                return intervalSummaryList;
            }
            try
            {
                using var sqlClient = _provider.GetRequiredService<ISqlSugarClient>();
                var dashboardConfig = await sqlClient.Queryable<AssetDashboardConfig>().Where(d => d.ConfigName == "ENERGY_INTERVAL_SUMMARY").FirstAsync();
                var sql = dashboardConfig.Sql;
                if (dashboardConfig != null)
                {

                    var assetIdQuery = new StringBuilder();
                    foreach (var id in assetIds)
                    {
                        if (assetIdQuery.Length > 0)
                        {
                            assetIdQuery.Append(" or ");
                        }
                        assetIdQuery.Append("r[\"assetid\"] == \"");
                        assetIdQuery.Append(id);
                        assetIdQuery.Append('\"');
                    }
                    string sql0 = sql
                         .Replace("[[DBName]]", influxDbConfig.Bucket)
                         .Replace("[[TimeInterval]]", interval)
                         .Replace("[[TableName]]", "archivedatarealtime")
                         .Replace("[[AssetIdList]]", assetIdQuery.ToString())
                         .Replace("[[StartDate]]", startTime.AddMinutes(-15).GetTimestampForSec().ToString())
                         .Replace("[[EndDate]]", endTime.Date.GetTimestampForSec().ToString());
                    using var influxClient = new InfluxDBClient(influxDbConfig.Url, influxDbConfig.UserName, influxDbConfig.Password);

                    var queryApi = influxClient.GetQueryApi();
                    var result = await queryApi.QueryAsync(sql0, influxDbConfig.OrgId ?? influxDbConfig.OrgName);
                    var localTimeZone = TimeZoneInfo.Local;
                    if (result == null || !result.Any())
                    {
                        return intervalSummaryList;
                    }
                    EnergyIntervalSummaryModel summaryModel;
                    foreach (var table in result)
                    {
                        for (var i = 0; i < table.Records.Count; i++)
                        {
                            var assetid = table.Records[i].GetValueByKey("assetid")?.ToString();
                            summaryModel = new EnergyIntervalSummaryModel();

                            var currentDate = TimeZoneInfo.ConvertTimeFromUtc(table.Records[i].GetTimeInDateTime().Value, localTimeZone);
                            var currentValue = Convert.ToDecimal(table.Records[i].GetValue());

                            if (currentValue < .0M)
                            {
                                currentValue = .0M;
                            }

                            summaryModel.AssetId = Convert.ToInt32(assetid);
                            summaryModel.Time = currentDate;
                            summaryModel.Value = currentValue;
                            intervalSummaryList.Add(summaryModel);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "EnergyManagementServer GetEnergyDataByHour Error.");
            }

            return intervalSummaryList;
        }
        /// <summary>
        /// 查询指定时间区间内的电量与电费消耗情况
        /// </summary>
        /// <param name="assetIds">资产id列表</param>
        /// <param name="startTime">查询的开始时间</param>
        /// <param name="endTime">查询的开始时间</param>
        /// <param name="interval">查询时间间隔,当前可输入"1h","15m"</param>
        private async Task<List<EnergyIntervalSummaryModel>> GetEnergyDataByInterval(List<int> assetIds, DateTime startTime, DateTime endTime, string interval, bool isSum = false)
        {
            var intervalSummaryList = new List<EnergyIntervalSummaryModel>();

            if (assetIds == null || !assetIds.Any())
            {
                return intervalSummaryList;
            }

            IConfiguration configuration = _provider.GetRequiredService<IConfiguration>();
            var session = configuration.GetSection(InfluxDBConfig.Influxdb);
            InfluxDBConfig influxDbConfig = null;
            if (session != null && session.Exists())
            {
                influxDbConfig = session.Get<InfluxDBConfig>();
            }

            if (influxDbConfig == null)
            {
                _logger.LogError("EnergyManagementServer init: Not found Influxdb config.");
                return intervalSummaryList;
            }

            try
            {
                using var sqlClient = _provider.GetRequiredService<ISqlSugarClient>();
                var dashboardConfig = await sqlClient.Queryable<AssetDashboardConfig>().Where(d => d.ConfigName == "ENERGY_INTERVAL_SUMMARY").FirstAsync();

                var sql = dashboardConfig.Sql;
                if (dashboardConfig != null)
                {

                    var assetIdQuery = new StringBuilder();
                    foreach (var id in assetIds)
                    {
                        if (assetIdQuery.Length > 0)
                        {
                            assetIdQuery.Append(" or ");
                        }
                        assetIdQuery.Append("r[\"assetid\"] == \"");
                        assetIdQuery.Append(id);
                        assetIdQuery.Append('\"');
                    }

                    string sql0 = sql
                         .Replace("[[DBName]]", influxDbConfig.Bucket)
                         .Replace("[[TimeInterval]]", interval)
                         .Replace("[[TableName]]", "archivedatarealtime")
                         .Replace("[[AssetIdList]]", assetIdQuery.ToString())
                         .Replace("[[StartDate]]", startTime.AddMinutes(-15).GetTimestampForSec().ToString())
                         .Replace("[[EndDate]]", endTime.Date.AddDays(1).GetTimestampForSec().ToString());


                    using var influxClient = new InfluxDBClient(influxDbConfig.Url, influxDbConfig.UserName, influxDbConfig.Password);

                    var queryApi = influxClient.GetQueryApi();
                    var result = await queryApi.QueryAsync(sql0, influxDbConfig.OrgId ?? influxDbConfig.OrgName);
                    var localTimeZone = TimeZoneInfo.Local;
                    Dictionary<DateTime, decimal> yearValues = new Dictionary<DateTime, decimal>();
                    // 查询一年的数据
                    var yearSummaryList = await _client.Queryable<EnergySummary>()
                        .Where(a => SqlFunc.ContainsArray(assetIds, a.AssetId))
                        .Where(a => a.ConsumptionDate >= new DateTime(DateTime.Now.Year, 1, 1) && a.ConsumptionDate <= DateTime.Now)
                        .ToListAsync();
                    var yearSummary = yearSummaryList.Select(a => new
                    {
                        Electricity = (a.PeakElectricity + a.FlatElectricity + a.ValleyElectricity)
                    });
                    //当前累计用电量
                    var sumElectricity = yearSummary.Sum(a => a.Electricity) / 1000.0M;

                    if (result == null || !result.Any())
                    {
                        return intervalSummaryList;
                    }
                    EnergyIntervalSummaryModel summaryModel;
                    var electricityList = await _client.Queryable<ElectricityConfig>()
                        .Where(a => a.electrovalenceType == "受电" || a.electrovalenceType == ElectrovalenceType.receivePower.ToString()).ToArrayAsync();
                    var history = await _client.Queryable<ElectricityConfigHistory>()
                        .Where(a => a.electrovalenceType == "受电" || a.electrovalenceType == ElectrovalenceType.receivePower.ToString()).ToArrayAsync();
                    //var assetIdList = assetIdFilter.ToString().Split(',');
                    foreach (var table in result)
                    {
                        for (var i = 0; i < table.Records.Count - 1; i++)
                        {
                            var assetid = table.Records[i].GetValueByKey("assetid")?.ToString();
                            //if (!assetIdList.Contains(assetid))
                            //{
                            //    continue;
                            //}
                            summaryModel = new EnergyIntervalSummaryModel();

                            var currentDate = TimeZoneInfo.ConvertTimeFromUtc(table.Records[i].GetTimeInDateTime().Value, localTimeZone);
                            var currentValue = (Convert.ToDecimal(table.Records[i + 1].GetValue()) - Convert.ToDecimal(table.Records[i].GetValue())) / 1000.0M;

                            if (currentValue < .0M)
                            {
                                currentValue = .0M;
                            }

                            summaryModel.AssetId = Convert.ToInt32(assetid);
                            summaryModel.Time = currentDate;
                            summaryModel.Value = currentValue;
                            var price = await _electricityCharge.GetElectricityPrice(currentDate, sumElectricity, electricityList, history);
                            summaryModel.Cost = currentValue * price * (interval == "1h" ? 1.0M : .25M);

                            intervalSummaryList.Add(summaryModel);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "EnergyManagementServer GetEnergyDataByHour Error.");
            }

            return intervalSummaryList;
        }
    }
}
