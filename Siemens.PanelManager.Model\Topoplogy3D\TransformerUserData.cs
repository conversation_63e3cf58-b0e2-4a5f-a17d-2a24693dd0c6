﻿using Newtonsoft.Json;
using Siemens.PanelManager.Model.Topology3D;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Topoplogy3D
{
    public class TransformerUserData : UserData
    {
        [JsonProperty(propertyName: "uuid", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public string Id { get; set; } = Guid.NewGuid().ToString();
        [JsonProperty(propertyName: "assetId")]
        public int AssetId { get; set; }
        [JsonProperty(propertyName: "panelType")]
        public string PanelType { get; set; } = string.Empty;
        [JsonProperty(propertyName: "busbarStructure")]
        public string? BusbarStructure { get; set; }
        [JsonProperty(propertyName: "circuitList")]
        public List<CircuitItem> CircuitList { get; set; } = new List<CircuitItem>();
        [JsonProperty(propertyName: "control")]
        public TransformerControl Control { get; set; } = new TransformerControl();
    }

    public class TransformerControl
    {
        [JsonProperty(propertyName: "selectable")]
        public bool Selectable { get; set; } = true;
        [JsonProperty(propertyName: "draggable")]
        public bool Draggable { get; set; } = true;
        [JsonProperty(propertyName: "transparent")]
        public bool Transparent { get; set; } = false;
        [JsonProperty(propertyName: "showPanel")]
        public bool ShowPanel { get; set; } = true;
        [JsonProperty(propertyName: "groupable")]
        public bool Groupable { get; set; } = true;
        [JsonProperty(propertyName: "showDetail")]
        public bool ShowDetail { get; set; } = true;
    }
}
