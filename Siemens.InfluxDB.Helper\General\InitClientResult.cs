﻿using InfluxDB.Client;
using InfluxDB.Client.Api.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.InfluxDB.Helper.General
{
    internal class InitClientResult
    {
        public InitClientResult(IInfluxDBClient client, Organization organization, Bucket bucket)
        {
            Client = client;
            Organization = organization;
            Bucket = bucket;
        }
        public IInfluxDBClient Client { get; set; }
        public Organization Organization { get; set;}
        public Bucket Bucket { get; set;}
    }
}
