﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Quartz.Impl.AdoJobStore.Common;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.Excel;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.Common.Model;
using Siemens.PanelManager.Common.Mqtt;
using Siemens.PanelManager.Job.Mqtt;
using Siemens.PanelManager.Job.UploadExcel._3D;
using Siemens.PanelManager.Job.UploadExcel.Diagram;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.Model.Database.Topology;
using Siemens.PanelManager.Model.Job;
using Siemens.PanelManager.Server.Asset;
using Siemens.PanelManager.Server.Topology;
using SqlSugar;
using System.Text;
using System.Text.RegularExpressions;
using TouchSocket.Core;

namespace Siemens.PanelManager.Job.UploadExcel
{
    public class UploadExcelJob : JobBase
    {
        private const string FileVersion = "1.0.20241030";
        private const string AssetByName = "这个地方给一个默认值";
        private ILogger<UploadExcelJob> _logger;
        private ISqlSugarClient _client;
        private SiemensExcelHelper _excelHelper;
        private SiemensCache _cache;
        private AssetExtendServer _extendServer;
        private IServiceProvider _provider;

        public UploadExcelJob(ILogger<UploadExcelJob> logger,
            ISqlSugarClient client,
            SiemensExcelHelper excelHelper,
            SiemensCache cache,
            IServiceProvider provider,
            AssetExtendServer extendServer)
        {
            _provider = provider;
            _logger = logger;
            _client = client;
            _excelHelper = excelHelper;
            _cache = cache;
            _extendServer = extendServer;
        }

        public override string Name => "UploadExcelJob";

        public override async Task Execute()
        {
            if (!ContextData.ContainsKey("User")
                || !ContextData.ContainsKey("Files")
                || !ContextData.ContainsKey("JobId"))
            {
                _logger.LogInformation($"{Name} 启动失败, 参数缺失");
                return;
            }

            var jobId = ContextData["JobId"];
            var jobInfo = _cache.Get<JobInfo>($"JobId:{jobId}");

            if (jobInfo == null)
            {
                _logger.LogInformation($"{Name}:{jobId} 已过期");
                return;
            }

            jobInfo.Result = new JobResultModel();
            jobInfo.JobStatus = 10;
            _cache.Set($"JobId:{jobId}", jobInfo, TimeSpan.FromMinutes(5));


            var filesStr = ContextData["Files"];
            if (string.IsNullOrEmpty(filesStr))
            {
                jobInfo.JobStatus = 99;
                jobInfo.Result.ErrorInfo.Add("Common_Param");
                _cache.Set($"JobId:{jobId}", jobInfo, TimeSpan.FromMinutes(5));
                return;
            }

            var fileList = new List<FileInfo>();

            var files = filesStr.Split(',');
            foreach (var f in files)
            {
                if (string.IsNullOrEmpty(f)) continue;
                var file = new FileInfo(f);
                if (file.Exists && Regex.IsMatch(file.Name, "\\.xlsx$"))
                {
                    fileList.Add(file);
                }
            }

            if (fileList.Count <= 0)
            {
                jobInfo.JobStatus = 99;
                jobInfo.Result.ErrorInfo.Add("Asset_FileNotExists");
                _cache.Set($"JobId:{jobId}", jobInfo, TimeSpan.FromMinutes(5));
                return;
            }

            var user = ContextData["User"];


            try
            {
                #region 初始化参数
                var panelDataList = new List<PanelModel>();
                var transformerDataList = new List<TransformerModel>();
                var circuitModelList = new List<CircuitModel>();

                var str = "DeviceType".ToUpper();
                var deviceTypeOriginalList = await _client.Queryable<SystemStaticModel>()
                    .Where(s => s.Type == str)
                    .WithCache($"SystemStaticModel:{str}", (int)TimeSpan.FromHours(1).TotalSeconds)
                    .ToArrayAsync();

                var deviceTypeList = deviceTypeOriginalList.Select(s => new DevciceTypeOrModel(s)).ToArray();

                str = "DeviceModel".ToUpper();
                var deviceModelOriginalList = await _client.Queryable<SystemStaticModel>()
                    .Where(s => s.Type == str)
                    .WithCache($"SystemStaticModel:{str}", (int)TimeSpan.FromHours(1).TotalSeconds)
                    .ToArrayAsync();

                var deviceModelList = deviceModelOriginalList.Select(s => new DevciceTypeOrModel(s)).ToArray();

                str = "UseScene".ToUpper();
                var useSceneList = await _client.Queryable<SystemStaticModel>()
                    .Where(s => s.Type == str)
                    .WithCache($"SystemStaticModel:{str}", (int)TimeSpan.FromHours(1).TotalSeconds)
                    .ToArrayAsync();

                str = "PanelType".ToUpper();
                var panelTypeList = await _client.Queryable<SystemStaticModel>()
                    .Where(s => s.Type == str)
                    .WithCache($"SystemStaticModel:{str}", (int)TimeSpan.FromHours(1).TotalSeconds)
                    .ToArrayAsync();

                str = "TransformerType".ToUpper();
                var transformerTypeList = await _client.Queryable<SystemStaticModel>()
                    .Where(s => s.Type == str)
                    .WithCache($"SystemStaticModel:{str}", (int)TimeSpan.FromHours(1).TotalSeconds)
                    .ToArrayAsync();

                str = "CircuitType".ToUpper();
                var circuitTypeList = await _client.Queryable<SystemStaticModel>()
                    .Where(s => s.Type == str)
                    .WithCache($"SystemStaticModel:{str}", (int)TimeSpan.FromHours(1).TotalSeconds)
                    .ToArrayAsync();

                str = "PanelModel".ToUpper();
                var panelModelList = await _client.Queryable<SystemStaticModel>()
                    .Where(s => s.Type == str)
                    .WithCache($"SystemStaticModel:{str}", (int)TimeSpan.FromHours(1).TotalSeconds)
                    .ToArrayAsync();

                str = "MeterType".ToUpper();
                var meterTypeList = await _client.Queryable<SystemStaticModel>()
                    .Where(s => s.Type == str)
                    .WithCache($"SystemStaticModel:{str}", (int)TimeSpan.FromHours(1).TotalSeconds)
                    .ToArrayAsync();

                var factoryList = new List<AssetInfo>();
                var substationList = new List<(AssetInfo, string)>();
                var panelList = new List<(AssetInfo, string, PanelModel)>();
                var transformerList = new List<(AssetInfo, string, TransformerModel)>();
                var circuitList = new List<(AssetInfo, string, CircuitModel)>();
                var deviceList = new List<(AssetInfo, string)>();
                #endregion

                #region Load Excel

                int drawingIndex = 0;
                var codes = await _client.Queryable<TopologyInfo>().Where(t => t.Code.Contains("-SLD-") || t.Code.Contains("-3D-")).Select(t => t.Code).ToArrayAsync();
                if (codes != null && codes.Length > 0)
                {
                    int maxIndex = 0;
                    var ruleStr = "-(SLD|3D)-([\\d]{3})$";
                    foreach (var c in codes)
                    {
                        var m = Regex.Match(c, ruleStr);
                        if (m.Success)
                        {
                            var index = int.Parse(m.Groups[2].Value);
                            if (index > maxIndex)
                            {
                                maxIndex = index;
                            }
                        }
                    }

                    drawingIndex = maxIndex + 1;
                }
                int panelIndex = 0;
                int circuitIndex = 0;
                int transformerIndex = 0;
                foreach (var file in fileList)
                {
                    var areaNumber = 0;
                    var substationNumber = 0;
                    var panelNumber = 0;
                    var transformerNumber = 0;
                    var circuitNumber = 0;
                    var deviceNumber = 0;

                    using (var fs = file.OpenRead())
                    {
                        using (var ms = new MemoryStream())
                        {
                            await fs.CopyToAsync(ms);
                            await _excelHelper.QueryAsync(ms, (r) =>
                            {
                                try
                                {
                                    switch (r.SheetName)
                                    {
                                        case "Area":
                                        case "厂区":
                                            {
                                                var assetLevel = AssetLevel.Area;

                                                foreach (var item in r.Datas)
                                                {
                                                    areaNumber++;
                                                    var error = new List<string>();
                                                    var assetInfo = new AssetInfo();
                                                    assetInfo.AssetLevel = assetLevel;
                                                    assetInfo.CreatedBy = user ?? string.Empty;
                                                    assetInfo.CreatedTime = DateTime.Now;
                                                    assetInfo.UpdatedBy = user ?? string.Empty;
                                                    assetInfo.UpdatedTime = DateTime.Now;
                                                    object? itemValue = null;

                                                    if (item.TryGetValue("A", out itemValue)
                                                        && itemValue != null 
                                                        && itemValue is string itemStr
                                                        && !string.IsNullOrWhiteSpace(itemStr))
                                                    {
                                                        var assetName = itemValue.ToString();
                                                        assetInfo.AssetName = assetName ?? string.Empty;

                                                        if (string.IsNullOrEmpty(assetInfo.AssetName)
                                                            || assetInfo.AssetName.MustCharAndNotOnlyNumberOrSymbol(30))
                                                        {
                                                            error.Add($"厂区sheet页中第{areaNumber + 1}行A列中的客户名称太长或者不符合要求(客户名称长度介于0到30之间)");
                                                        }

                                                        if (factoryList.Any(a => a != assetInfo && a.AssetName == assetInfo.AssetName))
                                                        {
                                                            error.Add($"厂区sheet页中第{areaNumber + 1}行A列中的客户名称已存在，请修改客户名称");
                                                        }

                                                    }
                                                    else
                                                    {
                                                        return Task.FromResult(0);
                                                    }

                                                    if (item.TryGetValue("B", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var location = itemValue.ToString();
                                                        assetInfo.Location = location;

                                                        if (assetInfo.Location.CanNullAndNotOnlySymbol(100))
                                                        {
                                                            error.Add($"厂区sheet页中第{areaNumber + 1}行B列中的项目地址太长或者不符合要求(项目地址长度介于0到100之间)");
                                                        }
                                                    }

                                                    if (item.TryGetValue("C", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var principal = itemValue.ToString();
                                                        assetInfo.Principal = principal;

                                                        if (assetInfo.Principal.CanNullAndNotOnlyNumberOrSymbol(30, 2) || Regex.IsMatch(assetInfo.Principal ?? string.Empty, "[\\$|#|!|@|\\^|\\*|\\(|\\)|\\||%|\\\\|/|<|>|;|\\[|\\]|\\{|\\}|\\?]+"))
                                                        {
                                                            error.Add($"厂区sheet页中第{areaNumber + 1}行C列中的负责人名太长或者不符合要求(负责人名称长度介于2到30之间)");
                                                        }
                                                    }

                                                    if (item.TryGetValue("D", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var tel = itemValue.ToString();
                                                        assetInfo.Telephone = tel;

                                                        if (!string.IsNullOrEmpty(assetInfo.Telephone) && !Regex.IsMatch(assetInfo.Telephone, "^[\\d]{7,11}$"))
                                                        {
                                                            error.Add($"厂区sheet页中第{areaNumber + 1}行D列中的电话太长或者含有特殊字符(电话号码不规范)");
                                                        }
                                                    }

                                                    if (item.TryGetValue("F", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var desc = itemValue.ToString();
                                                        assetInfo.Description = desc;

                                                        if (assetInfo.Description.CanNullAndNotOnlyNumberOrSymbol(100))
                                                        {
                                                            error.Add($"厂区sheet页中第{areaNumber + 1}行F列中的资产概述太长或者不符合要求(资产概述长度介于0到100之间)");
                                                        }
                                                    }

                                                    if (error.Count == 0)
                                                    {
                                                        factoryList.Add(assetInfo);
                                                    }
                                                    else
                                                    {
                                                        jobInfo.Result.ErrorInfo.AddRange(error);
                                                    }
                                                }
                                            }
                                            break;
                                        case "Substation":
                                        case "配电房":
                                            {
                                                var assetLevel = AssetLevel.Substation;
                                                foreach (var item in r.Datas)
                                                {
                                                    substationNumber++;
                                                    var error = new List<string>();
                                                    var assetInfo = new AssetInfo();
                                                    assetInfo.AssetLevel = assetLevel;
                                                    assetInfo.CreatedBy = user ?? string.Empty;
                                                    assetInfo.CreatedTime = DateTime.Now;
                                                    assetInfo.UpdatedBy = user ?? string.Empty;
                                                    assetInfo.UpdatedTime = DateTime.Now;
                                                    object? itemValue = null;
                                                    string parentName = string.Empty;

                                                    if (item.TryGetValue("A", out itemValue) && itemValue != null
                                                        && itemValue is string itemStr
                                                        && !string.IsNullOrWhiteSpace(itemStr))
                                                    {
                                                        var assetName = itemValue.ToString();
                                                        assetInfo.AssetName = assetName ?? string.Empty;

                                                        if (string.IsNullOrEmpty(assetInfo.AssetName)
                                                           || assetInfo.AssetName.MustCharAndNotOnlyNumberOrSymbol(30))
                                                        {
                                                            error.Add($"配电房sheet页中第{substationNumber + 1}行A列中的资产名称太长或者不符合要求(资产名称长度介于0到30之间)");
                                                        }

                                                        if (factoryList.Any(a => a != assetInfo && a.AssetName == assetInfo.AssetName))
                                                        {
                                                            error.Add($"配电房sheet页中第{substationNumber + 1}行A列中的资产名称已存在，请修改资产名称");
                                                        }

                                                        if (substationList.Select(p => p.Item1).Any(a => a != assetInfo && a.AssetName == assetInfo.AssetName))
                                                        {
                                                            error.Add($"配电房sheet页中第{substationNumber + 1}行A列中的资产名称已存在，请修改资产名称");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        return Task.FromResult(0);
                                                    }

                                                    if (item.TryGetValue("F", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        parentName = itemValue.ToString() ?? string.Empty;
                                                    }
                                                    else
                                                    {
                                                        parentName = AssetByName;
                                                        error.Add($"配电房sheet页中第{substationNumber + 1}行F列中的所属资产层级不能为空");
                                                    }

                                                    
                                                    if (item.TryGetValue("C", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var location = itemValue.ToString();
                                                        assetInfo.Location = location;

                                                        if (assetInfo.Location.CanNullAndNotOnlySymbol(100))
                                                        {
                                                            error.Add($"配电房sheet页中第{substationNumber + 1}行C列中的资产位置太长或者不符合要求(资产位置长度介于0到100之间)");
                                                        }
                                                    }

                                                    if (item.TryGetValue("D", out itemValue) && itemValue != null)
                                                    {
                                                        if (itemValue is string installDateStr)
                                                        {
                                                            if (DateTime.TryParse(installDateStr, out DateTime installDate))
                                                            {
                                                                assetInfo.InstallDate = installDate;
                                                            }
                                                            else
                                                            {
                                                                assetInfo.InstallDate = null;
                                                            }
                                                        }
                                                        else if (itemValue is DateTime installDate2)
                                                        {
                                                            assetInfo.InstallDate = installDate2;
                                                        }
                                                        else
                                                        {
                                                            error.Add($"配电房sheet页中第{substationNumber + 1}行D列中的建造时间格式不正确");
                                                        }
                                                    }

                                                    if (item.TryGetValue("E", out itemValue))
                                                    {
                                                        var desc = itemValue?.ToString();
                                                        assetInfo.Description = desc;

                                                        if (assetInfo.Description.CanNullAndNotOnlyNumberOrSymbol(100))
                                                        {
                                                            error.Add($"配电房sheet页中第{substationNumber + 1}行E列中的资产概述太长或者不符合要求(资产概述长度介于0到100之间)");
                                                        }
                                                    }

                                                    // 添加是否启用mqtt
                                                    if (item.TryGetValue("G", out itemValue))
                                                    {
                                                        assetInfo.EnableMqtt = Convert.ToInt32(itemValue) == 1 ? true : false;
                                                    }

                                                    if (error.Count > 0)
                                                    {
                                                        jobInfo.Result.ErrorInfo.AddRange(error);
                                                    }
                                                    else
                                                    {
                                                        substationList.Add((assetInfo, parentName));
                                                    }
                                                }
                                            }
                                            break;
                                        case "Panel":
                                        case "配电柜":
                                            {
                                                var assetLevel = AssetLevel.Panel;
                                                foreach (var item in r.Datas)
                                                {
                                                    panelNumber++;

                                                    var error = new List<string>();
                                                    var assetInfo = new AssetInfo();
                                                    assetInfo.AssetLevel = assetLevel;
                                                    assetInfo.CreatedBy = user ?? string.Empty;
                                                    assetInfo.CreatedTime = DateTime.Now;
                                                    assetInfo.UpdatedBy = user ?? string.Empty;
                                                    assetInfo.UpdatedTime = DateTime.Now;
                                                    object? itemValue = null;
                                                    string parentName = string.Empty;

                                                    var busBar = string.Empty;
                                                    var busBarIds = new string[0];
                                                    if (item.TryGetValue("A", out itemValue) && itemValue != null
                                                        && itemValue is string itemStr
                                                        && !string.IsNullOrWhiteSpace(itemStr))
                                                    {
                                                        var assetName = itemValue.ToString() ?? string.Empty;
                                                        assetInfo.AssetName = assetName;

                                                        if (string.IsNullOrEmpty(assetInfo.AssetName)
                                                          || assetInfo.AssetName.MustCharAndNotOnlyNumberOrSymbol(30))
                                                        {
                                                            error.Add($"配电柜sheet页中第{panelNumber + 1}行A列中的资产名称太长或者不符合要求(资产名称长度介于0到30之间)");
                                                        }

                                                        if (factoryList.Any(a => a != assetInfo && a.AssetName == assetInfo.AssetName))
                                                        {
                                                            error.Add($"配电柜sheet页中第{panelNumber + 1}行A列中的资产名称已存在，请修改资产名称");
                                                        }

                                                        if (substationList.Select(p => p.Item1).Any(a => a != assetInfo && a.AssetName == assetInfo.AssetName))
                                                        {
                                                            error.Add($"配电柜sheet页中第{panelNumber + 1}行A列中的资产名称已存在，请修改资产名称");
                                                        }

                                                        if (panelList.Select(p => p.Item1).Any(a => a != assetInfo && a.AssetName == assetInfo.AssetName))
                                                        {
                                                            error.Add($"配电柜sheet页中第{panelNumber + 1}行A列中的资产名称已存在，请修改资产名称");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        return Task.FromResult(0);
                                                    }

                                                    if (item.TryGetValue("M", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        busBar = itemValue.ToString();
                                                        if (!string.IsNullOrEmpty(busBar))
                                                        {
                                                            var busBars = busBar.Split(',');
                                                            busBarIds = busBars.Select(b => RomanNumberHelper.GetNo(b).ToString()).ToArray();
                                                        }
                                                    }
                                                    var index = panelIndex++;
                                                    if (busBarIds.Length > 1)
                                                    {
                                                        index += 900;
                                                    }

                                                    var panelModel = new PanelModel(assetInfo, index);
                                                    panelModel.BusBarStr = busBar ?? string.Empty;
                                                    panelModel.AssetInfo.BusBarId = string.Join(",", busBarIds);
                                                    if (item.TryGetValue("I", out itemValue) && itemValue != null)
                                                    {
                                                        parentName = itemValue.ToString() ?? string.Empty;
                                                    }
                                                    else
                                                    {
                                                        parentName = AssetByName;
                                                        error.Add($"配电柜sheet页中第{panelNumber + 1}行I列中所属资产层级不能为空");
                                                    }

                                                    if (item.TryGetValue("C", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var assetNumber = itemValue.ToString();
                                                        assetInfo.AssetNumber = assetNumber ?? string.Empty;

                                                        if (panelList.Select(p => p.Item1).Any(a => a != assetInfo && a.AssetNumber == assetInfo.AssetNumber))
                                                        {
                                                            error.Add($"配电柜sheet页中第{panelNumber + 1}行C列中的开关柜编号已存在，请修改开关柜编号");
                                                        }
                                                    }

                                                    if (item.TryGetValue("D", out itemValue)
                                                        && itemValue is string panelModelStr)
                                                    {
                                                        var panelType = panelTypeList.FirstOrDefault(p => p.Name == panelModelStr);
                                                        if (panelType != null)
                                                        {
                                                            assetInfo.AssetType = panelType.Code;
                                                        }
                                                    }

                                                    if (item.TryGetValue("E", out itemValue)
                                                        && itemValue is string model)
                                                    {
                                                        var modelItem = panelModelList.FirstOrDefault(p => p.Name == model);
                                                        if (modelItem != null)
                                                        {
                                                            assetInfo.AssetModel = modelItem.Code;
                                                        }
                                                    }

                                                    if (item.TryGetValue("F", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var assetMaker = itemValue.ToString();
                                                        assetInfo.AssetMaker = assetMaker;

                                                        if (assetInfo.AssetMaker.CanNullAndNotOnlyNumberOrSymbol(30))
                                                        {
                                                            error.Add($"配电柜sheet页中第{panelNumber + 1}行F列中的制造商太长或者不符合要求(制造商长度介于0到30之间)");
                                                        }
                                                    }

                                                    if (item.TryGetValue("G", out itemValue))
                                                    {
                                                        if (itemValue is string installDateStr)
                                                        {
                                                            if (DateTime.TryParse(installDateStr, out DateTime installDate))
                                                            {
                                                                assetInfo.InstallDate = installDate;
                                                            }
                                                            else
                                                            {
                                                                assetInfo.InstallDate = null;
                                                            }
                                                        }
                                                        else if (itemValue is DateTime installDate2)
                                                        {
                                                            assetInfo.InstallDate = installDate2;
                                                        }
                                                        else
                                                        {
                                                            if (itemValue != null && !string.IsNullOrEmpty(itemValue.ToString()))
                                                            {
                                                                error.Add($"配电柜sheet页中第{panelNumber + 1}行G列中的安装时间格式不正确");
                                                            }
                                                        }
                                                    }

                                                    if (item.TryGetValue("H", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var desc = itemValue.ToString();
                                                        assetInfo.Description = desc;

                                                        if (assetInfo.Description.CanNullAndNotOnlyNumberOrSymbol(100))
                                                        {
                                                            error.Add($"配电柜sheet页中第{panelNumber + 1}行H列中的资产概述太长或者不符合要求(资产概述长度介于0到100之间)");
                                                        }
                                                    }

                                                    if (item.TryGetValue("K", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var heightStr = itemValue.ToString();
                                                        if (int.TryParse(heightStr, out int height))
                                                        {
                                                            panelModel.Height = height;
                                                            assetInfo.Height = heightStr;
                                                        }
                                                    }

                                                    if (item.TryGetValue("J", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var widthStr = itemValue.ToString();
                                                        if (int.TryParse(widthStr, out int width))
                                                        {
                                                            panelModel.Width = width;
                                                            assetInfo.Width = widthStr;
                                                        }
                                                    }

                                                    if (item.TryGetValue("L", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var rowNoStr = itemValue.ToString();
                                                        var match = Regex.Match(rowNoStr ?? string.Empty, "^([\\d]+)-([\\d]+)$");

                                                        var entity = panelList.Select(p => p.Item1).Where(p => p.RowNo == rowNoStr).FirstOrDefault();

                                                        if (entity != null)
                                                        {
                                                            error.Add($"配电柜sheet页中第{panelNumber + 1}行L列排列号出现重复，请修改排列号");
                                                        }

                                                        if (match.Success)
                                                        {
                                                            panelModel.LineNo = int.Parse(match.Groups[1].Value);
                                                            panelModel.RowNo = int.Parse(match.Groups[2].Value);
                                                            assetInfo.RowNo = rowNoStr;
                                                        }
                                                        else
                                                        {
                                                            panelModel.LineNo = 1;
                                                            panelModel.RowNo = panelIndex;
                                                        }


                                                        //获取所有得排序号
                                                        var allRowList = panelList.Where(p => !string.IsNullOrEmpty(p.Item1?.RowNo)).Select(p => new
                                                        {
                                                            ParentName = p.Item2,
                                                            RowNo = p.Item1.RowNo?.Split('-')[0]
                                                        }).Distinct().ToList();

                                                        // 根据排序号获取分组
                                                        var grupRow = allRowList.GroupBy(p => p.RowNo).Select(p => new
                                                        {
                                                            RowNo = p.Key,
                                                            Num = p.Count()
                                                        }).FirstOrDefault(p => p.RowNo == rowNoStr!.Split('-')[0]);

                                                        if (grupRow != null && Convert.ToInt32(grupRow.Num) > 1)
                                                        {
                                                            error.Add($"配电柜sheet页中第{panelNumber + 1}行L列排列号同时在两个配电房出现，请修改排列号");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        panelModel.LineNo = 1;
                                                        panelModel.RowNo = panelIndex;
                                                    }

                                                    // 母线验证
                                                    //if (item.TryGetValue("M", out itemValue) && itemValue == null)
                                                    //{
                                                    //    jobInfo.Result.ErrorInfo.Add($"配电柜sheet页中第{serialNumber + 1}行M列中缺失母线");
                                                    //}

                                                    // 添加是否启用mqtt
                                                    if (item.TryGetValue("N", out itemValue))
                                                    {
                                                        assetInfo.EnableMqtt = Convert.ToInt32(itemValue) == 1 ? true : false;
                                                    }

                                                    if (item.TryGetValue("O", out itemValue) && itemValue != null)
                                                    {
                                                        panelModel.BusbarStructure = itemValue.ToString();
                                                    }

                                                    if (error.Count > 0)
                                                    {
                                                        jobInfo.Result.ErrorInfo.AddRange(error);
                                                    }
                                                    else
                                                    {
                                                        panelList.Add((assetInfo, parentName, panelModel));
                                                    }
                                                }
                                            }
                                            break;
                                        case "Transformer":
                                        case "变压器":
                                            {
                                                var assetLevel = AssetLevel.Transformer;
                                                foreach (var item in r.Datas)
                                                {
                                                    transformerNumber++;

                                                    var assetInfo = new AssetInfo();
                                                    assetInfo.AssetLevel = assetLevel;
                                                    assetInfo.CreatedBy = user ?? string.Empty;
                                                    assetInfo.CreatedTime = DateTime.Now;
                                                    assetInfo.UpdatedBy = user ?? string.Empty;
                                                    assetInfo.UpdatedTime = DateTime.Now;
                                                    object? itemValue = null;
                                                    string parentName = string.Empty;
                                                    string relationPanelName = string.Empty;
                                                    var busBar = string.Empty;
                                                    var busBarIds = new string[0];
                                                    if (item.TryGetValue("A", out itemValue) && itemValue != null
                                                        && itemValue is string itemStr
                                                        && !string.IsNullOrWhiteSpace(itemStr))
                                                    {
                                                        var assetName = itemValue.ToString() ?? string.Empty;
                                                        assetInfo.AssetName = assetName;

                                                        if (string.IsNullOrEmpty(assetInfo.AssetName)
                                                          || assetInfo.AssetName.MustCharAndNotOnlyNumberOrSymbol(30))
                                                        {
                                                            jobInfo.Result.ErrorInfo.Add($"变压器sheet页中第{transformerNumber + 1}行A列中的资产名称太长或者不符合要求(资产名称长度介于0到30之间)");
                                                        }

                                                        if (factoryList.Any(a => a != assetInfo && a.AssetName == assetInfo.AssetName))
                                                        {
                                                            jobInfo.Result.ErrorInfo.Add($"变压器sheet页中第{transformerNumber + 1}行A列中的资产名称已存在，请修改资产名称");
                                                        }

                                                        if (substationList.Select(p => p.Item1).Any(a => a != assetInfo && a.AssetName == assetInfo.AssetName))
                                                        {
                                                            jobInfo.Result.ErrorInfo.Add($"变压器sheet页中第{transformerNumber + 1}行A列中的资产名称已存在，请修改资产名称");
                                                        }

                                                        if (transformerList.Select(p => p.Item1).Any(a => a != assetInfo && a.AssetName == assetInfo.AssetName))
                                                        {
                                                            jobInfo.Result.ErrorInfo.Add($"变压器sheet页中第{transformerNumber + 1}行A列中的资产名称已存在，请修改资产名称");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        return Task.FromResult(0);
                                                    }

                                                    if (item.TryGetValue("N", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        busBar = itemValue.ToString();
                                                        if (!string.IsNullOrEmpty(busBar))
                                                        {
                                                            var busBars = busBar.Split(',');
                                                            busBarIds = busBars.Select(b => RomanNumberHelper.GetNo(b).ToString()).ToArray();
                                                        }
                                                    }
                                                    var index = transformerIndex++;
                                                    if (busBarIds.Length > 1)
                                                    {
                                                        index += 900;
                                                    }

                                                    var transformerModel = new TransformerModel(assetInfo, index);
                                                    transformerModel.BusBarStr = busBar ?? string.Empty;
                                                    transformerModel.AssetInfo.BusBarId = string.Join(",", busBarIds);
                                                    if (item.TryGetValue("I", out itemValue) && itemValue != null)
                                                    {
                                                        parentName = itemValue.ToString() ?? string.Empty;
                                                    }
                                                    else
                                                    {
                                                        parentName = AssetByName;
                                                        jobInfo.Result.ErrorInfo.Add($"变压器sheet页中第{transformerNumber + 1}行I列中所属资产层级不能为空");
                                                    }

                                                    transformerList.Add((assetInfo, parentName, transformerModel));

                                                    if (item.TryGetValue("C", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var assetNumber = itemValue.ToString();
                                                        assetInfo.AssetNumber = assetNumber ?? string.Empty;

                                                        if (transformerList.Select(p => p.Item1).Any(a => a != assetInfo && a.AssetNumber == assetInfo.AssetNumber))
                                                        {
                                                            jobInfo.Result.ErrorInfo.Add($"变压器sheet页中第{transformerNumber + 1}行C列中的变压器编号已存在，请修改变压器编号");
                                                        }
                                                    }



                                                    if (item.TryGetValue("D", out itemValue)
                                                        && itemValue is string panelModelStr)
                                                    {
                                                        var panelType = transformerTypeList.FirstOrDefault(p => p.Name == panelModelStr);
                                                        if (panelType != null)
                                                        {
                                                            assetInfo.AssetType = panelType.Code;
                                                        }
                                                    }

                                                    if (item.TryGetValue("E", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var assetModel = itemValue.ToString();
                                                        assetInfo.AssetModel = assetModel;

                                                        if (assetInfo.AssetModel.CanNullAndNotOnlyNumberOrSymbol(30))
                                                        {
                                                            jobInfo.Result.ErrorInfo.Add($"变压器sheet页中第{transformerNumber + 1}行E列中的变压器型号太长或者不符合要求(制造商长度介于0到30之间)");
                                                        }
                                                    }

                                                    if (item.TryGetValue("F", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var assetMaker = itemValue.ToString();
                                                        assetInfo.AssetMaker = assetMaker;

                                                        if (assetInfo.AssetMaker.CanNullAndNotOnlyNumberOrSymbol(30))
                                                        {
                                                            jobInfo.Result.ErrorInfo.Add($"变压器sheet页中第{transformerNumber + 1}行F列中的制造商太长或者不符合要求(制造商长度介于0到30之间)");
                                                        }
                                                    }

                                                    if (item.TryGetValue("G", out itemValue))
                                                    {
                                                        if (itemValue is string installDateStr
                                                            && DateTime.TryParse(installDateStr, out DateTime installDate))
                                                        {
                                                            assetInfo.InstallDate = installDate;
                                                        }
                                                        else if (itemValue is DateTime installDate2)
                                                        {
                                                            assetInfo.InstallDate = installDate2;
                                                        }
                                                        else
                                                        {
                                                            if (itemValue != null && !string.IsNullOrEmpty(itemValue.ToString()))
                                                            {
                                                                jobInfo.Result.ErrorInfo.Add($"变压器sheet页中第{transformerNumber + 1}行G列中的安装时间格式不正确");
                                                            }
                                                        }
                                                    }

                                                    if (item.TryGetValue("H", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var desc = itemValue.ToString();
                                                        assetInfo.Description = desc;

                                                        if (assetInfo.Description.CanNullAndNotOnlyNumberOrSymbol(100))
                                                        {
                                                            jobInfo.Result.ErrorInfo.Add($"变压器sheet页中第{transformerNumber + 1}行H列中的资产概述太长或者不符合要求(资产概述长度介于0到100之间)");
                                                        }
                                                    }

                                                    if (item.TryGetValue("L", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var heightStr = itemValue.ToString();
                                                        if (int.TryParse(heightStr, out int height))
                                                        {
                                                            transformerModel.Height = height;
                                                            assetInfo.Height = heightStr;
                                                        }
                                                    }
                                                    if (item.TryGetValue("J", out itemValue) && itemValue != null)
                                                    {
                                                        transformerModel.RelationPanelName = itemValue.ToString() ?? string.Empty;
                                                    }
                                                    else
                                                    {
                                                        transformerModel.RelationPanelName = AssetByName;
                                                        jobInfo.Result.ErrorInfo.Add($"变压器sheet页中第{transformerNumber + 1}行J列中连接配电柜不能为空");
                                                    }
                                                    if (item.TryGetValue("K", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var widthStr = itemValue.ToString();
                                                        if (int.TryParse(widthStr, out int width))
                                                        {
                                                            transformerModel.Width = width;
                                                            assetInfo.Width = widthStr;
                                                        }
                                                    }

                                                    if (item.TryGetValue("M", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var rowNoStr = itemValue.ToString();
                                                        var match = Regex.Match(rowNoStr ?? string.Empty, "^([\\d]+)-([\\d]+)$");

                                                        var entity = transformerList.Select(p => p.Item1).Where(p => p.RowNo == rowNoStr).FirstOrDefault();
                                                        var entity2 = panelList.Select(p => p.Item1).Where(p => p.RowNo == rowNoStr).FirstOrDefault();

                                                        if (entity != null|| entity2 !=null)
                                                        {
                                                            jobInfo.Result.ErrorInfo.Add($"变压器sheet页中第{transformerNumber + 1}行M列排列号出现重复，请修改排列号");
                                                        }

                                                        if (match.Success)
                                                        {
                                                            transformerModel.LineNo = int.Parse(match.Groups[1].Value);
                                                            transformerModel.RowNo = int.Parse(match.Groups[2].Value);
                                                            assetInfo.RowNo = rowNoStr;
                                                        }
                                                        else
                                                        {
                                                            transformerModel.LineNo = 1;
                                                            transformerModel.RowNo = panelIndex;
                                                        }

                                                        //获取所有得排序号
                                                        var allRowList = transformerList.Where(p => !string.IsNullOrEmpty(p.Item1.RowNo)).Select(p => new
                                                        {
                                                            ParentName = p.Item2,
                                                            RowNo = p.Item1.RowNo!.Split('-')[0]
                                                        }).Distinct().ToList();
                                                        var allRowList2 = panelList.Where(p => !string.IsNullOrEmpty(p.Item1.RowNo)).Select(p => new
                                                        {
                                                            ParentName = p.Item2,
                                                            RowNo = p.Item1.RowNo!.Split('-')[0]
                                                        }).Distinct().ToList();
                                                        allRowList.AddRange(allRowList2);
                                                        allRowList = allRowList.Distinct().ToList(); 
                                                        // 根据排序号获取分组
                                                        var grupRow = allRowList.GroupBy(p => p.RowNo).Select(p => new
                                                        {
                                                            RowNo = p.Key,
                                                            Num = p.Count()
                                                        }).FirstOrDefault(p => p.RowNo == rowNoStr!.Split('-')[0]);

                                                        if (grupRow != null && Convert.ToInt32(grupRow.Num) > 1)
                                                        {
                                                            jobInfo.Result.ErrorInfo.Add($"变压器sheet页中第{transformerNumber + 1}行M列排列号同时在两个配电房出现，请修改排列号");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        transformerModel.LineNo = 1;
                                                        transformerModel.RowNo = transformerIndex;
                                                    }

                                                    if (item.TryGetValue("O", out itemValue) && itemValue != null)
                                                    {
                                                        transformerModel.BusbarStructure = itemValue.ToString();
                                                    }
                                                }
                                            }
                                            break;
                                        case "Circuit":
                                        case "回路":
                                            {
                                                var assetLevel = AssetLevel.Circuit;
                                                foreach (var item in r.Datas)
                                                {
                                                    circuitNumber++;

                                                    var error = new List<string>();

                                                    var assetInfo = new AssetInfo();
                                                    assetInfo.AssetLevel = assetLevel;
                                                    assetInfo.CreatedBy = user ?? string.Empty;
                                                    assetInfo.CreatedTime = DateTime.Now;
                                                    assetInfo.UpdatedBy = user ?? string.Empty;
                                                    assetInfo.UpdatedTime = DateTime.Now;
                                                    object? itemValue = null;
                                                    string parentName = string.Empty;

                                                    var circuitModel = new CircuitModel(assetInfo, circuitIndex++);

                                                    if (item.TryGetValue("A", out itemValue)
                                                        && itemValue != null
                                                        && itemValue is string itemStr
                                                        && !string.IsNullOrWhiteSpace(itemStr))
                                                    {
                                                        var assetName = itemValue.ToString() ?? string.Empty;
                                                        assetInfo.AssetName = assetName;

                                                        if (string.IsNullOrEmpty(assetInfo.AssetName)
                                                         || assetInfo.AssetName.MustCharAndNotOnlyNumberOrSymbol(30))
                                                        {
                                                            error.Add($"回路sheet页中第{circuitNumber + 1}行A列中的资产名称太长或者不符合要求(资产名称长度介于0到30之间)");
                                                        }

                                                        if (factoryList.Any(a => a != assetInfo && a.AssetName == assetInfo.AssetName))
                                                        {
                                                            error.Add($"回路sheet页中第{circuitNumber + 1}行A列中的资产名称已存在，请修改资产名称");
                                                        }

                                                        if (substationList.Select(p => p.Item1).Any(a => a != assetInfo && a.AssetName == assetInfo.AssetName))
                                                        {
                                                            error.Add($"回路sheet页中第{circuitNumber + 1}行A列中的资产名称已存在，请修改资产名称");
                                                        }

                                                        if (panelList.Select(p => p.Item1).Any(a => a != assetInfo && a.AssetName == assetInfo.AssetName))
                                                        {
                                                            error.Add($"回路sheet页中第{circuitNumber + 1}行A列中的资产名称已存在，请修改资产名称");
                                                        }

                                                        if (circuitList.Select(p => p.Item1).Any(a => a != assetInfo && a.AssetName == assetInfo.AssetName))
                                                        {
                                                            error.Add($"回路sheet页中第{circuitNumber + 1}行A列中的资产名称已存在，请修改资产名称");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        return Task.FromResult(0);
                                                    }

                                                    if (item.TryGetValue("H", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        parentName = itemValue.ToString() ?? string.Empty;
                                                    }
                                                    else
                                                    {
                                                        parentName = AssetByName;
                                                        error.Add($"回路sheet页中第{circuitNumber + 1}行H列中的所属配电柜不能为空");
                                                    }


                                                    if (item.TryGetValue("D", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var assetNumber = itemValue.ToString() ?? string.Empty;
                                                        assetInfo.AssetNumber = assetNumber;

                                                        if (panelList.Select(p => p.Item1).Any(a => a != assetInfo && a.AssetNumber == assetInfo.AssetNumber))
                                                        {
                                                            error.Add($"回路sheet页中第{circuitNumber + 1}行D列中的回路编号已存在，请修改回路编号");
                                                        }

                                                        if (circuitList.Select(p => p.Item1).Any(a => a != assetInfo && a.AssetNumber == assetInfo.AssetNumber))
                                                        {
                                                            error.Add($"回路sheet页中第{circuitNumber + 1}行D列中的回路编号已存在，请修改回路编号");
                                                        }
                                                    }


                                                    if (item.TryGetValue("C", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var circuitName = itemValue.ToString() ?? string.Empty;
                                                        assetInfo.CircuitName = circuitName;
                                                    }

                                                    if (item.TryGetValue("E", out itemValue)
                                                        && itemValue is string assetTypeStr)
                                                    {
                                                        var circuitType = circuitTypeList.FirstOrDefault(p => p.Name == assetTypeStr);
                                                        if (circuitType != null)
                                                        {
                                                            assetInfo.AssetType = circuitType.Code;
                                                        }
                                                        else
                                                        {
                                                            error.Add($"回路sheet页中第{circuitNumber + 1}行E列中的回路类型不正确");
                                                        }
                                                    }

                                                    if (item.TryGetValue("F", out itemValue)
                                                        && itemValue is string useSceneStr)
                                                    {
                                                        var useScene = useSceneList.FirstOrDefault(p => p.Name == useSceneStr);
                                                        if (useScene != null)
                                                        {
                                                            assetInfo.UseScene = useScene.Code;
                                                        }
                                                    }

                                                    if (item.TryGetValue("G", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var desc = itemValue.ToString();
                                                        assetInfo.Description = desc;

                                                        if (assetInfo.Description.CanNullAndNotOnlyNumberOrSymbol(100))
                                                        {
                                                            error.Add($"回路sheet页中第{circuitNumber + 1}行G列中的资产概述太长或者不符合要求(资产概述长度介于0到100之间)");
                                                        }
                                                    }

                                                    if (item.TryGetValue("I", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var width = itemValue.ToString();
                                                        if (!string.IsNullOrEmpty(width))
                                                        {
                                                            circuitModel.Width = width;
                                                            assetInfo.Width = width;
                                                        }
                                                    }

                                                    if (item.TryGetValue("J", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var heigthStr = itemValue.ToString();
                                                        if (int.TryParse(heigthStr, out int heigth))
                                                        {
                                                            circuitModel.Heigth = heigth;
                                                            assetInfo.Height = heigthStr;
                                                        }
                                                    }

                                                    // 添加是否启用mqtt
                                                    if (item.TryGetValue("K", out itemValue))
                                                    {
                                                        assetInfo.EnableMqtt = Convert.ToInt32(itemValue) == 1 ? true : false;
                                                    }

                                                    if (error.Count > 0)
                                                    {
                                                        jobInfo.Result.ErrorInfo.AddRange(error);
                                                    }
                                                    else
                                                    {
                                                        circuitList.Add((assetInfo, parentName, circuitModel));
                                                    }
                                                }
                                            }
                                            break;
                                        case "Device":
                                        case "设备":
                                            {
                                                var assetLevel = AssetLevel.Device;
                                                foreach (var item in r.Datas)
                                                {
                                                    deviceNumber++;

                                                    var error = new List<string>();

                                                    var assetInfo = new AssetInfo();
                                                    assetInfo.AssetLevel = assetLevel;
                                                    assetInfo.CreatedBy = user ?? string.Empty;
                                                    assetInfo.CreatedTime = DateTime.Now;
                                                    assetInfo.UpdatedBy = user ?? string.Empty;
                                                    assetInfo.UpdatedTime = DateTime.Now;
                                                    object? itemValue = null;

                                                    string parentName = string.Empty;


                                                    if (item.TryGetValue("A", out itemValue)
                                                        && itemValue != null
                                                        && itemValue is string itemStr
                                                        && !string.IsNullOrWhiteSpace(itemStr))
                                                    {
                                                        var assetName = itemValue.ToString() ?? string.Empty;
                                                        assetInfo.AssetName = assetName;

                                                        if (string.IsNullOrEmpty(assetInfo.AssetName)
                                                         || assetInfo.AssetName.MustCharAndNotOnlyNumberOrSymbol(30))
                                                        {
                                                            error.Add($"设备sheet页中第{deviceNumber + 1}行A列中的资产名称太长或者不符合要求(资产名称长度介于0到30之间)");
                                                        }

                                                        if (factoryList.Any(a => a != assetInfo && a.AssetName == assetInfo.AssetName))
                                                        {
                                                            error.Add($"设备sheet页中第{deviceNumber + 1}行A列中的资产名称已存在，请修改资产名称");
                                                        }

                                                        if (substationList.Select(p => p.Item1).Any(a => a != assetInfo && a.AssetName == assetInfo.AssetName))
                                                        {
                                                            error.Add($"设备sheet页中第{deviceNumber + 1}行A列中的资产名称已存在，请修改资产名称");
                                                        }

                                                        if (panelList.Select(p => p.Item1).Any(a => a != assetInfo && a.AssetName == assetInfo.AssetName))
                                                        {
                                                            error.Add($"设备sheet页中第{deviceNumber + 1}行A列中的资产名称已存在，请修改资产名称");
                                                        }

                                                        if (circuitList.Select(p => p.Item1).Any(a => a != assetInfo && a.AssetName == assetInfo.AssetName))
                                                        {
                                                            error.Add($"设备sheet页中第{deviceNumber + 1}行A列中的资产名称已存在，请修改资产名称");
                                                        }

                                                        if (deviceList.Select(p => p.Item1).Any(a => a != assetInfo && a.AssetName == assetInfo.AssetName))
                                                        {
                                                            error.Add($"设备sheet页中第{deviceNumber + 1}行A列中的资产名称已存在，请修改资产名称");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        return Task.FromResult(0);
                                                    }

                                                    if (item.TryGetValue("J", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        parentName = itemValue.ToString() ?? string.Empty;
                                                    }
                                                    else
                                                    {
                                                        parentName = AssetByName;
                                                        error.Add($"设备sheet页中第{deviceNumber + 1}行I列中的所属资产层级不能为空");
                                                    }

                                                    if (item.TryGetValue("C", out itemValue)
                                                        && itemValue is string assetTypeStr)
                                                    {
                                                        var deviceType = deviceTypeList.FirstOrDefault(p => p.Name == assetTypeStr);
                                                        if (deviceType != null)
                                                        {
                                                            assetInfo.AssetType = deviceType.Code;
                                                        }
                                                        else
                                                        {
                                                            error.Add($"设备sheet页中第{deviceNumber + 1}行C列中的所属设备类型不正确");
                                                        }
                                                    }

                                                    if (item.TryGetValue("D", out itemValue)
                                                        && itemValue is string model)
                                                    {
                                                        var deviceModels = deviceModelList.Where(p => p.Name == model).ToArray();
                                                        if (deviceModels != null && deviceModels.Length > 0)
                                                        {
                                                            var deviceModel = deviceModels.FirstOrDefault(dm => dm.ModelByType.Equals(assetInfo.AssetType, StringComparison.OrdinalIgnoreCase));

                                                            if (deviceModel == null)
                                                            {
                                                                error.Add($"设备sheet页中第{deviceNumber + 1}行D列中的设备型号不正确");
                                                            }
                                                            else
                                                            {
                                                                assetInfo.AssetModel = deviceModel?.Code;
                                                            }
                                                        }

                                                    }

                                                    if (item.TryGetValue("E", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var mlfb = itemValue.ToString();
                                                        assetInfo.MLFB = mlfb;
                                                    }

                                                    if (item.TryGetValue("F", out itemValue))
                                                    {
                                                        if (itemValue is string installDateStr
                                                            && DateTime.TryParse(installDateStr, out DateTime installDate))
                                                        {
                                                            assetInfo.InstallDate = installDate;
                                                        }
                                                        else if (itemValue is DateTime installDate2)
                                                        {
                                                            assetInfo.InstallDate = installDate2;
                                                        }
                                                    }

                                                    if (item.TryGetValue("G", out itemValue) && itemValue is string meterType)
                                                    {
                                                        var meterTypeItem = meterTypeList.FirstOrDefault(m => m.Name == meterType);
                                                        if (meterTypeItem != null)
                                                        {
                                                            assetInfo.MeterType = meterTypeItem.Code;
                                                        }
                                                    }

                                                    if (item.TryGetValue("H", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var location = itemValue.ToString();
                                                        assetInfo.Location = location;

                                                        if (assetInfo.Location.CanNullAndNotOnlySymbol(100))
                                                        {
                                                            error.Add($"设备sheet页中第{deviceNumber + 1}行H列中的资产位置太长或者不符合要求(资产位置长度介于0到100之间)");
                                                        }
                                                    }

                                                    if (item.TryGetValue("I", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var desc = itemValue.ToString();
                                                        assetInfo.Description = desc;


                                                        if (assetInfo.Description.CanNullAndNotOnlyNumberOrSymbol(100))
                                                        {
                                                            error.Add($"设备sheet页中第{deviceNumber + 1}行I列中的资产描述太长或者不符合要求(资产描述长度介于0到100之间)");
                                                        }
                                                    }

                                                    if (item.TryGetValue("K", out itemValue)
                                                        && itemValue != null)
                                                    {
                                                        var ip = itemValue.ToString();
                                                        assetInfo.IPAddress = ip;
                                                    }

                                                    // 添加port端口号
                                                    if (item.TryGetValue("L", out itemValue))
                                                    {
                                                        assetInfo.Port = itemValue?.ToString();
                                                    }

                                                    // 添加是否启用mqtt
                                                    if (item.TryGetValue("M", out itemValue))
                                                    {
                                                        assetInfo.EnableMqtt = Convert.ToInt32(itemValue) == 1 ? true : false;
                                                    }

                                                    if (error.Count > 0)
                                                    {
                                                        jobInfo.Result.ErrorInfo.AddRange(error);
                                                    }
                                                    else
                                                    {
                                                        deviceList.Add((assetInfo, parentName));
                                                    }
                                                }
                                            }
                                            break;
                                        case "Version":
                                            if (r.Datas.Length >= 1)
                                            {
                                                var row = r.Datas[0];
                                                object? itemValue = null;

                                                if (row.TryGetValue("A", out itemValue)
                                                    && itemValue != null)
                                                {
                                                    var versionStr = itemValue.ToString();
                                                    if (FileVersion.Equals(versionStr))
                                                    {
                                                        return Task.FromResult(1);
                                                    }
                                                    else
                                                    {
                                                        jobInfo.Result.ErrorInfo.Add($"Asset_FileVersionWrong");
                                                    }
                                                }
                                            }

                                            break;

                                        default: break;
                                    }

                                    return Task.FromResult(1);
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogError(ex, "读取数据失败");
                                    return Task.FromResult(-1);
                                }
                            },
                            startCell: "A2",
                            pageCount: 100);
                        }
                    }
                }
                #endregion

                var assetChangeFlag = false;

                #region Load Asset

                try
                {
                    await _client.Ado.BeginTranAsync();

                    #region 删除旧数据

                    //await _extendServer.DeleteAllAsset(_client);

                    #endregion

                    var oldAssets = await _client.Queryable<AssetInfo>().ToListAsync();

                    var assets = new List<AssetInfo>();
                    var updateAssetList = new List<AssetInfo>();
                    var oldRelations = await _client.Queryable<AssetRelation>().ToListAsync();

                    var dicDrawingCode = new Dictionary<int, string>();
                    var sortNo = 1;

                    #region 工厂添加

                    foreach (var f in factoryList)
                    {
                        var oldAsset = oldAssets.FirstOrDefault(a => a.AssetName == f.AssetName && a.AssetLevel == AssetLevel.Area);

                        if (oldAsset != null)
                        {
                            oldAssets.Remove(oldAsset);
                            #region
                            {
                                f.Id = oldAsset.Id;
                                f.CreatedBy = oldAsset.CreatedBy;
                                f.CreatedTime = oldAsset.CreatedTime;
                                oldAsset.SortNo = sortNo;
                                sortNo++;
                                UpdateAssetByExcelData(oldAsset, f);
                                assets.Add(oldAsset);
                                updateAssetList.Add(oldAsset);
                                var existRelations = oldRelations.Where(r => r.ChildId == f.Id).ToList();
                                existRelations.ForEach(ar => oldRelations.Remove(ar));
                                var realRelation = existRelations.FirstOrDefault(ar => ar.ChildId == f.Id && ar.ParentId == 0);
                                if (realRelation == null)
                                {
                                    await _client.Insertable(new AssetRelation()
                                    {
                                        ParentId = 0,
                                        ChildId = f.Id,
                                        AssetLevel = AssetLevel.Area,
                                        CreatedBy = user ?? string.Empty,
                                        CreatedTime = DateTime.Now,
                                        UpdatedBy = user ?? string.Empty,
                                        UpdatedTime = DateTime.Now,
                                    }).ExecuteCommandAsync();
                                }
                                else
                                {
                                    existRelations.Remove(realRelation);
                                }

                                await _client.Deleteable(existRelations).ExecuteCommandAsync();
                            }
                            #endregion
                        }
                        else
                        {
                            assetChangeFlag = true;
                            f.SortNo = sortNo;
                            sortNo++;
                            var id = await _client.Insertable(f).ExecuteReturnIdentityAsync();
                            f.Id = id;
                            assets.Add(f);
                            await _client.Insertable(new AssetRelation()
                            {
                                ParentId = 0,
                                ChildId = id,
                                AssetLevel = AssetLevel.Area,
                                CreatedBy = user ?? string.Empty,
                                CreatedTime = DateTime.Now,
                                UpdatedBy = user ?? string.Empty,
                                UpdatedTime = DateTime.Now,
                            }).ExecuteCommandAsync();
                        }
                    }

                    #endregion

                    #region 配电房

                    int subStationNum = 0;
                    foreach (var s in substationList)
                    {
                        subStationNum++;
                        var oldAsset = oldAssets.FirstOrDefault(a => a.AssetName == s.Item1.AssetName && a.AssetLevel == AssetLevel.Substation);

                        if (oldAsset != null)
                        {
                            oldAssets.Remove(oldAsset);
                            #region
                            {
                                s.Item1.Id = oldAsset.Id;
                                s.Item1.CreatedBy = oldAsset.CreatedBy;
                                s.Item1.CreatedTime = oldAsset.CreatedTime;
                                oldAsset.SortNo = sortNo;
                                sortNo++;
                                UpdateAssetByExcelData(oldAsset, s.Item1);
                                assets.Add(oldAsset);
                                updateAssetList.Add(oldAsset);
                                var existRelations = oldRelations.Where(r => r.ChildId == s.Item1.Id).ToList();
                                existRelations.ForEach(ar => oldRelations.Remove(ar));

                                AssetInfo? parant = null;
                                AssetRelation? realRelation = null;
                                parant = assets.Where(a =>
                                     a.AssetLevel == AssetLevel.Area &&
                                     a.AssetName == s.Item2).FirstOrDefault();

                                if (parant == null)
                                {
                                    jobInfo.Result.ErrorInfo.Add($"配电房sheet页中第{subStationNum + 1}行F列中的所属资产层级不正确");
                                }
                                else
                                {
                                    realRelation = existRelations.FirstOrDefault(ar => ar.ChildId == s.Item1.Id && ar.ParentId == parant.Id);
                                }

                                if (realRelation == null)
                                {
                                    assetChangeFlag = true;
                                    await _client.Insertable(new AssetRelation()
                                    {
                                        ParentId = parant?.Id ?? 0,
                                        ChildId = s.Item1.Id,
                                        AssetLevel = AssetLevel.Substation,
                                        CreatedBy = user ?? string.Empty,
                                        CreatedTime = DateTime.Now,
                                        UpdatedBy = user ?? string.Empty,
                                        UpdatedTime = DateTime.Now,
                                    }).ExecuteCommandAsync();
                                }
                                else
                                {
                                    existRelations.Remove(realRelation);
                                }

                                if (existRelations.Count != 0)
                                {
                                    assetChangeFlag = true;
                                    await _client.Deleteable(existRelations).ExecuteCommandAsync();
                                }

                                var areaCode = string.Empty;

                                if (parant != null)
                                {
                                    areaCode = parant.AssetName + "-";
                                }

                                var drawingCode = $"{areaCode}{s.Item1.AssetName}";
                                dicDrawingCode.Add(s.Item1.Id, drawingCode + "-{0}-" + drawingIndex.ToString("000"));
                                drawingIndex++;
                            }

                            #endregion
                        }
                        else
                        {
                            assetChangeFlag = true;
                            s.Item1.SortNo = sortNo;
                            sortNo++;
                            var id = await _client.Insertable(s.Item1).ExecuteReturnIdentityAsync();
                            s.Item1.Id = id;
                            assets.Add(s.Item1);
                            AssetInfo? parant = null;
                            if (!string.IsNullOrEmpty(s.Item2))
                            {
                                parant = assets.Where(a =>
                                    a.AssetLevel == AssetLevel.Area &&
                                     a.AssetName == s.Item2).FirstOrDefault();

                                if (parant == null)
                                {
                                    //jobInfo.Result.ErrorInfo.Add("Asset_MissSubstation");

                                    jobInfo.Result.ErrorInfo.Add($"配电房sheet页中第{subStationNum + 1}行F列中的所属资产层级不正确");
                                }
                            }

                            var areaCode = string.Empty;
                            if (parant != null)
                            {
                                areaCode = parant.AssetName + "-";
                            }

                            var drawingCode = $"{areaCode}{s.Item1.AssetName}";
                            s.Item1.DrawingCode = drawingCode + "-{0}-" + drawingIndex.ToString("000");
                            drawingIndex++;

                            await _client.Insertable(new AssetRelation()
                            {
                                ParentId = parant?.Id ?? 0,
                                ChildId = id,
                                AssetLevel = AssetLevel.Substation,
                                CreatedBy = user ?? string.Empty,
                                CreatedTime = DateTime.Now,
                                UpdatedBy = user ?? string.Empty,
                                UpdatedTime = DateTime.Now,
                            }).ExecuteCommandAsync();
                        }
                    }

                    #endregion

                    #region 配电柜
                    int panelNum = 0;
                    foreach (var s in panelList)
                    {
                        panelNum++;
                        var oldAsset = oldAssets.FirstOrDefault(a => a.AssetName == s.Item1.AssetName && a.AssetLevel == AssetLevel.Panel);

                        if (oldAsset != null)
                        {
                            oldAssets.Remove(oldAsset);
                            #region
                            {
                                s.Item1.Id = oldAsset.Id;
                                s.Item1.CreatedBy = oldAsset.CreatedBy;
                                s.Item1.CreatedTime = oldAsset.CreatedTime;
                                oldAsset.SortNo = sortNo;
                                sortNo++;
                                UpdateAssetByExcelData(oldAsset, s.Item1);
                                assets.Add(oldAsset);
                                updateAssetList.Add(oldAsset);
                                var existRelations = oldRelations.Where(r => r.ChildId == s.Item1.Id).ToList();
                                existRelations.ForEach(ar => oldRelations.Remove(ar));

                                AssetInfo? parant = null;
                                AssetRelation? realRelation = null;
                                parant = assets.Where(a =>
                                     a.AssetLevel == AssetLevel.Substation &&
                                     a.AssetName == s.Item2).FirstOrDefault();

                                if (parant == null)
                                {
                                    jobInfo.Result.ErrorInfo.Add($"配电柜sheet页中第{panelNum + 1}行I列中的所属资产层级不正确");
                                }
                                else
                                {
                                    realRelation = existRelations.FirstOrDefault(ar => ar.ChildId == s.Item1.Id && ar.ParentId == parant.Id);
                                }

                                if (realRelation == null)
                                {
                                    assetChangeFlag = true;
                                    await _client.Insertable(new AssetRelation()
                                    {
                                        ParentId = parant?.Id ?? 0,
                                        ChildId = s.Item1.Id,
                                        AssetLevel = AssetLevel.Panel,
                                        CreatedBy = user ?? string.Empty,
                                        CreatedTime = DateTime.Now,
                                        UpdatedBy = user ?? string.Empty,
                                        UpdatedTime = DateTime.Now,
                                    }).ExecuteCommandAsync();
                                }
                                else
                                {
                                    existRelations.Remove(realRelation);
                                }

                                if (existRelations.Count > 0)
                                {
                                    assetChangeFlag = true;
                                    await _client.Deleteable(existRelations).ExecuteCommandAsync();
                                }

                                var areaCode = string.Empty;
                                if (parant != null)
                                {
                                    areaCode = parant.AssetName + "-";
                                }
                                var drawingCode = $"{areaCode}{s.Item1.AssetName}";
                                dicDrawingCode.Add(s.Item1.Id, drawingCode + "-{0}-" + drawingIndex.ToString("000"));
                                drawingIndex++;


                                s.Item3.ParentAsset = parant;

                                panelDataList.Add(s.Item3);
                            }
                            #endregion
                        }
                        else
                        {
                            s.Item1.SortNo = sortNo;
                            sortNo++;
                            var id = await _client.Insertable(s.Item1).ExecuteReturnIdentityAsync();
                            s.Item1.Id = id;

                            await _extendServer.AddPannelSystemHealthCheckList(s.Item1, _client);
                            assets.Add(s.Item1);
                            AssetInfo? parant = null;
                            if (!string.IsNullOrEmpty(s.Item2))
                            {
                                parant = assets.Where(a => a.AssetLevel == AssetLevel.Substation && a.AssetName == s.Item2).FirstOrDefault();

                                if (parant == null)
                                {
                                    //jobInfo.Result.ErrorInfo.Add("Asset_MissPanel");

                                    jobInfo.Result.ErrorInfo.Add($"配电柜sheet页中第{panelNum + 1}行I列中的所属资产层级不正确");
                                }
                            }

                            var drawingCode = $"{s.Item1.AssetName}-";
                            s.Item1.DrawingCode = drawingCode + "-{0}-" + drawingIndex.ToString("000");
                            drawingIndex++;

                            await _client.Insertable(new AssetRelation()
                            {
                                ParentId = parant?.Id ?? 0,
                                ChildId = id,
                                AssetLevel = AssetLevel.Panel,
                                CreatedBy = user ?? string.Empty,
                                CreatedTime = DateTime.Now,
                                UpdatedBy = user ?? string.Empty,
                                UpdatedTime = DateTime.Now,
                            }).ExecuteCommandAsync();

                            s.Item3.ParentAsset = parant;

                            panelDataList.Add(s.Item3);
                        }
                    }
                    #endregion
                    #region 变压器
                    int transformerNum = 0;
                    foreach (var s in transformerList)
                    {
                        transformerNum++;
                        AssetInfo? relationPanel = null;
                        relationPanel = assets.Where(a =>
                           a.AssetLevel == AssetLevel.Panel &&
                           a.AssetName == s.Item3.RelationPanelName).FirstOrDefault();
                        if (relationPanel == null)
                        {
                            jobInfo.Result.ErrorInfo.Add($"变压器sheet页中第{transformerNum + 1}行I列中的连接配电柜不正确");
                        }
                        else
                        {
                            s.Item1.RelationPanelId = relationPanel.Id;
                        }
                        var oldAsset = oldAssets.FirstOrDefault(a => a.AssetName == s.Item1.AssetName && a.AssetLevel == AssetLevel.Transformer);

                        if (oldAsset != null)
                        {
                            oldAssets.Remove(oldAsset);
                            #region
                            {
                               
                                s.Item1.Id = oldAsset.Id;
                                s.Item1.CreatedBy = oldAsset.CreatedBy;
                                s.Item1.CreatedTime = oldAsset.CreatedTime;
                                oldAsset.SortNo = sortNo;
                                sortNo++;
                                UpdateAssetByExcelData(oldAsset, s.Item1);
                                assets.Add(oldAsset);
                                updateAssetList.Add(oldAsset);
                                var existRelations = oldRelations.Where(r => r.ChildId == s.Item1.Id).ToList();
                                existRelations.ForEach(ar => oldRelations.Remove(ar));

                                AssetInfo? parant = null;
                                AssetRelation? realRelation = null;
                               
                                parant = assets.Where(a =>
                                     a.AssetLevel == AssetLevel.Substation &&
                                     a.AssetName == s.Item2).FirstOrDefault();

                                if (parant == null)
                                {
                                    jobInfo.Result.ErrorInfo.Add($"变压器sheet页中第{transformerNum + 1}行I列中的所属资产层级不正确");
                                }
                                else
                                {
                                    realRelation = existRelations.FirstOrDefault(ar => ar.ChildId == s.Item1.Id && ar.ParentId == parant.Id);
                                }
                               
                                if (realRelation == null)
                                {
                                    assetChangeFlag = true;
                                    await _client.Insertable(new AssetRelation()
                                    {
                                        ParentId = parant?.Id ?? 0,
                                        ChildId = s.Item1.Id,
                                        AssetLevel = AssetLevel.Transformer,
                                        CreatedBy = user ?? string.Empty,
                                        CreatedTime = DateTime.Now,
                                        UpdatedBy = user ?? string.Empty,
                                        UpdatedTime = DateTime.Now,
                                    }).ExecuteCommandAsync();
                                }
                                else
                                {
                                    existRelations.Remove(realRelation);
                                }

                                if (existRelations.Count > 0)
                                {
                                    assetChangeFlag = true;
                                    await _client.Deleteable(existRelations).ExecuteCommandAsync();
                                }

                                var areaCode = string.Empty;
                                if (parant != null)
                                {
                                    areaCode = parant.AssetName + "-";
                                }
                                var drawingCode = $"{areaCode}{s.Item1.AssetName}";
                                dicDrawingCode.Add(s.Item1.Id, drawingCode + "-{0}-" + drawingIndex.ToString("000"));
                                drawingIndex++;


                                s.Item3.ParentAsset = parant;

                                transformerDataList.Add(s.Item3);
                            }
                            #endregion
                        }
                        else
                        {
                            s.Item1.SortNo = sortNo;
                            sortNo++;
                            var id = await _client.Insertable(s.Item1).ExecuteReturnIdentityAsync();
                            s.Item1.Id = id;

                            //await _extendServer.AddPannelSystemHealthCheckList(s.Item1, _client);
                            assets.Add(s.Item1);
                            AssetInfo? parant = null;
                            if (!string.IsNullOrEmpty(s.Item2))
                            {
                                parant = assets.Where(a => a.AssetLevel == AssetLevel.Substation && a.AssetName == s.Item2).FirstOrDefault();

                                if (parant == null)
                                {
                                    //jobInfo.Result.ErrorInfo.Add("Asset_MissPanel");

                                    jobInfo.Result.ErrorInfo.Add($"变压器sheet页中第{transformerNum + 1}行I列中的所属资产层级不正确");
                                }
                            }

                            var drawingCode = $"{s.Item1.AssetName}-";
                            s.Item1.DrawingCode = drawingCode + "-{0}-" + drawingIndex.ToString("000");
                            drawingIndex++;

                            await _client.Insertable(new AssetRelation()
                            {
                                ParentId = parant?.Id ?? 0,
                                ChildId = id,
                                AssetLevel = AssetLevel.Transformer,
                                CreatedBy = user ?? string.Empty,
                                CreatedTime = DateTime.Now,
                                UpdatedBy = user ?? string.Empty,
                                UpdatedTime = DateTime.Now,
                            }).ExecuteCommandAsync();

                            s.Item3.ParentAsset = parant;

                            transformerDataList.Add(s.Item3);
                        }
                    }
                    #endregion

                    #region 回路
                    int circuitNum = 0;

                    foreach (var s in circuitList)
                    {
                        circuitNum++;
                        var oldAsset = oldAssets.FirstOrDefault(a => a.AssetName == s.Item1.AssetName && a.AssetLevel == AssetLevel.Circuit);

                        if (oldAsset != null)
                        {
                            oldAssets.Remove(oldAsset);
                            #region
                            {
                                s.Item1.Id = oldAsset.Id;
                                s.Item1.CreatedBy = oldAsset.CreatedBy;
                                s.Item1.CreatedTime = oldAsset.CreatedTime;
                                oldAsset.SortNo = sortNo;
                                sortNo++;
                                UpdateAssetByExcelData(oldAsset, s.Item1);
                                assets.Add(oldAsset);
                                updateAssetList.Add(oldAsset);
                                var existRelations = oldRelations.Where(r => r.ChildId == s.Item1.Id).ToList();
                                existRelations.ForEach(ar => oldRelations.Remove(ar));

                                AssetInfo? parant = null;
                                AssetRelation? realRelation = null;
                                parant = assets.Where(a =>
                                     a.AssetLevel == AssetLevel.Panel &&
                                     a.AssetName == s.Item2).FirstOrDefault();

                                if (parant == null)
                                {
                                    jobInfo.Result.ErrorInfo.Add($"配电房sheet页中第{subStationNum + 1}行F列中的所属资产层级不正确");
                                }
                                else
                                {
                                    realRelation = existRelations.FirstOrDefault(ar => ar.ChildId == s.Item1.Id && ar.ParentId == parant.Id);
                                }

                                if (realRelation == null)
                                {
                                    assetChangeFlag = true;
                                    await _client.Insertable(new AssetRelation()
                                    {
                                        ParentId = parant?.Id ?? 0,
                                        ChildId = s.Item1.Id,
                                        AssetLevel = AssetLevel.Circuit,
                                        CreatedBy = user ?? string.Empty,
                                        CreatedTime = DateTime.Now,
                                        UpdatedBy = user ?? string.Empty,
                                        UpdatedTime = DateTime.Now,
                                    }).ExecuteCommandAsync();
                                }
                                else
                                {
                                    existRelations.Remove(realRelation);
                                }

                                if (existRelations.Count > 0)
                                {
                                    assetChangeFlag = true;
                                    await _client.Deleteable(existRelations).ExecuteCommandAsync();
                                }

                                s.Item3.ParentAsset = parant;

                                if (parant != null)
                                {
                                    var parentPanel = panelDataList.FirstOrDefault(p => p.AssetInfo.Id == parant.Id);
                                    if (parentPanel != null)
                                    {
                                        parentPanel.SubCircuits.Add(s.Item3);
                                    }
                                }
                                circuitModelList.Add(s.Item3);
                            }
                            #endregion
                        }
                        else
                        {
                            assetChangeFlag = true;
                            s.Item1.SortNo = sortNo;
                            sortNo++;
                            var id = await _client.Insertable(s.Item1).ExecuteReturnIdentityAsync();
                            s.Item1.Id = id;

                            assets.Add(s.Item1);
                            AssetInfo? parant = null;
                            if (!string.IsNullOrEmpty(s.Item2))
                            {
                                parant = assets.Where(a => a.AssetLevel == AssetLevel.Panel && a.AssetName == s.Item2).FirstOrDefault();
                                if (parant == null)
                                {
                                    //jobInfo.Result.ErrorInfo.Add("Asset_MissCircuit");

                                    jobInfo.Result.ErrorInfo.Add($"回路sheet页中第{circuitNum + 1}行H列中的所属配电柜不正确");
                                }
                            }

                            await _client.Insertable(new AssetRelation()
                            {
                                ParentId = parant?.Id ?? 0,
                                ChildId = id,
                                AssetLevel = AssetLevel.Circuit,
                                CreatedBy = user ?? string.Empty,
                                CreatedTime = DateTime.Now,
                                UpdatedBy = user ?? string.Empty,
                                UpdatedTime = DateTime.Now,
                            }).ExecuteCommandAsync();

                            s.Item3.ParentAsset = parant;

                            if (parant != null)
                            {
                                var parentPanel = panelDataList.FirstOrDefault(p => p.AssetInfo.Id == parant.Id);
                                if (parentPanel != null)
                                {
                                    parentPanel.SubCircuits.Add(s.Item3);
                                }
                            }
                            circuitModelList.Add(s.Item3);
                        }
                    }
                    #endregion

                    #region 设备
                    var newDevices = new List<AssetInfo>();
                    int deviceNum = 0;
                    foreach (var s in deviceList)
                    {
                        deviceNum++;
                        var oldAsset = oldAssets.FirstOrDefault(a => a.AssetName == s.Item1.AssetName && a.AssetLevel == AssetLevel.Device);

                        if (oldAsset != null)
                        {
                            oldAssets.Remove(oldAsset);
                            #region
                            {
                                s.Item1.Id = oldAsset.Id;
                                s.Item1.CreatedBy = oldAsset.CreatedBy;
                                s.Item1.CreatedTime = oldAsset.CreatedTime;

                                UpdateAssetByExcelData(oldAsset, s.Item1);
                                oldAsset.SortNo = sortNo;
                                sortNo++;
                                assets.Add(oldAsset);

                                updateAssetList.Add(oldAsset);
                                var existRelations = oldRelations.Where(r => r.ChildId == s.Item1.Id).ToList();
                                existRelations.ForEach(ar => oldRelations.Remove(ar));

                                AssetInfo? parant = null;
                                AssetRelation? realRelation = null;
                                parant = assets.Where(a =>
                                    (a.AssetLevel == AssetLevel.Circuit ||
                                    a.AssetLevel == AssetLevel.Substation ||
                                    a.AssetLevel == AssetLevel.Panel||a.AssetLevel==AssetLevel.Transformer) &&
                                    a.AssetName == s.Item2).FirstOrDefault();

                                if (parant == null)
                                {
                                    jobInfo.Result.ErrorInfo.Add($"设备sheet页中第{deviceNum + 1}行J列中的所属资产层级不正确");
                                }
                                else
                                {
                                    realRelation = existRelations.FirstOrDefault(ar => ar.ChildId == s.Item1.Id && ar.ParentId == parant.Id);

                                    var circuitModel = circuitModelList.FirstOrDefault(p => p.AssetInfo.Id == parant.Id);

                                    if (circuitModel != null)
                                    {
                                        circuitModel.SubDevices.Add(s.Item1);
                                    }
                                }

                                if (realRelation == null)
                                {
                                    assetChangeFlag = true;
                                    await _client.Insertable(new AssetRelation()
                                    {
                                        ParentId = parant?.Id ?? 0,
                                        ChildId = s.Item1.Id,
                                        AssetLevel = AssetLevel.Device,
                                        CreatedBy = user ?? string.Empty,
                                        CreatedTime = DateTime.Now,
                                        UpdatedBy = user ?? string.Empty,
                                        UpdatedTime = DateTime.Now,
                                    }).ExecuteCommandAsync();
                                }
                                else
                                {
                                    existRelations.Remove(realRelation);
                                }

                                if (existRelations.Count > 0)
                                {
                                    assetChangeFlag = true;
                                    await _client.Deleteable(existRelations).ExecuteCommandAsync();
                                }
                            }
                            #endregion
                        }
                        else
                        {
                            assetChangeFlag = true;
                            s.Item1.SortNo = sortNo;
                            sortNo++;
                            var id = await _client.Insertable(s.Item1).ExecuteReturnIdentityAsync();
                            s.Item1.Id = id;

                            newDevices.Add(s.Item1);

                            assets.Add(s.Item1);
                            int parantId = 0;
                            if (!string.IsNullOrEmpty(s.Item2))
                            {
                                parantId = assets.Where(a =>
                                (a.AssetLevel == AssetLevel.Circuit ||
                                a.AssetLevel == AssetLevel.Substation ||
                                a.AssetLevel == AssetLevel.Panel 
                                ||a.AssetLevel==AssetLevel.Transformer) &&
                                a.AssetName == s.Item2).Select(a => a.Id).FirstOrDefault();

                                if (parantId == 0)
                                {
                                    //jobInfo.Result.ErrorInfo.Add("Asset_MissDevice");

                                    jobInfo.Result.ErrorInfo.Add($"设备sheet页中第{deviceNum + 1}行J列中的所属资产层级不正确");
                                }
                            }

                            await _client.Insertable(new AssetRelation()
                            {
                                ParentId = parantId,
                                ChildId = id,
                                AssetLevel = AssetLevel.Device,
                                CreatedBy = user ?? string.Empty,
                                CreatedTime = DateTime.Now,
                                UpdatedBy = user ?? string.Empty,
                                UpdatedTime = DateTime.Now,
                            }).ExecuteCommandAsync();

                            await _extendServer.AddDeviceInputOutputList(s.Item1, _client);
                            if (s.Item1.AssetModel == "Modbus" && s.Item1.AssetType == "Gateway" && !string.IsNullOrEmpty(s.Item1.Port) && s.Item1.Port.Length > 0)
                            {
                                var portInfo = new AssetThirdGateway
                                {
                                    Name = "串口1",
                                    Port = s.Item1.Port,
                                    AssetId = id,
                                    CreatedBy = user ?? string.Empty,
                                    CreatedTime = DateTime.Now,
                                    UpdatedBy = user ?? string.Empty,
                                    UpdatedTime = DateTime.Now
                                };
                                await _client.Insertable(portInfo).ExecuteCommandAsync();

                            }
                            var circuitModel = circuitModelList.FirstOrDefault(p => p.AssetInfo.Id == parantId);

                            if (circuitModel != null)
                            {
                                circuitModel.SubDevices.Add(s.Item1);
                            }
                        }
                    }
                    #endregion

                    #region 错误集合
                    if (jobInfo.Result.ErrorInfo.Count > 0)
                    {
                        throw new AssetExcelInfoException("存在验证错误得信息");
                    }
                    #endregion

                    var assetIds = new List<int>();

                    if (updateAssetList.Count > 0)
                    {
                        assetIds.AddRange(updateAssetList.Select(a => a.Id));
                    }
                    await _client.Updateable(updateAssetList).ExecuteCommandAsync();
                    if (oldAssets.Count > 0)
                    {
                        assetIds.AddRange(oldAssets.Select(a => a.Id));
                        assetChangeFlag = true;
                        await _extendServer.DeleteAsset(oldAssets.Select(a => a.Id).ToArray(), _client, user);
                    }

                    if (oldRelations.Count > 0)
                    {
                        assetChangeFlag = true;
                        await _client.Deleteable(oldRelations).ExecuteCommandAsync();
                    }

                    #region 删除组态图
                    if (assetChangeFlag)
                    {
                        var topologyIds = assets.Where(a => a.TopologyId.HasValue && a.AssetLevel != AssetLevel.Area).Select(a => a.TopologyId.Value).ToList();
                        topologyIds.AddRange(assets.Where(a => a.Topology3DId.HasValue).Select(a => a.Topology3DId.Value).ToList());
                        foreach (var item in dicDrawingCode)
                        {
                            var asset = assets.FirstOrDefault(a => a.Id == item.Key);
                            if (asset != null)
                            {
                                asset.DrawingCode = item.Value;
                            }
                        }
                        if (topologyIds != null)
                        {
                            var server = _provider.GetRequiredService<TopologyExtendFunc>();
                            await server.DeleteTopologyByTopologyIds(topologyIds.ToArray(), _client);
                        }
                    }

                    #endregion

                    #region 导入excel后初始化assetId集合
                    //if (assets.Any())
                    //{
                    //    _cache.Set<List<int>>("uploadExcelByAssetIds", assets.Select(p => p.Id).ToList());
                    //}
                    #endregion

                    #region 第三方断路器设置额定电压和额定电流默认值
                    {
                        var breakers = newDevices.Where(d => d.AssetModel == "Other" && (d.AssetType == "ACB")).ToList();
                        if (breakers.Count > 0)
                        {
                            var deviceDetails = new List<DeviceDetails>();
                            foreach (var b in breakers)
                            {
                                deviceDetails.Add(new DeviceDetails
                                {
                                    AssetId = b.Id,
                                    RatedCurrent = 2000,
                                    RatedVoltage = 400,
                                    CreatedBy = user ?? string.Empty,
                                    UpdatedBy = user ?? string.Empty,
                                    CreatedTime = DateTime.Now,
                                    UpdatedTime = DateTime.Now,
                                });
                            }

                            await _client.Insertable(deviceDetails).ExecuteCommandAsync();
                        }
                    }
                    #endregion

                    await _client.Ado.CommitTranAsync();

                    _extendServer.RemoveAssetEnableMqttCache(assetIds.ToArray());
                }
                catch (AssetExcelInfoException aex)
                {
                    _client.Ado.RollbackTran();
                    _logger.LogError(aex, "上传文件失败");
                    jobInfo.JobStatus = 99;
                    _cache.Set($"JobId:{jobId}", jobInfo, TimeSpan.FromMinutes(1));
                    return;
                }
                catch (Exception ex)
                {
                    _client.Ado.RollbackTran();
                    _logger.LogError(ex, "上传文件失败");
                    jobInfo.JobStatus = 99;
                    jobInfo.Result.ErrorInfo.Add("Common_ServerException");
                    _cache.Set($"JobId:{jobId}", jobInfo, TimeSpan.FromMinutes(1));
                    return;
                }

                //清空点位集合
                UniversalDeviceInfo.Instance._dicData.Clear();

                //初始化分组点位配置
                await InitMqttConfig(user ?? "管理员");
                #endregion

                #region 单线图
                if (assetChangeFlag)
                {
                    StringBuilder message = new StringBuilder();
                    try
                    {
                        var builder = new TopologyBuilder();
                        var panelValue = panelDataList.ToList();
                        //传变压器数据列表，后续3D和单线图使用处理
                        var transformerValue = transformerDataList.ToList();
                        var result = await builder.CreateTopology(panelValue,_client, user ?? "管理员", message, transformerValue);
                        GetErrorMessage(jobInfo.Result.ErrorInfo, message);
                        if (!result)
                        {
                            jobInfo.Result.ErrorInfo.Add("Asset_TopologyFail");
                        }
                        else
                        {
                            jobInfo.Result.ErrorInfo.Add("Asset_TopologySuccess");
                        }
                    }
                    catch (Exception e)
                    {
                        GetErrorMessage(jobInfo.Result.ErrorInfo, message);
                        jobInfo.Result.ErrorInfo.Add("Asset_TopologyFail");
                        _logger.LogError(e, "生成单线图失败");
                    }
                }
                #endregion

                #region 3D图
                if (assetChangeFlag)
                {
                    try
                    {
                        var builder = new Topology3DModelBuilder();
                        var panelValue = panelDataList.ToList();
                        //传变压器数据列表，后续3D和单线图使用处理
                        var transformerValue = transformerDataList.ToList();
                        var result = await builder.CreateTopology(panelValue, _client, user ?? "管理员", jobInfo.Result.ErrorInfo, transformerValue);
                        if (!result)
                        {
                            jobInfo.Result.ErrorInfo.Add("Asset_Topology3DFail");
                        }
                        else
                        {
                            jobInfo.Result.ErrorInfo.Add("Asset_Topology3DSuccess");
                        }
                    }
                    catch (Exception e)
                    {
                        jobInfo.Result.ErrorInfo.Add("Asset_Topology3DFail");
                        _logger.LogError(e, "生成3D图失败");
                    }
                }
                #endregion

                jobInfo.JobStatus = 20;
                _cache.Set($"JobId:{jobId}", jobInfo, TimeSpan.FromMinutes(1));
                return;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "上传文件失败");
                jobInfo.JobStatus = 99;
                jobInfo.Result.ErrorInfo.Add("Common_ServerException");
                _cache.Set($"JobId:{jobId}", jobInfo, TimeSpan.FromMinutes(1));
                return;
            }
        }

        private void UpdateAssetByExcelData(AssetInfo old, AssetInfo uploadData)
        {
            old.AssetName = uploadData.AssetName;
            if (uploadData.AssetLevel != AssetLevel.Area && uploadData.AssetLevel != AssetLevel.Substation && uploadData.AssetLevel != AssetLevel.Device)
            {
                old.AssetNumber = uploadData.AssetNumber;
            }

            if(uploadData.AssetLevel == AssetLevel.Device)
            {
                old.Location = uploadData.Location;
            }
            old.AssetType = uploadData.AssetType;
            old.AssetMaker = uploadData.AssetMaker;
            old.Description = uploadData.Description;
            old.CircuitName = uploadData.CircuitName;
            old.BusBarId = uploadData.BusBarId;
            old.Height = uploadData.Height;
            old.Width = uploadData.Width;
            old.RelationPanelId = uploadData.RelationPanelId;
            old.Port = uploadData.Port;
            old.IPAddress = uploadData.IPAddress;
            old.AssetModel = uploadData.AssetModel;
            old.RowNo = uploadData.RowNo;
            old.MLFB = uploadData.MLFB;
            old.UpdatedBy = uploadData.UpdatedBy;
            old.UpdatedTime = uploadData.UpdatedTime;
            old.EnableMqtt = uploadData.EnableMqtt;
            old.InstallDate = uploadData.InstallDate;
            old.MeterType = uploadData.MeterType;
            old.UseScene = uploadData.UseScene;
        }

        /// <summary>
        /// 初始化分组点位配置
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        private async Task InitMqttConfig(string user)
        {
            try
            {
                _client.Ado.BeginTran();

                //批量删除配置
                //await _client.Deleteable<ThirdModelConfig>().ExecuteCommandAsync();
                //await _client.Deleteable<UniversalDeviceConfig>().ExecuteCommandAsync();
                //await _client.Deleteable<BitConfig>().ExecuteCommandAsync();
                //await _client.Deleteable<AssetMqttDataPointConfig>().ExecuteCommandAsync();

                //获取文件夹的信息
                var assetDataPointDirectorys = await _client.Queryable<AssetDataPointDirectory>()
                    .OrderBy(p => p.Sort)
                    .Select(p => new
                    {
                        p.Name,
                        p.ParentName,
                        p.AssetModel
                    }).ToListAsync();

                //获取文件夹中AssetModel集合
                var assetModelList = assetDataPointDirectorys.Select(p => p.AssetModel).Distinct().ToList();

                //获取导入的EnableMqtt的数据
                var assetInfos = await _client.Queryable<AssetInfo>().WhereIF(assetModelList.Any(), p => assetModelList.Contains(p.AssetModel)
                    ).Where(p => p.EnableMqtt == true)
                    .Distinct()
                    .Select(p => new { p.Id, p.AssetModel })
                    .ToListAsync();

                //获取点位信息
                var assetDataPointInfos = await _client.Queryable<AssetDataPointInfo>().WhereIF(assetInfos.Any(), p => assetInfos.Select(o => o.AssetModel).Contains(p.AssetModel) && p.IsDefaultMqtt == 1).ToListAsync();

                var assetMqttDataPointConfigs = new List<AssetMqttDataPointConfig>();
                var existsMqttConfigs = await _client.Queryable<AssetMqttDataPointConfig>().ToListAsync();

                if (assetInfos.Any())
                {
                    foreach (var item in assetInfos)
                    {
                        if (existsMqttConfigs.Any(e => e.AssetId == item.Id)) continue;
                        //获取分组集合
                        var groupList = assetDataPointInfos.Where(p => p.AssetModel == item.AssetModel).Select(p => new
                        {
                            p.MqttGroupName,
                            p.MqttSamplingPeriod,

                        }).Distinct().ToList();

                        //添加分组
                        foreach (var _item in groupList)
                        {
                            assetMqttDataPointConfigs.Add(new AssetMqttDataPointConfig()
                            {
                                AssetId = item.Id,
                                ConfigType = GroupConfigType.Group,
                                Name = _item.MqttGroupName ?? "未命名",
                                SamplingPeriod = _item.MqttSamplingPeriod ?? 5,
                                CreatedBy = user,
                                CreatedTime = DateTime.Now,
                                UpdatedBy = user,
                                UpdatedTime = DateTime.Now
                            });
                        }

                        //获取子集点位数据
                        var childDatas = assetDataPointInfos.Where(p => p.AssetModel == item.AssetModel).ToList();

                        if (childDatas.Any())
                        {
                            foreach (var _item in childDatas)
                            {
                                var entity = new AssetMqttDataPointConfig()
                                {
                                    AssetId = item.Id,
                                    ConfigType = GroupConfigType.Measurement,
                                    GroupName = _item.MqttGroupName ?? "未命名",
                                    Code = _item.Code,
                                    AssetModel = _item.AssetModel,
                                    Name = _item.Name,
                                    SamplingPeriod = 0,
                                    CreatedBy = user,
                                    CreatedTime = DateTime.Now,
                                    UpdatedBy = user,
                                    UpdatedTime = DateTime.Now
                                };

                                assetMqttDataPointConfigs.Add(entity);
                            }
                        }

                    }
                }

                //批量添加
                await _client.Insertable(assetMqttDataPointConfigs).ExecuteCommandAsync();

                _client.Ado.CommitTran();

                var action = _provider.GetRequiredService<ExternalMqttAction>();
                //初始化数据
                await action.InitMqttConfigs();
            }
            catch (Exception ex)
            {
                _client.Ado.RollbackTran();
                Console.WriteLine(ex.Message);
            }
        }

        private static void GetErrorMessage(List<string> errorCodes, StringBuilder message)
        {
            if (message.Length > 0)
            {
                using (var sr = new StringReader(message.ToString()))
                {
                    string? error = null;

                    do
                    {
                        error = sr.ReadLine();
                        if (!string.IsNullOrEmpty(error)
                            && !errorCodes.Contains(error))
                        {
                            errorCodes.Add(error);
                        }
                    }
                    while (!string.IsNullOrEmpty(error));
                }
            }
        }
    }


    public class RowNoDto
    {
        public string? ParentName { get; set; }

        public string? RowNo { get; set; }
    }
}
