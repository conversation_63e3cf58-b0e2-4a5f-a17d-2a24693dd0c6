﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Asset
{
    [SugarTable("asset_substation_data_point_config")]
    public class SubstationDataPointConfig : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "substation_id", IsNullable = false)]
        public int SubstationId { get; set; }
        [SugarColumn(ColumnName = "custom_data_point_id", IsNullable = false)]
        public int CustomDataPointId { get; set; }
        [SugarColumn(ColumnName = "layout_name", Length = 256, IsNullable = false)]
        public string LayoutName { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "layout_format", Length = 50, ColumnDataType = "varchar", IsNullable = false)]
        public LayoutFormat LayoutFormat { get; set; }
    }

    
}
