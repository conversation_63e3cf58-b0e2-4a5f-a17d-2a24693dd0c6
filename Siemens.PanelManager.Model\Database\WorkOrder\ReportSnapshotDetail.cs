﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.WorkOrder
{
    [SugarTable("report_snapshot_detail")]
    public class ReportSnapshotDetail : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [SugarColumn(ColumnName = "report_id", IsNullable = true)]
        public int? ReportId { get; set; }

        [SugarColumn(ColumnName = "report_type", IsNullable = false)]
        public SnapshotReportType ReportType { get; set; }

        [SugarColumn(ColumnName = "asset_id", IsNullable = true)]
        public int? AssetId { get; set; }

        [SugarColumn(ColumnName = "asset_name", IsNullable = true, Length = 256)]
        public string AssetName { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "asset_number", IsNullable = true, Length = 256)]
        public string AssetNumber { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "buy_no", IsNullable = true, Length = 256)]
        public string BuyNo { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "position", IsNullable = true, Length = 256)]
        public string Position { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "indicator_info", IsNullable = true, Length = 256)]
        public string IndicatorInfo { get; set; } = string.Empty;
    }

    public enum SnapshotReportType : int
    {
        Order = 1,
        Panel = 2,
        Breaker = 3
    }
}
