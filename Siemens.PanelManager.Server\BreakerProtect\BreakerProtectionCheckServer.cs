﻿using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Model.Database.Asset;

namespace Siemens.PanelManager.Server.BreakerProtect
{
    public class BreakerProtectionCheckServer
    {
        private readonly ILogger<BreakerProtectionCheckServer> _logger;

        private IServiceProvider _provider;

        public BreakerProtectionCheckServer(ILogger<BreakerProtectionCheckServer> logger, IServiceProvider provider)
        {
            _logger = logger;
            _provider = provider;
        }

        /// <summary>
        /// 相邻上下级断路器选择性校验
        /// </summary>
        /// <param name="upperBreakerId">直接上级断路器id</param>
        /// <param name="lowerBreakerId">下级断路器id</param>
        public BreakerCheckResult? Check(int upperBreakerId, int lowerBreakerId, List<BreakerProtectionSetting> breakers, List<DeviceDetails> assetDetails, bool needGenerateChartData = true)
        {
            BreakerProtectionSetting? upperBreaker = null;
            DeviceDetails? upperDetail = null;
            BreakerProtectionSetting? lowerBreaker = null;
            DeviceDetails? lowerDetail = null;

            if (breakers == null
                || assetDetails == null)
            {
                return null;
            }

            upperBreaker = breakers.FirstOrDefault(a => a.AssetId == upperBreakerId);
            upperDetail = assetDetails.FirstOrDefault(a => a.AssetId == upperBreakerId);

            lowerBreaker = breakers.FirstOrDefault(a => a.AssetId == lowerBreakerId);
            lowerDetail = assetDetails.FirstOrDefault(a => a.AssetId == lowerBreakerId);

            if (upperBreaker == null
                || upperDetail == null
                || lowerBreaker == null
                || lowerDetail == null)
            {
                return null;
            }

            BreakerCheckResult breakerCheck = new BreakerCheckResult();
            var checkData = new List<(decimal x, decimal y1, decimal y2)>();
            var crossSection = new decimal[2];
            var errorSpan = new List<string>();
            bool checkPass = true;

            // 长延时计算公式: t = 36*tr*IR*IR/(I*I)或者1296*tr*IR*IR**IR*IR/(I*I*I*I)
            // 短延时计算公式: t = 144*tsd*IR*IR/(I*I) 或者Fix设定值
            // 瞬时计算：


            // 短延时起点（Isd)也是长延时的终点
            decimal upperIsd = (upperBreaker?.ST_ISD ?? string.Empty).ProtectValueToDecimal(.0M);
            decimal upperIi = upperBreaker?.INST_ONOFF == "1" ? (upperBreaker?.INST_II ?? string.Empty).ProtectValueToDecimal(.0M) : .0M;

            decimal upperIr = (upperBreaker?.LT_IR ?? string.Empty).ProtectValueToDecimal(.0M);
            decimal upperTr = (upperBreaker?.LT_TR ?? string.Empty).ProtectValueToDecimal(.0M);
            decimal upperTsd = (upperBreaker?.ST_TSD ?? string.Empty).ProtectValueToDecimal(.0M) / 1000M;
            // x轴（长延时）起点，Ir; 短延时起点（Isd), 瞬时起点：
            var x1LStart = upperIr;

            // 短延时起点（Isd)也是长延时的终点
            decimal lowerIsd = (lowerBreaker?.ST_ISD ?? string.Empty).ProtectValueToDecimal(.0M);
            decimal lowerIi = lowerBreaker?.INST_ONOFF == "1" ? (lowerBreaker?.INST_II ?? string.Empty).ProtectValueToDecimal(.0M) : .0M;

            decimal lowerIr = (upperBreaker?.LT_IR ?? string.Empty).ProtectValueToDecimal(.0M);
            decimal lowerTr = (lowerBreaker?.LT_TR ?? string.Empty).ProtectValueToDecimal(.0M);
            decimal lowerTsd = (lowerBreaker?.ST_TSD ?? string.Empty).ProtectValueToDecimal(.0M) / 1000M;

            // x轴（长延时）起点，Ir，短延时起点（Isd), 瞬时起点：
            var x2LStart = lowerIr;



            //一下起点与终点只针对长延时和短延时，不包括瞬时
            var joinStartPoint = Math.Min(x1LStart, x2LStart);
            var joinEndPoint = Math.Max(upperIi, lowerIi) + 2000.0M;
            var breakerSpan = new string[] { "L", "S", "I" };

            // 计算相交区域数据
            while (joinStartPoint <= joinEndPoint)
            {
                decimal upperT = 0;
                decimal lowerT = 0;
                string upperErrorSpan = string.Empty;
                string lowerErrorSpan = string.Empty;


                //上级断路器
                if (joinStartPoint < upperIsd)
                {
                    upperT = LongTrip(upperTr, upperIr, joinStartPoint);
                    upperErrorSpan = breakerSpan[0];
                }
                else if (joinStartPoint >= upperIsd && joinStartPoint < upperIi)
                {
                    upperT = ShortTrip(upperTsd, upperIr, joinStartPoint);
                    upperErrorSpan = breakerSpan[1];
                }
                else
                {
                    upperT = 0.02M;
                    upperErrorSpan = breakerSpan[2];
                }

                //下级断路器
                if (joinStartPoint < lowerIsd)
                {
                    lowerT = LongTrip(lowerTr, lowerIr, joinStartPoint);
                    lowerErrorSpan = breakerSpan[0];
                }
                else if (joinStartPoint >= lowerIsd && joinStartPoint < lowerIi)
                {
                    lowerT = ShortTrip(lowerTsd, lowerIr, joinStartPoint);
                    lowerErrorSpan = breakerSpan[1];
                }
                else
                {
                    lowerT = 0.02M;
                    lowerErrorSpan = breakerSpan[2];
                }

                if (lowerT >= upperT && joinStartPoint < lowerIi)
                {
                    checkPass = false;
                    crossSection[0] = joinStartPoint - 200;
                    crossSection[1] = joinStartPoint;

                    if (!errorSpan.Contains(upperErrorSpan))
                    {
                        errorSpan.Add(upperErrorSpan);
                    }

                    if (!errorSpan.Contains(lowerErrorSpan))
                    {
                        errorSpan.Add(lowerErrorSpan);
                    }
                }

                if (needGenerateChartData)
                {
                    checkData.Add((joinStartPoint, upperT, lowerT));
                }

                joinStartPoint += 200;
            }

            if (needGenerateChartData)
            {
                var extraData = new List<decimal> { upperIsd, upperIi, lowerIsd, lowerIi };

                extraData.ForEach(extra =>
                {
                    decimal upperT = 0;
                    decimal lowerT = 0;


                    //上级断路器
                    if (extra < upperIsd)
                    {
                        upperT = LongTrip(upperTr, upperIr, extra);
                    }
                    else if (extra >= upperIsd && extra < upperIi)
                    {
                        upperT = ShortTrip(upperTsd, upperIr, extra);
                    }
                    else
                    {
                        upperT = 0.02M;
                    }

                    //下级断路器
                    if (extra < lowerIsd)
                    {
                        lowerT = LongTrip(lowerTr, lowerIr, extra);
                    }
                    else if (extra >= lowerIsd && extra < lowerIi)
                    {
                        lowerT = ShortTrip(lowerTsd, lowerIr, extra);
                    }
                    else
                    {
                        lowerT = 0.02M;
                    }

                    checkData.Add((extra, upperT, lowerT));
                });


                if (checkData.Any())
                {
                    checkData = checkData.GroupBy(a => a.x).Select(a => a.First()).OrderBy(a => a.x).ToList();
                }
            }

            breakerCheck.CheckPass = checkPass;
            breakerCheck.CrossSection = crossSection;
            breakerCheck.CheckData = checkData;
            breakerCheck.ErrorSpan = errorSpan;

            var breakerType = typeof(BreakerProtectionSetting);
            var allOnOffNames = new string[] { "LTN_ONOFF", "ST_ONOFF", "INST_ONOFF", "GF_PROTECTION_ONOFF" };
            foreach (var onOffName in allOnOffNames)
            {
                var tempName = breakerType.GetProperty(onOffName);
                if (tempName != null)
                {
                    if (upperBreaker != null && tempName.GetValue(upperBreaker, null)?.ToString() == "1")
                    {
                        breakerCheck.UpSettings.Add(onOffName.Substring(0, 2));
                    }

                    if (lowerBreaker != null && tempName.GetValue(lowerBreaker, null)?.ToString() == "1")
                    {
                        breakerCheck.LowerSettings.Add(onOffName.Substring(0, 2));
                    }
                }
            }

            return breakerCheck;
        }

        /// <summary>
        /// 长延时时间计算公式
        /// </summary>
        /// <param name="tr">设定值Tr</param>
        /// <param name="ir">设定值Ir</param>
        /// <param name="i">电流</param>
        /// <returns></returns>
        public decimal LongTrip(decimal tr, decimal ir, decimal i)
        {
            //长延时计算公式: t = 36*tr*IR*IR/(I*I)
            if (i <= 0)
            {
                return 0;
            }

            return Math.Round((36 * tr * ir * ir) / (i * i), 2);
        }

        /// <summary>
        /// 短延时时间计算公式
        /// </summary>
        /// <param name="tsd">设定值Tsd</param>
        /// <param name="ir">设定值Isd</param>
        /// <param name="i">电流</param>
        /// <returns></returns>
        public decimal ShortTrip(decimal tsd, decimal ir, decimal i, bool isFix = false)
        {
            // 短延时计算公式: t = 144*tsd*ir*ir/(I*I) 或者Fix设定值
            if (i <= 0)
            {
                return 0;
            }

            if (isFix)
            {
                return tsd;
            }

            return Math.Max(Math.Round((144 * tsd * ir * ir) / (i * i), 2), tsd);
        }
    }

    public class BreakerCheckResult
    {
        public List<(decimal x, decimal y1, decimal y2)> CheckData { get; set; } = new List<(decimal x, decimal y1, decimal y2)> { };
        public bool CheckPass { get; set; }
        public decimal[] CrossSection { get; set; } = new decimal[0];
        public List<string> ErrorSpan { get; set; } = new List<string>();
        public List<string> UpSettings { get; set; } = new List<string>();
        public List<string> LowerSettings { get; set; } = new List<string>();
    }

    public static class ProtectCheckExtension
    {
        public static decimal ProtectValueToDecimal(this string? protectValue, decimal parseErrorSetDefault = 0.0M)
        {
            if (string.IsNullOrWhiteSpace(protectValue) || !decimal.TryParse(protectValue, out var tempValue))
            {
                return parseErrorSetDefault;
            }
            else
            {
                return (tempValue > 0 ? tempValue : parseErrorSetDefault);
            }
        }
    }
}
