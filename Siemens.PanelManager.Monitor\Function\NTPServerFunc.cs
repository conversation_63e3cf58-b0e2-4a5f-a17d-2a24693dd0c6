﻿using Siemens.PanelManager.Monitor.Cmd;

namespace Siemens.PanelManager.Monitor.Function
{
    public static class NTPServerFunc
    {
        public static async Task<string> GetCurrentNTPServer(ILogger logger)
        {
            var ipAddress = await ChangeNTPServerCmd.GetCurrentNTPServer(logger);
            return ipAddress;
        }

        public static async Task SetNTPServer(string ipAddress, ILogger logger)
        {
            await ChangeNTPServerCmd.SetNTPServer(ipAddress, logger);
            await ChangeNTPServerCmd.ReloadNTPConfig(logger);
            await ChangeNTPServerCmd.RestartChrony(logger);
        }

        public static async Task RestartChrony(ILogger logger)
        {
            await ChangeNTPServerCmd.RestartChrony(logger);
        }
    }
}
