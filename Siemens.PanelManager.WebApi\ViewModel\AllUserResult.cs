﻿using Siemens.PanelManager.Model.Database.Auth;
using Siemens.PanelManager.WebApi.StaticContent;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class AllUserResult
    {
        public AllUserResult(User[] users, UserRoleMapping[] userRoleMappings, Role[] roles, MessageContext messageContext)
        {
            #region Order by
            users = users.OrderByDescending(u => u.CreatedTime).ToArray();
            #endregion
            var usersList = new List<GetUserInfo>();
            foreach(var user in users) 
            {
                var roleIds = userRoleMappings
                    .Where(urm => urm.UserId == user.Id)
                    .Select(urm => urm.RoleId)
                    .ToArray();

                var tempRoles = roles.Where(r=>roleIds.Contains(r.Id)).ToArray();
                usersList.Add(new GetUserInfo(user, tempRoles, messageContext));
            }
            Items = usersList.ToArray();
            Total = Items.Length;
        }
        public GetUserInfo[] Items { get; set; }
        public int Total { get; set; }
    }

    public class GetUserInfo
    {
        public GetUserInfo(User user, Role[] roles, MessageContext messageContext)
        {
            Id = user.Id;
            UserName = user.LoginName;
            PersonName = user.UserName;
            Email = user.EmailAddress ?? string.Empty;
            PrefixTel = user.PrefixMobileNumber ?? string.Empty;
            Tel = user.MobileNumber ?? string.Empty;
            GmtCreate = user.CreatedTime;
            GmtModified = user.UpdatedTime;
            if (roles != null && roles.Length > 0)
            {
                var rolesList = new List<UserRoleInfo>();
                foreach (var r in roles)
                {
                    rolesList.Add(new UserRoleInfo()
                    {
                        Id = r.Id,
                        Name = messageContext.GetRoleName(r.RoleCode, r.RoleName)
                    });
                }

                Roles = rolesList.ToArray();
            }
        }

        public int Id { get; set; }
        public string UserName { get; set; }
        public string PersonName { get; set; }
        public string Email { get; set; } = string.Empty;
        public string PrefixTel { get; set; } = string.Empty;
        public string Tel { get; set; } = string.Empty;
        public DateTime GmtCreate { get; set; }
        public DateTime GmtModified { get; set;}
        public UserRoleInfo[]? Roles { get; set; }
    }
}
