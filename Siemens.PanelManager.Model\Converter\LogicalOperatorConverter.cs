﻿using Newtonsoft.Json;
using Siemens.PanelManager.Model.Emun;

namespace Siemens.PanelManager.Model.Converter
{
    internal class LogicalOperatorConverter : JsonConverter
    {
        public override bool CanConvert(Type objectType)
        {
            return typeof(LogicalOperator).IsAssignableFrom(objectType);
        }

        public override object ReadJson(JsonReader reader, Type objectType, object? existingValue, JsonSerializer serializer)
        {
            if (reader.ValueType == typeof(string))
            {
                if (reader.Value != null && reader.Value is string value && value != null)
                {
                    return value.ToLogicalOperator();
                }
            }

            return LogicalOperator.None;
        }

        public override void WriteJson(JsonWriter writer, object? value, JsonSerializer serializer)
        {
            if (value is LogicalOperator opt)
            {
                writer.WriteValue(opt.ToLogicalOperatorString());
            }
        }
    }
}
