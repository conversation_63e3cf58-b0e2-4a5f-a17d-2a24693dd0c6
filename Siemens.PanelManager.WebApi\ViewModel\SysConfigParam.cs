﻿
using Siemens.PanelManager.Common.mqtt;
using Siemens.PanelManager.Model.Database.System;
using SqlSugar;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    /// <summary>
    /// 系统配置入参
    /// </summary>
    public class SysConfigParam
    {
        /// <summary>
        /// 是否启动停止
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// 是否自动启用(0:不启用 1:启用)
        /// </summary>
        public string? AutoStart { get; set; } = "0";

        /// <summary>
        /// ip地址
        /// </summary>
        public string? Broker { get; set; }

        /// <summary>
        /// 端口号
        /// </summary>
        public string? Port { get; set; }

        /// <summary>
        /// ClientId
        /// </summary>
        public string? ClientId { get; set; }

        /// <summary>
        /// 是否使用证书(0:不使用，1：使用)
        /// </summary>
        public string? UseTLS { get; set; }

        /// <summary>
        /// Tls文件路径
        /// </summary>
        public string? TLSPemFile { get; set; }

        /// <summary>
        /// 验证类型(0:基本身份认证:1:TLs客户端证书认证)
        /// </summary>
        public string? AuthenticationType { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string? Password { get; set; }

        /// <summary>
        /// mqtt主题
        /// </summary>
        public string? Topic { get; set; }

        /// <summary>
        /// ClientPemFile证书路径
        /// </summary>
        public string? ClientPemFile { get; set; }

        /// <summary>
        /// ClientPrivateFile证书路径
        /// </summary>
        public string? ClientPrivateFile { get; set; }

        public async Task<MqttConfig> Save(ISqlSugarClient sqlSugarClient, string user)
        {
            var result = new MqttConfig();
            var configs = await sqlSugarClient.Queryable<SystemConfig>().Where(c => c.Type == "MQTT").ToListAsync();

            #region AutoStart
            {
                var config = configs.FirstOrDefault(c=> "AutoStart".Equals(c.Name));
                if (config == null)
                {
                    config = new SystemConfig
                    {
                        Name = "AutoStart",
                        CreatedBy = user,
                        CreatedTime = DateTime.Now,
                        Extend = null,
                        Sort = 0,
                        Type = "MQTT",
                        Value = AutoStart ?? "0",
                    };

                    configs.Add(config);
                }

                config.Value = AutoStart ?? "0";
                config.UpdatedBy = user;
                config.UpdatedTime = DateTime.Now;
                result.AutoStart = config.Value;
            }
            #endregion

            #region Broker
            {
                var config = configs.FirstOrDefault(c => "Broker".Equals(c.Name));
                if (config == null)
                {
                    config = new SystemConfig
                    {
                        Name = "Broker",
                        CreatedBy = user,
                        CreatedTime = DateTime.Now,
                        Extend = null,
                        Sort = 0,
                        Type = "MQTT",
                        Value = Broker ?? string.Empty,
                    };

                    configs.Add(config);
                }

                config.Value = Broker ?? string.Empty;
                config.UpdatedBy = user;
                config.UpdatedTime = DateTime.Now;
                result.Broker = config.Value;
            }
            #endregion

            #region Port
            {
                var config = configs.FirstOrDefault(c => "Port".Equals(c.Name));
                if (config == null)
                {
                    config = new SystemConfig
                    {
                        Name = "Port",
                        CreatedBy = user,
                        CreatedTime = DateTime.Now,
                        Extend = null,
                        Sort = 0,
                        Type = "MQTT",
                        Value = Port ?? "0",
                    };

                    configs.Add(config);
                }

                config.Value = Port ?? "0";
                config.UpdatedBy = user;
                config.UpdatedTime = DateTime.Now;

                result.Port = config.Value;
            }
            #endregion

            #region ClientId
            {
                var config = configs.FirstOrDefault(c => "ClientId".Equals(c.Name));
                if (config == null)
                {
                    config = new SystemConfig
                    {
                        Name = "ClientId",
                        CreatedBy = user,
                        CreatedTime = DateTime.Now,
                        Extend = null,
                        Sort = 0,
                        Type = "MQTT",
                    };

                    configs.Add(config);
                }

                config.Value = ClientId ?? string.Empty;
                config.UpdatedBy = user;
                config.UpdatedTime = DateTime.Now;

                result.ClientId = config.Value;
            }
            #endregion

            #region UseTLS
            {
                var config = configs.FirstOrDefault(c => "UseTLS".Equals(c.Name));
                if (config == null)
                {
                    config = new SystemConfig
                    {
                        Name = "UseTLS",
                        CreatedBy = user,
                        CreatedTime = DateTime.Now,
                        Extend = null,
                        Sort = 0,
                        Type = "MQTT",
                    };

                    configs.Add(config);
                }

                config.Value = UseTLS ?? "0";
                config.UpdatedBy = user;
                config.UpdatedTime = DateTime.Now;

                result.UseTLS = config.Value;
            }
            #endregion

            #region AuthenticationType
            {
                var config = configs.FirstOrDefault(c => "AuthenticationType".Equals(c.Name));
                if (config == null)
                {
                    config = new SystemConfig
                    {
                        Name = "AutoStatus",
                        CreatedBy = user,
                        CreatedTime = DateTime.Now,
                        Extend = null,
                        Sort = 0,
                        Type = "MQTT",
                    };

                    configs.Add(config);
                }

                config.Value = AuthenticationType ?? "0";
                config.UpdatedBy = user;
                config.UpdatedTime = DateTime.Now;

                result.AuthenticationType = config.Value;
            }
            #endregion

            #region UserName
            {
                var config = configs.FirstOrDefault(c => "UserName".Equals(c.Name));
                if (config == null)
                {
                    config = new SystemConfig
                    {
                        Name = "UserName",
                        CreatedBy = user,
                        CreatedTime = DateTime.Now,
                        Extend = null,
                        Sort = 0,
                        Type = "MQTT",
                    };

                    configs.Add(config);
                }

                config.Value = UserName ?? string.Empty;
                config.UpdatedBy = user;
                config.UpdatedTime = DateTime.Now;

                result.UserName = config.Value;
            }
            #endregion

            #region Password
            {
                var config = configs.FirstOrDefault(c => "Password".Equals(c.Name));
                if (config == null)
                {
                    config = new SystemConfig
                    {
                        Name = "Password",
                        CreatedBy = user,
                        CreatedTime = DateTime.Now,
                        Extend = null,
                        Sort = 0,
                        Type = "MQTT",
                    };

                    configs.Add(config);
                }

                config.Value = Password ?? string.Empty;
                config.UpdatedBy = user;
                config.UpdatedTime = DateTime.Now;

                result.Password = config.Value;
            }
            #endregion

            #region Topic
            {
                var config = configs.FirstOrDefault(c => "Topic".Equals(c.Name));
                if (config == null)
                {
                    config = new SystemConfig
                    {
                        Name = "AutoStatus",
                        CreatedBy = user,
                        CreatedTime = DateTime.Now,
                        Extend = null,
                        Sort = 0,
                        Type = "MQTT",
                    };

                    configs.Add(config);
                }

                config.Value = Topic ?? string.Empty;
                config.UpdatedBy = user;
                config.UpdatedTime = DateTime.Now;

                result.Topic = config.Value;
            }
            #endregion

            await sqlSugarClient.Storageable(configs).ExecuteCommandAsync();
            return result;
        }
    }
}
