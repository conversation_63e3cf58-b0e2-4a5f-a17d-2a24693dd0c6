﻿using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Model.Database.Alarm;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.ExportAlarm
{
    internal class ExportAlarmModel
    {

        public ExportAlarmModel(AlarmLog log, AlarmRule? rule, IMessageContext messageContext)
        {
            Id = log.Id;
            StringBuilder position = new StringBuilder(10);
            var targetName = string.Empty;
            if (!string.IsNullOrEmpty(log.SubstationName))
            {
                position.Append(log.SubstationName);
                targetName = log.SubstationName;
            }
            if (!string.IsNullOrEmpty(log.PanelName))
            {
                if (position.Length > 0)
                {
                    position.Append('/');
                }
                position.Append(log.PanelName);
                targetName = log.PanelName;
            }
            if (!string.IsNullOrEmpty(log.CircuitName))
            {
                if (position.Length > 0)
                {
                    position.Append('/');
                }
                position.Append(log.CircuitName);
                targetName = log.CircuitName;
            }
            if (!string.IsNullOrEmpty(log.DeviceName))
            {
                if (position.Length > 0)
                {
                    position.Append('/');
                }
                position.Append(log.DeviceName);
                targetName = log.DeviceName;
            }
            Position = position.ToString();
            Message = log.Message;
            TargetName = targetName;
            AlarmLevel = log.Severity.ToExportString(messageContext);

            LogTime = log.CreatedTime;
            EventType = log.EventType.ToExportString(messageContext);
            Status = log.Status.ToExportString(messageContext);
            if (rule != null)
            {
                RuleId = rule.Id.ToString();
                AlarmRuleName = rule.Name;
                Rule = rule.RuleInfo;
                Source = $"{AlarmRuleName}:{Rule}";
            }
            else if(log.EventType != AlarmEventType.OperationLog) 
            {
                Source = "UDC";
            }

            var status = log.AssetStatus;
            if (status != null)
            {
                GetAssetStatus(status);
            }
        }

        public long Id { get; set; }
        public string Position { get; set; } = string.Empty;
        public string Message { get; set; }= string.Empty;
        public string TargetName { get; set; } = string.Empty;
        public string AlarmLevel { get; set; } = string.Empty;
        public string RuleId { get; set; } = string.Empty;
        public DateTime LogTime { get; set; }
        public string EventType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string AlarmRuleName { get; set; } = string.Empty;
        public string Rule { get; set; } = string.Empty;
        public string Source { get;set; } = string.Empty;

        #region Asset Status
        public string Ua { get; set; } = string.Empty;
        public string Ub { get; set; } = string.Empty;
        public string Uc { get; set; } = string.Empty;
        public string Uab { get; set; } = string.Empty;
        public string Ubc { get; set; } = string.Empty;
        public string Uca { get; set; } = string.Empty;
        public string La { get; set; } = string.Empty;
        public string Lb { get; set; } = string.Empty;
        public string Lc { get; set; } = string.Empty;
        public string THD_Ua { get; set; } = string.Empty;
        public string THD_Ub { get; set; } = string.Empty;
        public string THD_Uc { get; set; } = string.Empty;
        public string THD_La { get; set; } = string.Empty;
        public string THD_Lb { get; set; } = string.Empty;
        public string THD_Lc { get; set; } = string.Empty;
        public string P { get; set; } = string.Empty;
        public string Q { get; set; } = string.Empty;
        public string S { get; set; } = string.Empty;
        public string F { get; set; } = string.Empty;

        /// <summary>
        /// 正向有功
        /// </summary>
        public string ForwardActivePower { get;set;} = string.Empty;
        /// <summary>
        /// 正向无功
        /// </summary>
        public string ForwardReactivePower { get; set; } = string.Empty;
        /// <summary>
        /// 反向有功
        /// </summary>
        public string ReverseActivePower { get; set; } = string.Empty;
        /// <summary>
        /// 反向无功
        /// </summary>
        public string ReverseReactivePower { get; set; } = string.Empty;

        /// <summary>
        /// L1 相位角
        /// </summary>
        public string PhaseShift_L1 { get; set; } = string.Empty;
        /// <summary>
        /// L2 相位角
        /// </summary>
        public string PhaseShift_L2 { get; set; } = string.Empty;
        /// <summary>
        /// L3 相位角
        /// </summary>
        public string PhaseShift_L3 { get; set; } = string.Empty;
        /// <summary>
        /// 三相不平衡
        /// 电压幅值不平衡度
        /// </summary>
        public string ThreePhaseUnbalance_Voltage { get; set; } = string.Empty;
        /// <summary>
        /// 三相不平衡
        /// 电流幅值不平衡度
        /// </summary>
        public string ThreePhaseUnbalance_Electricity { get; set; } = string.Empty;
        /// <summary>
        /// 三相不平衡
        /// 电压幅值不平衡度最大值
        /// </summary>
        public string ThreePhaseUnbalance_MaxVoltage { get; set; } = string.Empty;
        /// <summary>
        /// 三相不平衡
        /// 电流幅值不平衡度最大值
        /// </summary>
        public string ThreePhaseUnbalance_MaxElectricity { get; set; } = string.Empty;
        /// <summary>
        /// 电压闪变
        /// 骤降次数
        /// </summary>
        public string VoltageFlicker_DropsNumber { get; set; } = string.Empty;
        /// <summary>
        /// 电压闪变
        /// 骤升次数
        /// </summary>
        public string VoltageFlicker_SurgesNumber { get; set; } = string.Empty;
        /// <summary>
        /// 电压闪变
        /// 中断次数
        /// </summary>
        public string VoltageFlicker_InterruptionsNumber { get; set; } = string.Empty;
        /// <summary>
        /// Cosφ
        /// </summary>
        public string CosPhi { get; set; } = string.Empty;
        /// <summary>
        /// 健康评分
        /// </summary>
        public string HealthScore { get; set; } = string.Empty;
        /// <summary>
        /// 剩余寿命
        /// </summary>
        public string RemainingLife { get; set; } = string.Empty;

        /// <summary>
        /// 磨损率
        /// </summary>
        public string WearRate { get;set; } = string.Empty;
        /// <summary>
        /// 温度
        /// </summary>
        public string Temperature { get; set; } = string.Empty;
        #endregion

        private void GetAssetStatus(Dictionary<string, string> assetStatus)
        {
            foreach (var item in assetStatus)
            {
                UpdateValue(item);
            }
        }

        private void UpdateValue(KeyValuePair<string, string> item)
        {
            switch (item.Key)
            {
                case "Ua":
                    Ua = item.Value;
                    return;
                case "Ub":
                    Ub = item.Value;
                    return;
                case "Uc":
                    Uc = item.Value;
                    return;
                case "Uab":
                    Uab = item.Value;
                    return;
                case "Ubc":
                    Ubc = item.Value;
                    return;
                case "Uca":
                    Uca = item.Value;
                    return;
                case "La":
                    La = item.Value;
                    return;
                case "Lb":
                    Lb = item.Value;
                    return;
                case "Lc":
                    Lc = item.Value;
                    return;
                case "THD_La":
                    THD_La = item.Value;
                    return;
                case "THD_Lb":
                    THD_Lb = item.Value;
                    return;
                case "THD_Lc":
                    THD_Lc = item.Value;
                    return;
                case "THD_Ua":
                    THD_Ua = item.Value;
                    return;
                case "THD_Ub":
                    THD_Ub = item.Value;
                    return;
                case "THD_Uc":
                    THD_Uc = item.Value;
                    return;
                case "P":
                    P = item.Value;
                    return;
                case "Q":
                    Q = item.Value;
                    return;
                case "F":
                    F = item.Value;
                    return;
                case "ForwardActivePower":
                    ForwardActivePower = item.Value;
                    return;
                case "ForwardReactivePower":
                    ForwardReactivePower = item.Value;
                    return;
                case "ReverseActivePower":
                    ReverseActivePower = item.Value;
                    return;
                case "ReverseReactivePower":
                    ReverseReactivePower = item.Value;
                    return;
                case "PhaseShift_L1":
                    PhaseShift_L1 = item.Value;
                    return;
                case "PhaseShift_L2":
                    PhaseShift_L2 = item.Value;
                    return;
                case "PhaseShift_L3":
                    PhaseShift_L3 = item.Value;
                    return;
                case "ThreePhaseUnbalance_Voltage":
                    ThreePhaseUnbalance_Voltage = item.Value;
                    return;
                case "ThreePhaseUnbalance_Electricity":
                    ThreePhaseUnbalance_Electricity = item.Value;
                    return;
                case "ThreePhaseUnbalance_MaxVoltage":
                    ThreePhaseUnbalance_MaxVoltage = item.Value;
                    return;
                case "ThreePhaseUnbalance_MaxElectricity":
                    ThreePhaseUnbalance_MaxElectricity = item.Value;
                    return;
                case "VoltageFlicker_DropsNumber":
                    VoltageFlicker_DropsNumber = item.Value;
                    return;
                case "VoltageFlicker_SurgesNumber":
                    VoltageFlicker_SurgesNumber = item.Value;
                    return;
                case "VoltageFlicker_InterruptionsNumber":
                    VoltageFlicker_InterruptionsNumber = item.Value;
                    return;
                case "CosPhi":
                    CosPhi = item.Value;
                    return;
                case "HealthScore":
                    HealthScore = item.Value;
                    return;
                case "RemainingLife":
                    RemainingLife = item.Value;
                    return;
                case "WearRate":
                    WearRate = item.Value;
                    return;
                case "Temperature":
                    Temperature = item.Value;
                    return;
                default: return;
            }
        }
    }
}


