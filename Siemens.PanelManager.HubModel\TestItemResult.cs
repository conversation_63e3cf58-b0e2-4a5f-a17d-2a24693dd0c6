﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.HubModel
{
    public class TestItemResult
    {
        public TestItemResult()
        {
            Code = string.Empty;
            LogTime = DateTime.Now;
        }

        public TestItemResult(string itemCode)
        {
            Code = itemCode;
            LogTime = DateTime.Now;
        }

        public string Code { get; set; }
        public string Message { get; set; } = string.Empty;
        public int Result { get; set; }
        public int Status { get; set; }
        public DateTime LogTime { get; set; }
    }
    public enum ItemResult : int
    {
        None = 0,
        OK = 10,
        Fail = 90,
    }

    public enum ItemStatus: int
    {
        None = 0,
        InProcess = 10,
        Finish = 50,
        Error = 999,
    }
}
