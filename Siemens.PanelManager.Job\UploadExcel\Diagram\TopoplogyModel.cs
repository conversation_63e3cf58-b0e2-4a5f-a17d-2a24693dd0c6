﻿using Newtonsoft.Json;
using Siemens.PanelManager.Model.Topology;

namespace Siemens.PanelManager.Job.UploadExcel.Diagram
{
    /// <summary>
    /// 单线图最外层结构
    /// </summary>
    internal class TopologyModel
    {
        [JsonProperty("time")]
        public string Time { get; set; } = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        [JsonProperty("owner")]
        public string CreatedBy { get; set; } = string.Empty;
        [JsonProperty("topology")]
        public MainDigram? Topology { get; set; }
    }
}
