﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Siemens.PanelManager.Common;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.Topology;
using Siemens.PanelManager.Model.Database.Topoplogy;
using Siemens.PanelManager.Model.Topology;
using SqlSugar;

namespace Siemens.PanelManager.Server.Topoplogy
{
    public class TopoplogySplitService
    {
        private IServiceProvider _provider;
        public TopoplogySplitService(IServiceProvider provider) 
        {
            _provider = provider;
        }

        public async Task SplitPanelTopoplogy(ISqlSugarClient client, DateTime time)
        {
            var ts = DateTime.Now.GetTimestampForSec();

            var assets = await client.Queryable<AssetInfo>()
                .Where(a => a.AssetLevel == AssetLevel.Substation)
                .ToListAsync();

            var topoplogyIds = assets.Select(a => a.TopologyId).ToList();
            var assetIds = assets.Select(a => a.Id).ToList();

            if (topoplogyIds != null && topoplogyIds.Count > 0)
            {
                var topologyList = await client.Queryable<TopologyInfo>()
                    .Where(t => t.Type == "topology" && t.UpdatedTime > time && topoplogyIds.Contains(t.Id))
                    .ToArrayAsync();

                var assetRelations = await client.Queryable<AssetRelation>()
                    .Where(ar => assetIds.Contains(ar.ParentId) && ar.AssetLevel == AssetLevel.Panel)
                    .ToArrayAsync();

                foreach (var topology in topologyList)
                {
                    var substation = assets.Where(a => a.TopologyId == topology.Id).FirstOrDefault();
                    if (substation == null) continue;

                    var panels = assetRelations
                        .Where(ar => ar.ParentId == substation.Id)
                        .Select(ar => ar.ChildId)
                        .ToList();

                    var jObject = JObject.Parse(topology.Data);

                    var topologyToken = jObject.GetValue("topology");
                    if (topologyToken == null) continue;

                    var topologyInfo = topologyToken.ToObject<MainDigram>();
                    var nodesToken = ((JObject)topologyToken).GetValue("nodeDataArray");
                    var linesToken = ((JObject)topologyToken).GetValue("linkDataArray");
                    if (nodesToken is JArray nodesArray && linesToken is JArray linesArray)
                    {
                        if (topologyInfo?.GroupKeyMappings != null)
                        {
                            foreach (var p in panels)
                            {
                                if (topologyInfo.GroupKeyMappings.TryGetValue(p.ToString(), out var mapping)
                                    && mapping.List != null)
                                {
                                    var panelDigram = new JObject();
                                    var nodes = nodesArray.Where(n => mapping.List.Contains(n.Value<int>("key"))).ToList();
                                    panelDigram.Add("nodeDataArray", JToken.FromObject(nodes));

                                    var lines = linesArray.Where(n => mapping.List.Contains(n.Value<int>("from")) && mapping.List.Contains(n.Value<int>("to"))).ToList();
                                    panelDigram.Add("linkDataArray", JToken.FromObject(lines));

                                    var tempPath = Path.Combine("D:\\", "temp");

                                    if (!Directory.Exists(tempPath))
                                    {
                                        Directory.CreateDirectory(tempPath);
                                    }

                                    var panelObj = new JObject();
                                    panelObj.Add("topology", JToken.FromObject(panelDigram));

                                    await client.Insertable(new SplitPanelInfo
                                    {
                                        TopologyId = topology.Id,
                                        AssetId = p,
                                        Data = JsonConvert.SerializeObject(panelObj),
                                        Timestamp = ts,
                                        CreatedBy = topology.UpdatedBy,
                                        CreatedTime = DateTime.Now,
                                        UpdatedBy = topology.UpdatedBy,
                                        UpdatedTime = DateTime.Now,
                                    }).ExecuteCommandAsync();
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
