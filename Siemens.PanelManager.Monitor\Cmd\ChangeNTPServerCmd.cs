﻿using CliWrap;
using CliWrap.Buffered;
using System.Text;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.Monitor.Cmd
{
    public static class ChangeNTPServerCmd
    {
        //# echo 'server ********* iburst' > /etc/chrony/sources.d/local-ntp-server.sources
        //# chronyc reload sources

        public static readonly string NTP_Server_Config_File = "/etc/chrony/sources.d/local-ntp-server.sources";

        public static async Task<string> GetCurrentNTPServer(ILogger logger)
        {
            Func<string, Task> func = (message) =>
            {
                logger.LogInformation(message);
                return Task.CompletedTask;
            };

            string ntpServerIPOrUrl = string.Empty;

            var result = await Cli.Wrap("cat")
                .WithArguments(a => a
                .Add(NTP_Server_Config_File))
                .WithStandardOutputPipe(PipeTarget.ToDelegate(func))
                .WithStandardErrorPipe(PipeTarget.ToDelegate(func))
                .WithValidation(CommandResultValidation.None)
                .ExecuteBufferedAsync(Encoding.UTF8);

            if (!string.IsNullOrWhiteSpace(result.StandardOutput))
            {
                Regex ipRegex = new Regex(@"\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b");
                var matchResult = ipRegex.Matches(result.StandardOutput);

                if (matchResult.Any())
                {
                    ntpServerIPOrUrl = matchResult[0].Value;
                }
                else
                {
                    Regex urlRegex = new Regex(@"(?<!\b(?:https?|ftp):\/\/)(?:\w+\.)+\w{2,3}(\/\S*)?");
                    var urlMatches = urlRegex.Matches(result.StandardOutput);

                    if (urlMatches.Any())
                    {
                        ntpServerIPOrUrl = urlMatches[0].Value;
                    }
                }
            }

            return ntpServerIPOrUrl;
        }

        public static async Task SetNTPServer(string ipAddress, ILogger logger)
        {
            Func<string, Task> func = (message) =>
            {
                logger.LogInformation(message);
                return Task.CompletedTask;
            };

            var result = await Cli.Wrap("sh")
                .WithArguments(a => a
                .Add("-c")
                .Add($"echo 'server {ipAddress} iburst' > {NTP_Server_Config_File}"))
                .WithStandardOutputPipe(PipeTarget.ToDelegate(func))
                .WithStandardErrorPipe(PipeTarget.ToDelegate(func))
                .WithValidation(CommandResultValidation.None)
                .ExecuteAsync();
        }

        public static async Task ReloadNTPConfig(ILogger logger)
        {
            Func<string, Task> func = (message) =>
            {
                logger.LogInformation(message);
                return Task.CompletedTask;
            };

            var result = await Cli.Wrap("chronyc")
                .WithArguments(a => a
                .Add("reload")
                .Add("sources"))
                .WithStandardOutputPipe(PipeTarget.ToDelegate(func))
                .WithStandardErrorPipe(PipeTarget.ToDelegate(func))
                .WithValidation(CommandResultValidation.None)
                .ExecuteAsync();
        }

        public static async Task RestartChrony(ILogger logger)
        {
            Func<string, Task> func = (message) =>
            {
                logger.LogInformation(message);
                return Task.CompletedTask;
            };

            var result = await Cli.Wrap("systemctl")
                .WithArguments(a => a
                .Add("restart")
                .Add("chrony"))
                .WithStandardOutputPipe(PipeTarget.ToDelegate(func))
                .WithStandardErrorPipe(PipeTarget.ToDelegate(func))
                .WithValidation(CommandResultValidation.None)
                .ExecuteAsync();
        }
    }
}
