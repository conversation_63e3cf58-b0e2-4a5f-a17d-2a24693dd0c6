﻿using Microsoft.AspNetCore.Mvc;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class EnergyQualityParam
    {
        [FromRoute(Name = "chartName")]
        public string ChartName { get; set; } = string.Empty;
        [FromQuery(Name = "device")]
        public string Device { get; set; } = string.Empty;
        [FromQuery(Name = "thdType")]
        public THDType[]? THDTypes { get; set; }
        [FromQuery(Name = "frequency")]
        public int Frequency { get; set; } = 1;
        [FromQuery(Name = "date")]
        public string? Date { get; set; }
    }

    public class EnergyQualityBatchParam
    {
        [FromQuery(Name = "chartName")]
        public string[] ChartNames { get; set; } = new string[0];
        [FromRoute(Name = "assetId")]
        public int AssetId { get; set; }
        [FromQuery(Name = "thdType")]
        public THDType[]? THDTypes { get; set; }
        [FromQuery(Name = "frequency")]
        public int? Frequency { get; set; }
        [FromQuery(Name = "date")]
        public string? Date { get; set; }
    }
}
