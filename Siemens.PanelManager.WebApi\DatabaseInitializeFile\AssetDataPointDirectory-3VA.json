[{"AssetLevel": 50, "AssetModel": "3VA", "Name": "Status", "ParentName": "", "LanguageKey": "Status", "Sort": 1}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "3VAState", "ParentName": "Status", "LanguageKey": "3VAState", "Sort": 2}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "ProtectionFunctionState", "ParentName": "3VAState", "LanguageKey": "ProtectionFunctionState", "Sort": 3}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "MeteringFunctionState", "ParentName": "3VAState", "LanguageKey": "MeteringFunctionState", "Sort": 4}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "AlarmState", "ParentName": "3VAState", "LanguageKey": "AlarmState", "Sort": 5}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "TripCounters", "ParentName": "Status", "LanguageKey": "TripCounters", "Sort": 6}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "MaintenanceInformation", "ParentName": "Status", "LanguageKey": "MaintenanceInformation", "Sort": 7}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "EFB_MMBState", "ParentName": "Status", "LanguageKey": "EFB_MMBState", "Sort": 8}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "EFB_MMBThresholdActivationStates", "ParentName": "EFB_MMBState", "LanguageKey": "EFB_MMBThresholdActivationStates", "Sort": 9}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "Current", "ParentName": "", "LanguageKey": "Current", "Sort": 10}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "Current_InstantaneousValues", "ParentName": "Current", "LanguageKey": "InstantaneousValues", "Sort": 11}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "Current_ActualInstantaneousMeasurementValues", "ParentName": "Current_InstantaneousValues", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 12}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "VoltageL-N", "ParentName": "", "LanguageKey": "VoltageL-N", "Sort": 13}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "InstantaneousValuesL-N", "ParentName": "VoltageL-N", "LanguageKey": "InstantaneousValuesL-N", "Sort": 14}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "VoltageL-N_ActualInstantaneousMeasurementValues", "ParentName": "InstantaneousValuesL-N", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 15}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "VoltageL-L", "ParentName": "", "LanguageKey": "VoltageL-L", "Sort": 16}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "InstantaneousValuesL-L", "ParentName": "VoltageL-L", "LanguageKey": "InstantaneousValuesL-L", "Sort": 17}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "VoltageL-L_ActualInstantaneousMeasurementValues", "ParentName": "InstantaneousValuesL-L", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 18}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "Power", "ParentName": "", "LanguageKey": "Power", "Sort": 19}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "ActivePower", "ParentName": "Power", "LanguageKey": "ActivePower", "Sort": 20}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "ActivePower_InstantaneousValues", "ParentName": "ActivePower", "LanguageKey": "InstantaneousValues", "Sort": 21}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "ActivePower_ActualInstantaneousMeasurementValues", "ParentName": "ActivePower_InstantaneousValues", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 22}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "ReactivePower", "ParentName": "Power", "LanguageKey": "ReactivePower", "Sort": 23}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "MeasuringMethodVARtot", "ParentName": "ReactivePower", "LanguageKey": "MeasuringMethodVARtot", "Sort": 24}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "MeasuringMethodVARtot_InstantaneousValues", "ParentName": "MeasuringMethodVARtot", "LanguageKey": "InstantaneousValues", "Sort": 25}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "MeasuringMethodVARtot_ActualInstantaneousMeasurementValues", "ParentName": "MeasuringMethodVARtot_InstantaneousValues", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 26}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ParentName": "Power", "LanguageKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Sort": 27}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "ApparentPower_InstantaneousValues", "ParentName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LanguageKey": "InstantaneousValues", "Sort": 28}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "ApparentPower_ActualInstantaneousMeasurementValues", "ParentName": "ApparentPower_InstantaneousValues", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 29}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "PowerFactor", "ParentName": "Power", "LanguageKey": "PowerFactor", "Sort": 30}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "PowerFactor_InstantaneousValues", "ParentName": "PowerFactor", "LanguageKey": "InstantaneousValues", "Sort": 31}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "PowerFactor_ActualInstantaneousMeasurementValues", "ParentName": "PowerFactor_InstantaneousValues", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 32}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "Energy", "ParentName": "", "LanguageKey": "Energy", "Sort": 33}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "ActiveEnergy", "ParentName": "Energy", "LanguageKey": "ActiveEnergy", "Sort": 34}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "ActiveEnergy_Import", "ParentName": "ActiveEnergy", "LanguageKey": "Import", "Sort": 35}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "ActiveEnergy_Export", "ParentName": "ActiveEnergy", "LanguageKey": "Export", "Sort": 36}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "ReactiveEnergy", "ParentName": "Energy", "LanguageKey": "ReactiveEnergy", "Sort": 37}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "ReactiveEnergy_Import", "ParentName": "ReactiveEnergy", "LanguageKey": "Import", "Sort": 38}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "ReactiveEnergy_Export", "ParentName": "ReactiveEnergy", "LanguageKey": "Export", "Sort": 39}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "ApparentEnergy", "ParentName": "Energy", "LanguageKey": "ApparentEnergy", "Sort": 40}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "FrequencyValues", "ParentName": "", "LanguageKey": "FrequencyValues", "Sort": 41}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "InstantaneousValuesFrequency", "ParentName": "FrequencyValues", "LanguageKey": "InstantaneousValuesFrequency", "Sort": 42}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "Frequency_ActualInstantaneousMeasurementValues", "ParentName": "InstantaneousValuesFrequency", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 43}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "HarmonicDistortion", "ParentName": "", "LanguageKey": "HarmonicDistortion", "Sort": 44}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "THDCurrent", "ParentName": "HarmonicDistortion", "LanguageKey": "THDCurrent", "Sort": 45}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "InstantaneousValuesTHDCurrent", "ParentName": "THDCurrent", "LanguageKey": "InstantaneousValuesTHDCurrent", "Sort": 46}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "THDCurrent_ActualInstantaneousMeasurementValues", "ParentName": "InstantaneousValuesTHDCurrent", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 47}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "THDVoltageL-N", "ParentName": "HarmonicDistortion", "LanguageKey": "THDVoltageL-N", "Sort": 48}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "InstantaneousValuesTHDVoltageL-N", "ParentName": "THDVoltageL-N", "LanguageKey": "InstantaneousValuesTHDVoltageL-N", "Sort": 49}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "THDVoltageL-N_ActualInstantaneousMeasurementValues", "ParentName": "InstantaneousValuesTHDVoltageL-N", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 50}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "THDVoltageL-L", "ParentName": "HarmonicDistortion", "LanguageKey": "THDVoltageL-L", "Sort": 51}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "InstantaneousValuesTHDVoltageL-L", "ParentName": "THDVoltageL-L", "LanguageKey": "InstantaneousValuesTHDVoltageL-L", "Sort": 52}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "THDVoltageL-L_ActualInstantaneousMeasurementValues", "ParentName": "InstantaneousValuesTHDVoltageL-L", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 53}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "Temperatures", "ParentName": "", "LanguageKey": "Temperatures", "Sort": 54}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "Temperatures_InstantaneousValues", "ParentName": "Temperatures", "LanguageKey": "InstantaneousValues", "Sort": 55}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "Temperatures_ActualInstantaneousMeasurementValues", "ParentName": "Temperatures_InstantaneousValues", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 56}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "Warnings", "ParentName": "", "LanguageKey": "Warnings", "Sort": 57}, {"AssetLevel": 50, "AssetModel": "3VA", "Name": "Others", "ParentName": "", "LanguageKey": "Others", "Sort": 58}]