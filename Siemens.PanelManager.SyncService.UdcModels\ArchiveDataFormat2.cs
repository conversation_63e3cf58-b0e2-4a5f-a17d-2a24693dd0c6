﻿namespace Siemens.PanelManager.SyncService.UdcModels
{
    public partial class ArchiveDataFormat2
    {
        public string ObjectId { get; set; } = null!;
        public long Archive { get; set; }
        public long TimestampInS { get; set; }
        public string? VLnAggregationValueL1n { get; set; }
        public string? VLnAggregationValueL2n { get; set; }
        public string? VLnAggregationValueL3n { get; set; }
        public string? VLnAggregationValueAvg { get; set; }
        public string? VLnAggregationGreatestL1n { get; set; }
        public string? VLnAggregationGreatestL2n { get; set; }
        public string? VLnAggregationGreatestL3n { get; set; }
        public string? VLnAggregationGreatestAvg { get; set; }
        public string? VLnAggregationLowestL1n { get; set; }
        public string? VLnAggregationLowestL2n { get; set; }
        public string? VLnAggregationLowestL3n { get; set; }
        public string? VLnAggregationLowestAvg { get; set; }
        public string? VLlAggregationValueL1l2 { get; set; }
        public string? VLlAggregationValueL2l3 { get; set; }
        public string? VLlAggregationValueL3l1 { get; set; }
        public string? VLlAggregationValueAvg { get; set; }
        public string? VLlAggregationGreatestL1l2 { get; set; }
        public string? VLlAggregationGreatestL2l3 { get; set; }
        public string? VLlAggregationGreatestL3l1 { get; set; }
        public string? VLlAggregationGreatestAvg { get; set; }
        public string? VLlAggregationLowestL1l2 { get; set; }
        public string? VLlAggregationLowestL2l3 { get; set; }
        public string? VLlAggregationLowestL3l1 { get; set; }
        public string? VLlAggregationLowestAvg { get; set; }
        public string? IAggregationValueL1 { get; set; }
        public string? IAggregationValueL2 { get; set; }
        public string? IAggregationValueL3 { get; set; }
        public string? IAggregationValueAvg { get; set; }
        public string? IAggregationGreatestL1 { get; set; }
        public string? IAggregationGreatestL2 { get; set; }
        public string? IAggregationGreatestL3 { get; set; }
        public string? IAggregationGreatestAvg { get; set; }
        public string? IAggregationLowestL1 { get; set; }
        public string? IAggregationLowestL2 { get; set; }
        public string? IAggregationLowestL3 { get; set; }
        public string? IAggregationLowestAvg { get; set; }
        public string? PowerWAggregationValueL1 { get; set; }
        public string? PowerWAggregationValueL2 { get; set; }
        public string? PowerWAggregationValueL3 { get; set; }
        public string? PowerWAggregationValueSum { get; set; }
        public string? PowerWAggregationGreatestL1 { get; set; }
        public string? PowerWAggregationGreatestL2 { get; set; }
        public string? PowerWAggregationGreatestL3 { get; set; }
        public string? PowerWAggregationGreatestSum { get; set; }
        public string? PowerWAggregationLowestL1 { get; set; }
        public string? PowerWAggregationLowestL2 { get; set; }
        public string? PowerWAggregationLowestL3 { get; set; }
        public string? PowerWAggregationLowestSum { get; set; }
        public string? PowerVarQnAggregationValueL1 { get; set; }
        public string? PowerVarQnAggregationValueL2 { get; set; }
        public string? PowerVarQnAggregationValueL3 { get; set; }
        public string? PowerVarQnAggregationValueSum { get; set; }
        public string? PowerVarQnAggregationGreatestL1 { get; set; }
        public string? PowerVarQnAggregationGreatestL2 { get; set; }
        public string? PowerVarQnAggregationGreatestL3 { get; set; }
        public string? PowerVarQnAggregationGreatestSum { get; set; }
        public string? PowerVarQnAggregationLowestL1 { get; set; }
        public string? PowerVarQnAggregationLowestL2 { get; set; }
        public string? PowerVarQnAggregationLowestL3 { get; set; }
        public string? PowerVarQnAggregationLowestSum { get; set; }
        public string? PowerVaAggregationValueL1 { get; set; }
        public string? PowerVaAggregationValueL2 { get; set; }
        public string? PowerVaAggregationValueL3 { get; set; }
        public string? PowerVaAggregationValueSum { get; set; }
        public string? PowerVaAggregationGreatestL1 { get; set; }
        public string? PowerVaAggregationGreatestL2 { get; set; }
        public string? PowerVaAggregationGreatestL3 { get; set; }
        public string? PowerVaAggregationGreatestSum { get; set; }
        public string? PowerVaAggregationLowestL1 { get; set; }
        public string? PowerVaAggregationLowestL2 { get; set; }
        public string? PowerVaAggregationLowestL3 { get; set; }
        public string? PowerVaAggregationLowestSum { get; set; }
        public string? PowerFactorAggregationValueL1 { get; set; }
        public string? PowerFactorAggregationValueL2 { get; set; }
        public string? PowerFactorAggregationValueL3 { get; set; }
        public string? PowerFactorAggregationValueSum { get; set; }
        public string? PowerFactorAggregationGreatestL1 { get; set; }
        public string? PowerFactorAggregationGreatestL2 { get; set; }
        public string? PowerFactorAggregationGreatestL3 { get; set; }
        public string? PowerFactorAggregationGreatestSum { get; set; }
        public string? PowerFactorAggregationLowestL1 { get; set; }
        public string? PowerFactorAggregationLowestL2 { get; set; }
        public string? PowerFactorAggregationLowestL3 { get; set; }
        public string? PowerFactorAggregationLowestSum { get; set; }
        public string? FrequencyAggregationValueCommon { get; set; }
        public string? FrequencyAggregationGreatestCommon { get; set; }
        public string? FrequencyAggregationValueLowestCommon { get; set; }
        public string? EnergyWhImportOnpeaktariffSum { get; set; }
        public string? EnergyWhExportOnpeaktariffSum { get; set; }
        public long CounterUniversal { get; set; }
        public string? EnergyVarhExportOffpeaktariffSum { get; set; }
        public string? EnergyVarhExportOnpeaktariffSum { get; set; }
        public string? EnergyVarhImportOffpeaktariffSum { get; set; }
        public string? EnergyVarhImportOnpeaktariffSum { get; set; }
        public string? EnergyWhExportOffpeaktariffSum { get; set; }
        public string? EnergyWhImportOffpeaktariffSum { get; set; }
    }
}
