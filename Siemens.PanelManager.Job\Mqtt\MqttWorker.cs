﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MQTTnet;
using MQTTnet.Client;
using Newtonsoft.Json;
using Siemens.PanelManager.Common.UdcService;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.UDC;
using System.Text;

namespace Siemens.PanelManager.Job.Mqtt
{
    public class MqttWorker : BackgroundService
    {
        private readonly ILogger<MqttWorker> _logger;
        private readonly IConfiguration _configuration;
        private readonly MqttClientOptions _mqttClientOptions;
        private readonly MqttClientSubscribeOptions _mqttClientSubscribeOptions;
        private IMessageReceiver? _messageReceiver;
        private readonly IServiceProvider _provider;

        private int _count = 0;
        private DateTime _lastWriteTime = DateTime.Now;

        private int _retryLogErrorCount = 0;
        private Timer? _checkMqttMessageTimer;

        public MqttClientConfig? MqttClientConfig { get; private set; }
        private IMqttClient? _mqttClient;
        private DateTime _mqttLastMessageDate;
        private string _udcDeviceId = string.Empty;

        public MqttWorker(ILogger<MqttWorker> logger, IConfiguration configuration, IServiceProvider provider)
        {
            _logger = logger;
            _configuration = configuration;
            _provider = provider;

            MqttClientConfig = _configuration.GetSection(MqttClientConfig.MqttClient).Get<MqttClientConfig>();

            if (MqttClientConfig == null)
            {
                _logger.LogError("MqttWorker init: Not found mqtt config.");
            }

            _mqttClientOptions = new MqttClientOptionsBuilder()
                .WithTcpServer(MqttClientConfig?.Server, MqttClientConfig?.Port)
                .WithClientId(string.Concat(MqttClientConfig?.ClientId, Guid.NewGuid().ToString()))
                .WithCleanSession()
                .WithKeepAlivePeriod(TimeSpan.FromSeconds(60))
                .WithSessionExpiryInterval(300)
                .WithCredentials(MqttClientConfig?.UserName, MqttClientConfig?.Password)
                .WithTlsOptions(a =>
                {
                    a.UseTls(false);
                })
                .Build();

            _mqttClientSubscribeOptions = new MqttFactory()
                .CreateSubscribeOptionsBuilder()
                .WithTopicFilter(f =>
                {
                    f.WithTopic(MqttClientConfig?.Topic ?? "udc");
                }).Build();

            _mqttLastMessageDate = DateTime.Now;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            await Task.Delay(10000);
            _retryLogErrorCount = 0;
            var isOk = false;
            while (!stoppingToken.IsCancellationRequested)
            {
                _messageReceiver = _provider.GetService<IMessageReceiver>();
                if (_messageReceiver != null)
                {
                    isOk = true;
                    break;
                }

                _retryLogErrorCount++;
                await Task.Delay(5000, stoppingToken);

                if (_retryLogErrorCount > 3)
                {
                    _logger.LogError("MqttWorker ExecuteAsync error.");
                }
                else
                {
                    isOk = false;
                    break;
                }
            }

            if (isOk)
            {
                _checkMqttMessageTimer = new Timer(CheckUdcMqttService, null, TimeSpan.Zero, TimeSpan.FromSeconds(60));
                await CreatedMqttClient();
            }
        }

        public override Task StopAsync(CancellationToken cancellationToken)
        {
            _checkMqttMessageTimer?.Change(Timeout.Infinite, 0);
            return base.StopAsync(cancellationToken);
        }

        public override void Dispose()
        {
            _checkMqttMessageTimer?.Dispose();
            if (_mqttClient != null)
            {
                try
                {
                    _mqttClient.DisconnectAsync().GetAwaiter().GetResult();
                }
                catch { }
            }
            base.Dispose();
        }

        private async Task CreatedMqttClient()
        {
            if (_mqttClient == null)
            {
                _mqttLastMessageDate = DateTime.Now;

                _mqttClient = new MqttFactory().CreateMqttClient();

                _mqttClient.ApplicationMessageReceivedAsync += async e =>
                {
                    try
                    {
                        var content = Encoding.UTF8.GetString(e.ApplicationMessage.PayloadSegment);
                        if (!string.IsNullOrWhiteSpace(content))
                        {
                            var dataPointResult = JsonConvert.DeserializeObject<DeviceDataPointsResult>(content);

                            if (dataPointResult != null)
                            {
                                if (!string.IsNullOrWhiteSpace(dataPointResult.ItemName))
                                {
                                    _mqttLastMessageDate = DateTime.Now;
                                }

                                _count++;

                                if ((DateTime.Now - _lastWriteTime).TotalSeconds > (5 * 60))
                                {
                                    _lastWriteTime = DateTime.Now;
                                    _logger.LogInformation($"[Mqtt][{_count}]");
                                    _count = 0;
                                }
                                dataPointResult.From = "UDC";
                                _messageReceiver!.InputMessage(dataPointResult);
                            }
                        }
                        await Task.Delay(10);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "MqttClient ApplicationMessageReceivedAsync error.");
                    }
                };

                _mqttClient.ConnectedAsync += async e =>
                {
                    _logger.LogInformation("MQTT Client Connected.");
                    _retryLogErrorCount = 0;

                    try
                    {
                        await _mqttClient.SubscribeAsync(_mqttClientSubscribeOptions, CancellationToken.None);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "MQTT Client Subscribe failed.");
                    }
                };

                _mqttClient.DisconnectedAsync += async e =>
                {
                    _logger.LogInformation("MQTT Client Disconnected.");
                    await Task.Delay(TimeSpan.FromSeconds(1));
                    try
                    {
                        await _mqttClient.ConnectAsync(_mqttClientOptions, CancellationToken.None);
                        _logger.LogInformation("MQTT Client Reconnected.");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "MQTT Client Reconnected failed.");
                    }
                };
            }

            while (!_mqttClient.IsConnected)
            {
                try
                {
                    await _mqttClient.ConnectAsync(_mqttClientOptions, CancellationToken.None);
                    _logger.LogInformation("MQTT connect.");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "MQTT client connect error");
                    await Task.Delay(5000);
                    _logger.LogInformation("Attempt to reconnect MQTT.");
                }
            }
        }

        private async void CheckUdcMqttService(object? state)
        {
            try
            {
                using var udcHttpService = _provider.GetRequiredService<UdcHttpService>();
                if ((DateTime.Now - _mqttLastMessageDate).TotalSeconds >= 50)
                {
                    if (string.IsNullOrEmpty(_udcDeviceId))
                    {
                        _udcDeviceId = await udcHttpService.GetUdcProjectItemIdAsync();
                    }

                    var configs = await udcHttpService.GetUdcConfigAsync(_udcDeviceId);

                    var currentMqttState = configs.FirstOrDefault(x => x.InternalName == "MqttServiceStartType");
                    if (currentMqttState != null && currentMqttState.Value == "1")
                    {
                        await udcHttpService.StopUdcMqttServiceAsync(_udcDeviceId);
                        await udcHttpService.StartUdcMqttServiceAsync(_udcDeviceId);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "MqttWorker CheckUdcMqttServiceAsync error.");
            }
        }
    }
}
