﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Model.Database.Auth;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class RolesController : SiemensApiControllerBase
    {
        private ILogger<RolesController> _log;
        private ISqlSugarClient _client;
        private IServiceProvider _provider;
        private AlarmExtendServer _alarmExtendServer => _provider.GetRequiredService<AlarmExtendServer>();
        public RolesController(
            SqlSugarScope client,
            ILogger<RolesController> log,
            SiemensCache cache,
            IServiceProvider provider)
            : base(provider, cache) 
        {
            _client = client;
            _log = log;
            _provider = provider;
        }

        [HttpPut]
        [Authorize(Policy = Permission.RoleUpdate)]
        [SwaggerOperation(Summary = "Swagger_Roles_UpdateRole", Description = "Swagger_Roles_UpdateRole_Desc")]
        public async Task<ResponseBase<string>> UpdateRole(UpdateRoleParam param)
        {
            if (param == null || string.IsNullOrEmpty(param.Name) || param.RoleId <= 0)
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var role = await _client.Queryable<Role>().Where(r => r.Id == param.RoleId)
                .WithCache($"Role:{param.RoleId}", (int)TimeSpan.FromHours(1).TotalSeconds)
                .FirstAsync();

            if (role == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.RoleNotExists
                };
            }

            if (param.Desc != null)
            {
                role.RoleDescription = param.Desc;
            }

            role.RoleName = param.Name;
            role.UpdatedBy = UserName;
            role.UpdatedTime = DateTime.Now;
            await _client.Updateable(role).RemoveDataCache("Role:").ExecuteCommandAsync();
            await _alarmExtendServer.InsertOperationLog(UserName, "UpdateRole", Model.Database.Alarm.AlarmSeverity.Middle, _client, role.RoleName);
            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = MessageContext.Success
            };
        }

        [HttpGet]
        [Authorize(Policy = Permission.RoleRead)]
        [SwaggerOperation(Summary = "Swagger_Roles_GetAllRoles", Description = "Swagger_Roles_GetAllRoles_Desc")]
        public async Task<ResponseBase<AllRoleResult>> GetAllRoles()
        {
            var roles = await _client.Queryable<Role>()
                .WithCache("Role:All", (int)TimeSpan.FromHours(1).TotalSeconds)
                .ToArrayAsync();

            var pages = await _client.Queryable<Page>()
                .WithCache($"Page:All", (int)TimeSpan.FromHours(1).TotalSeconds)
                .ToListAsync();
            pages = pages.FilterSomePageBySpecialLogic(0);
            var rolePageMappings = await _client.Queryable<RolePageMapping>().ToListAsync();

            return new ResponseBase<AllRoleResult>()
            {
                Code = 20000,
                Data = new AllRoleResult(roles, pages, rolePageMappings,  MessageContext)
            };
        }

        [HttpGet("{roleId}/permission")]
        [Authorize(Policy = Permission.RoleRead)]
        [SwaggerOperation(Summary = "Swagger_Roles_GetRolePermissions", Description = "Swagger_Roles_GetRolePermissions_Desc")]
        public async Task<ResponseBase<GetRolePermissionResult>> GetRolePermissions(int roleId)
        {
            if (roleId <= 0)
            {
                return new ResponseBase<GetRolePermissionResult>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            if (!await _client.Queryable<Role>().AnyAsync(r => r.Id == roleId))
            {
                return new ResponseBase<GetRolePermissionResult>()
                {
                    Code = 40400,
                    Message = MessageContext.RoleNotExists
                };
            }

            var pages = await _client.Queryable<Page>()
                .WithCache($"Page:All",(int)TimeSpan.FromHours(1).TotalSeconds)
                .ToArrayAsync();
            pages = pages.FilterSomePageBySpecialLogic(0);
            var rolePageMappings = await _client.Queryable<RolePageMapping>().Where(rpm => rpm.RoleId == roleId).ToArrayAsync();

            return new ResponseBase<GetRolePermissionResult>()
            {
                Code = 20000,
                Data = new GetRolePermissionResult(pages, rolePageMappings, MessageContext)
            };
        }

        [HttpPost("{roleId}/permission")]
        [Authorize(Policy = Permission.RoleUpdate)]
        [SwaggerOperation(Summary = "Swagger_Roles_UpdateRolePermissions", Description = "Swagger_Roles_UpdateRolePermissions_Desc")]
        public async Task<ResponseBase<string>> UpdateRolePermissions(int roleId, RolePermissionParam param)
        {
            if (param == null
                || param.RoleId <= 0
                || roleId != param.RoleId
                || param.PermissionId == null
                || param.PermissionId.Length < 0)
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var role = await _client.Queryable<Role>()
                .Where(r => r.Id == param.RoleId)
                .WithCache($"Role:{param.RoleId}", (int)TimeSpan.FromHours(1).TotalSeconds)
                .FirstAsync();
            if (role == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.RoleNotExists
                };
            }

            var allPages = await _client.Queryable<Page>()
                .WithCache($"Page:All", (int)TimeSpan.FromHours(1).TotalSeconds)
                .ToArrayAsync();
            if (param.PermissionId.Any(p => !allPages.Any(page => page.Id == p)))
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var rolePageMappings = await _client.Queryable<RolePageMapping>().Where(rpm=>rpm.RoleId== param.RoleId).ToListAsync(); 

            var needInsertList = new List<RolePageMapping>();
            var permissionIds = param.PermissionId;
            permissionIds = permissionIds.AddSomePageBySpecialLogic(0);
            List<int> tempPageIds = new List<int>();
            foreach (var pId in permissionIds)
            {
                var levelIds = GetLevelId(pId);
                if (levelIds[2] == 0 && levelIds[1] != 0)
                {
                    levelIds[1] += 1;
                    var nextPageId = GetPageId(levelIds);
                    var pageIds = await _client.Queryable<Page>()
                        .Where(p => p.Id > pId && p.Id < nextPageId && p.IsSystemConfig)
                        .Select(p => p.Id)
                        .ToArrayAsync();

                    tempPageIds.Add(pId);
                    tempPageIds.AddRange(pageIds);
                }
                else
                {
                    tempPageIds.Add(pId);
                }
            }

            foreach (var id in tempPageIds)
            {
                var rpm = rolePageMappings.FirstOrDefault(o => o.PageId == id);
                if (rpm != null)
                {
                    rolePageMappings.Remove(rpm);
                }
                else
                {
                    needInsertList.Add(new RolePageMapping()
                    {
                        RoleId = param.RoleId,
                        PageId = id,
                        CreatedBy = UserName,
                        CreatedTime = DateTime.Now,
                        UpdatedBy = UserName,
                        UpdatedTime = DateTime.Now,
                    });
                }
            }

            try
            {
                _client.Ado.BeginTran();
                if (needInsertList.Count > 0)
                {
                    await _client.Insertable(needInsertList).ExecuteCommandAsync();
                }

                if (rolePageMappings.Count > 0)
                {
                    var ids = rolePageMappings.Select(rpm => rpm.Id).ToArray();
                    await _client.Deleteable<RolePageMapping>().Where(rpm => ids.Contains(rpm.Id)).ExecuteCommandAsync();
                }
                await _alarmExtendServer.InsertOperationLog(UserName, "UpdateRole", Model.Database.Alarm.AlarmSeverity.Middle, _client, role.RoleName);
                _client.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "角色添加权限失败");
                return new ResponseBase<string>
                {
                    Code = 50000,
                    Message = MessageContext.ServerException,
                };
            }


            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = MessageContext.Success
            };
        }

        private int[] GetLevelId(int pageId)
        {
            var levelIds = new int[3];
            levelIds[0] = pageId / 100000;
            levelIds[1] = (pageId % 100000) / 100;
            levelIds[2] = pageId % 100;

            return levelIds;
        }

        private int GetPageId(int[] levelIds)
        {
            if (levelIds.Length != 3)
            {
                return 0;
            }
            var pageId = 0;
            pageId += levelIds[0] * 100000;
            pageId += levelIds[1] * 100;
            pageId += levelIds[2];
            return pageId;
        }

    }
}
