﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Topology
{
    [SugarTable("topoplogy_id_manager")]
    public class TopologyIdManager : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "key_type", Length = 256, IsNullable = false)]
        public string Type { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "key", IsNullable = false)]
        public int Key { get; set; }
    }
}
