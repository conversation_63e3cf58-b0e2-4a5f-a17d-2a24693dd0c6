﻿using InfluxDB.Client.Api.Domain;
using Microsoft.Extensions.DependencyInjection;
using NetTaste;
using Newtonsoft.Json.Linq;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Model.Database.Topology;
using Siemens.PanelManager.Model.Database.Topoplogy;
using Siemens.PanelManager.Model.Topology;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Server.Topoplogy
{
    public class TopologyDraftService
    {
        private IServiceProvider _provider;
        public TopologyDraftService(IServiceProvider provider)
        {
            _provider = provider;
        }

        public async Task<TopologyDetails?> GetTopologyInfo(int topologyId, int userId,string topologyType, ISqlSugarClient? sqlClient = null)
        {
            sqlClient ??= _provider.GetRequiredService<SqlSugarScope>();

            var draft = await sqlClient.Queryable<TopologyDraftInfo>().FirstAsync(d => d.UserId == userId && d.TopologyId == topologyId && d.TopologyType == topologyType);

            if (draft != null)
            {
                var obj = JObject.Parse(draft.Data);
                var result = new TopologyDetails()
                {
                    From = TopologyFrom.Draft,
                    TopologyName = draft.TopologyName,
                    Code = obj.Value<string>("code") ?? string.Empty,
                    Description = obj.Value<string>("description") ?? string.Empty,
                    TopologyId = topologyId,
                    Data = obj,
                    Flag = draft.TopologyFlag ?? string.Empty
                };

                return result;
            }
            var topologyInfo = await sqlClient.Queryable<TopologyInfo>().FirstAsync(t => t.Id == topologyId && t.Type == topologyType);
            if (topologyInfo != null)
            {
                var result = new TopologyDetails()
                {
                    From = TopologyFrom.DB,
                    TopologyName = topologyInfo.Name,
                    Code = topologyInfo.Code,
                    Description = topologyInfo.Description,
                    TopologyId = topologyId,
                    Data = JObject.Parse(topologyInfo.Data),
                    Flag = topologyInfo.TopologyFlag
                };
                return result;
            }
            return null;
        }

        public async Task DeleteDraftByUserId(int userId, string topologyType, ISqlSugarClient? sqlClient = null, int topologyId = -1, bool remove = false)
        {
            var cache = _provider.GetRequiredService<SiemensCache>();
            var draftInfo = cache.Get<TopologyDraftCacheInfo>(string.Format(TopologyDraftCacheInfo.DraftCacheKey, topologyType, userId));

            if (draftInfo != null)
            {
                if (remove) 
                {
                    draftInfo.TopologyFlag = string.Empty;
                }
                draftInfo.IsActive = false;
                cache.Set(string.Format(TopologyDraftCacheInfo.DraftCacheKey, topologyType, userId), draftInfo, TimeSpan.FromMinutes(3));
            }

            sqlClient ??= _provider.GetRequiredService<SqlSugarScope>();

            var query = sqlClient.Deleteable<TopologyDraftInfo>().Where(d => d.UserId == userId && d.TopologyType == topologyType);
            if (topologyId > 0)
            {
                query = query.Where(t=>t.TopologyId == topologyId);
            }

            await query.ExecuteCommandAsync();
        }
    }
}
