﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.Model.Database.System;
using SqlSugar;

namespace Siemens.PanelManager.Job.ClearFile
{
    public class ClearUploadFileJob : JobBase
    {
        public override string Name => "ClearUploadFileJob";

        private ILogger<ClearUploadFileJob> _logger;
        private readonly IServiceProvider _provider;

        public ClearUploadFileJob(ILogger<ClearUploadFileJob> logger, IServiceProvider provider)
        {
            _logger = logger;
            _provider = provider;
        }

        public override async Task Execute()
        {
            var basePath = AppContext.BaseDirectory;
            var uploadFileFolder = Path.Combine(basePath, "wwwroot", "uploadfiles", "personal");

            if(Directory.Exists(uploadFileFolder))
            {
                using (var client = _provider.GetRequiredService<ISqlSugarClient>())
                {
                    var fileList = await client.Queryable<FileManager>().Where(f => !f.IsSystemFile).ToArrayAsync();
                    ClearFiles(uploadFileFolder, fileList);
                }
            }
           
        }

        private void ClearFiles(string folder, FileManager[] fileManagers)
        {
            var files = Directory.GetFiles(folder);
            foreach (var f in files)
            {
                var fileInfo = new FileInfo(f);
                var fileName = fileInfo.Name.Substring(0, fileInfo.Name.Length - fileInfo.Extension.Length);
                var fm = fileManagers.FirstOrDefault(f => f.Code == fileName);
                if (fm == null)
                {
                    try
                    {
                        fileInfo.Delete();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"{f}删除失败");
                    }
                }
            }

            var directories = Directory.GetDirectories(folder);
            if (directories.Length > 0)
            {
                foreach(var d in directories) 
                {
                    ClearFiles(d, fileManagers);
                }
            }
        }
    }
}
