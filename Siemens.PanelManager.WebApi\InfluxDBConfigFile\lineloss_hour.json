{"name": "lineloss_hour", "status": "active", "flux": "import \"date\"\n\noption task = {\n    name: \"lineloss_hour\",\n    every: 1h,\n}\n\nfrom(bucket: \"panel\")\n    |> range(start: -1h, stop: 0h)\n    |> filter(fn: (r) => r[\"_measurement\"] == \"line_loss\")\n    |> filter(fn: (r) => r[\"_field\"] == \"current\")\n    |> drop(columns: [\"phase\"])\n    |> mean()\n    |> map(fn: (r) => ({r with _measurement: \"lineloss_hour\", _field: \"mcurrent\", _time: date.truncate(t: -1h, unit: 1h)}))\n    |> to(\n        bucket: \"panel\",\n        tagColumns: [\"topology_id\", \"line_id\"],\n    )\n\nfrom(bucket: \"panel\")\n    |> range(start: -1h, stop: 0h)\n    |> filter(fn: (r) => r[\"_measurement\"] == \"line_loss\")\n    |> filter(fn: (r) => r[\"_field\"] == \"p_loss\")\n    |> drop(columns: [\"phase\"])\n    |> sum()\n    |> map(fn: (r) => ({r with _measurement: \"lineloss_hour\", _field: \"sump\", _time: date.truncate(t: -1h, unit: 1h)}))\n    |> to(\n        bucket: \"panel\",\n        tagColumns: [\"topology_id\", \"line_id\"],\n    )\n\nfrom(bucket: \"panel\")\n    |> range(start: -1h, stop: 0h)\n    |> filter(fn: (r) => r[\"_measurement\"] == \"line_loss\")\n    |> filter(fn: (r) => r[\"_field\"] == \"q_loss\")\n    |> drop(columns: [\"phase\"])\n    |> sum()\n    |> map(fn: (r) => ({r with _measurement: \"lineloss_hour\", _field: \"sumq\", _time: date.truncate(t: -1h, unit: 1h)}))\n    |> to(\n        bucket: \"panel\",\n        tagColumns: [\"topology_id\", \"line_id\"],\n    )", "every": "1h"}