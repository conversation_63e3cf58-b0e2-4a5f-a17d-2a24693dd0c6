.error{
    height: 100%;
    width: 100%;

    display: grid;
    grid-template-columns: 10px auto 10px;
    grid-template-rows: 100px 500px auto;
    grid-template-areas:
    ". header ."
    ". body ."
    ". foot .";
}

.error-header{
    grid-area: header;
    width: 100%;
}

.error-body{
    grid-area: body;
    width: 100%;
    display: flex;
    justify-content: center;
    font-size: calc(10px + 2vmin);
}

.error-foot{
    grid-area: foot;
    width: 100%;
    display: flex;
    justify-content: center;
}
