﻿using Microsoft.AspNetCore.SignalR;
using Siemens.PanelManager.HubModel;
using Siemens.PanelManager.Monitor.Function;
using Siemens.PanelManager.Monitor.Hubs;
using Siemens.PanelManager.Monitor.Logger;
using Siemens.PanelManager.Monitor.StaticData;
using System.ComponentModel;

namespace Siemens.PanelManager.Monitor.Workers
{
    public class AutoTestWorker : BackgroundService
    {
        private ILogger<AutoTestWorker> _logger;
        private IHubContext<AutoTestHub> _hubContext;
        public AutoTestWorker(ILogger<AutoTestWorker> logger, IHubContext<AutoTestHub> hubContext)
        {
            _logger = logger;
            _hubContext = hubContext;
        }

        private bool _isRunning = false;

        public override Task StartAsync(CancellationToken cancellationToken)
        {
            if (!_isRunning)
            {
                _isRunning = true;
                return base.StartAsync(cancellationToken);
            }
            return Task.CompletedTask;
        }

        public override Task StopAsync(CancellationToken cancellationToken)
        {
            _isRunning = false;
            return base.StopAsync(cancellationToken);
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            await Task.Delay(3000);
            var client = _hubContext.Clients.Group(Constants.Group_AutoTestClient);
            try
            {
                await client.SendAsync("AutoTestResult", new TestItemResult("All")
                {
                    Status = (int)ItemStatus.InProcess
                });
                await Working(client);
                await client.SendAsync("AutoTestResult", new TestItemResult("All")
                {
                    Status = (int)ItemStatus.Finish
                });
            }
            catch (Exception ex)
            {
                await client.SendAsync("LogInfo", new LogModel()
                {
                    Message = ex.Message,
                    Tag = "AutoTestFailed",
                    Timestamp = DateTime.Now.GetTimestampForSec()
                });
                _logger.LogError(ex, "Auto test failed");
                await client.SendAsync("AutoTestResult", new TestItemResult("All")
                {
                    Status = (int)ItemStatus.Error,
                    Message = ex.Message,
                });
            }
            finally
            { 
                _isRunning = false;
            }
        }

        /// <summary>
        /// 1 启动服务
        /// 2 检查服务
        /// 3 扫描设备
        /// 4 测试UDC功能
        /// 5. UDC Mqtt 测试
        /// 6. Poc添加网口测试
        /// 7. Poc添加固定IP检查
        /// 8. Poc Usb禁用检查
        /// 5 完成
        /// </summary>
        /// <returns></returns>
        private async Task Working(IClientProxy client)
        {
            #region 1. 启动服务
            await client.SendAsync("AutoTestResult", new TestItemResult("StartProcess")
            {
                Status = (int)ItemStatus.InProcess
            });
            using (var startLogger = new CustomLogger(client, _logger, "StartProcess"))
            {
                await DockerFunc.RestartService(UpgradePackageMananger.CurrentlyTarFile, UpgradePackageMananger.CurrentlyYamlFile, startLogger);
            }
            await client.SendAsync("AutoTestResult", new TestItemResult("StartProcess")
            {
                Status = (int)ItemStatus.Finish,
                Result = (int)ItemResult.OK
            });
            #endregion

            #region 2. 检查服务
            await client.SendAsync("AutoTestResult", new TestItemResult("CheckProcess")
            {
                Status = (int)ItemStatus.InProcess
            });
            using (var checkLogger = new CustomLogger(client, _logger, "CheckProcess"))
            {
                await Task.Delay(10000);
               var statusList =  await DockerFunc.GetAllProcessStatus(checkLogger);
                var isRunning = true;
                foreach(var status in statusList) 
                {
                    var message = $"Name: {status.Names}, ID: {status.ID}, State: {status.State}";
                    if(!("running".Equals(status.State, StringComparison.OrdinalIgnoreCase)
                        || "created".Equals(status.State,StringComparison.OrdinalIgnoreCase))) 
                    {
                        isRunning = false;
                    }
                    checkLogger.LogInformation(message);
                }
                var model = await MonitorFunc.GetMonitorModel(checkLogger);
                await client.SendAsync("MonitorInfo", model);
                if (!isRunning) 
                {
                    await client.SendAsync("AutoTestResult", new TestItemResult("CheckProcess")
                    {
                        Status = (int)ItemStatus.Finish,
                        Result = (int)ItemResult.Fail
                    });
                    return;
                }
            }
            await client.SendAsync("AutoTestResult", new TestItemResult("CheckProcess")
            {
                Status = (int)ItemStatus.Finish,
                Result = (int)ItemResult.OK
            });
            #endregion

            #region 3. 扫描设备
            await client.SendAsync("AutoTestResult", new TestItemResult("ScanDevice")
            {
                Status = (int)ItemStatus.InProcess
            });
            using (var scanDeviceLogger = new CustomLogger(client, _logger, "ScanDevice"))
            {
                scanDeviceLogger.LogInformation("Passed");
            }
            await client.SendAsync("AutoTestResult", new TestItemResult("ScanDevice")
            {
                Status = (int)ItemStatus.Finish,
                Result = (int)ItemResult.OK
            });
            #endregion

            #region 4. 测试UDC功能
            await client.SendAsync("AutoTestResult", new TestItemResult("TestUdc")
            {
                Status = (int)ItemStatus.InProcess
            });
            using (var customLogger = new CustomLogger(client, _logger, "TestUdc"))
            {
                customLogger.LogInformation("Test udc passed");
            }
            await client.SendAsync("AutoTestResult", new TestItemResult("TestUdc")
            {
                Status = (int)ItemStatus.Finish,
                Result = (int)ItemResult.OK
            });
            #endregion

            #region 5. UDC Mqtt 测试
            await client.SendAsync("AutoTestResult", new TestItemResult("TestUDCMqtt")
            {
                Status = (int)ItemStatus.InProcess
            });
            using (var customLogger = new CustomLogger(client, _logger, "TestUDCMqtt"))
            {
                customLogger.LogInformation("Test udc mqtt passed");
            }
            await client.SendAsync("AutoTestResult", new TestItemResult("TestUDCMqtt")
            {
                Status = (int)ItemStatus.Finish,
                Result = (int)ItemResult.OK
            });
            #endregion


            #region 6. Poc添加网口测试
            IpInfo[] ipInfos;
            await client.SendAsync("AutoTestResult", new TestItemResult("CheckNetPorts")
            {
                Status = (int)ItemStatus.InProcess
            });
            using (var customLogger = new CustomLogger(client, _logger, "CheckNetPorts"))
            {
                ipInfos = await CheckDeviceFunc.GetIpInfoes(customLogger);
            }
            await client.SendAsync("AutoTestResult", new TestItemResult("CheckNetPorts")
            {
                Status = (int)ItemStatus.Finish,
                Result = (int)ItemResult.OK
            });
            #endregion

            #region 7. Poc添加固定IP检查
            await client.SendAsync("AutoTestResult", new TestItemResult("CheckIpInfo")
            {
                Status = (int)ItemStatus.InProcess
            });
            using (var customLogger = new CustomLogger(client, _logger, "CheckIpInfo"))
            {
                customLogger.LogInformation("Check ip passed");
            }
            await client.SendAsync("AutoTestResult", new TestItemResult("CheckIpInfo")
            {
                Status = (int)ItemStatus.Finish,
                Result = (int)ItemResult.OK
            });
            #endregion


            #region 8. Poc Usb禁用检查
            await client.SendAsync("AutoTestResult", new TestItemResult("CheckUsb")
            {
                Status = (int)ItemStatus.InProcess
            });
            using (var customLogger = new CustomLogger(client, _logger, "CheckUsb"))
            {
                var usbInfoes = await CheckDeviceFunc.GetUsbInfoes(customLogger);
                foreach (var info in usbInfoes)
                {
                    customLogger.LogInformation(info);
                }
            }
            await client.SendAsync("AutoTestResult", new TestItemResult("CheckUsb")
            {
                Status = (int)ItemStatus.Finish,
                Result = (int)ItemResult.OK
            });
            #endregion

        }
    }
}
