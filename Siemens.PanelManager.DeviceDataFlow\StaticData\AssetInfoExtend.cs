﻿using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.DeviceDataFlow.MessageBus;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Config;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.Model.DataFlow;
using SqlSugar;

namespace Siemens.PanelManager.DeviceDataFlow.StaticData
{
    static class AssetInfoExtend
    {
        public static async Task<AssetSimpleInfo> AddAsset(AssetInfo assetInfo, ISqlSugarClient sqlClient, SiemensCache cache, IAssetDataProxyRef? assetDataProxyRef)
        {
            if (assetInfo.AssetLevel == AssetLevel.Area)
            {
                var areaSimpleInfo = new AssetSimpleInfo(assetInfo);
                cache.Set(string.Format(Constant.AssetSimpleInfoCacheKey, assetInfo.Id), areaSimpleInfo);
                return areaSimpleInfo;
            }
            var relations = await sqlClient.Queryable<AssetRelation>().ToParentListAsync(r => r.ParentId, assetInfo.Id);
            var simpleInfo = new AssetSimpleInfo(assetInfo);

            await SetParentSimpleInfo(assetInfo, sqlClient, relations, simpleInfo, cache);

            if (assetInfo.AssetLevel == AssetLevel.Circuit)
            {
                var circuitId = assetInfo.Id;
                await GetCircuitSourceId(sqlClient, simpleInfo, circuitId, cache);
            }
            else if (assetInfo.AssetLevel == AssetLevel.Panel)
            {
                var subRelations = await sqlClient.Queryable<AssetRelation>().ToChildListAsync(ar => ar.ParentId, assetInfo.Id);
                var subCircuits = subRelations.Where(r => r.AssetLevel == AssetLevel.Circuit).ToList();
                int? sourceId = null;
                foreach (var subCircuit in subCircuits)
                {
                    if (subRelations.Any(ar => ar.ParentId == subCircuit.ChildId))
                    {
                        sourceId = subCircuit.ChildId;
                        break;
                    }
                }
                simpleInfo.SourceAssetId = sourceId;
            }
            else if (assetInfo.AssetLevel == AssetLevel.Device)
            {
                var assetTypes = new string[] { "Meter", "ACB", "MCCB", "MCB", "MotorProtector", "GeneralDevice", "Gateway" };
                if (simpleInfo.CircuitSimpleInfo != null && assetTypes.Contains(assetInfo.AssetType))
                {
                    var circuitId = simpleInfo.CircuitSimpleInfo.AssetId;
                    var breakerId = await GetCircuitSourceId(sqlClient, simpleInfo.CircuitSimpleInfo, simpleInfo.CircuitSimpleInfo.AssetId, cache);
                    cache.Set(string.Format(Constant.AssetSimpleInfoCacheKey, simpleInfo.CircuitSimpleInfo.AssetId), simpleInfo.CircuitSimpleInfo);
                    if (circuitId != simpleInfo.CircuitSimpleInfo.AssetId && simpleInfo.CircuitSimpleInfo.SourceAssetId.HasValue)
                    {
                        var currently = cache.GetHashAllData(string.Format(Constant.AssetCurrentStatusCacheKey, simpleInfo.CircuitSimpleInfo.SourceAssetId.Value));
                        if (currently == null || currently.Count <= 0)
                        {
                            await RemoveCircuitCache(cache, null, simpleInfo.CircuitSimpleInfo);
                        }
                        else
                        {
                            assetDataProxyRef?.InputData(new AssetInputData
                            {
                                AssetId = simpleInfo.CircuitSimpleInfo.AssetId,
                                InputTime = DateTime.Now,
                                Datas = currently,
                            });
                        }
                    }

                    if (breakerId > 0)
                    {
                        var switchStatus = cache.GetHashData(string.Format(Constant.AssetCurrentStatusCacheKey, breakerId), new string[] { "Switch" });

                        if (switchStatus != null && switchStatus.Count > 0)
                        {
                            assetDataProxyRef?.InputData(new AssetInputData
                            {
                                AssetId = simpleInfo.CircuitSimpleInfo.AssetId,
                                InputTime = DateTime.Now,
                                Datas = switchStatus,
                            });
                        }
                    }
                }
            }

            cache.Set(string.Format(Constant.AssetSimpleInfoCacheKey, assetInfo.Id), simpleInfo);

            if (!string.IsNullOrEmpty(assetInfo.ObjectId))
            {
                cache.Set(string.Format(Constant.ObjectIdCacheKey, assetInfo.ObjectId), simpleInfo);
            }

            MessageBusContext.AssetChangeRegister.InputAsset(new AssetInfoOptionParam(AssetOpt.Add, simpleInfo));

            return simpleInfo;
        }

        public static async Task<AssetSimpleInfo> UpdateAsset(AssetInfo assetInfo, ISqlSugarClient sqlClient, SiemensCache cache, IAssetDataProxyRef assetDataProxyRef, ILogger? logger = null)
        {
            if (assetInfo.AssetLevel == AssetLevel.Area)
            {
                var newSimple = new AssetSimpleInfo(assetInfo);

                cache.Set(string.Format(Constant.AssetSimpleInfoCacheKey, assetInfo.Id), newSimple);
                MessageBusContext.AssetChangeRegister.InputAsset(new AssetInfoOptionParam(AssetOpt.Update, newSimple));
                return newSimple;
            }

            var relations = await sqlClient.Queryable<AssetRelation>().ToParentListAsync(r => r.ParentId, assetInfo.Id);
            var oldSimpleInfo = cache.Get<AssetSimpleInfo>(string.Format(Constant.AssetSimpleInfoCacheKey, assetInfo.Id));
            var newSimpleInfo = new AssetSimpleInfo(assetInfo);

            if (oldSimpleInfo != null)
            {
                await SetParentSimpleInfo(assetInfo, sqlClient, relations, newSimpleInfo, cache, oldSimpleInfo);
                cache.Set(string.Format(Constant.AssetSimpleInfoCacheKey, assetInfo.Id), newSimpleInfo);

                if (assetInfo.AssetLevel == AssetLevel.Device)
                {
                    if ((newSimpleInfo.CircuitSimpleInfo?.AssetId ?? 0) != (oldSimpleInfo.CircuitSimpleInfo?.AssetId ?? 0))
                    {
                        logger?.LogInformation($"{assetInfo.AssetName} 修改回路");
                        if (newSimpleInfo.CircuitSimpleInfo != null)
                        {
                            var circuitSimpleInfo = cache.Get<AssetSimpleInfo>(string.Format(Constant.AssetSimpleInfoCacheKey, newSimpleInfo.CircuitSimpleInfo.AssetId));
                            if (circuitSimpleInfo != null)
                            {
                                logger?.LogInformation($"new {circuitSimpleInfo.AssetName} 修改 Source Id");
                                var breakerId = await GetCircuitSourceId(sqlClient, circuitSimpleInfo, newSimpleInfo.CircuitSimpleInfo.AssetId, cache);
                                cache.Set(string.Format(Constant.AssetSimpleInfoCacheKey, newSimpleInfo.CircuitSimpleInfo.AssetId), circuitSimpleInfo);
                                if (newSimpleInfo.CircuitSimpleInfo.SourceAssetId.HasValue)
                                {
                                    await Task.Delay(300);
                                    logger?.LogInformation($"{circuitSimpleInfo.AssetName} 修改 Source {newSimpleInfo.CircuitSimpleInfo.SourceAssetId.Value} 信息");
                                    var currently = cache.GetHashAllData(string.Format(Constant.AssetCurrentStatusCacheKey, newSimpleInfo.CircuitSimpleInfo.SourceAssetId.Value));

                                    if (currently == null || currently.Count <= 0)
                                    {
                                        await RemoveCircuitCache(cache, logger, circuitSimpleInfo);
                                    }
                                    else
                                    {
                                        assetDataProxyRef.InputData(new AssetInputData
                                        {
                                            AssetId = newSimpleInfo.CircuitSimpleInfo.AssetId,
                                            InputTime = DateTime.Now,
                                            Datas = currently,
                                        });
                                    }
                                }
                                else
                                {
                                    await RemoveCircuitCache(cache, logger, circuitSimpleInfo);
                                }

                                if (breakerId > 0)
                                {
                                    var switchStatus = cache.GetHashData(string.Format(Constant.AssetCurrentStatusCacheKey, breakerId), ["Switch"]);

                                    if (switchStatus != null && switchStatus.Count > 0)
                                    {
                                        assetDataProxyRef?.InputData(new AssetInputData
                                        {
                                            AssetId = circuitSimpleInfo.AssetId,
                                            InputTime = DateTime.Now,
                                            Datas = switchStatus,
                                        });
                                    }
                                    else
                                    {
                                        assetDataProxyRef?.InputData(new AssetInputData
                                        {
                                            AssetId = circuitSimpleInfo.AssetId,
                                            InputTime = DateTime.Now,
                                            Datas = new Dictionary<string, string> { ["Switch"] = "0" },
                                        });
                                    }
                                }
                                else
                                {
                                    var switchStatus = new Dictionary<string, string> { ["Switch"] = "0" };

                                    if (switchStatus != null && switchStatus.Count > 0)
                                    {
                                        assetDataProxyRef?.InputData(new AssetInputData
                                        {
                                            AssetId = circuitSimpleInfo.AssetId,
                                            InputTime = DateTime.Now,
                                            Datas = switchStatus,
                                        });
                                    }
                                }
                            }
                        }

                        if (oldSimpleInfo.CircuitSimpleInfo != null)
                        {
                            var circuitSimpleInfo = cache.Get<AssetSimpleInfo>(string.Format(Constant.AssetSimpleInfoCacheKey, oldSimpleInfo.CircuitSimpleInfo.AssetId));
                            if (circuitSimpleInfo != null)
                            {
                                logger?.LogInformation($"old {circuitSimpleInfo.AssetName} 修改 Source Id");

                                var breakerId = await GetCircuitSourceId(sqlClient, circuitSimpleInfo, oldSimpleInfo.CircuitSimpleInfo.AssetId, cache);
                                cache.Set(string.Format(Constant.AssetSimpleInfoCacheKey, oldSimpleInfo.CircuitSimpleInfo.AssetId), circuitSimpleInfo);

                                if (oldSimpleInfo.CircuitSimpleInfo.SourceAssetId.HasValue)
                                {
                                    await Task.Delay(300);
                                    logger?.LogInformation($"{circuitSimpleInfo.AssetName} 修改 Source {oldSimpleInfo.CircuitSimpleInfo.SourceAssetId.Value} 信息");

                                    var currently = cache.GetHashAllData(string.Format(Constant.AssetCurrentStatusCacheKey, oldSimpleInfo.CircuitSimpleInfo.SourceAssetId.Value));
                                    if (currently == null || currently.Count <= 0)
                                    {
                                        await RemoveCircuitCache(cache, logger, circuitSimpleInfo);
                                    }
                                    else
                                    {
                                        assetDataProxyRef?.InputData(new AssetInputData
                                        {
                                            AssetId = oldSimpleInfo.CircuitSimpleInfo.AssetId,
                                            InputTime = DateTime.Now,
                                            Datas = currently,
                                        });
                                    }
                                }
                                else
                                {
                                    await RemoveCircuitCache(cache, logger, circuitSimpleInfo);
                                }

                                if (breakerId > 0)
                                {
                                    var switchStatus = cache.GetHashData(string.Format(Constant.AssetCurrentStatusCacheKey, breakerId), new string[] { "Switch" });

                                    if (switchStatus != null && switchStatus.Count > 0)
                                    {
                                        assetDataProxyRef?.InputData(new AssetInputData
                                        {
                                            AssetId = circuitSimpleInfo.AssetId,
                                            InputTime = DateTime.Now,
                                            Datas = switchStatus,
                                        });
                                    }
                                    else
                                    {
                                        assetDataProxyRef?.InputData(new AssetInputData
                                        {
                                            AssetId = circuitSimpleInfo.AssetId,
                                            InputTime = DateTime.Now,
                                            Datas = new Dictionary<string, string> { ["Switch"] = "0" },
                                        });
                                    }
                                }
                                else
                                {
                                    var switchStatus = new Dictionary<string, string> { ["Switch"] = "0" };

                                    if (switchStatus != null && switchStatus.Count > 0)
                                    {
                                        assetDataProxyRef?.InputData(new AssetInputData
                                        {
                                            AssetId = circuitSimpleInfo.AssetId,
                                            InputTime = DateTime.Now,
                                            Datas = switchStatus,
                                        });
                                    }
                                }
                            }
                        }
                    }

                    if (oldSimpleInfo.ObjectId != null && oldSimpleInfo.ObjectId != assetInfo.ObjectId)
                    {
                        cache.Clear(string.Format(Constant.ObjectIdCacheKey, oldSimpleInfo.ObjectId));
                    }

                    if (!string.IsNullOrEmpty(assetInfo.ObjectId))
                    {
                        cache.Set(string.Format(Constant.ObjectIdCacheKey, assetInfo.ObjectId), newSimpleInfo);
                    }
                }
                else if (assetInfo.AssetLevel == AssetLevel.Circuit)
                {
                    var breakerId = await GetCircuitSourceId(sqlClient, newSimpleInfo, assetInfo.Id, cache);
                    cache.Set(string.Format(Constant.AssetSimpleInfoCacheKey, assetInfo.Id), newSimpleInfo);

                    if (breakerId > 0)
                    {
                        var switchStatus = cache.GetHashData(string.Format(Constant.AssetCurrentStatusCacheKey, breakerId), ["Switch"]);

                        if (switchStatus != null && switchStatus.Count > 0)
                        {
                            assetDataProxyRef?.InputData(new AssetInputData
                            {
                                AssetId = assetInfo.Id,
                                InputTime = DateTime.Now,
                                Datas = switchStatus,
                            });
                        }
                    }
                }

                MessageBusContext.AssetChangeRegister.InputAsset(new AssetInfoOptionParam(AssetOpt.Update, newSimpleInfo));
            }
            else
            {
                newSimpleInfo = await AddAsset(assetInfo, sqlClient, cache, assetDataProxyRef);
                cache.Set(string.Format(Constant.AssetSimpleInfoCacheKey, assetInfo.Id), newSimpleInfo);
            }

            return newSimpleInfo;
        }

        private static async Task RemoveCircuitCache(SiemensCache cache, ILogger? logger, AssetSimpleInfo circuitSimpleInfo)
        {
            await Task.Delay(300);
            logger?.LogInformation($"{circuitSimpleInfo.AssetName} 删除数据信息");
            cache.RemoveHashData(string.Format(Constant.AssetCurrentStatusCacheKey, circuitSimpleInfo.AssetId));
        }

        public static async Task RemoveAsset(int assetId, SiemensCache cache, ISqlSugarClient sqlClient, IAssetDataProxyRef assetDataProxyRef)
        {
            cache.Clear(string.Format(Constant.AssetSimpleInfoCacheKey, assetId), out AssetSimpleInfo? oldSimpleInfo);
            if (oldSimpleInfo != null)
            {
                if (oldSimpleInfo.CircuitSimpleInfo != null)
                {
                    var circuitId = oldSimpleInfo.CircuitSimpleInfo.AssetId;
                    var circuitInfo = cache.Get<AssetSimpleInfo>(string.Format(Constant.AssetSimpleInfoCacheKey, circuitId));
                    if (circuitInfo != null)
                    {
                        if (circuitInfo.SourceAssetId == assetId)
                        {
                            var breakerId = await GetCircuitSourceId(sqlClient, circuitInfo, circuitId, cache);
                            cache.Set(string.Format(Constant.AssetSimpleInfoCacheKey, circuitInfo.AssetId), circuitInfo);
                            if (circuitInfo.SourceAssetId.HasValue)
                            {
                                var currently = cache.GetHashAllData(string.Format(Constant.AssetCurrentStatusCacheKey, circuitInfo.SourceAssetId.Value));
                                if (currently == null || currently.Count <= 0)
                                {
                                    await RemoveCircuitCache(cache, null, circuitInfo);
                                }
                                else
                                {
                                    assetDataProxyRef.InputData(new AssetInputData
                                    {
                                        AssetId = circuitInfo.AssetId,
                                        InputTime = DateTime.Now,
                                        Datas = currently,
                                    });
                                }
                            }
                            else
                            {
                                cache.RemoveHashData(string.Format(Constant.AssetCurrentStatusCacheKey, circuitInfo.AssetId));
                            }

                            if (breakerId > 0)
                            {
                                var switchStatus = cache.GetHashData(string.Format(Constant.AssetCurrentStatusCacheKey, breakerId), new string[] { "Switch" });

                                if (switchStatus != null && switchStatus.Count > 0)
                                {
                                    assetDataProxyRef?.InputData(new AssetInputData
                                    {
                                        AssetId = circuitInfo.AssetId,
                                        InputTime = DateTime.Now,
                                        Datas = switchStatus,
                                    });
                                }
                            }
                        }
                    }
                }

                MessageBusContext.AssetChangeRegister.InputAsset(new AssetInfoOptionParam(AssetOpt.Remove, oldSimpleInfo));
            }
        }

        public static async Task UpdateGetCircuitSource(ISqlSugarClient sqlClient, SiemensCache cache)
        {
            var circuitIds = await sqlClient.Queryable<AssetInfo>().Where(a => a.AssetLevel == AssetLevel.Circuit).Select(a => a.Id).ToArrayAsync();
            foreach (var circuitId in circuitIds)
            {
                var assetSimpleInfo = cache.Get<AssetSimpleInfo>(string.Format(Constant.AssetSimpleInfoCacheKey, circuitId));

                if (assetSimpleInfo != null)
                {
                    await GetCircuitSourceId(sqlClient, assetSimpleInfo, circuitId, cache);
                    cache.Set(string.Format(Constant.AssetSimpleInfoCacheKey, circuitId), assetSimpleInfo);
                }
            }
        }

        private static async Task<int> GetCircuitSourceId(ISqlSugarClient sqlClient, AssetSimpleInfo simpleInfo, int circuitId, SiemensCache cache)
        {
            simpleInfo.SourceAssetId = null;
            var subDevices = await sqlClient.Queryable<AssetRelation>().Where(ar => ar.ParentId == circuitId && ar.AssetLevel == AssetLevel.Device).ToArrayAsync();

            var subDeviceIds = subDevices.Select(ar => ar.ChildId).ToArray();
            var deviceAssetInfoes = await sqlClient.Queryable<AssetInfo>().Where(a => subDeviceIds.Contains(a.Id)).ToArrayAsync();

            var breakerId = 0;

            #region 电表
            var meter = deviceAssetInfoes.OrderByDescending(a => a.Id).FirstOrDefault(d => d.AssetType == "Meter");
            if (meter != null)
            {
                simpleInfo.SourceAssetId = meter.Id;
            }
            #endregion

            #region 第三方设备
            if (!simpleInfo.SourceAssetId.HasValue)
            {
                var config = cache.GetOrCreate<List<CircuitDataSourceConfig>>("CircuitDataSourceConfig", () =>
                {
                    var sysConfig = sqlClient.Queryable<SystemConfig>().First(c => c.Type == "CircuitConfig" && c.Name == "DataSource");
                    if (sysConfig == null)
                    {
                        return new List<CircuitDataSourceConfig>();
                    }

                    var list = JsonConvert.DeserializeObject<List<CircuitDataSourceConfig>>(sysConfig.Value);
                    if (list == null)
                    {
                        return new List<CircuitDataSourceConfig>();
                    }

                    return list;
                }, TimeSpan.FromHours(5));

                config = config.OrderBy(c => c.Index).ToList();
                foreach (var c in config)
                {
                    var thirdDevice = deviceAssetInfoes.OrderByDescending(d => d.Id).FirstOrDefault(d => d.AssetType == "GeneralDevice" && d.ThirdPartCode == c.ThridPartCode);
                    if (thirdDevice != null)
                    {
                        simpleInfo.SourceAssetId = thirdDevice.Id;
                        break;
                    }
                }
            }
            #endregion

            #region 断路器
            var breakerTypes = new string[] { "ACB", "MCCB", "MCB" };
            var breaker = deviceAssetInfoes.OrderByDescending(a => a.Id).FirstOrDefault(d => breakerTypes.Contains(d.AssetType));
            if (breaker != null)
            {
                breakerId = breaker.Id;
            }

            if (!simpleInfo.SourceAssetId.HasValue)
            {
                if (breaker != null)
                {
                    simpleInfo.SourceAssetId = breaker.Id;

                }
            }
            #endregion

            #region 马保
            if (!simpleInfo.SourceAssetId.HasValue)
            {
                var motor = deviceAssetInfoes.OrderByDescending(a => a.Id).FirstOrDefault(d => d.AssetType == "MotorProtector");
                if (motor != null)
                {
                    simpleInfo.SourceAssetId = motor.Id;
                }
            }
            #endregion

            #region 第三方设备
            if (!simpleInfo.SourceAssetId.HasValue)
            {
                var motor = deviceAssetInfoes.OrderByDescending(a => a.Id).FirstOrDefault(d => d.AssetType == "GeneralDevice");
                if (motor != null)
                {
                    simpleInfo.SourceAssetId = motor.Id;
                }
            }
            #endregion

            return breakerId;
        }


        private static async Task SetParentSimpleInfo(AssetInfo assetInfo,
            ISqlSugarClient sqlClient,
            List<AssetRelation> relations,
            AssetSimpleInfo simpleInfo,
            SiemensCache cache,
            AssetSimpleInfo? oldSimpleInfo = null)
        {
            var area = relations.FirstOrDefault(r => r.AssetLevel == AssetLevel.Area);
            AssetSimpleInfo? areaSimpleInfo = null;
            if (area != null)
            {
                if (oldSimpleInfo != null && oldSimpleInfo.AreaSimpleInfo != null
                    && oldSimpleInfo.AreaSimpleInfo.AssetId == area.ChildId)
                {
                    areaSimpleInfo = oldSimpleInfo.AreaSimpleInfo;
                    simpleInfo.AreaSimpleInfo = areaSimpleInfo;
                }
                else
                {
                    areaSimpleInfo = cache.Get<AssetSimpleInfo>(string.Format(Constant.AssetSimpleInfoCacheKey, area.ChildId));

                    if (areaSimpleInfo != null)
                    {
                        simpleInfo.AreaSimpleInfo = areaSimpleInfo;
                    }
                    else
                    {
                        var tempAsset = await sqlClient.Queryable<AssetInfo>()
                            .Where(a => a.Id == area.ChildId && a.AssetLevel == AssetLevel.Area)
                            .FirstAsync();

                        areaSimpleInfo = null;
                        if (tempAsset != null)
                        {
                            areaSimpleInfo = new AssetSimpleInfo(tempAsset);
                        }

                        simpleInfo.AreaSimpleInfo = areaSimpleInfo;
                    }
                }
            }

            AssetSimpleInfo? substationSimpleInfo = null;
            if (assetInfo.AssetLevel > AssetLevel.Substation)
            {
                var substation = relations.FirstOrDefault(r => r.AssetLevel == AssetLevel.Substation);
                if (substation != null)
                {
                    if (oldSimpleInfo != null && oldSimpleInfo.SubstationSimpleInfo != null && oldSimpleInfo.SubstationSimpleInfo.AssetId == substation.ChildId)
                    {
                        substationSimpleInfo = oldSimpleInfo.SubstationSimpleInfo;
                        simpleInfo.SubstationSimpleInfo = substationSimpleInfo;
                    }
                    else
                    {
                        substationSimpleInfo = cache.Get<AssetSimpleInfo>(string.Format(Constant.AssetSimpleInfoCacheKey, substation.ChildId));
                        if (substationSimpleInfo != null)
                        {
                            simpleInfo.SubstationSimpleInfo = substationSimpleInfo;
                        }
                        else
                        {
                            var tempAsset = await sqlClient.Queryable<AssetInfo>()
                                .Where(a => a.Id == substation.ChildId && a.AssetLevel == AssetLevel.Substation)
                                .FirstAsync();

                            substationSimpleInfo = null;
                            if (tempAsset != null)
                            {
                                substationSimpleInfo = new AssetSimpleInfo(tempAsset);
                            }

                            simpleInfo.SubstationSimpleInfo = substationSimpleInfo;
                        }
                    }
                }
            }

            AssetSimpleInfo? panelSimpleInfo = null;
            if (assetInfo.AssetLevel > AssetLevel.Panel)
            {
                var panel = relations.FirstOrDefault(r => r.AssetLevel >= AssetLevel.Panel && r.AssetLevel< AssetLevel.Circuit);
                if (panel != null)
                {
                    if (oldSimpleInfo != null && oldSimpleInfo.PanelSimpleInfo != null && oldSimpleInfo.PanelSimpleInfo.AssetId == panel.ChildId)
                    {
                        panelSimpleInfo = oldSimpleInfo.PanelSimpleInfo;
                        simpleInfo.PanelSimpleInfo = panelSimpleInfo;
                    }
                    else
                    {
                        panelSimpleInfo = cache.Get<AssetSimpleInfo>(string.Format(Constant.AssetSimpleInfoCacheKey, panel.ChildId));
                        if (panelSimpleInfo != null)
                        {
                            simpleInfo.PanelSimpleInfo = panelSimpleInfo;
                        }
                        else
                        {
                            var tempAsset = await sqlClient
                                .Queryable<AssetInfo>()
                                .Where(a => a.Id == panel.ChildId && a.AssetLevel >= AssetLevel.Panel && a.AssetLevel < AssetLevel.Circuit)
                                .FirstAsync();

                            panelSimpleInfo = null;
                            if (tempAsset != null)
                            {
                                panelSimpleInfo = new AssetSimpleInfo(tempAsset);
                            }

                            simpleInfo.PanelSimpleInfo = panelSimpleInfo;
                        }
                    }
                }
            }

            if (assetInfo.AssetLevel > AssetLevel.Circuit)
            {
                var circuit = relations.FirstOrDefault(r => r.AssetLevel == AssetLevel.Circuit);
                if (circuit != null)
                {
                    if (oldSimpleInfo != null && oldSimpleInfo.CircuitSimpleInfo != null && oldSimpleInfo.CircuitSimpleInfo.AssetId == circuit.ChildId)
                    {
                        simpleInfo.CircuitSimpleInfo = oldSimpleInfo.CircuitSimpleInfo;
                    }
                    else
                    {
                        var tempAsset = await sqlClient.Queryable<AssetInfo>()
                            .Where(a => a.Id == circuit.ChildId && a.AssetLevel == AssetLevel.Circuit)
                            .FirstAsync();

                        AssetSimpleInfo? circuitSimpleInfo = null;
                        if (tempAsset != null)
                        {
                            circuitSimpleInfo = new AssetSimpleInfo(tempAsset);
                        }

                        simpleInfo.CircuitSimpleInfo = circuitSimpleInfo;
                    }
                }
            }
        }
    }
}
