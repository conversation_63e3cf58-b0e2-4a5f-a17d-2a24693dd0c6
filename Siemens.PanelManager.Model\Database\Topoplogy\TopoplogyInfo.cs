﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Topology
{
    [SugarTable("topoplogy_info")]
    public class TopologyInfo: LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "topoplogy_type", Length = 256, IsNullable = false)]
        public string Type { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "code", Length = 256, IsNullable = true)]
        public string Code { get; set; } = Guid.NewGuid().ToString();
        [SugarColumn(ColumnName = "topoplogy_name", Length = 256, IsNullable = true)]
        public string Name { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "topoplogy_description", Length = 1024, IsNullable = true)]
        public string Description { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "topoplogy_maker", Length = 256, IsNullable = true)]
        public string Maker { get; set; } = string.Empty;
        /// <summary>
        /// 长度为10M
        /// </summary>
        [SugarColumn(ColumnName = "data", ColumnDataType = "varchar(10485760)", IsNullable = false)]
        public string Data { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "topoplogy_extend", ColumnDataType = "varchar(10240)", IsNullable = true)]
        public string? Extend { get; set; } = string.Empty;
        /// <summary>
        /// 组态图 更新标识
        /// 使用guid，当值不同代表被修改
        /// </summary>
        [SugarColumn(ColumnName = "topology_flag", IsNullable = true, Length = 50)]
        public string TopologyFlag { get; set; } = string.Empty;
    }
}
