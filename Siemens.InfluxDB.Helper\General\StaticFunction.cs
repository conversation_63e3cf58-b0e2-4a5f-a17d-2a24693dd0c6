﻿using InfluxDB.Client;
using InfluxDB.Client.Api.Domain;
using System.Collections.Concurrent;

namespace Siemens.InfluxDB.Helper.General
{
    static class StaticFunction
    {
        private static ConcurrentDictionary<string, Organization> _organizationDictionary = new ConcurrentDictionary<string, Organization>();
        private static ConcurrentDictionary<string, Bucket> _bucketDictionary = new ConcurrentDictionary<string, Bucket>();

        public static async Task<InitClientResult> InitClient(string url, string username, string password, string bucket, string org)
        {
            bucket = bucket.ToLower();
            org = org.ToLower();

            using var client = new InfluxDBClient(url, username, password);
            Organization orgItem;
            if (_organizationDictionary.TryGetValue(org, out Organization? existOrg) && existOrg != null)
            {
                orgItem = existOrg;
            }
            else
            {
                var orgApi = client.GetOrganizationsApi();
                var orgList = await orgApi.FindOrganizationsAsync(org: org);
                existOrg = orgList.FirstOrDefault();
                if (existOrg != null)
                {
                    orgItem = existOrg;
                }
                else 
                {
                    orgItem = await orgApi.CreateOrganizationAsync(org);
                }
                _organizationDictionary.TryAdd(org, orgItem);
            }

            Bucket bucketObj;
            if (_bucketDictionary.TryGetValue(bucket, out Bucket? existsBucket) && existsBucket != null)
            {
                bucketObj = existsBucket;
            }
            else
            {
                var bucketApi = client.GetBucketsApi();
                var bucketList = await bucketApi.FindBucketsAsync(name: bucket);
                existsBucket = bucketList.FirstOrDefault();
                if (existsBucket != null)
                {
                    bucketObj = existsBucket;
                }
                else
                {
                    bucketObj = await bucketApi.CreateBucketAsync(new Bucket()
                    {
                        Name = bucket,
                        OrgID = orgItem.Id
                    });
                }

                _bucketDictionary.TryAdd(bucket, bucketObj);
            }

            return new InitClientResult(client, orgItem, bucketObj);
        }
    }
}
