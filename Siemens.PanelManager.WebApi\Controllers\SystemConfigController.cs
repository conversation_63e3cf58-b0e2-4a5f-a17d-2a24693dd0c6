﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.WebApi.ViewModel;
using System.Text.RegularExpressions;
using System.Text;
using Microsoft.AspNetCore.Authorization;
using Swashbuckle.AspNetCore.Annotations;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.Model.Config;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.System;
using SqlSugar;
using Siemens.PanelManager.Interface.ActorRefs;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class SystemConfigController : SiemensApiControllerBase
    {
        private ILogger<SystemConfigController> _log;
        private SiemensCache _cache;
        private readonly IServiceProvider _provider;

        public SystemConfigController(IServiceProvider provider,
            SiemensCache cache,
            ILogger<SystemConfigController> log)
            : base(provider, cache)
        {
            _provider = provider;
            _cache = cache;
            _log = log;
        }

        [HttpGet("ntp_address")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_SystemConfig_GetNTPServerIPAddressAsync", Description = "Swagger_SystemConfig_GetNTPServerIPAddressAsync_Desc")]
        public async Task<ResponseBase<string>> GetNTPServerIPAddressAsync()
        {
            string ip = string.Empty;
            var config = _provider.GetRequiredService<IConfiguration>();
            var monitorApi = config.GetValue<string>("MonitorApi");
            if (!string.IsNullOrEmpty(monitorApi))
            {
                if (monitorApi.Last() != '/')
                {
                    monitorApi += '/';
                }

                using HttpClient client = new HttpClient();
                client.Timeout = TimeSpan.FromSeconds(15);

                var response = await client.GetAsync($"{monitorApi}api/Config/ntp_address");
                if (response != null && response.IsSuccessStatusCode)
                {
                    var resultStr = await response.Content.ReadAsStringAsync();

                    return new ResponseBase<string>()
                    {
                        Code = 20000,
                        Data = resultStr
                    };
                }
            }

            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = ip
            };
        }

        [HttpPost("ntp_address")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_SystemConfig_SetNTPServerIPAddressAsync", Description = "Swagger_SystemConfig_SetNTPServerIPAddressAsync_Desc")]
        public async Task<ResponseBase<string>> SetNTPServerIPAddressAsync([FromBody] IPConfigInfo ntpConfigInfo)
        {
            string isIP = @"^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
            string isUrl = @"^(?!https?://)[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*(\.[a-zA-Z]{2,})$";

            if (string.IsNullOrWhiteSpace(ntpConfigInfo.IpAddress)
                || (!Regex.IsMatch(ntpConfigInfo.IpAddress, isIP)
                && !Regex.IsMatch(ntpConfigInfo.IpAddress, isUrl)))
            {
                return new ResponseBase<string>()
                {
                    Code = 20000,
                    Data = "Error IP Or Error Url."
                };
            }

            var config = _provider.GetRequiredService<IConfiguration>();
            var monitorApi = config.GetValue<string>("MonitorApi");
            if (!string.IsNullOrEmpty(monitorApi))
            {
                if (monitorApi.Last() != '/')
                {
                    monitorApi += '/';
                }

                using HttpClient client = new HttpClient();
                client.Timeout = TimeSpan.FromSeconds(15);

                using StringContent jsonContent = new(JsonConvert.SerializeObject(ntpConfigInfo.IpAddress), Encoding.UTF8, "application/json");
                var response = await client.PostAsync($"{monitorApi}api/Config/ntp_address", jsonContent);
                if (response != null && response.IsSuccessStatusCode)
                {
                    var resultStr = await response.Content.ReadAsStringAsync();

                    return new ResponseBase<string>()
                    {
                        Code = 20000,
                        Data = resultStr
                    };
                }
            }

            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = "MonitorApi is empty or update config error."
            };
        }

        [HttpGet("x2p1_address")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_SystemConfig_GetX2P1IPAddressAsync", Description = "Swagger_SystemConfig_GetX2P1IPAddressAsync_Desc")]
        public async Task<ResponseBase<List<string>>> GetX2P1IPAddressAsync()
        {
            List<string> ipInfos = new List<string>();
            var config = _provider.GetRequiredService<IConfiguration>();
            var monitorApi = config.GetValue<string>("MonitorApi");
            if (!string.IsNullOrEmpty(monitorApi))
            {
                if (monitorApi.Last() != '/')
                {
                    monitorApi += '/';
                }

                using HttpClient client = new HttpClient();
                client.Timeout = TimeSpan.FromSeconds(15);

                var response = await client.GetAsync($"{monitorApi}api/Config/x2p1_address");
                if (response != null && response.IsSuccessStatusCode)
                {
                    var resultStr = await response.Content.ReadAsStringAsync();

                    return new ResponseBase<List<string>>()
                    {
                        Code = 20000,
                        Data = JsonConvert.DeserializeObject<List<string>>(resultStr)
                    };
                }
            }

            return new ResponseBase<List<string>>()
            {
                Code = 20000,
                Data = ipInfos
            };
        }

        [HttpPost("x2p1_address")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_SystemConfig_SetX2P1IPAddressAsync", Description = "Swagger_SystemConfig_SetX2P1IPAddressAsync_Desc")]
        public async Task<ResponseBase<string>> SetX2P1IPAddressAsync([FromBody] IPConfigInfo ipConfigInfo)
        {
            string isIP = @"^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";

            if (string.IsNullOrWhiteSpace(ipConfigInfo.IpAddress)
                || !Regex.IsMatch(ipConfigInfo.IpAddress, isIP))
            {
                return new ResponseBase<string>()
                {
                    Code = 20000,
                    Data = "Error IP"
                };
            }

            if (string.IsNullOrWhiteSpace(ipConfigInfo.SubnetMask)
                || !Regex.IsMatch(ipConfigInfo.SubnetMask, isIP))
            {
                return new ResponseBase<string>()
                {
                    Code = 20000,
                    Data = "Error Netmask"
                };
            }

            if (!string.IsNullOrWhiteSpace(ipConfigInfo.Gateway)
                && !Regex.IsMatch(ipConfigInfo.Gateway, isIP))
            {
                return new ResponseBase<string>()
                {
                    Code = 20000,
                    Data = "Error Gateway"
                };
            }

            var config = _provider.GetRequiredService<IConfiguration>();
            var monitorApi = config.GetValue<string>("MonitorApi");
            if (!string.IsNullOrEmpty(monitorApi))
            {
                if (monitorApi.Last() != '/')
                {
                    monitorApi += '/';
                }

                using HttpClient client = new HttpClient();
                client.Timeout = TimeSpan.FromSeconds(10);

                List<string> data = new List<string>
                {
                    ipConfigInfo.IpAddress,
                    ipConfigInfo.SubnetMask
                };

                if (!string.IsNullOrWhiteSpace(ipConfigInfo.Gateway))
                {
                    data.Add(ipConfigInfo.Gateway);
                }

                using StringContent jsonContent = new(JsonConvert.SerializeObject(data), Encoding.UTF8, "application/json");

                var response = await client.PostAsync($"{monitorApi}api/Config/x2p1_address", jsonContent);
                if (response != null && response.IsSuccessStatusCode)
                {
                    var resultStr = await response.Content.ReadAsStringAsync();

                    return new ResponseBase<string>()
                    {
                        Code = 20000,
                        Data = resultStr
                    };
                }
            }

            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = "MonitorApi is empty or update config error."
            };
        }


        [HttpGet("GetCircuitDataSourceConfig")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_SystemConfig_GetCircuitDataSourceConfig", Description = "Swagger_SystemConfig_GetCircuitDataSourceConfig_Desc")]
        public async Task<ResponseBase<List<CircuitDataSourceConfig>>> GetCircuitDataSourceConfig()
        {
            using (var client = _provider.GetRequiredService<ISqlSugarClient>())
            {
                var config = await client.Queryable<SystemConfig>().FirstAsync(c => c.Type == "CircuitConfig" && c.Name == "DataSource");

                if (config == null)
                {
                    return new ResponseBase<List<CircuitDataSourceConfig>>()
                    {
                        Code = 20000,
                        Data = new List<CircuitDataSourceConfig>()
                    };
                }
                var list = JsonConvert.DeserializeObject<List<CircuitDataSourceConfig>>(config.Value);

                if (list == null)
                {
                    return new ResponseBase<List<CircuitDataSourceConfig>>()
                    {
                        Code = 20000,
                        Data = new List<CircuitDataSourceConfig>()
                    };
                }

                var jsons = await client.Queryable<ThirdModelConfig>()
                    .Where(t => t.AssetType == "GeneralDevice")
                    .Select(t => new ThirdModelConfig
                {
                    Id = t.Id,
                    Name = t.Name,
                    Code = t.Code,
                }).ToArrayAsync();

                foreach (var l in list)
                {
                    var json = jsons.FirstOrDefault(j => j.Code == l.ThridPartCode);
                    if (json != null)
                    {
                        l.ThridPartName = json.Name;
                    }
                }

                return new ResponseBase<List<CircuitDataSourceConfig>>()
                {
                    Code = 20000,
                    Data = list
                };
            }
        }

        [HttpPost("UpdateCircuitDataSourceConfig")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_SystemConfig_UpdateCircuitDataSourceConfig", Description = "Swagger_SystemConfig_UpdateCircuitDataSourceConfig_Desc")]
        public async Task<ResponseBase<bool>> UpdateCircuitDataSourceConfig(List<CircuitDataSourceConfig> data)
        {
            using (var client = _provider.GetRequiredService<ISqlSugarClient>())
            {
                var config = await client.Queryable<SystemConfig>().FirstAsync(c => c.Type == "CircuitConfig" && c.Name == "DataSource");

                if (config == null)
                {
                    config = new SystemConfig()
                    {
                        Type = "CircuitConfig",
                        Name = "DataSource",
                        CreatedBy = UserName,
                        CreatedTime = DateTime.Now,
                        Sort = 1,
                        Value = string.Empty
                    };
                }

                var str = JsonConvert.SerializeObject(data);
                config.Value = str;
                config.UpdatedBy = UserName;
                config.UpdatedTime = DateTime.Now;
                await client.Storageable(config).ExecuteCommandAsync();
                var cache = _provider.GetRequiredService<SiemensCache>();
                cache.Clear("CircuitDataSourceConfig");

                var refObj = _provider.GetRequiredService<IAssetManagerRef>();
                refObj.UpdateCircuitSource();

                return new ResponseBase<bool>
                {
                    Code = 20000,
                    Data = true
                };
            }
        }

        [HttpGet("GetEnergyCoefficient")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_SystemConfig_GetEnergyCoefficientConfig", Description = "Swagger_SystemConfig_GetEnergyCoefficientConfig_Desc")]
        public async Task<ResponseBase<int?>> GetEnergyCoefficient()
        {
            using (var client = _provider.GetRequiredService<ISqlSugarClient>())
            {
                var config = await client.Queryable<SystemConfig>().FirstAsync(c => c.Type == "SpecialConfig" && c.Name == "EnergyCoefficient");
                if (config != null && int.TryParse(config.Value, out var coefficient))
                {
                    return new ResponseBase<int?>
                    {
                        Code = 20000,
                        Data = coefficient
                    };
                }

                return new ResponseBase<int?>
                {
                    Code = 20000,
                    Data = null
                };
            }
        }

        private const string EnergyCoefficientKey = "EnergyCoefficientKey";

        [HttpGet("SetEnergyCoefficient")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_SystemConfig_SetEnergyCoefficientConfig", Description = "Swagger_SystemConfig_SetEnergyCoefficientConfig_Desc")]
        public async Task<ResponseBase<bool>> SetEnergyCoefficient(int? coefficient)
        {
            using (var client = _provider.GetRequiredService<ISqlSugarClient>())
            {
                if (coefficient.HasValue)
                {
                    var config = await client.Queryable<SystemConfig>().FirstAsync(c => c.Type == "SpecialConfig" && c.Name == "EnergyCoefficient");
                    if (config == null)
                    {
                        config = new SystemConfig
                        {
                            CreatedBy = UserName,
                            CreatedTime = DateTime.Now,
                            UpdatedBy = UserName,
                            UpdatedTime = DateTime.Now,
                            Name = "EnergyCoefficient",
                            Type = "SpecialConfig",
                            Value = coefficient.Value.ToString(),
                            Sort = 1
                        };
                    }

                    config.Value = coefficient.Value.ToString();
                    config.UpdatedBy = UserName;
                    config.UpdatedTime = DateTime.Now;
                    await client.Storageable(config).ExecuteCommandAsync();
                }
                else
                {
                    await client.Deleteable<SystemConfig>()
                        .Where(c=> c.Type == "SpecialConfig" && c.Name == "EnergyCoefficient")
                        .ExecuteCommandAsync();
                }
            }

            _cache.Clear(EnergyCoefficientKey);

            return new ResponseBase<bool>
            {
                Code = 20000,
                Data = true,
            };
        }


        [HttpGet("GetCache")]
        [Authorize(policy: Permission.Default)]
        [SwaggerIgnore]
        public ResponseBase<object?> GetCache(string keyName)
        {
            var obj = _cache.Get<object>(keyName);

            return new ResponseBase<object?>
            {
                Code = 20000,
                Data = obj
            };
        }
    }
}
