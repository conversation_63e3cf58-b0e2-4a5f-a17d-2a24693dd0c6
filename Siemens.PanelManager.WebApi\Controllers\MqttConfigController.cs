﻿using Mapster;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Quartz.Impl.AdoJobStore.Common;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.Http;
using Siemens.PanelManager.Common.IO;
using Siemens.PanelManager.Common.mqtt;
using Siemens.PanelManager.Common.Mqtt;
using Siemens.PanelManager.Common.Tree;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Job.Mqtt;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.Model.UDC;
using Siemens.PanelManager.Server.Asset;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;
using System.ComponentModel;
using System.Diagnostics.CodeAnalysis;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Text.Unicode;
using JsonSerializer = System.Text.Json.JsonSerializer;


namespace Siemens.PanelManager.WebApi.Controllers
{
    /// <summary>
    /// mqtt配置接口服务
    /// </summary>
    [Route("api/v1/[controller]/[action]")]
    [ApiController]
    public class MqttConfigController : SiemensApiControllerBase, IDisposable
    {
        private readonly ISqlSugarClient _db;
        private readonly SiemensCache _cache;
        private readonly ILogger _logger;
        private readonly ExternalMqttAction _action;

        private const string MqttConfigCycleKey = "MqttDataConfig-{0}";
        private const string AssetMqttConfigKey = "AssetMqttConfig-{0}";

        /// <summary>
        ///  构造函数初始化
        /// </summary>
        /// <param name="provider"></param>
        /// <param name="cache"></param>
        /// <param name="logger"></param>
        public MqttConfigController(IServiceProvider provider, SiemensCache cache, ILogger<MqttConfigController> logger, ExternalMqttAction action) : base(provider, cache)
        {
            _cache = cache;
            _db = provider.GetService<ISqlSugarClient>()!;
            _logger = logger;
            _action = action;
        }

        /// <summary>
        ///  启动/停止mqtt(0:停止,1:启动)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [SwaggerOperation(Summary = "Swagger_Mqtt_MqttClientStartOrStop", Description = "Swagger_Mqtt_MqttClientStartOrStop_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> MqttClientStartOrStop([FromBody] SysConfigParam input)
        {
            ResponseBase<string>? result;

            try
            {
                //验证
                CheckValidation(input);

                var config = await input.Save(_db, UserName);

                switch (input.Status)
                {
                    case "1":
                        var mqttResult = await _action.StartMqtt(config);
                        if (mqttResult != null && !mqttResult.IsSuccess)
                        {
                            result = new ResponseBase<string>() { Code = 50000, Message = GetExcetionInfo(mqttResult.Msg) };
                        }
                        else
                        {
                            result = new ResponseBase<string>() { Code = 20000, Message = MessageContext.Success };
                        }
                        break;
                    default:
                        await _action.StopMqtt();
                        result = new ResponseBase<string>() { Code = 20000, Message = MessageContext.Success };
                        break;
                }


            }
            catch (Exception ex)
            {
                result = new ResponseBase<string>() { Code = 50000, Message = GetExcetionInfo(ex.Message) };
            }

            return Ok(result);
        }

        /// <summary>
        /// 获取mqtt状态
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [SwaggerOperation(Summary = "Swagger_Mqtt_GetMqttConnectedStatus", Description = "Swagger_Mqtt_GetMqttConnectedStatus_Desc")]
        [Authorize(policy: Permission.Default)]
        public IActionResult GetMqttConnectedStatus()
        {
            ResponseBase<string>? result;

            try
            {
                var resp = _action.GetMqttServerStatus();

                var msg = string.Empty;
                switch (resp)
                {
                    case 1:
                        msg = MessageContext.GetString("Mqtt_Info_Online");
                        break;
                    case 0:
                    default:
                        msg = MessageContext.GetString("Mqtt_Info_Brokenline");
                        break;
                }

                result = new ResponseBase<string>() { Code = 20000, Data = "....", Message = msg };
            }
            catch (Exception ex)
            {
                result = new ResponseBase<string>() { Code = 50000, Message = GetExcetionInfo(ex.Message) };
            }

            return Ok(result);
        }

        /// <summary>
        /// 查询mqtt配置信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [SwaggerOperation(Summary = "Swagger_Mqtt_GetMqttConfig", Description = "Swagger_Mqtt_GetMqttConfig_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> GetMqttConfig()
        {
            ResponseBase<SysConfigResult> result;

            try
            {
                var sysConfig = new SysConfigResult();
                await sysConfig.InitBySql(_db);
                var resp = _action.GetMqttServerStatus();
                // Status(1:在线,0:断线)
                switch (resp)
                {
                    case 1:
                        sysConfig.Status = "1";
                        break;
                    case 0:
                        sysConfig.Status = "0";
                        break;
                    case 2:
                        sysConfig.Status = "1";
                        break;
                }

                result = new ResponseBase<SysConfigResult>() { Code = 20000, Data = sysConfig, Message = MessageContext.GetString("Mqtt_Info_QueryWasSuccessful") };
            }
            catch (Exception ex)
            {
                result = new ResponseBase<SysConfigResult>() { Code = 50000, Data = null, Message = GetExcetionInfo(ex.Message) };
            }

            return Ok(result);
        }

        /// <summary>
        /// 更新mqtt配置信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [SwaggerOperation(Summary = "Swagger_Mqtt_ModifyMqttConfig", Description = "Swagger_Mqtt_ModifyMqttConfig_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<bool>> ModifyMqttConfig([FromBody] SysConfigParam input)
        {
            try
            {
                //验证
                CheckValidation(input);

                await input.Save(_db, UserName);

                return new ResponseBase<bool>() { Code = 20000, Data = true, Message = MessageContext.GetString("Mqtt_Info_UpdateSuccessful") };
            }
            catch (AggregateException ex)
            {
                return new ResponseBase<bool>() { Code = 40300, Data = false, Message = ex.Message };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ModifyMqttConfig 保存失败");
                return new ResponseBase<bool>() { Code = 50000, Data = false, Message = MessageContext.ServerException };
            }
        }

        /// <summary>
        /// 测试mqtt服务连接
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [SwaggerOperation(Summary = "Swagger_Mqtt_PingTestMqttConfig", Description = "Swagger_Mqtt_PingTestMqttConfig_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> PingTestMqttConfig([FromBody] SysConfigParam input)
        {
            ResponseBase<string> result;

            try
            {
                //验证
                CheckValidation(input);

                //类型转换
                var mqttConfig = input.Adapt<MqttConfig>();

                //启动
                var resp = await _action.CheckMqttConfig(mqttConfig)!;

                if (resp != null && !resp.IsSuccess)
                {
                    //throw new Exception(resp.Msg);
                    return Ok(new ResponseBase<string>() { Code = 50000, Message = GetExcetionInfo(resp.Msg) });
                }

                result = new ResponseBase<string>() { Code = 20000, Message = MessageContext.GetString("Mqtt_Info_ConnectionSuccessful") };

            }
            catch (Exception ex)
            {
                result = new ResponseBase<string>() { Code = 50000, Message = GetExcetionInfo(ex.Message) };
            }

            return Ok(result);

        }

        /// <summary>
        /// 上传Tls证书
        /// </summary>
        /// <param name="file"></param>
        /// <param name="tag">TLSPemFile，ClientPrivateFile，ClientPemFile</param>
        /// <returns></returns>
        [HttpPost]
        [SwaggerOperation(Summary = "Swagger_Mqtt_UploadTlsFiles", Description = "Swagger_Mqtt_UploadTlsFiles_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> UploadTlsFiles(IFormFile file, [FromQuery] string tag)
        {
            if (!"TLSPemFile".Equals(tag, StringComparison.OrdinalIgnoreCase)
                && !"ClientPrivateFile".Equals(tag, StringComparison.OrdinalIgnoreCase)
                && !"ClientPemFile".Equals(tag, StringComparison.OrdinalIgnoreCase))
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            try
            {
                var tlsFileName = string.Empty;
                if ("TLSPemFile".Equals(tag, StringComparison.OrdinalIgnoreCase))
                {
                    tlsFileName = "TLSPemFile";
                }
                else if ("ClientPrivateFile".Equals(tag, StringComparison.OrdinalIgnoreCase))
                {
                    tlsFileName = "ClientPrivateFile";
                }
                else
                {
                    tlsFileName = "ClientPemFile";
                }

                var config = await _db.Queryable<SystemConfig>().Where(c => c.Type == "MQTT" && tlsFileName.Equals(c.Name)).FirstAsync();

                FileManager? oldFileManager = null;

                if (config != null)
                {
                    oldFileManager = await _db.Queryable<FileManager>().Where(f => (f.Code == config.Value || f.Url == config.Value) && !f.IsSystemFile).FirstAsync();
                }
                else
                {
                    config = new SystemConfig
                    {
                        Type = "MQTT",
                        Name = tag,
                        Extend = null,
                        Sort = 0,
                        CreatedBy = UserName,
                        CreatedTime = DateTime.Now,
                    };
                }

                var m = Regex.IsMatch(file.FileName, "\\.(pem|key)$", RegexOptions.IgnoreCase);

                if (!m)
                {
                    return new ResponseBase<string>() { Code = 50000, Message = GetExcetionInfo(MessageContext.GetString("Mqtt_Err_Puafwtspemorkey")) };
                }

                var fileManager = await FileManagerFunc.CreateFileManager(file, UserName, _logger);

                if (fileManager != null && !string.IsNullOrEmpty(fileManager.Url))
                {
                    try
                    {
                        await _db.Ado.BeginTranAsync();
                        if (oldFileManager != null)
                        {
                            FileManagerFunc.RemoveFile(oldFileManager, _logger);
                            await _db.Deleteable(oldFileManager).ExecuteCommandAsync();
                        }

                        config.Value = fileManager.Code;
                        config.UpdatedBy = UserName;
                        config.UpdatedTime = DateTime.Now;

                        await _db.Insertable(fileManager).ExecuteCommandAsync();
                        await _db.Storageable(config).ExecuteCommandAsync();
                        await _db.Ado.CommitTranAsync();

                        return new ResponseBase<string>() { Code = 20000, Data = fileManager.Url, Message = MessageContext.GetString("Mqtt_Info_Successfullyuploadedcertificate") };
                    }
                    catch (Exception ex)
                    {
                        await _db.Ado.RollbackTranAsync();
                        _logger.LogError(ex, $"{fileManager.Name} 数据保存失败");
                        return new ResponseBase<string>() { Code = 50000, Message = MessageContext.ServerException };
                    }
                }

                return new ResponseBase<string>() { Code = 50000, Message = MessageContext.ServerException };
                #region Old Code

                //string extensionName = Path.GetExtension(file.FileName);
                //string noExtensionName = extensionName.Replace(".", "");
                //string fileName = Path.GetFileNameWithoutExtension(file.FileName);
                //string fileCode = "6cece0ac-aae4-89b5-a3b6-f5bb83643220_" + fileName + "_" + noExtensionName;
                //string fullName = fileCode + extensionName;

                //if (!(fullName.Contains(".pem") || fullName.Contains(".key")))
                //{

                //}

                //Stream fileStream = file.OpenReadStream();
                //var currentPath = Directory.GetCurrentDirectory();

                ////判断文件夹是否存在
                //var uploadFileDir = Path.Combine(currentPath, "wwwroot", "uploadfiles", "personal");
                //if (!Directory.Exists(uploadFileDir))
                //{
                //    Directory.CreateDirectory(uploadFileDir);
                //}

                ////判断文件是否存在
                //var filePath = Path.Combine(uploadFileDir, fullName);
                //if (System.IO.File.Exists(filePath))
                //{
                //    //删除文件
                //    System.IO.File.Delete(filePath);

                //    //删除数据库数据
                //    await _db.Deleteable<FileManager>().Where(p => p.Code == fileCode).ExecuteCommandAsync();
                //}

                ////保存文件
                //FileHelper.StreamToFile(fileStream, filePath);

                //// 文件配置
                //var fileManager = new FileManager()
                //{
                //    FileType = noExtensionName,
                //    Name = fileCode,
                //    Code = fileCode,
                //    Url = $"/uploadfiles/personal/{fullName}",
                //    CreatedBy = UserName,
                //    CreatedTime = DateTime.Now,
                //    UpdatedBy = UserName,
                //    UpdatedTime = DateTime.Now,
                //};
                #endregion
            }
            catch (Exception ex)
            {
                return new ResponseBase<string>() { Code = 50000, Message = MessageContext.ServerException };
            }
        }

        /// <summary>
        /// 删除Tls证书
        /// </summary>
        /// <param name="tag"></param>
        /// <returns></returns>
        [HttpDelete]
        [SwaggerOperation(Summary = "Swagger_Mqtt_DeleteTlsFiles", Description = "Swagger_Mqtt_DeleteTlsFiles_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<bool>> DeleteTlsFiles([FromQuery] string tag)
        {
            if (!"TLSPemFile".Equals(tag, StringComparison.OrdinalIgnoreCase)
                && !"ClientPrivateFile".Equals(tag, StringComparison.OrdinalIgnoreCase)
                && !"ClientPemFile".Equals(tag, StringComparison.OrdinalIgnoreCase))
            {
                return new ResponseBase<bool>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            try
            {
                var tlsFileName = string.Empty;
                if ("TLSPemFile".Equals(tag, StringComparison.OrdinalIgnoreCase))
                {
                    tlsFileName = "TLSPemFile";
                }
                else if ("ClientPrivateFile".Equals(tag, StringComparison.OrdinalIgnoreCase))
                {
                    tlsFileName = "ClientPrivateFile";
                }
                else
                {
                    tlsFileName = "ClientPemFile";
                }

                var config = await _db.Queryable<SystemConfig>().Where(c => c.Type == "MQTT" && tlsFileName.Equals(c.Name)).FirstAsync();

                if (config != null)
                {
                    var fileManager = await _db.Queryable<FileManager>().Where(f => (f.Code == config.Value || f.Url == config.Value) && !f.IsSystemFile).FirstAsync();

                    if (fileManager != null)
                    {
                        FileManagerFunc.RemoveFile(fileManager, _logger);
                        await _db.Deleteable(fileManager).ExecuteCommandAsync();
                    }

                    config.Value = string.Empty;
                    config.UpdatedBy = UserName;
                    config.UpdatedTime = DateTime.Now;

                    await _db.Updateable(config)
                        .UpdateColumns(config => new
                        {
                            Value = config.Value,
                            UpdatedBy = config.UpdatedBy,
                            UpdatedTime = config.UpdatedTime
                        })
                        .ExecuteCommandAsync();
                }

                return new ResponseBase<bool>() { Code = 20000, Data = true, Message = MessageContext.GetString("Mqtt_Info_Successfullydeleted") };
            }
            catch (Exception ex)
            {
                return new ResponseBase<bool>() { Code = 50000, Data = false, Message = MessageContext.ServerException };
            }
        }

        /// <summary>
        /// 查询点位配置信息
        /// </summary>
        /// <param name="assetId">资产信息主键id</param>
        /// <returns></returns>
        [HttpGet]
        [SwaggerOperation(Summary = "Swagger_Mqtt_GetPointConfigs", Description = "Swagger_Mqtt_GetPointConfigs_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> GetPointConfigs([FromQuery] string? assetId)
        {
            ResponseBase<List<ConfigurePointsDto>> result;

            try
            {
                if (string.IsNullOrWhiteSpace(assetId) || !int.TryParse(assetId, out var tempAssetId))
                {
                    result = new ResponseBase<List<ConfigurePointsDto>>() { Code = 20000, Data = new List<ConfigurePointsDto>() };

                    return Ok(result);
                }

                // 查询资产信息
                var assetInfo = await _db.Queryable<AssetInfo>().Where(p => p.Id == tempAssetId).FirstAsync();

                if (assetInfo == null)
                {
                    result = new ResponseBase<List<ConfigurePointsDto>>() { Code = 20000, Data = new List<ConfigurePointsDto>() };

                    return Ok(result);
                }

                //获取所有目录集合
                var assetDataPointDirectorys = await _db.Queryable<AssetDataPointDirectory>()
                    .WhereIF(assetInfo.AssetLevel == AssetLevel.Device, a => a.AssetModel == assetInfo.AssetModel)
                    .WhereIF(assetInfo.AssetLevel != AssetLevel.Device, a => a.AssetLevel == assetInfo.AssetLevel)
                    .OrderBy(a => a.Sort)
                    .ToListAsync();

                //查询选中的mqttDataPointConfig信息
                var assetMqttDataPointConfigs = await _db.Queryable<AssetMqttDataPointConfig>()
                    .Where(p => p.AssetId == tempAssetId && p.ConfigType == GroupConfigType.Measurement).ToListAsync();

                //查询当前资产的点位信息
                var assetDataPointInfos = await _db.Queryable<AssetDataPointInfo>()
                    .WhereIF(assetInfo.AssetLevel == AssetLevel.Device, p => p.AssetModel == assetInfo.AssetModel)
                    .WhereIF(assetInfo.AssetLevel != AssetLevel.Device, p => p.AssetLevel == assetInfo.AssetLevel)
                    .ToListAsync();


                #region 第三方设备mqtt文件夹添加

                var (assetDataPointDirectoryList, assetDataPointInfoList) = await InitThirdPartyDevicesCfg(Convert.ToInt32(assetId));

                if (assetDataPointDirectoryList.Any())
                {
                    assetDataPointDirectorys.AddRange(assetDataPointDirectoryList);
                }

                if (assetDataPointInfoList.Any())
                {
                    assetDataPointInfos.AddRange(assetDataPointInfoList);
                }

                #endregion

                //树形结构数据
                var treeNodes = new List<ConfigurePointsDto>();

                //查询目录下的点位信息
                if (assetDataPointDirectorys.Any())
                {
                    //树形结构临时表数据
                    treeNodes = assetDataPointDirectorys.Select(p => new ConfigurePointsDto()
                    {
                        Id = p.Id,
                        Pid = string.IsNullOrEmpty(p.ParentName) ? "0" : p.ParentName,
                        Rid = p.Name,
                        Name = p.Name,
                        Label = MessageContext.GetString($"Directory_{p.LanguageKey}") ?? p.Name,
                        IsPoint = false,
                        IsCheck = 0,
                        AssetModel = p.AssetModel

                    }).ToList();

                    // 获取最后一级的数据且判断是否选中
                    foreach (var item in assetDataPointInfos)
                    {
                        if (!string.IsNullOrWhiteSpace(item.ParentName))
                        {
                            var entity = new ConfigurePointsDto()
                            {
                                Id = item.Id,
                                Pid = item.ParentName,
                                Rid = item.Name,
                                Name = item.Name,
                                Label = MessageContext.GetString($"DataPoint_{item.Name}") ?? item.Name,
                                IsPoint = true,
                                IsCheck = 0,
                                Code = item.Code,
                                AssetModel = item.AssetModel
                            };

                            if (assetMqttDataPointConfigs.Exists(p => p.Code == item.Code))
                            {
                                entity.IsCheck = 1;
                            }

                            int i = 0;
                            var dic = new Dictionary<int, string?>();
                            string? parentName = item.ParentName;
                            while (!string.IsNullOrWhiteSpace(parentName))
                            {
                                i++;

                                var assetDataPointDirectory = assetDataPointDirectorys.FirstOrDefault(p => p.Name == parentName);

                                parentName = assetDataPointDirectory?.ParentName ?? "";

                                dic.Add(i, MessageContext.GetString($"Directory_{assetDataPointDirectory?.LanguageKey}") ?? assetDataPointDirectory?.Name);
                            }

                            var directoryList = dic.OrderByDescending(p => p.Key).Select(p => p.Value).ToList();
                            entity.Directory = string.Join("/", directoryList);
                            treeNodes.Add(entity);
                        }
                    }
                }

                var treeData = TreeHelper.BuildTree(treeNodes, "0");

                result = new ResponseBase<List<ConfigurePointsDto>>() { Code = 20000, Data = treeData, Message = MessageContext.GetString("Mqtt_Info_QueryWasSuccessful") };

            }
            catch (Exception ex)
            {
                result = new ResponseBase<List<ConfigurePointsDto>>() { Code = 50000, Data = null, Message = MessageContext.ServerException };
            }

            return Ok(result);
        }

        /// <summary>
        /// 初始化第三方设备的点位配置
        /// </summary>
        /// <returns></returns>
        private async Task<(List<AssetDataPointDirectory>, List<AssetDataPointInfo>)> InitThirdPartyDevicesCfg(int assetId)
        {
            var thirdModelConfig = await _db.Queryable<ThirdModelConfig>()
                    .LeftJoin<AssetInfo>((t1, t2) => t1.Code == t2.ThirdPartCode)
                    .Where((t1, t2) => t2.Id == assetId)
                    .Select((t1, t2) => new
                    {
                        t1.Code,
                        t1.JsonData

                    }).FirstAsync();

            if (thirdModelConfig == null)
            {
                return (new List<AssetDataPointDirectory>(), new List<AssetDataPointInfo>());
            }

            long maxId1 = await _db.Queryable<AssetDataPointDirectory>().MaxAsync(p => p.Id);

            long maxId2 = await _db.Queryable<AssetDataPointInfo>().MaxAsync(p => p.Id);

            var jsonData = JsonConvert.DeserializeObject<ThreePartModelTemplate>(thirdModelConfig.JsonData ?? "");

            var assetDataPointDirectorys = new List<AssetDataPointDirectory>();

            var assetDataPointInfos = new List<AssetDataPointInfo>();

            if (jsonData != null && jsonData.Treeview != null && jsonData.Treeview.Any())
            {
                foreach (var item in jsonData.Treeview!)
                {
                    //获取子集集合
                    if (item.SubGroups != null && item.SubGroups!.Any())
                    {
                        foreach (var _item in item.SubGroups!)
                        {
                            maxId1++;

                            if (!assetDataPointDirectorys.Exists(p => p.Name == _item.Name))
                            {
                                assetDataPointDirectorys.Add(new AssetDataPointDirectory
                                {
                                    Id = maxId1,
                                    ParentName = "",
                                    Name = _item.Name ?? "",
                                    LanguageKey = _item.Name ?? "",
                                    AssetModel = "GeneralDevice"
                                });
                            }

                            if (_item.Properties != null && _item.Properties.Any())
                            {
                                foreach (var secondItem in _item.Properties)
                                {
                                    maxId2++;

                                    assetDataPointInfos.Add(new AssetDataPointInfo
                                    {
                                        Id = Convert.ToInt32(maxId2),
                                        ParentName = _item.Name ?? "",
                                        Name = secondItem.DescriptionInEnglish ?? "",
                                        Code = secondItem.PropertyName ?? "",
                                        AssetModel = "GeneralDevice"
                                    });
                                }
                            }
                        }
                    }

                    if (item.Properties != null && item.Properties.Any())
                    {
                        foreach (var _item in item.Properties)
                        {
                            maxId1++;
                            maxId2++;

                            if (!assetDataPointDirectorys.Exists(p => p.Name == _item.GroupName))
                            {
                                assetDataPointDirectorys.Add(new AssetDataPointDirectory
                                {
                                    Id = maxId1,
                                    ParentName = "",
                                    Name = _item.GroupName ?? "",
                                    LanguageKey = _item.GroupName ?? "",
                                    AssetModel = "GeneralDevice"
                                });
                            }

                            assetDataPointInfos.Add(new AssetDataPointInfo
                            {
                                Id = Convert.ToInt32(maxId2),
                                ParentName = _item.GroupName ?? "",
                                Name = _item.DescriptionInEnglish ?? "",
                                Code = _item.PropertyName ?? "",
                                AssetModel = "GeneralDevice"
                            });
                        }
                    }
                }
            }

            return (assetDataPointDirectorys, assetDataPointInfos);
        }

        /// <summary>
        /// 查询分组点位配置信息
        /// </summary>
        /// <param name="assetId">资产信息主键id</param>
        /// <returns></returns>
        [HttpGet]
        [SwaggerOperation(Summary = "Swagger_Mqtt_GetMqttDataPointConfigs", Description = "Swagger_Mqtt_GetMqttDataPointConfigs_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> GetMqttDataPointConfigs([FromQuery] string? assetId)
        {
            ResponseBase<List<ConfigurePointsResult>> result;

            try
            {
                if (string.IsNullOrWhiteSpace(assetId) || !int.TryParse(assetId, out var tempAssetId))
                {
                    result = new ResponseBase<List<ConfigurePointsResult>>() { Code = 20000, Data = new List<ConfigurePointsResult>() };

                    return Ok(result);
                }

                // 查询资产信息
                var assetInfo = await _db.Queryable<AssetInfo>().Where(p => p.Id == tempAssetId).FirstAsync();

                if (assetInfo == null)
                {
                    result = new ResponseBase<List<ConfigurePointsResult>>() { Code = 20000, Data = new List<ConfigurePointsResult>(), Message = MessageContext.GetString("Mqtt_Info_QueryWasSuccessful") };

                    return Ok(result);
                }

                //构建树形结构
                var treeData = new List<ConfigurePointsResult>();

                //获取所有目录集合
                var assetDataPointDirectorys = await _db.Queryable<AssetDataPointDirectory>()
                    .WhereIF(assetInfo.AssetLevel == AssetLevel.Device, a => a.AssetModel == assetInfo.AssetModel)
                    .WhereIF(assetInfo.AssetLevel != AssetLevel.Device, a => a.AssetLevel == assetInfo.AssetLevel)
                    .OrderBy(a => a.Sort)
                    .ToListAsync();

                //查询当前资产的点位信息
                var assetDataPointInfos = await _db.Queryable<AssetDataPointInfo>()
                    .WhereIF(assetInfo.AssetLevel == AssetLevel.Device, p => p.AssetModel == assetInfo.AssetModel)
                    .WhereIF(assetInfo.AssetLevel != AssetLevel.Device, p => p.AssetLevel == assetInfo.AssetLevel)
                    .ToListAsync();

                // 获取分组的全部数据
                var assetMqttDataPointConfigs = await _db.Queryable<AssetMqttDataPointConfig>()
                    .Where(p => p.AssetId == tempAssetId).ToListAsync();

                //一级目录数据集合
                var firstDirectorys = assetMqttDataPointConfigs.Where(p => p.ConfigType == GroupConfigType.Group).ToList();

                //二级点位数据集合
                var sencodPoints = assetMqttDataPointConfigs.Where(p => p.ConfigType == GroupConfigType.Measurement).ToList();

                if (firstDirectorys.Any())
                {
                    foreach (var item in firstDirectorys)
                    {
                        var entity = new ConfigurePointsResult()
                        {
                            Id = item.Id,
                            AssetId = item.AssetId,
                            Name = item.Name,
                            Label = MessageContext.GetString($"Mqtt_Group_Name_{item.Name}") ?? item.Name,
                            IsPoint = false,
                            IsCheck = 0,
                            Code = item.Code,
                            Directory = "",
                            SamplingPeriod = item.SamplingPeriod,
                            AssetModel = item.AssetModel
                        };

                        if (sencodPoints.Any())
                        {
                            foreach (var _item in sencodPoints)
                            {
                                if (_item.GroupName == item.Name)
                                {
                                    var children = new ConfigurePoints()
                                    {
                                        Id = _item.Id,
                                        AssetId = item.AssetId,
                                        Name = _item.Name,
                                        Label = MessageContext.GetString($"DataPoint_{_item.Name}") ?? _item.Name,
                                        IsPoint = true,
                                        IsCheck = 1,
                                        Code = _item.Code,
                                        SamplingPeriod = 0,
                                        AssetModel = _item.AssetModel
                                    };

                                    var assetDataPointInfo = assetDataPointInfos.FirstOrDefault(p => p.Code == _item.Code);

                                    int i = 0;
                                    var dic = new Dictionary<int, string?>();
                                    var assetDataPointDirectory = assetDataPointDirectorys.FirstOrDefault(p => p.Name == assetDataPointInfo?.ParentName);

                                    string? parentName = assetDataPointDirectory?.Name;

                                    while (!string.IsNullOrWhiteSpace(parentName))
                                    {
                                        i++;

                                        var _assetDataPointDirectory = assetDataPointDirectorys.FirstOrDefault(p => p.Name == parentName);

                                        parentName = _assetDataPointDirectory?.ParentName ?? "";

                                        dic.Add(i, MessageContext.GetString($"Directory_{_assetDataPointDirectory?.LanguageKey}") ?? _assetDataPointDirectory?.Name);
                                    }

                                    var directoryList = dic.OrderByDescending(p => p.Key).Select(p => p.Value).ToList();
                                    children.Directory = string.Join("/", directoryList);
                                    entity.Children?.Add(children);

                                }
                            }
                        }

                        treeData.Add(entity);
                    }
                }

                result = new ResponseBase<List<ConfigurePointsResult>>() { Code = 20000, Data = treeData, Message = MessageContext.GetString("Mqtt_Info_QueryWasSuccessful") };

            }
            catch (Exception ex)
            {
                result = new ResponseBase<List<ConfigurePointsResult>>() { Code = 50000, Data = null, Message = MessageContext.ServerException };
            }

            return Ok(result);
        }

        /// <summary>
        /// 重命名分组名称
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [SwaggerOperation(Summary = "Swagger_Mqtt_ModifyGroupPointConfig", Description = "Swagger_Mqtt_ModifyGroupPointConfig_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> ModifyMqttDataPointConfig([FromBody] MqttDataPointConfigParam input)
        {
            ResponseBase<string> result;

            try
            {
                if (input.Id == 0)
                {
                    //throw new Exception(MessageContext.GetString("Mqtt_Err_Idcannotbeorempty"));

                    return Ok(new ResponseBase<string>() { Code = 50000, Message = MessageContext.GetString("Mqtt_Err_Idcannotbeorempty") });
                }

                if (string.IsNullOrEmpty(input.GroupName))
                {
                    // throw new Exception(MessageContext.GetString("Mqtt_Err_Groupnamecannotbeempty"));

                    return Ok(new ResponseBase<string>() { Code = 50000, Message = MessageContext.GetString("Mqtt_Err_Groupnamecannotbeempty") });
                }

                //获取一级目录
                var assetMqttDataPointConfig = await _db.Queryable<AssetMqttDataPointConfig>().FirstAsync(p => p.Id == input.Id);

                bool flag = await _db.Queryable<AssetMqttDataPointConfig>()
                    .AnyAsync(p => p.Name == input.GroupName
                                   && p.ConfigType == GroupConfigType.Group
                                   && p.AssetId == assetMqttDataPointConfig.AssetId);

                if (flag)
                {
                    //throw new Exception(MessageContext.GetString("Mqtt_Err_Tiagnwtsnpmitadname"));

                    return Ok(new ResponseBase<string>() { Code = 50000, Message = (MessageContext.GetString("Mqtt_Err_Tiagnwtsnpmitadname")) });
                }

                if (assetMqttDataPointConfig != null)
                {
                    //获取二级点位数据
                    var assetMqttDataPointConfigs = await _db.Queryable<AssetMqttDataPointConfig>()
                                   .Where(p => p.AssetId == assetMqttDataPointConfig.AssetId
                                    && p.ConfigType == GroupConfigType.Measurement
                                    && p.GroupName == assetMqttDataPointConfig.Name).ToListAsync();

                    //修改分组名称
                    assetMqttDataPointConfig.Name = input.GroupName;
                    assetMqttDataPointConfig.UpdatedTime = DateTime.Now;

                    if (assetMqttDataPointConfigs.Any())
                    {
                        foreach (var item in assetMqttDataPointConfigs)
                        {
                            item.GroupName = input.GroupName;
                            item.UpdatedTime = DateTime.Now;
                        }
                    }

                    //添加一级目录的内容
                    assetMqttDataPointConfigs.Add(assetMqttDataPointConfig);

                    //更新
                    await _db.Updateable(assetMqttDataPointConfigs).UpdateColumns(p => new { p.GroupName, p.Name, p.UpdatedTime }).ExecuteCommandAsync();
                }

                

                result = new ResponseBase<string>() { Code = 20000, Message = MessageContext.GetString("Mqtt_Info_UpdateSuccessful") };

            }
            catch (Exception ex)
            {
                result = new ResponseBase<string>() { Code = 50000, Message = MessageContext.ServerException };
            }

            return Ok(result);
        }

        /// <summary>
        /// 新增分组
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [SwaggerOperation(Summary = "Swagger_Mqtt_AddGroupConfig", Description = "Swagger_Mqtt_AddGroupConfig_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> AddGroupConfig([FromBody] MqttGroupPointConfigParam input)
        {
            ResponseBase<string> result;

            try
            {
                if (input.AssetId == 0)
                {
                    //throw new Exception(MessageContext.GetString("Mqtt_Err_AssetIdcannotbeempty"));

                    return Ok(new ResponseBase<string>() { Code = 50000, Message = MessageContext.GetString("Mqtt_Err_AssetIdcannotbeempty") });
                }

                bool flag = await _db.Queryable<AssetMqttDataPointConfig>()
                                   .AnyAsync(p => p.Name == input.Name
                                   && p.ConfigType == GroupConfigType.Group
                                   && p.AssetId == input.AssetId);

                // 判断是否存在同名分组名称
                if (flag)
                {
                    // throw new Exception(MessageContext.GetString("Mqtt_Err_Tiagnwtsnpmitadname"));

                    return Ok(new ResponseBase<string>() { Code = 50000, Message = MessageContext.GetString("Mqtt_Err_Tiagnwtsnpmitadname") });
                }

                //开启事务
                _db.Ado.BeginTran();

                //类型转换
                var entity = input.Adapt<AssetMqttDataPointConfig>();

                entity.Code = "";
                entity.ConfigType = GroupConfigType.Group;
                entity.CreatedBy = UserName;
                entity.CreatedTime = DateTime.Now;
                entity.UpdatedBy = UserName;
                entity.UpdatedTime = DateTime.Now;

                //添加分组
                await _db.Insertable(entity).ExecuteCommandAsync();

                //修改资产信息
                var assetInfo = await _db.Queryable<AssetInfo>().Where(p => p.Id == input.AssetId).FirstAsync();
                if (assetInfo != null)
                {
                    var service = Provider.GetRequiredService<AssetExtendServer>();

                    await service.UpdateAssetEnableMqtt(assetInfo, true, UserName, _db);
                }

                _db.Ado.CommitTran();

                result = new ResponseBase<string>() { Code = 20000, Message = MessageContext.GetString("Mqtt_Info_SaveSuccessful") };

            }
            catch (Exception ex)
            {
                //提交事务
                _db.Ado.RollbackTran();

                result = new ResponseBase<string>() { Code = 50000, Message = MessageContext.ServerException };
            }

            return Ok(result);
        }

        /// <summary>
        /// 保存分组点位信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [SwaggerOperation(Summary = "Swagger_Mqtt_AddMqttDataPointConfig", Description = "Swagger_Mqtt_AddMqttDataPointConfig_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> AddMqttDataPointConfig([FromBody] MqttPointConfigParam input)
        {
            ResponseBase<string> result;

            try
            {
                if (input.AssetId == 0)
                {
                    // throw new Exception(MessageContext.GetString("Mqtt_Err_AssetIdcannotbeempty"));
                    return Ok(new ResponseBase<string>() { Code = 50000, Message = MessageContext.GetString("Mqtt_Err_AssetIdcannotbeempty") });
                }

                List<int> periods = new List<int>();

                // 启用事务
                _db.Ado.BeginTran();

                //删除之前的数据
                await _db.Deleteable<AssetMqttDataPointConfig>().Where(p => p.AssetId == input.AssetId).ExecuteCommandAsync();

                var assetMqttDataPointConfigs = new List<AssetMqttDataPointConfig>();

                if (input.DataList != null && input.DataList.Any())
                {
                    foreach (var item in input.DataList)
                    {
                        assetMqttDataPointConfigs.Add(new AssetMqttDataPointConfig()
                        {
                            Code = item.Code,
                            AssetId = input.AssetId,
                            ConfigType = GroupConfigType.Group,
                            GroupName = "",
                            Name = item.Name,
                            AssetModel = item.AssetModel,
                            SamplingPeriod = item.SamplingPeriod,
                            CreatedBy = UserName,
                            CreatedTime = DateTime.Now,
                            UpdatedBy = UserName,
                            UpdatedTime = DateTime.Now,
                        });

                        periods.Add(item.SamplingPeriod);

                        if (item.Children != null && item.Children.Any())
                        {
                            foreach (var _item in item.Children)
                            {
                                assetMqttDataPointConfigs.Add(new AssetMqttDataPointConfig()
                                {
                                    Code = _item.Code,
                                    AssetId = input.AssetId,
                                    ConfigType = GroupConfigType.Measurement,
                                    GroupName = item.Name,
                                    Name = _item.Name,
                                    AssetModel = _item.AssetModel,
                                    SamplingPeriod = _item.SamplingPeriod,
                                    CreatedBy = UserName,
                                    CreatedTime = DateTime.Now,
                                    UpdatedBy = UserName,
                                    UpdatedTime = DateTime.Now,
                                });
                            }
                        }
                    }

                    var totalNum = assetMqttDataPointConfigs.Count(p => p.ConfigType == GroupConfigType.Group);

                    var groupNum = assetMqttDataPointConfigs.Where(p => p.ConfigType == GroupConfigType.Group).Select(p => p.Name).Distinct().Count();

                    if (totalNum != groupNum)
                    {
                        // throw new Exception(MessageContext.GetString("Mqtt_Err_Thegroupnameisduplicated"));

                        return Ok(new ResponseBase<string>() { Code = 50000, Message = MessageContext.GetString("Mqtt_Err_Thegroupnameisduplicated") });
                    }

                    //批量插入
                    await _db.Insertable(assetMqttDataPointConfigs).ExecuteCommandAsync();

                    //修改资产信息
                    var assetInfo = await _db.Queryable<AssetInfo>().Where(p => p.Id == input.AssetId).FirstAsync();
                    if (assetInfo != null)
                    {
                        var service = Provider.GetRequiredService<AssetExtendServer>();

                        await service.UpdateAssetEnableMqtt(assetInfo, true, UserName, _db);
                    }
                }

                //提交事务
                _db.Ado.CommitTran();

                #region 管理缓存
                {
                    var simpleInfoKey = string.Format("Asset:SimpleInfo-{0}", input.AssetId);
                    var assetSimpleInfo = _cache.Get<AssetSimpleInfo>(simpleInfoKey);
                    if (assetSimpleInfo != null && !assetSimpleInfo.EnableMqtt)
                    {
                        assetSimpleInfo.EnableMqtt = true;
                        _cache.Set(simpleInfoKey, assetSimpleInfo);
                    }
                    periods = periods.Distinct().ToList();
                    _cache.Clear(string.Format(AssetMqttConfigKey, input.AssetId));
                    foreach (var item in periods)
                    {
                        _cache.SetHashData<string>(string.Format(MqttConfigCycleKey, item), input.AssetId.ToString(), string.Empty);
                        await _action.TriggerCycleJob(item);
                    }
                }
                #endregion

                result = new ResponseBase<string>() { Code = 20000, Message = MessageContext.GetString("Mqtt_Info_SaveSuccessful") };

            }
            catch (Exception ex)
            {
                //回滚事务
                _db.Ado.RollbackTran();

                result = new ResponseBase<string>() { Code = 50000, Message = MessageContext.ServerException };
            }

            return Ok(result);
        }

        /// <summary>
        /// / 删除分组信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [SwaggerOperation(Summary = "Swagger_Mqtt_DelMqttDataPointConfig", Description = "Swagger_Mqtt_DelMqttDataPointConfig_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> DelMqttDataPointConfig(long id)
        {
            ResponseBase<string> result;

            try
            {
                if (id <= 0)
                {
                    // throw new Exception(MessageContext.GetString("Mqtt_Err_Idcannotbeorempty"));

                    return Ok(new ResponseBase<string>() { Code = 50000, Message = MessageContext.GetString("Mqtt_Err_Idcannotbeorempty") });

                }

                _db.Ado.BeginTran();

                //获取一级目录
                var assetMqttDataPointConfig = await _db.Queryable<AssetMqttDataPointConfig>().FirstAsync(p => p.Id == id);

                if (assetMqttDataPointConfig == null)
                {
                    result = new ResponseBase<string>() { Code = 20000, Data = "undefined", Message = MessageContext.GetString("Mqtt_Err_Groupingdoesnotexist") };

                    return Ok(result);
                }

                //获取二级点位数据
                var assetMqttDataPointConfigs = await _db.Queryable<AssetMqttDataPointConfig>()
                    .Where(p => p.GroupName == assetMqttDataPointConfig.Name && p.AssetId == assetMqttDataPointConfig.AssetId).ToListAsync();

                assetMqttDataPointConfigs.Add(assetMqttDataPointConfig);

                if (assetMqttDataPointConfigs.Any())
                {
                    //批量删除
                    await _db.Deleteable(assetMqttDataPointConfigs).ExecuteCommandAsync();
                }

                // 判断该assertId是否存在该配置点位
                var assetMqttDataPointConfigList = await _db.Queryable<AssetMqttDataPointConfig>()
                    .Where(p => p.AssetId == assetMqttDataPointConfig.AssetId).ToListAsync();

                if (!assetMqttDataPointConfigList.Any())
                {
                    //修改资产信息
                    var assetInfo = await _db.Queryable<AssetInfo>().Where(p => p.Id == assetMqttDataPointConfig.AssetId).FirstAsync();
                    if (assetInfo != null)
                    {
                        var service = Provider.GetRequiredService<AssetExtendServer>();

                        await service.UpdateAssetEnableMqtt(assetInfo, true, UserName, _db);
                    }
                }

                _db.Ado.CommitTran();

                #region 清楚缓存 确认Job是否需要停止
                {
                    _cache.Clear(string.Format(AssetMqttConfigKey, assetMqttDataPointConfig.AssetId));
                    _cache.RemoveHashData(string.Format(MqttConfigCycleKey, assetMqttDataPointConfig.SamplingPeriod), new string[]
                    {
                        assetMqttDataPointConfig.AssetId.ToString()
                    });


                    if (!await _db.Queryable<AssetMqttDataPointConfig>()
                        .AnyAsync(c => c.SamplingPeriod == assetMqttDataPointConfig.SamplingPeriod 
                        && c.ConfigType == GroupConfigType.Group))
                    {
                        await _action.RemoveCycleJob(assetMqttDataPointConfig.SamplingPeriod);
                    }
                }
                #endregion

                result = new ResponseBase<string>() { Code = 20000, Message = MessageContext.GetString("Mqtt_info_Deletesuccessful") };

            }
            catch (Exception ex)
            {
                _db.Ado.RollbackTran();
                result = new ResponseBase<string>() { Code = 50000, Message = MessageContext.ServerException };
            }

            return Ok(result);
        }

        /// <summary>
        /// 拷贝分组点位信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [SwaggerOperation(Summary = "Swagger_Mqtt_CopyToPoint", Description = "Swagger_Mqtt_CopyToPoint_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> CopyToPoint([FromBody] CopyPointConfigParam input)
        {
            ResponseBase<string> result;

            try
            {
                var assertList = new List<long>() { input.CopyAssetId, input.RepAssetId };
                var assets = await _db.Queryable<AssetInfo>()
                    .Where(p => assertList.Contains(p.Id))
                    .Select(p => new { p.AssetModel, p.AssetLevel })
                    .ToListAsync();

                if (assets.Count != 2)
                {
                    // throw new Exception(MessageContext.GetString("Mqtt_Err_Tatitcaroainacbr"));

                    return Ok(new ResponseBase<string>() { Code = 50000, Message = MessageContext.GetString("Mqtt_Err_Tatitcaroainacbr") });
                }

                if (assets[0].AssetLevel != assets[1].AssetLevel || assets[0].AssetModel != assets[1].AssetModel)
                {
                    // throw new Exception(MessageContext.GetString("Mqtt_Err_Tatitcaroainacbr"));

                    return Ok(new ResponseBase<string>() { Code = 50000, Message = MessageContext.GetString("Mqtt_Err_Tatitcaroainacbr") });
                }

                // 开启事务
                _db.Ado.BeginTran();

                //删除替换对象中的数据
                await _db.Deleteable<AssetMqttDataPointConfig>().Where(p => p.AssetId == input.RepAssetId)
                    .ExecuteCommandAsync();

                //查询替换对象要插入的数据
                var assetMqttDataPointConfigs = await _db.Queryable<AssetMqttDataPointConfig>()
                    .Where(p => p.AssetId == input.CopyAssetId).ToListAsync();

                if (assetMqttDataPointConfigs.Any())
                {
                    assetMqttDataPointConfigs.ForEach(p =>
                    {
                        p.Id = 0;
                        p.AssetId = (int)input.RepAssetId;
                    });
                }

                //批量插入数据
                await _db.Insertable(assetMqttDataPointConfigs).ExecuteCommandAsync();

                //提交事务
                _db.Ado.CommitTran();

                result = new ResponseBase<string>() { Code = 20000, Message = MessageContext.GetString("Mqtt_info_Pastesuccessful") };

            }
            catch (Exception ex)
            {
                //回滚事务
                _db.Ado.RollbackTran();

                result = new ResponseBase<string>() { Code = 50000, Message = MessageContext.ServerException };
            }

            return Ok(result);
        }

        /// <summary>
        ///  获取同步资产列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [SwaggerOperation(Summary = "Swagger_Mqtt_GetSynchronizeAssetinfo", Description = "Swagger_Mqtt_GetSynchronizeAssetinfo_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> GetSynchronizeAssetinfo()
        {
            ResponseBase<List<SynAssetinfoResult>> result;

            try
            {
                var synAssetinfos = await _db.Queryable<AssetInfo, AssetRelation>((t1, t2) => new JoinQueryInfos(
                       JoinType.Inner, t1.Id == t2.ChildId
                     )).Where(t1 => t1.AssetLevel != AssetLevel.Area && t1.EnableMqtt == true) //去掉区域部分的其他数据
                    .OrderBy((t1, t2) => t1.AssetLevel)
                    .Select((t1, t2) => new SynAssetinfoResult()
                    {
                        AssetType = t1.AssetLevel == AssetLevel.Substation ? MessageContext.GetString("Mqtt_info_Distributionroom")
                                    : t1.AssetLevel == AssetLevel.Panel ? MessageContext.GetString("Mqtt_info_Switchgear")
                                    : t1.AssetLevel == AssetLevel.Circuit ? MessageContext.GetString("Mqtt_info_Loop") : MessageContext.GetString("Mqtt_info_Equipment"),

                        AssetName = t1.AssetName,
                        ParentId = t2.ParentId,
                        ChildId = t2.ChildId

                    }).ToListAsync();

                result = new ResponseBase<List<SynAssetinfoResult>>() { Code = 20000, Data = synAssetinfos, Message = MessageContext.GetString("Mqtt_Info_QueryWasSuccessful") };

            }
            catch (Exception ex)
            {
                result = new ResponseBase<List<SynAssetinfoResult>>() { Code = 50000, Data = null, Message = MessageContext.ServerException };
            }

            return Ok(result);
        }

        /// <summary>
        /// Mqtt高级配置的重置
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [SwaggerOperation(Summary = "Swagger_Mqtt_MqttReset", Description = "Swagger_Mqtt_MqttReset_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> MqttReset()
        {
            ResponseBase<string> result;

            try
            {
                //开启事务
                _db.Ado.BeginTran();

                //初始化资产信息
                //var assetIds = _cache.Get<List<int>>("uploadExcelByAssetIds");

                //if (assetIds != null && assetIds.Any())
                //{
                //    await _db.Deleteable<AssetInfo>().Where(p => !assetIds.Contains(p.Id)).ExecuteCommandAsync();
                //}

                //删除分组点位配置的所有数据
                await _db.Deleteable<AssetMqttDataPointConfig>().ExecuteCommandAsync();

                //获取文件夹的信息
                var assetDataPointDirectorys = await _db.Queryable<AssetDataPointDirectory>()
                    .OrderBy(p => p.Sort)
                    .Select(p => new
                    {
                        p.Name,
                        p.ParentName,
                        p.AssetModel,
                        p.AssetLevel
                    }).ToListAsync();

                //获取文件夹中AssetModel集合
                var assetModelList = assetDataPointDirectorys.Select(p => p.AssetModel).Distinct().ToList();

                //获取导入的EnableMqtt的数据
                var assetInfos = await _db.Queryable<AssetInfo>().WhereIF(assetModelList.Any(), p => assetModelList.Contains(p.AssetModel)
                    ).Where(p => p.EnableMqtt == true)
                    .Distinct()
                    .Select(p => new { p.Id, p.AssetModel })
                    .ToListAsync();

                //获取点位信息
                var assetDataPointInfos = await _db.Queryable<AssetDataPointInfo>().WhereIF(assetInfos.Any(), p => assetInfos.Select(o => o.AssetModel).Contains(p.AssetModel) && p.IsDefaultMqtt == 1).ToListAsync();


                var assetMqttDataPointConfigs = new List<AssetMqttDataPointConfig>();

                if (assetInfos.Any())
                {
                    foreach (var item in assetInfos)
                    {
                        //获取分组集合
                        var groupList = assetDataPointInfos.Where(p => p.AssetModel == item.AssetModel).Select(p => new
                        {
                            p.MqttGroupName,
                            p.MqttSamplingPeriod,

                        }).Distinct().ToList();

                        //添加分组
                        foreach (var _item in groupList)
                        {
                            assetMqttDataPointConfigs.Add(new AssetMqttDataPointConfig()
                            {
                                AssetId = item.Id,
                                ConfigType = GroupConfigType.Group,
                                Name = _item.MqttGroupName ?? "未命名",
                                SamplingPeriod = _item.MqttSamplingPeriod ?? 5,
                                CreatedBy = this.UserName,
                                CreatedTime = DateTime.Now,
                                UpdatedBy = this.UserName,
                                UpdatedTime = DateTime.Now
                            });
                        }

                        //获取子集点位数据
                        var childDatas = assetDataPointInfos.Where(p => p.AssetModel == item.AssetModel).ToList();

                        if (childDatas.Any())
                        {
                            foreach (var _item in childDatas)
                            {
                                var entity = new AssetMqttDataPointConfig()
                                {
                                    AssetId = item.Id,
                                    ConfigType = GroupConfigType.Measurement,
                                    GroupName = _item.MqttGroupName ?? "未命名",
                                    Code = _item.Code,
                                    AssetModel = _item.AssetModel,
                                    Name = _item.Name,
                                    SamplingPeriod = 0,
                                    CreatedBy = this.UserName,
                                    CreatedTime = DateTime.Now,
                                    UpdatedBy = this.UserName,
                                    UpdatedTime = DateTime.Now
                                };

                                assetMqttDataPointConfigs.Add(entity);
                            }
                        }

                    }
                }

                //批量添加
                await _db.Insertable(assetMqttDataPointConfigs).ExecuteCommandAsync();

                _db.Ado.CommitTran();

                //初始化数据
                await _action.RestartMqtt();

                result = new ResponseBase<string>() { Code = 20000, Message = MessageContext.GetString("Mqtt_info_Resetsuccessful") };
            }
            catch (Exception ex)
            {
                //回滚事务
                _db.Ado.RollbackTran();

                result = new ResponseBase<string>() { Code = 50000, Message = MessageContext.ServerException };
            }

            return Ok(result);
        }

        /// <summary>
        ///  导出Mqtt配置
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [SwaggerOperation(Summary = "Swagger_Mqtt_DownMqttConfig", Description = "Swagger_Mqtt_DownMqttConfig_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> DownMqttConfig()
        {
            //构建json数据对象
            var mqttConfigResult = new MqttConfigResult();

            //文件信息
            mqttConfigResult.FileInfo = new() { Format = "DataSourceRequest", Version = "2.3.0" };


            //获取第三方设备的json文件的集合
            var assetByJsonList = await _db.Queryable<AssetInfo>()
                                      .InnerJoin<ThirdModelConfig>((t1, t2) => t1.ThirdPartCode == t2.Code)
                                      .Select((t1, t2) => new
                                      {
                                          AssetId = t1.Id,
                                          ThirdDeviceName = t2.ThirdDeviceName

                                      }).ToListAsync();

            //获取资产信息列表
            var assetinfos = await _db.Queryable<AssetInfo, AssetRelation>((t1, t2) =>
                new JoinQueryInfos(JoinType.Inner, t1.Id == t2.ChildId))
                .OrderBy((t1, t2) => t1.AssetLevel)
                .Select((t1, t2) => new
                {
                    t1.Id,
                    t1.AssetLevel,
                    t1.AssetType,
                    t1.AssetModel,
                    t1.AssetName,
                    t1.Description,
                    t2.ParentId,
                    t2.ChildId
                }).ToListAsync();

            if (assetinfos.Any())
            {
                var refObj = Provider.GetRequiredService<IUDCApiRef>();
                var licenses = await refObj.GetUdcLicensesAsync();

                var idStr = ""; // TODO 获取设备的序列号
                if (licenses != null)
                {
                    idStr = licenses.SerialNumber;
                }

                //获取第一级的资产
                var assetInfo = assetinfos.FirstOrDefault(p => p.AssetLevel == AssetLevel.Area);

                //资产信息
                mqttConfigResult.AssetInfo = new AssetInfoDto()
                {
                    AssetId = "",
                    Id = idStr,
                    Name = "Panel Manager",
                    Version = "V01.08.00.00_00.00.00.01",
                    Description = "",
                    TimeZone = "Asia/Shanghai",
                    Location = new LocationDto()
                };

                foreach (var item in assetinfos)
                {
                    var model = item.AssetModel ?? string.Empty;

                    if (item.AssetLevel == AssetLevel.Device
                        && ("Other".Equals(model)
                        || "GeneralDevice".Equals(model)))
                    {
                        model = "Modbus";
                    }

                    var entity = new ConfigurationInfoDto()
                    {
                        AssetType = item.AssetLevel.ToString(),
                        DeviceType = item.AssetType ?? string.Empty,
                        TypeId = model,
                        DeviceId = assetByJsonList.FirstOrDefault(p => p.AssetId == item.Id)?.ThirdDeviceName ?? "",
                        Id = item.Id.ToString(),
                        Name = item.AssetName,
                        Description = item.Description,
                        ParentId = item.ParentId.ToString(),
                        ChildIds = assetinfos
                                    .Where(p => p.ParentId == item.Id)
                                    .Select(p => p.ChildId.ToString())
                                    .ToList()
                    };

                    mqttConfigResult.ConfigurationInfo.Add(entity);
                }
            }

            string jsonText = JsonConvert.SerializeObject(mqttConfigResult);
            var jsonDocument = JsonDocument.Parse(jsonText);

            var jsonFormat = JsonSerializer.Serialize(jsonDocument, new JsonSerializerOptions()
            {
                WriteIndented = true,
                Encoder = JavaScriptEncoder.Create(UnicodeRanges.All)
            });
            Stream stream = FileHelper.JsonToStream(jsonFormat);

            return await Task.FromResult(new FileStreamResult(stream, "application/octet-stream")
            {
                FileDownloadName = $"{DateTime.Now:yyyyMMdd}_{DateTime.Now:HHmmss}_PanelManager_Request.json"
            });

        }

        /// <summary>
        /// 特殊场景验证
        /// </summary>
        /// <param name="input"></param>
        private void CheckValidation(SysConfigParam input)
        {
            if (string.IsNullOrEmpty(input.Broker))
            {
                throw new AggregateException(MessageContext.GetString("Mqtt_Err_BrokerserviceIPcannotbeempty"));
            }

            if (string.IsNullOrEmpty(input.Port) || !int.TryParse(input.Port, out _))
            {
                throw new AggregateException(MessageContext.GetString("Mqtt_Err_Theportnumbercannotbeempty"));
            }

            if (string.IsNullOrEmpty(input.ClientId))
            {
                throw new AggregateException(MessageContext.GetString("Mqtt_Err_ClientIdcannotbeempty"));
            }

            if (string.IsNullOrEmpty(input.Topic))
            {
                throw new AggregateException(MessageContext.GetString("Mqtt_Err_Themecannotbeempty"));
            }

            switch (input.UseTLS)
            {
                case "0":
                case "2":
                    break;

                case "1":
                    if (string.IsNullOrEmpty(input.TLSPemFile))
                    {
                        throw new AggregateException(MessageContext.GetString("Mqtt_info_PleaseuploadTLSPomFilefile"));
                    }
                    if (string.IsNullOrEmpty(input.ClientPrivateFile))
                    {
                        throw new AggregateException(MessageContext.GetString("Mqtt_info_PleaseuploadClientPrivateFilefile"));
                    }
                    if (string.IsNullOrEmpty(input.ClientPemFile))
                    {
                        throw new AggregateException(MessageContext.GetString("Mqtt_info_PleaseuploadClientPemFilefile"));
                    }

                    break;
            }
        }

        /// <summary>
        /// 转换mqtt的异常信息
        /// </summary>
        /// <returns></returns>
        private string GetExcetionInfo(string? exMsg)
        {
            if (string.IsNullOrEmpty(exMsg))
            {
                return string.Empty;
            }

            if (exMsg!.Contains("Mqtt_Err_Tconfcanbempty"))
            {
                exMsg = MessageContext.GetString("Mqtt_Err_Tconfcanbempty");
            }
            else if (exMsg!.Contains("Error while connecting with host"))
            {
                exMsg = MessageContext.GetString("Mqtt_Err_Errorwhileconnectingwithhost");
            }
            else if (exMsg!.Contains("Mqtt_Err_Ftosmqttwemsg:Error"))
            {
                exMsg = MessageContext.GetString("Mqtt_Err_Errorwhileconnectingwithhost");
            }
            else if (exMsg!.Contains("Specified argument was out of the range of valid values"))
            {
                exMsg = MessageContext.GetString("Mqtt_Err_Specifiedargumentwasoutoftherangeofvalidvalues");
            }
            else if (exMsg!.Contains("Exception while reading from stream"))
            {
                exMsg = MessageContext.GetString("Mqtt_Err_Exceptionwhilereadingfromstream");
            }
            else if (exMsg!.Contains("The operation has timed out"))
            {
                exMsg = MessageContext.GetString("Mqtt_Err_Theoperationhastimedout");
            }
            else if (exMsg!.Contains("Connecting with MQTT server failed"))
            {
                exMsg = MessageContext.GetString("Mqtt_Err_Connectingwithmqttserverfailed");
            }
            else if (exMsg!.Contains("EOF or 0 bytes"))
            {
                exMsg = MessageContext.GetString("Mqtt_Err_Eofor0bytes");
            }
            else if (exMsg!.Contains("RemoteCertificateValidationCallback"))
            {
                exMsg = MessageContext.GetString("Mqtt_Err_RemoteCertificateValidationCallback");
            }
            else if (exMsg!.Contains("no such file"))
            {
                exMsg = MessageContext.GetString("Mqtt_Err_Nosuchfile");
            }
            else if (exMsg!.Contains("Authentication failed"))
            {
                exMsg = MessageContext.GetString("Mqtt_Err_Authenticationfailed");
            }
            else if (exMsg!.Contains("The decryption operation failed, see inner exception"))
            {
                exMsg = MessageContext.GetString("Mqtt_Err_Thedecryptionoperationfailedseeinnerexception");
            }
            else if (exMsg!.Contains("Input string was not in a correct format."))
            {
                exMsg = MessageContext.GetString("Mqtt_Err_Inputstringwasnotinacorrectformat");
            }
            else if (exMsg!.Contains("系统找不到指定的文件"))
            {
                exMsg = MessageContext.GetString("Mqtt_Err_Tscanftsfile");
            }
            else if (exMsg!.Contains("Exception while connectingDbType"))
            {
                exMsg = MessageContext.GetString("Mqtt_Err_ExceptionwhileconnectingDbType");
            }
            else if (exMsg!.Contains("The key contents do not contain a PEM, the content is malformed, or the key does not match the certificate"))
            {
                exMsg = MessageContext.GetString("Mqtt_Err_Tkeycdonotcapemtcisotkdnmtcer");
            }
            else if (exMsg!.Contains("The certificate contents do not contain a PEM with a CERTIFICATE label, or the content is malformed"))
            {
                exMsg = MessageContext.GetString("Mqtt_Err_Cermorincerconformat");
            }
            else if (exMsg!.Contains("Expected at least 770 bytes but there are only 3 bytes"))
            {
                exMsg = MessageContext.GetString("Mqtt_Err_Pchkiftvtapnmath");
            }
            else if (exMsg!.Contains("MQTT connect canceled"))
            {
                exMsg = MessageContext.GetString("Mqtt_Err_MQTTconnectcanceled");
            }

            return exMsg!;
        }

        /// <summary>
        /// 资源释放
        /// </summary>
        public void Dispose()
        {
            _db.Close();
        }
    }
}
