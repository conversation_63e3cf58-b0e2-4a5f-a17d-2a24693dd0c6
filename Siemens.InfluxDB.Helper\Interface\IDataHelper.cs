﻿using Siemens.InfluxDB.Helper.FluxModel;
using System.Collections.ObjectModel;

namespace Siemens.InfluxDB.Helper.Interface
{
    internal abstract class DataHelperBase
    {
        public virtual Type DataType { get; protected set; }

        public virtual string Measurement { get; protected set; } = string.Empty;
        public virtual string TimeColumn { get; protected set; } = "Time";

        public virtual ReadOnlyDictionary<string, string> Tags { get; protected set; }
        public virtual ReadOnlyDictionary<string, string> Fields { get; protected set; }

        public virtual RangeModel Range { get; protected set; } = new RangeModel();
        public virtual Limit? Limit { get; set; }
        public virtual FirstModel? First { get; set; }
        public virtual LastModel? Last { get; set; }
        public virtual Filter Filter { get; protected set; }
        public virtual FromModel From { get; protected set; }

        //public virtual Group? Group { get; protected set; }
        public virtual AggregateWindow? AggregateWindow { get; set; }
        public virtual Sort Sort { get; protected set; } = new Sort();
        public bool IsGroupByTime { get { return AggregateWindow != null; } }
        //public virtual List<Sort> Sorts { get; protected set; } = new List<Sort>();

        public abstract int GetColumnFilter(string columnName, bool isLeft, out string flux);
    }
}
