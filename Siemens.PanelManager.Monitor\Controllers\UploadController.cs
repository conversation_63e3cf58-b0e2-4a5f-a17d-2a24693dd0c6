﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.FileProviders;
using Siemens.PanelManager.HubModel;
using Siemens.PanelManager.Monitor.Function;
using Siemens.PanelManager.Monitor.StaticData;
using Siemens.PanelManager.Monitor.Workers;
using System.IO;
using System.IO.Pipelines;
using System.Text;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.Monitor.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class UploadController : ControllerBase
    {
        private ILogger<UploadController> _logger;
        private UpgradeWorkerStatus _status;
        private IServiceProvider _provider;
        public UploadController(ILogger<UploadController> logger, UpgradeWorkerStatus status, IServiceProvider provider) 
        {
            _logger = logger;
            _status = status;
            _provider = provider;
        }

        [HttpPost("{jobId}/upgradePackage")]
        public async Task<bool> UpgradePackage(string jobId, [FromForm] IFormCollection form)
        {
            if(!_status.JobId.Equals(jobId)) return false;

            if (form.Files.Any(f => !Regex.IsMatch(f.FileName, "\\.zip$")))
            {
                return false;
            }

            var file = form.Files.FirstOrDefault();
            if (file == null) return false;
            var basePath = AppContext.BaseDirectory;
            var fileName = Guid.NewGuid().ToString();
            var tempPath = Path.Combine(basePath, UpgradePackageMananger.TempFolderName);
            if (!Directory.Exists(tempPath))
            {
                Directory.CreateDirectory(tempPath);
            }

            try
            {
                using (var fs = new FileStream(Path.Combine(tempPath, $"{fileName}.zip"), FileMode.CreateNew))
                {
                    await file.CopyToAsync(fs);
                }

                _status.UploadFile(Path.Combine(tempPath, $"{fileName}.zip"), fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存文件失败");
                return false;
            }
            return true;
        }

        [HttpPost("upgrade")]
        public async Task<UpgradeResult> Upgrade([FromForm] IFormCollection form)
        {
            var auth = false;
            if (Request.Headers.TryGetValue("Authorization", out var stringValue))
            {
                var token = stringValue.FirstOrDefault();
                if (!string.IsNullOrEmpty(token))
                {
                    var signatureBase64 = token[6..];
                    var signature = Encoding.UTF8.GetString(Convert.FromBase64String(signatureBase64));
                    var keys = signature.Split(':');

                    if (keys.Length == 2)
                    {
                        var func = _provider.GetRequiredService<LocalSecurityFunc>();
                        if (func != null)
                        {
                            var verifyResult = func.CheckSign(keys[0], keys[1]);
                            if (verifyResult)
                            {
                                auth = true;
                            }
                        }
                    }
                }
            }
            //if (!auth)
            //{
            //    return new UpgradeResult()
            //    {
            //        ValidatedPass = false
            //    };
            //}

            if (_status.Status != 0)
            {
                return new UpgradeResult
                {
                    ValidatedPass = false,
                    Message = "正在升级"
                };
            }

            var zipFile = form.Files.FirstOrDefault(f => Regex.IsMatch(f.FileName, "\\.zip$"));

            if (zipFile != null)
            {
                try
                {
                    var basePath = AppContext.BaseDirectory;
                    var fileName = Guid.NewGuid().ToString();
                    var tempPath = Path.Combine(basePath, UpgradePackageMananger.TempFolderName);
                    if (!Directory.Exists(tempPath))
                    {
                        Directory.CreateDirectory(tempPath);
                    }
                    var tempFile = Path.Combine(tempPath, $"{fileName}.zip");
                    using (var fs = new FileStream(Path.Combine(tempPath, $"{fileName}.zip"), FileMode.CreateNew))
                    {
                        await zipFile.CopyToAsync(fs);
                    }

                    var fileInfoes = new List<FileInfo>();
                    var pathParams = new Dictionary<string, string>();
                    var validatedPass = await UpgradePackageMananger.CheckZip(tempFile, fileName, _logger, fileInfoes, pathParams);
                    if (validatedPass) 
                    {
                        var paramDic = new Dictionary<string, string>();
                        foreach (var param in pathParams) 
                        {
                            paramDic.TryAdd(param.Key, param.Value);
                        }
                        foreach (var fileInfo in fileInfoes)
                        {
                            paramDic.TryAdd(fileInfo.Name, fileInfo.FullName);
                        }

                        var jobId = _status.StartUpgradeByUpload(paramDic);
                        if (!string.IsNullOrEmpty(jobId))
                        {
                            var worker = _provider.GetService<UpgradeWorker>();
                            if (worker != null)
                            {
                                await worker.StartAsync(CancellationToken.None);
                                return new UpgradeResult
                                {
                                    ValidatedPass = true,
                                };
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "保存文件失败");
                    return new UpgradeResult
                    {
                        ValidatedPass = false,
                        Message = ex.Message
                    };
                }
            }

            return new UpgradeResult
            {
                ValidatedPass = false,
                Message = "Failed"
            };
        }
        
    }
}
