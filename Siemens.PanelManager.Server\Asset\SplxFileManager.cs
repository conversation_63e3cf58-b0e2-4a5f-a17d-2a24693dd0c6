﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.Common;
using Siemens.PanelManager.Model.Database.Asset;
using SqlSugar;
using System.IO.Packaging;
using System.Text.RegularExpressions;
using System.Text;
using System.Xml;
using Siemens.PanelManager.Common.IO;
using Siemens.PanelManager.Common.Model;

namespace Siemens.PanelManager.Server.Asset
{
    public class SplxFileManager
    {
        private readonly IServiceProvider _provider;
        private readonly SplxServer _splxServer;
        private readonly ILogger _logger;

        public SplxFileManager(IServiceProvider provider, ILogger<SplxFileManager> logger, SplxServer spxServer)
        {
            _provider = provider;
            _logger = logger;
            _splxServer = spxServer;
        }

        /// <summary>
        /// 扫描主方法
        /// </summary>
        /// <param name="filePath"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public async Task<SimpleResult> ChangeSplxFile(string filePath, string userName)
        {
            var udcSplxPath = Path.Combine(filePath, "project.splx");
            string rePackageSplxPath = string.Empty;
            string unPackageTempPath = string.Empty;
            var simpleResult = new SimpleResult()
            {
                Success = true,
            };
            try
            {
                List<AssetInfo> modbusDevices;

                #region Unpackage

                unPackageTempPath = Path.Combine(Path.GetTempPath(), string.Concat("powerconfig_", Path.GetFileNameWithoutExtension(udcSplxPath)));
                DirectoryInfo unPackage = new DirectoryInfo(unPackageTempPath);

                if (unPackage.Exists)
                {
                    unPackage.Delete(true);
                }

                using (Package package = Package.Open(udcSplxPath, FileMode.Open, FileAccess.Read, FileShare.Read))
                {
                    foreach (var each in package.GetParts())
                    {
                        string relativePath = Uri.UnescapeDataString(each.Uri.ToString().TrimStart('/'));
                        string absolutePath = Path.Combine(unPackageTempPath, relativePath);
                        Directory.CreateDirectory(Path.GetDirectoryName(absolutePath)!);

                        using (FileStream fs = new FileStream(absolutePath, FileMode.Create))
                        {
                            await each.GetStream().CopyToAsync(fs);
                        }
                    }
                }

                #endregion

                var relationShipTarget = string.Empty;

                string indexFilePath = Path.Combine(unPackageTempPath, "Index.xml");
                XmlDocument doc = new XmlDocument();
                doc.Load(indexFilePath);
                var nodes = doc.DocumentElement?.SelectNodes("H")!;
                var existsObjectId = new List<string>();
                var modbusDeviceIds = new List<string>();
                foreach (XmlNode node in nodes)
                {
                    string id = node.Attributes?["id"]?.Value ?? string.Empty;
                    if (string.IsNullOrWhiteSpace(id))
                    {
                        continue;
                    }
                    existsObjectId.Add(id);
                    var typeId = node.Attributes?["typeId"]?.Value;
                    if (string.IsNullOrEmpty(typeId) || (!"14".Equals(typeId) && !"41".Equals(typeId))) continue;
                    modbusDeviceIds.Add(id);
                }

                using (var sqlClient = _provider.GetRequiredService<ISqlSugarClient>())
                {
                    //modbusDevices = await sqlClient.Queryable<AssetInfo>()
                    //    .Where(a => (a.AssetType == "TempMeasurement" && a.AssetModel == "SiemensTempMeasurement") || (a.AssetModel == "Other") || (a.AssetType == "GeneralDevice") || ("Modbus".Equals(a.AssetModel) && "Gateway".Equals(a.AssetType)))
                    //    .ToListAsync();

                    // 西门子温控设备
                    modbusDevices = await sqlClient.Queryable<AssetInfo>()
                        .Where(a => a.AssetType == "TempMeasurement" && a.AssetModel == "SiemensTempMeasurement")
                        .ToListAsync();

                    if (modbusDevices != null && modbusDevices.Count > 0)
                    {
                        #region 去除缺失IP设置的资产
                        var needRemoveList = new List<AssetInfo>();
                        foreach (var device in modbusDevices)
                        {
                            if (string.IsNullOrEmpty(device.IPAddress))
                            {
                                needRemoveList.Add(device);
                                continue;
                            }
                            var ipMatch = Regex.Match(device.IPAddress, "^(?<ip>[\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3})(?<port>:[\\d]{2,5})?(?<modbus>[\\|/]{1}[\\d]{1,5})?$");
                            if (!ipMatch.Success)
                            {
                                needRemoveList.Add(device);
                                continue;
                            }
                            simpleResult.ModbusDevices.Add(device);
                        }

                        foreach (var device in needRemoveList)
                        {
                            modbusDevices.Remove(device);
                        }
                        #endregion

                        var changeAssetInfo = new List<AssetInfo>();
                        foreach (var deviceId in modbusDeviceIds)
                        {
                            var communicationParameterFile = Path.Combine(unPackageTempPath, "ProjectItems", deviceId, "CommunicationParameter.xml");
                            if (File.Exists(communicationParameterFile))
                            {
                                var asset = modbusDevices.FirstOrDefault(m => m.ObjectId == deviceId);
                                XmlDocument document = new XmlDocument();
                                document.Load(communicationParameterFile);

                                var modbusNode = document.DocumentElement?.SelectSingleNode("ModbusAddress");
                                var modbus = modbusNode?.InnerText ?? "";
                                var node = document.DocumentElement?.SelectSingleNode("Tcp");
                                if (node != null && node is XmlElement element)
                                {
                                    var ipNode = element.SelectSingleNode("HostAddress");
                                    var portNode = element.SelectSingleNode("Port");
                                    if (ipNode != null && portNode != null)
                                    {
                                        var ip = ipNode.InnerText;
                                        var port = portNode.InnerText;
                                        if (asset != null)
                                        {
                                            var assetIp = "0.0.0.0";
                                            var assetPort = "0";
                                            var assetModbus = "1";
                                            if (asset != null && !string.IsNullOrEmpty(asset.IPAddress))
                                            {
                                                var ipMatch = Regex.Match(asset.IPAddress, "^(?<ip>[\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3})(?<port>:[\\d]{2,5})?(?<modbus>[\\|/]{1}[\\d]{1,5})?$");
                                                if (ipMatch.Success)
                                                {
                                                    assetIp = ipMatch.Groups["ip"].Value;
                                                    if (!string.IsNullOrEmpty(ipMatch.Groups["port"].Value))
                                                    {
                                                        assetPort = ipMatch.Groups["port"].Value;
                                                    }

                                                    bool needSave = false;
                                                    if (!ip.Equals(assetIp))
                                                    {
                                                        ipNode.InnerText = assetIp;
                                                        needSave = true;
                                                    }

                                                    if (!string.IsNullOrEmpty(ipMatch.Groups["modbus"].Value))
                                                    {
                                                        assetModbus = ipMatch.Groups["modbus"].Value;
                                                        if (modbusNode != null && !assetModbus.Equals(modbusNode.InnerText))
                                                        {
                                                            modbusNode.InnerText = assetModbus;
                                                            needSave = true;
                                                        }
                                                    }

                                                    if (!string.IsNullOrEmpty(assetPort) && !port.Equals(assetPort))
                                                    {
                                                        portNode.InnerText = assetPort;
                                                        needSave = true;
                                                    }

                                                    if (needSave)
                                                    {
                                                        document.Save(communicationParameterFile);
                                                    }
                                                }
                                            }
                                        }
                                        else
                                        {
                                            var fullIp = new StringBuilder();
                                            string ipModbus = ip;
                                            if (modbus != "1")
                                            {
                                                ipModbus = ip + "/" + modbus;
                                            }
                                            asset = modbusDevices.FirstOrDefault(m => ipModbus.Equals(m.IPAddress) && port.Equals(m.Port));
                                            if (asset != null)
                                            {
                                                asset.ObjectId = deviceId;
                                                asset.UpdatedBy = userName;
                                                asset.UpdatedTime = DateTime.Now;
                                                changeAssetInfo.Add(asset);
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        var uninputDevices = modbusDevices.Where(m => !changeAssetInfo.Any(c => c.Id == m.Id)).ToArray();
                        foreach (var info in uninputDevices)
                        {
                            if (string.IsNullOrEmpty(info.ObjectId))
                            {
                                if (!string.IsNullOrEmpty(info.IPAddress))
                                {
                                    info.ObjectId = Guid.NewGuid().ToString();
                                    info.UpdatedBy = userName;
                                    info.UpdatedTime = DateTime.Now;
                                    changeAssetInfo.Add(info);
                                }
                            }

                            if (!string.IsNullOrEmpty(info.ObjectId))
                            {
                                if (!existsObjectId.Contains(info.ObjectId))
                                {
                                    string typeId = "41";
                                    if ("Modbus".Equals(info.AssetModel) && "Gateway".Equals(info.AssetType))
                                    {
                                        typeId = "14";
                                    }
                                    var hNode = doc.CreateElement("H");
                                    XmlAttribute idAttr = doc.CreateAttribute("id");
                                    idAttr.InnerText = info.ObjectId;
                                    XmlAttribute nameAttr = doc.CreateAttribute("name");
                                    nameAttr.InnerText = info.AssetName;
                                    XmlAttribute typeIdAttr = doc.CreateAttribute("typeId");
                                    typeIdAttr.InnerText = typeId;
                                    hNode.Attributes.Append(idAttr);
                                    hNode.Attributes.Append(nameAttr);
                                    hNode.Attributes.Append(typeIdAttr);
                                    doc.DocumentElement?.AppendChild(hNode);
                                }
                            }
                        }

                        if (changeAssetInfo.Count > 0)
                        {
                            await sqlClient.Updateable(changeAssetInfo).UpdateColumns(a => new { a.ObjectId, a.UpdatedBy, a.UpdatedTime }).ExecuteCommandAsync();
                        }
                    }

                    nodes = doc.DocumentElement?.SelectNodes("H")!;

                    var projectRelsPath = string.Empty;
                    var relIds = new Dictionary<string, string>();

                    foreach (XmlNode xmlNode in nodes)
                    {
                        var typeId = xmlNode.Attributes?["typeId"]?.Value;
                        string id = xmlNode.Attributes?["id"]?.Value ?? string.Empty;
                        if (string.IsNullOrWhiteSpace(id) || string.IsNullOrEmpty(typeId))
                        {
                            continue;
                        }

                        if ("28".Equals(typeId))
                        {
                            projectRelsPath = Path.Combine(unPackageTempPath, "ProjectItems", id, "_rels", "Properties.xml.rels");
                            relationShipTarget = $"/ProjectItems/{id}/Properties.xml";
                        }
                        else if (UniversalDeviceInfo.Instance._deviceTypeMappings.ContainsKey(typeId))
                        {
                            await _splxServer.CreateCloudData(null, unPackageTempPath, typeId, id);
                        }
                        else if ("41".Equals(typeId) || "14".Equals(typeId))
                        {
                            var modbusDevice = modbusDevices?.FirstOrDefault(m => m.ObjectId == id);
                            if (modbusDevice == null) continue;
                            var projectPath = Path.Combine(unPackageTempPath, "ProjectItems", id);

                            if (!Directory.Exists(projectPath))
                            {
                                Directory.CreateDirectory(projectPath);
                            }

                            if ("SiemensTempMeasurement".Equals(modbusDevice.AssetModel) || ("Modbus".Equals(modbusDevice.AssetModel) && "Gateway".Equals(modbusDevice.AssetType)))
                            {
                                _splxServer.CreatePropertiesData(modbusDevice, projectPath, typeId);
                            }
                            else
                            {
                                await _splxServer.ChangeThirdDeviceBySplxFile(modbusDevice.Id, userName, projectPath);
                            }

                            _splxServer.CreateCommunicationParameter(modbusDevice, projectPath);

                            relIds.Add(id, _splxServer.CreateRels(projectPath, relationShipTarget));

                            await _splxServer.CreateCloudData(modbusDevice, unPackageTempPath, typeId, id);
                        }
                    }

                    _splxServer.AddChildRels(projectRelsPath, relIds);

                    doc.Save(indexFilePath);
                }

                #region 生成本地splx文件

                rePackageSplxPath = Path.Combine(filePath, string.Concat(Path.GetFileNameWithoutExtension(udcSplxPath), "_", DateTime.Now.ToString("yyMdHmm"), Path.GetExtension(udcSplxPath)));

                using (Package package = Package.Open(rePackageSplxPath, FileMode.Create))
                {
                    var files = new DirectoryInfo(unPackageTempPath).GetFiles("*", SearchOption.AllDirectories);
                    foreach (var file in files)
                    {
                        Uri partUri = PackUriHelper.CreatePartUri(new Uri(file.FullName.Replace(unPackageTempPath, ""), UriKind.Relative));
                        PackagePart packagePart = package
                            .CreatePart(partUri, Path.GetExtension(file.FullName)?.ToLower() == ".xml" ? "application/xml" : "application/vnd.openxmlformats-package.relationships+xml", CompressionOption.NotCompressed);

                        byte[] fileContents = File.ReadAllBytes(file.FullName);

                        using (Stream stream = packagePart.GetStream())
                        {
                            await stream.WriteAsync(fileContents, 0, fileContents.Length);
                        }
                    }
                }

                #endregion
            }
            catch (Exception ex)
            {

                _logger.LogError("ChangeSplxFile中生成splx文件过程中的异常信息:" + ex.Message);

                return new SimpleResult()
                {
                    Success = false,
                    Message = "unpackage or repackage error"
                };
            }

            bool result = false;

            try
            {
                Stream fileStream = FileHelper.FileToStream(rePackageSplxPath);
                var udcApiRef = _provider.GetRequiredService<IUDCApiRef>();
                result = await udcApiRef.ImportProject(fileStream);

                if (Directory.Exists(unPackageTempPath))
                {
                    Directory.Delete(unPackageTempPath, true);
                }

                if (File.Exists(rePackageSplxPath))
                {
                    File.Delete(rePackageSplxPath);
                }

                fileStream.Close();
            }
            catch (Exception ex)
            {
                _logger.LogError("ChangeSplxFile中导入udc中splx文件异常信息:" + ex.Message);
            }

            if (!result)
            {
                return new SimpleResult
                {
                    Success = false,
                    Message = "Upload splx file error"
                };
            }

            return simpleResult;
        }

    }
}
