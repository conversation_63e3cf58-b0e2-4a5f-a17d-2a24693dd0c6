﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;

namespace Siemens.PanelManager.SyncService.UdcModels
{
    public partial class UdcContext : DbContext
    {
        public UdcContext()
        {
        }

        public UdcContext(DbContextOptions<UdcContext> options)
            : base(options)
        {
        }

        public virtual DbSet<ArchiveDataFormat1> ArchiveDataFormat1s { get; set; } = null!;
        public virtual DbSet<ArchiveDataFormat2> ArchiveDataFormat2s { get; set; } = null!;
        public virtual DbSet<ArchiveDataFormat3> ArchiveDataFormat3s { get; set; } = null!;
        public virtual DbSet<ArchiveDataFormat4> ArchiveDataFormat4s { get; set; } = null!;
        public virtual DbSet<ArchiveDataFormat5> ArchiveDataFormat5s { get; set; } = null!;
        public virtual DbSet<ArchiveDataFormat6> ArchiveDataFormat6s { get; set; } = null!;
        public virtual DbSet<ArchiveDataFormat7> ArchiveDataFormat7s { get; set; } = null!;
        public virtual DbSet<ArchiveDataFormatBasicEnergy> ArchiveDataFormatBasicEnergies { get; set; } = null!;
        public virtual DbSet<ArchiveDataFormatCustom> ArchiveDataFormatCustoms { get; set; } = null!;
        public virtual DbSet<ArchiveDataFormatLookUp> ArchiveDataFormatLookUps { get; set; } = null!;
        public virtual DbSet<Messages> Messages { get; set; } = null!;
        public virtual DbSet<Parameter> Parameters { get; set; } = null!;
        public virtual DbSet<Slave> Slaves { get; set; } = null!;
        public virtual DbSet<SlaveMessages> SlaveMessages { get; set; } = null!;

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<ArchiveDataFormat1>(entity =>
            {
                entity.HasKey(e => new { e.ObjectId, e.TimestampInS, e.Archive });

                entity.ToTable("ArchiveDataFormat1");

                entity.Property(e => e.Counter1).HasColumnName("counter_1");

                entity.Property(e => e.Counter10).HasColumnName("counter_10");

                entity.Property(e => e.Counter2).HasColumnName("counter_2");

                entity.Property(e => e.Counter3).HasColumnName("counter_3");

                entity.Property(e => e.Counter4).HasColumnName("counter_4");

                entity.Property(e => e.Counter5).HasColumnName("counter_5");

                entity.Property(e => e.Counter6).HasColumnName("counter_6");

                entity.Property(e => e.Counter7).HasColumnName("counter_7");

                entity.Property(e => e.Counter8).HasColumnName("counter_8");

                entity.Property(e => e.Counter9).HasColumnName("counter_9");

                entity.Property(e => e.CounterUniversal1).HasColumnName("counter_universal1");

                entity.Property(e => e.CounterUniversal2).HasColumnName("counter_universal2");

                entity.Property(e => e.EnergyVarhExportOffpeaktariffSum)
                    .HasColumnName("energy_varh_export_offpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyVarhExportOnpeaktariffSum)
                    .HasColumnName("energy_varh_export_onpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyVarhImportOffpeaktariffSum)
                    .HasColumnName("energy_varh_import_offpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyVarhImportOnpeaktariffSum)
                    .HasColumnName("energy_varh_import_onpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyWhExportOffpeaktariffSum)
                    .HasColumnName("energy_wh_export_offpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyWhExportOnpeaktariffSum).HasColumnName("energy_wh_export_onpeaktariff_sum");

                entity.Property(e => e.EnergyWhImportOffpeaktariffSum)
                    .HasColumnName("energy_wh_import_offpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyWhImportOnpeaktariffSum).HasColumnName("energy_wh_import_onpeaktariff_sum");

                entity.Property(e => e.FrequencyAggregationInstGreatestCommon).HasColumnName("frequency_aggregation_inst_greatest_common");

                entity.Property(e => e.FrequencyAggregationInstLowestCommon).HasColumnName("frequency_aggregation_inst_lowest_common");

                entity.Property(e => e.FrequencyAggregationInstValueCommon).HasColumnName("frequency_aggregation_inst_value_common");

                entity.Property(e => e.IAggregationGreatestAvg).HasColumnName("i_aggregation_greatest_avg");

                entity.Property(e => e.IAggregationGreatestL1).HasColumnName("i_aggregation_greatest_l1");

                entity.Property(e => e.IAggregationGreatestL2).HasColumnName("i_aggregation_greatest_l2");

                entity.Property(e => e.IAggregationGreatestL3).HasColumnName("i_aggregation_greatest_l3");

                entity.Property(e => e.IAggregationGreatestLn).HasColumnName("i_aggregation_greatest_ln");

                entity.Property(e => e.IAggregationLowestAvg).HasColumnName("i_aggregation_lowest_avg");

                entity.Property(e => e.IAggregationLowestL1).HasColumnName("i_aggregation_lowest_l1");

                entity.Property(e => e.IAggregationLowestL2).HasColumnName("i_aggregation_lowest_l2");

                entity.Property(e => e.IAggregationLowestL3).HasColumnName("i_aggregation_lowest_l3");

                entity.Property(e => e.IAggregationLowestLn).HasColumnName("i_aggregation_lowest_ln");

                entity.Property(e => e.IAggregationValueAvg).HasColumnName("i_aggregation_value_avg");

                entity.Property(e => e.IAggregationValueL1).HasColumnName("i_aggregation_value_l1");

                entity.Property(e => e.IAggregationValueL2).HasColumnName("i_aggregation_value_l2");

                entity.Property(e => e.IAggregationValueL3).HasColumnName("i_aggregation_value_l3");

                entity.Property(e => e.IAggregationValueLn).HasColumnName("i_aggregation_value_ln");

                entity.Property(e => e.M1AggregationGreatestMode4).HasColumnName("m1_aggregation_greatest_mode4");

                entity.Property(e => e.M1AggregationGreatestMode5020ma).HasColumnName("m1_aggregation_greatest_mode5_020ma");

                entity.Property(e => e.M1AggregationGreatestMode5420ma).HasColumnName("m1_aggregation_greatest_mode5_420ma");

                entity.Property(e => e.M1AggregationGreatestMode5Centralgroundingpointcurrent).HasColumnName("m1_aggregation_greatest_mode5_centralgroundingpointcurrent");

                entity.Property(e => e.M1AggregationGreatestMode6020ma).HasColumnName("m1_aggregation_greatest_mode6_020ma");

                entity.Property(e => e.M1AggregationGreatestMode6420ma).HasColumnName("m1_aggregation_greatest_mode6_420ma");

                entity.Property(e => e.M1AggregationGreatestMode6Centralgroundingpointcurrent).HasColumnName("m1_aggregation_greatest_mode6_centralgroundingpointcurrent");

                entity.Property(e => e.M1AggregationLowestMode4).HasColumnName("m1_aggregation_lowest_mode4");

                entity.Property(e => e.M1AggregationLowestMode5020ma).HasColumnName("m1_aggregation_lowest_mode5_020ma");

                entity.Property(e => e.M1AggregationLowestMode5420ma).HasColumnName("m1_aggregation_lowest_mode5_420ma");

                entity.Property(e => e.M1AggregationLowestMode5Centralgroundingpointcurrent).HasColumnName("m1_aggregation_lowest_mode5_centralgroundingpointcurrent");

                entity.Property(e => e.M1AggregationLowestMode6020ma).HasColumnName("m1_aggregation_lowest_mode6_020ma");

                entity.Property(e => e.M1AggregationLowestMode6420ma).HasColumnName("m1_aggregation_lowest_mode6_420ma");

                entity.Property(e => e.M1AggregationLowestMode6Centralgroundingpointcurrent).HasColumnName("m1_aggregation_lowest_mode6_centralgroundingpointcurrent");

                entity.Property(e => e.M1AggregationValueMode4).HasColumnName("m1_aggregation_value_mode4");

                entity.Property(e => e.M1AggregationValueMode5020ma).HasColumnName("m1_aggregation_value_mode5_020ma");

                entity.Property(e => e.M1AggregationValueMode5420ma).HasColumnName("m1_aggregation_value_mode5_420ma");

                entity.Property(e => e.M1AggregationValueMode5Centralgroundingpointcurrent).HasColumnName("m1_aggregation_value_mode5_centralgroundingpointcurrent");

                entity.Property(e => e.M1AggregationValueMode6020ma).HasColumnName("m1_aggregation_value_mode6_020ma");

                entity.Property(e => e.M1AggregationValueMode6420ma).HasColumnName("m1_aggregation_value_mode6_420ma");

                entity.Property(e => e.M1AggregationValueMode6Centralgroundingpointcurrent).HasColumnName("m1_aggregation_value_mode6_centralgroundingpointcurrent");

                entity.Property(e => e.M2AggregationGreatestMode4).HasColumnName("m2_aggregation_greatest_mode4");

                entity.Property(e => e.M2AggregationGreatestMode5Adc2020ma).HasColumnName("m2_aggregation_greatest_mode5_adc2_020ma");

                entity.Property(e => e.M2AggregationGreatestMode5Adc2420ma).HasColumnName("m2_aggregation_greatest_mode5_adc2_420ma");

                entity.Property(e => e.M2AggregationGreatestMode5adc2Residualcurrent).HasColumnName("m2_aggregation_greatest_mode5adc2_residualcurrent");

                entity.Property(e => e.M2AggregationGreatestMode6Adc3).HasColumnName("m2_aggregation_greatest_mode6_adc3");

                entity.Property(e => e.M2AggregationGreatestMode6Adc3020ma).HasColumnName("m2_aggregation_greatest_mode6_adc3_020ma");

                entity.Property(e => e.M2AggregationGreatestMode6Adc3420ma).HasColumnName("m2_aggregation_greatest_mode6_adc3_420ma");

                entity.Property(e => e.M2AggregationLowestMode4).HasColumnName("m2_aggregation_lowest_mode4");

                entity.Property(e => e.M2AggregationLowestMode5Adc2020ma).HasColumnName("m2_aggregation_lowest_mode5_adc2_020ma");

                entity.Property(e => e.M2AggregationLowestMode5Adc2420ma).HasColumnName("m2_aggregation_lowest_mode5_adc2_420ma");

                entity.Property(e => e.M2AggregationLowestMode5adc2Residualcurrent).HasColumnName("m2_aggregation_lowest_mode5adc2_residualcurrent");

                entity.Property(e => e.M2AggregationLowestMode6Adc3).HasColumnName("m2_aggregation_lowest_mode6_adc3");

                entity.Property(e => e.M2AggregationLowestMode6Adc3020ma).HasColumnName("m2_aggregation_lowest_mode6_adc3_020ma");

                entity.Property(e => e.M2AggregationLowestMode6Adc3420ma).HasColumnName("m2_aggregation_lowest_mode6_adc3_420ma");

                entity.Property(e => e.M2AggregationValueMode4).HasColumnName("m2_aggregation_value_mode4");

                entity.Property(e => e.M2AggregationValueMode5Adc2020ma).HasColumnName("m2_aggregation_value_mode5_adc2_020ma");

                entity.Property(e => e.M2AggregationValueMode5Adc2420ma).HasColumnName("m2_aggregation_value_mode5_adc2_420ma");

                entity.Property(e => e.M2AggregationValueMode5adc2Residualcurrent).HasColumnName("m2_aggregation_value_mode5adc2_residualcurrent");

                entity.Property(e => e.M2AggregationValueMode6Adc3).HasColumnName("m2_aggregation_value_mode6_adc3");

                entity.Property(e => e.M2AggregationValueMode6Adc3020ma).HasColumnName("m2_aggregation_value_mode6_adc3_020ma");

                entity.Property(e => e.M2AggregationValueMode6Adc3420ma).HasColumnName("m2_aggregation_value_mode6_adc3_420ma");

                entity.Property(e => e.PhaseshiftAggregationCosphiGreatestL1).HasColumnName("phaseshift_aggregation_cosphi_greatest_l1");

                entity.Property(e => e.PhaseshiftAggregationCosphiGreatestL2).HasColumnName("phaseshift_aggregation_cosphi_greatest_l2");

                entity.Property(e => e.PhaseshiftAggregationCosphiGreatestL3).HasColumnName("phaseshift_aggregation_cosphi_greatest_l3");

                entity.Property(e => e.PhaseshiftAggregationCosphiLowestL1).HasColumnName("phaseshift_aggregation_cosphi_lowest_l1");

                entity.Property(e => e.PhaseshiftAggregationCosphiLowestL2).HasColumnName("phaseshift_aggregation_cosphi_lowest_l2");

                entity.Property(e => e.PhaseshiftAggregationCosphiLowestL3).HasColumnName("phaseshift_aggregation_cosphi_lowest_l3");

                entity.Property(e => e.PhaseshiftAggregationCosphiValueL1).HasColumnName("phaseshift_aggregation_cosphi_value_l1");

                entity.Property(e => e.PhaseshiftAggregationCosphiValueL2).HasColumnName("phaseshift_aggregation_cosphi_value_l2");

                entity.Property(e => e.PhaseshiftAggregationCosphiValueL3).HasColumnName("phaseshift_aggregation_cosphi_value_l3");

                entity.Property(e => e.PhaseshiftAggregationPhaseangleGreatestL1).HasColumnName("phaseshift_aggregation_phaseangle_greatest_l1");

                entity.Property(e => e.PhaseshiftAggregationPhaseangleGreatestL2).HasColumnName("phaseshift_aggregation_phaseangle_greatest_l2");

                entity.Property(e => e.PhaseshiftAggregationPhaseangleGreatestL3).HasColumnName("phaseshift_aggregation_phaseangle_greatest_l3");

                entity.Property(e => e.PhaseshiftAggregationPhaseangleLowestL1).HasColumnName("phaseshift_aggregation_phaseangle_lowest_l1");

                entity.Property(e => e.PhaseshiftAggregationPhaseangleLowestL2).HasColumnName("phaseshift_aggregation_phaseangle_lowest_l2");

                entity.Property(e => e.PhaseshiftAggregationPhaseangleLowestL3).HasColumnName("phaseshift_aggregation_phaseangle_lowest_l3");

                entity.Property(e => e.PhaseshiftAggregationPhaseangleValueL1).HasColumnName("phaseshift_aggregation_phaseangle_value_l1");

                entity.Property(e => e.PhaseshiftAggregationPhaseangleValueL2).HasColumnName("phaseshift_aggregation_phaseangle_value_l2");

                entity.Property(e => e.PhaseshiftAggregationPhaseangleValueL3).HasColumnName("phaseshift_aggregation_phaseangle_value_l3");

                entity.Property(e => e.PowerAggregationFactorGreatestL1).HasColumnName("power_aggregation_factor_greatest_l1");

                entity.Property(e => e.PowerAggregationFactorGreatestL2).HasColumnName("power_aggregation_factor_greatest_l2");

                entity.Property(e => e.PowerAggregationFactorGreatestL3).HasColumnName("power_aggregation_factor_greatest_l3");

                entity.Property(e => e.PowerAggregationFactorGreatestSum).HasColumnName("power_aggregation_factor_greatest_sum");

                entity.Property(e => e.PowerAggregationFactorLowestL1).HasColumnName("power_aggregation_factor_lowest_l1");

                entity.Property(e => e.PowerAggregationFactorLowestL2).HasColumnName("power_aggregation_factor_lowest_l2");

                entity.Property(e => e.PowerAggregationFactorLowestL3).HasColumnName("power_aggregation_factor_lowest_l3");

                entity.Property(e => e.PowerAggregationFactorLowestSum).HasColumnName("power_aggregation_factor_lowest_sum");

                entity.Property(e => e.PowerAggregationFactorValueL1).HasColumnName("power_aggregation_factor_value_l1");

                entity.Property(e => e.PowerAggregationFactorValueL2).HasColumnName("power_aggregation_factor_value_l2");

                entity.Property(e => e.PowerAggregationFactorValueL3).HasColumnName("power_aggregation_factor_value_l3");

                entity.Property(e => e.PowerAggregationFactorValueSum).HasColumnName("power_aggregation_factor_value_sum");

                entity.Property(e => e.PowerAggregationVaGreatestL1).HasColumnName("power_aggregation_va_greatest_l1");

                entity.Property(e => e.PowerAggregationVaGreatestL2).HasColumnName("power_aggregation_va_greatest_l2");

                entity.Property(e => e.PowerAggregationVaGreatestL3).HasColumnName("power_aggregation_va_greatest_l3");

                entity.Property(e => e.PowerAggregationVaGreatestSum).HasColumnName("power_aggregation_va_greatest_sum");

                entity.Property(e => e.PowerAggregationVaLowestL1).HasColumnName("power_aggregation_va_lowest_l1");

                entity.Property(e => e.PowerAggregationVaLowestL2).HasColumnName("power_aggregation_va_lowest_l2");

                entity.Property(e => e.PowerAggregationVaLowestL3).HasColumnName("power_aggregation_va_lowest_l3");

                entity.Property(e => e.PowerAggregationVaLowestSum).HasColumnName("power_aggregation_va_lowest_sum");

                entity.Property(e => e.PowerAggregationVaValueL1).HasColumnName("power_aggregation_va_value_l1");

                entity.Property(e => e.PowerAggregationVaValueL2).HasColumnName("power_aggregation_va_value_l2");

                entity.Property(e => e.PowerAggregationVaValueL3).HasColumnName("power_aggregation_va_value_l3");

                entity.Property(e => e.PowerAggregationVaValueSum).HasColumnName("power_aggregation_va_value_sum");

                entity.Property(e => e.PowerAggregationVarQ1GreatestL1).HasColumnName("power_aggregation_var_q1_greatest_l1");

                entity.Property(e => e.PowerAggregationVarQ1GreatestL2).HasColumnName("power_aggregation_var_q1_greatest_l2");

                entity.Property(e => e.PowerAggregationVarQ1GreatestL3).HasColumnName("power_aggregation_var_q1_greatest_l3");

                entity.Property(e => e.PowerAggregationVarQ1GreatestSum).HasColumnName("power_aggregation_var_q1_greatest_sum");

                entity.Property(e => e.PowerAggregationVarQ1LowestL1).HasColumnName("power_aggregation_var_q1_lowest_l1");

                entity.Property(e => e.PowerAggregationVarQ1LowestL2).HasColumnName("power_aggregation_var_q1_lowest_l2");

                entity.Property(e => e.PowerAggregationVarQ1LowestL3).HasColumnName("power_aggregation_var_q1_lowest_l3");

                entity.Property(e => e.PowerAggregationVarQ1LowestSum).HasColumnName("power_aggregation_var_q1_lowest_sum");

                entity.Property(e => e.PowerAggregationVarQ1ValueL1).HasColumnName("power_aggregation_var_q1_value_l1");

                entity.Property(e => e.PowerAggregationVarQ1ValueL2).HasColumnName("power_aggregation_var_q1_value_l2");

                entity.Property(e => e.PowerAggregationVarQ1ValueL3).HasColumnName("power_aggregation_var_q1_value_l3");

                entity.Property(e => e.PowerAggregationVarQ1ValueSum).HasColumnName("power_aggregation_var_q1_value_sum");

                entity.Property(e => e.PowerAggregationVarQnGreatestL1).HasColumnName("power_aggregation_var_qn_greatest_l1");

                entity.Property(e => e.PowerAggregationVarQnGreatestL2).HasColumnName("power_aggregation_var_qn_greatest_l2");

                entity.Property(e => e.PowerAggregationVarQnGreatestL3).HasColumnName("power_aggregation_var_qn_greatest_l3");

                entity.Property(e => e.PowerAggregationVarQnGreatestSum).HasColumnName("power_aggregation_var_qn_greatest_sum");

                entity.Property(e => e.PowerAggregationVarQnLowestL1).HasColumnName("power_aggregation_var_qn_lowest_l1");

                entity.Property(e => e.PowerAggregationVarQnLowestL2).HasColumnName("power_aggregation_var_qn_lowest_l2");

                entity.Property(e => e.PowerAggregationVarQnLowestL3).HasColumnName("power_aggregation_var_qn_lowest_l3");

                entity.Property(e => e.PowerAggregationVarQnLowestSum).HasColumnName("power_aggregation_var_qn_lowest_sum");

                entity.Property(e => e.PowerAggregationVarQnValueL1).HasColumnName("power_aggregation_var_qn_value_l1");

                entity.Property(e => e.PowerAggregationVarQnValueL2).HasColumnName("power_aggregation_var_qn_value_l2");

                entity.Property(e => e.PowerAggregationVarQnValueL3).HasColumnName("power_aggregation_var_qn_value_l3");

                entity.Property(e => e.PowerAggregationVarQnValueSum).HasColumnName("power_aggregation_var_qn_value_sum");

                entity.Property(e => e.PowerAggregationVarQtotGreatestL1).HasColumnName("power_aggregation_var_qtot_greatest_l1");

                entity.Property(e => e.PowerAggregationVarQtotGreatestL2).HasColumnName("power_aggregation_var_qtot_greatest_l2");

                entity.Property(e => e.PowerAggregationVarQtotGreatestL3).HasColumnName("power_aggregation_var_qtot_greatest_l3");

                entity.Property(e => e.PowerAggregationVarQtotGreatestSum).HasColumnName("power_aggregation_var_qtot_greatest_sum");

                entity.Property(e => e.PowerAggregationVarQtotLowestL1).HasColumnName("power_aggregation_var_qtot_lowest_l1");

                entity.Property(e => e.PowerAggregationVarQtotLowestL2).HasColumnName("power_aggregation_var_qtot_lowest_l2");

                entity.Property(e => e.PowerAggregationVarQtotLowestL3).HasColumnName("power_aggregation_var_qtot_lowest_l3");

                entity.Property(e => e.PowerAggregationVarQtotLowestSum).HasColumnName("power_aggregation_var_qtot_lowest_sum");

                entity.Property(e => e.PowerAggregationVarQtotValueL1).HasColumnName("power_aggregation_var_qtot_value_l1");

                entity.Property(e => e.PowerAggregationVarQtotValueL2).HasColumnName("power_aggregation_var_qtot_value_l2");

                entity.Property(e => e.PowerAggregationVarQtotValueL3).HasColumnName("power_aggregation_var_qtot_value_l3");

                entity.Property(e => e.PowerAggregationVarQtotValueSum).HasColumnName("power_aggregation_var_qtot_value_sum");

                entity.Property(e => e.PowerAggregationWGreatestL1).HasColumnName("power_aggregation_w_greatest_l1");

                entity.Property(e => e.PowerAggregationWGreatestL2).HasColumnName("power_aggregation_w_greatest_l2");

                entity.Property(e => e.PowerAggregationWGreatestL3).HasColumnName("power_aggregation_w_greatest_l3");

                entity.Property(e => e.PowerAggregationWGreatestSum).HasColumnName("power_aggregation_w_greatest_sum");

                entity.Property(e => e.PowerAggregationWLowestL1).HasColumnName("power_aggregation_w_lowest_l1");

                entity.Property(e => e.PowerAggregationWLowestL2).HasColumnName("power_aggregation_w_lowest_l2");

                entity.Property(e => e.PowerAggregationWLowestL3).HasColumnName("power_aggregation_w_lowest_l3");

                entity.Property(e => e.PowerAggregationWLowestSum).HasColumnName("power_aggregation_w_lowest_sum");

                entity.Property(e => e.PowerAggregationWValueL1).HasColumnName("power_aggregation_w_value_l1");

                entity.Property(e => e.PowerAggregationWValueL2).HasColumnName("power_aggregation_w_value_l2");

                entity.Property(e => e.PowerAggregationWValueL3).HasColumnName("power_aggregation_w_value_l3");

                entity.Property(e => e.PowerAggregationWValueSum).HasColumnName("power_aggregation_w_value_sum");

                entity.Property(e => e.ThdAggregationDistortionIInstGreatestL1).HasColumnName("thd_aggregation_distortion_i_inst_greatest_l1");

                entity.Property(e => e.ThdAggregationDistortionIInstGreatestL2).HasColumnName("thd_aggregation_distortion_i_inst_greatest_l2");

                entity.Property(e => e.ThdAggregationDistortionIInstGreatestL3).HasColumnName("thd_aggregation_distortion_i_inst_greatest_l3");

                entity.Property(e => e.ThdAggregationDistortionIInstLowestL1).HasColumnName("thd_aggregation_distortion_i_inst_lowest_l1");

                entity.Property(e => e.ThdAggregationDistortionIInstLowestL2).HasColumnName("thd_aggregation_distortion_i_inst_lowest_l2");

                entity.Property(e => e.ThdAggregationDistortionIInstLowestL3).HasColumnName("thd_aggregation_distortion_i_inst_lowest_l3");

                entity.Property(e => e.ThdAggregationDistortionIInstValueL1).HasColumnName("thd_aggregation_distortion_i_inst_value_l1");

                entity.Property(e => e.ThdAggregationDistortionIInstValueL2).HasColumnName("thd_aggregation_distortion_i_inst_value_l2");

                entity.Property(e => e.ThdAggregationDistortionIInstValueL3).HasColumnName("thd_aggregation_distortion_i_inst_value_l3");

                entity.Property(e => e.ThdAggregationIInstGreatestL1).HasColumnName("thd_aggregation_i_inst_greatest_l1_");

                entity.Property(e => e.ThdAggregationIInstGreatestL2).HasColumnName("thd_aggregation_i_inst_greatest_l2_");

                entity.Property(e => e.ThdAggregationIInstGreatestL3).HasColumnName("thd_aggregation_i_inst_greatest_l3_");

                entity.Property(e => e.ThdAggregationIInstLowestL1).HasColumnName("thd_aggregation_i_inst_lowest_l1_");

                entity.Property(e => e.ThdAggregationIInstLowestL2).HasColumnName("thd_aggregation_i_inst_lowest_l2_");

                entity.Property(e => e.ThdAggregationIInstLowestL3).HasColumnName("thd_aggregation_i_inst_lowest_l3_");

                entity.Property(e => e.ThdAggregationIInstValueL1).HasColumnName("thd_aggregation_i_inst_value_l1_");

                entity.Property(e => e.ThdAggregationIInstValueL2).HasColumnName("thd_aggregation_i_inst_value_l2_");

                entity.Property(e => e.ThdAggregationIInstValueL3).HasColumnName("thd_aggregation_i_inst_value_l3_");

                entity.Property(e => e.ThdAggregationVLlInstGreatestL1l2).HasColumnName("thd_aggregation_v_ll_inst_greatest_l1l2_");

                entity.Property(e => e.ThdAggregationVLlInstGreatestL2l3).HasColumnName("thd_aggregation_v_ll_inst_greatest_l2l3_");

                entity.Property(e => e.ThdAggregationVLlInstGreatestL3l1).HasColumnName("thd_aggregation_v_ll_inst_greatest_l3l1_");

                entity.Property(e => e.ThdAggregationVLlInstLowestL1l2).HasColumnName("thd_aggregation_v_ll_inst_lowest_l1l2_");

                entity.Property(e => e.ThdAggregationVLlInstLowestL2l3).HasColumnName("thd_aggregation_v_ll_inst_lowest_l2l3_");

                entity.Property(e => e.ThdAggregationVLlInstLowestL3l1).HasColumnName("thd_aggregation_v_ll_inst_lowest_l3l1_");

                entity.Property(e => e.ThdAggregationVLlInstValueL1l2).HasColumnName("thd_aggregation_v_ll_inst_value_l1l2_");

                entity.Property(e => e.ThdAggregationVLlInstValueL2l3).HasColumnName("thd_aggregation_v_ll_inst_value_l2l3_");

                entity.Property(e => e.ThdAggregationVLlInstValueL3l1).HasColumnName("thd_aggregation_v_ll_inst_value_l3l1_");

                entity.Property(e => e.ThdAggregationVLnInstGreatestL1n).HasColumnName("thd_aggregation_v_ln_inst_greatest_l1n_");

                entity.Property(e => e.ThdAggregationVLnInstGreatestL2n).HasColumnName("thd_aggregation_v_ln_inst_greatest_l2n_");

                entity.Property(e => e.ThdAggregationVLnInstGreatestL3n).HasColumnName("thd_aggregation_v_ln_inst_greatest_l3n_");

                entity.Property(e => e.ThdAggregationVLnInstLowestL1n).HasColumnName("thd_aggregation_v_ln_inst_lowest_l1n_");

                entity.Property(e => e.ThdAggregationVLnInstLowestL2n).HasColumnName("thd_aggregation_v_ln_inst_lowest_l2n_");

                entity.Property(e => e.ThdAggregationVLnInstLowestL3n).HasColumnName("thd_aggregation_v_ln_inst_lowest_l3n_");

                entity.Property(e => e.ThdAggregationVLnInstValueL1n).HasColumnName("thd_aggregation_v_ln_inst_value_l1n_");

                entity.Property(e => e.ThdAggregationVLnInstValueL2n).HasColumnName("thd_aggregation_v_ln_inst_value_l2n_");

                entity.Property(e => e.ThdAggregationVLnInstValueL3n).HasColumnName("thd_aggregation_v_ln_inst_value_l3n_");

                entity.Property(e => e.ThreephasesystemAggregationGreatestIAmplbal).HasColumnName("threephasesystem_aggregation_greatest_i_amplbal_");

                entity.Property(e => e.ThreephasesystemAggregationGreatestIBal).HasColumnName("threephasesystem_aggregation_greatest_i_bal_");

                entity.Property(e => e.ThreephasesystemAggregationGreatestSysanglel1l1).HasColumnName("threephasesystem_aggregation_greatest_sysanglel1l1");

                entity.Property(e => e.ThreephasesystemAggregationGreatestSysanglel1l2).HasColumnName("threephasesystem_aggregation_greatest_sysanglel1l2");

                entity.Property(e => e.ThreephasesystemAggregationGreatestSysanglel1l3).HasColumnName("threephasesystem_aggregation_greatest_sysanglel1l3");

                entity.Property(e => e.ThreephasesystemAggregationGreatestVAmplbal).HasColumnName("threephasesystem_aggregation_greatest_v_amplbal_");

                entity.Property(e => e.ThreephasesystemAggregationGreatestVBal).HasColumnName("threephasesystem_aggregation_greatest_v_bal_");

                entity.Property(e => e.ThreephasesystemAggregationLowestIAmplbal).HasColumnName("threephasesystem_aggregation_lowest_i_amplbal_");

                entity.Property(e => e.ThreephasesystemAggregationLowestIBal).HasColumnName("threephasesystem_aggregation_lowest_i_bal_");

                entity.Property(e => e.ThreephasesystemAggregationLowestSysanglel1l1).HasColumnName("threephasesystem_aggregation_lowest_sysanglel1l1");

                entity.Property(e => e.ThreephasesystemAggregationLowestSysanglel1l2).HasColumnName("threephasesystem_aggregation_lowest_sysanglel1l2");

                entity.Property(e => e.ThreephasesystemAggregationLowestSysanglel1l3).HasColumnName("threephasesystem_aggregation_lowest_sysanglel1l3");

                entity.Property(e => e.ThreephasesystemAggregationLowestVAmplbal).HasColumnName("threephasesystem_aggregation_lowest_v_amplbal_");

                entity.Property(e => e.ThreephasesystemAggregationLowestVBal).HasColumnName("threephasesystem_aggregation_lowest_v_bal_");

                entity.Property(e => e.ThreephasesystemAggregationValueIAmplbal).HasColumnName("threephasesystem_aggregation_value_i_amplbal_");

                entity.Property(e => e.ThreephasesystemAggregationValueIBal).HasColumnName("threephasesystem_aggregation_value_i_bal_");

                entity.Property(e => e.ThreephasesystemAggregationValueSysanglel1l1).HasColumnName("threephasesystem_aggregation_value_sysanglel1l1");

                entity.Property(e => e.ThreephasesystemAggregationValueSysanglel1l2).HasColumnName("threephasesystem_aggregation_value_sysanglel1l2");

                entity.Property(e => e.ThreephasesystemAggregationValueSysanglel1l3).HasColumnName("threephasesystem_aggregation_value_sysanglel1l3");

                entity.Property(e => e.ThreephasesystemAggregationValueVAmplbal).HasColumnName("threephasesystem_aggregation_value_v_amplbal_");

                entity.Property(e => e.ThreephasesystemAggregationValueVBal).HasColumnName("threephasesystem_aggregation_value_v_bal_");

                entity.Property(e => e.VLlAggregationGreatestAvg).HasColumnName("v_ll_aggregation_greatest_avg");

                entity.Property(e => e.VLlAggregationGreatestL1l2).HasColumnName("v_ll_aggregation_greatest_l1l2");

                entity.Property(e => e.VLlAggregationGreatestL2l3).HasColumnName("v_ll_aggregation_greatest_l2l3");

                entity.Property(e => e.VLlAggregationGreatestL3l1).HasColumnName("v_ll_aggregation_greatest_l3l1");

                entity.Property(e => e.VLlAggregationLowestAvg).HasColumnName("v_ll_aggregation_lowest_avg");

                entity.Property(e => e.VLlAggregationLowestL1l2).HasColumnName("v_ll_aggregation_lowest_l1l2");

                entity.Property(e => e.VLlAggregationLowestL2l3).HasColumnName("v_ll_aggregation_lowest_l2l3");

                entity.Property(e => e.VLlAggregationLowestL3l1).HasColumnName("v_ll_aggregation_lowest_l3l1");

                entity.Property(e => e.VLlAggregationValueAvg).HasColumnName("v_ll_aggregation_value_avg");

                entity.Property(e => e.VLlAggregationValueL1l2).HasColumnName("v_ll_aggregation_value_l1l2");

                entity.Property(e => e.VLlAggregationValueL2l3).HasColumnName("v_ll_aggregation_value_l2l3");

                entity.Property(e => e.VLlAggregationValueL3l1).HasColumnName("v_ll_aggregation_value_l3l1");

                entity.Property(e => e.VLnAggregationGreatestAvg).HasColumnName("v_ln_aggregation_greatest_avg");

                entity.Property(e => e.VLnAggregationGreatestL1n).HasColumnName("v_ln_aggregation_greatest_l1n");

                entity.Property(e => e.VLnAggregationGreatestL2n).HasColumnName("v_ln_aggregation_greatest_l2n");

                entity.Property(e => e.VLnAggregationGreatestL3n).HasColumnName("v_ln_aggregation_greatest_l3n");

                entity.Property(e => e.VLnAggregationLowestAvg).HasColumnName("v_ln_aggregation_lowest_avg");

                entity.Property(e => e.VLnAggregationLowestL1n).HasColumnName("v_ln_aggregation_lowest_l1n");

                entity.Property(e => e.VLnAggregationLowestL2n).HasColumnName("v_ln_aggregation_lowest_l2n");

                entity.Property(e => e.VLnAggregationLowestL3n).HasColumnName("v_ln_aggregation_lowest_l3n");

                entity.Property(e => e.VLnAggregationValueAvg).HasColumnName("v_ln_aggregation_value_avg");

                entity.Property(e => e.VLnAggregationValueL1n).HasColumnName("v_ln_aggregation_value_l1n");

                entity.Property(e => e.VLnAggregationValueL2n).HasColumnName("v_ln_aggregation_value_l2n");

                entity.Property(e => e.VLnAggregationValueL3n).HasColumnName("v_ln_aggregation_value_l3n");
            });

            modelBuilder.Entity<ArchiveDataFormat2>(entity =>
            {
                entity.HasKey(e => new { e.ObjectId, e.TimestampInS, e.Archive });

                entity.ToTable("ArchiveDataFormat2");

                entity.Property(e => e.CounterUniversal).HasColumnName("counter_universal");

                entity.Property(e => e.EnergyVarhExportOffpeaktariffSum)
                    .HasColumnName("energy_varh_export_offpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyVarhExportOnpeaktariffSum)
                    .HasColumnName("energy_varh_export_onpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyVarhImportOffpeaktariffSum)
                    .HasColumnName("energy_varh_import_offpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyVarhImportOnpeaktariffSum)
                    .HasColumnName("energy_varh_import_onpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyWhExportOffpeaktariffSum)
                    .HasColumnName("energy_wh_export_offpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyWhExportOnpeaktariffSum).HasColumnName("energy_wh_export_onpeaktariff_sum");

                entity.Property(e => e.EnergyWhImportOffpeaktariffSum)
                    .HasColumnName("energy_wh_import_offpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyWhImportOnpeaktariffSum).HasColumnName("energy_wh_import_onpeaktariff_sum");

                entity.Property(e => e.FrequencyAggregationGreatestCommon).HasColumnName("frequency_aggregation_greatest_common");

                entity.Property(e => e.FrequencyAggregationValueCommon).HasColumnName("frequency_aggregation_value_common");

                entity.Property(e => e.FrequencyAggregationValueLowestCommon).HasColumnName("frequency_aggregation_value_lowest_common");

                entity.Property(e => e.IAggregationGreatestAvg).HasColumnName("i_aggregation_greatest_avg");

                entity.Property(e => e.IAggregationGreatestL1).HasColumnName("i_aggregation_greatest_l1");

                entity.Property(e => e.IAggregationGreatestL2).HasColumnName("i_aggregation_greatest_l2");

                entity.Property(e => e.IAggregationGreatestL3).HasColumnName("i_aggregation_greatest_l3");

                entity.Property(e => e.IAggregationLowestAvg).HasColumnName("i_aggregation_lowest_avg");

                entity.Property(e => e.IAggregationLowestL1).HasColumnName("i_aggregation_lowest_l1");

                entity.Property(e => e.IAggregationLowestL2).HasColumnName("i_aggregation_lowest_l2");

                entity.Property(e => e.IAggregationLowestL3).HasColumnName("i_aggregation_lowest_l3");

                entity.Property(e => e.IAggregationValueAvg).HasColumnName("i_aggregation_value_avg");

                entity.Property(e => e.IAggregationValueL1).HasColumnName("i_aggregation_value_l1");

                entity.Property(e => e.IAggregationValueL2).HasColumnName("i_aggregation_value_l2");

                entity.Property(e => e.IAggregationValueL3).HasColumnName("i_aggregation_value_l3");

                entity.Property(e => e.PowerFactorAggregationGreatestL1).HasColumnName("power_factor_aggregation_greatest_l1");

                entity.Property(e => e.PowerFactorAggregationGreatestL2).HasColumnName("power_factor_aggregation_greatest_l2");

                entity.Property(e => e.PowerFactorAggregationGreatestL3).HasColumnName("power_factor_aggregation_greatest_l3");

                entity.Property(e => e.PowerFactorAggregationGreatestSum).HasColumnName("power_factor_aggregation_greatest_sum");

                entity.Property(e => e.PowerFactorAggregationLowestL1).HasColumnName("power_factor_aggregation_lowest_l1");

                entity.Property(e => e.PowerFactorAggregationLowestL2).HasColumnName("power_factor_aggregation_lowest_l2");

                entity.Property(e => e.PowerFactorAggregationLowestL3).HasColumnName("power_factor_aggregation_lowest_l3");

                entity.Property(e => e.PowerFactorAggregationLowestSum).HasColumnName("power_factor_aggregation_lowest_sum");

                entity.Property(e => e.PowerFactorAggregationValueL1).HasColumnName("power_factor_aggregation_value_l1");

                entity.Property(e => e.PowerFactorAggregationValueL2).HasColumnName("power_factor_aggregation_value_l2");

                entity.Property(e => e.PowerFactorAggregationValueL3).HasColumnName("power_factor_aggregation_value_l3");

                entity.Property(e => e.PowerFactorAggregationValueSum).HasColumnName("power_factor_aggregation_value_sum");

                entity.Property(e => e.PowerVaAggregationGreatestL1).HasColumnName("power_va_aggregation_greatest_l1");

                entity.Property(e => e.PowerVaAggregationGreatestL2).HasColumnName("power_va_aggregation_greatest_l2");

                entity.Property(e => e.PowerVaAggregationGreatestL3).HasColumnName("power_va_aggregation_greatest_l3");

                entity.Property(e => e.PowerVaAggregationGreatestSum).HasColumnName("power_va_aggregation_greatest_sum");

                entity.Property(e => e.PowerVaAggregationLowestL1).HasColumnName("power_va_aggregation_lowest_l1");

                entity.Property(e => e.PowerVaAggregationLowestL2).HasColumnName("power_va_aggregation_lowest_l2");

                entity.Property(e => e.PowerVaAggregationLowestL3).HasColumnName("power_va_aggregation_lowest_l3");

                entity.Property(e => e.PowerVaAggregationLowestSum).HasColumnName("power_va_aggregation_lowest_sum");

                entity.Property(e => e.PowerVaAggregationValueL1).HasColumnName("power_va_aggregation_value_l1");

                entity.Property(e => e.PowerVaAggregationValueL2).HasColumnName("power_va_aggregation_value_l2");

                entity.Property(e => e.PowerVaAggregationValueL3).HasColumnName("power_va_aggregation_value_l3");

                entity.Property(e => e.PowerVaAggregationValueSum).HasColumnName("power_va_aggregation_value_sum");

                entity.Property(e => e.PowerVarQnAggregationGreatestL1).HasColumnName("power_var_qn_aggregation_greatest_l1");

                entity.Property(e => e.PowerVarQnAggregationGreatestL2).HasColumnName("power_var_qn_aggregation_greatest_l2");

                entity.Property(e => e.PowerVarQnAggregationGreatestL3).HasColumnName("power_var_qn_aggregation_greatest_l3");

                entity.Property(e => e.PowerVarQnAggregationGreatestSum).HasColumnName("power_var_qn_aggregation_greatest_sum");

                entity.Property(e => e.PowerVarQnAggregationLowestL1).HasColumnName("power_var_qn_aggregation_lowest_l1");

                entity.Property(e => e.PowerVarQnAggregationLowestL2).HasColumnName("power_var_qn_aggregation_lowest_l2");

                entity.Property(e => e.PowerVarQnAggregationLowestL3).HasColumnName("power_var_qn_aggregation_lowest_l3");

                entity.Property(e => e.PowerVarQnAggregationLowestSum).HasColumnName("power_var_qn_aggregation_lowest_sum");

                entity.Property(e => e.PowerVarQnAggregationValueL1).HasColumnName("power_var_qn_aggregation_value_l1");

                entity.Property(e => e.PowerVarQnAggregationValueL2).HasColumnName("power_var_qn_aggregation_value_l2");

                entity.Property(e => e.PowerVarQnAggregationValueL3).HasColumnName("power_var_qn_aggregation_value_l3");

                entity.Property(e => e.PowerVarQnAggregationValueSum).HasColumnName("power_var_qn_aggregation_value_sum");

                entity.Property(e => e.PowerWAggregationGreatestL1).HasColumnName("power_w_aggregation_greatest_l1");

                entity.Property(e => e.PowerWAggregationGreatestL2).HasColumnName("power_w_aggregation_greatest_l2");

                entity.Property(e => e.PowerWAggregationGreatestL3).HasColumnName("power_w_aggregation_greatest_l3");

                entity.Property(e => e.PowerWAggregationGreatestSum).HasColumnName("power_w_aggregation_greatest_sum");

                entity.Property(e => e.PowerWAggregationLowestL1).HasColumnName("power_w_aggregation_lowest_l1");

                entity.Property(e => e.PowerWAggregationLowestL2).HasColumnName("power_w_aggregation_lowest_l2");

                entity.Property(e => e.PowerWAggregationLowestL3).HasColumnName("power_w_aggregation_lowest_l3");

                entity.Property(e => e.PowerWAggregationLowestSum).HasColumnName("power_w_aggregation_lowest_sum");

                entity.Property(e => e.PowerWAggregationValueL1).HasColumnName("power_w_aggregation_value_l1");

                entity.Property(e => e.PowerWAggregationValueL2).HasColumnName("power_w_aggregation_value_l2");

                entity.Property(e => e.PowerWAggregationValueL3).HasColumnName("power_w_aggregation_value_l3");

                entity.Property(e => e.PowerWAggregationValueSum).HasColumnName("power_w_aggregation_value_sum");

                entity.Property(e => e.VLlAggregationGreatestAvg).HasColumnName("v_ll_aggregation_greatest_avg");

                entity.Property(e => e.VLlAggregationGreatestL1l2).HasColumnName("v_ll_aggregation_greatest_l1l2");

                entity.Property(e => e.VLlAggregationGreatestL2l3).HasColumnName("v_ll_aggregation_greatest_l2l3");

                entity.Property(e => e.VLlAggregationGreatestL3l1).HasColumnName("v_ll_aggregation_greatest_l3l1");

                entity.Property(e => e.VLlAggregationLowestAvg).HasColumnName("v_ll_aggregation_lowest_avg");

                entity.Property(e => e.VLlAggregationLowestL1l2).HasColumnName("v_ll_aggregation_lowest_l1l2");

                entity.Property(e => e.VLlAggregationLowestL2l3).HasColumnName("v_ll_aggregation_lowest_l2l3");

                entity.Property(e => e.VLlAggregationLowestL3l1).HasColumnName("v_ll_aggregation_lowest_l3l1");

                entity.Property(e => e.VLlAggregationValueAvg).HasColumnName("v_ll_aggregation_value_avg");

                entity.Property(e => e.VLlAggregationValueL1l2).HasColumnName("v_ll_aggregation_value_l1l2");

                entity.Property(e => e.VLlAggregationValueL2l3).HasColumnName("v_ll_aggregation_value_l2l3");

                entity.Property(e => e.VLlAggregationValueL3l1).HasColumnName("v_ll_aggregation_value_l3l1");

                entity.Property(e => e.VLnAggregationGreatestAvg).HasColumnName("v_ln_aggregation_greatest_avg");

                entity.Property(e => e.VLnAggregationGreatestL1n).HasColumnName("v_ln_aggregation_greatest_l1n");

                entity.Property(e => e.VLnAggregationGreatestL2n).HasColumnName("v_ln_aggregation_greatest_l2n");

                entity.Property(e => e.VLnAggregationGreatestL3n).HasColumnName("v_ln_aggregation_greatest_l3n");

                entity.Property(e => e.VLnAggregationLowestAvg).HasColumnName("v_ln_aggregation_lowest_avg");

                entity.Property(e => e.VLnAggregationLowestL1n).HasColumnName("v_ln_aggregation_lowest_l1n");

                entity.Property(e => e.VLnAggregationLowestL2n).HasColumnName("v_ln_aggregation_lowest_l2n");

                entity.Property(e => e.VLnAggregationLowestL3n).HasColumnName("v_ln_aggregation_lowest_l3n");

                entity.Property(e => e.VLnAggregationValueAvg).HasColumnName("v_ln_aggregation_value_avg");

                entity.Property(e => e.VLnAggregationValueL1n).HasColumnName("v_ln_aggregation_value_l1n");

                entity.Property(e => e.VLnAggregationValueL2n).HasColumnName("v_ln_aggregation_value_l2n");

                entity.Property(e => e.VLnAggregationValueL3n).HasColumnName("v_ln_aggregation_value_l3n");
            });

            modelBuilder.Entity<ArchiveDataFormat3>(entity =>
            {
                entity.HasKey(e => new { e.ObjectId, e.TimestampInS, e.Archive });

                entity.ToTable("ArchiveDataFormat3");

                entity.Property(e => e.CounterUniversal).HasColumnName("counter_universal");

                entity.Property(e => e.EnergyVarhExportOffpeaktariffSum)
                    .HasColumnName("energy_varh_export_offpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyVarhExportOnpeaktariffSum)
                    .HasColumnName("energy_varh_export_onpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyVarhImportOffpeaktariffSum)
                    .HasColumnName("energy_varh_import_offpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyVarhImportOnpeaktariffSum)
                    .HasColumnName("energy_varh_import_onpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyWhExportOffpeaktariffSum)
                    .HasColumnName("energy_wh_export_offpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyWhExportOnpeaktariffSum).HasColumnName("energy_wh_export_onpeaktariff_sum");

                entity.Property(e => e.EnergyWhImportOffpeaktariffSum)
                    .HasColumnName("energy_wh_import_offpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyWhImportOnpeaktariffSum).HasColumnName("energy_wh_import_onpeaktariff_sum");

                entity.Property(e => e.FrequencyAggregationGreatestCommon).HasColumnName("frequency_aggregation_greatest_common");

                entity.Property(e => e.FrequencyAggregationValueCommon).HasColumnName("frequency_aggregation_value_common");

                entity.Property(e => e.FrequencyAggregationValueLowestCommon).HasColumnName("frequency_aggregation_value_lowest_common");

                entity.Property(e => e.IAggregationGreatestAvg).HasColumnName("i_aggregation_greatest_avg");

                entity.Property(e => e.IAggregationGreatestIn).HasColumnName("i_aggregation_greatest_in");

                entity.Property(e => e.IAggregationGreatestL1).HasColumnName("i_aggregation_greatest_l1");

                entity.Property(e => e.IAggregationGreatestL2).HasColumnName("i_aggregation_greatest_l2");

                entity.Property(e => e.IAggregationGreatestL3).HasColumnName("i_aggregation_greatest_l3");

                entity.Property(e => e.IAggregationLowestAvg).HasColumnName("i_aggregation_lowest_avg");

                entity.Property(e => e.IAggregationLowestIn).HasColumnName("i_aggregation_lowest_in");

                entity.Property(e => e.IAggregationLowestL1).HasColumnName("i_aggregation_lowest_l1");

                entity.Property(e => e.IAggregationLowestL2).HasColumnName("i_aggregation_lowest_l2");

                entity.Property(e => e.IAggregationLowestL3).HasColumnName("i_aggregation_lowest_l3");

                entity.Property(e => e.IAggregationValueAvg).HasColumnName("i_aggregation_value_avg");

                entity.Property(e => e.IAggregationValueIn).HasColumnName("i_aggregation_value_in");

                entity.Property(e => e.IAggregationValueL1).HasColumnName("i_aggregation_value_l1");

                entity.Property(e => e.IAggregationValueL2).HasColumnName("i_aggregation_value_l2");

                entity.Property(e => e.IAggregationValueL3).HasColumnName("i_aggregation_value_l3");

                entity.Property(e => e.PowerFactorAggregationGreatestL1).HasColumnName("power_factor_aggregation_greatest_l1");

                entity.Property(e => e.PowerFactorAggregationGreatestL2).HasColumnName("power_factor_aggregation_greatest_l2");

                entity.Property(e => e.PowerFactorAggregationGreatestL3).HasColumnName("power_factor_aggregation_greatest_l3");

                entity.Property(e => e.PowerFactorAggregationGreatestSum).HasColumnName("power_factor_aggregation_greatest_sum");

                entity.Property(e => e.PowerFactorAggregationLowestL1).HasColumnName("power_factor_aggregation_lowest_l1");

                entity.Property(e => e.PowerFactorAggregationLowestL2).HasColumnName("power_factor_aggregation_lowest_l2");

                entity.Property(e => e.PowerFactorAggregationLowestL3).HasColumnName("power_factor_aggregation_lowest_l3");

                entity.Property(e => e.PowerFactorAggregationLowestSum).HasColumnName("power_factor_aggregation_lowest_sum");

                entity.Property(e => e.PowerFactorAggregationValueL1).HasColumnName("power_factor_aggregation_value_l1");

                entity.Property(e => e.PowerFactorAggregationValueL2).HasColumnName("power_factor_aggregation_value_l2");

                entity.Property(e => e.PowerFactorAggregationValueL3).HasColumnName("power_factor_aggregation_value_l3");

                entity.Property(e => e.PowerFactorAggregationValueSum).HasColumnName("power_factor_aggregation_value_sum");

                entity.Property(e => e.PowerVaAggregationGreatestL1).HasColumnName("power_va_aggregation_greatest_l1");

                entity.Property(e => e.PowerVaAggregationGreatestL2).HasColumnName("power_va_aggregation_greatest_l2");

                entity.Property(e => e.PowerVaAggregationGreatestL3).HasColumnName("power_va_aggregation_greatest_l3");

                entity.Property(e => e.PowerVaAggregationGreatestSum).HasColumnName("power_va_aggregation_greatest_sum");

                entity.Property(e => e.PowerVaAggregationLowestL1).HasColumnName("power_va_aggregation_lowest_l1");

                entity.Property(e => e.PowerVaAggregationLowestL2).HasColumnName("power_va_aggregation_lowest_l2");

                entity.Property(e => e.PowerVaAggregationLowestL3).HasColumnName("power_va_aggregation_lowest_l3");

                entity.Property(e => e.PowerVaAggregationLowestSum).HasColumnName("power_va_aggregation_lowest_sum");

                entity.Property(e => e.PowerVaAggregationValueL1).HasColumnName("power_va_aggregation_value_l1");

                entity.Property(e => e.PowerVaAggregationValueL2).HasColumnName("power_va_aggregation_value_l2");

                entity.Property(e => e.PowerVaAggregationValueL3).HasColumnName("power_va_aggregation_value_l3");

                entity.Property(e => e.PowerVaAggregationValueSum).HasColumnName("power_va_aggregation_value_sum");

                entity.Property(e => e.PowerVarQnAggregationGreatestL1).HasColumnName("power_var_qn_aggregation_greatest_l1");

                entity.Property(e => e.PowerVarQnAggregationGreatestL2).HasColumnName("power_var_qn_aggregation_greatest_l2");

                entity.Property(e => e.PowerVarQnAggregationGreatestL3).HasColumnName("power_var_qn_aggregation_greatest_l3");

                entity.Property(e => e.PowerVarQnAggregationGreatestSum).HasColumnName("power_var_qn_aggregation_greatest_sum");

                entity.Property(e => e.PowerVarQnAggregationLowestL1).HasColumnName("power_var_qn_aggregation_lowest_l1");

                entity.Property(e => e.PowerVarQnAggregationLowestL2).HasColumnName("power_var_qn_aggregation_lowest_l2");

                entity.Property(e => e.PowerVarQnAggregationLowestL3).HasColumnName("power_var_qn_aggregation_lowest_l3");

                entity.Property(e => e.PowerVarQnAggregationLowestSum).HasColumnName("power_var_qn_aggregation_lowest_sum");

                entity.Property(e => e.PowerVarQnAggregationValueL1).HasColumnName("power_var_qn_aggregation_value_l1");

                entity.Property(e => e.PowerVarQnAggregationValueL2).HasColumnName("power_var_qn_aggregation_value_l2");

                entity.Property(e => e.PowerVarQnAggregationValueL3).HasColumnName("power_var_qn_aggregation_value_l3");

                entity.Property(e => e.PowerVarQnAggregationValueSum).HasColumnName("power_var_qn_aggregation_value_sum");

                entity.Property(e => e.PowerWAggregationGreatestL1).HasColumnName("power_w_aggregation_greatest_l1");

                entity.Property(e => e.PowerWAggregationGreatestL2).HasColumnName("power_w_aggregation_greatest_l2");

                entity.Property(e => e.PowerWAggregationGreatestL3).HasColumnName("power_w_aggregation_greatest_l3");

                entity.Property(e => e.PowerWAggregationGreatestSum).HasColumnName("power_w_aggregation_greatest_sum");

                entity.Property(e => e.PowerWAggregationLowestL1).HasColumnName("power_w_aggregation_lowest_l1");

                entity.Property(e => e.PowerWAggregationLowestL2).HasColumnName("power_w_aggregation_lowest_l2");

                entity.Property(e => e.PowerWAggregationLowestL3).HasColumnName("power_w_aggregation_lowest_l3");

                entity.Property(e => e.PowerWAggregationLowestSum).HasColumnName("power_w_aggregation_lowest_sum");

                entity.Property(e => e.PowerWAggregationValueL1).HasColumnName("power_w_aggregation_value_l1");

                entity.Property(e => e.PowerWAggregationValueL2).HasColumnName("power_w_aggregation_value_l2");

                entity.Property(e => e.PowerWAggregationValueL3).HasColumnName("power_w_aggregation_value_l3");

                entity.Property(e => e.PowerWAggregationValueSum).HasColumnName("power_w_aggregation_value_sum");

                entity.Property(e => e.ThdIAggregationGreatestL1).HasColumnName("thd_i_aggregation_greatest_l1");

                entity.Property(e => e.ThdIAggregationGreatestL2).HasColumnName("thd_i_aggregation_greatest_l2");

                entity.Property(e => e.ThdIAggregationGreatestL3).HasColumnName("thd_i_aggregation_greatest_l3");

                entity.Property(e => e.ThdIAggregationLowestL1).HasColumnName("thd_i_aggregation_lowest_l1");

                entity.Property(e => e.ThdIAggregationLowestL2).HasColumnName("thd_i_aggregation_lowest_l2");

                entity.Property(e => e.ThdIAggregationLowestL3).HasColumnName("thd_i_aggregation_lowest_l3");

                entity.Property(e => e.ThdIAggregationValueL1).HasColumnName("thd_i_aggregation_value_l1");

                entity.Property(e => e.ThdIAggregationValueL2).HasColumnName("thd_i_aggregation_value_l2");

                entity.Property(e => e.ThdIAggregationValueL3).HasColumnName("thd_i_aggregation_value_l3");

                entity.Property(e => e.ThdVLnAggregationGreatestL1n).HasColumnName("thd_v_ln_aggregation_greatest_l1n");

                entity.Property(e => e.ThdVLnAggregationGreatestL2n).HasColumnName("thd_v_ln_aggregation_greatest_l2n");

                entity.Property(e => e.ThdVLnAggregationGreatestL3n).HasColumnName("thd_v_ln_aggregation_greatest_l3n");

                entity.Property(e => e.ThdVLnAggregationLowestL1n).HasColumnName("thd_v_ln_aggregation_lowest_l1n");

                entity.Property(e => e.ThdVLnAggregationLowestL2n).HasColumnName("thd_v_ln_aggregation_lowest_l2n");

                entity.Property(e => e.ThdVLnAggregationLowestL3n).HasColumnName("thd_v_ln_aggregation_lowest_l3n");

                entity.Property(e => e.ThdVLnAggregationValueL1n).HasColumnName("thd_v_ln_aggregation_value_l1n");

                entity.Property(e => e.ThdVLnAggregationValueL2n).HasColumnName("thd_v_ln_aggregation_value_l2n");

                entity.Property(e => e.ThdVLnAggregationValueL3n).HasColumnName("thd_v_ln_aggregation_value_l3n");

                entity.Property(e => e.ThreephasesystemAggregationGreatestIAmplbal).HasColumnName("threephasesystem_aggregation_greatest_i_amplbal_");

                entity.Property(e => e.ThreephasesystemAggregationGreatestVAmplbal).HasColumnName("threephasesystem_aggregation_greatest_v_amplbal_");

                entity.Property(e => e.ThreephasesystemAggregationIAmplbal).HasColumnName("threephasesystem_aggregation_i_amplbal_");

                entity.Property(e => e.ThreephasesystemAggregationLowestIAmplbal).HasColumnName("threephasesystem_aggregation_lowest_i_amplbal_");

                entity.Property(e => e.ThreephasesystemAggregationLowestVAmplbal).HasColumnName("threephasesystem_aggregation_lowest_v_amplbal_");

                entity.Property(e => e.ThreephasesystemAggregationVAmplbal).HasColumnName("threephasesystem_aggregation_v_amplbal_");

                entity.Property(e => e.VLlAggregationGreatestAvg).HasColumnName("v_ll_aggregation_greatest_avg");

                entity.Property(e => e.VLlAggregationGreatestL1l2).HasColumnName("v_ll_aggregation_greatest_l1l2");

                entity.Property(e => e.VLlAggregationGreatestL2l3).HasColumnName("v_ll_aggregation_greatest_l2l3");

                entity.Property(e => e.VLlAggregationGreatestL3l1).HasColumnName("v_ll_aggregation_greatest_l3l1");

                entity.Property(e => e.VLlAggregationLowestAvg).HasColumnName("v_ll_aggregation_lowest_avg");

                entity.Property(e => e.VLlAggregationLowestL1l2).HasColumnName("v_ll_aggregation_lowest_l1l2");

                entity.Property(e => e.VLlAggregationLowestL2l3).HasColumnName("v_ll_aggregation_lowest_l2l3");

                entity.Property(e => e.VLlAggregationLowestL3l1).HasColumnName("v_ll_aggregation_lowest_l3l1");

                entity.Property(e => e.VLlAggregationValueAvg).HasColumnName("v_ll_aggregation_value_avg");

                entity.Property(e => e.VLlAggregationValueL1l2).HasColumnName("v_ll_aggregation_value_l1l2");

                entity.Property(e => e.VLlAggregationValueL2l3).HasColumnName("v_ll_aggregation_value_l2l3");

                entity.Property(e => e.VLlAggregationValueL3l1).HasColumnName("v_ll_aggregation_value_l3l1");

                entity.Property(e => e.VLnAggregationGreatestAvg).HasColumnName("v_ln_aggregation_greatest_avg");

                entity.Property(e => e.VLnAggregationGreatestL1n).HasColumnName("v_ln_aggregation_greatest_l1n");

                entity.Property(e => e.VLnAggregationGreatestL2n).HasColumnName("v_ln_aggregation_greatest_l2n");

                entity.Property(e => e.VLnAggregationGreatestL3n).HasColumnName("v_ln_aggregation_greatest_l3n");

                entity.Property(e => e.VLnAggregationLowestAvg).HasColumnName("v_ln_aggregation_lowest_avg");

                entity.Property(e => e.VLnAggregationLowestL1n).HasColumnName("v_ln_aggregation_lowest_l1n");

                entity.Property(e => e.VLnAggregationLowestL2n).HasColumnName("v_ln_aggregation_lowest_l2n");

                entity.Property(e => e.VLnAggregationLowestL3n).HasColumnName("v_ln_aggregation_lowest_l3n");

                entity.Property(e => e.VLnAggregationValueAvg).HasColumnName("v_ln_aggregation_value_avg");

                entity.Property(e => e.VLnAggregationValueL1n).HasColumnName("v_ln_aggregation_value_l1n");

                entity.Property(e => e.VLnAggregationValueL2n).HasColumnName("v_ln_aggregation_value_l2n");

                entity.Property(e => e.VLnAggregationValueL3n).HasColumnName("v_ln_aggregation_value_l3n");
            });

            modelBuilder.Entity<ArchiveDataFormat4>(entity =>
            {
                entity.HasKey(e => new { e.ObjectId, e.TimestampInS, e.Archive });

                entity.ToTable("ArchiveDataFormat4");

                entity.Property(e => e.CounterUniversal).HasColumnName("counter_universal");

                entity.Property(e => e.EnergyVarhExportOffpeaktariffSum)
                    .HasColumnName("energy_varh_export_offpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyVarhExportOnpeaktariffSum)
                    .HasColumnName("energy_varh_export_onpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyVarhImportOffpeaktariffSum)
                    .HasColumnName("energy_varh_import_offpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyVarhImportOnpeaktariffSum)
                    .HasColumnName("energy_varh_import_onpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyWhExportOffpeaktariffSum)
                    .HasColumnName("energy_wh_export_offpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyWhExportOnpeaktariffSum).HasColumnName("energy_wh_export_onpeaktariff_sum");

                entity.Property(e => e.EnergyWhImportOffpeaktariffSum)
                    .HasColumnName("energy_wh_import_offpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyWhImportOnpeaktariffSum).HasColumnName("energy_wh_import_onpeaktariff_sum");

                entity.Property(e => e.FrequencyAggregationGreatestCommon).HasColumnName("frequency_aggregation_greatest_common");

                entity.Property(e => e.FrequencyAggregationValueCommon).HasColumnName("frequency_aggregation_value_common");

                entity.Property(e => e.FrequencyAggregationValueLowestCommon).HasColumnName("frequency_aggregation_value_lowest_common");

                entity.Property(e => e.IAggregationGreatestAvg).HasColumnName("i_aggregation_greatest_avg");

                entity.Property(e => e.IAggregationGreatestIn).HasColumnName("i_aggregation_greatest_in");

                entity.Property(e => e.IAggregationGreatestL1).HasColumnName("i_aggregation_greatest_l1");

                entity.Property(e => e.IAggregationGreatestL2).HasColumnName("i_aggregation_greatest_l2");

                entity.Property(e => e.IAggregationGreatestL3).HasColumnName("i_aggregation_greatest_l3");

                entity.Property(e => e.IAggregationLowestAvg).HasColumnName("i_aggregation_lowest_avg");

                entity.Property(e => e.IAggregationLowestIn).HasColumnName("i_aggregation_lowest_in");

                entity.Property(e => e.IAggregationLowestL1).HasColumnName("i_aggregation_lowest_l1");

                entity.Property(e => e.IAggregationLowestL2).HasColumnName("i_aggregation_lowest_l2");

                entity.Property(e => e.IAggregationLowestL3).HasColumnName("i_aggregation_lowest_l3");

                entity.Property(e => e.IAggregationValueAvg).HasColumnName("i_aggregation_value_avg");

                entity.Property(e => e.IAggregationValueIn).HasColumnName("i_aggregation_value_in");

                entity.Property(e => e.IAggregationValueL1).HasColumnName("i_aggregation_value_l1");

                entity.Property(e => e.IAggregationValueL2).HasColumnName("i_aggregation_value_l2");

                entity.Property(e => e.IAggregationValueL3).HasColumnName("i_aggregation_value_l3");

                entity.Property(e => e.PowerFactorAggregationGreatestL1).HasColumnName("power_factor_aggregation_greatest_l1");

                entity.Property(e => e.PowerFactorAggregationGreatestL2).HasColumnName("power_factor_aggregation_greatest_l2");

                entity.Property(e => e.PowerFactorAggregationGreatestL3).HasColumnName("power_factor_aggregation_greatest_l3");

                entity.Property(e => e.PowerFactorAggregationGreatestSum).HasColumnName("power_factor_aggregation_greatest_sum");

                entity.Property(e => e.PowerFactorAggregationLowestL1).HasColumnName("power_factor_aggregation_lowest_l1");

                entity.Property(e => e.PowerFactorAggregationLowestL2).HasColumnName("power_factor_aggregation_lowest_l2");

                entity.Property(e => e.PowerFactorAggregationLowestL3).HasColumnName("power_factor_aggregation_lowest_l3");

                entity.Property(e => e.PowerFactorAggregationLowestSum).HasColumnName("power_factor_aggregation_lowest_sum");

                entity.Property(e => e.PowerFactorAggregationValueL1).HasColumnName("power_factor_aggregation_value_l1");

                entity.Property(e => e.PowerFactorAggregationValueL2).HasColumnName("power_factor_aggregation_value_l2");

                entity.Property(e => e.PowerFactorAggregationValueL3).HasColumnName("power_factor_aggregation_value_l3");

                entity.Property(e => e.PowerFactorAggregationValueSum).HasColumnName("power_factor_aggregation_value_sum");

                entity.Property(e => e.PowerVaAggregationGreatestL1).HasColumnName("power_va_aggregation_greatest_l1");

                entity.Property(e => e.PowerVaAggregationGreatestL2).HasColumnName("power_va_aggregation_greatest_l2");

                entity.Property(e => e.PowerVaAggregationGreatestL3).HasColumnName("power_va_aggregation_greatest_l3");

                entity.Property(e => e.PowerVaAggregationGreatestSum).HasColumnName("power_va_aggregation_greatest_sum");

                entity.Property(e => e.PowerVaAggregationLowestL1).HasColumnName("power_va_aggregation_lowest_l1");

                entity.Property(e => e.PowerVaAggregationLowestL2).HasColumnName("power_va_aggregation_lowest_l2");

                entity.Property(e => e.PowerVaAggregationLowestL3).HasColumnName("power_va_aggregation_lowest_l3");

                entity.Property(e => e.PowerVaAggregationLowestSum).HasColumnName("power_va_aggregation_lowest_sum");

                entity.Property(e => e.PowerVaAggregationValueL1).HasColumnName("power_va_aggregation_value_l1");

                entity.Property(e => e.PowerVaAggregationValueL2).HasColumnName("power_va_aggregation_value_l2");

                entity.Property(e => e.PowerVaAggregationValueL3).HasColumnName("power_va_aggregation_value_l3");

                entity.Property(e => e.PowerVaAggregationValueSum).HasColumnName("power_va_aggregation_value_sum");

                entity.Property(e => e.PowerVarQnAggregationGreatestL1).HasColumnName("power_var_qn_aggregation_greatest_l1");

                entity.Property(e => e.PowerVarQnAggregationGreatestL2).HasColumnName("power_var_qn_aggregation_greatest_l2");

                entity.Property(e => e.PowerVarQnAggregationGreatestL3).HasColumnName("power_var_qn_aggregation_greatest_l3");

                entity.Property(e => e.PowerVarQnAggregationGreatestSum).HasColumnName("power_var_qn_aggregation_greatest_sum");

                entity.Property(e => e.PowerVarQnAggregationLowestL1).HasColumnName("power_var_qn_aggregation_lowest_l1");

                entity.Property(e => e.PowerVarQnAggregationLowestL2).HasColumnName("power_var_qn_aggregation_lowest_l2");

                entity.Property(e => e.PowerVarQnAggregationLowestL3).HasColumnName("power_var_qn_aggregation_lowest_l3");

                entity.Property(e => e.PowerVarQnAggregationLowestSum).HasColumnName("power_var_qn_aggregation_lowest_sum");

                entity.Property(e => e.PowerVarQnAggregationValueL1).HasColumnName("power_var_qn_aggregation_value_l1");

                entity.Property(e => e.PowerVarQnAggregationValueL2).HasColumnName("power_var_qn_aggregation_value_l2");

                entity.Property(e => e.PowerVarQnAggregationValueL3).HasColumnName("power_var_qn_aggregation_value_l3");

                entity.Property(e => e.PowerVarQnAggregationValueSum).HasColumnName("power_var_qn_aggregation_value_sum");

                entity.Property(e => e.PowerWAggregationGreatestL1).HasColumnName("power_w_aggregation_greatest_l1");

                entity.Property(e => e.PowerWAggregationGreatestL2).HasColumnName("power_w_aggregation_greatest_l2");

                entity.Property(e => e.PowerWAggregationGreatestL3).HasColumnName("power_w_aggregation_greatest_l3");

                entity.Property(e => e.PowerWAggregationGreatestSum).HasColumnName("power_w_aggregation_greatest_sum");

                entity.Property(e => e.PowerWAggregationLowestL1).HasColumnName("power_w_aggregation_lowest_l1");

                entity.Property(e => e.PowerWAggregationLowestL2).HasColumnName("power_w_aggregation_lowest_l2");

                entity.Property(e => e.PowerWAggregationLowestL3).HasColumnName("power_w_aggregation_lowest_l3");

                entity.Property(e => e.PowerWAggregationLowestSum).HasColumnName("power_w_aggregation_lowest_sum");

                entity.Property(e => e.PowerWAggregationValueL1).HasColumnName("power_w_aggregation_value_l1");

                entity.Property(e => e.PowerWAggregationValueL2).HasColumnName("power_w_aggregation_value_l2");

                entity.Property(e => e.PowerWAggregationValueL3).HasColumnName("power_w_aggregation_value_l3");

                entity.Property(e => e.PowerWAggregationValueSum).HasColumnName("power_w_aggregation_value_sum");

                entity.Property(e => e.ThdIAggregationGreatestL1).HasColumnName("thd_i_aggregation_greatest_l1");

                entity.Property(e => e.ThdIAggregationGreatestL2).HasColumnName("thd_i_aggregation_greatest_l2");

                entity.Property(e => e.ThdIAggregationGreatestL3).HasColumnName("thd_i_aggregation_greatest_l3");

                entity.Property(e => e.ThdIAggregationLowestL1).HasColumnName("thd_i_aggregation_lowest_l1");

                entity.Property(e => e.ThdIAggregationLowestL2).HasColumnName("thd_i_aggregation_lowest_l2");

                entity.Property(e => e.ThdIAggregationLowestL3).HasColumnName("thd_i_aggregation_lowest_l3");

                entity.Property(e => e.ThdIAggregationValueL1).HasColumnName("thd_i_aggregation_value_l1");

                entity.Property(e => e.ThdIAggregationValueL2).HasColumnName("thd_i_aggregation_value_l2");

                entity.Property(e => e.ThdIAggregationValueL3).HasColumnName("thd_i_aggregation_value_l3");

                entity.Property(e => e.ThdVLnAggregationGreatestL1n).HasColumnName("thd_v_ln_aggregation_greatest_l1n");

                entity.Property(e => e.ThdVLnAggregationGreatestL2n).HasColumnName("thd_v_ln_aggregation_greatest_l2n");

                entity.Property(e => e.ThdVLnAggregationGreatestL3n).HasColumnName("thd_v_ln_aggregation_greatest_l3n");

                entity.Property(e => e.ThdVLnAggregationLowestL1n).HasColumnName("thd_v_ln_aggregation_lowest_l1n");

                entity.Property(e => e.ThdVLnAggregationLowestL2n).HasColumnName("thd_v_ln_aggregation_lowest_l2n");

                entity.Property(e => e.ThdVLnAggregationLowestL3n).HasColumnName("thd_v_ln_aggregation_lowest_l3n");

                entity.Property(e => e.ThdVLnAggregationValueL1n).HasColumnName("thd_v_ln_aggregation_value_l1n");

                entity.Property(e => e.ThdVLnAggregationValueL2n).HasColumnName("thd_v_ln_aggregation_value_l2n");

                entity.Property(e => e.ThdVLnAggregationValueL3n).HasColumnName("thd_v_ln_aggregation_value_l3n");

                entity.Property(e => e.ThreephasesystemAggregationGreatestIAmplbal).HasColumnName("threephasesystem_aggregation_greatest_i_amplbal_");

                entity.Property(e => e.ThreephasesystemAggregationGreatestVAmplbal).HasColumnName("threephasesystem_aggregation_greatest_v_amplbal_");

                entity.Property(e => e.ThreephasesystemAggregationIAmplbal).HasColumnName("threephasesystem_aggregation_i_amplbal_");

                entity.Property(e => e.ThreephasesystemAggregationLowestIAmplbal).HasColumnName("threephasesystem_aggregation_lowest_i_amplbal_");

                entity.Property(e => e.ThreephasesystemAggregationLowestVAmplbal).HasColumnName("threephasesystem_aggregation_lowest_v_amplbal_");

                entity.Property(e => e.ThreephasesystemAggregationVAmplbal).HasColumnName("threephasesystem_aggregation_v_amplbal_");

                entity.Property(e => e.VLlAggregationGreatestAvg).HasColumnName("v_ll_aggregation_greatest_avg");

                entity.Property(e => e.VLlAggregationGreatestL1l2).HasColumnName("v_ll_aggregation_greatest_l1l2");

                entity.Property(e => e.VLlAggregationGreatestL2l3).HasColumnName("v_ll_aggregation_greatest_l2l3");

                entity.Property(e => e.VLlAggregationGreatestL3l1).HasColumnName("v_ll_aggregation_greatest_l3l1");

                entity.Property(e => e.VLlAggregationLowestAvg).HasColumnName("v_ll_aggregation_lowest_avg");

                entity.Property(e => e.VLlAggregationLowestL1l2).HasColumnName("v_ll_aggregation_lowest_l1l2");

                entity.Property(e => e.VLlAggregationLowestL2l3).HasColumnName("v_ll_aggregation_lowest_l2l3");

                entity.Property(e => e.VLlAggregationLowestL3l1).HasColumnName("v_ll_aggregation_lowest_l3l1");

                entity.Property(e => e.VLlAggregationValueAvg).HasColumnName("v_ll_aggregation_value_avg");

                entity.Property(e => e.VLlAggregationValueL1l2).HasColumnName("v_ll_aggregation_value_l1l2");

                entity.Property(e => e.VLlAggregationValueL2l3).HasColumnName("v_ll_aggregation_value_l2l3");

                entity.Property(e => e.VLlAggregationValueL3l1).HasColumnName("v_ll_aggregation_value_l3l1");

                entity.Property(e => e.VLnAggregationGreatestAvg).HasColumnName("v_ln_aggregation_greatest_avg");

                entity.Property(e => e.VLnAggregationGreatestL1n).HasColumnName("v_ln_aggregation_greatest_l1n");

                entity.Property(e => e.VLnAggregationGreatestL2n).HasColumnName("v_ln_aggregation_greatest_l2n");

                entity.Property(e => e.VLnAggregationGreatestL3n).HasColumnName("v_ln_aggregation_greatest_l3n");

                entity.Property(e => e.VLnAggregationLowestAvg).HasColumnName("v_ln_aggregation_lowest_avg");

                entity.Property(e => e.VLnAggregationLowestL1n).HasColumnName("v_ln_aggregation_lowest_l1n");

                entity.Property(e => e.VLnAggregationLowestL2n).HasColumnName("v_ln_aggregation_lowest_l2n");

                entity.Property(e => e.VLnAggregationLowestL3n).HasColumnName("v_ln_aggregation_lowest_l3n");

                entity.Property(e => e.VLnAggregationValueAvg).HasColumnName("v_ln_aggregation_value_avg");

                entity.Property(e => e.VLnAggregationValueL1n).HasColumnName("v_ln_aggregation_value_l1n");

                entity.Property(e => e.VLnAggregationValueL2n).HasColumnName("v_ln_aggregation_value_l2n");

                entity.Property(e => e.VLnAggregationValueL3n).HasColumnName("v_ln_aggregation_value_l3n");
            });

            modelBuilder.Entity<ArchiveDataFormat5>(entity =>
            {
                entity.HasKey(e => new { e.ObjectId, e.TimestampInS, e.Archive });

                entity.ToTable("ArchiveDataFormat5");

                entity.Property(e => e.CounterUniversal).HasColumnName("counter_universal");

                entity.Property(e => e.EnergyVarhExportOffpeaktariffSum)
                    .HasColumnName("energy_varh_export_offpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyVarhExportOnpeaktariffSum)
                    .HasColumnName("energy_varh_export_onpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyVarhImportOffpeaktariffSum)
                    .HasColumnName("energy_varh_import_offpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyVarhImportOnpeaktariffSum)
                    .HasColumnName("energy_varh_import_onpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyWhExportOffpeaktariffSum)
                    .HasColumnName("energy_wh_export_offpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyWhExportOnpeaktariffSum).HasColumnName("energy_wh_export_onpeaktariff_sum");

                entity.Property(e => e.EnergyWhImportOffpeaktariffSum)
                    .HasColumnName("energy_wh_import_offpeaktariff_sum")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.EnergyWhImportOnpeaktariffSum).HasColumnName("energy_wh_import_onpeaktariff_sum");

                entity.Property(e => e.FrequencyAggregationGreatestCommon).HasColumnName("frequency_aggregation_greatest_common");

                entity.Property(e => e.FrequencyAggregationValueCommon).HasColumnName("frequency_aggregation_value_common");

                entity.Property(e => e.FrequencyAggregationValueLowestCommon).HasColumnName("frequency_aggregation_value_lowest_common");

                entity.Property(e => e.IAggregationGreatestAvg).HasColumnName("i_aggregation_greatest_avg");

                entity.Property(e => e.IAggregationGreatestIn).HasColumnName("i_aggregation_greatest_in");

                entity.Property(e => e.IAggregationGreatestL1).HasColumnName("i_aggregation_greatest_l1");

                entity.Property(e => e.IAggregationGreatestL2).HasColumnName("i_aggregation_greatest_l2");

                entity.Property(e => e.IAggregationGreatestL3).HasColumnName("i_aggregation_greatest_l3");

                entity.Property(e => e.IAggregationLowestAvg).HasColumnName("i_aggregation_lowest_avg");

                entity.Property(e => e.IAggregationLowestIn).HasColumnName("i_aggregation_lowest_in");

                entity.Property(e => e.IAggregationLowestL1).HasColumnName("i_aggregation_lowest_l1");

                entity.Property(e => e.IAggregationLowestL2).HasColumnName("i_aggregation_lowest_l2");

                entity.Property(e => e.IAggregationLowestL3).HasColumnName("i_aggregation_lowest_l3");

                entity.Property(e => e.IAggregationValueAvg).HasColumnName("i_aggregation_value_avg");

                entity.Property(e => e.IAggregationValueIn).HasColumnName("i_aggregation_value_in");

                entity.Property(e => e.IAggregationValueL1).HasColumnName("i_aggregation_value_l1");

                entity.Property(e => e.IAggregationValueL2).HasColumnName("i_aggregation_value_l2");

                entity.Property(e => e.IAggregationValueL3).HasColumnName("i_aggregation_value_l3");

                entity.Property(e => e.PowerFactorAggregationGreatestL1).HasColumnName("power_factor_aggregation_greatest_l1");

                entity.Property(e => e.PowerFactorAggregationGreatestL2).HasColumnName("power_factor_aggregation_greatest_l2");

                entity.Property(e => e.PowerFactorAggregationGreatestL3).HasColumnName("power_factor_aggregation_greatest_l3");

                entity.Property(e => e.PowerFactorAggregationGreatestSum).HasColumnName("power_factor_aggregation_greatest_sum");

                entity.Property(e => e.PowerFactorAggregationLowestL1).HasColumnName("power_factor_aggregation_lowest_l1");

                entity.Property(e => e.PowerFactorAggregationLowestL2).HasColumnName("power_factor_aggregation_lowest_l2");

                entity.Property(e => e.PowerFactorAggregationLowestL3).HasColumnName("power_factor_aggregation_lowest_l3");

                entity.Property(e => e.PowerFactorAggregationLowestSum).HasColumnName("power_factor_aggregation_lowest_sum");

                entity.Property(e => e.PowerFactorAggregationValueL1).HasColumnName("power_factor_aggregation_value_l1");

                entity.Property(e => e.PowerFactorAggregationValueL2).HasColumnName("power_factor_aggregation_value_l2");

                entity.Property(e => e.PowerFactorAggregationValueL3).HasColumnName("power_factor_aggregation_value_l3");

                entity.Property(e => e.PowerFactorAggregationValueSum).HasColumnName("power_factor_aggregation_value_sum");

                entity.Property(e => e.PowerVaAggregationGreatestL1).HasColumnName("power_va_aggregation_greatest_l1");

                entity.Property(e => e.PowerVaAggregationGreatestL2).HasColumnName("power_va_aggregation_greatest_l2");

                entity.Property(e => e.PowerVaAggregationGreatestL3).HasColumnName("power_va_aggregation_greatest_l3");

                entity.Property(e => e.PowerVaAggregationGreatestSum).HasColumnName("power_va_aggregation_greatest_sum");

                entity.Property(e => e.PowerVaAggregationLowestL1).HasColumnName("power_va_aggregation_lowest_l1");

                entity.Property(e => e.PowerVaAggregationLowestL2).HasColumnName("power_va_aggregation_lowest_l2");

                entity.Property(e => e.PowerVaAggregationLowestL3).HasColumnName("power_va_aggregation_lowest_l3");

                entity.Property(e => e.PowerVaAggregationLowestSum).HasColumnName("power_va_aggregation_lowest_sum");

                entity.Property(e => e.PowerVaAggregationValueL1).HasColumnName("power_va_aggregation_value_l1");

                entity.Property(e => e.PowerVaAggregationValueL2).HasColumnName("power_va_aggregation_value_l2");

                entity.Property(e => e.PowerVaAggregationValueL3).HasColumnName("power_va_aggregation_value_l3");

                entity.Property(e => e.PowerVaAggregationValueSum).HasColumnName("power_va_aggregation_value_sum");

                entity.Property(e => e.PowerVarQnAggregationGreatestL1).HasColumnName("power_var_qn_aggregation_greatest_l1");

                entity.Property(e => e.PowerVarQnAggregationGreatestL2).HasColumnName("power_var_qn_aggregation_greatest_l2");

                entity.Property(e => e.PowerVarQnAggregationGreatestL3).HasColumnName("power_var_qn_aggregation_greatest_l3");

                entity.Property(e => e.PowerVarQnAggregationGreatestSum).HasColumnName("power_var_qn_aggregation_greatest_sum");

                entity.Property(e => e.PowerVarQnAggregationLowestL1).HasColumnName("power_var_qn_aggregation_lowest_l1");

                entity.Property(e => e.PowerVarQnAggregationLowestL2).HasColumnName("power_var_qn_aggregation_lowest_l2");

                entity.Property(e => e.PowerVarQnAggregationLowestL3).HasColumnName("power_var_qn_aggregation_lowest_l3");

                entity.Property(e => e.PowerVarQnAggregationLowestSum).HasColumnName("power_var_qn_aggregation_lowest_sum");

                entity.Property(e => e.PowerVarQnAggregationValueL1).HasColumnName("power_var_qn_aggregation_value_l1");

                entity.Property(e => e.PowerVarQnAggregationValueL2).HasColumnName("power_var_qn_aggregation_value_l2");

                entity.Property(e => e.PowerVarQnAggregationValueL3).HasColumnName("power_var_qn_aggregation_value_l3");

                entity.Property(e => e.PowerVarQnAggregationValueSum).HasColumnName("power_var_qn_aggregation_value_sum");

                entity.Property(e => e.PowerWAggregationGreatestL1).HasColumnName("power_w_aggregation_greatest_l1");

                entity.Property(e => e.PowerWAggregationGreatestL2).HasColumnName("power_w_aggregation_greatest_l2");

                entity.Property(e => e.PowerWAggregationGreatestL3).HasColumnName("power_w_aggregation_greatest_l3");

                entity.Property(e => e.PowerWAggregationGreatestSum).HasColumnName("power_w_aggregation_greatest_sum");

                entity.Property(e => e.PowerWAggregationLowestL1).HasColumnName("power_w_aggregation_lowest_l1");

                entity.Property(e => e.PowerWAggregationLowestL2).HasColumnName("power_w_aggregation_lowest_l2");

                entity.Property(e => e.PowerWAggregationLowestL3).HasColumnName("power_w_aggregation_lowest_l3");

                entity.Property(e => e.PowerWAggregationLowestSum).HasColumnName("power_w_aggregation_lowest_sum");

                entity.Property(e => e.PowerWAggregationValueL1).HasColumnName("power_w_aggregation_value_l1");

                entity.Property(e => e.PowerWAggregationValueL2).HasColumnName("power_w_aggregation_value_l2");

                entity.Property(e => e.PowerWAggregationValueL3).HasColumnName("power_w_aggregation_value_l3");

                entity.Property(e => e.PowerWAggregationValueSum).HasColumnName("power_w_aggregation_value_sum");

                entity.Property(e => e.ThdIAggregationGreatestL1).HasColumnName("thd_i_aggregation_greatest_l1");

                entity.Property(e => e.ThdIAggregationGreatestL2).HasColumnName("thd_i_aggregation_greatest_l2");

                entity.Property(e => e.ThdIAggregationGreatestL3).HasColumnName("thd_i_aggregation_greatest_l3");

                entity.Property(e => e.ThdIAggregationLowestL1).HasColumnName("thd_i_aggregation_lowest_l1");

                entity.Property(e => e.ThdIAggregationLowestL2).HasColumnName("thd_i_aggregation_lowest_l2");

                entity.Property(e => e.ThdIAggregationLowestL3).HasColumnName("thd_i_aggregation_lowest_l3");

                entity.Property(e => e.ThdIAggregationValueL1).HasColumnName("thd_i_aggregation_value_l1");

                entity.Property(e => e.ThdIAggregationValueL2).HasColumnName("thd_i_aggregation_value_l2");

                entity.Property(e => e.ThdIAggregationValueL3).HasColumnName("thd_i_aggregation_value_l3");

                entity.Property(e => e.ThdVLnAggregationGreatestL1n).HasColumnName("thd_v_ln_aggregation_greatest_l1n");

                entity.Property(e => e.ThdVLnAggregationGreatestL2n).HasColumnName("thd_v_ln_aggregation_greatest_l2n");

                entity.Property(e => e.ThdVLnAggregationGreatestL3n).HasColumnName("thd_v_ln_aggregation_greatest_l3n");

                entity.Property(e => e.ThdVLnAggregationLowestL1n).HasColumnName("thd_v_ln_aggregation_lowest_l1n");

                entity.Property(e => e.ThdVLnAggregationLowestL2n).HasColumnName("thd_v_ln_aggregation_lowest_l2n");

                entity.Property(e => e.ThdVLnAggregationLowestL3n).HasColumnName("thd_v_ln_aggregation_lowest_l3n");

                entity.Property(e => e.ThdVLnAggregationValueL1n).HasColumnName("thd_v_ln_aggregation_value_l1n");

                entity.Property(e => e.ThdVLnAggregationValueL2n).HasColumnName("thd_v_ln_aggregation_value_l2n");

                entity.Property(e => e.ThdVLnAggregationValueL3n).HasColumnName("thd_v_ln_aggregation_value_l3n");

                entity.Property(e => e.ThreephasesystemAggregationGreatestIAmplbal).HasColumnName("threephasesystem_aggregation_greatest_i_amplbal_");

                entity.Property(e => e.ThreephasesystemAggregationGreatestVAmplbal).HasColumnName("threephasesystem_aggregation_greatest_v_amplbal_");

                entity.Property(e => e.ThreephasesystemAggregationIAmplbal).HasColumnName("threephasesystem_aggregation_i_amplbal_");

                entity.Property(e => e.ThreephasesystemAggregationLowestIAmplbal).HasColumnName("threephasesystem_aggregation_lowest_i_amplbal_");

                entity.Property(e => e.ThreephasesystemAggregationLowestVAmplbal).HasColumnName("threephasesystem_aggregation_lowest_v_amplbal_");

                entity.Property(e => e.ThreephasesystemAggregationVAmplbal).HasColumnName("threephasesystem_aggregation_v_amplbal_");

                entity.Property(e => e.VLlAggregationGreatestAvg).HasColumnName("v_ll_aggregation_greatest_avg");

                entity.Property(e => e.VLlAggregationGreatestL1l2).HasColumnName("v_ll_aggregation_greatest_l1l2");

                entity.Property(e => e.VLlAggregationGreatestL2l3).HasColumnName("v_ll_aggregation_greatest_l2l3");

                entity.Property(e => e.VLlAggregationGreatestL3l1).HasColumnName("v_ll_aggregation_greatest_l3l1");

                entity.Property(e => e.VLlAggregationLowestAvg).HasColumnName("v_ll_aggregation_lowest_avg");

                entity.Property(e => e.VLlAggregationLowestL1l2).HasColumnName("v_ll_aggregation_lowest_l1l2");

                entity.Property(e => e.VLlAggregationLowestL2l3).HasColumnName("v_ll_aggregation_lowest_l2l3");

                entity.Property(e => e.VLlAggregationLowestL3l1).HasColumnName("v_ll_aggregation_lowest_l3l1");

                entity.Property(e => e.VLlAggregationValueAvg).HasColumnName("v_ll_aggregation_value_avg");

                entity.Property(e => e.VLlAggregationValueL1l2).HasColumnName("v_ll_aggregation_value_l1l2");

                entity.Property(e => e.VLlAggregationValueL2l3).HasColumnName("v_ll_aggregation_value_l2l3");

                entity.Property(e => e.VLlAggregationValueL3l1).HasColumnName("v_ll_aggregation_value_l3l1");

                entity.Property(e => e.VLnAggregationGreatestAvg).HasColumnName("v_ln_aggregation_greatest_avg");

                entity.Property(e => e.VLnAggregationGreatestL1n).HasColumnName("v_ln_aggregation_greatest_l1n");

                entity.Property(e => e.VLnAggregationGreatestL2n).HasColumnName("v_ln_aggregation_greatest_l2n");

                entity.Property(e => e.VLnAggregationGreatestL3n).HasColumnName("v_ln_aggregation_greatest_l3n");

                entity.Property(e => e.VLnAggregationLowestAvg).HasColumnName("v_ln_aggregation_lowest_avg");

                entity.Property(e => e.VLnAggregationLowestL1n).HasColumnName("v_ln_aggregation_lowest_l1n");

                entity.Property(e => e.VLnAggregationLowestL2n).HasColumnName("v_ln_aggregation_lowest_l2n");

                entity.Property(e => e.VLnAggregationLowestL3n).HasColumnName("v_ln_aggregation_lowest_l3n");

                entity.Property(e => e.VLnAggregationValueAvg).HasColumnName("v_ln_aggregation_value_avg");

                entity.Property(e => e.VLnAggregationValueL1n).HasColumnName("v_ln_aggregation_value_l1n");

                entity.Property(e => e.VLnAggregationValueL2n).HasColumnName("v_ln_aggregation_value_l2n");

                entity.Property(e => e.VLnAggregationValueL3n).HasColumnName("v_ln_aggregation_value_l3n");
            });

            modelBuilder.Entity<ArchiveDataFormat6>(entity =>
            {
                entity.HasKey(e => new { e.ObjectId, e.Archive, e.TimestampInS });

                entity.ToTable("ArchiveDataFormat6");

                entity.Property(e => e.EnergyKvarhExportSum).HasColumnName("energy_kvarh_export_sum");

                entity.Property(e => e.EnergyKvarhExportTariff1Value).HasColumnName("energy_kvarh_export_tariff1_value");

                entity.Property(e => e.EnergyKvarhExportTariff2Value).HasColumnName("energy_kvarh_export_tariff2_value");

                entity.Property(e => e.EnergyKvarhImportSum).HasColumnName("energy_kvarh_import_sum");

                entity.Property(e => e.EnergyKvarhImportTariff1Value).HasColumnName("energy_kvarh_import_tariff1_value");

                entity.Property(e => e.EnergyKvarhImportTariff2Value).HasColumnName("energy_kvarh_import_tariff2_value");

                entity.Property(e => e.EnergyKvarhSum).HasColumnName("energy_kvarh_sum");

                entity.Property(e => e.EnergyKwhExportSum).HasColumnName("energy_kwh_export_sum");

                entity.Property(e => e.EnergyKwhExportTariff1Value).HasColumnName("energy_kwh_export_tariff1_value");

                entity.Property(e => e.EnergyKwhExportTariff2Value).HasColumnName("energy_kwh_export_tariff2_value");

                entity.Property(e => e.EnergyKwhImportSum).HasColumnName("energy_kwh_import_sum");

                entity.Property(e => e.EnergyKwhImportTariff1Value).HasColumnName("energy_kwh_import_tariff1_value");

                entity.Property(e => e.EnergyKwhImportTariff2Value).HasColumnName("energy_kwh_import_tariff2_value");

                entity.Property(e => e.EnergyKwhSum).HasColumnName("energy_kwh_sum");
            });

            modelBuilder.Entity<ArchiveDataFormat7>(entity =>
            {
                entity.HasKey(e => new { e.ObjectId, e.Archive, e.TimestampInS });

                entity.ToTable("ArchiveDataFormat7");

                entity.Property(e => e.EtuEaL1Export).HasColumnName("etu_ea_l1_export");

                entity.Property(e => e.EtuEaL1Import).HasColumnName("etu_ea_l1_import");

                entity.Property(e => e.EtuEaL1Total).HasColumnName("etu_ea_l1_total");

                entity.Property(e => e.EtuEaL2Export).HasColumnName("etu_ea_l2_export");

                entity.Property(e => e.EtuEaL2Import).HasColumnName("etu_ea_l2_import");

                entity.Property(e => e.EtuEaL2Total).HasColumnName("etu_ea_l2_total");

                entity.Property(e => e.EtuEaL3Export).HasColumnName("etu_ea_l3_export");

                entity.Property(e => e.EtuEaL3Import).HasColumnName("etu_ea_l3_import");

                entity.Property(e => e.EtuEaL3Total).HasColumnName("etu_ea_l3_total");

                entity.Property(e => e.EtuEaSumExport).HasColumnName("etu_ea_sum_export");

                entity.Property(e => e.EtuEaSumImport).HasColumnName("etu_ea_sum_import");

                entity.Property(e => e.EtuEaSumTotal).HasColumnName("etu_ea_sum_total");

                entity.Property(e => e.EtuEapL1).HasColumnName("etu_eap_l1");

                entity.Property(e => e.EtuEapL2).HasColumnName("etu_eap_l2");

                entity.Property(e => e.EtuEapL3).HasColumnName("etu_eap_l3");

                entity.Property(e => e.EtuEapSum).HasColumnName("etu_eap_sum");

                entity.Property(e => e.EtuErL1Export).HasColumnName("etu_er_l1_export");

                entity.Property(e => e.EtuErL1Import).HasColumnName("etu_er_l1_import");

                entity.Property(e => e.EtuErL1Total).HasColumnName("etu_er_l1_total");

                entity.Property(e => e.EtuErL2Export).HasColumnName("etu_er_l2_export");

                entity.Property(e => e.EtuErL2Import).HasColumnName("etu_er_l2_import");

                entity.Property(e => e.EtuErL2Total).HasColumnName("etu_er_l2_total");

                entity.Property(e => e.EtuErL3Export).HasColumnName("etu_er_l3_export");

                entity.Property(e => e.EtuErL3Import).HasColumnName("etu_er_l3_import");

                entity.Property(e => e.EtuErL3Total).HasColumnName("etu_er_l3_total");

                entity.Property(e => e.EtuErSumExport).HasColumnName("etu_er_sum_export");

                entity.Property(e => e.EtuErSumImport).HasColumnName("etu_er_sum_import");

                entity.Property(e => e.EtuErSumTotal).HasColumnName("etu_er_sum_total");
            });

            modelBuilder.Entity<ArchiveDataFormatBasicEnergy>(entity =>
            {
                entity.HasKey(e => new { e.ObjectId, e.TimestampInS, e.Archive });

                entity.ToTable("ArchiveDataFormatBasicEnergy");

                entity.Property(e => e.ActiveEnergyExportTariff1).HasColumnName("active_energy_export_tariff1");

                entity.Property(e => e.ActiveEnergyExportTariff2)
                    .HasColumnName("active_energy_export_tariff2")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.ActiveEnergyImportTariff1).HasColumnName("active_energy_import_tariff1");

                entity.Property(e => e.ActiveEnergyImportTariff2)
                    .HasColumnName("active_energy_import_tariff2")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.CounterUniversal1).HasColumnName("counter_universal1");

                entity.Property(e => e.CounterUniversal2).HasColumnName("counter_universal2");

                entity.Property(e => e.ReactiveEnergyExportTariff1)
                    .HasColumnName("reactive_energy_export_tariff1")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.ReactiveEnergyExportTariff2)
                    .HasColumnName("reactive_energy_export_tariff2")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.ReactiveEnergyImportTariff1)
                    .HasColumnName("reactive_energy_import_tariff1")
                    .HasDefaultValueSql("0.0");

                entity.Property(e => e.ReactiveEnergyImportTariff2)
                    .HasColumnName("reactive_energy_import_tariff2")
                    .HasDefaultValueSql("0.0");
            });

            modelBuilder.Entity<ArchiveDataFormatCustom>(entity =>
            {
                entity.HasKey(e => new { e.ObjectId, e.Archive, e.TimestampInS });

                entity.ToTable("ArchiveDataFormatCustom");

                entity.Property(e => e.Dp1).HasColumnName("dp1");

                entity.Property(e => e.Dp10).HasColumnName("dp10");

                entity.Property(e => e.Dp100).HasColumnName("dp100");

                entity.Property(e => e.Dp11).HasColumnName("dp11");

                entity.Property(e => e.Dp12).HasColumnName("dp12");

                entity.Property(e => e.Dp13).HasColumnName("dp13");

                entity.Property(e => e.Dp14).HasColumnName("dp14");

                entity.Property(e => e.Dp15).HasColumnName("dp15");

                entity.Property(e => e.Dp16).HasColumnName("dp16");

                entity.Property(e => e.Dp17).HasColumnName("dp17");

                entity.Property(e => e.Dp18).HasColumnName("dp18");

                entity.Property(e => e.Dp19).HasColumnName("dp19");

                entity.Property(e => e.Dp2).HasColumnName("dp2");

                entity.Property(e => e.Dp20).HasColumnName("dp20");

                entity.Property(e => e.Dp21).HasColumnName("dp21");

                entity.Property(e => e.Dp22).HasColumnName("dp22");

                entity.Property(e => e.Dp23).HasColumnName("dp23");

                entity.Property(e => e.Dp24).HasColumnName("dp24");

                entity.Property(e => e.Dp25).HasColumnName("dp25");

                entity.Property(e => e.Dp26).HasColumnName("dp26");

                entity.Property(e => e.Dp27).HasColumnName("dp27");

                entity.Property(e => e.Dp28).HasColumnName("dp28");

                entity.Property(e => e.Dp29).HasColumnName("dp29");

                entity.Property(e => e.Dp3).HasColumnName("dp3");

                entity.Property(e => e.Dp30).HasColumnName("dp30");

                entity.Property(e => e.Dp31).HasColumnName("dp31");

                entity.Property(e => e.Dp32).HasColumnName("dp32");

                entity.Property(e => e.Dp33).HasColumnName("dp33");

                entity.Property(e => e.Dp34).HasColumnName("dp34");

                entity.Property(e => e.Dp35).HasColumnName("dp35");

                entity.Property(e => e.Dp36).HasColumnName("dp36");

                entity.Property(e => e.Dp37).HasColumnName("dp37");

                entity.Property(e => e.Dp38).HasColumnName("dp38");

                entity.Property(e => e.Dp39).HasColumnName("dp39");

                entity.Property(e => e.Dp4).HasColumnName("dp4");

                entity.Property(e => e.Dp40).HasColumnName("dp40");

                entity.Property(e => e.Dp41).HasColumnName("dp41");

                entity.Property(e => e.Dp42).HasColumnName("dp42");

                entity.Property(e => e.Dp43).HasColumnName("dp43");

                entity.Property(e => e.Dp44).HasColumnName("dp44");

                entity.Property(e => e.Dp45).HasColumnName("dp45");

                entity.Property(e => e.Dp46).HasColumnName("dp46");

                entity.Property(e => e.Dp47).HasColumnName("dp47");

                entity.Property(e => e.Dp48).HasColumnName("dp48");

                entity.Property(e => e.Dp49).HasColumnName("dp49");

                entity.Property(e => e.Dp5).HasColumnName("dp5");

                entity.Property(e => e.Dp50).HasColumnName("dp50");

                entity.Property(e => e.Dp51).HasColumnName("dp51");

                entity.Property(e => e.Dp52).HasColumnName("dp52");

                entity.Property(e => e.Dp53).HasColumnName("dp53");

                entity.Property(e => e.Dp54).HasColumnName("dp54");

                entity.Property(e => e.Dp55).HasColumnName("dp55");

                entity.Property(e => e.Dp56).HasColumnName("dp56");

                entity.Property(e => e.Dp57).HasColumnName("dp57");

                entity.Property(e => e.Dp58).HasColumnName("dp58");

                entity.Property(e => e.Dp59).HasColumnName("dp59");

                entity.Property(e => e.Dp6).HasColumnName("dp6");

                entity.Property(e => e.Dp60).HasColumnName("dp60");

                entity.Property(e => e.Dp61).HasColumnName("dp61");

                entity.Property(e => e.Dp62).HasColumnName("dp62");

                entity.Property(e => e.Dp63).HasColumnName("dp63");

                entity.Property(e => e.Dp64).HasColumnName("dp64");

                entity.Property(e => e.Dp65).HasColumnName("dp65");

                entity.Property(e => e.Dp66).HasColumnName("dp66");

                entity.Property(e => e.Dp67).HasColumnName("dp67");

                entity.Property(e => e.Dp68).HasColumnName("dp68");

                entity.Property(e => e.Dp69).HasColumnName("dp69");

                entity.Property(e => e.Dp7).HasColumnName("dp7");

                entity.Property(e => e.Dp70).HasColumnName("dp70");

                entity.Property(e => e.Dp71).HasColumnName("dp71");

                entity.Property(e => e.Dp72).HasColumnName("dp72");

                entity.Property(e => e.Dp73).HasColumnName("dp73");

                entity.Property(e => e.Dp74).HasColumnName("dp74");

                entity.Property(e => e.Dp75).HasColumnName("dp75");

                entity.Property(e => e.Dp76).HasColumnName("dp76");

                entity.Property(e => e.Dp77).HasColumnName("dp77");

                entity.Property(e => e.Dp78).HasColumnName("dp78");

                entity.Property(e => e.Dp79).HasColumnName("dp79");

                entity.Property(e => e.Dp8).HasColumnName("dp8");

                entity.Property(e => e.Dp80).HasColumnName("dp80");

                entity.Property(e => e.Dp81).HasColumnName("dp81");

                entity.Property(e => e.Dp82).HasColumnName("dp82");

                entity.Property(e => e.Dp83).HasColumnName("dp83");

                entity.Property(e => e.Dp84).HasColumnName("dp84");

                entity.Property(e => e.Dp85).HasColumnName("dp85");

                entity.Property(e => e.Dp86).HasColumnName("dp86");

                entity.Property(e => e.Dp87).HasColumnName("dp87");

                entity.Property(e => e.Dp88).HasColumnName("dp88");

                entity.Property(e => e.Dp89).HasColumnName("dp89");

                entity.Property(e => e.Dp9).HasColumnName("dp9");

                entity.Property(e => e.Dp90).HasColumnName("dp90");

                entity.Property(e => e.Dp91).HasColumnName("dp91");

                entity.Property(e => e.Dp92).HasColumnName("dp92");

                entity.Property(e => e.Dp93).HasColumnName("dp93");

                entity.Property(e => e.Dp94).HasColumnName("dp94");

                entity.Property(e => e.Dp95).HasColumnName("dp95");

                entity.Property(e => e.Dp96).HasColumnName("dp96");

                entity.Property(e => e.Dp97).HasColumnName("dp97");

                entity.Property(e => e.Dp98).HasColumnName("dp98");

                entity.Property(e => e.Dp99).HasColumnName("dp99");
            });

            modelBuilder.Entity<ArchiveDataFormatLookUp>(entity =>
            {
                entity.HasKey(e => e.ObjectId);

                entity.ToTable("ArchiveDataFormatLookUp");

                entity.Property(e => e.Dp1).HasColumnName("dp1");

                entity.Property(e => e.Dp10).HasColumnName("dp10");

                entity.Property(e => e.Dp100).HasColumnName("dp100");

                entity.Property(e => e.Dp11).HasColumnName("dp11");

                entity.Property(e => e.Dp12).HasColumnName("dp12");

                entity.Property(e => e.Dp13).HasColumnName("dp13");

                entity.Property(e => e.Dp14).HasColumnName("dp14");

                entity.Property(e => e.Dp15).HasColumnName("dp15");

                entity.Property(e => e.Dp16).HasColumnName("dp16");

                entity.Property(e => e.Dp17).HasColumnName("dp17");

                entity.Property(e => e.Dp18).HasColumnName("dp18");

                entity.Property(e => e.Dp19).HasColumnName("dp19");

                entity.Property(e => e.Dp2).HasColumnName("dp2");

                entity.Property(e => e.Dp20).HasColumnName("dp20");

                entity.Property(e => e.Dp21).HasColumnName("dp21");

                entity.Property(e => e.Dp22).HasColumnName("dp22");

                entity.Property(e => e.Dp23).HasColumnName("dp23");

                entity.Property(e => e.Dp24).HasColumnName("dp24");

                entity.Property(e => e.Dp25).HasColumnName("dp25");

                entity.Property(e => e.Dp26).HasColumnName("dp26");

                entity.Property(e => e.Dp27).HasColumnName("dp27");

                entity.Property(e => e.Dp28).HasColumnName("dp28");

                entity.Property(e => e.Dp29).HasColumnName("dp29");

                entity.Property(e => e.Dp3).HasColumnName("dp3");

                entity.Property(e => e.Dp30).HasColumnName("dp30");

                entity.Property(e => e.Dp31).HasColumnName("dp31");

                entity.Property(e => e.Dp32).HasColumnName("dp32");

                entity.Property(e => e.Dp33).HasColumnName("dp33");

                entity.Property(e => e.Dp34).HasColumnName("dp34");

                entity.Property(e => e.Dp35).HasColumnName("dp35");

                entity.Property(e => e.Dp36).HasColumnName("dp36");

                entity.Property(e => e.Dp37).HasColumnName("dp37");

                entity.Property(e => e.Dp38).HasColumnName("dp38");

                entity.Property(e => e.Dp39).HasColumnName("dp39");

                entity.Property(e => e.Dp4).HasColumnName("dp4");

                entity.Property(e => e.Dp40).HasColumnName("dp40");

                entity.Property(e => e.Dp41).HasColumnName("dp41");

                entity.Property(e => e.Dp42).HasColumnName("dp42");

                entity.Property(e => e.Dp43).HasColumnName("dp43");

                entity.Property(e => e.Dp44).HasColumnName("dp44");

                entity.Property(e => e.Dp45).HasColumnName("dp45");

                entity.Property(e => e.Dp46).HasColumnName("dp46");

                entity.Property(e => e.Dp47).HasColumnName("dp47");

                entity.Property(e => e.Dp48).HasColumnName("dp48");

                entity.Property(e => e.Dp49).HasColumnName("dp49");

                entity.Property(e => e.Dp5).HasColumnName("dp5");

                entity.Property(e => e.Dp50).HasColumnName("dp50");

                entity.Property(e => e.Dp51).HasColumnName("dp51");

                entity.Property(e => e.Dp52).HasColumnName("dp52");

                entity.Property(e => e.Dp53).HasColumnName("dp53");

                entity.Property(e => e.Dp54).HasColumnName("dp54");

                entity.Property(e => e.Dp55).HasColumnName("dp55");

                entity.Property(e => e.Dp56).HasColumnName("dp56");

                entity.Property(e => e.Dp57).HasColumnName("dp57");

                entity.Property(e => e.Dp58).HasColumnName("dp58");

                entity.Property(e => e.Dp59).HasColumnName("dp59");

                entity.Property(e => e.Dp6).HasColumnName("dp6");

                entity.Property(e => e.Dp60).HasColumnName("dp60");

                entity.Property(e => e.Dp61).HasColumnName("dp61");

                entity.Property(e => e.Dp62).HasColumnName("dp62");

                entity.Property(e => e.Dp63).HasColumnName("dp63");

                entity.Property(e => e.Dp64).HasColumnName("dp64");

                entity.Property(e => e.Dp65).HasColumnName("dp65");

                entity.Property(e => e.Dp66).HasColumnName("dp66");

                entity.Property(e => e.Dp67).HasColumnName("dp67");

                entity.Property(e => e.Dp68).HasColumnName("dp68");

                entity.Property(e => e.Dp69).HasColumnName("dp69");

                entity.Property(e => e.Dp7).HasColumnName("dp7");

                entity.Property(e => e.Dp70).HasColumnName("dp70");

                entity.Property(e => e.Dp71).HasColumnName("dp71");

                entity.Property(e => e.Dp72).HasColumnName("dp72");

                entity.Property(e => e.Dp73).HasColumnName("dp73");

                entity.Property(e => e.Dp74).HasColumnName("dp74");

                entity.Property(e => e.Dp75).HasColumnName("dp75");

                entity.Property(e => e.Dp76).HasColumnName("dp76");

                entity.Property(e => e.Dp77).HasColumnName("dp77");

                entity.Property(e => e.Dp78).HasColumnName("dp78");

                entity.Property(e => e.Dp79).HasColumnName("dp79");

                entity.Property(e => e.Dp8).HasColumnName("dp8");

                entity.Property(e => e.Dp80).HasColumnName("dp80");

                entity.Property(e => e.Dp81).HasColumnName("dp81");

                entity.Property(e => e.Dp82).HasColumnName("dp82");

                entity.Property(e => e.Dp83).HasColumnName("dp83");

                entity.Property(e => e.Dp84).HasColumnName("dp84");

                entity.Property(e => e.Dp85).HasColumnName("dp85");

                entity.Property(e => e.Dp86).HasColumnName("dp86");

                entity.Property(e => e.Dp87).HasColumnName("dp87");

                entity.Property(e => e.Dp88).HasColumnName("dp88");

                entity.Property(e => e.Dp89).HasColumnName("dp89");

                entity.Property(e => e.Dp9).HasColumnName("dp9");

                entity.Property(e => e.Dp90).HasColumnName("dp90");

                entity.Property(e => e.Dp91).HasColumnName("dp91");

                entity.Property(e => e.Dp92).HasColumnName("dp92");

                entity.Property(e => e.Dp93).HasColumnName("dp93");

                entity.Property(e => e.Dp94).HasColumnName("dp94");

                entity.Property(e => e.Dp95).HasColumnName("dp95");

                entity.Property(e => e.Dp96).HasColumnName("dp96");

                entity.Property(e => e.Dp97).HasColumnName("dp97");

                entity.Property(e => e.Dp98).HasColumnName("dp98");

                entity.Property(e => e.Dp99).HasColumnName("dp99");
            });

            modelBuilder.Entity<Messages>(entity =>
            {
                entity.HasKey(e => e.Oid);

                entity.ToTable("Messages");

                entity.HasIndex(e => new { e.RefObjectId, e.RefOid }, "IX_Messages_RefObjectId_RefOid")
                    .IsUnique();
            });

            modelBuilder.Entity<Parameter>(entity =>
            {
                entity.ToTable("Parameter");
            });

            modelBuilder.Entity<Slave>(entity =>
            {
                entity.HasKey(e => e.ObjectId);

                entity.ToTable("Slave");
            });

            modelBuilder.Entity<SlaveMessages>(entity =>
            {
                entity.HasKey(e => new { e.Oid, e.DbSlaveObjectId });

                entity.ToTable("SlaveMessages");

                entity.HasIndex(e => e.DbSlaveObjectId, "IX_SlaveMessages_DbSlaveObjectId");

                entity.HasOne(d => d.DbSlaveObject)
                    .WithMany(p => p.SlaveMessages)
                    .HasForeignKey(d => d.DbSlaveObjectId);
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
