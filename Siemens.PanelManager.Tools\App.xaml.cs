﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace Siemens.PanelManager.Tools
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            HttpService.Server.StartService();
            if (!string.IsNullOrEmpty(StaticInfo.Settings.Ip))
            {
                StaticInfo.CreateConnect(StaticInfo.Settings.Ip);
            }

            base.OnStartup(e);
        }

        protected override void OnExit(ExitEventArgs e)
        {
            HttpService.Server.StopService();
            base.OnExit(e);
        }
    }
}
