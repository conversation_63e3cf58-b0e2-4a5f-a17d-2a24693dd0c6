{"name": "quarter_data", "status": "active", "flux": "import \"date\"\n\noption task = {\n    name: \"quarter_data\",\n    every: 15m,\n}\n\nfrom(bucket: \"panel\")\n    |> range(start: -30m)\n    |> filter(fn: (r) => r[\"_measurement\"] == \"archivedatarealtime\")\n    |> aggregateWindow(every: 15m, fn: max)\n    |> map(fn: (r) => ({r with _measurement: \"archivedataquarter\", time: now()}))\n    |> to(bucket: \"panel\", tagColumns: [\"assetid\", \"objectid\"])", "every": "15m"}