﻿using Newtonsoft.Json;
using Siemens.PanelManager.Tools.HttpService.Model;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Xml.Linq;
using TouchSocket.Core;
using TouchSocket.Http;

namespace Siemens.PanelManager.Tools.HttpService
{
    internal class ApiPlugin : HttpPluginBase<HttpSocketClient>
    {
        private static readonly Dictionary<string, Func<HttpContext, Task<string>>> _getApiList = new Dictionary<string, Func<HttpContext, Task<string>>>();
        private static readonly Dictionary<string, Func<HttpContext, Task<string>>> _postApiList = new Dictionary<string, Func<HttpContext, Task<string>>>();
        static ApiPlugin()
        {
            _getApiList.Add("/api/currentStatus", GetCurrentStatus);
            _getApiList.Add("/api/getProcessList", GetProcessList);
            _getApiList.Add("/api/getMonitorList", GetMonitorList);
            _getApiList.Add("/api/getSettings", GetSettings);
            _getApiList.Add("/api/upgrademessage", GetUpgradeMessage);
            _getApiList.Add("/api/UpgradeStatus", GetUpgradeStatus);
    
            _postApiList.Add("/api/uploadPackage", UploadPackage);
            _postApiList.Add("/api/setSettings", SetSettings);
        }

        #region get post 监听

        protected override async Task OnGetAsync(HttpSocketClient client, HttpContextEventArgs e)
        {
            Func<HttpContext, Task<string>>? func = null;
            foreach (var kvp in _getApiList) 
            {
                if(e.Context.Request.UrlEquals(kvp.Key)) 
                {
                    func = kvp.Value;
                }
            }

            if(func != null) 
            {
                e.Handled= true;
                try
                {
                    var data = await func(e.Context);
                    e.Context.Response.FromJson(data).Answer();
                }
                catch (Exception ex)
                {
                    e.Context.Response.StatusCode = "500";
                    e.Context.Response.FromText(ex.Message).Answer();
                }
            }

            await base.OnGetAsync(client, e);
        }

        protected override async Task OnPostAsync(HttpSocketClient client, HttpContextEventArgs e)
        {
            Func<HttpContext, Task<string>>? func = null;
            foreach (var kvp in _postApiList)
            {
                if (e.Context.Request.UrlEquals(kvp.Key))
                {
                    func = kvp.Value;
                }
            }

            if (func != null)
            {
                e.Handled = true;
                try
                {
                    var data = await func(e.Context);
                    e.Context.Response.FromJson(data).Answer();
                }
                catch (Exception ex)
                {
                    e.Context.Response.StatusCode = "500";
                    e.Context.Response.FromText(ex.Message).Answer();
                }
            }

            await base.OnPostAsync(client, e);
        }

        #endregion

        private static Task<string> GetCurrentStatus(HttpContext context) 
        {
            return Task.FromResult(JsonConvert.SerializeObject(StaticInfo.CurrentStatus));
        }

        private static Task<string> GetProcessList(HttpContext request) 
        {
            return Task.FromResult(JsonConvert.SerializeObject(StaticInfo.ProcessInfos));
        }

        private static async Task<string> GetMonitorList(HttpContext context)
        {
            var data = new MonitorChartModel();
            if (StaticInfo.ConnectStatus != null)
            {
                data.Legend = new ChartItem<string>()
                {
                    Data = new string[] { "CPU(%)", "MEM(%)" }
                };
                var monitorList = await StaticInfo.ConnectStatus.Client.GetMonitorHistory();
                var timeList = new List<string>();
                var cpuList = new List<decimal>();
                var memList = new List<decimal>();
                foreach (var monitor in monitorList)
                {
                    cpuList.Add(monitor.CPU);
                    memList.Add(Math.Round(monitor.MemUsed / monitor.MemTotal * 100m, 2));
                    timeList.Add(monitor.LogTime.ToString("HH:mm:ss"));
                }
                var timeAxis = new ChartItem<string>()
                {
                    Type = "category",
                    Data = timeList.ToArray(),
                };
                data.XAxis.Add(timeAxis);
                var cpuAxis = new ChartItem<decimal>()
                {
                    Type = "line",
                    Data = cpuList.ToArray(),
                    Name = "CPU(%)",
                };
                data.Series.Add(cpuAxis);
                var memAxis = new ChartItem<decimal>()
                {
                    Type = "line",
                    Data = memList.ToArray(),
                    Name = "MEM(%)",
                };
                data.Series.Add(memAxis);
            }
            return JsonConvert.SerializeObject(data);
        }

        private static Task<string> GetSettings(HttpContext context)
        {
            return Task.FromResult(JsonConvert.SerializeObject(StaticInfo.Settings));
        }

        private static Task<string> GetUpgradeMessage(HttpContext context)
        {
            if (StaticInfo.UpgrageLog != null)
            {
                long lastIndex = 0L; 
                var param = context.Request.Query.Get("lastIndex");
                long.TryParse(param, out lastIndex);
                var data = StaticInfo.UpgrageLog.ReadLog(lastIndex, out long next);
                return Task.FromResult(JsonConvert.SerializeObject(new UpgrateLogModel()
                {
                    Message = data,
                    LastIndex = next,
                }));

            }
            return Task.FromResult(JsonConvert.SerializeObject(new UpgrateLogModel()));
        }

        private static Task<string> GetUpgradeStatus(HttpContext context)
        {
            return Task.FromResult(JsonConvert.SerializeObject(new ResponseUpgrateStatus()
            {
                Status = StaticInfo.UpgradeStatus?.Status ?? 0,
                CurrentStatus = StaticInfo.UpgradeStatus?.CurrentStatus ?? new Dictionary<int, string>()
            }));
        }

        private static async Task<string> SetSettings(HttpContext context)
        {
            bool isOk = false;
            var data = context.Request.GetBody();
            try
            {
                bool needReload = false;

                var settings = JsonConvert.DeserializeObject<Settings>(data);
                if (settings != null)
                {
                    needReload = StaticInfo.Settings.Ip != settings.Ip;
                    StaticInfo.Settings.Ip = settings.Ip;
                }
                await StaticInfo.Settings.SaveToFile();
                if (needReload)
                {
                    if (StaticInfo.ConnectStatus != null)
                    {
                        await StaticInfo.ConnectStatus.Client.Stop();
                    }
                    StaticInfo.CreateConnect(StaticInfo.Settings.Ip);
                }
                isOk = true;
            }
            finally
            {
                
            }
            var result = new GeneralResponseModel();
            result.Code = isOk ? "200" : "500";
            return JsonConvert.SerializeObject(result);
        }

        private static async Task<string> UploadPackage(HttpContext context)
        {
            if (StaticInfo.ConnectStatus == null) return JsonConvert.SerializeObject(new GeneralResponseModel() 
            {
                Code = "501",
                Message = "尚未连接"
            });
            var multifileCollection = context.Request.GetMultifileCollection();
            var datas = multifileCollection.First().Data;
            if (datas.Length == 0) return JsonConvert.SerializeObject(new GeneralResponseModel()
            {
                Code = "502",
                Message = "未收到文件"
            });
            string jobId = await StaticInfo.ConnectStatus.Client.BeginUpgrade();
            if (string.IsNullOrEmpty(jobId)) return JsonConvert.SerializeObject(new GeneralResponseModel() 
            {
                Code = "505",
                Message = "升级中。。。"
            });
            
            var client = new System.Net.Http.HttpClient();
            using (var content = new MultipartFormDataContent())
            {
                var byteContent = new ByteArrayContent(datas);
                content.Add(byteContent, "file",multifileCollection.First().FileName);
                var response = await client.PostAsync($"http://{StaticInfo.ConnectStatus.IP}:{StaticInfo.ConnectStatus.Port}/api/Upload/{jobId}/upgradePackage", content);
                var responseStr = await response.Content.ReadAsStringAsync();
                LogHelper.Info($"上传返回 {responseStr}");
                if (StaticInfo.UpgradeStatus != null)
                {
                    StaticInfo.StartUpgrade();
                }

            }

            return JsonConvert.SerializeObject(new GeneralResponseModel()
            {
                Code = "200",
                Message = "OK"
            });
        }

    }
}
