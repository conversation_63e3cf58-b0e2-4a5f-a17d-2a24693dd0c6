﻿using SqlSugar;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    /// <summary>
    /// 通用设备配置表返回对象
    /// </summary>
    public class UniversalDeviceConfigResult
    {
        /// <summary>
        /// 主键id
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 资产表的主键id
        /// </summary>
        public int AssetId { get; set; }

        /// <summary>
        /// 点位英文名称
        /// </summary>
        public string? PropertyEnName { get; set; }

        /// <summary>
        /// 点位中文名称
        /// </summary>
        public string? PropertyCnName { get; set; }

        /// <summary>
        /// 是否二进制位
        /// </summary>
        public bool? IsBit { get; set; } = false;

        /// <summary>
        /// 系数
        /// </summary>
        public string? Coefficient { get; set; }
    }

    /// <summary>
    ///  保存通用设备配置表入参
    /// </summary>
    public class UniversalDeviceConfigParam
    {
        /// <summary>
        /// 主键id
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        ///AssetInfo表的主键id
        /// </summary>
        public int AssetId { get; set; }

        /// <summary>
        /// 点位英文名称
        /// </summary>
        public string? PropertyEnName { get; set; }

        /// <summary>
        /// 点位中文名称
        /// </summary>
        public string? PropertyCnName { get; set; }

        /// <summary>
        /// 是否二进制位
        /// </summary>
        public bool? IsBit { get; set; } = false;

        /// <summary>
        /// 系数
        /// </summary>
        public string? Coefficient { get; set; } = "1.0";
        public string? ThirdPartCode { get; set; } = string.Empty;
    }

    /// <summary>
    /// 二进制点位说明
    /// </summary>
    public class BitConfigResult
    {
        /// <summary>
        /// 主键id
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// UniversalDeviceConfig表的主键id
        /// </summary>
        public int UniversalDeviceConfigId { get; set; }

        /// <summary>
        /// 二进制名称
        /// </summary>
        public string? BitName { get; set; }

        /// <summary>
        ///  bit位序号
        /// </summary>
        public int BitNumber { get; set; }

        /// <summary>
        /// 事件类型
        /// </summary>
        public int EventType { get; set; } = -1;

        /// <summary>
        /// 告警级别
        /// </summary>
        public int AlarmLevel { get; set; } = -1;
        public string ThirdPartCode { get; set; } = string.Empty;
        public string SourceCode { get; set; } = string.Empty;
    }

    /// <summary>
    /// 定时图表入参
    /// </summary>
    public class TimeChartParam
    {
        /// <summary>
        /// 资产id
        /// </summary>
        public int AssetId { get; set; }

        /// <summary>
        /// 点位英文名称
        /// </summary>
        public string? PropertyEnName { get; set; }

        /// <summary>
        /// 点位中文名字
        /// </summary>
        public string? PropertyCnName { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? Unit { get; set; }

        /// <summary>
        /// 时间间隔
        /// </summary>
        public string? TimeInterval { get; set; }

    }

    /// <summary>
    /// 图表入参
    /// </summary>
    public class ChartDataParam
    {
        /// <summary>
        /// 资产id
        /// </summary>
        public int AssetId { get; set; }

        /// <summary>
        /// 时间间隔
        /// </summary>
        public string? TimeInterval { get; set; }

        /// <summary>
        /// 选择的json点位集合
        /// </summary>
        public List<ChartDataDto>? ChartDatas { get; set; }
    }

    /// <summary>
    /// json点位属性
    /// </summary>
    public class ChartDataDto
    {
        /// <summary>
        /// 点位英文名称
        /// </summary>
        public string? PropertyEnName { get; set; }

        /// <summary>
        /// 点位中文名字
        /// </summary>
        public string? PropertyCnName { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? Unit { get; set; }
    }

    /// <summary>
    /// 图表返回参数
    /// </summary>
    public class ChartDataResult
    {
        /// <summary>
        /// 点位英文名称
        /// </summary>
        public string? PropertyEnName { get; set; }

        /// <summary>
        /// 点位中文名字
        /// </summary>
        public string? PropertyCnName { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? Unit { get; set; }

        /// <summary>
        /// 是否二级制点位
        /// </summary>
        public bool IsBit { get; set; } = false;

        /// <summary>
        ///  influxdb中的数据
        /// </summary>
        public ChartDto? ChartDatas { get; set; }
    }

    /// <summary>
    /// 图表的值
    /// </summary>
    public class ChartDto
    {
        /// <summary>
        ///  x轴
        /// </summary>
        public List<string?> Xval { get; set; } = new List<string?>();

        /// <summary>
        /// y轴
        /// </summary>
        public List<string?> Yval { get; set; } = new List<string?>();
    }
}
