import { IxButton } from "@siemens/ix-react";
import { useNavigate } from "react-router-dom";
import "./Error.css";

export default function NotFoundUI() {
  const navigate = useNavigate();
  return (
    <div class="error">
      <div class="error-header"></div>
      <div class="error-body">
        <span class="text-h2">页面不存在</span>
      </div>
      <div class="error-foot">
        <IxButton onClick={() => navigate("/")}>返回首页</IxButton>
      </div>
    </div>
  );
}
