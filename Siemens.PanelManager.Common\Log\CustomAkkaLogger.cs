﻿using Akka.Event;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Common.Log
{
    public class CustomAkkaLogger : DefaultLogger
    {
        public CustomAkkaLogger()
        {
        }

        protected override void Print(LogEvent logEvent)
        {
            if (logEvent != null)
            {
                switch (logEvent.LogLevel())
                {
                    case LogLevel.InfoLevel:
                        LogHelper.Info(logEvent.ToString());
                        break;
                    case LogLevel.ErrorLevel:
                        LogHelper.Error(logEvent.ToString());
                        break;
                    default: break;
                }
            }
        }
    }
}
