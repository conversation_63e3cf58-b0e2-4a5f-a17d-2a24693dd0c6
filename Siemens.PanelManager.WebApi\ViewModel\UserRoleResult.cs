﻿using Siemens.PanelManager.Model.Database.Auth;
using Siemens.PanelManager.WebApi.StaticContent;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class UserRoleResult
    {
        public UserRoleResult(Role[] roles, UserRoleMapping[] userRoleMappings, MessageContext messageContext)
        {
            var rolesList = new List<UserRoleInfo>();
            foreach (Role role in roles) 
            {
                rolesList.Add(new UserRoleInfo()
                {
                    Id = role.Id,
                    Name = messageContext.GetRoleName(role.RoleCode, role.RoleName)
                });
            }
            AllRolesList = rolesList.OrderBy(r=>r.Id).ToArray();

            var assignRolesList = new List<UserRoleInfo>();
            foreach (var roleMapping in userRoleMappings)
            {
                if (roles.Any(r => r.Id == roleMapping.RoleId))
                {
                    assignRolesList.Add(new UserRoleInfo()
                    {
                        Id = roleMapping.RoleId
                    });
                }
            }
            AssignRoles = assignRolesList.ToArray();
        }

        public UserRoleInfo[] AllRolesList { get; set; }
        public UserRoleInfo[] AssignRoles { get; set; }
    }

    public class UserRoleInfo
    {
        public int Id { get; set; }
        public string? RoleName { get; set; }
        public string? Name { get; set; }
    }
}
