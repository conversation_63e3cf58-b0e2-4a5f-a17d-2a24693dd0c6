﻿using Siemens.PanelManager.Model.Database.Asset;

namespace Siemens.PanelManager.Job.UploadExcel
{
    internal class CircuitModel
    {
        public CircuitModel(AssetInfo assetInfo, int index)
        {
            AssetInfo = assetInfo;
            Index = index;
        }
        public int Index { get; private set; }
        public AssetInfo AssetInfo { get; set; }
        public int Heigth { get; set; } = 300;
        public string Width { get; set; } = "1";
        public string BusBarStr { get; set; } = string.Empty;
        public AssetInfo? ParentAsset { get; set; }
        public List<AssetInfo> SubDevices { get; set; } = new List<AssetInfo>();

        public string[] BusBars
        {
            get
            {
                if (string.IsNullOrEmpty(BusBarStr)) return new string[0];
                return BusBarStr.Split(',');
            }
        }
    }
}
