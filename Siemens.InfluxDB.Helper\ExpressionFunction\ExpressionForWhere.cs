﻿using Siemens.InfluxDB.Helper.Interface;
using System.Collections.Concurrent;
using System.Linq.Expressions;
using System.Text;
using System.Text.RegularExpressions;

namespace Siemens.InfluxDB.Helper.ExpressionFunction
{
    internal class ExpressionForWhere
    {
        public ExpressionForWhere(DataHelperBase dataHelper)
        {
            _dataHelper = dataHelper;
        }

        private const string TIMEKEY = "TIMEKEY";
        private DataHelperBase _dataHelper;
        private ConcurrentDictionary<Expression, bool> _expressionParameterCache = new ConcurrentDictionary<Expression, bool>();
        private List<string> _parameter = new List<string>();
        private bool _isLeft = true;

        private Dictionary<string, object> _objectCache = new Dictionary<string, object>();

        public string WhereExpression(Expression exp)
        {
            StringBuilder sql = new StringBuilder();

            switch (exp.NodeType)
            {
                case ExpressionType.Lambda:
                    var lambda = exp as LambdaExpression;
                    if (lambda != null)
                    {
                        var nameList = lambda.Parameters.Select(p => p.Name ?? string.Empty);
                        _parameter.AddRange(nameList);
                        return WhereExpression(lambda.Body);
                    }
                    break;
                case ExpressionType.MemberAccess:
                    {
                        var memberAccess = exp as MemberExpression;
                        if (memberAccess != null)
                        {

                            switch (memberAccess.Member.MemberType)
                            {
                                case System.Reflection.MemberTypes.Field:
                                    {
                                        if (memberAccess.Expression != null && memberAccess.Expression.NodeType == ExpressionType.Constant)
                                        {
                                            object? value = Expression.Lambda(memberAccess).Compile().DynamicInvoke();
                                            if (value != null)
                                            {
                                                var key = $"KEY-{Guid.NewGuid().ToString()}";
                                                _objectCache.Add(key, value);
                                                return key;
                                            }
                                        }
                                        break;
                                    }
                                case System.Reflection.MemberTypes.Property:
                                    {
                                        if (IsParameterExpression(memberAccess))
                                        {
                                            string? columnName = string.Empty;
                                            var result = _dataHelper.GetColumnFilter(memberAccess.Member.Name, _isLeft, out string flux);
                                            switch (result)
                                            {
                                                case 1:
                                                    return flux;
                                                case 2:
                                                    return TIMEKEY;
                                                default: throw new InfluxDBHelperException("使用了没有InfluxDB.Client.Core.Column Attribute的字段");
                                            }
                                        }

                                        if (memberAccess.Member.DeclaringType != null && memberAccess.Expression != null && memberAccess.Expression.NodeType == ExpressionType.MemberAccess)
                                        {
                                            var key = WhereExpression(memberAccess.Expression);
                                            if (!string.IsNullOrEmpty(key))
                                            {
                                                _objectCache.Remove(key, out object? obj);
                                                if (obj != null)
                                                {
                                                    if (memberAccess.Member.MemberType == System.Reflection.MemberTypes.Property)
                                                    {
                                                        var property = memberAccess.Member.DeclaringType.GetProperty(memberAccess.Member.Name);
                                                        var value = property?.GetValue(obj);
                                                        return ConstantToValue(value);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    break;
                            }
                        }
                        break;
                    }
                case ExpressionType.Parameter:
                    {
                        var p = exp as ParameterExpression;
                        if (p != null && !string.IsNullOrEmpty(p.Name))
                        {
                            var result = _dataHelper.GetColumnFilter(p.Name, _isLeft, out string flux);
                            if (result == 1)
                            {
                                return flux;
                            }
                            else if (result == 2)
                            {
                                return TIMEKEY;
                            }
                            else
                            {
                                throw new InfluxDBHelperException($"{p.Name}字段不存在");
                            }
                        }
                        break;
                    }
                case ExpressionType.Call:
                    {
                        throw new InfluxDBHelperException("where 语句中不支持方法");
                    }
                case ExpressionType.Constant:
                    {
                        var constant = exp as ConstantExpression;
                        if (constant != null)
                        {
                            return ConstantToValue(constant.Value);
                        }
                        return string.Empty;
                    }
                case ExpressionType.Convert:
                    {
                        var convert = exp as UnaryExpression;
                        if (convert != null)
                        {
                            if (convert.Operand is Expression expression)
                            {
                                return WhereExpression(expression);
                            }
                            return ConstantToValue(convert.Operand);
                        }
                        break;
                    }
                case ExpressionType.Equal:
                case ExpressionType.NotEqual:
                case ExpressionType.GreaterThan:
                case ExpressionType.LessThan:
                case ExpressionType.GreaterThanOrEqual:
                case ExpressionType.LessThanOrEqual:
                case ExpressionType.Or:
                case ExpressionType.OrElse:
                case ExpressionType.And:
                case ExpressionType.AndAlso:
                case ExpressionType.Subtract:
                case ExpressionType.Add:
                case ExpressionType.Modulo:
                case ExpressionType.Divide:
                case ExpressionType.Multiply:
                    {
                        var andAlso = exp as BinaryExpression;
                        return WhereBinaryExpressionValue(exp.NodeType, andAlso);
                    }
                // -exp
                case ExpressionType.Negate:
                #region 三元计算
                case ExpressionType.Conditional:
                #endregion
                case ExpressionType.Not:
                default:
                    throw new NotSupportedException($"{exp.NodeType}");
            }
            return string.Empty;
        }

        private string WhereBinaryExpressionValue(ExpressionType type, BinaryExpression? andAlso)
        {
            if (andAlso == null) return string.Empty;

            string format = string.Empty;

            bool canBeTime = false;
            switch (type)
            {
                case ExpressionType.And:
                case ExpressionType.AndAlso:
                    format = "({0} and {1})";
                    break;
                case ExpressionType.Or:
                case ExpressionType.OrElse:
                    format = "({0} or {1})";
                    break;
                case ExpressionType.LessThanOrEqual:
                    format = "({0} <= {1})";
                    canBeTime = true;
                    break;
                case ExpressionType.GreaterThanOrEqual:
                    format = "({0} >= {1})";
                    canBeTime = true;
                    break;
                case ExpressionType.LessThan:
                    format = "({0} < {1})";
                    canBeTime = true;
                    break;
                case ExpressionType.GreaterThan:
                    format = "({0} > {1})";
                    canBeTime = true;
                    break;
                case ExpressionType.NotEqual:
                    format = "({0} != {1})";
                    break;
                case ExpressionType.Equal:
                    format = "({0} == {1})";
                    break;
                case ExpressionType.Subtract:
                    format = "({0} - {1})";
                    break;
                case ExpressionType.Add:
                    format = "({0} + {1})";
                    break;
                case ExpressionType.Modulo:
                    format = "({0} % {1})";
                    break;
                case ExpressionType.Divide:
                    format = "({0} / {1})";
                    break;
                case ExpressionType.Multiply:
                    format = "({0} * {1})";
                    break;
            }

            _isLeft = true;
            var left = WhereExpression(andAlso.Left);
            left = GetValue(left);
            _isLeft = false;
            var right = WhereExpression(andAlso.Right);
            right = GetValue(right);
            _isLeft = true;

            if (canBeTime && TIMEKEY.Equals(left))
            {
                if (long.TryParse(right, out long timestamp))
                {
                    if (type == ExpressionType.LessThanOrEqual || type == ExpressionType.LessThan)
                    {
                        _dataHelper.Range.End = timestamp.GetDateTimeBySec();
                    }
                    else
                    {
                        _dataHelper.Range.Start = timestamp.GetDateTimeBySec();
                    }
                    return string.Empty;
                }
            }

            if (canBeTime && TIMEKEY.Equals(right))
            {
                if (long.TryParse(left, out long timestamp))
                {
                    if (type == ExpressionType.LessThanOrEqual || type == ExpressionType.LessThan)
                    {
                        _dataHelper.Range.Start = timestamp.GetDateTimeBySec();
                    }
                    else
                    {
                        _dataHelper.Range.End = timestamp.GetDateTimeBySec();
                    }
                    return string.Empty;
                }
            }
            if (string.IsNullOrEmpty(left))
                return right;
            if (string.IsNullOrEmpty(right))
                return left;

            return string.Format(format, left, right);
        }

        private string GetValue(string input)
        {
            if (Regex.IsMatch(input, "^KEY-[\\w|-]{36}$")
                && _objectCache.TryGetValue(input, out object? value)
                && value != null)
            {
                input = ConstantToValue(value);
            }

            return input;
        }

        private bool IsParameterExpression(Expression exp)
        {
            if (exp == null) return false;
            return _expressionParameterCache.GetOrAdd(exp, expression =>
            {
                if (expression is BinaryExpression)
                {
                    var call = expression as BinaryExpression;
                    if (call == null) return false;
                    return (call.Conversion != null && IsParameterExpression(call.Conversion)) || IsParameterExpression(call.Left) || IsParameterExpression(call.Right);
                }
                else if (expression is BlockExpression)
                {
                    var call = expression as BlockExpression;
                    if (call != null)
                    {
                        return call.Expressions.Any(f => IsParameterExpression(f)) || IsParameterExpression(call.Result) || call.Variables.Any(f => IsParameterExpression(f));
                    }
                    return false;
                }
                else if (expression is ConditionalExpression)
                {
                    var call = expression as ConditionalExpression;
                    if (call != null)
                    {
                        return IsParameterExpression(call.IfFalse) || IsParameterExpression(call.IfTrue) || IsParameterExpression(call.Test);
                    }
                    return false;
                }
                else if (expression is GotoExpression)
                {
                    var call = expression as GotoExpression;
                    if (call != null && call.Value != null)
                    {
                        return IsParameterExpression(call.Value);
                    }
                    return false;
                }
                else if (expression is IndexExpression)
                {
                    var call = expression as IndexExpression;
                    if (call != null)
                    {
                        return call.Arguments.Any(f => IsParameterExpression(f)) || (call.Object != null && IsParameterExpression(call.Object));
                    }
                }
                else if (expression is InvocationExpression)
                {
                    var call = expression as InvocationExpression;
                    if (call != null)
                    {
                        return call.Arguments.Any(f => IsParameterExpression(f)) || IsParameterExpression(call.Expression);
                    }
                }
                else if (expression is LabelExpression)
                {
                    var call = expression as LabelExpression;
                    if (call != null && call.DefaultValue != null)
                    {
                        return IsParameterExpression(call.DefaultValue);
                    }
                }
                else if (expression is LambdaExpression)
                {
                    var call = expression as LambdaExpression;
                    if (call != null)
                    {
                        return IsParameterExpression(call.Body) || call.Parameters.Any(f => IsParameterExpression(f));
                    }
                }
                else if (expression is ListInitExpression)
                {
                    var call = expression as ListInitExpression;
                    if (call != null)
                    {
                        return IsParameterExpression(call.NewExpression);
                    }
                }
                else if (expression is LoopExpression)
                {
                    var call = expression as LoopExpression;
                    if (call != null)
                    {
                        return IsParameterExpression(call.Body);
                    }
                }
                else if (expression is MemberExpression)
                {
                    var call = expression as MemberExpression;
                    if (call != null && call.Expression != null)
                    {
                        return IsParameterExpression(call.Expression);
                    }
                }
                else if (expression is MemberInitExpression)
                {
                    var call = expression as MemberInitExpression;
                    if (call == null) return false;
                    return IsParameterExpression(call.NewExpression);
                }
                else if (expression is MethodCallExpression)
                {
                    var call = expression as MethodCallExpression;
                    if (call == null) return false;
                    return call.Arguments.Any(f => IsParameterExpression(f)) ||(call.Object !=null && IsParameterExpression(call.Object));
                }
                else if (expression is NewArrayExpression)
                {
                    var call = expression as NewArrayExpression;
                    if (call != null)
                    {
                        return call.Expressions.Any(f => IsParameterExpression(f));
                    }
                }
                else if (expression is NewExpression)
                {
                    var call = expression as NewExpression;
                    if (call != null)
                    {
                        return call.Arguments.Any(f => IsParameterExpression(f));
                    }
                }
                else if (expression is ParameterExpression)
                {
                    var call = expression as ParameterExpression;
                    return call == null ? false : _parameter.Contains(call.Name ?? string.Empty);
                }
                else if (expression is RuntimeVariablesExpression)
                {
                    var call = expression as RuntimeVariablesExpression;
                    if (call != null)
                    {
                        return call.Variables.Any(f => IsParameterExpression(f));
                    }
                }
                else if (expression is SwitchExpression)
                {
                    var call = expression as SwitchExpression;
                    if (call != null)
                    {
                        return (call.DefaultBody != null && IsParameterExpression(call.DefaultBody)) || (call.SwitchValue != null && IsParameterExpression(call.SwitchValue));
                    }
                }
                else if (expression is TryExpression)
                {
                    var call = expression as TryExpression;
                    if (call != null)
                    {
                        return IsParameterExpression(call.Body) ||
                        (call.Fault != null && IsParameterExpression(call.Fault))
                        || (call.Finally != null && IsParameterExpression(call.Finally));
                    }
                }
                else if (expression is TypeBinaryExpression)
                {
                    var call = expression as TypeBinaryExpression;
                    if (call != null)
                    {
                        return IsParameterExpression(call.Expression);
                    }
                }
                else if (expression is UnaryExpression)
                {
                    var call = expression as UnaryExpression;
                    if (call != null)
                    {
                        return IsParameterExpression(call.Operand);
                    }
                }

                return false;
            });
        }

        private string ConstantToValue(object? value)
        {
            if (value == null) return "null";
            if (value is System.Enum)
            {
                return $"\"{value}\"";
            }

            if (value is decimal || value is float || value is double)
            {
                return ((double)value).ToString("0.0#####");
            }

            if (value is int
                || value is long
                || value is short
                || value is char
                || value is byte)
            {
                return ((long)value).ToString();
            }

            if (value is uint
                || value is ulong
                || value is ushort)
            {
                return ((ulong)value).ToString();
            }

            if (value is DateTime)
            {
                return ((DateTime)value).GetTimestampForSec().ToString();
            }

            if (value is string)
            {
                return $"\"{value}\"";
            }
            throw new InfluxDBHelperException("数据类型不支持");
        }
    }
}
