﻿using Akka.Actor;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.DeviceDataFlow.StaticData;
using Siemens.PanelManager.Model.DataFlow;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.DeviceDataFlow.DataFlowActors
{
    internal class AssetAllDataActor : ReceiveActor
    {
        private readonly ILogger _logger;
        private readonly SiemensCache _cache;
        

        public AssetAllDataActor(ILogger<AssetAllDataActor> logger, SiemensCache cache)
        {
            _logger = logger;
            _cache = cache;

            Receive<AssetInputData>(AssetInputData);
            Receive<AssetChangeData>(AssetChangeData);
            Receive<AssetDetailsData>(AssetDetatilsData);
        }

        private void AssetInputData(AssetInputData data)
        {
            if (data == null || data.Datas == null || data.Datas.Count == 0)
            {
                return;
            }

            _cache.SetHashData<string>(string.Format(Constant.AssetAllDataCacheKey, data.AssetId), data.Datas);
        }

        private void AssetChangeData(AssetChangeData data)
        {
            if (data == null || data.ChangeDatas == null || data.ChangeDatas.Count == 0)
            {
                return;
            }

            _cache.SetHashData<string>(string.Format(Constant.AssetAllDataCacheKey, data.AssetId), data.ChangeDatas);
        }

        private void AssetDetatilsData(AssetDetailsData data)
        {
            if (data == null || data.Details == null || data.Details.Count == 0)
            {
                return;
            }

            _cache.SetHashData<string>(string.Format(Constant.AssetAllDataCacheKey, data.AssetId), data.Details);
        }
    }
}
