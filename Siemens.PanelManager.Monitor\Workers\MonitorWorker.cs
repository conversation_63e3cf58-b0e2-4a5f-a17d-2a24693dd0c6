﻿using Microsoft.AspNetCore.SignalR;
using Siemens.PanelManager.HubModel.Client;
using Siemens.PanelManager.Monitor.Function;
using Siemens.PanelManager.Monitor.Hubs;
using Siemens.PanelManager.Monitor.StaticData;

namespace Siemens.PanelManager.Monitor.Workers
{
    public class MonitorWorker : BackgroundService
    {
        private ILogger<MonitorWorker> _logger { get; set; }
        private IHubContext<MonitorHub> _hubContext { get; set; }
        public MonitorWorker(ILogger<MonitorWorker> logger, IHubContext<MonitorHub> hubContext)
        {
            _logger = logger;
            _hubContext = hubContext;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while(true) 
            {
                if(stoppingToken.IsCancellationRequested) return;
                try
                {
                    var model = await MonitorFunc.GetMonitorModel(_logger);
                    if (model != null)
                    {
                        await _hubContext.Clients.Group(Constants.Group_MonitorClient).SendAsync("MonitorInfo", model);
                        model.DockerProcessStats = new HubModel.DockerProcessStats[0];
                        MonitorTimeCache.Append(model);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "周期采集失败");
                }

                await Task.Delay(10000);
            }
        }
    }
}
