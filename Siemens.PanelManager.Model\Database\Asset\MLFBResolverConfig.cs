﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Asset
{
    [SugarTable("asset_mlfb_resolve_config")]
    public class MLFBResolveConfig : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        /// <summary>
        /// 配置组名
        /// </summary>
        [Uniqueness]
        [SugarColumn(ColumnName = "code", IsNullable = false, Length = 128)]
        public string GroupCode { get; set; } = string.Empty;
        /// <summary>
        /// 设备类型
        /// </summary>
        [Uniqueness]
        [SugarColumn(ColumnName = "device_type", IsNullable = false, Length = 50)]
        public string DeviceType { get; set; } = string.Empty;
        /// <summary>
        /// 设备型号
        /// </summary>
        [Uniqueness]
        [SugarColumn(ColumnName = "device_model", IsNullable = false, Length = 50)]
        public string DeviceModel { get; set; } = string.Empty;
        /// <summary>
        /// MLFB 中标识长度
        /// </summary>
        [SugarColumn(ColumnName = "length", IsNullable = false)]
        public int Length { get; set; }
        /// <summary>
        /// MLFB 中标识起点
        /// </summary>
        [SugarColumn(ColumnName = "begin", IsNullable = false)]
        public int Begin { get; set; }
        /// <summary>
        /// MLFB 中对应代码的正则匹配规则
        /// </summary>
        [Uniqueness]
        [SugarColumn(ColumnName = "key_rule", IsNullable = false, Length = 150)]
        public string KeyRule { get; set; } = string.Empty;
        /// <summary>
        /// MLFB 中对应标识代表的值
        /// </summary>
        [SugarColumn(ColumnName = "value", IsNullable = false, Length = 50)]
        public string Value { get; set; } = string.Empty;

        private string _ruleString = string.Empty;
        [SugarColumn(IsIgnore = true)]
        public string RuleString
        {
            get
            {
                if (string.IsNullOrEmpty(_ruleString))
                {
                    GetRuleString();
                }
                return _ruleString;
            }
        }

        private void GetRuleString()
        {
            var ruleBuilder = new StringBuilder(Begin + Length);
            ruleBuilder.Append('^');
            if (Begin > 1)
            {
                ruleBuilder.Append("(.){");
                ruleBuilder.Append(Begin - 1);
                ruleBuilder.Append('}');
            }
            ruleBuilder.Append(KeyRule);
            _ruleString = ruleBuilder.ToString();
        }
    }
}
