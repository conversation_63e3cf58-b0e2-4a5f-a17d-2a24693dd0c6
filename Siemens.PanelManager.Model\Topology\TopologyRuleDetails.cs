﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.Model.Topology
{
    public class TopologyRuleDetails
    {
        [JsonProperty("assetId")]
        public int AssetId { get; set; }
        [JsonProperty("bindValue")]
        public string DataPoint { get; set; } = string.Empty;
        [JsonProperty("bindItem", NullValueHandling = NullValueHandling.Ignore)]
        public TopologyTargeInfo? TargeInfo { get; set; }
    }

    public class TopologyTargeInfo
    {
        [JsonProperty("key")]
        public string TargetIdentify { get; set; } = string.Empty;
        [JsonProperty("parameter")]
        public string TargetProperty { get; set; } = string.Empty;
    }
}
