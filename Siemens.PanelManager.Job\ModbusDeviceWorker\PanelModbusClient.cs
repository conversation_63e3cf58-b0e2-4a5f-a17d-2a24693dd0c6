﻿using FluentModbus;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NPOI.SS.Formula.Functions;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Job.ModbusDeviceWorker.DataParse;
using Siemens.PanelManager.Model.UDC;
using System.Collections.Concurrent;
using System.Net;

namespace Siemens.PanelManager.Job.ModbusDeviceWorker
{
    public class PanelModbusClient : IDisposable
    {
        private readonly ILogger<PanelModbusClient> _logger;
        private readonly ConcurrentDictionary<string, List<PanelModbusFunction>> _modbusFunctionConfigs;
        private readonly IServiceProvider _provider;
        private int _ReadTimeout = 1000;
        private int _readInterval = 100;
        private int _deviceReadInterval = 100;
        private PanelModbusClientConfig _config;
        private ModbusTcpClient? _modbusTcpClient;
        private Task? _modbusTcpClientTask;
        private CancellationTokenSource? _modbusTaskCts;
        private IMessageReceiver? _messageReceiver;

        private int _count = 0;
        private DateTime _lastWriteTime = DateTime.Now;
        private const int ATTEMPT_COUNT = 3;

        public PanelModbusClient(PanelModbusClientConfig config,
            ConcurrentDictionary<string, List<PanelModbusFunction>> modbusFunctionConfigs,
            IServiceProvider serviceProvider)
        {
            _config = config;
            _modbusTcpClient = new ModbusTcpClient();
            _modbusFunctionConfigs = modbusFunctionConfigs;
            _provider = serviceProvider;
            _logger = _provider.GetRequiredService<ILoggerFactory>().CreateLogger<PanelModbusClient>();
        }

        public void StartMonitor()
        {
            _modbusTaskCts = new CancellationTokenSource();

            _modbusTcpClientTask = Task.Run(async () =>
            {
                var needReconnection = false;

                while (!_modbusTaskCts.IsCancellationRequested)
                {
                    try
                    {
                        if (_modbusTcpClient != null)
                        {
                            _modbusTcpClient.ReadTimeout = _ReadTimeout;
                            _modbusTcpClient.ConnectTimeout = _ReadTimeout;

                            if (!_modbusTcpClient.IsConnected)
                            {
                                _modbusTcpClient.Connect(new IPEndPoint(IPAddress.Parse(_config.IPAddress), _config.Port), ModbusEndianness.BigEndian);
                            }

                            if (_modbusTcpClient.IsConnected)
                            {
                                PanelModbusClientStatus.GetInstance().SetClientStatus(_config.AssetId, "1");

                                needReconnection = !ReadRegisterData(_modbusTcpClient, _config);
                            }
                        }

                        if (needReconnection)
                        {
                            try
                            {
                                if (_modbusTcpClient != null)
                                {
                                    _modbusTcpClient?.Disconnect();
                                    _modbusTcpClient = null;
                                }
                            }
                            catch (Exception disposeEx)
                            {
                                _logger.LogError($"{DateTime.Now},{disposeEx.Message}");
                            }

                            _modbusTcpClient = new ModbusTcpClient();
                            needReconnection = false;
                        }

                        await Task.Delay(TimeSpan.FromMilliseconds(_readInterval));
                    }
                    catch (Exception ex)
                    {
                        // _logger.LogError(ex, $"{DateTime.Now},{ex.Message}");

                        SetAllClientStatus("0");

                        await Task.Delay(TimeSpan.FromMilliseconds(_readInterval));

                        try
                        {
                            if (_modbusTcpClient != null)
                            {
                                _modbusTcpClient?.Disconnect();
                                _modbusTcpClient = null;
                            }
                        }
                        catch (Exception disposeEx)
                        {
                            // _logger.LogError($"{DateTime.Now},{disposeEx.Message}");
                        }

                        _modbusTcpClient = new ModbusTcpClient();
                        needReconnection = false;
                    }
                }
            }, _modbusTaskCts.Token);
        }

        public void StopMonitor()
        {
            _modbusTaskCts?.Cancel();
            Dispose();
        }

        private bool ReadRegisterData(ModbusTcpClient client, PanelModbusClientConfig config)
        {
            try
            {
                //读取网关的相关信息
                if (!InitMessageBody(client, _config.SlaveId, _config.ThirdPartCode, _config.AssetId, _config.AssetName, _config.ObjectId))
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                // _logger.LogError(ex, $"{DateTime.Now},SlaveId[{_config.SlaveId}], {ex.Message}");
            }

            if (_config.PanelModbusSlaves != null && _config.PanelModbusSlaves.Any())
            {
                var readfailSlaveCount = 0;
                var breakOuter = false;
                foreach (var tempSlave in _config.PanelModbusSlaves)
                {
                    try
                    {
                        // Maximum read(ushort) length of a single read:125,byte:250
                        if (!InitMessageBody(client, tempSlave.SlaveId, tempSlave.ThirdPartCode, tempSlave.AssetId, tempSlave.AssetName, tempSlave.ObjectId))
                        {
                            readfailSlaveCount++;
                            //break;
                        }

                        Thread.Sleep(_deviceReadInterval);
                    }
                    catch (Exception ex)
                    {
                        PanelModbusClientStatus.GetInstance().SetClientStatus(tempSlave.AssetId, "0");
                        readfailSlaveCount++;
                        Thread.Sleep(_deviceReadInterval);
                        // _logger.LogError(ex, $"{DateTime.Now},SlaveId[{tempSlave.SlaveId}], {ex.Message}");
                    }
                }

                if (readfailSlaveCount >= _config.PanelModbusSlaves.Count)
                {
                    breakOuter = true;
                }

                return !breakOuter;
            }

            return true;
        }

        private bool InitMessageBody(ModbusTcpClient client, int slaveId, string? thirdPartCode, int assetId, string assetName, string objectId)
        {
            if (!string.IsNullOrEmpty(thirdPartCode) && _modbusFunctionConfigs.TryGetValue(thirdPartCode, out var panelModbusFunctions))
            {
                Span<byte> data;

                var fc03Or04Datas = panelModbusFunctions.Where(a => a.FunctionCode == "FC3" || a.FunctionCode == "FC4");

                DeviceDataPointsResult deviceDataPointsResult = new DeviceDataPointsResult();

                deviceDataPointsResult.ItemId = objectId;
                deviceDataPointsResult.ItemName = assetName;
                deviceDataPointsResult.TimeStamp = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:sszz");

                // 由于是分批读取寄存器，只有所有分批读取全部连接不上，才能主动设置成离线，期间有一次读取，需要重置状态
                var needSetOffline = false;
                var needBreakOuter = false;

                foreach (var fcdata in fc03Or04Datas)
                {
                    foreach (var readSpan in fcdata.PanelModbusReadSpans)
                    {
                        var startObj = readSpan.PanelModbusDataPoints.FirstOrDefault();

                        var attemptCount = 0;

                        while (attemptCount < ATTEMPT_COUNT)
                        {
                            try
                            {
                                if (fcdata.FunctionCode == "FC3")
                                {
                                    data = client.ReadHoldingRegisters<byte>(slaveId, startObj.Register, readSpan.ReadRegisterCount * 2);
                                }
                                else
                                {
                                    data = client.ReadInputRegisters<byte>(slaveId, startObj.Register, readSpan.ReadRegisterCount * 2);
                                }

                                PanelModbusClientStatus.GetInstance().SetClientStatus(assetId, "1");


                                if (data.Length == readSpan.ReadRegisterCount * 2)
                                {
                                    needSetOffline = false;

                                    var firstObjRegister = startObj?.Register;

                                    foreach (var tempDataPoint in readSpan.PanelModbusDataPoints)
                                    {
                                        try
                                        {
                                            string? readValue = string.Empty;
                                            int readTempIndex = (int)(tempDataPoint.Register - firstObjRegister) * 2;

                                            var customFactor = PanelModbusClientStatus.GetInstance().GetDataPointCustomFactor(assetId, tempDataPoint.PropertyName);

                                            readValue = PanelModbusReadFactory
                                                .Create(tempDataPoint.TransformationType, fcdata.IsBigEndian, tempDataPoint.ParseMode, tempDataPoint.Factor, (float)customFactor, tempDataPoint.Intercept)?
                                                .ReadData(data.Slice(readTempIndex, tempDataPoint.RegisterCount * 2));

                                            deviceDataPointsResult.Embedded.Items.Add(new DeivceDataPointItem
                                            {
                                                InternalName = tempDataPoint.PropertyName,
                                                Id = -1,
                                                DisplayName = tempDataPoint.DescriptionInEnglish,
                                                DisplayValue = "inactive",
                                                Value = readValue ?? "0",
                                                Unit = tempDataPoint.Unit,
                                                Quality = "valid"
                                            });
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger.LogError($"{DateTime.Now},{ex.Message}");
                                        }
                                    }

                                    Thread.Sleep(_deviceReadInterval);
                                    break;
                                }
                                else
                                {
                                    attemptCount++;
                                    _logger.LogInformation($"[{DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffzz")}]:ECount S:[{slaveId}] [{assetName}][{assetId}] [{fcdata.FunctionCode}] TL:[{readSpan.ReadRegisterCount * 2}] RL:[{data.Length}]");
                                    Thread.Sleep(_deviceReadInterval);
                                }
                            }
                            catch (Exception ex)
                            {
                                needSetOffline = true;
                                attemptCount++;
                                if (attemptCount>2)
                                {
                                    _logger.LogError($"{DateTime.Now},FC:{fcdata.FunctionCode},SID:{slaveId},StartR:{startObj.Register},Count:{readSpan.ReadRegisterCount},{ex.Message}");
                                }
                                Thread.Sleep(_deviceReadInterval);
                            }
                        }

                        if (attemptCount >= ATTEMPT_COUNT)
                        {
                            needBreakOuter = true;
                            break;
                        }
                    }

                    if (needBreakOuter)
                    {
                        break;
                    }
                }

                if (needBreakOuter)
                {
                    return false;
                }

                try
                {
                    if (_messageReceiver == null)
                    {
                        _messageReceiver = _provider.GetService<IMessageReceiver>();
                    }

                    if (_messageReceiver == null)
                    {
                        return true;
                    }

                    if (deviceDataPointsResult.Embedded.Items != null && deviceDataPointsResult.Embedded.Items.Count != 0)
                    {
                        PanelModbusClientStatus.GetInstance().SetClientStatus(assetId, "1");

                        int itemCount = deviceDataPointsResult.Embedded.Items.Count;
                        deviceDataPointsResult.Count = itemCount;
                        deviceDataPointsResult.Total = itemCount;

                        _count++;

                        if ((DateTime.Now - _lastWriteTime).TotalSeconds > (5 * 60))
                        {
                            _lastWriteTime = DateTime.Now;
                            _logger.LogInformation($"[{_config.IPAddress}][{_count}]");
                            _count = 0;
                        }

                        _messageReceiver?.InputMessage(deviceDataPointsResult);
                    }
                    else
                    {
                        if (needSetOffline)
                        {
                            PanelModbusClientStatus.GetInstance().SetClientStatus(assetId, "0");
                        }
                    }
                }
                catch (Exception ex)
                {
                    // _logger.LogError(ex, $"{DateTime.Now},InitMessageBody:IMessageReceiver:{ex.Message}");
                }
            }

            return true;
        }

        private void SetAllClientStatus(string status)
        {
            PanelModbusClientStatus.GetInstance().SetClientStatus(_config.AssetId, status);
            if (_config.PanelModbusSlaves != null && _config.PanelModbusSlaves.Any())
            {
                _config.PanelModbusSlaves.ForEach(p =>
                {
                    PanelModbusClientStatus.GetInstance().SetClientStatus(p.AssetId, status);
                });
            }
        }

        public void Dispose()
        {
            _modbusTcpClient?.Disconnect();
            _modbusTcpClient = null;
        }
    }
}
