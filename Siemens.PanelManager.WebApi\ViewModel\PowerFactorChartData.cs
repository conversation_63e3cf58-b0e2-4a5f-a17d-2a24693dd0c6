﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class PowerFactorChartData
    {
        [JsonProperty("device_names")]
        public string[] DeviceNames { get; set; } = new string[0];
        public PowerFactorChartValue Value { get; set; }= new PowerFactorChartValue();
    }
    public class PowerFactorChartValue
    {
        public Dictionary<string, decimal[]> Mean { get; set; } = new Dictionary<string, decimal[]>();
        public Dictionary<string, decimal[]> Max { get; set; } = new Dictionary<string, decimal[]>();
        public Dictionary<string, decimal[]> Min { get; set; } = new Dictionary<string, decimal[]>();
    }
}
