﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using TouchSocket.Core;
using TouchSocket.Http;
using TouchSocket.Sockets;

namespace Siemens.PanelManager.Tools.HttpService
{
    static class Server
    {
        private static TouchSocket.Http.HttpService? _httpService;
        private static object _lock = new object();
        public const string Url = "http://127.0.0.1:15567/";
        public static void StartService()
        {
            if (_httpService == null)
            { 
                lock(_lock) 
                {
                    if(_httpService== null) 
                    {
                        _httpService = new TouchSocket.Http.HttpService();
                        var config = new TouchSocketConfig();
                        config.UsePlugin()
                            .SetListenIPHosts(new IPHost[] { new IPHost(IPAddress.Parse("127.0.0.1"), 15567) })
                            .ConfigureContainer(container =>
                            {
                                container.AddEasyLogger(EasyLogger);
                            })
                            .ConfigurePlugins(plugins =>
                            {
                                plugins.Add<ApiPlugin>();
                                var staticPagePlugin = plugins.Add<HttpStaticPagePlugin>();
                                staticPagePlugin.AddFolder("UI");
                                plugins.UseDefaultHttpServicePlugin();
                            });

                        _httpService.Setup(config).Start();
                    }
                }
            }
        }

        public static void StopService()
        {
            if (_httpService != null)
            {
                lock (_lock)
                {
                    try
                    {
                        _httpService?.Stop();
                    }
                    finally
                    {
                        Thread.Sleep(1000);
                        _httpService = null;
                    }
                }
            }
        }

        private static void EasyLogger(string message) 
        {
            LogHelper.Info(message);
        }
    }
}
