﻿using Akka.Actor;
using Akka.DependencyInjection;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Quartz.Impl.AdoJobStore.Common;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.DeviceDataFlow.StaticData;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.DataFlow;
using SqlSugar;
using System.Collections.Concurrent;

namespace Siemens.PanelManager.DeviceDataFlow.DataFlowActors
{
    internal class AssetProxyActor : ReceiveActor
    {
        private ConcurrentDictionary<int, IActorRef> _assetIdMappings;
        private SiemensCache _cache;
        private readonly ILogger<AssetProxyActor> _logger;
        private readonly IServiceProvider _provider;

        public AssetProxyActor(ILogger<AssetProxyActor> logger, SiemensCache cache,
            IServiceProvider provider, AssetSimpleInfo[]? assetSimpleInfos)
        {
            _cache = cache;
            _logger = logger;
            _provider = provider;
            _assetIdMappings = new ConcurrentDictionary<int, IActorRef>();

            if (assetSimpleInfos != null)
            {
                GetConfigBySql(assetSimpleInfos);
                foreach (var simpleInfo in assetSimpleInfos)
                {
                    var actorRef = CreateActor(simpleInfo);
                    if (actorRef != null)
                    {
                        _assetIdMappings.TryAdd(simpleInfo.AssetId, actorRef);
                    }
                }
            }
            ReceviceFunc();
        }

        protected override bool AroundReceive(Receive receive, object message)
        {
            try
            {
                return base.AroundReceive(receive, message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AssetProxyActor Failed");
                return true;
            }
        }

        private void GetConfigBySql(AssetSimpleInfo[]? assetSimpleInfos)
        {
            if (assetSimpleInfos == null) return;
            try
            {
                var sqlClient = _provider.GetRequiredService<SqlSugarScope>();
                var assetIds = assetSimpleInfos.Where(a => a.AssetLevel == AssetLevel.Device).Select(a => a.AssetId).ToArray();
                var customDataPointConfigs = sqlClient.Queryable<CustomDataPoint>().ToArray();
                if (customDataPointConfigs == null || customDataPointConfigs.Any())
                {
                    return;
                }

                var r = new Random();

                foreach (var assetId in assetIds)
                {
                    var customDataPointConfig = customDataPointConfigs.Where(c=>c.RealAssetId == assetId).ToArray();
                    if (customDataPointConfig == null) 
                    {
                        customDataPointConfig = Array.Empty<CustomDataPoint>();
                    }

                    _cache.Set($"CustomDataPoint-{assetId}", customDataPointConfig, TimeSpan.FromSeconds(r.Next(1200, 3000)));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"GetConfigBySql");
            }
        }

        private void ReceviceFunc()
        {
            Receive<AssetInputData>(InputDataFunc);
            Receive<AssetChangeData>(ChangeDataFunc);
            ReceiveAsync<AssetOptionParam>(AssetOptionFunc);
            ReceiveAsync<AssetOpt>(ReloadAssetCacheFunc);
        }

        private async Task ReloadAssetCacheFunc(AssetOpt opt)
        {
            var sqlClient = _provider.GetRequiredService<SqlSugarScope>();
            var assetInfos = await sqlClient.Queryable<AssetInfo>().ToArrayAsync();

            await AssetInfoExtend.UpdateGetCircuitSource(sqlClient, _cache);
        }

        private void InputDataFunc(AssetInputData inputData)
        {
            IActorRef? target = null;

            if (target == null && inputData.ParentId.HasValue)
            {
                _assetIdMappings.TryGetValue(inputData.ParentId.Value, out target);
            }

            if (target == null && inputData.AssetId.HasValue)
            {
                _assetIdMappings.TryGetValue(inputData.AssetId.Value, out target);
            }

            if (target == null && !string.IsNullOrEmpty(inputData.ObjectId))
            {
                var simpleInfo = _cache.Get<AssetSimpleInfo>(string.Format(Constant.ObjectIdCacheKey, inputData.ObjectId));
                if (simpleInfo != null)
                {
                    _assetIdMappings.TryGetValue(simpleInfo.AssetId, out target);
                }
            }

            if (target != null)
            {
                target.Tell(inputData);
            }
        }

        private void ChangeDataFunc(AssetChangeData changeData)
        {
            IActorRef? target = null;
            _assetIdMappings.TryGetValue(changeData.AssetId, out target);
            if (target != null)
            {
                target.Tell(changeData);
            }
        }

        private async Task AssetOptionFunc(AssetOptionParam param)
        {
            switch (param.Opt)
            {
                case AssetOpt.Update:
                    {
                        var time = _cache.Get<DateTime>($"AssetChangeFlag-{param.AssetId}");

                        var delayTime = (DateTime.Now - time).TotalSeconds;

                        if ((DateTime.Now - time).TotalSeconds < 500)
                        {
                            delayTime = 500d - delayTime;
                            await Task.Delay((int)delayTime);
                        }
                        
                        var proxyRef = _provider.GetRequiredService<IAssetDataProxyRef>();
                        using (var sqlClient = _provider.GetRequiredService<ISqlSugarClient>())
                        {
                            var assetInfo = await sqlClient.Queryable<AssetInfo>().FirstAsync(a => a.Id == param.AssetId);
                            if (assetInfo != null)
                            {
                                var simpleInfo = await AssetInfoExtend.UpdateAsset(assetInfo, sqlClient, _cache, proxyRef, _logger);
                            }
                            else
                            {
                                await AssetInfoExtend.RemoveAsset(param.AssetId, _cache, sqlClient, proxyRef);
                                if (_assetIdMappings.TryRemove(param.AssetId, out var refObj))
                                {
                                    refObj.Tell(Kill.Instance);
                                }
                            }
                        }

                        break;
                    }
                case AssetOpt.Add:
                    {
                        var time = _cache.Get<DateTime>($"AssetAddFlag-{param.AssetName}");

                        var delayTime = (DateTime.Now - time).TotalSeconds;

                        if ((DateTime.Now - time).TotalSeconds < 500)
                        {
                            delayTime = 500d - delayTime;
                            await Task.Delay((int)delayTime);
                        }

                        var refObj = _provider.GetRequiredService<IAssetDataProxyRef>();
                        using (var sqlClient = _provider.GetRequiredService<ISqlSugarClient>())
                        {
                            for (var i = 0; i < 2; i++)
                            {
                                var assetInfo = await sqlClient.Queryable<AssetInfo>().FirstAsync(a => a.AssetName == param.AssetName);
                                if (assetInfo != null)
                                {
                                    var simpleInfo = await AssetInfoExtend.AddAsset(assetInfo, sqlClient, _cache, refObj);
                                    var actorRef = CreateActor(simpleInfo);
                                    if (actorRef != null)
                                    {
                                        ReplaceActor(simpleInfo.AssetId, actorRef);
                                    }

                                    break;
                                }
                                else
                                {
                                    await Task.Delay(500);
                                }
                            }
                        }
                        break;
                    }
                case AssetOpt.Remove:
                    {
                        var time = _cache.Get<DateTime>($"AssetChangeFlag-{param.AssetId}");

                        var delayTime = (DateTime.Now - time).TotalSeconds;

                        if ((DateTime.Now - time).TotalSeconds < 500)
                        {
                            delayTime = 500d - delayTime;
                            await Task.Delay((int)delayTime);
                        }

                        var proxyRef = _provider.GetRequiredService<IAssetDataProxyRef>();
                        using (var sqlClient = _provider.GetRequiredService<ISqlSugarClient>())
                        {
                            await AssetInfoExtend.RemoveAsset(param.AssetId, _cache, sqlClient, proxyRef);
                            if (_assetIdMappings.TryRemove(param.AssetId, out var refObj))
                            {
                                refObj.Tell(Kill.Instance);
                            }
                        }
                        break;
                    }
                default: break;
            }
        }

        private void ReplaceActor(int assetId, IActorRef actorRef)
        {
            if (_assetIdMappings.TryRemove(assetId, out var refObj))
            {
                refObj.Tell(Kill.Instance);
            }
            _assetIdMappings.TryAdd(assetId, actorRef);
        }

        private IActorRef? CreateActor(AssetSimpleInfo simpleInfo)
        {
            if (simpleInfo != null)
            {
                switch (simpleInfo.AssetLevel)
                {
                    case AssetLevel.Substation:
                        {
                            var props = DependencyResolver.For(Context.System).Props<SubstationActor>(simpleInfo);
                            var actor = Context.ActorOf(props);
                            return actor;
                        }
                    case AssetLevel.Panel:
                        {
                            var props = DependencyResolver.For(Context.System).Props<PanelActor>(simpleInfo);
                            var actor = Context.ActorOf(props);
                            return actor;
                        }
                    case AssetLevel.Transformer:
                        {
                            var props = DependencyResolver.For(Context.System).Props<TransformerActor>(simpleInfo);
                            var actor = Context.ActorOf(props);
                            return actor;
                        }
                    case AssetLevel.Circuit:
                        {
                            var props = DependencyResolver.For(Context.System).Props<CircuitActor>(simpleInfo);
                            var actor = Context.ActorOf(props);
                            return actor;
                        }
                    case AssetLevel.Device:
                        {
                            var props = DependencyResolver.For(Context.System).Props<DeviceActor>(simpleInfo);
                            var actor = Context.ActorOf(props);
                            return actor;
                        }
                    default: break;
                }
            }

            return null;
        }

    }
}
