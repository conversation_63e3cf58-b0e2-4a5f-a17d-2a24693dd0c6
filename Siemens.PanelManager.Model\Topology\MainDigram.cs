﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.Model.Topology
{
    public class MainDigram
    {
        [JsonProperty("class")]
        public string Class => "GraphLinksModel";
        [JsonProperty("linkKeyProperty")]
        public string LinkKeyProperty => "key";
        [JsonProperty("nodeDataArray")]
        public List<NodeData> Nodes { get; set; } = new List<NodeData>();
        [JsonProperty("linkDataArray")]
        public List<LineData> Lines { get; set; } = new List<LineData>();
        [JsonProperty("source")]
        public List<List<int>> SourceKeyList { get; set; } = new List<List<int>>();
        [JsonProperty("group", NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, GroupKeyMappingInfo>? GroupKeyMappings { get; set; }
        /// <summary>
        /// 绑定数据
        /// </summary>
        [JsonProperty("rule", NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, TopologyRuleDetails>? BindRule { get; set; }
        [JsonIgnore]
        public int X { get; set; }
        [JsonIgnore]
        public int Y { get; set; }
    }

    public class GroupKeyMappingInfo
    {
        [JsonProperty("list", NullValueHandling = NullValueHandling.Ignore)]
        public List<int>? List { get; set; }
        [JsonProperty("level", NullValueHandling = NullValueHandling.Ignore)]
        public string? Level { get; set; }
    }
}
