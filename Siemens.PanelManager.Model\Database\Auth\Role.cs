﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Auth
{
    [SugarTable("auth_role")]
    public class Role : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [UniquenessAttribute]
        [SugarColumn(ColumnName = "role_name", Length = 256, IsNullable = false)]
        public string RoleName { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "role_code", Length = 256, IsNullable = false)]
        public string RoleCode { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "role_description", Length = 2048, IsNullable = false)]
        public string? RoleDescription { get; set; }
        [SugarColumn(ColumnName = "role_count", IsNullable = true)]
        public int? RoleCount { get; set; }

        //[SugarColumn(ColumnName = "is_system_role", IsNullable = false)]
        //public bool IsSystemRole { get; set; } = false;

    }
}
