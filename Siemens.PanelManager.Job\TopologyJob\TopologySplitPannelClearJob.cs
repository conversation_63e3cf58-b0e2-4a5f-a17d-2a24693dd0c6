﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Common;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.Model.Database.Topology;
using Siemens.PanelManager.Model.Database.Topoplogy;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.TopologyJob
{
    public class TopologySplitPannelClearJob : JobBase
    {
        public override string Name => "TopologySplitPannelClearJob";

        private ILogger<TopologySplitPannelClearJob> _logger;
        private IServiceProvider _provider;

        public TopologySplitPannelClearJob(IServiceProvider provider, ILogger<TopologySplitPannelClearJob> logger)
        {
            _provider = provider;
            _logger = logger;
        }

        public override async Task Execute()
        {
            var ts = DateTime.Today.AddHours(-1).GetTimestampForSec();
            using (var client = _provider.GetRequiredService<ISqlSugarClient>())
            {
                var topologyIds = await client.Queryable<TopologyInfo>()
                    .Where(t => t.Type == "topology")
                    .Select(t => t.Id)
                    .ToListAsync();

                var splitInfoes = await client.Queryable<SplitPanelInfo>()
                    .GroupBy(t => t.TopologyId)
                    .Select(s => new TopologyGroupInfo
                    {
                        TopologyId = s.TopologyId,
                        Timestamp = SqlFunc.AggregateMax(s.Timestamp)
                    })
                    .ToListAsync();

                foreach(var split in splitInfoes) 
                {
                    if (!topologyIds.Contains(split.TopologyId))
                    {
                        await client.Deleteable<SplitPanelInfo>()
                            .Where(s => s.TopologyId == split.TopologyId)
                            .ExecuteCommandAsync();
                    }
                    else
                    {
                        if (split.Timestamp <= ts)
                        {
                            await client.Deleteable<SplitPanelInfo>()
                                .Where(s => s.TopologyId == split.TopologyId && s.Timestamp < split.Timestamp)
                                .ExecuteCommandAsync();
                        }
                    }
                }
            }
        }
    }
}
