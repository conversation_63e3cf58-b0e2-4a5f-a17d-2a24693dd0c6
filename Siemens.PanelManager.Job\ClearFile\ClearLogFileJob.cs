﻿using Microsoft.Extensions.Logging;
using Quartz;
using Siemens.PanelManager.Common.Job;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.Job.ClearFile
{
    [DisallowConcurrentExecution]
    public class ClearLogFileJob : JobBase
    {
        private ILogger<ClearLogFileJob> _logger;
        public ClearLogFileJob(ILogger<ClearLogFileJob> logger)
        {
            _logger = logger;
        }
        public override string Name => "ClearLogFileJob";

        public override Task Execute()
        {
            var days = 7;
            if (ContextData.ContainsKey("Days"))
            {
                var config = ContextData["Days"];
                int newDays;
                if (int.TryParse(config, out newDays))
                {
                    days = newDays;
                }
            }
            var basePath = AppContext.BaseDirectory;
            var logPath = Path.Combine(basePath, "logs");
            var files = Directory.GetFiles(logPath, "*.log");
            var dateTimeList = new string[]
            {
                DateTime.Now.ToString("MMddHH"),
                DateTime.Now.AddHours(-1).ToString("MMddHH"),
                DateTime.Now.AddHours(-2).ToString("MMddHH"),
                DateTime.Now.AddHours(-3).ToString("MMddHH")
            };

            #region 清除 ApiRuntimeLog
            var dateStrList = new List<string>();
            for (var i = 0; i <= days; i++)
            {
                var date = DateTime.Today.AddDays(-i);
                dateStrList.Add(date.ToString("-MMdd-"));
            }
            #endregion

            foreach (var file in files) 
            {
                var fileInfo = new FileInfo(file);
                var match = Regex.Match(fileInfo.Name, "^([\\d]{4})-([\\d]{1,2})-([\\d]{1,2})");
                if (match.Success)
                {
                    var year = int.Parse(match.Groups[1].Value);
                    var month = int.Parse(match.Groups[2].Value);
                    var day = int.Parse(match.Groups[3].Value);

                    try
                    {
                        var dateTime = new DateTime(year, month, day);
                        if (dateTime <= DateTime.Today.AddDays(0 - days))
                        {
                            try
                            {
                                File.Delete(file);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError($"文件({fileInfo.Name})删除失败", ex);
                            }
                        }
                    }
                    catch
                    {
                    }
                    continue;
                }
                match = Regex.Match(fileInfo.Name, "^dataPoints_([\\d]{6})");
                if (match.Success)
                {
                    if (!dateTimeList.Contains(match.Groups[1].Value))
                    {
                        try
                        {
                            File.Delete(file);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError($"文件({fileInfo.Name})删除失败", ex);
                        }
                    }
                    continue;
                }

                #region 清除 ApiRuntimeLog
                if (!dateStrList.Any(x => fileInfo.Name.Contains(x)))
                {
                    try
                    {
                        File.Delete(file);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"文件({fileInfo.Name})删除失败", ex);
                    }
                    continue;
                }
                #endregion
            }

            return Task.CompletedTask;
        }
    }
}
