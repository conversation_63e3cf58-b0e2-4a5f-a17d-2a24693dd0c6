﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Auth
{
    [SugarTable("auth_role_page")]
    public class RolePageMapping : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsIdentity = true, IsPrimaryKey = true)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "role_id", IsNullable = false)]
        public int RoleId { get; set; }
        [SugarColumn(ColumnName = "page_id", IsNullable = false)]
        public int PageId { get; set; }
    }
}
