﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Asset
{
    [SugarTable("asset_device_harmonic_history")]
    public class DeviceHarmonicHistory : IPanelDataTable
    {
        [SugarColumn(ColumnName = "timestamp", IsPrimaryKey = true)]
        public long Ts { get; set; }
        [SugarColumn(ColumnName = "asset_id", IsPrimaryKey = true, IsNullable = false)]
        public int AssetId { get; set; }
        [SugarColumn(ColumnName = "ua_1", IsNullable = true)]
        public decimal? Ua1 { get; set; }
        [SugarColumn(ColumnName = "ub_1", IsNullable = true)]
        public decimal? Ub1 { get; set; }
        [SugarColumn(ColumnName = "uc_1", IsNullable = true)]
        public decimal? Uc1 { get; set; }
        [SugarColumn(ColumnName = "ua_2", IsNullable = true)]
        public decimal? Ua2 { get; set; }
        [SugarColumn(ColumnName = "ub_2", IsNullable = true)]
        public decimal? Ub2 { get; set; }

        [SugarColumn(ColumnName = "uc_2", IsNullable = true)]
        public decimal? Uc2 { get; set; }

        [SugarColumn(ColumnName = "ua_3", IsNullable = true)]
        public decimal? Ua3 { get; set; }

        [SugarColumn(ColumnName = "ub_3", IsNullable = true)]
        public decimal? Ub3 { get; set; }

        [SugarColumn(ColumnName = "uc_3", IsNullable = true)]
        public decimal? Uc3 { get; set; }

        [SugarColumn(ColumnName = "ua_4", IsNullable = true)]
        public decimal? Ua4 { get; set; }

        [SugarColumn(ColumnName = "ub_4", IsNullable = true)]
        public decimal? Ub4 { get; set; }

        [SugarColumn(ColumnName = "uc_4", IsNullable = true)]
        public decimal? Uc4 { get; set; }

        [SugarColumn(ColumnName = "ua_5", IsNullable = true)]
        public decimal? Ua5 { get; set; }

        [SugarColumn(ColumnName = "ub_5", IsNullable = true)]
        public decimal? Ub5 { get; set; }

        [SugarColumn(ColumnName = "uc_5", IsNullable = true)]
        public decimal? Uc5 { get; set; }

        [SugarColumn(ColumnName = "ua_6", IsNullable = true)]
        public decimal? Ua6 { get; set; }

        [SugarColumn(ColumnName = "ub_6", IsNullable = true)]
        public decimal? Ub6 { get; set; }

        [SugarColumn(ColumnName = "uc_6", IsNullable = true)]
        public decimal? Uc6 { get; set; }

        [SugarColumn(ColumnName = "ua_7", IsNullable = true)]
        public decimal? Ua7 { get; set; }

        [SugarColumn(ColumnName = "ub_7", IsNullable = true)]
        public decimal? Ub7 { get; set; }

        [SugarColumn(ColumnName = "uc_7", IsNullable = true)]
        public decimal? Uc7 { get; set; }

        [SugarColumn(ColumnName = "ua_8", IsNullable = true)]
        public decimal? Ua8 { get; set; }

        [SugarColumn(ColumnName = "ub_8", IsNullable = true)]
        public decimal? Ub8 { get; set; }

        [SugarColumn(ColumnName = "uc_8", IsNullable = true)]
        public decimal? Uc8 { get; set; }

        [SugarColumn(ColumnName = "ua_9", IsNullable = true)]
        public decimal? Ua9 { get; set; }

        [SugarColumn(ColumnName = "ub_9", IsNullable = true)]
        public decimal? Ub9 { get; set; }

        [SugarColumn(ColumnName = "uc_9", IsNullable = true)]
        public decimal? Uc9 { get; set; }

        [SugarColumn(ColumnName = "ua_10", IsNullable = true)]
        public decimal? Ua10 { get; set; }

        [SugarColumn(ColumnName = "ub_10", IsNullable = true)]
        public decimal? Ub10 { get; set; }

        [SugarColumn(ColumnName = "uc_10", IsNullable = true)]
        public decimal? Uc10 { get; set; }

        [SugarColumn(ColumnName = "ua_11", IsNullable = true)]
        public decimal? Ua11 { get; set; }

        [SugarColumn(ColumnName = "ub_11", IsNullable = true)]
        public decimal? Ub11 { get; set; }

        [SugarColumn(ColumnName = "uc_11", IsNullable = true)]
        public decimal? Uc11 { get; set; }

        [SugarColumn(ColumnName = "ua_12", IsNullable = true)]
        public decimal? Ua12 { get; set; }

        [SugarColumn(ColumnName = "ub_12", IsNullable = true)]
        public decimal? Ub12 { get; set; }

        [SugarColumn(ColumnName = "uc_12", IsNullable = true)]
        public decimal? Uc12 { get; set; }

        [SugarColumn(ColumnName = "ua_13", IsNullable = true)]
        public decimal? Ua13 { get; set; }

        [SugarColumn(ColumnName = "ub_13", IsNullable = true)]
        public decimal? Ub13 { get; set; }

        [SugarColumn(ColumnName = "uc_13", IsNullable = true)]
        public decimal? Uc13 { get; set; }

        [SugarColumn(ColumnName = "ua_14", IsNullable = true)]
        public decimal? Ua14 { get; set; }

        [SugarColumn(ColumnName = "ub_14", IsNullable = true)]
        public decimal? Ub14 { get; set; }

        [SugarColumn(ColumnName = "uc_14", IsNullable = true)]
        public decimal? Uc14 { get; set; }

        [SugarColumn(ColumnName = "ua_15", IsNullable = true)]
        public decimal? Ua15 { get; set; }

        [SugarColumn(ColumnName = "ub_15", IsNullable = true)]
        public decimal? Ub15 { get; set; }

        [SugarColumn(ColumnName = "uc_15", IsNullable = true)]
        public decimal? Uc15 { get; set; }

        [SugarColumn(ColumnName = "ua_16", IsNullable = true)]
        public decimal? Ua16 { get; set; }

        [SugarColumn(ColumnName = "ub_16", IsNullable = true)]
        public decimal? Ub16 { get; set; }

        [SugarColumn(ColumnName = "uc_16", IsNullable = true)]
        public decimal? Uc16 { get; set; }

        [SugarColumn(ColumnName = "ua_17", IsNullable = true)]
        public decimal? Ua17 { get; set; }

        [SugarColumn(ColumnName = "ub_17", IsNullable = true)]
        public decimal? Ub17 { get; set; }

        [SugarColumn(ColumnName = "uc_17", IsNullable = true)]
        public decimal? Uc17 { get; set; }

        [SugarColumn(ColumnName = "ua_18", IsNullable = true)]
        public decimal? Ua18 { get; set; }

        [SugarColumn(ColumnName = "ub_18", IsNullable = true)]
        public decimal? Ub18 { get; set; }

        [SugarColumn(ColumnName = "uc_18", IsNullable = true)]
        public decimal? Uc18 { get; set; }

        [SugarColumn(ColumnName = "ua_19", IsNullable = true)]
        public decimal? Ua19 { get; set; }

        [SugarColumn(ColumnName = "ub_19", IsNullable = true)]
        public decimal? Ub19 { get; set; }

        [SugarColumn(ColumnName = "uc_19", IsNullable = true)]
        public decimal? Uc19 { get; set; }

        [SugarColumn(ColumnName = "ua_20", IsNullable = true)]
        public decimal? Ua20 { get; set; }

        [SugarColumn(ColumnName = "ub_20", IsNullable = true)]
        public decimal? Ub20 { get; set; }

        [SugarColumn(ColumnName = "uc_20", IsNullable = true)]
        public decimal? Uc20 { get; set; }

        [SugarColumn(ColumnName = "ua_21", IsNullable = true)]
        public decimal? Ua21 { get; set; }

        [SugarColumn(ColumnName = "ub_21", IsNullable = true)]
        public decimal? Ub21 { get; set; }

        [SugarColumn(ColumnName = "uc_21", IsNullable = true)]
        public decimal? Uc21 { get; set; }

        [SugarColumn(ColumnName = "ua_22", IsNullable = true)]
        public decimal? Ua22 { get; set; }

        [SugarColumn(ColumnName = "ub_22", IsNullable = true)]
        public decimal? Ub22 { get; set; }

        [SugarColumn(ColumnName = "uc_22", IsNullable = true)]
        public decimal? Uc22 { get; set; }

        [SugarColumn(ColumnName = "ua_23", IsNullable = true)]
        public decimal? Ua23 { get; set; }

        [SugarColumn(ColumnName = "ub_23", IsNullable = true)]
        public decimal? Ub23 { get; set; }

        [SugarColumn(ColumnName = "uc_23", IsNullable = true)]
        public decimal? Uc23 { get; set; }

        [SugarColumn(ColumnName = "ua_24", IsNullable = true)]
        public decimal? Ua24 { get; set; }

        [SugarColumn(ColumnName = "ub_24", IsNullable = true)]
        public decimal? Ub24 { get; set; }

        [SugarColumn(ColumnName = "uc_24", IsNullable = true)]
        public decimal? Uc24 { get; set; }

        [SugarColumn(ColumnName = "ua_25", IsNullable = true)]
        public decimal? Ua25 { get; set; }

        [SugarColumn(ColumnName = "ub_25", IsNullable = true)]
        public decimal? Ub25 { get; set; }

        [SugarColumn(ColumnName = "uc_25", IsNullable = true)]
        public decimal? Uc25 { get; set; }

        [SugarColumn(ColumnName = "ua_26", IsNullable = true)]
        public decimal? Ua26 { get; set; }

        [SugarColumn(ColumnName = "ub_26", IsNullable = true)]
        public decimal? Ub26 { get; set; }

        [SugarColumn(ColumnName = "uc_26", IsNullable = true)]
        public decimal? Uc26 { get; set; }

        [SugarColumn(ColumnName = "ua_27", IsNullable = true)]
        public decimal? Ua27 { get; set; }

        [SugarColumn(ColumnName = "ub_27", IsNullable = true)]
        public decimal? Ub27 { get; set; }

        [SugarColumn(ColumnName = "uc_27", IsNullable = true)]
        public decimal? Uc27 { get; set; }

        [SugarColumn(ColumnName = "ua_28", IsNullable = true)]
        public decimal? Ua28 { get; set; }

        [SugarColumn(ColumnName = "ub_28", IsNullable = true)]
        public decimal? Ub28 { get; set; }

        [SugarColumn(ColumnName = "uc_28", IsNullable = true)]
        public decimal? Uc28 { get; set; }

        [SugarColumn(ColumnName = "ua_29", IsNullable = true)]
        public decimal? Ua29 { get; set; }

        [SugarColumn(ColumnName = "ub_29", IsNullable = true)]
        public decimal? Ub29 { get; set; }

        [SugarColumn(ColumnName = "uc_29", IsNullable = true)]
        public decimal? Uc29 { get; set; }

        [SugarColumn(ColumnName = "ua_30", IsNullable = true)]
        public decimal? Ua30 { get; set; }

        [SugarColumn(ColumnName = "ub_30", IsNullable = true)]
        public decimal? Ub30 { get; set; }

        [SugarColumn(ColumnName = "uc_30", IsNullable = true)]
        public decimal? Uc30 { get; set; }

        [SugarColumn(ColumnName = "ua_31", IsNullable = true)]
        public decimal? Ua31 { get; set; }

        [SugarColumn(ColumnName = "ub_31", IsNullable = true)]
        public decimal? Ub31 { get; set; }

        [SugarColumn(ColumnName = "uc_31", IsNullable = true)]
        public decimal? Uc31 { get; set; }

        [SugarColumn(ColumnName = "ua_32", IsNullable = true)]
        public decimal? Ua32 { get; set; }

        [SugarColumn(ColumnName = "ub_32", IsNullable = true)]
        public decimal? Ub32 { get; set; }

        [SugarColumn(ColumnName = "uc_32", IsNullable = true)]
        public decimal? Uc32 { get; set; }

        [SugarColumn(ColumnName = "ua_33", IsNullable = true)]
        public decimal? Ua33 { get; set; }

        [SugarColumn(ColumnName = "ub_33", IsNullable = true)]
        public decimal? Ub33 { get; set; }

        [SugarColumn(ColumnName = "uc_33", IsNullable = true)]
        public decimal? Uc33 { get; set; }

        [SugarColumn(ColumnName = "ua_34", IsNullable = true)]
        public decimal? Ua34 { get; set; }

        [SugarColumn(ColumnName = "ub_34", IsNullable = true)]
        public decimal? Ub34 { get; set; }

        [SugarColumn(ColumnName = "uc_34", IsNullable = true)]
        public decimal? Uc34 { get; set; }

        [SugarColumn(ColumnName = "ua_35", IsNullable = true)]
        public decimal? Ua35 { get; set; }

        [SugarColumn(ColumnName = "ub_35", IsNullable = true)]
        public decimal? Ub35 { get; set; }

        [SugarColumn(ColumnName = "uc_35", IsNullable = true)]
        public decimal? Uc35 { get; set; }

        [SugarColumn(ColumnName = "ua_36", IsNullable = true)]
        public decimal? Ua36 { get; set; }

        [SugarColumn(ColumnName = "ub_36", IsNullable = true)]
        public decimal? Ub36 { get; set; }

        [SugarColumn(ColumnName = "uc_36", IsNullable = true)]
        public decimal? Uc36 { get; set; }

        [SugarColumn(ColumnName = "ua_37", IsNullable = true)]
        public decimal? Ua37 { get; set; }

        [SugarColumn(ColumnName = "ub_37", IsNullable = true)]
        public decimal? Ub37 { get; set; }

        [SugarColumn(ColumnName = "uc_37", IsNullable = true)]
        public decimal? Uc37 { get; set; }

        [SugarColumn(ColumnName = "ua_38", IsNullable = true)]
        public decimal? Ua38 { get; set; }

        [SugarColumn(ColumnName = "ub_38", IsNullable = true)]
        public decimal? Ub38 { get; set; }

        [SugarColumn(ColumnName = "uc_38", IsNullable = true)]
        public decimal? Uc38 { get; set; }

        [SugarColumn(ColumnName = "ua_39", IsNullable = true)]
        public decimal? Ua39 { get; set; }

        [SugarColumn(ColumnName = "ub_39", IsNullable = true)]
        public decimal? Ub39 { get; set; }

        [SugarColumn(ColumnName = "uc_39", IsNullable = true)]
        public decimal? Uc39 { get; set; }

        [SugarColumn(ColumnName = "ua_40", IsNullable = true)]
        public decimal? Ua40 { get; set; }

        [SugarColumn(ColumnName = "ub_40", IsNullable = true)]
        public decimal? Ub40 { get; set; }

        [SugarColumn(ColumnName = "uc_40", IsNullable = true)]
        public decimal? Uc40 { get; set; }

        [SugarColumn(ColumnName = "ua_41", IsNullable = true)]
        public decimal? Ua41 { get; set; }

        [SugarColumn(ColumnName = "ub_41", IsNullable = true)]
        public decimal? Ub41 { get; set; }

        [SugarColumn(ColumnName = "uc_41", IsNullable = true)]
        public decimal? Uc41 { get; set; }

        [SugarColumn(ColumnName = "ua_42", IsNullable = true)]
        public decimal? Ua42 { get; set; }

        [SugarColumn(ColumnName = "ub_42", IsNullable = true)]
        public decimal? Ub42 { get; set; }

        [SugarColumn(ColumnName = "uc_42", IsNullable = true)]
        public decimal? Uc42 { get; set; }

        [SugarColumn(ColumnName = "ua_43", IsNullable = true)]
        public decimal? Ua43 { get; set; }

        [SugarColumn(ColumnName = "ub_43", IsNullable = true)]
        public decimal? Ub43 { get; set; }

        [SugarColumn(ColumnName = "uc_43", IsNullable = true)]
        public decimal? Uc43 { get; set; }

        [SugarColumn(ColumnName = "ua_44", IsNullable = true)]
        public decimal? Ua44 { get; set; }

        [SugarColumn(ColumnName = "ub_44", IsNullable = true)]
        public decimal? Ub44 { get; set; }

        [SugarColumn(ColumnName = "uc_44", IsNullable = true)]
        public decimal? Uc44 { get; set; }

        [SugarColumn(ColumnName = "ua_45", IsNullable = true)]
        public decimal? Ua45 { get; set; }

        [SugarColumn(ColumnName = "ub_45", IsNullable = true)]
        public decimal? Ub45 { get; set; }

        [SugarColumn(ColumnName = "uc_45", IsNullable = true)]
        public decimal? Uc45 { get; set; }

        [SugarColumn(ColumnName = "ua_46", IsNullable = true)]
        public decimal? Ua46 { get; set; }

        [SugarColumn(ColumnName = "ub_46", IsNullable = true)]
        public decimal? Ub46 { get; set; }

        [SugarColumn(ColumnName = "uc_46", IsNullable = true)]
        public decimal? Uc46 { get; set; }

        [SugarColumn(ColumnName = "ua_47", IsNullable = true)]
        public decimal? Ua47 { get; set; }

        [SugarColumn(ColumnName = "ub_47", IsNullable = true)]
        public decimal? Ub47 { get; set; }

        [SugarColumn(ColumnName = "uc_47", IsNullable = true)]
        public decimal? Uc47 { get; set; }

        [SugarColumn(ColumnName = "ua_48", IsNullable = true)]
        public decimal? Ua48 { get; set; }

        [SugarColumn(ColumnName = "ub_48", IsNullable = true)]
        public decimal? Ub48 { get; set; }

        [SugarColumn(ColumnName = "uc_48", IsNullable = true)]
        public decimal? Uc48 { get; set; }

        [SugarColumn(ColumnName = "ua_49", IsNullable = true)]
        public decimal? Ua49 { get; set; }

        [SugarColumn(ColumnName = "ub_49", IsNullable = true)]
        public decimal? Ub49 { get; set; }

        [SugarColumn(ColumnName = "uc_49", IsNullable = true)]
        public decimal? Uc49 { get; set; }

        [SugarColumn(ColumnName = "ua_50", IsNullable = true)]
        public decimal? Ua50 { get; set; }

        [SugarColumn(ColumnName = "ub_50", IsNullable = true)]
        public decimal? Ub50 { get; set; }

        [SugarColumn(ColumnName = "uc_50", IsNullable = true)]
        public decimal? Uc50 { get; set; }

        [SugarColumn(ColumnName = "ua_51", IsNullable = true)]
        public decimal? Ua51 { get; set; }

        [SugarColumn(ColumnName = "ub_51", IsNullable = true)]
        public decimal? Ub51 { get; set; }

        [SugarColumn(ColumnName = "uc_51", IsNullable = true)]
        public decimal? Uc51 { get; set; }

        [SugarColumn(ColumnName = "ua_52", IsNullable = true)]
        public decimal? Ua52 { get; set; }

        [SugarColumn(ColumnName = "ub_52", IsNullable = true)]
        public decimal? Ub52 { get; set; }

        [SugarColumn(ColumnName = "uc_52", IsNullable = true)]
        public decimal? Uc52 { get; set; }

        [SugarColumn(ColumnName = "ua_53", IsNullable = true)]
        public decimal? Ua53 { get; set; }

        [SugarColumn(ColumnName = "ub_53", IsNullable = true)]
        public decimal? Ub53 { get; set; }

        [SugarColumn(ColumnName = "uc_53", IsNullable = true)]
        public decimal? Uc53 { get; set; }

        [SugarColumn(ColumnName = "ua_54", IsNullable = true)]
        public decimal? Ua54 { get; set; }

        [SugarColumn(ColumnName = "ub_54", IsNullable = true)]
        public decimal? Ub54 { get; set; }

        [SugarColumn(ColumnName = "uc_54", IsNullable = true)]
        public decimal? Uc54 { get; set; }

        [SugarColumn(ColumnName = "ua_55", IsNullable = true)]
        public decimal? Ua55 { get; set; }

        [SugarColumn(ColumnName = "ub_55", IsNullable = true)]
        public decimal? Ub55 { get; set; }

        [SugarColumn(ColumnName = "uc_55", IsNullable = true)]
        public decimal? Uc55 { get; set; }

        [SugarColumn(ColumnName = "ua_56", IsNullable = true)]
        public decimal? Ua56 { get; set; }

        [SugarColumn(ColumnName = "ub_56", IsNullable = true)]
        public decimal? Ub56 { get; set; }

        [SugarColumn(ColumnName = "uc_56", IsNullable = true)]
        public decimal? Uc56 { get; set; }

        [SugarColumn(ColumnName = "ua_57", IsNullable = true)]
        public decimal? Ua57 { get; set; }

        [SugarColumn(ColumnName = "ub_57", IsNullable = true)]
        public decimal? Ub57 { get; set; }

        [SugarColumn(ColumnName = "uc_57", IsNullable = true)]
        public decimal? Uc57 { get; set; }

        [SugarColumn(ColumnName = "ua_58", IsNullable = true)]
        public decimal? Ua58 { get; set; }

        [SugarColumn(ColumnName = "ub_58", IsNullable = true)]
        public decimal? Ub58 { get; set; }

        [SugarColumn(ColumnName = "uc_58", IsNullable = true)]
        public decimal? Uc58 { get; set; }

        [SugarColumn(ColumnName = "ua_59", IsNullable = true)]
        public decimal? Ua59 { get; set; }

        [SugarColumn(ColumnName = "ub_59", IsNullable = true)]
        public decimal? Ub59 { get; set; }

        [SugarColumn(ColumnName = "uc_59", IsNullable = true)]
        public decimal? Uc59 { get; set; }

        [SugarColumn(ColumnName = "ua_60", IsNullable = true)]
        public decimal? Ua60 { get; set; }

        [SugarColumn(ColumnName = "ub_60", IsNullable = true)]
        public decimal? Ub60 { get; set; }

        [SugarColumn(ColumnName = "uc_60", IsNullable = true)]
        public decimal? Uc60 { get; set; }

        [SugarColumn(ColumnName = "ua_61", IsNullable = true)]
        public decimal? Ua61 { get; set; }

        [SugarColumn(ColumnName = "ub_61", IsNullable = true)]
        public decimal? Ub61 { get; set; }

        [SugarColumn(ColumnName = "uc_61", IsNullable = true)]
        public decimal? Uc61 { get; set; }

        [SugarColumn(ColumnName = "ua_62", IsNullable = true)]
        public decimal? Ua62 { get; set; }

        [SugarColumn(ColumnName = "ub_62", IsNullable = true)]
        public decimal? Ub62 { get; set; }

        [SugarColumn(ColumnName = "uc_62", IsNullable = true)]
        public decimal? Uc62 { get; set; }

        [SugarColumn(ColumnName = "ua_63", IsNullable = true)]
        public decimal? Ua63 { get; set; }

        [SugarColumn(ColumnName = "ub_63", IsNullable = true)]
        public decimal? Ub63 { get; set; }

        [SugarColumn(ColumnName = "uc_63", IsNullable = true)]
        public decimal? Uc63 { get; set; }

        [SugarColumn(ColumnName = "ua_64", IsNullable = true)]
        public decimal? Ua64 { get; set; }

        [SugarColumn(ColumnName = "ub_64", IsNullable = true)]
        public decimal? Ub64 { get; set; }

        [SugarColumn(ColumnName = "uc_64", IsNullable = true)]
        public decimal? Uc64 { get; set; }
    }
}
