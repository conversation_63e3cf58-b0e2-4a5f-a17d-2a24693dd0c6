﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Asset
{
    [SugarTable("asset_third_gateway")]
    public class AssetThirdGateway : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "asset_id", IsNullable = true)]
        public int? AssetId { get; set; }
        [SugarColumn(ColumnName = "name", IsNullable = true, Length = 50)]
        public string? Name { get; set; }
        [SugarColumn(ColumnName = "port", IsNullable = true, Length = 50)]
        public string? Port { get; set; }
    }
}
