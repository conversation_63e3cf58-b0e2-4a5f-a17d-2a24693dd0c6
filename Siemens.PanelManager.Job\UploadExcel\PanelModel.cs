﻿using Siemens.PanelManager.Model.Database.Asset;

namespace Siemens.PanelManager.Job.UploadExcel
{
    internal class PanelModel : IPanelModel
    {
        public PanelModel(AssetInfo assetInfo, int index)
        {
            AssetInfo = assetInfo;
            Index = index;
        }
        public int Index { get; private set; }
        public AssetInfo AssetInfo { get; set; }

        public int Height { get; set; } = 2200;
        public int Width { get; set; } = 800;
        public int LineNo { get; set; } = 1;
        public int RowNo { get; set; } = 1;
        public string? BusbarStructure { get; set; }
        public AssetInfo? ParentAsset { get; set; }
        public List<CircuitModel> SubCircuits { get; set;} = new List<CircuitModel>();

        public string[] BusBars
        {
            get
            {
                if (string.IsNullOrEmpty(BusBarStr)) return new string[0];
                return BusBarStr.Split(',');
            }
        }
    }
}
