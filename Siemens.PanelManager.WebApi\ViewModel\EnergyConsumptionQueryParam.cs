﻿using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;
using System.Xml.Linq;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class EnergyConsumptionQueryParam : EnergyQueryParamBase
    {
        /// <summary>
        /// Day:0,Month:1,Year:2,Custom:3
        /// </summary>
        [FromQuery(Name = "dateType")]
        public ChartDateType DateType { get; set; }

        [FromQuery(Name = "startDate")]
        public string? StartDate { get; set; }

        [FromQuery(Name = "endDate")]
        public string? EndDate { get; set; }

        /// <summary>
        /// Year(同比):0,Chain(环比):1
        /// </summary>
        [FromQuery(Name = "compareType")]
        public ChartCompareType CompareType { get; set; }

        /// <summary>
        /// Electricity(耗电):0,Fee(费用):1
        /// </summary>
        [FromQuery(Name = "chartDataType")]
        public ChartDataType ChartDataType { get; set; }
    }

    public enum ChartDateType
    {
        [Description("日")]
        Day,
        [Description("月")]
        Month,
        [Description("年")]
        Year,
        [Description("自定义")]
        Custom,
        Get24Hour
    }

    public enum ChartCompareType
    {
        Year,
        Chain
    }

    public enum ChartDataType
    {
        Electricity,
        Fee
    }
}
