﻿namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class GatewaySubDevice
    {
        public int ConnectStatus { get; set; }
        public int AssetId { get; set; }
        public string AssetName { get; set; } = string.Empty;
        public string CircuitName { get; set; } = string.Empty;
        public string DeviceAddress { get; set; } = string.Empty;
        public string DevicePosition { get; set; } = string.Empty;
        public string OrderNo { get; set; } = string.Empty;
        public string SerialNo { get; set; } = string.Empty;
        public string DeviceNo { get; set; } = string.Empty;
        public string PositionNo { get; set; } = string.Empty;
    }
}
