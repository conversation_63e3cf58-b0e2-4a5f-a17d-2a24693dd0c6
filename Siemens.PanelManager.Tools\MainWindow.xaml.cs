﻿using Newtonsoft.Json;
using Siemens.PanelManager.Tools.HttpService;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace Siemens.PanelManager.Tools
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {

        private async Task UpdateWinodowDataAsync()
        {
            if (webView.CoreWebView2 != null)
            {
                await webView.CoreWebView2.ExecuteScriptAsync($"sessionStorage.setItem('isSettings','{(string.IsNullOrEmpty(StaticInfo.Settings.Ip) ? "0" : "1")}')");
                await webView.CoreWebView2.ExecuteScriptAsync($"sessionStorage.setItem('isConnected','{(StaticInfo.ConnectStatus != null && StaticInfo.ConnectStatus.Client.IsConneted ? "1" : "0")}')");
            }
        }

        public void UpdateWinodowData()
        {
            Dispatcher.BeginInvoke(UpdateWinodowDataAsync);
        }

        private async Task SetUpgradeStepAsync()
        {
            if (webView.CoreWebView2 != null)
            {
                await webView.CoreWebView2.ExecuteScriptAsync($"sessionStorage.setItem('upgradeSteps','{(StaticInfo.UpgradeStatus == null ? "[]" : JsonConvert.SerializeObject(StaticInfo.UpgradeStatus.Steps))}')");
            }
        }
        public void SetUpgradeStep()
        {
            Dispatcher.BeginInvoke(SetUpgradeStepAsync);
        }

        public MainWindow()
        {
            
        }
        protected override void OnInitialized(EventArgs e)
        {
            base.OnInitialized(e);
            
            StaticInfo.ConnectStatusChange = UpdateWinodowData;
            StaticInfo.SetUpgradeSteps = SetUpgradeStep;
        }


        protected override void OnClosing(CancelEventArgs e)
        {
            StaticInfo.ConnectStatusChange = ()=> { };
            StaticInfo.SetUpgradeSteps = () => { };
            base.OnClosing(e);
        }

        private void Window_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            webView.CoreWebView2?.Reload();
        }

        private async void webView_NavigationCompleted(object sender, Microsoft.Web.WebView2.Core.CoreWebView2NavigationCompletedEventArgs e)
        {
            await UpdateWinodowDataAsync();
            await SetUpgradeStepAsync();
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            Left = 0;
            Top = 0;
            Width = SystemParameters.PrimaryScreenWidth;
            Height = SystemParameters.PrimaryScreenHeight - 30;
        }

        private async void webView_Initialized(object sender, EventArgs e)
        {
            await webView.EnsureCoreWebView2Async();
            webView.CoreWebView2.Settings.AreDefaultContextMenusEnabled = false;
            await webView.CoreWebView2.ExecuteScriptAsync("window.addEventListener('contextmenu', window => {window.preventDefault();});");
            await webView.CoreWebView2.ExecuteScriptAsync("window.addEventListener('drop',function(e){" +
            "e.preventDefault();" +
            "}, false);");
            webView.CoreWebView2.Navigate(Server.Url + "index.html");
            
        }
    }
}
