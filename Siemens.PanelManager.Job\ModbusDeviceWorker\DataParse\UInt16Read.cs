﻿using System;
using System.Buffers.Binary;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.ModbusDeviceWorker.DataParse
{
    public class UInt16Read : PanelModbusRead
    {
        public UInt16Read(bool isBigEndian, string? parseMode, float factor, float customFactor, float intercept)
            : base(isBigEndian, parseMode, factor, customFactor, intercept)
        {
        }

        public override string ReadData(ReadOnlySpan<byte> source)
        {
            //uint16只占用一个寄存器，所以只有两种情况，例如高位 低位(AB) 或者 低位 高位(BA)
            string readValue = string.Empty;
            if (!string.IsNullOrEmpty(ParseMode))
            {
                if (ParseMode == "ABCD")
                {
                    readValue = Calculate(BinaryPrimitives.ReadUInt16BigEndian(source));
                }
                else if (ParseMode == "DCBA")
                {
                    readValue = Calculate(BinaryPrimitives.ReadUInt16LittleEndian(source));
                }
                else
                {
                    // 剩余的两种情况在一个寄存器情况下，不存在
                    if (IsBigEndian)
                    {
                        readValue = Calculate(BinaryPrimitives.ReadUInt16BigEndian(source));
                    }
                    else
                    {
                        readValue = Calculate(BinaryPrimitives.ReadUInt16LittleEndian(source));
                    }
                }
            }
            else
            {
                if (IsBigEndian)
                {
                    readValue = Calculate(BinaryPrimitives.ReadUInt16BigEndian(source));
                }
                else
                {
                    readValue = Calculate(BinaryPrimitives.ReadUInt16LittleEndian(source));
                }
            }

            return readValue;
        }
    }
}
