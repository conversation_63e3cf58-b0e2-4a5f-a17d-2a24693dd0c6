﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components.Server.Circuits;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Common.Model;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Chart;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.Model.UDC;
using Siemens.PanelManager.Server.Asset;
using Siemens.PanelManager.Server.DataPoint;
using Siemens.PanelManager.Server.Energy;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;
using System.Collections.Generic;
using System.Text;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v1/status")]
    [ApiController]
    public class AssetStatusController : SiemensApiControllerBase, IDisposable
    {
        private const string AssetCurrentStatusCacheKey = "AssetStatus:Currently-{0}";
        private const string CurrentDayEnergyCacheKey = "Energy:CurrentDay-{0}";
        private const string AssetAllDataCacheKey = "AssetStatus:All-{0}";
        private ILogger<AssetStatusController> _log;
        private readonly ISqlSugarClient _client;
        private readonly IServiceProvider _provider;
        private readonly SiemensCache _cache;
        /// <summary>
        /// 异步锁
        /// </summary>
        private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1);

        /// <summary>
        /// 构造函数初始化
        /// </summary>
        /// <param name="cache"></param>
        /// <param name="log"></param>
        /// <param name="provider"></param>
        public AssetStatusController(
           SiemensCache cache,
           ILogger<AssetStatusController> log,
           IServiceProvider provider)
           : base(provider, cache)
        {
            _client = provider.GetService<ISqlSugarClient>()!;
            _log = log;
            _provider = provider;
            _cache = cache;
        }

        [HttpGet("{assetId}/getchartdata")]
        [SwaggerOperation(Summary = "Swagger_AssetStatus_GetChartData", Description = "Swagger_AssetStatus_GetChartData_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<Dictionary<string, IChart?>>> GetChartData(int assetId, [FromQuery] ChartParam param)
        {
            DateTime startDate = DateTime.Today;
            DateTime endDate = DateTime.Today;
            if (assetId <= 0 || param == null ||
                !param.DateType.HasValue ||
                string.IsNullOrEmpty(param.ChartCodes))
            {
                return new ResponseBase<Dictionary<string, IChart?>>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            var chartCodes = param.ChartCodes.Split(',');
            var assetInfo = _cache.Get<AssetSimpleInfo>($"Asset:SimpleInfo-{assetId}");
            if (assetInfo == null)
            {
                var emptyResult = new Dictionary<string, IChart?>();

                foreach (var name in chartCodes)
                {
                    emptyResult.TryAdd(name, null);
                }

                return new ResponseBase<Dictionary<string, IChart?>>()
                {
                    Code = 20000,
                    Data = emptyResult
                };
            }

            var targetAssetId = assetId;

            if (assetInfo.SourceAssetId.HasValue)
            {
                if (assetInfo.AssetLevel == AssetLevel.Circuit)
                {
                    targetAssetId = assetInfo.SourceAssetId.Value;
                }
                else if (assetInfo.AssetLevel == AssetLevel.Panel)
                {
                    var circuit = _cache.Get<AssetSimpleInfo>($"Asset:SimpleInfo-{assetInfo.SourceAssetId.Value}");
                    if (circuit != null && circuit.SourceAssetId.HasValue)
                    {
                        targetAssetId = circuit.SourceAssetId.Value;
                    }
                }
            }

            var paramList = new Dictionary<string, string>()
            {
                ["ChartDateType"] = ((int)param.DateType).ToString(),
                ["AssetId"] = targetAssetId.ToString(),
            };

            if (!string.IsNullOrEmpty(param.StartDate))
            {
                paramList.Add("StartDate", param.StartDate);
            }
            if (!string.IsNullOrEmpty(param.EndDate))
            {
                paramList.Add("EndDate", param.EndDate);
            }

            DeviceDetails? details = null;

            if (chartCodes.Length > 0 && chartCodes.Any(c => "LoadRate".Equals(c) || "CapacityAnalysis".Equals(c)))
            {
                details = await _client.Queryable<DeviceDetails>().FirstAsync(d => d.AssetId == assetId);
            }

            var result = new Dictionary<string, IChart?>();
            var service = _provider.GetRequiredService<AssetDashboardServer>();
            foreach (var name in chartCodes)
            {
                var queryName = name;
                IChart? chart = null;
                try
                {
                    var f = new Dictionary<string, string>(paramList);

                    if ("LoadRate".Equals(name))
                    {
                        if (details == null || !details.RatedCurrent.HasValue)
                        {
                            result.Add(name, new LineChartModel()
                            {
                                X = new string[0]
                            });
                            continue;
                        }
                        f.Add("RatedCurrent", details.RatedCurrent.Value.ToString());

                        AssetSimpleInfo deviceAsset;
                        if(assetId == targetAssetId)
                        {
                            deviceAsset = assetInfo;
                        }
                        else
                        {
                            var temp = _cache.Get<AssetSimpleInfo>($"Asset:SimpleInfo-{targetAssetId}");
                            if (temp == null)
                            {
                                continue;
                            }

                            deviceAsset = temp;
                        }

                        if (deviceAsset.AssetModel == "Other")
                        {
                            queryName = "LoadRate_ThirdPart";
                        }
                    }

                    chart = await service.GetDashboard(queryName, MessageContext, f, _client);

                    // 容量分析要除以1000
                    if (name.Equals("CapacityAnalysis"))
                    {

                        var capacityAnalysisData = chart as LineChartModel;

                        if (capacityAnalysisData != null && capacityAnalysisData.Y1 != null && capacityAnalysisData.Y1.Any())
                        {
                            for (int i = 0; i < capacityAnalysisData.Y1.Length; i++)
                            {
                                capacityAnalysisData.Y1[i] = capacityAnalysisData.Y1[i] / 1000;
                            }

                            chart = capacityAnalysisData;
                        }
                    }

                }
                catch (Exception ex)
                {
                    _log.LogError(ex, "获取报表报错");
                }

                result.Add(name, chart);
            }

            return new ResponseBase<Dictionary<string, IChart?>>()
            {
                Code = 20000,
                Data = result
            };
        }

        /// <summary>
        /// 伪造空数组
        /// </summary>
        /// <returns></returns>
        private LineChartModel GetChartByObj()
        {
            //伪造数组
            var obj = new LineChartModel();
            obj.X = new string[96];
            obj.Y1 = new decimal[96];

            string startTime = "00:00:00";

            for (int i = 0; i < 96; i++)
            {
                DateTime dateTime = Convert.ToDateTime(startTime).AddMinutes(i * 15);

                string endTime = dateTime.ToString("HH:mm:ss");

                obj.X![i] = endTime;

                obj.Y1![i] = 0;
            }

            return obj;
        }

        [HttpGet("CircuitTemp/{id}")]
        [SwaggerOperation(Summary = "Swagger_AssetStatus_CircuitTemp", Description = "Swagger_AssetStatus_CircuitTemp_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<LineChartModel>> GetCircuitTemp(int id)
        {
            if (id <= 0)
            {
                return new ResponseBase<LineChartModel>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            if (await _client.Queryable<AssetInfo>()
                .AnyAsync(a => a.Id == id && a.AssetLevel == AssetLevel.Circuit))
            {
                var dataPointServer = _provider.GetRequiredService<DataPointServer>();
                var dataPoints = await dataPointServer.GetDataPointInfos(AssetLevel.Circuit);
                var timePoints = dataPoints.Where(m => m.FilterIds != null && m.FilterIds.Contains("[T]")).Select(m => m.Code).ToArray();
                var currentStatus = _cache.GetHashData(string.Format(AssetCurrentStatusCacheKey, id), timePoints);
                var y1 = new List<decimal>();
                var x = new List<string>();

                foreach (var status in currentStatus)
                {
                    var temp = 0m;
                    decimal.TryParse(status.Value, out temp);
                    x.Add(status.Key);
                    y1.Add(temp);
                }
                LineChartModel data;
                if (x.Count > 0)
                {
                    data = new LineChartModel()
                    {
                        XColumn = "temperatureType",
                        YColumns = new List<string> { "value" },
                        Y1 = y1.ToArray(),
                        X = x.ToArray(),
                        Standard = 75
                    };

                    var messageContext = MessageContext;
                    if (data.X != null)
                    {
                        for (int i = 0; i < data.X.Length; i++)
                        {
                            data.X[i] = messageContext.GetDataPointName(data.X[i]);
                        }
                    }
                }
                else
                {
                    data = new LineChartModel()
                    {
                        XColumn = "temperatureType",
                        YColumns = new List<string> { "value" },
                        Y1 = new decimal[] { 0m, 0m, 0m },
                        X = new string[] { MessageContext.GetDataPointName("APhaseTemp1"), MessageContext.GetDataPointName("BPhaseTemp1"), MessageContext.GetDataPointName("CPhaseTemp1") },
                        Standard = 75
                    };
                }

                return new ResponseBase<LineChartModel>()
                {
                    Code = 20000,
                    Data = data
                };
            }

            return new ResponseBase<LineChartModel>()
            {
                Code = 40400,
                Message = MessageContext.AssetNotExists
            };
        }

        [HttpGet("Currently/{assetId}")]
        [SwaggerOperation(Summary = "Swagger_AssetStatus_Currently", Description = "Swagger_AssetStatus_Currently_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> GetCurrently(int assetId)
        {
            try
            {
                if (!await _client.Queryable<AssetInfo>().AnyAsync(p => p.Id == assetId))
                {
                    return Ok(new ResponseBase<Dictionary<string, string>>() { Code = 20000, Data = new Dictionary<string, string>() });
                }

                var currentlyStatus = _cache.GetHashAllData(string.Format(AssetCurrentStatusCacheKey, assetId));
                return Ok(new ResponseBase<Dictionary<string, string>>() { Code = 20000, Data = currentlyStatus });
            }
            catch (Exception)
            {
                return Ok(new ResponseBase<Dictionary<string, string>>() { Code = 50000, Message = MessageContext.ServerException });
            }
        }

        [HttpGet("Currentlys")]
        [SwaggerOperation(Summary = "Swagger_AssetStatus_Currently", Description = "Swagger_AssetStatus_Currently_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> GetCurrentlys()
        {
            Dictionary<int, Dictionary<string, string>> result = new Dictionary<int, Dictionary<string, string>>();
            try
            {
                var assetIds = await _client.Queryable<AssetInfo>().Where(a => a.AssetLevel == AssetLevel.Device)
                    .Select(a => a.Id).ToListAsync();

                foreach (int assetId in assetIds)
                {
                    var currentlyStatus = _cache.GetHashAllData(string.Format(AssetCurrentStatusCacheKey, assetId));
                    if (currentlyStatus != null)
                    {
                        result.Add(assetId, currentlyStatus);
                    }
                    
                }
                return Ok(new ResponseBase<Dictionary<int, Dictionary<string, string>>>() { Code = 20000, Data= result });
            }
            catch (Exception ex)
            {
                return Ok(new ResponseBase<Dictionary<int, Dictionary<string, string>>>() { Code = 50000, Message = ex.Message });
            }
        }
        /// <summary>
        /// 通用设备数据
        /// </summary>
        /// <param name="assetInfo"></param>
        /// <param name="dicData"></param>
        /// <returns></returns>
        private async Task<List<UserCheckPointResult>> GetUserCheckGeneralDevicePoint(AssetInfo assetInfo, Dictionary<string, string> dicData)
        {
            var pointData = new List<UserCheckPointResult>();

            var thirdModelConfig = await _client.Queryable<ThirdModelConfig>().FirstAsync(p => p.Code == assetInfo.ThirdPartCode);

            if (thirdModelConfig == null)
            {
                return pointData;
            }

            var jsonData = JsonConvert.DeserializeObject<ThreePartModelTemplate>(thirdModelConfig.JsonData ?? "");

            var universalDeviceConfigs = await _client.Queryable<UniversalDeviceConfig>()
                                                      .Where(p => p.AssetId == assetInfo.Id).ToListAsync();

            //获取二进点位集合
            List<int> universalDeviceConfigIds = universalDeviceConfigs.Where(p => p.IsBit).Select(p => p.Id).ToList();

            //二级制点位配置集合
            var bitConfigList = await _client.Queryable<BitConfig>()
                .Where(p => universalDeviceConfigIds.Contains(p.UniversalDeviceConfigId))
                .ToListAsync();

            if (jsonData != null && jsonData.Treeview != null && jsonData.Treeview.Any())
            {
                foreach (var item in jsonData.Treeview!)
                {
                    //获取子集集合
                    if (item.SubGroups != null && item.SubGroups.Any())
                    {
                        foreach (var _item in item.SubGroups)
                        {
                            if (_item.Properties != null && _item.Properties.Any())
                            {
                                UniversalDeviceInfo.Instance._dicPoint.TryGetValue(_item.Name ?? "", out string? value);

                                var entity = new UserCheckPointResult()
                                {
                                    EnParentName = _item.Name,
                                    CnParentName = value
                                };

                                foreach (var secondItem in _item.Properties)
                                {
                                    dicData.TryGetValue(secondItem.PropertyName ?? "", out string? data);

                                    var exEntity = universalDeviceConfigs.FirstOrDefault(p =>
                                              p.PropertyEnName == secondItem.PropertyName);

                                    if (exEntity != null && exEntity.IsBit)
                                    {
                                        var model = new UserCheckPointEntity()
                                        {
                                            PropertyEnName = exEntity?.PropertyEnName ?? secondItem.PropertyName,
                                            PropertyCnName = exEntity?.PropertyCnName,
                                            Value = data ?? "0000000000000000",
                                            Unit = secondItem.Unit ?? "",
                                            IsBit = true
                                        };

                                        //获取二进制点位
                                        var bitConfigs = bitConfigList.Where(p => p.UniversalDeviceConfigId == exEntity?.Id).ToList();

                                        if (bitConfigs.Any())
                                        {
                                            // 是否有科学计数法
                                            if (model.Value.Contains("E") || model.Value.Contains("e"))
                                            {
                                                model.Value = Decimal.Parse(model.Value, System.Globalization.NumberStyles.Float).ToString();
                                            }

                                            // 判断是否是16位二级制
                                            if (!(model.Value.Length == 16 && StringFunction.IsBinary(model.Value)))
                                            {
                                                model.Value = StringFunction.TenToBit(Convert.ToInt32(model.Value));
                                            }

                                            List<string?> bits = StringFunction.BitToList(model.Value);

                                            foreach (var bitConfig in bitConfigs)
                                            {
                                                if (model.BitDatas != null && !model.BitDatas.ContainsKey(bitConfig.BitNumber.ToString()))
                                                {
                                                    model.BitDatas?.Add(bitConfig.BitNumber.ToString(), new BitDataDto()
                                                    {
                                                        Name = bitConfig.BitName,
                                                        Value = bits[bitConfig.BitNumber]
                                                    });
                                                }
                                            }
                                        }
                                        else
                                        {
                                            for (int i = 0; i < 16; i++)
                                            {
                                                model.BitDatas?.Add(i.ToString(), new BitDataDto()
                                                {
                                                    Name = $"bit{i}",
                                                    Value = "0"
                                                });
                                            }
                                        }

                                        entity.UserCheckPoints?.Add(model);
                                    }
                                    else
                                    {
                                        string strData = string.IsNullOrWhiteSpace(data) ? "0" : data;
                                        decimal val = Decimal.Parse(strData, System.Globalization.NumberStyles.Float);

                                        entity.UserCheckPoints?.Add(new UserCheckPointEntity()
                                        {
                                            PropertyEnName = exEntity?.PropertyEnName ?? secondItem.PropertyName,
                                            PropertyCnName = exEntity?.PropertyCnName ?? secondItem.DescriptionInGerman,
                                            Value = string.IsNullOrWhiteSpace(data) ? "" : val.ToString("0.00"),
                                            Unit = secondItem.Unit ?? "",
                                            IsBit = false
                                        });
                                    }
                                }

                                pointData.Add(entity);
                            }
                        }
                    }

                    if (item.Properties != null && item.Properties.Any())
                    {
                        foreach (var _item in item.Properties)
                        {
                            UniversalDeviceInfo.Instance._dicPoint.TryGetValue(_item.GroupName!, out string? value);

                            var entity = new UserCheckPointResult()
                            {
                                EnParentName = _item.GroupName,
                                CnParentName = value
                            };

                            dicData.TryGetValue(_item.PropertyName ?? "", out string? data);

                            var exEntity = universalDeviceConfigs.FirstOrDefault(p =>
                                              p.PropertyEnName == _item.PropertyName);

                            if (exEntity != null && exEntity.IsBit)
                            {
                                var model = new UserCheckPointEntity()
                                {
                                    PropertyEnName = exEntity?.PropertyEnName ?? _item.PropertyName,
                                    PropertyCnName = exEntity?.PropertyCnName ?? _item.DescriptionInGerman,
                                    Value = data ?? "0000000000000000",
                                    Unit = _item.Unit ?? "",
                                    IsBit = true
                                };

                                //获取二进制点位
                                var bitConfigs = bitConfigList.Where(p => p.UniversalDeviceConfigId == exEntity?.Id).ToList();

                                if (bitConfigs.Any())
                                {
                                    // 是否有科学计数法
                                    if (model.Value.Contains("E") || model.Value.Contains("e"))
                                    {
                                        model.Value = Decimal.Parse(model.Value, System.Globalization.NumberStyles.Float).ToString();
                                    }

                                    // 判断是否是16位二级制
                                    if (!(model.Value.Length == 16 && StringFunction.IsBinary(model.Value)))
                                    {
                                        model.Value = StringFunction.TenToBit(Convert.ToInt32(model.Value));
                                    }

                                    List<string?> bits = StringFunction.BitToList(model.Value);

                                    foreach (var bitConfig in bitConfigs)
                                    {
                                        if (model.BitDatas != null && !model.BitDatas.ContainsKey(bitConfig.BitNumber.ToString()))
                                        {
                                            model.BitDatas?.Add(bitConfig.BitNumber.ToString(), new BitDataDto()
                                            {
                                                Name = bitConfig.BitName,
                                                Value = (bitConfig.BitNumber > bits.Count - 1) ? "0" : bits[bitConfig.BitNumber]
                                            });
                                        }
                                    }
                                }
                                else
                                {
                                    for (int i = 0; i < 16; i++)
                                    {
                                        model.BitDatas?.Add(i.ToString(), new BitDataDto()
                                        {
                                            Name = $"bit{i}",
                                            Value = "0"
                                        });
                                    }
                                }

                                entity.UserCheckPoints?.Add(model);
                            }
                            else
                            {
                                string strData = string.IsNullOrWhiteSpace(data) ? "0" : data;
                                decimal val = Decimal.Parse(strData, System.Globalization.NumberStyles.Float);

                                entity.UserCheckPoints?.Add(new UserCheckPointEntity()
                                {
                                    PropertyEnName = exEntity?.PropertyEnName ?? _item.PropertyName,
                                    PropertyCnName = exEntity?.PropertyCnName ?? _item.DescriptionInGerman,
                                    Value = string.IsNullOrWhiteSpace(data) ? "" : val.ToString("0.00"),
                                    Unit = _item.Unit ?? "",
                                    IsBit = false
                                });
                            }

                            pointData.Add(entity);
                        }
                    }
                }
            }

            return pointData;
        }

        [HttpGet("{assetId}/GetChart")]
        [SwaggerOperation(Summary = "Swagger_AssetStatus_GetChart", Description = "Swagger_AssetStatus_GetChart_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<Dictionary<string, IChart?>>> GetCharts([FromQuery] AssetChartParam param)
        {
            if (param == null || param.AssetId <= 0 || param.ChartNames == null || param.ChartNames.Length <= 0)
            {
                return new ResponseBase<Dictionary<string, IChart?>>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }

            var nameList = param.ChartNames.Distinct().ToArray();
            var assetInfo = _cache.Get<AssetSimpleInfo>($"Asset:SimpleInfo-{param.AssetId}");
            if (assetInfo == null)
            {
                var emptyResult = new Dictionary<string, IChart?>();

                foreach (var name in nameList)
                {
                    emptyResult.TryAdd(name, null);
                }

                return new ResponseBase<Dictionary<string, IChart?>>()
                {
                    Code = 20000,
                    Data = emptyResult
                };
            }
            var assetId = param.AssetId;

            if (assetInfo.SourceAssetId.HasValue)
            {
                if (assetInfo.AssetLevel == AssetLevel.Circuit)
                {
                    assetId = assetInfo.SourceAssetId.Value;

                    #region 旧代码
                    //var connectStatusAll = _cache.Get<Dictionary<int, bool>>("AssetConnectStatus:All");
                    //if (connectStatusAll != null)
                    //{
                    //    bool circuitConnectStatus = false;
                    //    if (connectStatusAll.TryGetValue(assetId, out bool hasConnect0))
                    //    {
                    //        circuitConnectStatus = hasConnect0;
                    //    }
                    //    var subDeviceIds = await _client.Queryable<AssetRelation>().Where(ar => ar.ParentId == param.AssetId && ar.AssetLevel == AssetLevel.Device).Select(ar => ar.ChildId).ToArrayAsync();
                    //    var deviceAssetInfoes = await _client.Queryable<AssetInfo>().Where(a => subDeviceIds.Contains(a.Id)).ToArrayAsync();
                    //    #region 从电表取
                    //    if (!circuitConnectStatus)
                    //    {
                    //        var meter = deviceAssetInfoes.OrderByDescending(a => a.Id).FirstOrDefault(d => d.AssetType == "Meter");
                    //        if (meter != null)
                    //        {
                    //            var sourceAssetId = meter.Id;
                    //            if (connectStatusAll.TryGetValue(sourceAssetId, out bool hasConnect))
                    //            {
                    //                assetId = hasConnect ? sourceAssetId : assetId;
                    //                circuitConnectStatus = hasConnect;
                    //            }
                    //        }
                    //    }
                    //    #endregion
                    //    #region 从断路器取
                    //    if (!circuitConnectStatus)
                    //    {
                    //        var breakerTypes = new string[] { "ACB", "MCCB", "MCB" };
                    //        var breaker = deviceAssetInfoes.OrderByDescending(a => a.Id).FirstOrDefault(d => breakerTypes.Contains(d.AssetType));
                    //        if (breaker != null)
                    //        {
                    //            var sourceAssetId = breaker.Id;
                    //            if (connectStatusAll.TryGetValue(sourceAssetId, out bool hasConnect))
                    //            {
                    //                assetId = hasConnect ? sourceAssetId : assetId;
                    //                circuitConnectStatus = hasConnect;
                    //            }
                    //        }
                    //    }
                    //    #endregion
                    //    #region 从马保取
                    //    if (!circuitConnectStatus)
                    //    {
                    //        var motor = deviceAssetInfoes.OrderByDescending(a => a.Id).FirstOrDefault(d => d.AssetType == "MotorProtector");
                    //        if (motor != null)
                    //        {
                    //            var sourceAssetId = motor.Id;
                    //            if (connectStatusAll.TryGetValue(sourceAssetId, out bool hasConnect))
                    //            {
                    //                assetId = hasConnect ? sourceAssetId : assetId;
                    //                circuitConnectStatus = hasConnect;
                    //            }
                    //        }
                    //    }
                    //    #endregion
                    //    #region 从第三方设备取
                    //    if (!circuitConnectStatus)
                    //    {
                    //        var thirdDevice = deviceAssetInfoes.OrderByDescending(a => a.Id).FirstOrDefault(d => d.AssetType == "GeneralDevice");
                    //        if (thirdDevice != null)
                    //        {
                    //            var sourceAssetId = thirdDevice.Id;
                    //            if (connectStatusAll.TryGetValue(sourceAssetId, out bool hasConnect))
                    //            {
                    //                assetId = hasConnect ? sourceAssetId : assetId;
                    //                circuitConnectStatus = hasConnect;
                    //            }
                    //        }
                    //    }
                    //    #endregion
                    //}
                    #endregion
                }
                else if (assetInfo.AssetLevel == AssetLevel.Panel)
                {
                    var circuit = _cache.Get<AssetSimpleInfo>($"Asset:SimpleInfo-{assetInfo.SourceAssetId.Value}");
                    if (circuit != null && circuit.SourceAssetId.HasValue)
                    {
                        assetId = circuit.SourceAssetId.Value;
                    }
                }
            }
            var result = new Dictionary<string, IChart?>();
            var service = _provider.GetRequiredService<AssetDashboardServer>();

            var filter = new Dictionary<string, string>()
            {
                ["ChartDateType"] = ((int)param.ChartDateType).ToString(),
                ["AssetId"] = assetId.ToString(),
            };

            if (!string.IsNullOrEmpty(param.StartDate))
            {
                filter.Add("StartDate", param.StartDate);
            }
            if (!string.IsNullOrEmpty(param.EndDate))
            {
                filter.Add("EndDate", param.EndDate);
            }

            foreach (var name in nameList)
            {
                IChart? chart = null;
                try
                {
                    var f = new Dictionary<string, string>(filter);
                    chart = await service.GetDashboard(name, MessageContext, f, _client);

                    if (name.Equals("Asset_THD_U"))
                    {
                        // 删除电压畸变率，电压畸变率改为三相电压畸变率
                        var thdAssetData = chart as LineChartModel;
                        if (thdAssetData != null)
                        {
                            thdAssetData.YColumns = new List<string>() { "A相", "B相", "C相", "三相电压畸变率" };
                            chart = thdAssetData;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _log.LogError(ex, "获取报表报错");
                }
                result.Add(name, chart);
            }

            return new ResponseBase<Dictionary<string, IChart?>>()
            {
                Code = 20000,
                Data = result
            };
        }

        [HttpGet("{assetId}/GetSumPower")]
        [SwaggerOperation(Summary = "Swagger_AssetStatus_GetSumPower", Description = "Swagger_AssetStatus_GetSumPower_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<IChart?>> GetSumPower([FromQuery] GetSumPowerParam param)
        {
            if (param == null || param.AssetId <= 0)
            {
                return new ResponseBase<IChart?>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            var assetInfo = _cache.Get<AssetSimpleInfo>($"Asset:SimpleInfo-{param.AssetId}");
            if (assetInfo == null)
            {
                return new ResponseBase<IChart?>()
                {
                    Code = 20000,
                    Data = new LineChartModel()
                    {
                        X = new string[0]
                    }
                };
            }

            var filter = new Dictionary<string, string>();

            var gateway = MeasurementType.Gateway.ToString();

            var service = _provider.GetRequiredService<AssetDashboardServer>();

            var assetRelationList = await _client.Queryable<AssetRelation>().ToListAsync();

            var chirdAssetRelations = assetRelationList.Where(p => p.ParentId == param.AssetId).ToList();

            var deviceAssetIds = new List<int>();

            if (chirdAssetRelations != null && chirdAssetRelations.Any())
            {
                var deviceAssetRelationsIds = new List<int>();

                while (chirdAssetRelations.Count > 0)
                {
                    deviceAssetRelationsIds = chirdAssetRelations.Select(p => p.ChildId).ToList();

                    chirdAssetRelations = assetRelationList.Where(p => deviceAssetRelationsIds.Contains(p.ParentId)).ToList();

                    if (chirdAssetRelations.Any(p => p.AssetLevel == AssetLevel.Device))
                    {
                        deviceAssetIds.AddRange(chirdAssetRelations.Where(p => p.AssetLevel == AssetLevel.Device).Select(p => p.ChildId).ToList());
                    }

                }
            }

            //var childAssetIds = await _client.Queryable<AssetRelation>().ToChildListAsync(ar => ar.ParentId, param.AssetId);

            //var assetIds = childAssetIds.Select(a => a.ChildId).ToList();

            var deviceAssets = await _client.Queryable<AssetInfo>().Where(a => deviceAssetIds.Contains(a.Id)
                 && a.AssetLevel == AssetLevel.Device && a.MeterType == gateway)
                .Select(a => a.Id).ToListAsync();

            if (deviceAssets.Any())
            {
                var assetIdQuery = new StringBuilder();
                foreach (var id in deviceAssets)
                {
                    if (assetIdQuery.Length > 0)
                    {
                        assetIdQuery.Append(" or ");
                    }
                    assetIdQuery.Append("r[\"assetid\"] == \"");
                    assetIdQuery.Append(id);
                    assetIdQuery.Append('\"');
                }

                filter.Add("AssetIdList", assetIdQuery.ToString());
                filter.Add("ChartDateType", ((int)param.ChartDateType).ToString());
                var chart = await service.GetDashboard("Asset_TotalPower", MessageContext, filter, _client);

                return new ResponseBase<IChart?>()
                {
                    Code = 20000,
                    Data = chart
                };
            }
            else
            {
                return new ResponseBase<IChart?>()
                {
                    Code = 20000,
                    Data = new LineChartModel()
                    {
                        X = new string[0]
                    }
                };
            }
        }

        /// <summary>
        /// 获取配电房的有功功率，无功功率，当电能的数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpGet("GetPowerCalculation")]
        [SwaggerOperation(Summary = "Swagger_AssetStatus_GetPowerCalculation", Description = "Swagger_AssetStatus_GetPowerCalculation_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> GetPowerCalculation([FromQuery] PowerCalculationParam input)
        {
            ResponseBase<PowerCalculationResutl> result;

            try
            {

                if (input == null || input.AssetId <= 0)
                {
                    return Ok(new ResponseBase<PowerCalculationResutl> { Code = 50000, Message = MessageContext.ErrorParam });
                }

                var entity = new PowerCalculationResutl();

                var filter = new Dictionary<string, string>();

                var service = _provider.GetRequiredService<AssetDashboardServer>();

                var assetRelationList = await _client.Queryable<AssetRelation>().ToListAsync();

                var chirdAssetRelations = assetRelationList.Where(p => p.ParentId == input.AssetId).ToList();

                var deviceAssetIds = new List<int>();

                if (chirdAssetRelations != null && chirdAssetRelations.Any())
                {
                    var deviceAssetRelationsIds = new List<int>();

                    while (chirdAssetRelations.Count > 0)
                    {
                        deviceAssetRelationsIds = chirdAssetRelations.Select(p => p.ChildId).ToList();

                        chirdAssetRelations = assetRelationList.Where(p => deviceAssetRelationsIds.Contains(p.ParentId)).ToList();

                        if (chirdAssetRelations.Any(p => p.AssetLevel == AssetLevel.Device))
                        {
                            deviceAssetIds.AddRange(chirdAssetRelations.Where(p => p.AssetLevel == AssetLevel.Device).Select(p => p.ChildId));
                        }
                    }
                }

                // 获取电能设备的集合
                var deviceByEnergyIds = await _client.Queryable<AssetInfo>().Where(a => deviceAssetIds.Contains(a.Id)
                                                  && a.AssetLevel == AssetLevel.Device && a.MeterType == MeasurementType.Gateway.ToString())
                                                  .Select(a => a.Id).ToListAsync();
                decimal totalEnergy = 0;
                if (deviceByEnergyIds != null && deviceByEnergyIds.Any())
                {
                    var assetIdQuery = new StringBuilder();
                    var assetIdFilter = new StringBuilder();

                    foreach (var id in deviceByEnergyIds)
                    {
                        if (assetIdQuery.Length > 0)
                        {
                            assetIdQuery.Append(" or ");
                            assetIdFilter.Append(",");
                        }

                        assetIdQuery.Append("r[\"assetid\"] == \"");
                        assetIdQuery.Append(id);
                        assetIdFilter.Append(id);
                        assetIdQuery.Append('\"');
                    }
                    filter.Add("StartDate", DateTime.Now.ToString("yyyy-MM-dd"));
                    //filter.Add("AssetIdFilter", assetIdFilter.ToString());
                    filter.Add("AssetIdList", assetIdQuery.ToString());
                    filter.Add("ChartDateType", ((int)ChartDateType.Day).ToString());
                    decimal totalP = 0;
                    decimal totalQ = 0;
                   
                    foreach (int assetId in deviceByEnergyIds)
                    {
                        var currentlyStatus = _cache.GetHashAllData(string.Format(AssetCurrentStatusCacheKey, assetId));
                        if (currentlyStatus != null&& currentlyStatus.Count>0)
                        {
                            if (currentlyStatus.ContainsKey("P"))
                            {
                                decimal p = 0;
                                var success = decimal.TryParse(currentlyStatus["P"], out p);
                                if (success)
                                {
                                    totalP += p;
                                }
                               
                            }
                            if (currentlyStatus.ContainsKey("Q")) {
                                decimal q = 0;
                                var success = decimal.TryParse(currentlyStatus["Q"], out q);
                                if (success)
                                {
                                    totalQ += q;
                                }
                            }
                            if (currentlyStatus.ContainsKey("active_power"))
                            {
                                decimal p = 0;
                                var success = decimal.TryParse(currentlyStatus["active_power"], out p);
                                if (success)
                                {
                                    totalP += p;
                                }
                            }
                            if (currentlyStatus.ContainsKey("collective_reactive_power"))
                            {
                                decimal q = 0;
                                var success = decimal.TryParse(currentlyStatus["collective_reactive_power"], out q);
                                if (success)
                                {
                                    totalQ += q;
                                }
                            }
                            //if (currentlyStatus.ContainsKey("ForwardActivePower"))
                            //{
                            //    decimal energy = 0;
                            //    var success = decimal.TryParse(currentlyStatus["ForwardActivePower"], out energy);
                            //    if (success)
                            //    {
                            //        totalEnergy += energy;
                            //    }
                            //}
                            //if (currentlyStatus.ContainsKey("active_energy_import"))
                            //{
                            //    decimal energy = 0;
                            //    var success = decimal.TryParse(currentlyStatus["active_energy_import"], out energy);
                            //    if (success)
                            //    {
                            //        totalEnergy += energy;
                            //    }
                            //}

                        }
                    }
                    //var chartData = await service.GetDashboard("Asset_TotalPower", MessageContext, filter, _client) as LineChartModel;

                    // 有功功率
                    //entity.ActivePower = (chartData != null && chartData.Y1 != null && chartData.Y1.Any(p => p > 0)) ? chartData.Y1.Last(p => p > 0) : 0;
                    entity.ActivePower = totalP*100/100.0M;

                    // 无功功率
                    //entity.ReactivePower = (chartData != null && chartData.Y2 != null && chartData.Y2.Any(p => p > 0)) ? chartData.Y2.Last(p => p > 0) : 0;
                    entity.ReactivePower= totalQ*100/100.0M;
                    //decimal energyDay = 0;
                    //缓存当日0点的所有设备电能数据,后续计算用当前设备的总电能-当天0点电能
                    //string cacheKey = string.Format(CurrentDayEnergyCacheKey, input.AssetId.ToString());
                    //var energyCache = _cache.Get<decimal>(cacheKey);
                    //_log.LogInformation("缓存中获取的energyCache为：" + energyCache);
                    var energyManagementServer = _provider.GetRequiredService<EnergyManagementServer>();
                    var data = await energyManagementServer.GetEnergyDataByHour(deviceByEnergyIds, DateTime.Now.Date.AddMinutes(-1), DateTime.Now, true);

                    // 获取电能的值
                    if (data != null)
                    {
                        var coefficient = 1;

                        var systemConfig = await _client.Queryable<SystemConfig>().FirstAsync(c => c.Type == "SpecialConfig" && c.Name == "EnergyCoefficient");
                        if (systemConfig != null && int.TryParse(systemConfig.Value, out var coefficientInt))
                        {
                            coefficient = coefficientInt * 1000;
                        }

                        entity.DailyByElectricity = data.Sum(a => a.Value) * coefficient;
                    }
                    //if (energyCache != 0)
                    //{
                    //    energyDay = energyCache;
                    //}
                    //else
                    //{
                    //    var energyManagementServer = _provider.GetRequiredService<EnergyManagementServer>();
                    //    var data = await energyManagementServer.GetEnergyData(deviceByEnergyIds, DateTime.Now.Date, DateTime.Now.Date, "1h", true);
                    //    // 获取电能的值
                    //    if (data != null)
                    //    {
                    //        energyDay = data.Sum(a => a.Value);
                    //        _cache.Set(cacheKey, energyDay, TimeSpan.FromSeconds(600));
                    //    }

                    //}
                   
                    //_log.LogInformation("totalEnergy：" + totalEnergy + ",energyDay:" + energyDay);
                    //entity.DailyByElectricity = totalEnergy - energyDay;
                }
               
                result = new ResponseBase<PowerCalculationResutl>() { Code = 20000, Data = entity };

            }
            catch (Exception ex)
            {
                _log.LogError(ex,"计算当日电能功率出错");
                result = new ResponseBase<PowerCalculationResutl>() { Code = 20000, Data = new PowerCalculationResutl() };
            }
            return Ok(result);
        }

        /// <summary>
        /// 获取设备数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}/AllDatas")]
        [SwaggerOperation(Summary = "Swagger_AssetStatus_GetAllDatas", Description = "Swagger_AssetStatus_GetAllDatas_Desc")]
        [Authorize(policy: Permission.Default)]
        public ResponseBase<Dictionary<string, string>> GetAllDatas(int id)
        {
            var datas = _cache.GetHashAllData(string.Format(AssetAllDataCacheKey, id));
            return new ResponseBase<Dictionary<string, string>>()
            {
                Code = 20000,
                Data = datas
            };
        }

        /// <summary>
        /// 资源释放
        /// </summary>
        public void Dispose()
        {
            _client.Close();
        }
    }
}

