﻿using Siemens.PanelManager.Model.Database.Auth;
using SqlSugar;

namespace Siemens.PanelManager.WebApi.StaticContent
{
    static class SpecialLogic
    {
        /// <summary>
        /// 部分情况不返回个人中心
        /// 部分情况返回
        /// </summary>
        /// <param name="pages"></param>
        /// <param name="specialNum"></param>
        /// <returns></returns>
        public static Page[] FilterSomePageBySpecialLogic(this Page[] pages, int specialNum)
        {
            var newPages = new List<Page>();
            switch (specialNum)
            {
                case 0:
                    foreach (var p in pages)
                    {
                        if (p.PageCode == "My" || p.PageCode == "Profile")
                        {
                            continue;
                        }
                        newPages.Add(p);
                    }
                    break;
                case 1:
                default: return pages;
            }
            return newPages.ToArray();
        }

        /// <summary>
        /// 部分情况不返回个人中心
        /// 部分情况返回
        /// </summary>
        /// <param name="pages"></param>
        /// <param name="specialNum"></param>
        /// <returns></returns>
        public static List<Page> FilterSomePageBySpecialLogic(this List<Page> pages, int specialNum)
        {
            var newPages = new List<Page>();
            switch (specialNum)
            {
                case 0:
                    foreach (var p in pages)
                    {
                        if (p.PageCode == "My" || p.PageCode == "Profile")
                        {
                            continue;
                        }
                        newPages.Add(p);
                    }
                    break;
                case 1:
                default: return pages;
            }
            return newPages.ToList();
        }

        /// <summary>
        /// 默认添加个人中心
        /// </summary>
        /// <param name="pageIds"></param>
        /// <param name="specialNum"></param>
        /// <returns></returns>
        public static int[] AddSomePageBySpecialLogic(this int[] pageIds, int specialNum)
        {
            var newPageIds = new List<int>();
            switch (specialNum)
            {
                case 0:
                    newPageIds.AddRange(pageIds);
                    newPageIds.Add(300000); //个人中心
                    newPageIds.Add(300100); //个人照片
                    break;
                case 1:
                default: return pageIds;
            }
            return newPageIds.ToArray();
        }

        public static string[] AddSomePageBySpecialLogic(this string[] pageIds, int specialNum)
        {
            var newPageIds = new List<string>();
            switch (specialNum)
            {
                case 0:
                    newPageIds.AddRange(newPageIds);
                    newPageIds.Add("300000"); //个人中心
                    newPageIds.Add("300100"); //个人照片
                    break;
                case 1:
                default: return pageIds;
            }
            return newPageIds.ToArray();
        }

        public static async Task<bool> CheckRoleCount(this int roleId, ISugarQueryable<UserRoleMapping> roleMappingQuery, ISugarQueryable<Role> roleQuery)
        {
            var role = await roleQuery
                .Where(r=>r.Id == roleId)
                .WithCache($"Role:{roleId}", (int)TimeSpan.FromHours(1).TotalSeconds)
                .FirstAsync();

            if(role == null || !role.RoleCount.HasValue) return true;

            var roleCount = roleMappingQuery.Where(rm => rm.RoleId == roleId).Count();
            return role.RoleCount.Value > roleCount;
        }

        public static async Task<bool> CheckRoleCount(this int[] roleIds, ISugarQueryable<UserRoleMapping> roleMappingQuery, ISugarQueryable<Role> roleQuery)
        {
            foreach(var roleId in roleIds) 
            {
                var roleMappingCloneQuery = roleMappingQuery.Clone();
                var roleCloneQuery = roleQuery.Clone();
                if (!(await roleId.CheckRoleCount(roleMappingCloneQuery, roleCloneQuery))) 
                    return false;
            }
            return true;
        }
    }
}
