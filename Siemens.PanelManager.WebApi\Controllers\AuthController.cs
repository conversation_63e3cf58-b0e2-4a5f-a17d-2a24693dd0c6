﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Model.Database.Auth;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.WebApi.Models;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;

namespace Siemens.PanelManager.WebApi.Controllers
{
    /// <summary>
    /// 授权接口
    /// </summary>
    /// <remarks>授权接口</remarks>
    [Route("api/v1")]
    [ApiController]
    public class AuthController : SiemensApiControllerBase
    {
        private IConfiguration _config;
        private ISqlSugarClient _client;
        private SiemensCache _cache;
        private AlarmExtendServer _alarmExtendServer;

        public AuthController(IConfiguration configuration,
            SqlSugarScope client,
            SiemensCache cache,
            IServiceProvider provider,
            AlarmExtendServer alarmExtendServer)
            : base(provider, cache)
        {
            _config = configuration;
            _client = client;
            _cache = cache;
            _alarmExtendServer = alarmExtendServer;
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        /// <remarks>用户登录接口</remarks>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost("login")]
        [AllowAnonymous]
        [SwaggerOperation(Summary = "Swagger_Auth_Login", Description = "Swagger_Auth_Login_Desc")]
        public async Task<ResponseBase<LoginResult>> Login(LoginParam param)
        {
            if (string.IsNullOrWhiteSpace(param.UserName) || string.IsNullOrWhiteSpace(param.Password))
            {
                return new ResponseBase<LoginResult>()
                {
                    Code = 50100,
                    Message = MessageContext.ErrorAuthUserPassword
                };
            }
            param.UserName = param.UserName.ToLower();
            var md5 = MD5.Create();
            var code = Convert.ToBase64String(md5.ComputeHash(Encoding.UTF8.GetBytes($"Siemens-{param.Password}")));
            var user = await _client.Queryable<User>()
                .Where(u => u.LoginName == param.UserName && u.PasswordHash == code && !u.IsDeleted)
                .FirstAsync();

            if (user == null)
            {
                return new ResponseBase<LoginResult>()
                {
                    Code = 50100,
                    Message = MessageContext.ErrorAuthUserPassword
                };
            }

            user.LastLoginTime = DateTime.Now;
            await _client.Updateable(user).ExecuteCommandAsync();

            await _alarmExtendServer.InsertOperationLog(user.UserName, "Login", Model.Database.Alarm.AlarmSeverity.Low, _client);
            var roles = await _client.Queryable<Role>()
                .InnerJoin<UserRoleMapping>((r, urm) => urm.RoleId == r.Id)
                .Where((r, urm) => urm.UserId == user.Id)
                .Select<Role>()
                .ToListAsync();

            var roleIds = roles.Select(r => r.Id).ToArray();
            var pages = await _client.Queryable<Page>()
                .InnerJoin<RolePageMapping>((p, rpm) => rpm.PageId == p.Id)
                .Where((p, rpm) => roleIds.Contains(rpm.RoleId))
                .Select<Page>()
                .ToListAsync();

            var session = new UserSessionInfo(user, roles, pages);
            if (_config.GetValue<bool>("TestEnv"))
            {
                _cache.SetBySlip($"UserSession:{session.Id}", session, TimeSpan.FromSeconds(20));
            }
            else
            {
                // TODO
                _cache.SetBySlip($"UserSession:{session.Id}", session, TimeSpan.FromDays(3));

                //_cache.SetBySlip($"UserSession:{session.Id}", session, TimeSpan.FromMinutes(20));
            }

            var token = CreateToken(session.Id, session.UserName, session.SyncDeviceInfo, DateTime.Now.AddSeconds(1));
            var result = new LoginResult()
            {
                Id = user.Id,
                Token = token
            };

            return new ResponseBase<LoginResult>()
            {
                Data = result
            };
        }

        /// <summary>
        /// 程序登录
        /// </summary>
        /// <remarks>程序登录接口</remarks>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost("loginByApp")]
        [AllowAnonymous]
        public async Task<ResponseBase<LoginResult>> LoginByApp(LoginParam param)
        {
            if (string.IsNullOrWhiteSpace(param.UserName) || string.IsNullOrWhiteSpace(param.AppName))
            {
                return new ResponseBase<LoginResult>()
                {
                    Code = 50100,
                    Message = MessageContext.ErrorAuthUserPassword
                };
            }
            param.UserName = param.UserName.ToLower();

            if (param.UserName != "admin" || !"_algorithm_loss_evaluation_".Contains($"_{param.AppName}_", StringComparison.OrdinalIgnoreCase))
            {
                return new ResponseBase<LoginResult>()
                {
                    Code = 50100,
                    Message = MessageContext.ErrorAuthUserPassword
                };
            }
            var user = await _client.Queryable<User>()
                .Where(u => u.LoginName == param.UserName)
                .FirstAsync();

            if (user == null)
            {
                return new ResponseBase<LoginResult>()
                {
                    Code = 50100,
                    Message = MessageContext.ErrorAuthUserPassword
                };
            }

            var roles = await _client.Queryable<Role>()
                .InnerJoin<UserRoleMapping>((r, urm) => urm.RoleId == r.Id)
                .Where((r, urm) => urm.UserId == user.Id)
                .Select<Role>()
                .ToListAsync();

            var roleIds = roles.Select(r => r.Id).ToArray();
            var pages = await _client.Queryable<Page>()
                .InnerJoin<RolePageMapping>((p, rpm) => rpm.PageId == p.Id)
                .Where((p, rpm) => roleIds.Contains(rpm.RoleId))
                .Select<Page>()
                .ToListAsync();

            var session = new UserSessionInfo(user, roles, pages);
            if (_config.GetValue<bool>("TestEnv"))
            {
                _cache.SetBySlip($"UserSession:{session.Id}", session, TimeSpan.FromSeconds(20));
            }
            else
            {
                _cache.SetBySlip($"UserSession:{session.Id}", session, TimeSpan.FromHours(2));
            }

            var token = CreateToken(session.Id, session.UserName, session.SyncDeviceInfo, DateTime.Now.AddSeconds(1));
            var result = new LoginResult()
            {
                Id = user.Id,
                Token = token
            };

            return new ResponseBase<LoginResult>()
            {
                Data = result
            };
        }

        /// <summary>
        /// 用户登出
        /// </summary>
        /// <returns></returns>
        [HttpGet("logout")]
        [SwaggerOperation(Summary = "Swagger_Auth_Logout", Description = "Swagger_Auth_Logout_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> Logout()
        {
            var sessionId = User.GetSessionId();
            _cache.Clear($"UserSession:{sessionId}");
            await _alarmExtendServer.InsertOperationLog(UserName, "Logout", Model.Database.Alarm.AlarmSeverity.Low, _client);
            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = MessageContext.Success
            };
        }

        /// <summary>
        ///  判断当前用户是否有管理员权限
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPost("IsUserByPrivileges")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_Auth_IsUserByPrivileges", Description = "Swagger_Auth_IsUserByPrivileges_Desc")]
        public async Task<IActionResult> IsUserByPrivileges(LoginParam param)
        {
            ResponseBase<bool> result;

            try
            {
                if (string.IsNullOrWhiteSpace(param.UserName) || string.IsNullOrWhiteSpace(param.Password))
                {
                    return Ok(new ResponseBase<bool>()
                    {
                        Code = 20000,
                        Data = false,
                        Message = MessageContext.ErrorAuthUserPassword
                    });
                }

                var md5 = MD5.Create();
                param.UserName = param.UserName.ToLower();
                var code = Convert.ToBase64String(md5.ComputeHash(Encoding.UTF8.GetBytes($"Siemens-{param.Password}")));
                var user = await _client.Queryable<User>().Where(u => u.LoginName == param.UserName
                           && u.PasswordHash == code && !u.IsDeleted).FirstAsync();

                if (user == null)
                {
                    return Ok(new ResponseBase<bool>()
                    {
                        Code = 20000,
                        Data = true,
                        Message = MessageContext.ErrorAuthUserPassword
                    });
                }

                // 判断是否有管理员权限
                var IsExists = await _client.Queryable<UserRoleMapping>()
                             .LeftJoin<Role>((t1, t2) => t1.RoleId == t2.Id)
                             .Where((t1, t2) => t1.UserId == user.Id && (t2.RoleCode == "Root" || t2.RoleCode == "Para"))
                             .AnyAsync();

                // 不存在管理员权限
                if (!IsExists)
                {
                    return Ok(new ResponseBase<bool>()
                    {
                        Code = 20000,
                        Data = true,
                        Message = MessageContext.GetString("Error_Auth_NoExistByAdmin")
                    });
                }

                result = new ResponseBase<bool>() { Code = 20000, Data = true, Message = MessageContext.Success };

            }
            catch (Exception ex)
            {
                result = new ResponseBase<bool>() { Code = 50000, Data = false, Message = ex.Message };
            }

            return Ok(result);
        }


        private string CreateToken(string id, string user, string syncDevice, DateTime expireTime)
        {
            // 1. 定义需要使用到的Claims
            var claims = new[]
            {
                new Claim(ClaimTypes.Name, user),
                new Claim(ClaimTypes.NameIdentifier, id),
                new Claim("SyncDevice", syncDevice),
            };

            // 2. 从 appsettings.json 中读取SecretKey
            var secretKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_config["SecretKey"] ?? string.Empty));

            // 3. 选择加密算法
            var algorithm = SecurityAlgorithms.HmacSha256;

            // 4. 生成Credentials
            var signingCredentials = new SigningCredentials(secretKey, algorithm);

            // 5. 根据以上，生成token
            var jwtSecurityToken = new JwtSecurityToken(
                "SiemensIssuer",    //Issuer
                "WebAppAudience",   //Audience
                claims,             //Claims,
                DateTime.Now,       //notBefore
                expireTime,         //expires
                signingCredentials  //Credentials
            );

            // 6. 将token变为string
            var token = new JwtSecurityTokenHandler().WriteToken(jwtSecurityToken);
            return token;
        }


    }


}
