[{"Code": "IsConnected", "Name": "IsConnected", "GroupName": "Status", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "UDCConnect", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 1, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Ua", "Name": "Ua", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "V_LN/Inst/Value/L1N", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 2, "ParentName": "VoltageL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ub", "Name": "Ub", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "V_LN/Inst/Value/L2N", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 3, "ParentName": "VoltageL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Uc", "Name": "Uc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "V_LN/Inst/Value/L3N", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 4, "ParentName": "VoltageL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Uab", "Name": "Uab", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "V_LL/Inst/Value/L1L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 5, "ParentName": "VoltageL-L_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ubc", "Name": "Ubc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "V_LL/Inst/Value/L2L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 6, "ParentName": "VoltageL-L_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Uca", "Name": "Uca", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "V_LL/Inst/Value/L3L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 7, "ParentName": "VoltageL-L_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ia", "Name": "Ia", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "I/Inst/Value/L1", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 8, "ParentName": "Current_InstantaneousValues_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ib", "Name": "Ib", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "I/Inst/Value/L2", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9, "ParentName": "Current_InstantaneousValues_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ic", "Name": "Ic", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "I/Inst/Value/L3", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 10, "ParentName": "Current_InstantaneousValues_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "In", "Name": "In", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "I/Inst/Value/LN", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 11, "ParentName": "Current_InstantaneousValues_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "I_Agv", "Name": "I_Agv", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "I/Inst/Value/AVG", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 13, "ParentName": "Current_InstantaneousValues_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "P", "Name": "P", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Power/W/Inst/Value/Sum", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 14, "ParentName": "ActivePower_InstantaneousValues_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Pa", "Name": "Pa", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Power/W/Inst/Value/L1", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 15, "ParentName": "ActivePower_InstantaneousValues_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Pb", "Name": "Pb", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Power/W/Inst/Value/L2", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 16, "ParentName": "ActivePower_InstantaneousValues_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Pc", "Name": "Pc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Power/W/Inst/Value/L3", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 17, "ParentName": "ActivePower_InstantaneousValues_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Q", "Name": "Q", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Power/var/Qtot/Inst/Value/Sum", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 18, "ParentName": "MeasuringMethodVARtot_InstantaneousValues_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Qa", "Name": "Qa", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Power/var/Q1/Inst/Value/L1", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19, "ParentName": "MeasuringMethodVARtot_InstantaneousValues_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Qb", "Name": "Qb", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Power/var/Q1/Inst/Value/L2", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 20, "ParentName": "MeasuringMethodVARtot_InstantaneousValues_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Qc", "Name": "Qc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Power/var/Q1/Inst/Value/L3", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 21, "ParentName": "MeasuringMethodVARtot_InstantaneousValues_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "S", "Name": "S", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Power/VA/Inst/Value/Sum", "Unit": "VA", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 22, "ParentName": "ApparentPower_InstantaneousValues_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor", "Name": "PowFactor", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Power/Factor/Inst/Value/AVG", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 23, "ParentName": "PowerFactor_InstantaneousValues_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor_A", "Name": "PowFactor_A", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Power/Factor/Inst/Value/L1", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 24, "ParentName": "PowerFactor_InstantaneousValues_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor_B", "Name": "PowFactor_B", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Power/Factor/Inst/Value/L2", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 25, "ParentName": "PowerFactor_InstantaneousValues_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor_C", "Name": "PowFactor_C", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Power/Factor/Inst/Value/L3", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 26, "ParentName": "PowerFactor_InstantaneousValues_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "F", "Name": "F", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Frequency/Inst/Value/Common", "Unit": "Hz", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 27, "ParentName": "InstantaneousValuesFrequency_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ua", "Name": "THD_Ua", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "THD/V_LN/Inst/Value/L1N#", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 28, "ParentName": "THDVoltageL-L_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ub", "Name": "THD_Ub", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "THD/V_LN/Inst/Value/L2N#", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 29, "ParentName": "THDVoltageL-L_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Uc", "Name": "THD_Uc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "THD/V_LN/Inst/Value/L3N#", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 30, "ParentName": "THDVoltageL-L_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ia", "Name": "THD_Ia", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "THD/I/Inst/Value/L1#", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 31, "ParentName": "InstantaneousValuesTHDCurrent_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ib", "Name": "THD_Ib", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "THD/I/Inst/Value/L2#", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 32, "ParentName": "InstantaneousValuesTHDCurrent_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ic", "Name": "THD_Ic", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "THD/I/Inst/Value/L3#", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 33, "ParentName": "InstantaneousValuesTHDCurrent_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ForwardActivePower", "Name": "ForwardActivePower", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "", "Unit": "Wh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TotalEnergy\"}", "Sort": 34, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ForwardReactivePower", "Name": "ForwardReactivePower", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "", "Unit": "varh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TotalEnergy\"}", "Sort": 35, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ReverseActivePower", "Name": "ReverseActivePower", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "", "Unit": "Wh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TotalEnergy\"}", "Sort": 36, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ReverseReactivePower", "Name": "ReverseReactivePower", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "", "Unit": "varh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TotalEnergy\"}", "Sort": 37, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ForwardActivePower_Tariff1", "Name": "ForwardActivePower_Tariff1", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Energy/Wh/Import/OnPeakTariff/Sum", "Unit": "Wh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 38, "ParentName": "Tariff1_ActiveEnergy", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ForwardReactivePower_Tariff1", "Name": "ForwardReactivePower_Tariff1", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Energy/varh/Import/OnPeakTariff/Sum", "Unit": "varh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 39, "ParentName": "Tariff1_ReactiveEnergy", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ReverseActivePower_Tariff1", "Name": "ReverseActivePower_Tariff1", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Energy/Wh/Export/OnPeakTariff/Sum", "Unit": "Wh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 40, "ParentName": "Tariff1_ActiveEnergy", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ReverseReactivePower_Tariff1", "Name": "ReverseReactivePower_Tariff1", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Energy/varh/Export/OnPeakTariff/Sum", "Unit": "varh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 41, "ParentName": "Tariff1_ReactiveEnergy", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ForwardActivePower_Tariff2", "Name": "ForwardActivePower_Tariff2", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Energy/Wh/Import/OffPeakTariff/Sum", "Unit": "Wh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 42, "ParentName": "Tariff2_ActiveEnergy", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ForwardReactivePower_Tariff2", "Name": "ForwardReactivePower_Tariff2", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Energy/varh/Import/OffPeakTariff/Sum", "Unit": "varh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 43, "ParentName": "Tariff2_ReactiveEnergy", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ReverseActivePower_Tariff2", "Name": "ReverseActivePower_Tariff2", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Energy/Wh/Export/OffPeakTariff/Sum", "Unit": "Wh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 44, "ParentName": "Tariff2_ActiveEnergy", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ReverseReactivePower_Tariff2", "Name": "ReverseReactivePower_Tariff2", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Energy/varh/Export/OffPeakTariff/Sum", "Unit": "varh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 45, "ParentName": "Tariff2_ReactiveEnergy", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "DO_0.0", "Name": "DO_0.0", "GroupName": "DigitalInputOutput", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Output_Status/Output_Status_Device/DO_0.0_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 46, "ParentName": "DigitalOutputs_Device", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "DO_0.1", "Name": "DO_0.1", "GroupName": "DigitalInputOutput", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Output_Status/Output_Status_Device/DO_0.1_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 47, "ParentName": "DigitalOutputs_Device", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "DO_4.0", "Name": "DO_4.0", "GroupName": "DigitalInputOutput", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Output_Status/Output_Status_Slot1/DO_4.0_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 48, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "DO_4.1", "Name": "DO_4.1", "GroupName": "DigitalInputOutput", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Output_Status/Output_Status_Slot1/DO_4.1_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 49, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "DO_8.0", "Name": "DO_8.0", "GroupName": "DigitalInputOutput", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Output_Status/Output_Status_Slot2/DO_8.0_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 50, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "DO_8.1", "Name": "DO_8.1", "GroupName": "DigitalInputOutput", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Output_Status/Output_Status_Slot2/DO_8.1_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 51, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "DI_0.0", "Name": "DI_0.0", "GroupName": "DigitalInputOutput", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Input_Status/Input_Status_Device/DI_0.0_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 52, "ParentName": "DigitalInputs_Device", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "DI_0.1", "Name": "DI_0.1", "GroupName": "DigitalInputOutput", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Input_Status/Input_Status_Device/DI_0.1_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 53, "ParentName": "DigitalInputs_Device", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "DI_4.0", "Name": "DI_4.0", "GroupName": "DigitalInputOutput", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Input_Status/Input_Status_Slot1/DI_4.0_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 54, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "DI_4.1", "Name": "DI_4.1", "GroupName": "DigitalInputOutput", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Input_Status/Input_Status_Slot1/DI_4.1_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 55, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "DI_4.2", "Name": "DI_4.2", "GroupName": "DigitalInputOutput", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Input_Status/Input_Status_Slot1/DI_4.2_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 56, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "DI_4.3", "Name": "DI_4.3", "GroupName": "DigitalInputOutput", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Input_Status/Input_Status_Slot1/DI_4.3_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 57, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "DI_8.0", "Name": "DI_8.0", "GroupName": "DigitalInputOutput", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Input_Status/Input_Status_Slot2/DI_8.0_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 58, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "DI_8.1", "Name": "DI_8.1", "GroupName": "DigitalInputOutput", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Input_Status/Input_Status_Slot2/DI_8.1_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 59, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "DI_8.2", "Name": "DI_8.2", "GroupName": "DigitalInputOutput", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Input_Status/Input_Status_Slot2/DI_8.2_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 60, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "DI_8.3", "Name": "DI_8.3", "GroupName": "DigitalInputOutput", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Input_Status/Input_Status_Slot2/DI_8.3_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 61, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LimitMonitoring_0", "Name": "LimitMonitoring_0", "GroupName": "LimitMonitoring", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "LimitMonitoring/0_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 62, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LimitMonitoring_1", "Name": "LimitMonitoring_1", "GroupName": "LimitMonitoring", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "LimitMonitoring/1_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 63, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LimitMonitoring_2", "Name": "LimitMonitoring_2", "GroupName": "LimitMonitoring", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "LimitMonitoring/2_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 64, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LimitMonitoring_3", "Name": "LimitMonitoring_3", "GroupName": "LimitMonitoring", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "LimitMonitoring/3_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 65, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LimitMonitoring_4", "Name": "LimitMonitoring_4", "GroupName": "LimitMonitoring", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "LimitMonitoring/4_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 66, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LimitMonitoring_5", "Name": "LimitMonitoring_5", "GroupName": "LimitMonitoring", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "LimitMonitoring/5_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 67, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LimitMonitoring_6", "Name": "LimitMonitoring_6", "GroupName": "LimitMonitoring", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "LimitMonitoring/6_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 68, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LimitMonitoring_7", "Name": "LimitMonitoring_7", "GroupName": "LimitMonitoring", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "LimitMonitoring/7_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 69, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LimitMonitoring_8", "Name": "LimitMonitoring_8", "GroupName": "LimitMonitoring", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "LimitMonitoring/8_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 70, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LimitMonitoring_9", "Name": "LimitMonitoring_9", "GroupName": "LimitMonitoring", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "LimitMonitoring/9_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 71, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LimitMonitoring_10", "Name": "LimitMonitoring_10", "GroupName": "LimitMonitoring", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "LimitMonitoring/10_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 72, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LimitMonitoring_11", "Name": "LimitMonitoring_11", "GroupName": "LimitMonitoring", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "LimitMonitoring/11_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 73, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LogicResult", "Name": "LogicResult", "GroupName": "LogicFunction", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "LogicFunction/Result_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 74, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LogicFunction_1", "Name": "LogicFunction_1", "GroupName": "LogicFunction", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "LogicFunction/Gate1_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 75, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LogicFunction_2", "Name": "LogicFunction_2", "GroupName": "LogicFunction", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "LogicFunction/Gate2_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 76, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LogicFunction_3", "Name": "LogicFunction_3", "GroupName": "LogicFunction", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "LogicFunction/Gate3_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 77, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LogicFunction_4", "Name": "LogicFunction_4", "GroupName": "LogicFunction", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "LogicFunction/Gate4_", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 78, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "OperatingHours", "Name": "OperatingHours", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Counter/OperatingHours", "Unit": "h", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 79, "ParentName": "Counters", "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Actual<PERSON><PERSON><PERSON>", "Name": "Actual<PERSON><PERSON><PERSON>", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "CostManagement/actual tariff", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 80, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Slot_1", "Name": "Slot_1", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Slot1_ModuleInfo_OrderNumber", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"SlotFunc\"}", "Sort": 81, "ParentName": "LocalDeviceStatus", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Slot_2", "Name": "Slot_2", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "Slot2_ModuleInfo_OrderNumber", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"SlotFunc\"}", "Sort": 82, "ParentName": "LocalDeviceStatus", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "MLFB", "Name": "MLFB", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "OrderNumber", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "SerialNumber", "Name": "SerialNumber", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "SerialNumber", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "UseVoltageTransformer", "Name": "UseVoltageTransformer", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "UseVoltageTransformer", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 85, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "PrimaryVoltage", "Name": "PrimaryVoltage", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "PrimaryVoltage", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 86, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "SecondaryVoltage", "Name": "SecondaryVoltage", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "SecondaryVoltage", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 87, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "PrimaryCurrent", "Name": "PrimaryCurrent", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "PrimaryCurrent", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 88, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "SecondaryCurrent", "Name": "SecondaryCurrent", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "SecondaryCurrent", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 89, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_1", "Name": "Harmonic_Ua_1", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/1/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 90, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_1", "Name": "Harmonic_Ub_1", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/1/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 91, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_1", "Name": "Harmonic_Uc_1", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/1/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 92, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_2", "Name": "Harmonic_Ua_2", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/2/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 93, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_2", "Name": "Harmonic_Ub_2", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/2/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 94, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_2", "Name": "Harmonic_Uc_2", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/2/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 95, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_3", "Name": "Harmonic_Ua_3", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/3/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 96, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_3", "Name": "Harmonic_Ub_3", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/3/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 97, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_3", "Name": "Harmonic_Uc_3", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/3/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 98, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_4", "Name": "Harmonic_Ua_4", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/4/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 99, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_4", "Name": "Harmonic_Ub_4", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/4/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 100, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_4", "Name": "Harmonic_Uc_4", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/4/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 101, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_5", "Name": "Harmonic_Ua_5", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/5/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 102, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_5", "Name": "Harmonic_Ub_5", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/5/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 103, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_5", "Name": "Harmonic_Uc_5", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/5/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 104, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_6", "Name": "Harmonic_Ua_6", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/6/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 105, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_6", "Name": "Harmonic_Ub_6", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/6/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 106, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_6", "Name": "Harmonic_Uc_6", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/6/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 107, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_7", "Name": "Harmonic_Ua_7", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/7/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 108, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_7", "Name": "Harmonic_Ub_7", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/7/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 109, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_7", "Name": "Harmonic_Uc_7", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/7/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 110, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_8", "Name": "Harmonic_Ua_8", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/8/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 111, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_8", "Name": "Harmonic_Ub_8", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/8/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 112, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_8", "Name": "Harmonic_Uc_8", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/8/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 113, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_9", "Name": "Harmonic_Ua_9", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/9/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 114, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_9", "Name": "Harmonic_Ub_9", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/9/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 115, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_9", "Name": "Harmonic_Uc_9", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/9/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 116, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_10", "Name": "Harmonic_Ua_10", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/10/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 117, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_10", "Name": "Harmonic_Ub_10", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/10/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 118, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_10", "Name": "Harmonic_Uc_10", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/10/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 119, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_11", "Name": "Harmonic_Ua_11", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/11/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 120, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_11", "Name": "Harmonic_Ub_11", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/11/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 121, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_11", "Name": "Harmonic_Uc_11", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/11/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 122, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_12", "Name": "Harmonic_Ua_12", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/12/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 123, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_12", "Name": "Harmonic_Ub_12", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/12/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 124, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_12", "Name": "Harmonic_Uc_12", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/12/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 125, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_13", "Name": "Harmonic_Ua_13", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/13/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 126, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_13", "Name": "Harmonic_Ub_13", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/13/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 127, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_13", "Name": "Harmonic_Uc_13", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/13/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 128, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_14", "Name": "Harmonic_Ua_14", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/14/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 129, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_14", "Name": "Harmonic_Ub_14", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/14/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 130, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_14", "Name": "Harmonic_Uc_14", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/14/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 131, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_15", "Name": "Harmonic_Ua_15", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/15/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 132, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_15", "Name": "Harmonic_Ub_15", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/15/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 133, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_15", "Name": "Harmonic_Uc_15", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/15/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 134, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_16", "Name": "Harmonic_Ua_16", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/16/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 135, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_16", "Name": "Harmonic_Ub_16", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/16/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 136, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_16", "Name": "Harmonic_Uc_16", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/16/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 137, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_17", "Name": "Harmonic_Ua_17", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/17/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 138, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_17", "Name": "Harmonic_Ub_17", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/17/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 139, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_17", "Name": "Harmonic_Uc_17", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/17/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 140, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_18", "Name": "Harmonic_Ua_18", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/18/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 141, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_18", "Name": "Harmonic_Ub_18", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/18/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 142, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_18", "Name": "Harmonic_Uc_18", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/18/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 143, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_19", "Name": "Harmonic_Ua_19", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/19/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 144, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_19", "Name": "Harmonic_Ub_19", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/19/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 145, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_19", "Name": "Harmonic_Uc_19", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/19/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 146, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_20", "Name": "Harmonic_Ua_20", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/20/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 147, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_20", "Name": "Harmonic_Ub_20", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/20/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 148, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_20", "Name": "Harmonic_Uc_20", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/20/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 149, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_21", "Name": "Harmonic_Ua_21", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/21/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 150, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_21", "Name": "Harmonic_Ub_21", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/21/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 151, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_21", "Name": "Harmonic_Uc_21", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/21/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 152, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_22", "Name": "Harmonic_Ua_22", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/22/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 153, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_22", "Name": "Harmonic_Ub_22", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/22/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 154, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_22", "Name": "Harmonic_Uc_22", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/22/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 155, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_23", "Name": "Harmonic_Ua_23", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/23/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 156, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_23", "Name": "Harmonic_Ub_23", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/23/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 157, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_23", "Name": "Harmonic_Uc_23", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/23/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 158, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_24", "Name": "Harmonic_Ua_24", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/24/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 159, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_24", "Name": "Harmonic_Ub_24", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/24/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 160, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_24", "Name": "Harmonic_Uc_24", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/24/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 161, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_25", "Name": "Harmonic_Ua_25", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/25/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 162, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_25", "Name": "Harmonic_Ub_25", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/25/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 163, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_25", "Name": "Harmonic_Uc_25", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/25/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 164, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_26", "Name": "Harmonic_Ua_26", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/26/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 165, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_26", "Name": "Harmonic_Ub_26", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/26/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 166, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_26", "Name": "Harmonic_Uc_26", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/26/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 167, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_27", "Name": "Harmonic_Ua_27", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/27/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 168, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_27", "Name": "Harmonic_Ub_27", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/27/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 169, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_27", "Name": "Harmonic_Uc_27", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/27/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 170, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_28", "Name": "Harmonic_Ua_28", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/28/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 171, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_28", "Name": "Harmonic_Ub_28", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/28/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 172, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_28", "Name": "Harmonic_Uc_28", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/28/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 173, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_29", "Name": "Harmonic_Ua_29", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/29/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 174, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_29", "Name": "Harmonic_Ub_29", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/29/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 175, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_29", "Name": "Harmonic_Uc_29", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/29/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 176, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_30", "Name": "Harmonic_Ua_30", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/30/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 177, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_30", "Name": "Harmonic_Ub_30", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/30/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 178, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_30", "Name": "Harmonic_Uc_30", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/30/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 179, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_31", "Name": "Harmonic_Ua_31", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/31/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 180, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_31", "Name": "Harmonic_Ub_31", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/31/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 181, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_31", "Name": "Harmonic_Uc_31", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/31/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 182, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_32", "Name": "Harmonic_Ua_32", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/32/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 183, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_32", "Name": "Harmonic_Ub_32", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/32/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 184, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_32", "Name": "Harmonic_Uc_32", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/32/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 185, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_33", "Name": "Harmonic_Ua_33", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/33/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 186, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_33", "Name": "Harmonic_Ub_33", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/33/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 187, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_33", "Name": "Harmonic_Uc_33", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/33/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 188, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_34", "Name": "Harmonic_Ua_34", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/34/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 189, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_34", "Name": "Harmonic_Ub_34", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/34/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 190, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_34", "Name": "Harmonic_Uc_34", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/34/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 191, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_35", "Name": "Harmonic_Ua_35", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/35/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 192, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_35", "Name": "Harmonic_Ub_35", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/35/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 193, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_35", "Name": "Harmonic_Uc_35", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/35/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 194, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_36", "Name": "Harmonic_Ua_36", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/36/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 195, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_36", "Name": "Harmonic_Ub_36", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/36/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 196, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_36", "Name": "Harmonic_Uc_36", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/36/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 197, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_37", "Name": "Harmonic_Ua_37", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/37/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 198, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_37", "Name": "Harmonic_Ub_37", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/37/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 199, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_37", "Name": "Harmonic_Uc_37", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/37/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 200, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_38", "Name": "Harmonic_Ua_38", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/38/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 201, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_38", "Name": "Harmonic_Ub_38", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/38/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 202, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_38", "Name": "Harmonic_Uc_38", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/38/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 203, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_39", "Name": "Harmonic_Ua_39", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/39/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 204, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_39", "Name": "Harmonic_Ub_39", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/39/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 205, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_39", "Name": "Harmonic_Uc_39", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/39/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 206, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_40", "Name": "Harmonic_Ua_40", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/40/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 207, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_40", "Name": "Harmonic_Ub_40", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/40/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 208, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_40", "Name": "Harmonic_Uc_40", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/40/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 209, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_41", "Name": "Harmonic_Ua_41", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/41/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 210, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_41", "Name": "Harmonic_Ub_41", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/41/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 211, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_41", "Name": "Harmonic_Uc_41", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/41/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 212, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_42", "Name": "Harmonic_Ua_42", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/42/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 213, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_42", "Name": "Harmonic_Ub_42", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/42/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 214, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_42", "Name": "Harmonic_Uc_42", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/42/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 215, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_43", "Name": "Harmonic_Ua_43", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/43/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 216, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_43", "Name": "Harmonic_Ub_43", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/43/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 217, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_43", "Name": "Harmonic_Uc_43", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/43/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 218, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_44", "Name": "Harmonic_Ua_44", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/44/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 219, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_44", "Name": "Harmonic_Ub_44", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/44/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 220, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_44", "Name": "Harmonic_Uc_44", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/44/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 221, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_45", "Name": "Harmonic_Ua_45", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/45/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 222, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_45", "Name": "Harmonic_Ub_45", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/45/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 223, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_45", "Name": "Harmonic_Uc_45", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/45/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 224, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_46", "Name": "Harmonic_Ua_46", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/46/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 225, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_46", "Name": "Harmonic_Ub_46", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/46/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 226, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_46", "Name": "Harmonic_Uc_46", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/46/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 227, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_47", "Name": "Harmonic_Ua_47", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/47/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 228, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_47", "Name": "Harmonic_Ub_47", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/47/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 229, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_47", "Name": "Harmonic_Uc_47", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/47/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 230, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_48", "Name": "Harmonic_Ua_48", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/48/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 231, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_48", "Name": "Harmonic_Ub_48", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/48/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 232, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_48", "Name": "Harmonic_Uc_48", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/48/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 233, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_49", "Name": "Harmonic_Ua_49", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/49/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 234, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_49", "Name": "Harmonic_Ub_49", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/49/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 235, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_49", "Name": "Harmonic_Uc_49", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/49/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 236, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_50", "Name": "Harmonic_Ua_50", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/50/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 237, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_50", "Name": "Harmonic_Ub_50", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/50/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 238, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_50", "Name": "Harmonic_Uc_50", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/50/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 239, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_51", "Name": "Harmonic_Ua_51", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/51/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 240, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_51", "Name": "Harmonic_Ub_51", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/51/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 241, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_51", "Name": "Harmonic_Uc_51", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/51/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 242, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_52", "Name": "Harmonic_Ua_52", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/52/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 243, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_52", "Name": "Harmonic_Ub_52", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/52/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 244, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_52", "Name": "Harmonic_Uc_52", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/52/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 245, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_53", "Name": "Harmonic_Ua_53", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/53/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 246, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_53", "Name": "Harmonic_Ub_53", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/53/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 247, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_53", "Name": "Harmonic_Uc_53", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/53/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 248, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_54", "Name": "Harmonic_Ua_54", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/54/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 249, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_54", "Name": "Harmonic_Ub_54", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/54/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 250, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_54", "Name": "Harmonic_Uc_54", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/54/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 251, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_55", "Name": "Harmonic_Ua_55", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/55/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 252, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_55", "Name": "Harmonic_Ub_55", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/55/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 253, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_55", "Name": "Harmonic_Uc_55", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/55/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 254, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_56", "Name": "Harmonic_Ua_56", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/56/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 255, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_56", "Name": "Harmonic_Ub_56", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/56/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 256, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_56", "Name": "Harmonic_Uc_56", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/56/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 257, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_57", "Name": "Harmonic_Ua_57", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/57/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 258, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_57", "Name": "Harmonic_Ub_57", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/57/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 259, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_57", "Name": "Harmonic_Uc_57", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/57/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 260, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_58", "Name": "Harmonic_Ua_58", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/58/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 261, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_58", "Name": "Harmonic_Ub_58", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/58/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 262, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_58", "Name": "Harmonic_Uc_58", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/58/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 263, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_59", "Name": "Harmonic_Ua_59", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/59/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 264, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_59", "Name": "Harmonic_Ub_59", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/59/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 265, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_59", "Name": "Harmonic_Uc_59", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/59/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 266, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_60", "Name": "Harmonic_Ua_60", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/60/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 267, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_60", "Name": "Harmonic_Ub_60", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/60/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 268, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_60", "Name": "Harmonic_Uc_60", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/60/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 269, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_61", "Name": "Harmonic_Ua_61", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/61/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 270, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_61", "Name": "Harmonic_Ub_61", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/61/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 271, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_61", "Name": "Harmonic_Uc_61", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/61/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 272, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_62", "Name": "Harmonic_Ua_62", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/62/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 273, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_62", "Name": "Harmonic_Ub_62", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/62/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 274, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_62", "Name": "Harmonic_Uc_62", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/62/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 275, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_63", "Name": "Harmonic_Ua_63", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/63/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 276, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_63", "Name": "Harmonic_Ub_63", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/63/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 277, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_63", "Name": "Harmonic_Uc_63", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/63/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 278, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ua_64", "Name": "Harmonic_Ua_64", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/64/Inst/Value/L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 279, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Ub_64", "Name": "Harmonic_Ub_64", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/64/Inst/Value/L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 280, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Harmonic_Uc_64", "Name": "Harmonic_Uc_64", "GroupName": "Harmonic", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC4200", "FilterIds": "", "UdcCode": "HV_LNF/64/Inst/Value/L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 281, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}]