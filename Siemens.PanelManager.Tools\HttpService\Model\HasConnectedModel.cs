﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Tools.HttpService.Model
{
    internal class HasConnectedModel
    {
        [JsonProperty(PropertyName = "ip")]
        public string IP { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "isConnected")]
        public bool IsConnected { get; set; }
    }
}
