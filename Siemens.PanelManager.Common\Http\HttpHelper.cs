﻿using Newtonsoft.Json;
using System.Text;

namespace Siemens.PanelManager.Common.Http
{
    /// <summary>
    /// HttpClient帮助类
    /// </summary>
    public class HttpHelper
    {
        /// <summary>
        /// 私有构造函数防止实例化
        /// </summary>
        private HttpHelper() { }

        /// <summary>
        /// 线程安全的单例模式
        /// </summary>
        private static readonly HttpHelper Sinstance = new HttpHelper();

        /// <summary>
        /// 建立外部访问器
        /// </summary>
        /// <returns></returns>
        public static HttpHelper Instance
        {
            get { return Sinstance; }
        }

        /// <summary>
        /// 异步获取httpGet请求
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public async Task<ResultDto?> GetDataAsync(string? url)
        {
            ResultDto? result;
            try
            {
                //忽略掉证书异常
                var httpClientHandler = new HttpClientHandler
                {
                    ServerCertificateCustomValidationCallback = (_,_,_,_) => true
                };

                using var client = new HttpClient(httpClientHandler);
                client.Timeout = TimeSpan.FromSeconds(2);
                //client.DefaultRequestHeaders.Add("appId", Options.AppId);
                //client.DefaultRequestHeaders.Add("appSecret", Options.AppSecret);
                HttpResponseMessage res = await client.GetAsync(url);

                if (res.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    return new ResultDto { Code = (int)res.StatusCode, Msg = res.ReasonPhrase };
                }

                string jsonBody = await res.Content.ReadAsStringAsync();
                result = JsonConvert.DeserializeObject<ResultDto>(jsonBody);
            }
            catch (Exception ex)
            {
                result = new ResultDto { Code = 40400, Msg = $"请求数据超时, 异常信息: {ex.Message}" };
            }

            return result;
        }


        /// <summary>
        /// 异步获取httpPost请求
        /// </summary>
        /// <param name="url"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        public async Task<ResultDto?> PostDataAsync(string url, dynamic data)
        {
            ResultDto? result;
            try
            {
                //忽略掉证书异常
                var httpClientHandler = new HttpClientHandler
                {
                    ServerCertificateCustomValidationCallback = (_, _, _, _) => true
                };

                using var client = new HttpClient(httpClientHandler);
                client.Timeout = TimeSpan.FromSeconds(2);
                var paramBody = JsonConvert.SerializeObject(data);
                var httpParam = new StringContent(paramBody, Encoding.UTF8, "application/json");
                //client.DefaultRequestHeaders.Add("appId", Options.AppId);
                //client.DefaultRequestHeaders.Add("appSecret", Options.AppSecret);
                HttpResponseMessage res = await client.PostAsync(url, httpParam);

                if (res.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    return new ResultDto { Code = (int)res.StatusCode, Msg = res.ReasonPhrase };
                }

                string jsonBody = await res.Content.ReadAsStringAsync();
                //获取返回的json对象
                result = JsonConvert.DeserializeObject<ResultDto>(jsonBody);
            }
            catch (Exception ex)
            {
                result = new ResultDto { Code = 40400, Msg = $"请求数据超时, 异常信息: {ex.Message}" };
            }

            return result;
        }
    }

    /// <summary>
    /// 返回值对象
    /// </summary>
    public class ResultDto
    {
        /// <summary>
        /// 是否成功(true,false)
        /// </summary>
        [JsonProperty("isSuccess")]
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 返回结果状态码
        /// </summary>
        [JsonProperty("code")]
        public int Code { get; set; }

        /// <summary>
        /// 返回执行结果的消息
        /// </summary>
        [JsonProperty("msg")]
        public string? Msg { get; set; }
    }

}
