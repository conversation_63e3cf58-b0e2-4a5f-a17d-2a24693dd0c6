[{"Code": "HaveAlarm", "Name": "HaveAlarm", "GroupName": "Status", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "Alarm", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 1, "ParentName": "Status"}, {"Code": "Ua", "Name": "Ua", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "V", "CollectMode": "CommonPanel", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 2, "ParentName": "Measurement"}, {"Code": "Ub", "Name": "Ub", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "V", "CollectMode": "CommonPanel", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 3, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Uc", "Name": "Uc", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "V", "CollectMode": "CommonPanel", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 4, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Uab", "Name": "Uab", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "V", "CollectMode": "CommonPanel", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 5, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Ubc", "Name": "Ubc", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "V", "CollectMode": "CommonPanel", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 6, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Uca", "Name": "Uca", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "V", "CollectMode": "CommonPanel", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 7, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Ia", "Name": "Ia", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "A", "CollectMode": "CommonPanel", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 8, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Ib", "Name": "Ib", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "A", "CollectMode": "CommonPanel", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Ic", "Name": "Ic", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "A", "CollectMode": "CommonPanel", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 10, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "I_Agv", "Name": "I_Agv", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "A", "CollectMode": "CommonPanel", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 12, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "P", "Name": "P", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "W", "CollectMode": "CommonPanel", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 13, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Pa", "Name": "Pa", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 14, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Pb", "Name": "Pb", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 15, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Pc", "Name": "Pc", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 16, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Q", "Name": "Q", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "var", "CollectMode": "CommonPanel", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 17, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Qa", "Name": "Qa", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 18, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Qb", "Name": "Qb", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Qc", "Name": "Qc", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 20, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "S", "Name": "S", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "VA", "CollectMode": "CommonPanel", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 21, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "F", "Name": "F", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "Hz", "CollectMode": "CommonPanel", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 22, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "PowFactor", "Name": "PowFactor", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "CommonPanel", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 23, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "THD_Ua", "Name": "THD_Ua", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "%", "CollectMode": "CommonPanel", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 24, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "THD_Ub", "Name": "THD_Ub", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "%", "CollectMode": "CommonPanel", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 25, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "THD_Uc", "Name": "THD_Uc", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "%", "CollectMode": "CommonPanel", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 26, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "THD_U", "Name": "THD_U", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "%", "CollectMode": "CommonPanel", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 28, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "THD_Ia", "Name": "THD_Ia", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "%", "CollectMode": "CommonPanel", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 29, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "THD_Ib", "Name": "THD_Ib", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "%", "CollectMode": "CommonPanel", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 30, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "THD_Ic", "Name": "THD_Ic", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "%", "CollectMode": "CommonPanel", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 31, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "THD_I", "Name": "THD_I", "GroupName": "Measurement", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "%", "CollectMode": "CommonPanel", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 33, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "AConnectPoint_1_1", "Name": "AConnectPoint_1_1", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 34, "ParentName": "BusBarTemperature"}, {"Code": "BConnectPoint_1_1", "Name": "BConnectPoint_1_1", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 35, "ParentName": "BusBarTemperature"}, {"Code": "CConnectPoint_1_1", "Name": "CConnectPoint_1_1", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 36, "ParentName": "BusBarTemperature"}, {"Code": "NConnectPoint_1_1", "Name": "NConnectPoint_1_1", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 37, "ParentName": "BusBarTemperature"}, {"Code": "AConnectPoint_1_2", "Name": "AConnectPoint_1_2", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 38, "ParentName": "BusBarTemperature"}, {"Code": "BConnectPoint_1_2", "Name": "BConnectPoint_1_2", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 39, "ParentName": "BusBarTemperature"}, {"Code": "CConnectPoint_1_2", "Name": "CConnectPoint_1_2", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 40, "ParentName": "BusBarTemperature"}, {"Code": "NConnectPoint_1_2", "Name": "NConnectPoint_1_2", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 41, "ParentName": "BusBarTemperature"}, {"Code": "AConnectPoint_1_3", "Name": "AConnectPoint_1_3", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 42, "ParentName": "BusBarTemperature"}, {"Code": "BConnectPoint_1_3", "Name": "BConnectPoint_1_3", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 43, "ParentName": "BusBarTemperature"}, {"Code": "CConnectPoint_1_3", "Name": "CConnectPoint_1_3", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 44, "ParentName": "BusBarTemperature"}, {"Code": "NConnectPoint_1_3", "Name": "NConnectPoint_1_3", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 45, "ParentName": "BusBarTemperature"}, {"Code": "AConnectPoint_1_4", "Name": "AConnectPoint_1_4", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 46, "ParentName": "BusBarTemperature"}, {"Code": "BConnectPoint_1_4", "Name": "BConnectPoint_1_4", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 47, "ParentName": "BusBarTemperature"}, {"Code": "CConnectPoint_1_4", "Name": "CConnectPoint_1_4", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 48, "ParentName": "BusBarTemperature"}, {"Code": "NConnectPoint_1_4", "Name": "NConnectPoint_1_4", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 49, "ParentName": "BusBarTemperature"}, {"Code": "AConnectPoint_2_1", "Name": "AConnectPoint_2_1", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 50, "ParentName": "BusBarTemperature"}, {"Code": "BConnectPoint_2_1", "Name": "BConnectPoint_2_1", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 51, "ParentName": "BusBarTemperature"}, {"Code": "CConnectPoint_2_1", "Name": "CConnectPoint_2_1", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 52, "ParentName": "BusBarTemperature"}, {"Code": "NConnectPoint_2_1", "Name": "NConnectPoint_2_1", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 53, "ParentName": "BusBarTemperature"}, {"Code": "AConnectPoint_2_2", "Name": "AConnectPoint_2_2", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 54, "ParentName": "BusBarTemperature"}, {"Code": "BConnectPoint_2_2", "Name": "BConnectPoint_2_2", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 55, "ParentName": "BusBarTemperature"}, {"Code": "CConnectPoint_2_2", "Name": "CConnectPoint_2_2", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 56, "ParentName": "BusBarTemperature"}, {"Code": "NConnectPoint_2_2", "Name": "NConnectPoint_2_2", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 57, "ParentName": "BusBarTemperature"}, {"Code": "AConnectPoint_2_3", "Name": "AConnectPoint_2_3", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 58, "ParentName": "BusBarTemperature"}, {"Code": "BConnectPoint_2_3", "Name": "BConnectPoint_2_3", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 59, "ParentName": "BusBarTemperature"}, {"Code": "CConnectPoint_2_3", "Name": "CConnectPoint_2_3", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 60, "ParentName": "BusBarTemperature"}, {"Code": "NConnectPoint_2_3", "Name": "NConnectPoint_2_3", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 61, "ParentName": "BusBarTemperature"}, {"Code": "AConnectPoint_2_4", "Name": "AConnectPoint_2_4", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 62, "ParentName": "BusBarTemperature"}, {"Code": "BConnectPoint_2_4", "Name": "BConnectPoint_2_4", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 63, "ParentName": "BusBarTemperature"}, {"Code": "CConnectPoint_2_4", "Name": "CConnectPoint_2_4", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 64, "ParentName": "BusBarTemperature"}, {"Code": "NConnectPoint_2_4", "Name": "NConnectPoint_2_4", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 65, "ParentName": "BusBarTemperature"}, {"Code": "AConnector_Left1", "Name": "AConnector_Left1", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 66, "ParentName": "BusBarTemperature"}, {"Code": "BConnector_Left1", "Name": "BConnector_Left1", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 67, "ParentName": "BusBarTemperature"}, {"Code": "CConnector_Left1", "Name": "CConnector_Left1", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 68, "ParentName": "BusBarTemperature"}, {"Code": "NConnector_Left1", "Name": "NConnector_Left1", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 69, "ParentName": "BusBarTemperature"}, {"Code": "AConnector_Right1", "Name": "AConnector_Right1", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 70, "ParentName": "BusBarTemperature"}, {"Code": "BConnector_Right1", "Name": "BConnector_Right1", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 71, "ParentName": "BusBarTemperature"}, {"Code": "CConnector_Right1", "Name": "CConnector_Right1", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 72, "ParentName": "BusBarTemperature"}, {"Code": "NConnector_Right1", "Name": "NConnector_Right1", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 73, "ParentName": "BusBarTemperature"}, {"Code": "AConnector_Left2", "Name": "AConnector_Left2", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 74, "ParentName": "BusBarTemperature"}, {"Code": "BConnector_Left2", "Name": "BConnector_Left2", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 75, "ParentName": "BusBarTemperature"}, {"Code": "CConnector_Left2", "Name": "CConnector_Left2", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 76, "ParentName": "BusBarTemperature"}, {"Code": "NConnector_Left2", "Name": "NConnector_Left2", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 77, "ParentName": "BusBarTemperature"}, {"Code": "AConnector_Right2", "Name": "AConnector_Right2", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 78, "ParentName": "BusBarTemperature"}, {"Code": "BConnector_Right2", "Name": "BConnector_Right2", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 79, "ParentName": "BusBarTemperature"}, {"Code": "CConnector_Right2", "Name": "CConnector_Right2", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 80, "ParentName": "BusBarTemperature"}, {"Code": "NConnector_Right2", "Name": "NConnector_Right2", "GroupName": "BusBarTemperature", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 81, "ParentName": "BusBarTemperature"}, {"Code": "Alarm_Severity", "Name": "Alarm_Severity", "GroupName": "Warnings", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Alarm/Value/AlarmSeverity", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 82, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Status", "Name": "Alarm_Status", "GroupName": "Warnings", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Alarm/Value/AlarmStatus", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 83, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Path", "Name": "Alarm_Path", "GroupName": "Warnings", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Alarm/Value/AlarmPath", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 84, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Info", "Name": "Alarm_Info", "GroupName": "Warnings", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Alarm/Value/AlarmInfo", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 85, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Time", "Name": "Alarm_Time", "GroupName": "Warnings", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Alarm/Value/AlarmTime", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 86, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "HealthScore", "Name": "HealthScore", "GroupName": "Others", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "HealthScore", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 87, "ParentName": "Others"}, {"Code": "Health_Grade", "Name": "Health_Grade", "GroupName": "Others", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Health_Grade", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 88, "ParentName": "Others"}, {"Code": "Abnormal_Count", "Name": "Abnormal_Count", "GroupName": "Others", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Abnormal_Count", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 89, "ParentName": "Others"}, {"Code": "Alarm_Status_Count", "Name": "Alarm_Status_Count", "GroupName": "Others", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Alarm_Status_Count", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 90, "ParentName": "Others"}, {"Code": "Max_Temperature", "Name": "Max_Temperature", "GroupName": "Others", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Max_Temperature", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 91, "ParentName": "Others"}, {"Code": "Max_Temperature_Status", "Name": "Max_Temperature_Status", "GroupName": "Others", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Max_Temperature_Status", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 92, "ParentName": "Others"}, {"Code": "Max_Electricity", "Name": "Max_Electricity", "GroupName": "Others", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Max_Temperature_Status", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 93, "ParentName": "Others"}, {"Code": "Max_Electricity_Status", "Name": "Max_Electricity_Status", "GroupName": "Others", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Max_Electricity_Status", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 94, "ParentName": "Others"}, {"Code": "Remaining_Life_Percentage", "Name": "Remaining_Life_Percentage", "GroupName": "Others", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Remaining_Life_Percentage", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 95, "ParentName": "Others"}, {"Code": "Remaining_Life_Status", "Name": "Remaining_Life_Status", "GroupName": "Others", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Remaining_Life_Status", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 96, "ParentName": "Others"}, {"Code": "Panel_Custom_Indicators", "Name": "Panel_Custom_Indicators", "GroupName": "Others", "AssetLevel": 30, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Panel_Custom_Indicators", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 97, "ParentName": "Others"}]