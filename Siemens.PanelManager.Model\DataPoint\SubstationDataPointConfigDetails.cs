﻿using Siemens.PanelManager.Model.Database.Asset;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.DataPoint
{
    public class SubstationDataPointConfigDetails
    {
        public int Id { get; set; }
        public string ShowName { get; set; } = string.Empty;
        public string DataPointCode { get; set; } = string.Empty;
        public LayoutFormat ShowType { get; set; }
        public int BindAssetId { get; set; }
        public string? BindAssetModel { get; set; }
        public string? BindAssetType { get; set; }
        public AssetLevel BindAssetLevel { get; set; }
        public string BindAssetName { get; set; } = string.Empty;
        public string BindDataPoint { get; set; } = string.Empty;
        public string BindDataPointName { get; set; } = string.Empty;
        public string BindDataPointUnit { get; set; } = string.Empty;
    }
}
