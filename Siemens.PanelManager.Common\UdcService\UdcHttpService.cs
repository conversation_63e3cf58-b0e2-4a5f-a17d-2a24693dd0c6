﻿using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Siemens.PanelManager.Model.UDC;
using System.Net;
using System.Text;

namespace Siemens.PanelManager.Common.UdcService
{
    public sealed class UdcHttpService : IDisposable
    {
        private readonly HttpClient _httpClient = null!;
        private readonly ILogger<UdcHttpService> _logger = null!;

        private readonly string _itemUrl = "api/v1/items";
        private readonly string _cancelUrl = "api/v1/items/{DeviceId}/jobs?cancel=true&filter=type:scan_id,scan_gw&filter=status.state:inactive,active";
        private readonly string _createScanJobUrl = "api/v1/items/{DeviceId}/jobs";
        private readonly string _scanProcessUrl = "api/v1/items/{DeviceId}/jobs/{JobId}";
        private readonly string _scanResultUrl = "api/v1/items/{DeviceId}/jobs/{JobId}/result";
        private readonly string _uploadSplxFileUrl = "api/v1/items/1/commands/_?internal_name=ImportProject&password=undefined&culture=en-us";
        private readonly string _mqttConfigUrl = "api/v1/items/{DeviceId}/datapoints?internal_name=timezone,mqttservicestate,mqttservicestarttype,mqttbroker,mqttport,mqtttlsenabled,mqttclientid,mqttpublishvalues&select=internal_name,value&culture=en-us";
        private readonly string _updateMqttConfigUrl = "api/v1/items/{DeviceId}/parameters?culture=en-us";
        private readonly string _startOrStopMqttUrl = "api/v1/items/{DeviceId}/commands/_?internal_name={StartOrStopCommand}&culture=en-us";


        public UdcHttpService(HttpClient httpClient, ILogger<UdcHttpService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
        }

        public async Task<string> GetUdcProjectItemIdAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync(string.Concat(_itemUrl, "?filter=type_name:powercenter3000"));
                response.EnsureSuccessStatusCode();

                var items = JsonConvert.DeserializeObject<DeviceItemsResult>(await response.Content.ReadAsStringAsync());

                if (items != null
                    && items.Embedded != null
                    && items.Embedded.Items != null
                    && items.Embedded.Items.Count == 1)
                {
                    return items.Embedded.Items[0].Id;
                }

                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetUdcProjectItemIdAsync Error");
            }

            return string.Empty;
        }

        public async Task<UdcAddDeviceResult?> AddDeviceToUdcProjectAsync(UdcAddDevice udcAddDevice)
        {
            try
            {
                using StringContent jsonContent = new(JsonConvert.SerializeObject(udcAddDevice), Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync(_itemUrl, jsonContent);
                response.EnsureSuccessStatusCode();

                var udcAddDeviceResult = JsonConvert.DeserializeObject<UdcAddDeviceResult>(await response.Content.ReadAsStringAsync());

                return udcAddDeviceResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetUdcProjectItemIdAsync Error");
            }

            return null;
        }

        public async Task<string> CancelUdcScanJobAsync(string deviceId)
        {
            if (string.IsNullOrWhiteSpace(deviceId))
            {
                return $"{nameof(deviceId)} is null or space.";
            }

            try
            {
                var response = await _httpClient.DeleteAsync(_cancelUrl.Replace("{DeviceId}", deviceId));
                response.EnsureSuccessStatusCode();

                if (response.StatusCode == HttpStatusCode.NoContent)
                    return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "CancelUdcScanJobAsync Error");
            }

            return "Error";
        }

        /// <summary>
        /// 创建扫描任务
        /// </summary>
        /// <param name="deviceId">项目或者资产Id</param>
        /// <param name="actualInterface">内部接口名称："internal" | "external" 默认："internal"</param>
        /// <param name="udcScanType">扫描类型</param>
        /// <param name="option">具体参数</param>
        /// <returns>jobId</returns>
        public async Task<string> CreateUdcScanJobAndReturnIdAsync(string deviceId, string actualInterface = "internal", UdcScanType udcScanType = UdcScanType.Scan_Id, UdcScanRequestOptions? option = null)
        {
            if (string.IsNullOrWhiteSpace(deviceId))
            {
                return string.Empty;
            }

            try
            {
                string udcScanJobId = string.Empty;

                // option 参数是 null 默认通过内部网口扫描
                if (option == null)
                {
                    option = new UdcScanRequestOptions
                    {
                        Interface = actualInterface,
                        Result = new UdcScanRequestResult
                        {
                            Format = "json"
                        }
                    };

                    udcScanType = UdcScanType.Scan_Id;
                }

                UdcScanRequest udcScanRequest = new UdcScanRequest
                {
                    Type = udcScanType.ToString().ToLower(),
                    Options = option,
                };
                using StringContent jsonContent = new(JsonConvert.SerializeObject(udcScanRequest), Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(_createScanJobUrl.Replace("{DeviceId}", deviceId), jsonContent);
                response.EnsureSuccessStatusCode();

                JObject? jObject = JsonConvert.DeserializeObject(await response.Content.ReadAsStringAsync()) as JObject;

                if (jObject != null)
                {
                    return jObject["id"]?.ToString() ?? string.Empty;
                }

                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "CreateUdcScanJobAndReturnIdAsync Error");
            }

            return string.Empty;
        }

        public async Task<UdcScanProcess?> GetUdcScanProcessStatusAsync(string deviceId, string jobId)
        {
            if (string.IsNullOrWhiteSpace(deviceId) || string.IsNullOrWhiteSpace(jobId))
            {
                return null;
            }

            try
            {
                var response = await _httpClient.GetAsync(_scanProcessUrl.Replace("{DeviceId}", deviceId).Replace("{JobId}", jobId));
                response.EnsureSuccessStatusCode();

                var udcScanProcess = JsonConvert.DeserializeObject<UdcScanProcess>(await response.Content.ReadAsStringAsync());

                return udcScanProcess;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetUdcScanProcessStatusAsync Error");
            }

            return null;
        }

        public async Task<string?> CancelUdcScanSingleJobAsync(string deviceId, string jobId)
        {
            if (string.IsNullOrWhiteSpace(deviceId) || string.IsNullOrWhiteSpace(jobId))
            {
                return null;
            }

            try
            {
                var response = await _httpClient.DeleteAsync(_scanProcessUrl.Replace("{DeviceId}", deviceId).Replace("{JobId}", jobId) + "?cancel=true");
                response.EnsureSuccessStatusCode();
                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "CancelUdcScanSingleJobAsync Error");
            }

            return "Error";
        }

        public async Task<List<UdcScanResult>> GetUdcScanResultAsync(string deviceId, string jobId)
        {
            var scanResult = new List<UdcScanResult>();
            if (string.IsNullOrWhiteSpace(deviceId) || string.IsNullOrWhiteSpace(jobId))
            {
                return scanResult;
            }

            try
            {
                var response = await _httpClient.GetAsync(_scanResultUrl.Replace("{DeviceId}", deviceId).Replace("{JobId}", jobId));
                response.EnsureSuccessStatusCode();

                scanResult = JsonConvert.DeserializeObject<List<UdcScanResult>>(await response.Content.ReadAsStringAsync());

                return scanResult ?? new List<UdcScanResult>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetUdcScanResultAsync Error");
            }

            return scanResult ?? new List<UdcScanResult>();
        }

        public async Task<string> UploadSplxFile(string rePackageSplxPath)
        {
            if (string.IsNullOrWhiteSpace(rePackageSplxPath))
            {
                return $"{nameof(rePackageSplxPath)} is null or empty.";
            }

            try
            {
                using FileStream uploadStream = new FileStream(rePackageSplxPath, FileMode.Open, FileAccess.Read);
                using var content = new StreamContent(uploadStream);

                var response = await _httpClient.PostAsync(_uploadSplxFileUrl, content);
                response.EnsureSuccessStatusCode();

                return string.Empty;
            }
            catch (Exception ex)
            {

                _logger.LogError(ex, "UploadSplxFile Error");
            }

            return "Error";
        }

        public async Task<string> ClearProject()
        {
            try
            {
                var response = await _httpClient.PostAsync(_uploadSplxFileUrl, null);
                response.EnsureSuccessStatusCode();

                return string.Empty;
            }
            catch (Exception ex)
            {

                _logger.LogError(ex, "UploadSplxFile Error");
            }

            return "Error";
        }

        public async Task<List<DeivceDataPointItem>> GetUdcConfigAsync(string deviceId)
        {
            try
            {
                var response = await _httpClient.GetAsync(_mqttConfigUrl.Replace("{DeviceId}", deviceId));
                response.EnsureSuccessStatusCode();

                var items = JsonConvert.DeserializeObject<DeviceDataPointsResult>(await response.Content.ReadAsStringAsync());

                if (items != null
                    && items.Embedded != null
                    && items.Embedded.Items != null
                    && items.Embedded.Items.Any())
                {
                    return items.Embedded.Items;
                }

                return new List<DeivceDataPointItem>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetUdcConfigAsync Error");
            }

            return new List<DeivceDataPointItem>();
        }

        public async Task<string> UpdateUdcConfigAsync(string deviceId, List<DeivceDataPointItem> deivceDataPoints)
        {
            try
            {
                var items = deivceDataPoints.Select(a => new { internal_name = a.InternalName, value = a.Value }).ToArray();
                using StringContent jsonContent = new(JsonConvert.SerializeObject(new { item = items }), Encoding.UTF8, "application/json");

                var response = await _httpClient.PatchAsync(_updateMqttConfigUrl.Replace("{DeviceId}", deviceId), jsonContent);
                response.EnsureSuccessStatusCode();

                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "UpdateUdcConfigAsync Error");
            }

            return "UpdateUdcConfigAsync Error";
        }

        public async Task<string> StartUdcMqttServiceAsync(string deviceId)
        {
            try
            {
                var response = await _httpClient.PostAsync(_startOrStopMqttUrl.Replace("{DeviceId}", deviceId).Replace("{StartOrStopCommand}", "StartMqttService"), null);
                response.EnsureSuccessStatusCode();

                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "StartUdcMqttServiceAsync Error");
            }

            return "StartUdcMqttServiceAsync Error";
        }

        public async Task<string> StopUdcMqttServiceAsync(string deviceId)
        {
            try
            {
                var response = await _httpClient.PostAsync(_startOrStopMqttUrl.Replace("{DeviceId}", deviceId).Replace("{StartOrStopCommand}", "StopMqttService"), null);
                response.EnsureSuccessStatusCode();

                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "StopUdcMqttServiceAsync Error");
            }

            return "StopUdcMqttServiceAsync Error";
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
