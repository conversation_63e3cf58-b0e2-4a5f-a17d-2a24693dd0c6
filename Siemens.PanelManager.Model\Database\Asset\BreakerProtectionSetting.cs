﻿using SqlSugar;

namespace Siemens.PanelManager.Model.Database.Asset
{
    [SugarTable("asset_breaker_protection_settings")]
    public class BreakerProtectionSetting : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "asset_id", IsNullable = false)]
        public int AssetId { get; set; }
        #region
        ///
        /// LT保护功能投退
        ///
        [SugarColumn(ColumnName = "lt_onoff", Length = 50, IsNullable = true)]
        public string? LT_ONOFF { get; set; }

        ///
        /// LT电流设定值
        ///
        [SugarColumn(ColumnName = "lt_ir", Length = 50, IsNullable = true)]
        public string? LT_IR { get; set; }

        ///
        /// LT脱扣时间 6xIr
        ///
        [SugarColumn(ColumnName = "lt_tr", Length = 50, IsNullable = true)]
        public string? LT_TR { get; set; }

        ///
        /// LT特性曲线
        ///
        [SugarColumn(ColumnName = "lt_para_curve", Length = 50, IsNullable = true)]
        public string? LT_PARA_CURVE { get; set; }

        ///
        /// LT保护功能投入，e.SET
        ///
        [SugarColumn(ColumnName = "lt_onoff_remote", Length = 50, IsNullable = true)]
        public string? LT_ONOFF_REMOTE { get; set; }

        ///
        /// LT电流设定值，e.SET
        ///
        [SugarColumn(ColumnName = "lt_ir_remote", Length = 50, IsNullable = true)]
        public string? LT_IR_REMOTE { get; set; }

        ///
        /// LT脱扣时间 6xIr，e.SET
        ///
        [SugarColumn(ColumnName = "lt_tr_remote", Length = 50, IsNullable = true)]
        public string? LT_TR_REMOTE { get; set; }

        ///
        /// LT特性曲线，e.SET
        ///
        [SugarColumn(ColumnName = "lt_para_curve_remote", Length = 50, IsNullable = true)]
        public string? LT_PARA_CURVE_REMOTE { get; set; }

        ///
        /// 冷却时间常数
        ///
        [SugarColumn(ColumnName = "lt_tau", Length = 50, IsNullable = true)]
        public string? LT_TAU { get; set; }

        ///
        /// 热记忆
        ///
        [SugarColumn(ColumnName = "lt_thermal_mem_onoff", Length = 50, IsNullable = true)]
        public string? LT_THERMAL_MEM_ONOFF { get; set; }

        ///
        /// 相不平衡保护
        ///
        [SugarColumn(ColumnName = "lt_phase_loss_sensitiv_onoff", Length = 50, IsNullable = true)]
        public string? LT_PHASE_LOSS_SENSITIV_ONOFF { get; set; }

        ///
        /// PAL过载预告警
        ///
        [SugarColumn(ColumnName = "pal_onoff", Length = 50, IsNullable = true)]
        public string? PAL_ONOFF { get; set; }

        ///
        /// PAL过载告警电流定值
        ///
        [SugarColumn(ColumnName = "pal_ir", Length = 50, IsNullable = true)]
        public string? PAL_IR { get; set; }

        ///
        /// PAL过载告警时间
        ///
        [SugarColumn(ColumnName = "pal_tr", Length = 50, IsNullable = true)]
        public string? PAL_TR { get; set; }

        ///
        /// LT N保护功能投退
        ///
        [SugarColumn(ColumnName = "ltn_onoff", Length = 50, IsNullable = true)]
        public string? LTN_ONOFF { get; set; }

        ///
        /// LT N电流设定值
        ///
        [SugarColumn(ColumnName = "ltn_in", Length = 50, IsNullable = true)]
        public string? LTN_IN { get; set; }

        ///
        /// PAL电流设定值
        ///
        [SugarColumn(ColumnName = "pal_in", Length = 50, IsNullable = true)]
        public string? PAL_IN { get; set; }

        ///
        /// ST保护功能投退
        ///
        [SugarColumn(ColumnName = "st_onoff", Length = 50, IsNullable = true)]
        public string? ST_ONOFF { get; set; }

        ///
        /// ST电流设定值
        ///
        [SugarColumn(ColumnName = "st_isd", Length = 50, IsNullable = true)]
        public string? ST_ISD { get; set; }

        ///
        /// ST脱扣时间
        ///
        [SugarColumn(ColumnName = "st_tsd", Length = 50, IsNullable = true)]
        public string? ST_TSD { get; set; }

        ///
        /// ST特性曲线
        ///
        [SugarColumn(ColumnName = "st_i2t_on_off", Length = 50, IsNullable = true)]
        public string? ST_I2t_ON_OFF { get; set; }

        ///
        /// ST保护功能投入，e.SET
        ///
        [SugarColumn(ColumnName = "st_onoff_remote", Length = 50, IsNullable = true)]
        public string? ST_ONOFF_REMOTE { get; set; }

        ///
        /// ST电流设定值，e.SET
        ///
        [SugarColumn(ColumnName = "st_isd_remote", Length = 50, IsNullable = true)]
        public string? ST_ISD_REMOTE { get; set; }

        ///
        /// ST脱扣时间，e.SET
        ///
        [SugarColumn(ColumnName = "st_tsd_remote", Length = 50, IsNullable = true)]
        public string? ST_TSD_REMOTE { get; set; }

        ///
        /// ST特性曲线，e.SET
        ///
        [SugarColumn(ColumnName = "st_i2t_on_off_remote", Length = 50, IsNullable = true)]
        public string? ST_I2t_ON_OFF_REMOTE { get; set; }

        ///
        /// ST参考点
        ///
        [SugarColumn(ColumnName = "st_isd_ref_i2tsd", Length = 50, IsNullable = true)]
        public string? ST_ISD_REF_I2TSD { get; set; }

        ///
        /// ST间隙保护功能投入
        ///
        [SugarColumn(ColumnName = "st_intermittent_onoff", Length = 50, IsNullable = true)]
        public string? ST_INTERMITTENT_ONOFF { get; set; }

        ///
        /// DST保护功能投退
        ///
        [SugarColumn(ColumnName = "dst_onoff", Length = 50, IsNullable = true)]
        public string? DST_ONOFF { get; set; }

        ///
        /// DST正向电流设定值
        ///
        [SugarColumn(ColumnName = "dst_isd_fw", Length = 50, IsNullable = true)]
        public string? DST_ISD_FW { get; set; }

        ///
        /// DST反向电流设定值
        ///
        [SugarColumn(ColumnName = "dst_isd_rev", Length = 50, IsNullable = true)]
        public string? DST_ISD_REV { get; set; }

        ///
        /// DST正向跳闸时间
        ///
        [SugarColumn(ColumnName = "dst_tsd_fw", Length = 50, IsNullable = true)]
        public string? DST_TSD_FW { get; set; }

        ///
        /// DST反向跳闸时间
        ///
        [SugarColumn(ColumnName = "dst_tsd_rev", Length = 50, IsNullable = true)]
        public string? DST_TSD_REV { get; set; }

        ///
        /// INST保护功能投退
        ///
        [SugarColumn(ColumnName = "inst_onoff", Length = 50, IsNullable = true)]
        public string? INST_ONOFF { get; set; }

        ///
        /// INST电流设定值
        ///
        [SugarColumn(ColumnName = "inst_ii", Length = 50, IsNullable = true)]
        public string? INST_II { get; set; }

        ///
        /// INST电流设定值，e.SET
        ///
        [SugarColumn(ColumnName = "inst_onoff_remote", Length = 50, IsNullable = true)]
        public string? INST_ONOFF_REMOTE { get; set; }

        ///
        /// INST电流设定值，e.SET
        ///
        [SugarColumn(ColumnName = "inst_ii_remote", Length = 50, IsNullable = true)]
        public string? INST_II_REMOTE { get; set; }

        ///
        /// GF接地保护功能投退
        ///
        [SugarColumn(ColumnName = "gf_protection_onoff", Length = 50, IsNullable = true)]
        public string? GF_PROTECTION_ONOFF { get; set; }

        ///
        /// GF间隙性检测
        ///
        [SugarColumn(ColumnName = "gf_intermittent_onoff", Length = 50, IsNullable = true)]
        public string? GF_INTERMITTENT_ONOFF { get; set; }

        ///
        /// GF保护特性曲线
        ///
        [SugarColumn(ColumnName = "gf_std_para_curve", Length = 50, IsNullable = true)]
        public string? GF_STD_PARA_CURVE { get; set; }

        ///
        /// GF合成零序电流设定值
        ///
        [SugarColumn(ColumnName = "gf_std_ig_residual", Length = 50, IsNullable = true)]
        public string? GF_STD_IG_RESIDUAL { get; set; }

        ///
        /// GF合成零序脱扣时间
        ///
        [SugarColumn(ColumnName = "gf_std_tg_residual", Length = 50, IsNullable = true)]
        public string? GF_STD_TG_RESIDUAL { get; set; }

        ///
        /// GF外接零序电流设定值
        ///
        [SugarColumn(ColumnName = "gf_std_ig_direct", Length = 50, IsNullable = true)]
        public string? GF_STD_IG_DIRECT { get; set; }

        ///
        /// GF外接零序脱扣时间
        ///
        [SugarColumn(ColumnName = "gf_std_tg_direct", Length = 50, IsNullable = true)]
        public string? GF_STD_TG_DIRECT { get; set; }

        ///
        /// GF Dual限制性零序电流设定值
        ///
        [SugarColumn(ColumnName = "gf_std_ig_dm_ref", Length = 50, IsNullable = true)]
        public string? GF_STD_IG_DM_REF { get; set; }

        ///
        /// GF Dual限制性零序脱扣时间
        ///
        [SugarColumn(ColumnName = "gf_std_tg_dm_ref", Length = 50, IsNullable = true)]
        public string? GF_STD_TG_DM_REF { get; set; }

        ///
        /// GF Dual非限制性零序电流设定值
        ///
        [SugarColumn(ColumnName = "gf_std_ig_dm_uref", Length = 50, IsNullable = true)]
        public string? GF_STD_IG_DM_UREF { get; set; }

        ///
        /// GF Dual非限制性零序脱扣时间
        ///
        [SugarColumn(ColumnName = "gf_std_tg_dm_uref", Length = 50, IsNullable = true)]
        public string? GF_STD_TG_DM_UREF { get; set; }

        ///
        /// GF限制性零序电流设定值
        ///
        [SugarColumn(ColumnName = "gf_std_ig_hiz_ref_sec", Length = 50, IsNullable = true)]
        public string? GF_STD_IG_HIZ_REF_SEC { get; set; }

        ///
        /// GF限制性零序脱扣时间
        ///
        [SugarColumn(ColumnName = "gf_std_tg_hiz_ref", Length = 50, IsNullable = true)]
        public string? GF_STD_TG_HIZ_REF { get; set; }

        ///
        /// GF非限制性零序电流设定值
        ///
        [SugarColumn(ColumnName = "gf_std_ig_hiz_uref", Length = 50, IsNullable = true)]
        public string? GF_STD_IG_HIZ_UREF { get; set; }

        ///
        /// GF非限制性零序脱扣时间
        ///
        [SugarColumn(ColumnName = "gf_std_tg_hiz_uref", Length = 50, IsNullable = true)]
        public string? GF_STD_TG_HIZ_UREF { get; set; }

        ///
        /// GF告警功能投退
        ///
        [SugarColumn(ColumnName = "gf_alarm_onoff", Length = 50, IsNullable = true)]
        public string? GF_ALARM_ONOFF { get; set; }

        ///
        /// GF合成零序告警电流设定值
        ///
        [SugarColumn(ColumnName = "gf_alarm_ig_residual", Length = 50, IsNullable = true)]
        public string? GF_ALARM_IG_RESIDUAL { get; set; }

        ///
        /// GF外接零序告警电流设定值
        ///
        [SugarColumn(ColumnName = "gf_alarm_ig_direct", Length = 50, IsNullable = true)]
        public string? GF_ALARM_IG_DIRECT { get; set; }

        ///
        /// GF Dual非限制性接地告警电流设定值
        ///
        [SugarColumn(ColumnName = "gf_alarm_ig_dm_uref", Length = 50, IsNullable = true)]
        public string? GF_ALARM_IG_DM_UREF { get; set; }

        ///
        /// GF 高阻非限制性接地告警电流设定值
        ///
        [SugarColumn(ColumnName = "gf_alarm_ig_hiz_uref", Length = 50, IsNullable = true)]
        public string? GF_ALARM_IG_HIZ_UREF { get; set; }

        ///
        /// GF告警时间
        ///
        [SugarColumn(ColumnName = "gf_alarm_tg", Length = 50, IsNullable = true)]
        public string? GF_ALARM_TG { get; set; }

        ///
        /// RP保护投退
        ///
        [SugarColumn(ColumnName = "rp_onoff", Length = 50, IsNullable = true)]
        public string? RP_ONOFF { get; set; }

        ///
        /// RP功率设定值
        ///
        [SugarColumn(ColumnName = "rp_pickup", Length = 50, IsNullable = true)]
        public string? RP_PICKUP { get; set; }

        ///
        /// RP脱扣时间
        ///
        [SugarColumn(ColumnName = "rp_delay", Length = 50, IsNullable = true)]
        public string? RP_DELAY { get; set; }
        /// <summary>
        /// Tc/Tp 选择
        /// </summary>
        [SugarColumn(ColumnName = "mp_tctp_selector", Length = 50, IsNullable = true)]
        public string? MP_TcTp_SELECTOR { get; set; }
        /// <summary>
        /// 脱扣等级 Tc
        /// </summary>
        [SugarColumn(ColumnName = "mp_tc", Length = 50, IsNullable = true)]
        public string? MP_Tc { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(ColumnName = "mp_tp", Length = 50, IsNullable = true)]
        public string? MP_Tp { get; set; }
        /// <summary>
        /// 瞬时保护（弧闪模式）
        /// </summary>
        [SugarColumn(ColumnName = "inst_ii_arc", Length = 50, IsNullable = true)]
        public string? INST_II_ARC { get; set; }

        /// <summary>
        /// 接地故障（弧闪模式）
        /// </summary>
        [SugarColumn(ColumnName = "gf_ig_arc", Length = 50, IsNullable = true)]
        public string? GF_IG_ARC { get; set; }

        /// <summary>
        /// 外接零序接地保护时间设定值
        /// </summary>
        [SugarColumn(ColumnName = "gf_xtd_tg", Length = 50, IsNullable = true)]
        public string? GF_XTD_TG { get; set; }

        /// <summary>
        /// 外接零序接地保护电流设定值
        /// </summary>
        [SugarColumn(ColumnName = "gf_xtd_ig", Length = 50, IsNullable = true)]
        public string? GF_XTD_IG { get; set; }
        /// <summary>
        /// 接地故障告警电流设定值
        /// </summary>
        [SugarColumn(ColumnName = "gf_alarm_ig", Length = 50, IsNullable = true)]
        public string? GF_ALARM_IG { get; set; }
        /// <summary>
        /// 合成零序接地保护电流设定值
        /// </summary>
        [SugarColumn(ColumnName = "gf_ig", Length = 50, IsNullable = true)]
        public string? GF_IG { get; set; }
        /// <summary>
        /// 合成零序接地保护时间设定值
        /// </summary>
        [SugarColumn(ColumnName = "gf_tg", Length = 50, IsNullable = true)]
        public string? GF_TG { get; set; }

        /// <summary>
        /// GF 脱扣开/关
        /// </summary>
        [SugarColumn(ColumnName = "gf_onoff", Length = 50, IsNullable = true)]
        public string? GF_ONOFF { get; set; }

        /// <summary>
        /// GF 特性曲线的特征
        /// </summary>
        [SugarColumn(ColumnName = "gf_para_curve", Length = 50, IsNullable = true)]
        public string? GF_PARA_CURVE { get; set; }

        /// <summary>
        /// GF 故障的方法
        /// </summary>
        [SugarColumn(ColumnName = "gf_type", Length = 50, IsNullable = true)]
        public string? GF_TYPE { get; set; }
        #endregion
    }
}
