﻿using Microsoft.Extensions.DependencyInjection;

namespace Siemens.PanelManager.SyncService.Sqlite
{
    public static class SyncHandleExtension
    {
        public static void AddSyncArchiveDataHandle(this IServiceCollection services)
        {
            var interfaceType = typeof(ISyncArchiveDataHandle);
            var allSyncType = interfaceType.Assembly.GetTypes()
                .Where(x => x.IsClass && !x.IsInterface && x.IsAssignableTo(interfaceType))
                .ToList();

            allSyncType?.ForEach(t =>
            {
                services.AddTransient(interfaceType, t);
            });
        }

        public static void AddSyncMessageHandle(this IServiceCollection services)
        {
            var interfaceType = typeof(ISyncMessageHandle);
            var allSyncType = interfaceType.Assembly.GetTypes()
                .Where(x => x.IsClass && !x.IsInterface && x.IsAssignableTo(interfaceType))
                .ToList();

            allSyncType?.ForEach(t =>
            {
                services.AddTransient(interfaceType, t);
            });
        }
    }
}
