﻿using Siemens.InfluxDB.Helper.Enum;

namespace Siemens.InfluxDB.Helper.General
{
    internal class SelectAnalyseResult
    {
        public List<GroupFunctionEnum> GroupFunctions { get; set; } = new List<GroupFunctionEnum>();

        public List<SelectColumnInfo> ColumnInfos { get; set; } = new List<SelectColumnInfo>();
    }

    internal class SelectColumnInfo
    {
        public string ColumnName { get; set; } = string.Empty;
        public GroupFunctionEnum Function { get; set; } = GroupFunctionEnum.Default;
        public string TargetName { get; set; } = string.Empty;
        public object? DefaultValue { get;set; }
    }
}
