﻿using CliWrap;
using CliWrap.Buffered;
using System.Net;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.Monitor.Cmd
{
    public static class ChangeIPCmd
    {
        public static readonly string Eno1_Config_File = "/etc/network/interfaces.d/eno1";
        public static readonly string Eno2_Config_File = "/etc/network/interfaces.d/eno2";

        public static async Task<(bool IsDHCP, string[] IpInfo)> GetX1p1Config(ILogger logger)
        {
            Func<string, Task> func = (message) =>
            {
                logger.LogInformation(message);
                return Task.CompletedTask;
            };

            string[] ipInfo = new string[2];

            var result = await Cli.Wrap("ifconfig")
               .WithArguments(a => a
               .Add("eno1"))
               .WithStandardOutputPipe(PipeTarget.ToDelegate(func))
               .WithStandardErrorPipe(PipeTarget.ToDelegate(func))
               .WithValidation(CommandResultValidation.None)
               .ExecuteBufferedAsync();

            if (!string.IsNullOrWhiteSpace(result.StandardOutput))
            {
                string ipPattern = @"inet (\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})";
                string subnetPattern = @"netmask (\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})";

                Match ipMatch = Regex.Match(result.StandardOutput, ipPattern);
                Match subnetMatch = Regex.Match(result.StandardOutput, subnetPattern);

                if (ipMatch.Success)
                {
                    ipInfo[0] = ipMatch.Groups[1].Value;
                }

                if (subnetMatch.Success)
                {
                    ipInfo[1] = subnetMatch.Groups[1].Value;
                }
            }

            bool isDHCP = false;
            var dhcpResult = await Cli.Wrap("cat")
               .WithArguments(a => a
               .Add(Eno1_Config_File))
               .WithStandardOutputPipe(PipeTarget.ToDelegate(func))
               .WithStandardErrorPipe(PipeTarget.ToDelegate(func))
               .WithValidation(CommandResultValidation.None)
               .ExecuteBufferedAsync();

            if (!string.IsNullOrWhiteSpace(dhcpResult.StandardOutput))
            {
                isDHCP = dhcpResult.StandardOutput.Contains("dhcp", StringComparison.OrdinalIgnoreCase);
            }

            return (isDHCP, ipInfo);
        }

        public static async Task SetX1p1Config(List<string> ipInfos, bool isDHCP, ILogger logger)
        {
            // auto eno1
            // allow-hotplug eno1
            // iface eno1 inet dhcp
            string setTemplateString = string.Empty;
            if (isDHCP)
            {
                setTemplateString = $"echo 'auto eno1\nallow-hotplug eno1\niface eno1 inet dhcp' > {Eno1_Config_File}";
            }
            else
            {
                if (ipInfos.Count() < 2)
                {
                    return;
                }

                setTemplateString = $"echo 'auto eno1\nallow-hotplug eno1\niface eno1 inet static\naddress {ipInfos[0]}\nnetmask {ipInfos[1]}' > {Eno1_Config_File}";

                if (ipInfos.Count() == 3)
                {
                    setTemplateString = $"echo 'auto eno1\nallow-hotplug eno1\niface eno1 inet static\naddress {ipInfos[0]}\nnetmask {ipInfos[1]}\ngateway {ipInfos[2]}' > {Eno1_Config_File}";
                }
            }


            Func<string, Task> func = (message) =>
            {
                logger.LogInformation(message);
                return Task.CompletedTask;
            };

            var result = await Cli.Wrap("sh")
               .WithArguments(a => a
               .Add("-c")
               .Add(setTemplateString))
               .WithStandardOutputPipe(PipeTarget.ToDelegate(func))
               .WithStandardErrorPipe(PipeTarget.ToDelegate(func))
               .WithValidation(CommandResultValidation.None)
               .ExecuteAsync();
        }

        public static async Task<string[]> GetX2p1Config(ILogger logger)
        {
            Func<string, Task> func = (message) =>
            {
                logger.LogInformation(message);
                return Task.CompletedTask;
            };

            string[] ipInfo = new string[2];

            var result = await Cli.Wrap("ifconfig")
               .WithArguments(a => a
               .Add("eno2"))
               .WithStandardOutputPipe(PipeTarget.ToDelegate(func))
               .WithStandardErrorPipe(PipeTarget.ToDelegate(func))
               .WithValidation(CommandResultValidation.None)
               .ExecuteBufferedAsync();

            if (!string.IsNullOrWhiteSpace(result.StandardOutput))
            {
                string ipPattern = @"inet (\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})";
                string subnetPattern = @"netmask (\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})";

                Match ipMatch = Regex.Match(result.StandardOutput, ipPattern);
                Match subnetMatch = Regex.Match(result.StandardOutput, subnetPattern);

                if (ipMatch.Success)
                {
                    ipInfo[0] = ipMatch.Groups[1].Value;
                }

                if (subnetMatch.Success)
                {
                    ipInfo[1] = subnetMatch.Groups[1].Value;
                }
            }

            return ipInfo;
        }

        public static async Task SetX2p1Config(List<string> ipInfos, ILogger logger)
        {
            //auto eno2
            //allow-hotplug eno2
            //iface eno2 inet static
            //address *************
            //netmask *************
            //gateway

            if (ipInfos.Count() < 2)
            {
                return;
            }

            string setTemplateString = $"echo 'auto eno2\nallow-hotplug eno2\niface eno2 inet static\naddress {ipInfos[0]}\nnetmask {ipInfos[1]}' > {Eno2_Config_File}";

            if (ipInfos.Count() == 3)
            {
                setTemplateString = $"echo 'auto eno2\nallow-hotplug eno2\niface eno2 inet static\naddress {ipInfos[0]}\nnetmask {ipInfos[1]}\ngateway {ipInfos[2]}' > {Eno2_Config_File}";
            }

            Func<string, Task> func = (message) =>
            {
                logger.LogInformation(message);
                return Task.CompletedTask;
            };

            var result = await Cli.Wrap("sh")
               .WithArguments(a => a
               .Add("-c")
               .Add(setTemplateString))
               .WithStandardOutputPipe(PipeTarget.ToDelegate(func))
               .WithStandardErrorPipe(PipeTarget.ToDelegate(func))
               .WithValidation(CommandResultValidation.None)
               .ExecuteAsync();
        }

        public static async Task RestartNetwork(ILogger logger)
        {
            Func<string, Task> func = (message) =>
            {
                logger.LogInformation(message);
                return Task.CompletedTask;
            };

            var result = await Cli.Wrap("/etc/init.d/networking")
                .WithArguments(a => a
                .Add("restart"))
                .WithStandardOutputPipe(PipeTarget.ToDelegate(func))
                .WithStandardErrorPipe(PipeTarget.ToDelegate(func))
                .WithValidation(CommandResultValidation.None)
                .ExecuteAsync();
        }

        public static async Task Reboot(ILogger logger)
        {
            Func<string, Task> func = (message) =>
            {
                logger.LogInformation(message);
                return Task.CompletedTask;
            };

            var result = await Cli.Wrap("reboot")
                .WithStandardOutputPipe(PipeTarget.ToDelegate(func))
                .WithStandardErrorPipe(PipeTarget.ToDelegate(func))
                .WithValidation(CommandResultValidation.None)
                .ExecuteAsync();
        }
    }
}
