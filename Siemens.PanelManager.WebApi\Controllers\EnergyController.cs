﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using Quartz.Impl.AdoJobStore.Common;
using Siemens.PanelManager.Common;
using Quartz.Util;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.Excel;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Chart;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.Model.Database.WorkOrder;
using Siemens.PanelManager.Server.Asset;
using Siemens.PanelManager.Server.Energy;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Linq;
using System.Xml.Linq;
using Siemens.PanelManager.Model.Database.Energy;
using TouchSocket.Sockets;

namespace Siemens.PanelManager.WebApi.Controllers
{

    [Route("api/v1/[controller]")]
    [ApiController]
    public class EnergyController : SiemensApiControllerBase
    {
        private const string EnergyCoefficientKey = "EnergyCoefficientKey";
        private const string AssetSimpleInfoCacheKey = "Asset:SimpleInfo-{0}";
        private static readonly DateTime UtcTime = new DateTime(1970, 1, 1);
        private ILogger<EnergyController> _log;
        private ISqlSugarClient _client;
        private SiemensExcelHelper _excelHelper => _provider.GetRequiredService<SiemensExcelHelper>();
        private IServiceProvider _provider;
        private SiemensCache _cache;

        private async Task<int> GetEnergyCoefficient()
        {
            int coefficient = 1000;

            coefficient = await _cache.GetOrCreateAsync<int>(EnergyCoefficientKey, async () =>
            {
                var config = await _client.Queryable<SystemConfig>().FirstAsync(c => c.Type == "SpecialConfig" && c.Name == "EnergyCoefficient");
                int valueInt = 0;
                if (!(config != null && int.TryParse(config.Value, out valueInt)))
                {
                    valueInt = 1000;
                }
                return valueInt;
            });

            return coefficient;
        }

        public EnergyController(SiemensCache cache, SqlSugarScope client, IServiceProvider provider, ILogger<EnergyController> logger)
            : base(provider, cache)
        {
            _log = logger;
            _client = client;
            _provider = provider;
            _cache = cache;
        }

        [HttpGet("summary")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_Energy_GetSummary", Description = "Swagger_Energy_GetSummary_Desc")]
        public async Task<ResponseBase<List<EnergySummaryInfo>>> GetSummary([FromQuery] EnergyQueryParamBase query)
        {
            List<EnergySummaryInfo> energySummaries = new List<EnergySummaryInfo>();
            EnergySummaryInfo energySummary;

            List<int> assetRelations = new List<int>();
            if (query.CircuitId > 0)
            {
                assetRelations = await _client.Queryable<AssetRelation>()
                    .Where(a => a.ParentId == query.CircuitId)
                    .Select(a => a.ChildId)
                    .ToListAsync();
            }

            var coefficient = await GetEnergyCoefficient();

            var assetIds = await _client.Queryable<AssetInfo>()
                .Where(a => a.AssetLevel == AssetLevel.Device)
                .WhereIF(!string.IsNullOrWhiteSpace(query.MeasurementFinal), a => a.MeterType == query.MeasurementFinal)
                .WhereIF(query.CircuitId > 0, a => SqlFunc.ContainsArray(assetRelations.ToArray(), a.Id))
                .WhereIF(query.AssetId > 0, a => a.Id == query.AssetId)
                .Select(a => a.Id)
                .ToListAsync();

            #region 新查询方式
            // 查询所有数据
            var energyManagementServer = _provider.GetRequiredService<EnergyManagementServer>();
            var now = DateTime.Now;
            var todayZero = new DateTime(now.Year, now.Month, now.Day, now.Hour, 0, 5);
            var data = await energyManagementServer.GetEnergyDataByHour(assetIds, todayZero, now, true);
            if (data == null)
            {
                data = new List<EnergyIntervalSummaryModel>();
            }

            // 查询当年得数据 
            //var yearSummaryList = data.Where(a => a.Time >= new DateTime(DateTime.Now.Year, 1, 1));

            //// step 1 Day
            //var daySummary = yearSummaryList.Where(a => a.Time >= DateTime.Now.Date);

            //var dayChartResult = daySummary.GroupBy(a => a.Time).Select(a => new
            //{
            //    Time = a.Key,
            //    Value = Math.Round(a.Sum(p => p.Value), 2),
            //    Cost = Math.Round(a.Sum(p => p.Cost), 2)
            //}).OrderBy(a => a.Time);

            //energySummary = new EnergySummaryInfo();
            //energySummary.TotalType = TotalType.Day;
            //energySummary.Electricity = daySummary.Sum(a => a.Value);
            //energySummary.ElectricityUnit = "kWh";
            //energySummary.Fee = daySummary.Sum(a => a.Cost);
            //energySummary.FeeUnit = "CNY";
            //energySummary.SortIndex = 0;
            //energySummaries.Add(energySummary);

            //// step 2 Month
            //var monthSummary = yearSummaryList.Where(a => a.Time >= new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1));

            //energySummary = new EnergySummaryInfo();
            //energySummary.TotalType = TotalType.Month;
            //energySummary.Electricity = monthSummary.Sum(a => a.Value);
            //energySummary.ElectricityUnit = "kWh";
            //energySummary.Fee = monthSummary.Sum(a => a.Cost);
            //energySummary.FeeUnit = "CNY";
            //energySummary.SortIndex = 1;
            //energySummaries.Add(energySummary);

            //// step 3 Year
            //energySummary = new EnergySummaryInfo();
            //energySummary.TotalType = TotalType.Year;
            //energySummary.Electricity = yearSummaryList.Sum(a => a.Value);
            //energySummary.ElectricityUnit = "kWh";
            //energySummary.Fee = yearSummaryList.Sum(a => a.Cost);
            //energySummary.FeeUnit = "CNY";
            //energySummary.SortIndex = 2;
            //energySummaries.Add(energySummary);

            // step 4 All
            //energySummary = new EnergySummaryInfo();
            //energySummary.TotalType = TotalType.All;
            //energySummary.Electricity = data.Sum(a => a.Value);
            //energySummary.ElectricityUnit = "kWh";
            //energySummary.Fee = data.Sum(a => a.Cost);
            //energySummary.FeeUnit = "CNY";
            //energySummary.SortIndex = 3;
            //energySummaries.Add(energySummary);

            #endregion

            #region 之前得查询方式

            // 查询一年的数据
            var yearSummaryList = await _client.Queryable<EnergySummary>()
                .Where(a => SqlFunc.ContainsArray(assetIds, a.AssetId))
                .Where(a => a.ConsumptionDate >= new DateTime(DateTime.Now.Year, 1, 1) && a.ConsumptionDate <= DateTime.Now)
                .ToListAsync();

            var hourElectricity = data.Sum(a => a.Value);
            var hourCost = data.Sum(a => a.Cost);
            // step 1 Day
            var daySummary = yearSummaryList.Where(a => a.ConsumptionDate >= DateTime.Now.Date).Select(a => new
            {
                Electricity = (a.PeakElectricity + a.FlatElectricity + a.ValleyElectricity + a.DeepValleyElectricity + a.HighPeakElectricity),
                Cost = a.PeakCost + a.FlatCost + a.ValleyCost + a.DeepValleyCost + a.HighPeakCost,
            });

            energySummary = new EnergySummaryInfo();
            energySummary.TotalType = TotalType.Day;
            energySummary.Electricity = daySummary.Sum(a => a.Electricity) / coefficient + hourElectricity;
            energySummary.ElectricityUnit = "kWh";
            energySummary.Fee = daySummary.Sum(a => a.Cost) + hourCost;
            energySummary.FeeUnit = "CNY";
            energySummary.SortIndex = 0;
            energySummaries.Add(energySummary);

            // step 2 Month
            var monthSummary = yearSummaryList.Where(a => a.ConsumptionDate >= new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1)).Select(a => new
            {
                Electricity = (a.PeakElectricity + a.FlatElectricity + a.ValleyElectricity + a.DeepValleyElectricity + a.HighPeakElectricity),
                Cost = a.PeakCost + a.FlatCost + a.ValleyCost + a.DeepValleyCost + a.HighPeakCost,
            });

            energySummary = new EnergySummaryInfo();
            energySummary.TotalType = TotalType.Month;
            energySummary.Electricity = monthSummary.Sum(a => a.Electricity) / coefficient + hourElectricity;
            energySummary.ElectricityUnit = "kWh";
            energySummary.Fee = monthSummary.Sum(a => a.Cost) + hourCost;
            energySummary.FeeUnit = "CNY";
            energySummary.SortIndex = 1;
            energySummaries.Add(energySummary);

            // step 3 Year
            var yearSummary = yearSummaryList.Select(a => new
            {
                Electricity = (a.PeakElectricity + a.FlatElectricity + a.ValleyElectricity + a.DeepValleyElectricity + a.HighPeakElectricity),
                Cost = a.PeakCost + a.FlatCost + a.ValleyCost + a.DeepValleyCost + a.HighPeakCost,
            });
            energySummary = new EnergySummaryInfo();
            energySummary.TotalType = TotalType.Year;
            energySummary.Electricity = yearSummary.Sum(a => a.Electricity) / coefficient + hourElectricity;
            energySummary.ElectricityUnit = "kWh";
            energySummary.Fee = yearSummary.Sum(a => a.Cost) + hourCost;
            energySummary.FeeUnit = "CNY";
            energySummary.SortIndex = 2;
            energySummaries.Add(energySummary);

            // step 4 All
            //总计数据单独查询
            var totalSummary = await _client.Queryable<EnergySummary>()
                .Where(a => SqlFunc.ContainsArray(assetIds, a.AssetId))
                .Select(a => new
                {
                    Electricity = SqlFunc.AggregateSum(a.PeakElectricity + a.FlatElectricity + a.ValleyElectricity + a.DeepValleyElectricity + a.HighPeakElectricity),
                    Cost = SqlFunc.AggregateSum(a.PeakCost + a.FlatCost + a.ValleyCost + a.DeepValleyCost + a.HighPeakCost),
                }).FirstAsync();

            energySummary = new EnergySummaryInfo();
            energySummary.TotalType = TotalType.All;
            energySummary.Electricity = totalSummary.Electricity / coefficient + hourElectricity;
            energySummary.ElectricityUnit = "kWh";
            energySummary.Fee = totalSummary.Cost + hourCost;
            energySummary.FeeUnit = "CNY";
            energySummary.SortIndex = 3;
            energySummaries.Add(energySummary);

            #endregion

            return new ResponseBase<List<EnergySummaryInfo>>()
            {
                Code = 20000,
                Data = energySummaries
            };
        }

        [HttpGet("chart")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_Energy_GetChart", Description = "Swagger_Energy_GetChart_Desc")]
        public async Task<ResponseBase<EnergyChartResult>> GetChart([FromQuery] EnergyConsumptionQueryParam query)
        {
            DateTime tempEndDate = DateTime.MinValue;
            if (query == null
                || string.IsNullOrWhiteSpace(query.StartDate)
                || !DateTime.TryParse(query.StartDate, out var tempStartDate)
                || tempStartDate > DateTime.Now
                || (query.DateType == ChartDateType.Custom && !DateTime.TryParse(query.EndDate, out tempEndDate)))
            {
                return new ResponseBase<EnergyChartResult>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var coefficient = await GetEnergyCoefficient();
            List<int> assetRelations = new List<int>();
            if (query.CircuitId > 0)
            {
                assetRelations = await _client.Queryable<AssetRelation>()
                    .Where(a => a.ParentId == query.CircuitId)
                    .Select(a => a.ChildId)
                    .ToListAsync();
            }

            var assetIds = await _client.Queryable<AssetInfo>()
                .Where(a => a.AssetLevel == AssetLevel.Device)
                .WhereIF(!string.IsNullOrWhiteSpace(query.MeasurementFinal), a => a.MeterType == query.MeasurementFinal)
                .WhereIF(query.CircuitId > 0, a => SqlFunc.ContainsArray(assetRelations.ToArray(), a.Id))
                .WhereIF(query.AssetId > 0, a => a.Id == query.AssetId)
                .Select(a => a.Id)
                .ToListAsync();

            var energyManagementServer = _provider.GetRequiredService<EnergyManagementServer>();

            var energyChartResult = new EnergyChartResult();
            LineChartModel lineChartModel = new LineChartModel();
            List<string> xAxis = new List<string>();

            Random random = new Random();
            DateTime currentDate = DateTime.Now;

            #region 新的查询方式

            var lineChart = await energyManagementServer.GetEnergyAnalysisChart(assetIds,
                tempStartDate.Date,
                tempEndDate,
                query.DateType.GetHashCode(),
                query.CompareType.GetHashCode(),
                query.ChartDataType.GetHashCode());

            lineChartModel = lineChart as LineChartModel;
            #endregion

            #region 以前的查询方式
            //// udc接入后查询实际数据表统计数据
            //if (query.DateType == ChartDateType.Day)
            //{
            //    var dayChart = await energyManagementServer.GetEnergyDayChart(assetIds, tempStartDate.Date, query.ChartDataType.GetHashCode());

            //    lineChartModel = dayChart as LineChartModel;
            //}
            //else
            //{
            //    var lineChart = await energyManagementServer.GetEnergyChart(assetIds,
            //        tempStartDate.Date,
            //        tempEndDate,
            //        query.DateType.GetHashCode(),
            //        query.CompareType.GetHashCode(),
            //        query.ChartDataType.GetHashCode());

            //    lineChartModel = lineChart as LineChartModel;
            //}
            #endregion

            #region 根据系数调整结果
            if (coefficient == 1 && query.ChartDataType == ChartDataType.Electricity)
            {
                if (lineChartModel != null)
                {
                    if (lineChartModel.Y1 != null && lineChartModel.Y1.Length > 0)
                    {
                        for (var i = 0; i < lineChartModel.Y1.Length; i++)
                        {
                            var v = lineChartModel.Y1[i] * 1000;
                            lineChartModel.Y1[i] = v;
                        }
                    }
                }
            }
            #endregion

            energyChartResult.LineChartModel = lineChartModel;
            energyChartResult.MaxValue = lineChartModel?.Y1?.DefaultIfEmpty().Max() ?? .0M;
            energyChartResult.MinValue = lineChartModel?.Y1?.DefaultIfEmpty().Min() ?? .0M;
            energyChartResult.AvgValue = lineChartModel?.Y1?.DefaultIfEmpty().Average() ?? .0M;
            energyChartResult.ValueUnit = query.ChartDataType == ChartDataType.Electricity ? "kWh" : "CNY";

            return new ResponseBase<EnergyChartResult>()
            {
                Code = 20000,
                Data = energyChartResult
            };
        }

        [HttpGet("export/power-consumption")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_Energy_ExportPowerConsumption", Description = "Swagger_Energy_ExportPowerConsumption_Desc")]
        public async Task<IActionResult> ExportPowerConsumption([FromQuery] EnergyExportConsumptionParam query)
        {
            if (query == null
                || string.IsNullOrWhiteSpace(query.StartDate)
                || !DateTime.TryParse(query.StartDate, out var tempStartDate)
                || tempStartDate > DateTime.Now
                || string.IsNullOrWhiteSpace(query.EndDate)
                || !DateTime.TryParse(query.EndDate, out var tempEndDate))
            {
                return NotFound();
            }

            List<ConsumptionExcelData> consumptionData = new List<ConsumptionExcelData>();
            List<ConsumptionExcelData> feeData = new List<ConsumptionExcelData>();

            List<int> assetRelations = new List<int>();
            AssetInfo? circuitAsset = null;
            AssetInfo? assetInfo = null;
            if (query.CircuitId > 0)
            {
                assetRelations = await _client.Queryable<AssetRelation>()
                    .Where(a => a.ParentId == query.CircuitId)
                    .Select(a => a.ChildId)
                    .ToListAsync();

                circuitAsset = await _client.Queryable<AssetInfo>().Where(a => a.Id == query.CircuitId).FirstAsync();
            }

            if (query.AssetId > 0)
            {
                assetInfo = await _client.Queryable<AssetInfo>().Where(a => a.Id == query.AssetId).FirstAsync();
            }

            var coefficient = await GetEnergyCoefficient();

            var assets = await _client.Queryable<AssetInfo>()
                .Where(a => a.AssetLevel == AssetLevel.Device)
                .WhereIF(query.CircuitId > 0, a => SqlFunc.ContainsArray(assetRelations.ToArray(), a.Id))
                .WhereIF(query.AssetId > 0, a => a.Id == query.AssetId)
                .Select(a => new { a.Id, a.AssetName })
                .ToListAsync();

            var assetIds = assets.Select(a => a.Id).ToArray();

            #region 新的查询方式

            var energyManagementServer = _provider.GetRequiredService<EnergyManagementServer>();

            var exportData = await energyManagementServer.GetEnergyDataByQuarter(assetIds.ToList(), tempStartDate, tempEndDate);

            #region 
            if (coefficient == 1)
            {
                if (exportData != null && exportData.Count > 0)
                {
                    foreach (var d in exportData)
                    {
                        d.Value = d.Value * 1000M;
                    }
                }
            }
            #endregion

            if (query.CircuitId > 0 || query.AssetId > 0)
            {
                var exportSummary = exportData.GroupBy(a => a.Time)
                    .Select(a => new
                    {
                        Time = a.Key,
                        Value = Math.Round(a.Sum(p => p.Value), 2),
                        Cost = Math.Round(a.Sum(p => p.Cost), 2)
                    }).OrderBy(a => a.Time);

                consumptionData = exportSummary.Select(a => new ConsumptionExcelData { CircuitName = circuitAsset?.AssetName ?? string.Empty, DateTime = a.Time.ToString("yyyy-MM-dd HH:mm"), Value = a.Value }).ToList();
                feeData = exportSummary.Select(a => new ConsumptionExcelData { CircuitName = circuitAsset?.AssetName ?? string.Empty, DateTime = a.Time.ToString("yyyy-MM-dd HH:mm"), Value = a.Cost }).ToList();
            }
            else
            {
                var assetAndCircuits = await _client.Queryable<AssetInfo>()
                .Where(a => a.AssetLevel == AssetLevel.Device || a.AssetLevel == AssetLevel.Circuit)
                .Select(a => new { a.Id, a.AssetName, a.AssetLevel })
                .ToListAsync();

                var circuitIds = assetAndCircuits.Where(a => a.AssetLevel == AssetLevel.Circuit).Select(a => a.Id).ToArray();

                var tempRelation = await _client.Queryable<AssetRelation>().Where(a => SqlFunc.ContainsArray(circuitIds, a.ParentId)).ToListAsync();

                foreach (var circuitId in circuitIds)
                {
                    var tempAllIds = tempRelation.Where(a => a.ParentId == circuitId).Select(a => a.ChildId).ToList();
                    var tempCircuitName = assetAndCircuits.FirstOrDefault(a => a.Id == circuitId)?.AssetName ?? string.Empty;

                    var exportSummary = exportData
                        .Where(a => tempAllIds.Contains(a.AssetId))
                        .GroupBy(a => a.Time)
                        .Select(a => new
                        {
                            Time = a.Key,
                            Value = Math.Round(a.Sum(p => p.Value), 2),
                            Cost = Math.Round(a.Sum(p => p.Cost), 2)
                        }).OrderBy(a => a.Time);

                    var tempConsumptionData = exportSummary.Select(a => new ConsumptionExcelData { CircuitName = tempCircuitName, DateTime = a.Time.ToString("yyyy-MM-dd HH:mm"), Value = a.Value }).ToList();
                    var tempFeeData = exportSummary.Select(a => new ConsumptionExcelData { CircuitName = tempCircuitName, DateTime = a.Time.ToString("yyyy-MM-dd HH:mm"), Value = a.Cost }).ToList();

                    if (tempConsumptionData != null && tempConsumptionData.Any())
                    {
                        consumptionData.AddRange(tempConsumptionData);
                    }

                    if (tempFeeData != null && tempFeeData.Any())
                    {
                        feeData.AddRange(tempFeeData);
                    }
                }
            }

            #endregion


            #region 以前的查询方式

            //// 查询对应区间的数据
            //var summaryList = await _client.Queryable<EnergySummary>()
            //    .Where(a => SqlFunc.ContainsArray(assetIds, a.AssetId))
            //    .Where(a => a.ConsumptionDate >= tempStartDate.Date && a.ConsumptionDate < a.ConsumptionDate.Date.AddDays(1))
            //    .GroupBy(a => a.ConsumptionDate)
            //    .Select(a => new
            //    {
            //        a.ConsumptionDate,
            //        Electricity = SqlFunc.AggregateSum((a.PeakElectricity + a.FlatElectricity + a.ValleyElectricity) / 1000.0M),
            //        Cost = SqlFunc.AggregateSum(a.PeakCost + a.FlatCost + a.ValleyCost)
            //    })
            //    .OrderBy(a => a.ConsumptionDate)
            //    .ToListAsync();

            //consumptionData = summaryList.Select(a => new ConsumptionExcelData { DateTime = a.ConsumptionDate.ToString("yyyy-MM-dd"), Value = Math.Round(a.Electricity, 2) }).ToList();
            //feeData = summaryList.Select(a => new ConsumptionExcelData { DateTime = a.ConsumptionDate.ToString("yyyy-MM-dd"), Value = Math.Round(a.Cost, 2) }).ToList();

            #endregion

            var finalLoopText = query.CircuitId > 0 ? circuitAsset?.AssetName ?? string.Empty : (UserLanguage == "zh-cn" ? "全部" : "ALL");
            var finalDeviceText = query.AssetId > 0 ? assetInfo?.AssetName ?? string.Empty : (UserLanguage == "zh-cn" ? "全部" : "ALL");
            var data = new Dictionary<string, object>()
            {
                ["Consumption"] = new[]{new EnergyExportConsumptionResult
                {
                    Loop = finalLoopText,
                    Device = finalDeviceText,
                    DateRange = $"{tempStartDate.ToString("yyyy-MM-dd")}~{tempEndDate.ToString("yyyy-MM-dd")}",
                    MaxValue = consumptionData.Max(a => a.Value).GetValueOrDefault(),
                    MinValue = consumptionData.Min(a => a.Value).GetValueOrDefault(),
                    AvgValue = consumptionData.Average(a => a.Value).GetValueOrDefault()
                }},
                ["ConsumptionData"] = consumptionData,
                ["Fee"] = new[]{new EnergyExportConsumptionResult
                {
                    Loop = finalLoopText,
                    Device = finalDeviceText,
                    DateRange = $"{tempStartDate.ToString("yyyy-MM-dd")}~{tempEndDate.ToString("yyyy-MM-dd")}",
                    MaxValue = feeData.Max(a => a.Value).GetValueOrDefault(),
                    MinValue = feeData.Min(a => a.Value).GetValueOrDefault(),
                    AvgValue = feeData.Average(a => a.Value).GetValueOrDefault()
                }},
                ["FeeData"] = feeData,
            };

            var ms = new MemoryStream();
            await _excelHelper.SaveExcelAsync(ms, data, UserLanguage, "PowerConsumptionTemplate");
            ms.Seek(0, SeekOrigin.Begin);
            return File(ms, "application/octet-stream", fileDownloadName: $"{MessageContext.PowerConsumptionFileName}.xlsx", enableRangeProcessing: true);
        }

        [HttpGet("analysis/chart/power")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_Energy_GetPowerChart", Description = "Swagger_Energy_GetPowerChart_Desc")]
        public async Task<ResponseBase<List<PowerChartResult>>> GetPowerChart([FromQuery] EnergyPowerQueryParam query)
        {
            if (query == null
                || string.IsNullOrWhiteSpace(query.StartDate)
                || !DateTime.TryParse(query.StartDate, out var tempStartDate)
                || tempStartDate > DateTime.Now)
            {
                return new ResponseBase<List<PowerChartResult>>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            List<int> assetRelations = new List<int>();
            if (query.CircuitId > 0)
            {
                assetRelations = await _client.Queryable<AssetRelation>()
                    .Where(a => a.ParentId == query.CircuitId)
                    .Select(a => a.ChildId)
                    .ToListAsync();
            }

            var assetIds = await _client.Queryable<AssetInfo>()
                .Where(a => a.AssetLevel == AssetLevel.Device)
                .WhereIF(!string.IsNullOrWhiteSpace(query.MeasurementFinal), a => a.MeterType == query.MeasurementFinal)
                .WhereIF(query.CircuitId > 0, a => SqlFunc.ContainsArray(assetRelations.ToArray(), a.Id))
                .WhereIF(query.AssetId > 0, a => a.Id == query.AssetId)
                .Select(a => a.Id)
                .ToListAsync();

            List<PowerChartResult> powerCharts = new List<PowerChartResult>();

            if (query.PowerType == null || !query.PowerType.Any())
            {
                query.PowerType = new List<ThreePhasePowerType>();
                foreach (int item in Enum.GetValues(typeof(ThreePhasePowerType)))
                {
                    query.PowerType.Add((ThreePhasePowerType)item);
                }
            }

            string[] tempPowerUnit = ["kW", "kVar", "kVA"];
            string[] influxFieldName = ["p", "q", "s"];

            PowerChartResult powerChartResult;
            LineChartModel lineChartModel;

            var endDate = DateTime.Now;

            if (query.ChartDateType == 0)
            {
                tempStartDate = tempStartDate.Date;

                if (tempStartDate.Date == DateTime.Now.Date)
                {
                    endDate = DateTime.Now;
                }
                else
                {
                    endDate = tempStartDate.AddDays(1).AddSeconds(-1);
                }
            }
            else
            {
                tempStartDate = new DateTime(tempStartDate.Year, tempStartDate.Month, 1);

                if (tempStartDate.Year != endDate.Year || tempStartDate.Month != endDate.Month)
                {
                    endDate = tempStartDate.AddDays(DateTime.DaysInMonth(tempStartDate.Year, tempStartDate.Month)).AddSeconds(-1);
                }
            }

            var energyManagementServer = _provider.GetRequiredService<EnergyManagementServer>();

            var powerData = await energyManagementServer.GetPowerData(assetIds, tempStartDate, endDate, query.PowerType.Select(a => a.GetHashCode()).ToList());

            if (powerData == null)
            {
                return new ResponseBase<List<PowerChartResult>>()
                {
                    Code = 20000,
                    Data = powerCharts
                };
            }

            query.PowerType.ForEach(power =>
            {
                var sourceData = powerData.Where(a => a.Field == influxFieldName[power.GetHashCode()]).ToList();
                var tempDataCount = sourceData.Count();

                powerChartResult = new PowerChartResult();
                var xAxis = new string[tempDataCount];
                var yAxis1 = new decimal[tempDataCount];

                powerChartResult.PowerType = power;
                powerChartResult.MaxValue = sourceData.DefaultIfEmpty().Max(a => a.Value);
                powerChartResult.MinValue = sourceData.DefaultIfEmpty().Min(a => a.Value);
                powerChartResult.AvgValue = sourceData.DefaultIfEmpty().Average(a => a.Value);
                powerChartResult.ValueUnit = tempPowerUnit[power.GetHashCode()];

                for (int i = 0; i < tempDataCount; i++)
                {
                    xAxis[i] = sourceData[i].Date.ToString("yyyy-MM-dd HH:mm");
                    yAxis1[i] = Math.Round(sourceData[i].Value, 2);
                }

                lineChartModel = new LineChartModel();
                lineChartModel.X = xAxis;
                lineChartModel.Y1 = yAxis1;
                lineChartModel.Standard = powerChartResult.AvgValue;

                powerChartResult.LineChartModel = lineChartModel;
                powerCharts.Add(powerChartResult);
            });

            return new ResponseBase<List<PowerChartResult>>()
            {
                Code = 20000,
                Data = powerCharts
            };
        }

        [HttpGet("analysis/chart/peak-flat-valley")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_Energy_GetPeakFlatValleyChart", Description = "Swagger_Energy_GetPeakFlatValleyChart_Desc")]
        public async Task<ResponseBase<EnergyChartResult>> GetPeakFlatValleyChart([FromQuery] EnergyPeakFlatValleyQueryParam query)
        {
            if (query == null
                || string.IsNullOrWhiteSpace(query.StartDate)
                || !DateTime.TryParse(query.StartDate, out var tempStartDate)
                || tempStartDate > DateTime.Now)
            {
                return new ResponseBase<EnergyChartResult>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }
            try
            {
                var coefficient = await GetEnergyCoefficient();

                var energyManagementServer = _provider.GetRequiredService<EnergyManagementServer>();
                var energyChartResult = new EnergyChartResult();
                LineChartModel lineChartModel = new LineChartModel();

                #region 新的查询方式

                //var peakFlatValleyData = await energyManagementServer.GetPeakFlatValleyRealTimeData(tempStartDate, query.MeasurementFinal, query.CircuitId, query.AssetId);

                #endregion

                #region 以前的获取方式

                var peakFlatValleyData = await energyManagementServer.GetPeakFlatValleyData(tempStartDate, query.MeasurementFinal, query.CircuitId, query.AssetId);

                #endregion

                var allDataCount = peakFlatValleyData.Count;
                var xAxis = new string[allDataCount];
                var yAxis1 = new decimal[allDataCount];
                var yAxis2 = new decimal[allDataCount];
                var yAxis3 = new decimal[allDataCount];
                var yAxis4 = new decimal[allDataCount];
                var yAxis5 = new decimal[allDataCount];

                for (int i = 0; i < allDataCount; i++)
                {
                    xAxis[i] = peakFlatValleyData[i].ConsumptionDate.ToString("MM-dd");
                    yAxis1[i] = Math.Round(peakFlatValleyData[i].PeakElectricity / coefficient, 2);
                    yAxis2[i] = Math.Round(peakFlatValleyData[i].FlatElectricity / coefficient, 2);
                    yAxis3[i] = Math.Round(peakFlatValleyData[i].ValleyElectricity / coefficient, 2);
                    yAxis4[i] = Math.Round(peakFlatValleyData[i].HighPeakElectricity / coefficient, 2);
                    yAxis5[i] = Math.Round(peakFlatValleyData[i].DeepValleyElectricity / coefficient, 2);
                }

                lineChartModel.X = xAxis;
                lineChartModel.Y1 = yAxis1;
                lineChartModel.Y2 = yAxis2;
                lineChartModel.Y3 = yAxis3;
                lineChartModel.Y4 = yAxis4;
                lineChartModel.Y5 = yAxis5;
                energyChartResult.LineChartModel = lineChartModel;
                energyChartResult.ValueUnit = "kWh";

                return new ResponseBase<EnergyChartResult>()
                {
                    Code = 20000,
                    Data = energyChartResult
                };
            }
            catch (Exception ex)
            {
                _log.LogError(ex.Message, ex);
                return new ResponseBase<EnergyChartResult>()
                {
                    Code = 20000,
                    Data = new EnergyChartResult()
                };
            }

        }

        [HttpGet("analysis/chart/structure")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_Energy_GetStructureChart", Description = "Swagger_Energy_GetStructureChart_Desc")]
        public async Task<ResponseBase<PieChartModel>> GetStructureChart([FromQuery] EnergyStructureQueryParam query)
        {
            DateTime tempEndDate = DateTime.MinValue;

            if (query == null
                || string.IsNullOrWhiteSpace(query.StartDate)
                || !DateTime.TryParse(query.StartDate, out var tempStartDate)
                || tempStartDate > DateTime.Now
                || (query.DateType == ChartDateType.Custom && !DateTime.TryParse(query.EndDate, out tempEndDate)))
            {
                return new ResponseBase<PieChartModel>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            PieChartModel pieChartModel = new PieChartModel();
            List<PieInfo> pieInfos = new List<PieInfo>();

            var energyManagementServer = _provider.GetRequiredService<EnergyManagementServer>();

            #region  新的的查询方式

            var chartData = await energyManagementServer.GetStructureRealTimeData(tempStartDate, tempEndDate, query.DateType.GetHashCode());

            #endregion


            #region  以前的查询方式

            //var chartData = await energyManagementServer.GetStructureData(tempStartDate, tempEndDate, query.DateType.GetHashCode());

            #endregion

            var str = "UseScene".ToUpper();
            var useSceneList = await _client.Queryable<SystemStaticModel>()
                .Where(s => s.Type == str)
                .WithCache($"SystemStaticModel:{str}", (int)TimeSpan.FromHours(1).TotalSeconds)
                .ToArrayAsync();

            PieInfo pieInfo;
            chartData.ForEach(d =>
            {
                pieInfo = new PieInfo();

                pieInfo.Name = UserLanguage == "zh-cn" ? useSceneList.FirstOrDefault(a => a.Code == d.Name)?.Name ?? d.Name : d.Name;
                pieInfo.Value = d.Value;

                pieInfos.Add(pieInfo);
            });

            pieChartModel.Summary = pieInfos.ToArray();

            return new ResponseBase<PieChartModel>()
            {
                Code = 20000,
                Data = pieChartModel
            };
        }

        [HttpGet("analysis/chart/top10")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_Energy_GetTopTenChart", Description = "Swagger_Energy_GetTopTenChart_Desc")]
        public async Task<ResponseBase<LineChartModel>> GetTopTenChart([FromQuery] EnergyStructureQueryParam query)
        {
            DateTime tempEndDate = DateTime.MinValue;

            if (query == null
                || string.IsNullOrWhiteSpace(query.StartDate)
                || !DateTime.TryParse(query.StartDate, out var tempStartDate)
                || tempStartDate > DateTime.Now
                || (query.DateType == ChartDateType.Custom && !DateTime.TryParse(query.EndDate, out tempEndDate)))
            {
                return new ResponseBase<LineChartModel>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var coefficient = await GetEnergyCoefficient();
            var energyManagementServer = _provider.GetRequiredService<EnergyManagementServer>();

            #region  新的的查询方式

            //var chartData = await energyManagementServer.GetTopTenRealTimeData(tempStartDate, tempEndDate, query.DateType.GetHashCode());

            #endregion


            #region  以前的查询方式

            var chartData = await energyManagementServer.GetTopTenData(tempStartDate, tempEndDate, query.DateType.GetHashCode());

            #endregion

            #region
            if (coefficient == 1)
            {
                if (chartData != null && chartData.Count > 0)
                {
                    var newData = new List<(string AssetName, decimal Value)>();
                    foreach (var d in chartData)
                    {
                        newData.Add((d.AssetName, d.Value * 1000M));
                    }

                    chartData = newData;
                }
            }
            #endregion

            LineChartModel lineChartModel = new LineChartModel();

            lineChartModel.X = chartData.Select(a => a.AssetName).ToArray();
            lineChartModel.Y1 = chartData.Select(a => a.Value).ToArray();

            return new ResponseBase<LineChartModel>()
            {
                Code = 20000,
                Data = lineChartModel
            };
        }

        [HttpGet("analysis/export/power")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_Energy_ExportPower", Description = "Swagger_Energy_ExportPower_Desc")]
        public async Task<IActionResult> ExportPower([FromQuery] EnergyPowerExportParam query)
        {
            if (query == null
                || string.IsNullOrWhiteSpace(query.StartDate)
                || !DateTime.TryParse(query.StartDate, out var tempStartDate)
                || tempStartDate > DateTime.Now
                || string.IsNullOrWhiteSpace(query.EndDate)
                || !DateTime.TryParse(query.EndDate, out var tempEndDate))
            {
                return NotFound();
            }

            List<ConsumptionExcelData> consumptionData = new List<ConsumptionExcelData>();
            List<ConsumptionExcelData> feeData = new List<ConsumptionExcelData>();

            List<int> assetRelations = new List<int>();
            AssetInfo? circuitAsset = null;
            AssetInfo? assetInfo = null;
            if (query.CircuitId > 0)
            {
                assetRelations = await _client.Queryable<AssetRelation>()
                    .Where(a => a.ParentId == query.CircuitId)
                    .Select(a => a.ChildId)
                    .ToListAsync();

                circuitAsset = await _client.Queryable<AssetInfo>().Where(a => a.Id == query.CircuitId).FirstAsync();
            }

            if (query.AssetId > 0)
            {
                assetInfo = await _client.Queryable<AssetInfo>().Where(a => a.Id == query.AssetId).FirstAsync();
            }

            var assets = await _client.Queryable<AssetInfo>()
                .Where(a => a.AssetLevel == AssetLevel.Device)
                .WhereIF(query.CircuitId > 0, a => SqlFunc.ContainsArray(assetRelations.ToArray(), a.Id))
                .WhereIF(query.AssetId > 0, a => a.Id == query.AssetId)
                .Select(a => new { a.Id, a.AssetName })
                .ToListAsync();

            var assetIds = assets.Select(a => a.Id).ToList();

            var energyManagementServer = _provider.GetRequiredService<EnergyManagementServer>();
            var powerData = await energyManagementServer.ExportPowerData(assetIds, tempStartDate, tempEndDate);

            List<ConsumptionExcelData> activePowerData = new List<ConsumptionExcelData>();
            List<ConsumptionExcelData> reactivePowerData = new List<ConsumptionExcelData>();
            List<ConsumptionExcelData> apparentPowerData = new List<ConsumptionExcelData>();

            if (query.CircuitId > 0 || query.AssetId > 0)
            {
                var exportSummary = powerData
                     .GroupBy(a => a.Time)
                     .Select(a => new
                     {
                         Time = a.Key,
                         P = Math.Round(a.Sum(p => p.P), 2),
                         Q = Math.Round(a.Sum(p => p.Q), 2),
                         S = Math.Round(a.Sum(p => p.S), 2)
                     }).OrderBy(a => a.Time);

                activePowerData = exportSummary.Select(a => new ConsumptionExcelData
                {
                    CircuitName = circuitAsset?.AssetName ?? string.Empty,
                    DateTime = a.Time.ToString("yyyy-MM-dd HH:mm"),
                    Value = a.P
                }).ToList();
                reactivePowerData = exportSummary.Select(a => new ConsumptionExcelData
                {
                    CircuitName = circuitAsset?.AssetName ?? string.Empty,
                    DateTime = a.Time.ToString("yyyy-MM-dd HH:mm"),
                    Value = a.Q
                }).ToList();
                apparentPowerData = exportSummary.Select(a => new ConsumptionExcelData
                {
                    CircuitName = circuitAsset?.AssetName ?? string.Empty,
                    DateTime = a.Time.ToString("yyyy-MM-dd HH:mm"),
                    Value = a.S
                }).ToList();
            }
            else
            {
                var assetAndCircuits = await _client.Queryable<AssetInfo>()
                .Where(a => a.AssetLevel == AssetLevel.Device || a.AssetLevel == AssetLevel.Circuit)
                .Select(a => new { a.Id, a.AssetName, a.AssetLevel })
                .ToListAsync();

                var circuitIds = assetAndCircuits.Where(a => a.AssetLevel == AssetLevel.Circuit).Select(a => a.Id).ToArray();

                var tempRelation = await _client.Queryable<AssetRelation>().Where(a => SqlFunc.ContainsArray(circuitIds, a.ParentId)).ToListAsync();

                foreach (var circuitId in circuitIds)
                {
                    var tempAllIds = tempRelation.Where(a => a.ParentId == circuitId).Select(a => a.ChildId).ToList();
                    var tempCircuitName = assetAndCircuits.FirstOrDefault(a => a.Id == circuitId)?.AssetName ?? string.Empty;

                    var exportSummary = powerData
                        .Where(a => tempAllIds.Contains(a.AssetId))
                        .GroupBy(a => a.Time)
                        .Select(a => new
                        {
                            Time = a.Key,
                            P = Math.Round(a.Sum(p => p.P), 2),
                            Q = Math.Round(a.Sum(p => p.Q), 2),
                            S = Math.Round(a.Sum(p => p.S), 2)
                        }).OrderBy(a => a.Time);

                    var tempActivePowerData = exportSummary.Select(a => new ConsumptionExcelData
                    {
                        CircuitName = tempCircuitName,
                        DateTime = a.Time.ToString("yyyy-MM-dd HH:mm"),
                        Value = a.P
                    }).ToList();
                    var tempReactivePowerData = exportSummary.Select(a => new ConsumptionExcelData
                    {
                        CircuitName = tempCircuitName,
                        DateTime = a.Time.ToString("yyyy-MM-dd HH:mm"),
                        Value = a.Q
                    }).ToList();
                    var tempApparentPowerData = exportSummary.Select(a => new ConsumptionExcelData
                    {
                        CircuitName = tempCircuitName,
                        DateTime = a.Time.ToString("yyyy-MM-dd HH:mm"),
                        Value = a.S
                    }).ToList();

                    if (tempActivePowerData != null && tempActivePowerData.Count != 0)
                    {
                        activePowerData.AddRange(tempActivePowerData);
                    }

                    if (tempReactivePowerData != null && tempReactivePowerData.Count != 0)
                    {
                        reactivePowerData.AddRange(tempReactivePowerData);
                    }

                    if (tempApparentPowerData != null && tempApparentPowerData.Count != 0)
                    {
                        apparentPowerData.AddRange(tempApparentPowerData);
                    }
                }
            }

            var finalLoopText = query.CircuitId > 0 ? circuitAsset?.AssetName ?? string.Empty : (UserLanguage == "zh-cn" ? "全部" : "ALL");
            var finalDeviceText = query.AssetId > 0 ? assetInfo?.AssetName ?? string.Empty : (UserLanguage == "zh-cn" ? "全部" : "ALL");

            var data = new Dictionary<string, object>()
            {
                ["ActivePower"] = new[] {new{
                    Loop = finalLoopText,
                    Device = finalDeviceText,
                    DateRange = $"{tempStartDate.ToString("yyyy-MM-dd")}~{tempEndDate.ToString("yyyy-MM-dd")}",
                    CreateDate = DateTime.Now.ToString("yyyy-MM-dd"),
                }},
                ["ActivePowerData"] = activePowerData,
                ["ReactivePower"] = new[] {new{
                    Loop = finalLoopText,
                    Device = finalDeviceText,
                    DateRange = $"{tempStartDate.ToString("yyyy-MM-dd")}~{tempEndDate.ToString("yyyy-MM-dd")}",
                    CreateDate = DateTime.Now.ToString("yyyy-MM-dd"),
                } },
                ["ReactivePowerData"] = reactivePowerData,
                ["ApparentPower"] = new[] {new{
                    Loop = finalLoopText,
                    Device = finalDeviceText,
                    DateRange = $"{tempStartDate.ToString("yyyy-MM-dd")}~{tempEndDate.ToString("yyyy-MM-dd")}",
                    CreateDate = DateTime.Now.ToString("yyyy-MM-dd"),
                } },
                ["ApparentPowerData"] = apparentPowerData,
            };

            var ms = new MemoryStream();
            await _excelHelper.SaveExcelAsync(ms, data, UserLanguage, "PowerTemplate");
            ms.Seek(0, SeekOrigin.Begin);
            return File(ms, "application/octet-stream", fileDownloadName: $"{MessageContext.PowerFileName}.xlsx", enableRangeProcessing: true);
        }

        [HttpGet("analysis/export/peak-flat-valley")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_Energy_ExportPeakFlatValley", Description = "Swagger_Energy_ExportPeakFlatValley_Desc")]
        public async Task<IActionResult> ExportPeakFlatValley([FromQuery] EnergyPowerExportParam query)
        {
            if (query == null
                || string.IsNullOrWhiteSpace(query.StartDate)
                || !DateTime.TryParse(query.StartDate, out var tempStartDate)
                || tempStartDate > DateTime.Now)
            {
                return NotFound();
            }

            var energyManagementServer = _provider.GetRequiredService<EnergyManagementServer>();
            //var peakFlatValleyData = await energyManagementServer.GetPeakFlatValleyRealTimeData(tempStartDate, query.MeasurementFinal, query.CircuitId, query.AssetId);
            var peakFlatValleyData = await energyManagementServer.GetPeakFlatValleyData(tempStartDate, query.MeasurementFinal, query.CircuitId, query.AssetId);

            var peakFlatValleyList = peakFlatValleyData.Select(a => new
            {
                DateTime = a.ConsumptionDate.ToString("MM-dd"),
                Peak = Math.Round(a.PeakElectricity, 2),
                Flat = Math.Round(a.FlatElectricity, 2),
                Valley = Math.Round(a.ValleyElectricity, 2),
                Total = Math.Round(a.PeakElectricity + a.FlatElectricity + a.ValleyElectricity, 2),
            });

            AssetInfo? circuitAsset = null;
            AssetInfo? assetInfo = null;

            if (query.CircuitId > 0)
            {
                circuitAsset = await _client.Queryable<AssetInfo>().Where(a => a.Id == query.CircuitId).FirstAsync();
            }

            if (query.AssetId > 0)
            {
                assetInfo = await _client.Queryable<AssetInfo>().Where(a => a.Id == query.AssetId).FirstAsync();
            }


            var finalMeasureText = string.Empty;

            if (string.IsNullOrWhiteSpace(query.MeasurementFinal))
            {
                finalMeasureText = UserLanguage == "zh-cn" ? "全部" : "ALL";
            }
            else if (string.Equals(query.MeasurementFinal, MeasurementType.Gateway.ToString(), StringComparison.CurrentCultureIgnoreCase))
            {
                finalMeasureText = UserLanguage == "zh-cn" ? "关口计量" : MeasurementType.Gateway.ToString();
            }
            else if (string.Equals(query.MeasurementFinal, MeasurementType.Normal.ToString(), StringComparison.CurrentCultureIgnoreCase))
            {
                finalMeasureText = UserLanguage == "zh-cn" ? "普通计量" : MeasurementType.Normal.ToString();
            }
            else
            {
                finalMeasureText = UserLanguage == "zh-cn" ? "终端计量" : MeasurementType.Terminal.ToString();
            }

            var finalLoopText = query.CircuitId > 0 ? circuitAsset?.AssetName ?? string.Empty : (UserLanguage == "zh-cn" ? "全部" : "ALL");
            var finalDeviceText = query.AssetId > 0 ? assetInfo?.AssetName ?? string.Empty : (UserLanguage == "zh-cn" ? "全部" : "ALL");
            var data = new Dictionary<string, object>()
            {
                ["Title"] = new[]{new
                {
                    Measurement = finalMeasureText,
                    Loop = finalLoopText,
                    Device = finalDeviceText,
                    Date = tempStartDate.ToString("yyyy-MM-dd"),
                    CreateDate = DateTime.Now.ToString("yyyy-MM-dd"),
                }},
                ["Data"] = peakFlatValleyList
            };

            var ms = new MemoryStream();
            await _excelHelper.SaveExcelAsync(ms, data, UserLanguage, "PeakFlatValleyTemplate");
            ms.Seek(0, SeekOrigin.Begin);
            return File(ms, "application/octet-stream", fileDownloadName: $"{MessageContext.PeakFlatValleyFileName}.xlsx", enableRangeProcessing: true);
        }

        [HttpGet("analysis/export/structure")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_Energy_ExportStructure", Description = "Swagger_Energy_ExportStructure_Desc")]
        public async Task<IActionResult> ExportStructure([FromQuery] EnergyStructureQueryParam query)
        {
            DateTime tempEndDate = DateTime.MinValue;

            if (query == null
                || string.IsNullOrWhiteSpace(query.StartDate)
                || !DateTime.TryParse(query.StartDate, out var tempStartDate)
                || tempStartDate > DateTime.Now
                || (query.DateType == ChartDateType.Custom && !DateTime.TryParse(query.EndDate, out tempEndDate)))
            {
                return NotFound();
            }

            string[] dateTypes = new string[] { "日", "月", "年", "自定义" };

            var energyManagementServer = _provider.GetRequiredService<EnergyManagementServer>();

            #region  新的的查询方式

            var chartData = await energyManagementServer.GetStructureRealTimeData(tempStartDate, tempEndDate, query.DateType.GetHashCode());

            #endregion


            #region  以前的查询方式

            //var chartData = await energyManagementServer.GetStructureData(tempStartDate, tempEndDate, query.DateType.GetHashCode());

            #endregion

            var str = "UseScene".ToUpper();
            var useSceneList = await _client.Queryable<SystemStaticModel>()
                .Where(s => s.Type == str)
                .WithCache($"SystemStaticModel:{str}", (int)TimeSpan.FromHours(1).TotalSeconds)
                .ToArrayAsync();

            List<dynamic> tempData = new List<dynamic>();
            chartData.ForEach(d =>
            {
                tempData.Add(new { DeviceType = useSceneList.FirstOrDefault(a => a.Code == d.Name)?.Name ?? d.Name, d.Value });
            });


            var dateString = string.Empty;

            if (query.DateType == ChartDateType.Day)
            {
                dateString = tempStartDate.ToString("yyyy-MM-dd");
            }
            else if (query.DateType == ChartDateType.Month)
            {
                dateString = tempStartDate.ToString("yyyy-MM");
            }
            else if (query.DateType == ChartDateType.Year)
            {
                dateString = tempStartDate.ToString("yyyy");
            }
            else
            {
                dateString = $"{tempStartDate.ToString("yyyy-MM-dd")} - {tempEndDate.ToString("yyyy-MM-dd")}";
            }

            var data = new Dictionary<string, object>()
            {
                ["Title"] = new[]{new
                {
                    DateType = dateTypes[(int)query.DateType],
                    Date = dateString,
                    CreateDate = DateTime.Now.ToString("yyyy-MM-dd"),
                }},
                ["Data"] = tempData,
            };

            var ms = new MemoryStream();
            await _excelHelper.SaveExcelAsync(ms, data, UserLanguage, "StructureTemplate");
            ms.Seek(0, SeekOrigin.Begin);
            return File(ms, "application/octet-stream", fileDownloadName: $"{MessageContext.StructureFileName}.xlsx", enableRangeProcessing: true);
        }

        [HttpGet("analysis/export/top10")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_Energy_ExportTopTen", Description = "Swagger_Energy_ExportTopTen_Desc")]
        public async Task<IActionResult> ExportTopTen([FromQuery] EnergyStructureQueryParam query)
        {
            DateTime tempEndDate = DateTime.MinValue;

            if (query == null
                || string.IsNullOrWhiteSpace(query.StartDate)
                || !DateTime.TryParse(query.StartDate, out var tempStartDate)
                || tempStartDate > DateTime.Now
                || (query.DateType == ChartDateType.Custom && !DateTime.TryParse(query.EndDate, out tempEndDate)))
            {
                return NotFound();
            }

            string[] dateTypes = new string[] { "日", "月", "年", "自定义" };
            var dateString = string.Empty;

            if (query.DateType == ChartDateType.Day)
            {
                dateString = tempStartDate.ToString("yyyy-MM-dd");
            }
            else if (query.DateType == ChartDateType.Month)
            {
                dateString = tempStartDate.ToString("yyyy-MM");
            }
            else if (query.DateType == ChartDateType.Year)
            {
                dateString = tempStartDate.ToString("yyyy");
            }
            else
            {
                dateString = $"{tempStartDate.ToString("yyyy-MM-dd")} - {tempEndDate.ToString("yyyy-MM-dd")}";
            }

            List<dynamic> tempData = new List<dynamic>();

            var energyManagementServer = _provider.GetRequiredService<EnergyManagementServer>();

            #region  新的的查询方式

            //var chartData = await energyManagementServer.GetTopTenRealTimeData(tempStartDate, tempEndDate, query.DateType.GetHashCode());

            #endregion


            #region  以前的查询方式

            var chartData = await energyManagementServer.GetTopTenData(tempStartDate, tempEndDate, query.DateType.GetHashCode());

            #endregion  

            var data = new Dictionary<string, object>()
            {
                ["Title"] = new[]{new
                {
                    DateType = dateTypes[(int)query.DateType],
                    Date = dateString,
                    CreateDate = DateTime.Now.ToString("yyyy-MM-dd"),
                }},
                ["Data"] = chartData.Select(a => new { Device = a.AssetName, a.Value }).ToList(),
            };

            var ms = new MemoryStream();
            await _excelHelper.SaveExcelAsync(ms, data, UserLanguage, "TopTenTemplate");
            ms.Seek(0, SeekOrigin.Begin);
            return File(ms, "application/octet-stream", fileDownloadName: $"{MessageContext.TopTenFileName}.xlsx", enableRangeProcessing: true);
        }

        [HttpGet("quality/chart/{chartName}")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_Energy_GetQualityChart", Description = "Swagger_Energy_GetQualityChart_Desc")]
        public async Task<ResponseBase<IChart?>> GetQualityChart([FromQuery] EnergyQualityParam param)
        {
            int assetId = 0;
            var startDate = DateTime.Today;
            var endDate = DateTime.Now.AddMinutes(5 - DateTime.Now.Minute % 5);
            if (string.IsNullOrWhiteSpace(param.Device) || !int.TryParse(param.Device, out assetId))
            {
                return new ResponseBase<IChart?>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var service = _provider.GetRequiredService<AssetDashboardServer>();
            param.ChartName = param.ChartName.ToLower();
            switch (param.ChartName)
            {
                case "frequency":
                case "power-factor":
                    {
                        var result = await GetChartInfo(param.ChartName, assetId, startDate, endDate, service);
                        if (result != null)
                        {
                            return new ResponseBase<IChart?>()
                            {
                                Code = 20000,
                                Data = result
                            };
                        }
                        break;
                    }
                case "thd":
                    {
                        var result = await GetChartInfo(param.ChartName, assetId, startDate, endDate, service);

                        if (result != null && result is LineChartModel lineChart && param.THDTypes != null)
                        {
                            var newLineChart = new LineChartModel();
                            newLineChart.X = lineChart.X;
                            newLineChart.Lines = lineChart.Lines;
                            newLineChart.Standard = lineChart.Standard;
                            newLineChart.XColumn = lineChart.XColumn;
                            newLineChart.YColumns = lineChart.YColumns;
                            newLineChart.XTitles = lineChart.XTitles;

                            foreach (var thdType in param.THDTypes)
                            {
                                switch (thdType)
                                {
                                    case THDType.Ua:
                                        newLineChart.Y1 = lineChart.Y1;
                                        break;
                                    case THDType.Ub:
                                        newLineChart.Y2 = lineChart.Y2;
                                        break;
                                    case THDType.Uc:
                                        newLineChart.Y3 = lineChart.Y3;
                                        break;
                                    case THDType.Ia:
                                        newLineChart.Y5 = lineChart.Y5;
                                        break;
                                    case THDType.Ib:
                                        newLineChart.Y6 = lineChart.Y6;
                                        break;
                                    case THDType.Ic:
                                        newLineChart.Y7 = lineChart.Y7;
                                        break;
                                    default: break;
                                }
                            }

                            return new ResponseBase<IChart?>()
                            {
                                Code = 20000,
                                Data = newLineChart
                            };

                        }
                        break;
                    }
                case "harmonic":
                    {
                        var time = DateTime.Today;
                        if (!string.IsNullOrEmpty(param.Date) &&
                            !DateTime.TryParse(param.Date, out time))
                        {
                            time = DateTime.Today;
                        }

                        var result = await GetHarmonicChart(assetId, time, param.Frequency);
                        if (result != null)
                        {
                            return new ResponseBase<IChart?>()
                            {
                                Code = 20000,
                                Data = result,
                            };
                        }
                        break;
                    }
                default: break;
            }

            return new ResponseBase<IChart?>()
            {
                Code = 40300,
                Message = MessageContext.ErrorParam
            };
        }

        [HttpGet("quality/{assetId}/batchchart")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_Energy_GetQualityBatchChart", Description = "Swagger_Energy_GetQualityBatchChart_Desc")]
        public async Task<ResponseBase<Dictionary<string, IChart>>> GetBatchQualityChart([FromQuery] EnergyQualityBatchParam param)
        {
            var startDate = DateTime.Today;
            var endDate = DateTime.Now.AddMinutes(5 - DateTime.Now.Minute % 5);
            if (param.AssetId <= 0)
            {
                return new ResponseBase<Dictionary<string, IChart>>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var dic = new Dictionary<string, IChart>();
            var service = _provider.GetRequiredService<AssetDashboardServer>();
            foreach (var chartName in param.ChartNames)
            {
                switch (chartName)
                {
                    case "frequency":
                    case "power-factor":
                        {
                            var result = await GetChartInfo(chartName, param.AssetId, startDate, endDate, service);
                            if (result != null)
                            {
                                dic.Add(chartName, result);
                            }
                        }
                        break;
                    case "thd":
                        {
                            var result = await GetChartInfo(chartName, param.AssetId, startDate, endDate, service);

                            if (result != null && result is LineChartModel lineChart && param.THDTypes != null)
                            {
                                var newLineChart = new LineChartModel();
                                newLineChart.X = lineChart.X;
                                newLineChart.Lines = lineChart.Lines;
                                newLineChart.Standard = lineChart.Standard;
                                newLineChart.XColumn = lineChart.XColumn;
                                newLineChart.YColumns = lineChart.YColumns;
                                newLineChart.XTitles = lineChart.XTitles;

                                foreach (var thdType in param.THDTypes)
                                {
                                    switch (thdType)
                                    {
                                        case THDType.Ua:
                                            newLineChart.Y1 = lineChart.Y1;
                                            break;
                                        case THDType.Ub:
                                            newLineChart.Y2 = lineChart.Y2;
                                            break;
                                        case THDType.Uc:
                                            newLineChart.Y3 = lineChart.Y3;
                                            break;
                                        case THDType.Ia:
                                            newLineChart.Y5 = lineChart.Y5;
                                            break;
                                        case THDType.Ib:
                                            newLineChart.Y6 = lineChart.Y6;
                                            break;
                                        case THDType.Ic:
                                            newLineChart.Y7 = lineChart.Y7;
                                            break;
                                        default: break;
                                    }
                                }

                                dic.Add(chartName, newLineChart);
                            }
                            break;
                        }
                    case "harmonic":
                        {
                            if (param.Frequency == null || param.Frequency <= 0 || string.IsNullOrEmpty(param.Date))
                            {
                                break;
                            }
                            if (!DateTime.TryParse(param.Date, out var time))
                            {
                                time = DateTime.Today;
                            }

                            var result = await GetHarmonicChart(param.AssetId, time, param.Frequency ?? 0);
                            if (result != null)
                            {
                                dic.Add(chartName, result);
                            }
                        }
                        break;
                    default: break;
                }
            }

            return new ResponseBase<Dictionary<string, IChart>>()
            {
                Code = 20000,
                Data = dic
            };
        }

        [HttpGet("quality/{assetId}/spectrogram/{key}")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_Energy_GetQualitySpectrogram", Description = "Swagger_Energy_GetQualitySpectrogram_Desc")]
        public async Task<ResponseBase<IChart?>> GetSpectrogram(int assetId, long key)
        {
            LineChartModel? lineModel = null;
            var data = await _client.Queryable<DeviceHarmonicHistory>().FirstAsync(d => d.AssetId == assetId && d.Ts == key);

            if (data != null)
            {
                lineModel = new LineChartModel();
                ChangeChart(lineModel, data);
            }

            return new ResponseBase<IChart?>()
            {
                Code = 20000,
                Data = lineModel
            };
        }

        private async Task<IChart?> GetChartInfo(string chartName, int assetId, DateTime startDate, DateTime endDate, AssetDashboardServer service)
        {
            var paramList = new Dictionary<string, string>()
            {
                ["ChartDateType"] = (5).ToString(),
                ["AssetId"] = assetId.ToString(),
                ["StartDate"] = startDate.ToString("yyyy-MM-dd HH:mm:ss"),
                ["EndDate"] = endDate.ToString("yyyy-MM-dd HH:mm:ss"),
            };
            switch (chartName)
            {
                case "frequency":
                    {
                        var configName = "Asset_F";
                        var result = await service.GetDashboard(configName, MessageContext, paramList, _client);
                        return result;
                    }
                case "power-factor":
                    {
                        var configName = "Asset_PowerFactory";
                        var result = await service.GetDashboard(configName, MessageContext, paramList, _client);
                        return result;
                    }
                case "thd":
                    {
                        var configName = "Asset_THD";
                        var result = await service.GetDashboard(configName, MessageContext, paramList, _client);
                        return result;
                    }
                default: break;
            }
            return null;
        }

        private async Task<EnergyHarmonicChartResult?> GetHarmonicChart(int assetId, DateTime time, int f)
        {
            var assetInfo = _cache.Get<AssetSimpleInfo>(string.Format(AssetSimpleInfoCacheKey, assetId));
            if (assetInfo == null) return null;
            var typeStr = "DeviceModelForHarmonic".ToUpper();
            var staticConfigs = await _client.Queryable<SystemStaticModel>()
                .Where(s => s.Type == typeStr)
                .ToArrayAsync();
            //添加assetInfo.AssetModel!= "GeneralDevice"，暂时不支持第三方设备，等第三方设备谐波分析介入后，再去掉这个判断
            var config = staticConfigs.FirstOrDefault(s => assetInfo.AssetModel != "GeneralDevice" && s.Code == assetInfo.AssetModel);
            var src = _provider.GetRequiredService<AssetDashboardServer>();

            var result = new EnergyHarmonicChartResult
            {
                CanShow = config != null
            };

            if (config != null)
            {
                var frequencyCount = 0;
                if (!string.IsNullOrEmpty(config.Extend))
                {
                    var jObj = JObject.Parse(config.Extend);
                    if (jObj != null && jObj.ContainsKey("FrequencyCount"))
                    {
                        frequencyCount = jObj.Value<int>("FrequencyCount");
                    }
                }

                result.FrequencyCount = frequencyCount;

                var start = time.Date.GetTimestampForSec();
                var end = start + 86400L;
                var harmonicData = await _client.Queryable<DeviceHarmonicHistory>()
                    .Where(d => d.AssetId == assetId && d.Ts >= start && d.Ts < end)
                    .OrderByDescending(d => d.Ts)
                    .Select(d => new
                    {
                        Ua = d.Ua1,
                        Ub = d.Ub1,
                        Uc = d.Uc1
                    })
                    .FirstAsync();
                if (harmonicData != null)
                {
                    result.APhaseVfund = harmonicData.Ua;
                    result.BPhaseVfund = harmonicData.Ub;
                    result.CPhaseVfund = harmonicData.Uc;
                }

                var param = new Dictionary<string, string>()
                {
                    ["AssetId"] = assetId.ToString(),
                    ["Start"] = start.ToString(),
                    ["End"] = end.ToString(),
                    ["FrequencyCount"] = f.ToString(),
                };
                var resultDashboard = await src.GetDashboard("deviceHarmonicInfo", MessageContext, param, _client);

                if (resultDashboard is LineChartModel chartModel && chartModel.X != null)
                {
                    var xTitles = new string[chartModel.X.Length];
                    for (var i = 0; i < chartModel.X.Length; i++)
                    {
                        if (long.TryParse(chartModel.X[i], out var r))
                        {
                            xTitles[i] = chartModel.X[i];
                            var datetime = r.GetDateTimeBySec();
                            chartModel.X[i] = datetime.ToString("HH:mm");
                        }
                    }
                    chartModel.XTitles = xTitles;
                    result.LineChartModel = chartModel;
                }
            }

            return result;
        }

        private void ChangeChart(LineChartModel line, DeviceHarmonicHistory data)
        {
            var xList = new List<string>();
            var y1List = new List<decimal>();
            var y2List = new List<decimal>();
            var y3List = new List<decimal>();

            var type = typeof(DeviceHarmonicHistory);
            var pList = type.GetProperties();
            for (var i = 1; i <= 64; i++)
            {
                var uaName = $"Ua{i}";
                var ubName = $"Ub{i}";
                var ucName = $"Uc{i}";
                #region Ua
                {
                    var property = pList.FirstOrDefault(p => p.Name == uaName);
                    if (property == null) continue;
                    xList.Add($"{i} th");
                    var valueObj = property.GetValue(data);
                    if (valueObj == null) break;
                    try
                    {
                        var decimalValue = Convert.ToDecimal(valueObj);
                        y1List.Add(decimalValue);
                    }
                    catch
                    {
                        break;
                    }
                }
                #endregion
                #region Ub
                {
                    var property = pList.FirstOrDefault(p => p.Name == ubName);
                    if (property == null) continue;
                    var valueObj = property.GetValue(data);
                    try
                    {
                        var decimalValue = Convert.ToDecimal(valueObj);
                        y2List.Add(decimalValue);
                    }
                    catch
                    {
                        y2List.Add(0m);
                    }
                }
                #endregion
                #region Uc
                {
                    var property = pList.FirstOrDefault(p => p.Name == ucName);
                    if (property == null) continue;
                    var valueObj = property.GetValue(data);
                    try
                    {
                        var decimalValue = Convert.ToDecimal(valueObj);
                        y3List.Add(decimalValue);
                    }
                    catch
                    {
                        y3List.Add(0m);
                    }
                }
                #endregion
            }

            line.X = xList.ToArray();
            line.Y1 = y1List.ToArray();
            line.Y2 = y2List.ToArray();
            line.Y3 = y3List.ToArray();
        }
    }
}
