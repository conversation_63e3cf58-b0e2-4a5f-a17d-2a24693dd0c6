﻿using Siemens.PanelManager.Common.mqtt;
using Siemens.PanelManager.Model.Database.System;
using SqlSugar;

namespace Siemens.PanelManager.WebApi.ViewModel
{  
    /// <summary>
    ///  mqtt配置返回值
    /// </summary>
    public class SysConfigResult : MqttConfig
    {
        public string? TLSPemFileName { get; private set; }
        public string? ClientPemFilePathName { get; private set; }
        public string? ClientPrivateFilePathName { get; private set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string? Status { get; set; }

        protected override async Task UpdateFilePath(ISqlSugarClient sqlSugarClient)
        {
            var fileIds = new string[] { TLSPemFile ?? string.Empty, ClientPemFile ?? string.Empty, ClientPrivateFile ?? string.Empty };
            var fileInfoes = await sqlSugarClient.Queryable<FileManager>().Where(f => (fileIds.Contains(f.Code) || fileIds.Contains(f.Url)) && !f.IsSystemFile).ToArrayAsync();
            
            foreach (FileManager file in fileInfoes)
            {
                if (string.IsNullOrWhiteSpace(file.Code) || string.IsNullOrWhiteSpace(file.Url)) continue;

                if (file.Url == TLSPemFile || file.Code == TLSPemFile)
                {
                    TLSPemFileName = $"{file.Name}.{file.FileType}";
                }

                if (file.Url == ClientPemFile || file.Code == ClientPemFile)
                {
                    ClientPemFilePathName = $"{file.Name}.{file.FileType}";
                }

                if (file.Url == ClientPrivateFile || file.Code == ClientPrivateFile)
                {
                    ClientPrivateFilePathName = $"{file.Name}.{file.FileType}";
                }
            }

        }

    }
}
