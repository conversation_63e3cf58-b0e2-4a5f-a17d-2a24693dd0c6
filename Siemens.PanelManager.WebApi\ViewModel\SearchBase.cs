﻿namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class SearchBase<T>
    {
        public int Code { get; set; } = 20000;
        public int Page { get; set; }
        public int Count
        {
            get
            {
                if (Items == null) return 0;
                return Items.Count;
            }
        }
        public int TotalCount { get; set; }
        public IList<T>? Items { get; set; }
        public string? Message { get; set; }
    }
}
