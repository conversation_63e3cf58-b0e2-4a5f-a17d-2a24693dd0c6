﻿using Siemens.InfluxDB.Helper.Enum;
using System.Linq.Expressions;

namespace Siemens.InfluxDB.Helper.Interface
{
    public interface IQueryable<T> : IClientBase, ISelectable<T>
        where T : IInfluxData
    {
        IQueryable<T> Limit(uint size, uint offset);
        IQueryable<T> GroupByTime(uint interval, TimeInterval time);
        IQueryable<T> WhereAnd(Expression<Func<T, bool>> predicate);
        IQueryable<T> WhereOr(Expression<Func<T, bool>> predicate);
        IQueryable<T> First();
        IQueryable<T> Last();
        ISelectable<R> Select<R>(Expression<Func<T, R>> predicate) where R : IInfluxData;
    }
}
