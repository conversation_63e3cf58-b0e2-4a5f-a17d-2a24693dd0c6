﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Asset
{
    [SugarTable("asset_knowledge")]
    public class AssetKnowledge : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [SugarColumn(ColumnName = "asset_model", Length = 50, IsNullable = true)]
        public string? AssetModel { get; set; }

        [SugarColumn(ColumnName = "asset_type", Length = 50, IsNullable = true)]
        public string? AssetType { get; set; }

        [SugarColumn(ColumnName = "breakdown", Length = 2048, IsNullable = true)]
        public string? Breakdown { get; set; }

        [SugarColumn(ColumnName = "possible_cause", Length = 2048, IsNullable = true)]
        public string? PossibleCause { get; set; }

        [SugarColumn(ColumnName = "measure", Length = 2048, IsNullable = true)]
        public string? Measure { get; set; }

        [SugarColumn(ColumnName = "breakdown_comment", Length = 2048, IsNullable = true)]
        public string? BreakdownComment { get; set; }

        [SugarColumn(ColumnName = "possible_cause_comment", Length = 2048, IsNullable = true)]
        public string? PossibleCauseComment { get; set; }

        [SugarColumn(ColumnName = "measure_comment", Length = 2048, IsNullable = true)]
        public string? MeasureComment { get; set; }

    }
}
