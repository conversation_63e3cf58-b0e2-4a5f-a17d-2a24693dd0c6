﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.ExportAlarm
{
    internal class ExportAlarmMain
    {
        public List<ExportAlarmModel> AlarmModel { get; set; } = new List<ExportAlarmModel>();
        public List<ExportAlarmRuleChangeLog> ChangeLogs { get; set; } = new List<ExportAlarmRuleChangeLog>();

        public IDictionary<string, object> GetDictionary()
        {
            return new Dictionary<string, object>() 
            {
                ["AlarmModel"] = AlarmModel,
                ["ChangeLogs"] = ChangeLogs
            };
        }
    }
}
