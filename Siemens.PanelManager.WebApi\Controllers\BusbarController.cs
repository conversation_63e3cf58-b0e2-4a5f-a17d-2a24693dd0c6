﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Chart;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Server.Asset;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;
using System.Diagnostics.CodeAnalysis;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class BusbarController : SiemensApiControllerBase
    {
        private ISqlSugarClient _client;
        private SiemensCache _cache;
        private IServiceProvider _provider;
        public BusbarController(IServiceProvider provider, SiemensCache cache, SqlSugarScope client)
            : base(provider, cache)
        {
            _client = client;
            _cache = cache;
            _provider = provider;
        }

        [HttpGet("{busbarId}/Panels")]
        [SwaggerOperation(Summary = "Swagger_Busbar_GetPanelList", Description = "Swagger_Busbar_GetPanelList_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<SearchBase<SimpleAsset>> GetPanelsByBusbarId(int busbarId)
        {
            if (busbarId > 6 || busbarId <= 0)
            {
                return new SearchBase<SimpleAsset>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            var busId = busbarId.ToString();
            var datas = await _client.Queryable<AssetInfo>()
                .Where(a => a.BusBarId != null && a.BusBarId.Contains(busId) && a.AssetLevel == AssetLevel.Panel)
                .Select(a => new SimpleAsset()
                {
                    Id = a.Id,
                    AssetName = a.AssetName,
                    AssetModel = a.AssetModel,
                    AssetType = a.AssetType,
                    AssetLevel = a.AssetLevel
                })
                .OrderBy(a => a.AssetName)
                .ToListAsync();

            return new SearchBase<SimpleAsset>()
            {
                Code = 20000,
                Items = datas,
                Page = 1,
                TotalCount = datas.Count
            };
        }

        [HttpGet("GetBusbarTemperatures")]
        [SwaggerOperation(Summary = "Swagger_Busbar_GetBusbarTemperatures", Description = "Swagger_Busbar_GetBusbarTemperatures_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<IChart?>> GetBusbarTemperatures(int assetId, [AllowNull] string lineName)
        {
            var assetInfo = _cache.Get<AssetSimpleInfo>($"Asset:SimpleInfo-{assetId}");
            if (assetInfo == null || assetInfo.AssetLevel != AssetLevel.Panel)
            {
                return new ResponseBase<IChart?>()
                {
                    Code = 20000,
                    Message = string.Empty
                };
            }

            var charName = "Asset_BusbarTemp";
            var service = _provider.GetRequiredService<AssetDashboardServer>();

            switch (lineName)
            {
                case "1": charName += "_1"; break;
                case "2": charName += "_2"; break;
                default: break;
            }

            var filter = new Dictionary<string, string>()
            {
                ["ChartDateType"] = "4",
                ["AssetId"] = assetId.ToString()
            };

            var chart = await service.GetDashboard(charName, MessageContext, filter, _client);

            return new ResponseBase<IChart?>()
            {
                Code = 20000,
                Data = chart
            };
        }

        [HttpGet("GetBusbarTemperaturesCustomer")]
        [SwaggerOperation(Summary = "Swagger_Busbar_GetBusbarTemperaturesCustomer", Description = "Swagger_Busbar_GetBusbarTemperaturesCustomer_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<IChart?>> GetBusbarTemperaturesCustomer(int assetId, [AllowNull] string lineName)
        {
            var assetInfo = _cache.Get<AssetSimpleInfo>($"Asset:SimpleInfo-{assetId}");
            if (assetInfo == null || assetInfo.AssetLevel != AssetLevel.Panel)
            {
                return new ResponseBase<IChart?>()
                {
                    Code = 20000,
                    Message = string.Empty
                };
            }

            var charName = "Asset_BusbarTempCustomer";
            var service = _provider.GetRequiredService<AssetDashboardServer>();
            string aconnectpoint = "aconnectpoint";
            string bconnectpoint = "bconnectpoint";
            string cconnectpoint = "cconnectpoint";
            string nconnectpoint = "nconnectpoint";
            switch (lineName)
            {
                case "1-1": 
                    aconnectpoint += "_1_1";
                    bconnectpoint += "_1_1";
                    cconnectpoint += "_1_1";
                    nconnectpoint += "_1_1";
                    break;
                case "1-2":
                    aconnectpoint += "_1_2";
                    bconnectpoint += "_1_2";
                    cconnectpoint += "_1_2";
                    nconnectpoint += "_1_2";
                    break;
                case "1-3":
                    aconnectpoint += "_1_3";
                    bconnectpoint += "_1_3";
                    cconnectpoint += "_1_3";
                    nconnectpoint += "_1_3";
                    break;
                case "1-4":
                    aconnectpoint += "_1_4";
                    bconnectpoint += "_1_4";
                    cconnectpoint += "_1_4";
                    nconnectpoint += "_1_4";
                    break;
                case "2-1":
                    aconnectpoint += "_2_1";
                    bconnectpoint += "_2_1";
                    cconnectpoint += "_2_1";
                    nconnectpoint += "_2_1";
                    break;
                case "2-2":
                    aconnectpoint += "_2_2";
                    bconnectpoint += "_2_2";
                    cconnectpoint += "_2_2";
                    nconnectpoint += "_2_2";
                    break;
                case "2-3":
                    aconnectpoint += "_2_3";
                    bconnectpoint += "_2_3";
                    cconnectpoint += "_2_3";
                    nconnectpoint += "_2_3";
                    break;
                case "2-4":
                    aconnectpoint += "_2_4";
                    bconnectpoint += "_2_4";
                    cconnectpoint += "_2_4";
                    nconnectpoint += "_2_4";
                    break;
                default: break;
            }
            var customerList = await _client.Queryable<CustomDataPoint>().Where(a => a.TargetAssetId == assetId).ToListAsync();
            foreach( var customer in customerList)
            {
                if(customer.TargetDataPointName.Equals(aconnectpoint, StringComparison.OrdinalIgnoreCase)&&!string.IsNullOrEmpty(customer.RealDataPointName))
                {
                    aconnectpoint = customer.RealDataPointName.ToLower();
                }
                if (customer.TargetDataPointName.Equals(bconnectpoint, StringComparison.OrdinalIgnoreCase) && !string.IsNullOrEmpty(customer.RealDataPointName))
                {
                    bconnectpoint = customer.RealDataPointName.ToLower();
                }
                if (customer.TargetDataPointName.Equals(cconnectpoint, StringComparison.OrdinalIgnoreCase) && !string.IsNullOrEmpty(customer.RealDataPointName))
                {
                    cconnectpoint = customer.RealDataPointName.ToLower();
                }
                if (customer.TargetDataPointName.Equals(nconnectpoint, StringComparison.OrdinalIgnoreCase) && !string.IsNullOrEmpty(customer.RealDataPointName))
                {
                    nconnectpoint = customer.RealDataPointName.ToLower();
                }
                assetId = customer.RealAssetId;
            }
            var filter = new Dictionary<string, string>()
            {
                ["ChartDateType"] = "4",
                ["AssetId"] = assetId.ToString(),
                ["aconnectpoint"]= aconnectpoint,
                ["bconnectpoint"] = bconnectpoint,
                ["cconnectpoint"] = cconnectpoint,
                ["nconnectpoint"] = nconnectpoint,
                ["Extend"] = "[\"" + aconnectpoint + "\",\"" + bconnectpoint + "\",\"" + cconnectpoint + "\",\"" + nconnectpoint + "\"]"
            };

            var chart = await service.GetDashboard(charName, MessageContext, filter, _client);

            return new ResponseBase<IChart?>()
            {
                Code = 20000,
                Data = chart
            };
        }

        [HttpGet("{busbarId}/GetBreaker")]
        [SwaggerOperation(Summary = "Swagger_Busbar_GetBreaker", Description = "Swagger_Busbar_GetBreaker_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<SimpleAsset>> GetBreaker(int busbarId)
        {
            if (busbarId > 6 || busbarId <= 0)
            {
                return new ResponseBase<SimpleAsset>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }

            var busId = busbarId.ToString();
            var circuits = await _client.Queryable<AssetInfo>()
                .InnerJoin<AssetRelation>((p, r) => p.Id == r.ParentId)
                .InnerJoin<AssetInfo>((p, r, c) => c.Id == r.ChildId)
                .Where((p, r, c) => p.BusBarId != null
                    && busId.Equals(p.BusBarId)
                    && p.AssetLevel == AssetLevel.Panel
                    && c.AssetLevel == AssetLevel.Circuit
                    && (c.AssetType == "Incoming" || c.AssetType == "BusCoupler"))
                .OrderBy((p, r, c) => c.Id)
                .Select((p, r, c) => new SimpleAsset()
                {
                    Id = c.Id,
                    AssetName = c.AssetName,
                    AssetModel = c.AssetModel,
                    AssetType = c.AssetType,
                    AssetLevel = c.AssetLevel
                })
                .ToListAsync();

            SimpleAsset? breaker = null;
            var breakerTypes = new string[] { "ACB", "MCCB", "MCB" };
            circuits = circuits.OrderBy(c => c.Sort).ToList();
            foreach (var c in circuits)
            {
                breaker = await _client.Queryable<AssetRelation>()
                    .InnerJoin<AssetInfo>((ar, a) => ar.ChildId == a.Id)
                    .Where((ar, a) => ar.ParentId == c.Id && breakerTypes.Contains(a.AssetType))
                    .Select((ar, a) => new SimpleAsset()
                    {
                        Id = a.Id,
                        AssetName = a.AssetName,
                        AssetModel = a.AssetModel,
                        AssetType = a.AssetType,
                        AssetLevel = a.AssetLevel
                    })
                    .FirstAsync();

                if (breaker != null)
                    break;
            }

            return new ResponseBase<SimpleAsset>
            {
                Code = 20000,
                Data = breaker,
                Message = breaker == null ? "Not found" : "Success"
            };
        }
    }
}
