﻿using Siemens.PanelManager.Model.Database.Alarm;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class AlarmLogListItemResult
    {
        public AlarmLogListItemResult(AlarmLog log, AlarmRule? rule)
        {
            Id = log.Id;
            SubstationName = log.SubstationName ?? string.Empty;
            PanelName= log.PanelName ?? string.Empty;
            CircuitName= log.CircuitName ?? string.Empty;
            DeviceName= log.DeviceName ?? string.Empty;
            LogTime = log.CreatedTime;
            if (rule != null)
            {
                AlarmName = rule.Name;
                AlarmRule = rule.RuleInfo;
            }
            Severity = (int)log.Severity;
            AlarmStatus = (int)log.Status;
            EventType = (int)log.EventType;
            AlarmInfo = log.Message;
        }

        public long Id { get; set; }
        public string SubstationName { get; set; } = string.Empty;
        public string PanelName { get; set; } = string.Empty;
        public string CircuitName { get; set; } = string.Empty;
        public string DeviceName { get; set; } = string.Empty;
        public string AlarmName { get; set; } = string.Empty;
        public int EventType { get; set; }
        public int Severity { get; set; }
        public string AlarmRule { get; set; } = string.Empty;
        public string AlarmInfo { get; set; } = string.Empty;
        public int AlarmStatus { get; set; }
        public DateTime LogTime { get; set; }
    }
}
