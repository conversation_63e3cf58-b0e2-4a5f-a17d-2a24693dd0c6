﻿using System;
using System.Collections.Generic;

namespace Siemens.PanelManager.SyncService.UdcModels
{
    public partial class ArchiveDataFormatBasicEnergy
    {
        public string ObjectId { get; set; } = null!;
        public long Archive { get; set; }
        public long TimestampInS { get; set; }
        public string? ActiveEnergyImportTariff1 { get; set; }
        public string? ActiveEnergyExportTariff1 { get; set; }
        public long CounterUniversal1 { get; set; }
        public long CounterUniversal2 { get; set; }
        public string? ActiveEnergyExportTariff2 { get; set; }
        public string? ActiveEnergyImportTariff2 { get; set; }
        public string? ReactiveEnergyExportTariff1 { get; set; }
        public string? ReactiveEnergyExportTariff2 { get; set; }
        public string? ReactiveEnergyImportTariff1 { get; set; }
        public string? ReactiveEnergyImportTariff2 { get; set; }
    }
}
