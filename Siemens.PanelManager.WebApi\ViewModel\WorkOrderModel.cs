﻿using Siemens.PanelManager.Model.Database.WorkOrder;
using Siemens.PanelManager.WebApi.StaticContent;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class WorkOrderModel
    {
        private MessageContext _messageContext;

        public int Id { get; set; }
        public string? WorkOrderCode { get; set; }
        public string? WorkOrderName { get; set; }
        public string? AssetType { get; set; }
        public string? Device { get; set; }
        public string? DeviceName { get; set; }
        public string? WorkOrderType { get; set; }
        public string? WorkOrderTypeName
        {
            get
            {
                return _messageContext.GetStaticModelName($"WORK_ORDER_TYPE_{WorkOrderType}");
            }
        }
        public DateTime ProcessingTime { get; set; }
        public DateTime ProcessingEndTime { get; set; }

        public string? Status { get; set; }
        public string? StatusName
        {
            get
            {
                return _messageContext.GetStaticModelName($"WORK_ORDER_STATUS_{Status}");
            }
        }
        public DateTime CreatedTime { get; set; }

        public List<WorkOrderContentModel>? Contents { get; set; } = new List<WorkOrderContentModel>();

        public WorkOrderModel(MessageContext messageContext) => _messageContext = messageContext;

        public WorkOrderModel(WorkOrderInfo workOrderInfo, MessageContext messageContext, List<FileInfoModel> fileInfoModels = null)
        {
            _messageContext = messageContext;
            Id = workOrderInfo.Id;
            WorkOrderCode = workOrderInfo.WorkOrderCode;
            WorkOrderName = workOrderInfo.WorkOrderName;
            WorkOrderType = workOrderInfo.WorkOrderType;
            AssetType = workOrderInfo.AssetType;
            Device = workOrderInfo.Device;
            DeviceName = workOrderInfo.DeviceName;
            CreatedTime = workOrderInfo.CreatedTime;
            ProcessingTime = workOrderInfo.ProcessingTime;
            ProcessingEndTime = workOrderInfo.ProcessingEndTime;
            Status = workOrderInfo.Status;

            if (workOrderInfo != null && workOrderInfo.Contents != null && workOrderInfo.Contents.Count != 0)
            {
                Contents = new List<WorkOrderContentModel>();

                WorkOrderContentModel workOrderContentModel;
                foreach (var item in workOrderInfo.Contents)
                {
                    workOrderContentModel = new();

                    workOrderContentModel.Id = item.Id;
                    workOrderContentModel.WorkOrderId = item.WorkOrderId;
                    workOrderContentModel.Content = item.Content;
                    workOrderContentModel.Measure = item.Measure;
                    workOrderContentModel.AddTime = item.AddTime;
                    workOrderContentModel.Tag = item.Tag;
                    workOrderContentModel.ReserveId = item.ReserveId;
                    workOrderContentModel.Attachment = Array.Empty<FileInfoModel>();

                    if (fileInfoModels != null && item.AttachmentIds != null && item.AttachmentIds.Length != 0)
                    {
                        workOrderContentModel.Attachment = fileInfoModels
                            .Where(a => item.AttachmentIds.ToList().Contains(a.Id))
                            .ToArray();
                    }

                    Contents.Add(workOrderContentModel);
                }
            }
        }
    }
}
