{"info": {"_postman_id": "3db0103e-e5ad-444b-8e67-000340ecdb81", "name": "05使用管理员账号进入panel manager智慧分析中的损耗分析菜单，切换当天or当月or当年or自定义（时间范围）查看对应损耗分析图表", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 6", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "获取单个资产的详情 Copy 5", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "let s1 = pm.response.json().items[0].id\r", "pm.environment.set(\"s1\", s1);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Asset/search?levels=Substation", "host": ["{{baseUrl}}"], "path": ["api", "v1", "<PERSON><PERSON>", "search"], "query": [{"key": "levels", "value": "Substation"}]}}, "response": []}, {"name": "获取损耗分析图表 Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"成功查看当天损耗分析图表\", function () {\r", "    pm.expect(pm.response.text()).to.include(\":00\");\r", "    pm.expect(pm.response.text()).to.include(\"x\");\r", "    pm.expect(pm.response.text()).to.include(\"y\");\r", "    pm.expect(pm.response.text()).to.include(\"Loss\");\r", "    pm.expect(pm.response.text()).to.include(\"Reference\");\r", "    pm.expect(pm.response.text()).to.include(\"KMS\");\r", "    pm.expect(pm.response.text()).to.include(\"%\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/LossDiagnosis/{{s1}}/GetChartData?dateType=0&chartCodes=LossDiagnosisForKMS&startDate=2023-04-20", "host": ["{{baseUrl}}"], "path": ["api", "v1", "LossDiagnosis", "{{s1}}", "GetChartData"], "query": [{"key": "dateType", "value": "0"}, {"key": "endDate", "value": "2023-04-20", "disabled": true}, {"key": "chartCodes", "value": "LossDiagnosisForKMS"}, {"key": "startDate", "value": "2023-04-20"}]}}, "response": []}, {"name": "获取损耗分析图表 Copy 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"成功查看当月损耗分析图表\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"-01\");\r", "    pm.expect(pm.response.text()).to.include(\"x\");\r", "    pm.expect(pm.response.text()).to.include(\"y\");\r", "    pm.expect(pm.response.text()).to.include(\"Loss\");\r", "    pm.expect(pm.response.text()).to.include(\"Reference\");\r", "    pm.expect(pm.response.text()).to.include(\"KMS\");\r", "    pm.expect(pm.response.text()).to.include(\"%\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/LossDiagnosis/{{s1}}/GetChartData?dateType=1&chartCodes=LossDiagnosisForKMS&startDate=2023-04", "host": ["{{baseUrl}}"], "path": ["api", "v1", "LossDiagnosis", "{{s1}}", "GetChartData"], "query": [{"key": "dateType", "value": "1"}, {"key": "endDate", "value": "2023-04-20", "disabled": true}, {"key": "chartCodes", "value": "LossDiagnosisForKMS"}, {"key": "startDate", "value": "2023-04"}]}}, "response": []}, {"name": "获取损耗分析图表 Copy 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"成功查看当年损耗分析图表\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"2023-01-01\");\r", "    pm.expect(pm.response.text()).to.include(\"x\");\r", "    pm.expect(pm.response.text()).to.include(\"y\");\r", "    pm.expect(pm.response.text()).to.include(\"Loss\");\r", "    pm.expect(pm.response.text()).to.include(\"Reference\");\r", "    pm.expect(pm.response.text()).to.include(\"KMS\");\r", "    pm.expect(pm.response.text()).to.include(\"%\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/LossDiagnosis/{{s1}}/GetChartData?dateType=2&chartCodes=LossDiagnosisForKMS&startDate=2023", "host": ["{{baseUrl}}"], "path": ["api", "v1", "LossDiagnosis", "{{s1}}", "GetChartData"], "query": [{"key": "dateType", "value": "2"}, {"key": "endDate", "value": "2023-04-20", "disabled": true}, {"key": "chartCodes", "value": "LossDiagnosisForKMS"}, {"key": "startDate", "value": "2023"}]}}, "response": []}, {"name": "获取损耗分析图表 Copy 3", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"成功查看自定义损耗分析图表\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"2023-04-01\");\r", "    pm.expect(pm.response.text()).to.include(\"2023-04-20\");\r", "    pm.expect(pm.response.text()).to.include(\"x\");\r", "    pm.expect(pm.response.text()).to.include(\"y\");\r", "    pm.expect(pm.response.text()).to.include(\"Loss\");\r", "    pm.expect(pm.response.text()).to.include(\"Reference\");\r", "    pm.expect(pm.response.text()).to.include(\"KMS\");\r", "    pm.expect(pm.response.text()).to.include(\"%\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/LossDiagnosis/{{s1}}/GetChartData?dateType=3&endDate=2023-04-20&chartCodes=LossDiagnosisForKMS&startDate=2023-04-01", "host": ["{{baseUrl}}"], "path": ["api", "v1", "LossDiagnosis", "{{s1}}", "GetChartData"], "query": [{"key": "dateType", "value": "3"}, {"key": "endDate", "value": "2023-04-20"}, {"key": "chartCodes", "value": "LossDiagnosisForKMS"}, {"key": "startDate", "value": "2023-04-01"}]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}]}