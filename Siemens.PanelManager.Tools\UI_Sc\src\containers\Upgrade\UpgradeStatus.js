import { useState } from "react";
import "./upgradeStatus.css";
import { useNavigate } from "react-router-dom";
import { packageUpload } from "../../api/upgradeApi";
import { IxTile, IxUpload } from "@siemens/ix-react";

export default function UpgradeStatus(props) {
  const [uploadState, setUploadState] = useState("SELECT_FILE");
  const navigate = useNavigate();
  const fileUpload = (e) => {
    console.log(e);
    setUploadState("LOADING");
    packageUpload(
      e.detail[0],
      (r) => {
        console.log(r);
        setUploadState("UPLOAD_SUCCESSED");
        navigate(0);
      },
      () => {
        setUploadState("UPLOAD_FAILED");
      }
    );
  };
  const uploadDisable = props.upgradeStatus === 1;
  let statusStr = "";
  if (props.upgradeStatus === 1) {
    statusStr = "升级正在进行中...";
  } else if (props.upgradeStatus === 2) {
    statusStr = "升级成功";
  } else if (props.upgradeStatus === 99) {
    statusStr = "升级失败";
  } else {
    statusStr = "升级未进行";
  }
  return (
    <div className="upgradeStatus-body">
      <IxTile size="medium" className="mr-1 upgradeStatus-tile">
        <div slot="header">升级状态</div>
        <div className="text-l">{statusStr}</div>
      </IxTile>
      {uploadDisable ? (
        <IxUpload
          className="upgradeStatus-upload"
          accept=".zip"
          disabled
          state={"LOADING"}
        ></IxUpload>
      ) : (
        <IxUpload
          className="upgradeStatus-upload"
          accept=".zip"
          state={uploadState}
          onFilesChanged={(e) => fileUpload(e)}
        ></IxUpload>
      )}
    </div>
  );
}
