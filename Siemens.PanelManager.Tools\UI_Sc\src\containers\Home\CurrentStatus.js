import React, { useState } from "react";
import { IxTile } from "@siemens/ix-react";
import "./currentStatus.css";
import { currentStatus } from "../../api/homeApi";

export default function CurrentStatus() {
  let [data, setData] = useState(null);
  if (data === null) {
    currentStatus(setData);
    data = { CPU: "N/A", mem: "N/A", runTime: "N/A" };
  } else {
    setTimeout(() => {
      currentStatus(setData);
    }, 10000);
  }
  return (
    <div className="currentStatus-body">
      <IxTile size="medium" className="mr-1 currentStatus-item">
        <div slot="header">CPU占用</div>
        <div className="text-l">{data?.CPU}</div>
      </IxTile>
      <IxTile size="medium" className="mr-1 currentStatus-item">
        <div slot="header">内存占用</div>
        <div className="text-l">{data?.mem}</div>
      </IxTile>
      <IxTile size="medium" className="mr-1 currentStatus-item">
        <div slot="header">运行时间</div>
        <div className="text-l">{data?.runTime}</div>
      </IxTile>
    </div>
  );
}
