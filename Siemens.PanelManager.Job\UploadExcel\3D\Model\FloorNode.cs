﻿using Siemens.PanelManager.Model.Topology3D;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.UploadExcel._3D.Model
{
    /// <summary>
    /// 地板的3D模型
    /// </summary>
    internal class FloorNode: NodeBase3D
    {
        public FloorNode()
            : base()
        {
            Name = "地面铁板";
            Size.Height = 50;
            Size.Depth = 1200;
        }

        public override string NodeType => "ironPlate";

        public void SetWidth(decimal width)
        {
            Size.Width = width;
        } 
    }
}
