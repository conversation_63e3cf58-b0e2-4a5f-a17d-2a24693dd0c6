import { IxMenu, IxMenuItem } from "@siemens/ix-react";
import "./Menu.css";
import React from "react";
import { useNavigate } from "react-router-dom";

function ClientMenu() {
  const navigate = useNavigate();
  return (
    <IxMenu enableToggleTheme="true">
      <IxMenuItem tab-icon="home" onClick={() => navigate("/home")}>
        首页
      </IxMenuItem>
      <IxMenuItem tab-icon="cloud-upload" onClick={() => navigate("/upload")}>
        升级
      </IxMenuItem>
      {/* <IxMenuItem tab-icon="history" onClick={() => navigate("/history")}>
        History
      </IxMenuItem> */}
      <IxMenuItem
        onClick={() => navigate("/settings")}
        slot="bottom"
        tabIcon="configuration"
      >
        配置
      </IxMenuItem>
    </IxMenu>
  );
}

export default ClientMenu;
