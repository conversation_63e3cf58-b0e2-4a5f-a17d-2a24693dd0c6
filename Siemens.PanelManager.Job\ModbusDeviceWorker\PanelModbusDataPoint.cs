﻿namespace Siemens.PanelManager.Job.ModbusDeviceWorker
{
    public class PanelModbusDataPoint
    {
        public string? TransformationType { get; set; }
        public string? FunctionCode { get; set; }
        public string? PropertyName { get; set; }
        public string? OriginalPropertyName { get; set; }
        public string? DescriptionInEnglish { get; set; }
        public string? DescriptionInGerman { get; set; }
        public string? Unit { get; set; }
        public float Factor { get; set; }
        public float Intercept { get; set; }
        public ushort Register { get; set; }
        public string? ParseMode { get; set; }
        public ushort RegisterCount { get; set; }
    }
}
