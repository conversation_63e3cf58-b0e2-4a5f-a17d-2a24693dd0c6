[{"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "VoltageL-N", "ParentName": "", "LanguageKey": "VoltageL-N", "Sort": 1}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "InstantaneousValuesL-N", "ParentName": "VoltageL-N", "LanguageKey": "InstantaneousValuesL-N", "Sort": 2}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "InstantaneousValuesL-N_ActualInstantaneousMeasurementValues", "ParentName": "InstantaneousValuesL-N", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 3}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "InstantaneousValuesL-N_GreatestMeasuredValues", "ParentName": "InstantaneousValuesL-N", "LanguageKey": "GreatestMeasuredValues", "Sort": 4}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "InstantaneousValuesL-N_LowestMeasuredValues", "ParentName": "InstantaneousValuesL-N", "LanguageKey": "LowestMeasuredValues", "Sort": 5}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "VoltageL-L", "ParentName": "", "LanguageKey": "VoltageL-L", "Sort": 6}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "InstantaneousValuesL-L", "ParentName": "VoltageL-L", "LanguageKey": "InstantaneousValuesL-L", "Sort": 7}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "InstantaneousValuesL-L_ActualInstantaneousMeasurementValues", "ParentName": "InstantaneousValuesL-L", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 8}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "InstantaneousValuesL-L_GreatestMeasuredValues", "ParentName": "InstantaneousValuesL-L", "LanguageKey": "GreatestMeasuredValues", "Sort": 9}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "InstantaneousValuesL-L_LowestMeasuredValues", "ParentName": "InstantaneousValuesL-L", "LanguageKey": "LowestMeasuredValues", "Sort": 10}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "Current", "ParentName": "", "LanguageKey": "Current", "Sort": 11}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "RMS", "ParentName": "Current", "LanguageKey": "RMS", "Sort": 12}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "RMS_ActualInstantaneousMeasurementValues", "ParentName": "RMS", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 13}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "GreatestMeasuredValues", "ParentName": "RMS", "LanguageKey": "GreatestMeasuredValues", "Sort": 14}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "RMS_LowestMeasuredValues", "ParentName": "RMS", "LanguageKey": "LowestMeasuredValues", "Sort": 15}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "Power", "ParentName": "", "LanguageKey": "Power", "Sort": 16}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "ActivePower", "ParentName": "Power", "LanguageKey": "ActivePower", "Sort": 17}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "ActivePower_InstantaneousValues", "ParentName": "ActivePower", "LanguageKey": "ActivePower_InstantaneousValues", "Sort": 18}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "ActivePower_ActualInstantaneousMeasurementValues", "ParentName": "ActivePower_InstantaneousValues", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 19}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "ActivePower_GreatestMeasuredValues", "ParentName": "ActivePower_InstantaneousValues", "LanguageKey": "GreatestMeasuredValues", "Sort": 20}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "ActivePower_LowestMeasuredValues", "ParentName": "ActivePower_InstantaneousValues", "LanguageKey": "LowestMeasuredValues", "Sort": 21}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "ReactivePower", "ParentName": "Power", "LanguageKey": "ReactivePower", "Sort": 22}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "MeasuringMethodVAR1", "ParentName": "ReactivePower", "LanguageKey": "MeasuringMethodVAR1", "Sort": 23}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "ReactivePower_InstantaneousValues", "ParentName": "MeasuringMethodVAR1", "LanguageKey": "ReactivePower_InstantaneousValues", "Sort": 24}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "MeasuringMethodVAR1_ActualInstantaneousMeasurementValues", "ParentName": "ReactivePower_InstantaneousValues", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 25}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "MeasuringMethodVAR1_GreatestMeasuredValues", "ParentName": "ReactivePower_InstantaneousValues", "LanguageKey": "GreatestMeasuredValues", "Sort": 26}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "ReactivePower_LowestMeasuredValues", "ParentName": "ReactivePower_InstantaneousValues", "LanguageKey": "LowestMeasuredValues", "Sort": 27}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "PowerFactor", "ParentName": "Power", "LanguageKey": "PowerFactor", "Sort": 28}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "PowerFactor_ActualInstantaneousMeasurementValues", "ParentName": "PowerFactor", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 29}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "PowerFactor_GreatestMeasuredValues", "ParentName": "PowerFactor", "LanguageKey": "GreatestMeasuredValues", "Sort": 30}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "PowerFactor_LowestMeasuredValues", "ParentName": "PowerFactor", "LanguageKey": "LowestMeasuredValues", "Sort": 31}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "Energy", "ParentName": "", "LanguageKey": "Energy", "Sort": 32}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "ActiveEnergy_Wh", "ParentName": "Energy", "LanguageKey": "ActiveEnergy_Wh", "Sort": 33}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "ReactiveEnergy_Varh", "ParentName": "Energy", "LanguageKey": "ReactiveEnergy_Varh", "Sort": 34}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "FrequencyValues", "ParentName": "", "LanguageKey": "FrequencyValues", "Sort": 34}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "InstantaneousValuesFrequency", "ParentName": "FrequencyValues", "LanguageKey": "InstantaneousValuesFrequency", "Sort": 35}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "InstantaneousValuesFrequency_ActualInstantaneousMeasurementValues", "ParentName": "InstantaneousValuesFrequency", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 36}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "InstantaneousValuesFrequency_GreatestMeasuredValues", "ParentName": "InstantaneousValuesFrequency", "LanguageKey": "GreatestMeasuredValues", "Sort": 37}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "InstantaneousValuesFrequency_LowestMeasuredValues", "ParentName": "InstantaneousValuesFrequency", "LanguageKey": "LowestMeasuredValues", "Sort": 38}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "GlobalDeviceDiagnostics", "ParentName": "", "LanguageKey": "GlobalDeviceDiagnostics", "Sort": 39}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "LocalDeviceStatus", "ParentName": "", "LanguageKey": "LocalDeviceStatus", "Sort": 40}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "GlobalDeviceStatus", "ParentName": "", "LanguageKey": "GlobalDeviceStatus", "Sort": 41}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "InputStatus", "ParentName": "", "LanguageKey": "InputStatus", "Sort": 42}, {"AssetLevel": 50, "AssetModel": "PAC1020", "Name": "OutputStatus", "ParentName": "", "LanguageKey": "OutputStatus", "Sort": 43}]