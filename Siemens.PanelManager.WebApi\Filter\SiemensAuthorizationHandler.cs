﻿using Microsoft.AspNetCore.Authorization;
using Newtonsoft.Json;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.WebApi.Models;
using Siemens.PanelManager.WebApi.StaticContent;

namespace Siemens.PanelManager.WebApi.Filter
{
    public class SiemensAuthorizationHandler : AuthorizationHandler<SiemensRequirement>
    {
        private readonly SiemensCache _cache;
        public SiemensAuthorizationHandler(SiemensCache cache)
        {
            _cache = cache;
        }

        protected override Task HandleRequirementAsync
            (AuthorizationHandlerContext context,
            SiemensRequirement requirement)
        {
            var userId = context.User.GetSessionId();
            if (userId == null)
            {
                context.Fail();
                return Task.CompletedTask;
            }

            if (Permission.Default.Equals(requirement.Name))
            {
                if (!_cache.Contains($"UserSession:{userId}"))
                {
                    context.Fail();
                    return Task.CompletedTask;
                }

                context.Succeed(requirement);
                return Task.CompletedTask;
            }

            if (requirement.IsSyncDeviceInfo)
            {
                var value = context.User.GetClaimValue("SyncDevice");

                if (value == null)
                {
                    context.Fail();
                    return Task.CompletedTask;
                }

                var deviceAccessList = JsonConvert.DeserializeObject<string[]>(value);
                if (deviceAccessList == null || !deviceAccessList.Contains(requirement.Name))
                {
                    context.Fail();
                    return Task.CompletedTask;
                }

                context.Succeed(requirement);
                return Task.CompletedTask;
            }

            var userSession = _cache.Get<UserSessionInfo>($"UserSession:{userId}");
            if (userSession == null)
            {


                context.Fail();
                return Task.CompletedTask;
            }

            if (!userSession.Pages.Contains(requirement.Name))
            {
                context.Fail();
                return Task.CompletedTask;
            }
            context.Succeed(requirement);
            return Task.CompletedTask;
        }
    }
}
