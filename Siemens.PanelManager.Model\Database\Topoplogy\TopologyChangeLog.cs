﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Topology
{
    [SugarTable("topoplogy_data_change")]
    public class TopologyDataChange : LogicDataBase
    {
        /// <summary>
        /// 时间戳
        /// </summary>
        [SugarColumn(ColumnName = "timestamp", IsPrimaryKey = true)]
        public long Timestamp { get; set; }
        [SugarColumn(ColumnName = "topology_id", IsPrimaryKey = true)]
        public int TopologyId { get; set; }
        [SugarColumn(ColumnName = "data_change", ColumnDataType = "varchar(10240)", IsNullable = false)]
        public string DataChange { get; set; } = string.Empty;
    }
}
