﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.WorkOrder
{
    [SugarTable("maintenance_report_snapshot")]
    public class MaintenanceReportSnapshot : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [SugarColumn(ColumnName = "generated_time", IsNullable = true)]
        public DateTime GeneratedTime { get; set; }

        [SugarColumn(ColumnName = "report_name", IsNullable = true, Length = 256)]
        public string ReportName { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "customer_name", IsNullable = true, Length = 256)]
        public string CustomerName { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "substation_name", IsNullable = true, Length = 256)]
        public string SubstationName { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "indicator_overview", IsNullable = true, Length = 256)]
        public string IndicatorOverview { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "station_exception_list", IsNullable = true, Length = 512)]
        public string StationExceptionList { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "cabinet_exception_list", IsNullable = true, Length = 2048)]
        public string CabinetExceptionList { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "circuit_exception_list", IsNullable = true, Length = 2048)]
        public string CircuitExceptionList { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "device_exception_list", IsNullable = true, Length = 2048)]
        public string DeviceExceptionList { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "last_alarm_id", IsNullable = true)]
        public long? LastAlarmId { get; set; }

        [SugarColumn(ColumnName = "alarm_overview", IsNullable = true, Length = 256)]
        public string AlarmOverview { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "replacement_part_chart", IsNullable = true, Length = 256)]
        public string ReplacementPartChart { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "panel_health_chart", IsNullable = true, Length = 256)]
        public string PanelHealthChart { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "breaker_health_chart", IsNullable = true, Length = 256)]
        public string BreakerHealthChart { get; set; } = string.Empty;
    }

}
