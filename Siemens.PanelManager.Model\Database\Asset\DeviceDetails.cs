﻿using SqlSugar;

namespace Siemens.PanelManager.Model.Database.Asset
{
    [SugarTable("asset_device_details")]
    public class DeviceDetails : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "asset_id", IsNullable = false)]
        public int AssetId { get; set; }
        /// <summary>
        /// 额定电流
        /// </summary>
        [SugarColumn(ColumnName = "rated_current", IsNullable = true)]
        public decimal? RatedCurrent { get; set; }
        /// <summary>
        /// 额定电压
        /// </summary>
        [SugarColumn(ColumnName = "rated_voltage", IsNullable = true)]
        public decimal? RatedVoltage { get; set; }
        /// <summary>
        /// 额定功耗
        /// </summary>
        [SugarColumn(ColumnName = "rated_power", IsNullable = true)]
        public decimal? RatedPower { get; set; }
        /// <summary>
        /// 运行小时数
        /// </summary>
        [SugarColumn(ColumnName = "operating_hours", IsNullable = true)]
        public decimal? OperatingHours { get; set; }
        /// <summary>
        /// 当前费率
        /// </summary>
        [SugarColumn(ColumnName = "actual_tariff", IsNullable = true)]
        public string? ActualTariff { get; set; }
        /// <summary>
        /// 硬件写保护
        /// </summary>
        [SugarColumn(ColumnName = "harware_write_protection", IsNullable = true, Length = 50)]
        public string? HarwareWriteProtectionStatus { get; set; }
        /// <summary>
        /// 插槽 1 是否激活
        /// </summary>
        [SugarColumn(ColumnName = "slot_1", IsNullable = true)]
        public int? Slot_1 { get; set; }
        /// <summary>
        /// 插槽 2 是否激活
        /// </summary>
        [SugarColumn(ColumnName = "slot_2", IsNullable = true)]
        public int? Slot_2 { get; set; }
        /// <summary>
        /// 插槽 3 是否激活
        /// </summary>
        [SugarColumn(ColumnName = "slot_3", IsNullable = true)]
        public int? Slot_3 { get; set; }
        /// <summary>
        /// 插槽 4 是否激活
        /// </summary>
        [SugarColumn(ColumnName = "slot_4", IsNullable = true)]
        public int? Slot_4 { get; set; }
        /// <summary>
        /// 是否使用电压互感器
        /// </summary>
        [SugarColumn(ColumnName = "use_voltage_transformer", IsNullable = true)]
        public bool? UseVoltageTransformer { get; set; }

        /// <summary>
        /// MLFB
        /// </summary>
        [SugarColumn(ColumnName = "mlfb", IsNullable = true, Length = 50)]
        public string MLFB { get; set; } = string.Empty;
        /// <summary>
        /// 断路器订单号
        /// </summary>
        [SugarColumn(ColumnName = "breaker_order_no", IsNullable = true, Length = 50)]
        public string? BreakerOrderNo { get; set; }
        /// <summary>
        /// 设备序列号
        /// </summary>
        [SugarColumn(ColumnName = "serial_number", IsNullable = true, Length = 50)]
        public string? SerialNumber { get; set; }
        /// <summary>
        /// 设备识别号
        /// </summary>
        [SugarColumn(ColumnName = "ident_number", IsNullable = true, Length = 50)]
        public string? IdentNumber { get; set; }
        /// <summary>
        /// 固件版本号 1
        /// </summary>
        [SugarColumn(ColumnName = "firmware_revision_1", IsNullable = true, Length = 50)]
        public string? FirmwareRevision_1 { get; set; }
        /// <summary>
        /// 固件版本号 2
        /// </summary>
        [SugarColumn(ColumnName = "firmware_revision_2", IsNullable = true, Length = 50)]
        public string? FirmwareRevision_2 { get; set; }
        /// <summary>
        /// 固件版本号 3
        /// </summary>
        [SugarColumn(ColumnName = "firmware_revision_3", IsNullable = true, Length = 50)]
        public string? FirmwareRevision_3 { get; set; }
        /// <summary>
        /// 固件版本号 4
        /// </summary>
        [SugarColumn(ColumnName = "firmware_revision_4", IsNullable = true, Length = 50)]
        public string? FirmwareRevision_4 { get; set; }
        /// <summary>
        /// 固件版本号 5
        /// </summary>
        [SugarColumn(ColumnName = "firmware_revision_5", IsNullable = true, Length = 50)]
        public string? FirmwareRevision_5 { get; set; }
        /// <summary>
        /// 总脱扣数
        /// </summary>
        [SugarColumn(ColumnName = "all_trips", IsNullable = true)]
        public int? AllTrips { get; set; }
        /// <summary>
        /// 过载保护 脱扣次数
        /// </summary>
        [SugarColumn(ColumnName = "lt_trips", IsNullable = true)]
        public int? LTTrips { get; set; }
        /// <summary>
        /// 短时间延迟短路保护 脱扣次数
        /// </summary>
        [SugarColumn(ColumnName = "st_trips", IsNullable = true)]
        public int? STTrips { get; set; }
        /// <summary>
        /// 瞬时保护 脱扣次数
        /// </summary>
        [SugarColumn(ColumnName = "inst_trips", IsNullable = true)]
        public int? INSTTrips { get; set; }
        /// <summary>
        /// 接地保护 脱扣次数
        /// </summary>
        [SugarColumn(ColumnName = "gf_trips", IsNullable = true)]
        public int? GFTrips { get; set; }
        /// <summary>
        /// 中性线 (N) 过载保护 脱扣次数
        /// </summary>
        [SugarColumn(ColumnName = "n_trips", IsNullable = true)]
        public int? NTrips { get; set; }
        /// <summary>
        /// 反向功率保护 脱扣次数
        /// </summary>
        [SugarColumn(ColumnName = "rp_trips", IsNullable = true)]
        public int? RPTrips { get; set; }
        /// <summary>
        /// 机械操作循环次数
        /// 不带载
        /// </summary>
        [SugarColumn(ColumnName = "mechanical_switch_cycles", IsNullable = true)]
        public int? MechanicalSwitchCycles { get; set; }
        /// <summary>
        /// 电气操作循环次数
        /// 带载
        /// </summary>
        [SugarColumn(ColumnName = "electrical_switch_cycles", IsNullable = true)]
        public int? ElectricalSwitchCycles { get; set; }
        /// <summary>
        /// 机械操作循环次数（样本值）
        /// 不带载
        /// </summary>
        [SugarColumn(ColumnName = "mechanical_switch_sample_cycles", IsNullable = true)]
        public int? MechanicalSwitchSampleCycles { get; set; }
        /// <summary>
        /// 电气操作循环次数（样本值）
        /// 带载
        /// </summary>
        [SugarColumn(ColumnName = "electrical_switch_sample_cycles", IsNullable = true)]
        public int? ElectricalSwitchSampleCycles { get; set; }
        [SugarColumn(ColumnName = "icw", IsNullable = true)]
        public decimal? Icw { get; set; }
        /// <summary>
        /// 一次侧电压
        /// </summary>
        [SugarColumn(ColumnName = "primary_voltage", IsNullable = true)]
        public decimal? PrimaryVoltage { get; set; }
        /// <summary>
        /// 二次侧电压
        /// </summary>
        [SugarColumn(ColumnName = "secondary_voltage", IsNullable = true)]
        public decimal? SecondaryVoltage { get; set; }

        /// <summary>
        /// 一次侧电流
        /// </summary>
        [SugarColumn(ColumnName = "primary_current", IsNullable = true)]
        public decimal? PrimaryCurrent { get; set; }
        /// <summary>
        /// 二次侧电流
        /// </summary>
        [SugarColumn(ColumnName = "secondary_current", IsNullable = true)]
        public decimal? SecondaryCurrent { get; set; }

        /// <summary>
        /// 主触头状态
        /// </summary>
        [SugarColumn(ColumnName = "main_contant_status", IsNullable = true, Length = 50)]
        public string? MainContantStatus { get; set; }

        /// <summary>
        /// 健康评分
        /// </summary>
        [SugarColumn(ColumnName = "health_score", IsNullable = true, Length = 50)]
        public string? HealthScore { get; set; }

        /// <summary>
        /// 剩余寿命
        /// </summary>
        [SugarColumn(ColumnName = "remaining_life", IsNullable = true, Length = 50)]
        public string? RemainingLife { get; set; }

        /// <summary>
        /// 断路器温度
        /// </summary>
        [SugarColumn(ColumnName = "breaker_temperature", IsNullable = true, Length = 50)]
        public string? BreakerTemp { get; set; }

        /// <summary>
        /// 隔室温度
        /// </summary>
        [SugarColumn(ColumnName = "com_temperature", IsNullable = true, Length = 50)]
        public string? ComTemp { get; set; }

        /// <summary>
        /// 健康级别
        /// normal
        /// attention
        /// maintain
        /// rushRepair
        /// </summary>
        [SugarColumn(ColumnName = "health_level", IsNullable = true, Length = 50)]
        public string? HealthLevel { get; set; }

        /// <summary>
        /// 触头磨损率
        /// </summary>
        [SugarColumn(ColumnName = "contact_wear_rate", IsNullable = true)]
        public double? ContactWearRate { get; set; }

        #region Sum I²t
        /// <summary>
        /// Sum I²t L1
        /// </summary>
        [SugarColumn(ColumnName = "sumi2t_a", IsNullable = true, Length = 50)]
        public decimal? SumI2t_A { get; set; }

        /// <summary>
        /// Sum I²t L2
        /// </summary>
        [SugarColumn(ColumnName = "sumi2t_b", IsNullable = true, Length = 50)]
        public decimal? SumI2t_B { get; set; }

        /// <summary>
        /// Sum I²t L3
        /// </summary>
        [SugarColumn(ColumnName = "sumi2t_c", IsNullable = true, Length = 50)]
        public decimal? SumI2t_C { get; set; }

        /// <summary>
        /// Sum I²t Ln
        /// </summary>
        [SugarColumn(ColumnName = "sumi2t_n", IsNullable = true, Length = 50)]
        public decimal? SumI2t_N { get; set; }
        #endregion

        /// <summary>
        /// 电表位置
        /// </summary>
        [SugarColumn(ColumnName = "meter_site", IsNullable = true)]
        public MeterSite? MeterSite { get; set; }
    }

    public enum MeterSite : int
    {
        /// <summary>
        /// 线路侧
        /// </summary>
        LineSide = 0,

        /// <summary>
        /// 母线侧
        /// </summary>
        BusbarSide = 10
    }
}
