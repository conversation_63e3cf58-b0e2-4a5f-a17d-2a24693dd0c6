﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using System.Xml.Linq;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class EnergyExportConsumptionParam
    {
        [FromQuery(Name = "startDate")]
        public string? StartDate { get; set; }

        [FromQuery(Name = "endDate")]

        public string? EndDate { get; set; }
        [FromQuery(Name = "loop")]
        public string? Loop { get; set; }

        [FromQuery(Name = "device")]
        public string? Device { get; set; }

        [BindNever]
        public int CircuitId
        {
            get
            {
                try
                {
                    if (string.IsNullOrWhiteSpace(Loop) || Loop.ToUpper() == "ALL")
                    {
                        return 0;
                    }

                    if (int.TryParse(Loop, out var tempValue))
                    {
                        return tempValue;
                    }

                    return 0;
                }
                catch
                {
                    return 0;
                }
            }
        }

        [BindNever]
        public int AssetId
        {
            get
            {
                try
                {
                    if (string.IsNullOrWhiteSpace(Device) || Device.ToUpper() == "ALL")
                    {
                        return 0;
                    }

                    if (int.TryParse(Device, out var tempValue))
                    {
                        return tempValue;
                    }

                    return 0;
                }
                catch
                {
                    return 0;
                }
            }
        }
    }
}
