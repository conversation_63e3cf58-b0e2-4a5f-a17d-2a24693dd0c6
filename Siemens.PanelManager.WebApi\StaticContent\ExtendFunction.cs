﻿using Newtonsoft.Json.Linq;
using Siemens.PanelManager.Common;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Model.Chart;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.Auth;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.Model.Database.Topology;
using Siemens.PanelManager.WebApi.Controllers;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.WebApi.StaticContent
{
    static class ExtendFunction
    {  


        /// <summary>
        /// 
        /// </summary>
        /// <param name="assetInfo"></param>
        /// <param name="assetQuery"></param>
        /// <param name="relations"></param>
        /// <param name="topoplogyQuery"></param>
        /// <param name="connetStatus"></param>
        /// <param name="fileQuery"></param>
        /// <param name="IsConnected">是否连接</param>
        /// <returns></returns>
        public static AssetInfoModel GetAssetInfoModel(this AssetInfo assetInfo,
            ISugarQueryable<AssetInfo> assetQuery,
            List<AssetRelation>? relations,
            ISugarQueryable<TopologyInfo> topoplogyQuery,
            Dictionary<int, bool>? connetStatus,
            ISugarQueryable<FileManager>? fileQuery = null, bool IsConnected = true)
        {
            AssetInfo[] assetInfos;
            if (relations == null || relations.Count == 0)
            {
                assetInfos = new AssetInfo[0];
            }
            else
            {
                List<int> assetIds = new List<int>(relations.Count);
                foreach (var r in relations)
                {
                    assetIds.Add(r.ChildId);
                }
                assetInfos = assetQuery.Where(a => assetIds.Contains(a.Id)).ToArray();
            }
            AssetInfoModel[] children;
            AssetInfoModel assetModel;
            var tq = topoplogyQuery.Clone();
            if (fileQuery == null)
            {
                var hasConnected = string.Empty;
                if (connetStatus != null && IsConnected && connetStatus.TryGetValue(assetInfo.Id, out bool hasConnect))
                {
                    hasConnected = hasConnect ? "1" : "0";
                }
                assetModel = new AssetInfoModel(assetInfo, tq, hasConnected);
                children = assetInfos.Select(a =>
                {
                    var subConnected = string.Empty;
                    var newTq = topoplogyQuery.Clone();
                    if (connetStatus != null && IsConnected && connetStatus.TryGetValue(a.Id, out bool hasConnect))
                    {
                        subConnected = hasConnect ? "1" : "0";
                    }
                    return new AssetInfoModel(a, newTq, subConnected);
                }).ToArray();
            }
            else
            {
                var fq = fileQuery.Clone();
                var hasConnected = string.Empty;
                if (connetStatus != null && IsConnected && connetStatus.TryGetValue(assetInfo.Id, out bool hasConnect))
                {
                    hasConnected = hasConnect ? "1" : "0";
                }
                assetModel = new AssetInfoModel(assetInfo, fq, tq, hasConnected);
                children = assetInfos.Select(a =>
                {
                    var newFq = fileQuery.Clone();
                    var newTq = topoplogyQuery.Clone();
                    var subConnected = string.Empty;
                    if (connetStatus != null && IsConnected && connetStatus.TryGetValue(a.Id, out bool hasConnect))
                    {
                        subConnected = hasConnect ? "1" : "0";
                    }
                    return new AssetInfoModel(a, newFq, newTq, subConnected);
                }).ToArray();
            }

            if (relations != null && relations.Count > 0)
            {
                var parentId = assetInfo.Id;
                foreach (var relation in relations)
                {
                    var childAsset = children.FirstOrDefault(a => a.Id == relation.ChildId);
                    if (childAsset == null) continue;

                    if (relation.ParentId == assetInfo.Id)
                    {
                        if (assetModel.Children == null)
                        {
                            assetModel.Children = new List<AssetInfoModel>();
                        }
                        assetModel.Children.Add(childAsset);
                    }
                    else
                    {
                        var parent = children.FirstOrDefault(a => a.Id == relation.ParentId);
                        if (parent != null)
                        {
                            if (parent.Children == null)
                            {
                                parent.Children = new List<AssetInfoModel>();
                            }
                            parent.Children.Add(childAsset);
                        }
                    }
                }
            }

            #region 排序
            {
                SortAssetes(assetModel);
            }
            #endregion

            return assetModel;
        }

        private static void SortAssetes(AssetInfoModel asset)
        {
            if (asset.Children == null || asset.Children.Count == 0)
            {
                return;
            }

            var children = asset.Children.OrderBy(c => c.SortNo)
                .OrderByDescending(c => c.AssetLevel)
                .ToList();

            asset.Children = children;

            foreach (var c in asset.Children)
            {
                SortAssetes(c);
            }
        }

        public static bool IsGeneralEquipment(this AssetInfo asset)
        {
            return "Other".Equals(asset.AssetModel , StringComparison.OrdinalIgnoreCase)
                || "GeneralDevice".Equals(asset.AssetType, StringComparison.OrdinalIgnoreCase)
                || ("Modbus".Equals(asset.AssetModel, StringComparison.OrdinalIgnoreCase)
                && "Gateway".Equals(asset.AssetType, StringComparison.OrdinalIgnoreCase));
        }

        public static async Task<List<IChart>> GetChartsBySql(this AssetDashboardConfig[] configs,
            ISqlSugarClient client,
            MessageContext messageContext)
        {
            var charts = new List<IChart>();
            foreach (var config in configs)
            {
                var chart = await config.GetChartBySql(client, messageContext);
                if (chart != null)
                {
                    charts.Add(chart);
                }
            }
            return charts;
        }

        public static async Task<IChart?> GetChartBySql(this AssetDashboardConfig config,
            ISqlSugarClient client,
            MessageContext messageContext)
        {
            if (string.IsNullOrEmpty(config.Sql)) return null;
            JObject extendObj;
            if (string.IsNullOrEmpty(config.Extend))
            {
                extendObj = new JObject();
            }
            else
            {
                try
                {
                    extendObj = JObject.Parse(config.Extend);
                }
                catch
                {
                    extendObj = new JObject();
                }
            }
            switch (config.DashboardType)
            {
                case "Line":
                    {
                        var lineList = await client.Ado.SqlQueryAsync<LineChartInfo>(config.Sql);

                        JToken? value;
                        var staticX = new string[0];
                        if (extendObj.TryGetValue("X", out value) && value != null)
                        {
                            try
                            {
                                staticX = value.Values<string>().Where(v => !string.IsNullOrEmpty(v)).OfType<string>().ToArray() ?? staticX;
                            }
                            catch
                            {

                            }
                        }

                        var count = lineList.Count >= staticX.Length ? lineList.Count : staticX.Length;

                        var xList = new string[count];
                        var y1List = new decimal[count];
                        var y2List = new decimal[count];
                        var y3List = new decimal[count];
                        var y4List = new decimal[count];
                        var y5List = new decimal[count];
                        var y6List = new decimal[count];
                        var y7List = new decimal[count];
                        var y8List = new decimal[count];
                        var y9List = new decimal[count];
                        var xTitleList = new string?[count];
                        var lineChart = new LineChartModel();
                        lineChart.X = xList;
                        lineChart.Y1 = y1List;


                        if (extendObj.TryGetValue("Standard", out value) && value != null)
                        {
                            try
                            {
                                lineChart.Standard = value.Value<decimal>();
                            }
                            catch
                            {

                            }
                        }

                        if (extendObj.TryGetValue("XColumn", out value) && value != null)
                        {
                            try
                            {
                                lineChart.XColumn = value.Value<string>();
                            }
                            catch
                            {

                            }
                        }

                        if (extendObj.TryGetValue("YColumns", out value) && value != null)
                        {
                            try
                            {
                                lineChart.YColumns = value.Values<string>().Where(t => !string.IsNullOrEmpty(t)).OfType<string>().Select(y => messageContext.GetOverviewValue(y)).ToList();
                                if (lineChart.YColumns.Count >= 2)
                                {
                                    lineChart.Y2 = y2List;
                                }

                                if (lineChart.YColumns.Count >= 3)
                                {
                                    lineChart.Y3 = y3List;
                                }
                                if (lineChart.YColumns.Count >= 4)
                                {
                                    lineChart.Y4 = y4List;
                                }
                                if (lineChart.YColumns.Count >= 5)
                                {
                                    lineChart.Y5 = y5List;
                                }
                                if (lineChart.YColumns.Count >= 6)
                                {
                                    lineChart.Y6 = y6List;
                                }
                                if (lineChart.YColumns.Count >= 7)
                                {
                                    lineChart.Y7 = y7List;
                                }
                                if (lineChart.YColumns.Count >= 8)
                                {
                                    lineChart.Y8 = y8List;
                                }
                                if (lineChart.YColumns.Count >= 9)
                                {
                                    lineChart.Y9 = y9List;
                                }
                            }
                            catch
                            {

                            }
                        }

                        Func<string, string> typeNameMappings = (s) => s;
                        if (extendObj.TryGetValue("TypeNameMappings", out value) && value != null && value is JObject valueObj)
                        {
                            try
                            {
                                if (valueObj.ContainsKey("StaticKey"))
                                {
                                    if (valueObj.TryGetValue("StaticKey", out var staticKeyToken) && staticKeyToken != null)
                                    {
                                        var staticKey = staticKeyToken.Value<string>();
                                        typeNameMappings = (s) => messageContext.GetString($"{staticKey}_{s}") ?? s;
                                    }
                                }
                                else
                                {
                                    var dicObj = valueObj.ToObject<Dictionary<string, string>>();

                                    if (dicObj != null)
                                    {
                                        typeNameMappings = (s) => messageContext.GetString(dicObj[s]) ?? s;
                                    }
                                }
                            }
                            catch
                            {

                            }
                        }

                        int i = 0;
                        for (; i < lineList.Count; i++)
                        {
                            var lineInfo = lineList[i];
                            if (i == 0)
                            {
                                if (lineInfo.Y2.HasValue)
                                {
                                    lineChart.Y2 = y2List;
                                }
                                if (lineInfo.Y3.HasValue)
                                {
                                    lineChart.Y3 = y3List;
                                }
                                if (lineInfo.Y4.HasValue)
                                {
                                    lineChart.Y4 = y4List;
                                }
                                if (lineInfo.Y5.HasValue)
                                {
                                    lineChart.Y5 = y5List;
                                }
                                if (lineInfo.Y6.HasValue)
                                {
                                    lineChart.Y6 = y6List;
                                }
                                if (lineInfo.Y7.HasValue)
                                {
                                    lineChart.Y7 = y7List;
                                }
                                if (lineInfo.Y8.HasValue)
                                {
                                    lineChart.Y8 = y8List;
                                }
                                if (lineInfo.Y9.HasValue)
                                {
                                    lineChart.Y9 = y9List;
                                }
                            }
                            xList[i] = typeNameMappings(lineInfo.X ?? string.Empty);
                            y1List[i] = lineInfo.Y1 ?? 0m;
                            y2List[i] = lineInfo.Y2 ?? 0m;
                            y3List[i] = lineInfo.Y3 ?? 0m;
                            y4List[i] = lineInfo.Y4 ?? 0m;
                            y5List[i] = lineInfo.Y5 ?? 0m;
                            y6List[i] = lineInfo.Y6 ?? 0m;
                            y7List[i] = lineInfo.Y7 ?? 0m;
                            y8List[i] = lineInfo.Y8 ?? 0m;
                            y9List[i] = lineInfo.Y9 ?? 0m;
                            xTitleList[i] = lineInfo.XTitles;
                        }

                        if (staticX.Length > 0)
                        {
                            foreach (var item in staticX)
                            {
                                if (string.IsNullOrEmpty(item)) continue;
                                var x = item;

                                x = typeNameMappings(x);
                                if (!xList.Contains(x))
                                {
                                    xList[i] = x;
                                    y1List[i] = 0m;
                                    y2List[i] = 0m;
                                    y3List[i] = 0m;
                                    y4List[i] = 0m;
                                    y5List[i] = 0m;
                                    y6List[i] = 0m;
                                    y7List[i] = 0m;
                                    y8List[i] = 0m;
                                    y9List[i] = 0m;
                                    i++;
                                }
                            }
                        }

                        var needAdd = false;
                        foreach (var x in xTitleList)
                        {
                            if (!string.IsNullOrEmpty(x))
                            {
                                needAdd = true;
                                break;
                            }
                        }

                        if (needAdd)
                        {
                            lineChart.XTitles = xTitleList;
                        }
                        return lineChart;
                    }
                case "Pie":
                    {
                        var pieList = await client.Ado.SqlQueryAsync<PieInfo>(config.Sql);
                        var pieChart = new PieChartModel();
                        JToken? value;
                        if (extendObj.TryGetValue("TypeList", out value) && value != null)
                        {
                            try
                            {
                                pieChart.TypeList = value.Values<string>().Where(t => !string.IsNullOrEmpty(t)).OfType<string>().ToArray();
                            }
                            catch
                            {

                            }
                        }
                        Func<string, string> typeNameMappings = (s) => s;
                        if (extendObj.TryGetValue("TypeNameMappings", out value) && value != null && value is JObject valueObj)
                        {
                            try
                            {
                                if (valueObj.ContainsKey("StaticKey"))
                                {
                                    if (valueObj.TryGetValue("StaticKey", out var staticKeyToken) && staticKeyToken != null)
                                    {
                                        var staticKey = staticKeyToken.ToString();
                                        typeNameMappings = (s) => messageContext.GetString($"{staticKey}_{s}") ?? $"{staticKey}_{s}";
                                    }
                                }
                                else
                                {
                                    var dicObj = valueObj.ToObject<Dictionary<string, string>>();

                                    if (dicObj != null)
                                    {
                                        typeNameMappings = (s) => messageContext.GetString(dicObj[s]) ?? s;
                                    }
                                }
                            }
                            catch
                            {

                            }
                        }
                        pieChart.Summary = pieList?.ToArray() ?? new PieInfo[0];
                        if (pieList == null) break;
                        var sum = pieList.Sum(t => t.Value.HasValue ? t.Value.Value : 0m);
                        decimal pSum = 0m;

                        for (var i = 0; i < pieList.Count; i++)
                        {
                            var pirInfo = pieList[i];
                            if (!pirInfo.Value.HasValue)
                            {
                                pirInfo.Value = 0m;
                                pirInfo.Percentage = 0m;
                                continue;
                            }
                            var p = Math.Round(pirInfo.Value.Value / sum, 4);

                            if (pSum + p > 1)
                            {
                                p = 1 - pSum;
                                pSum = 1;
                            }
                            else
                            {
                                if (i == pieList.Count - 1)
                                {
                                    p = 1 - pSum;
                                }
                                pSum += p;
                            }

                            pirInfo.Percentage = p * 100;
                            pirInfo.Code = pirInfo.Name ?? string.Empty;
                            pirInfo.Name = typeNameMappings(pirInfo.Name ?? string.Empty);

                        }
                        return pieChart;
                    }
                default: break;
            }
            return null;
        }


        public static string? GetSessionId(this ClaimsPrincipal user)
        {
            var identity = user.Identities.FirstOrDefault();
            if (identity != null)
            {
                return identity.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
            }
            return null;
        }

        public static string? GetClaimValue(this ClaimsPrincipal user, string type)
        {
            var identity = user.Identities.FirstOrDefault();
            if (identity != null)
            {
                return identity.Claims.FirstOrDefault(c => c.Type == type)?.Value;
            }
            return null;
        }

        public static string GetPasswordCode(this string password)
        {
            var md5 = MD5.Create();
            return Convert.ToBase64String(md5.ComputeHash(Encoding.UTF8.GetBytes($"Siemens-{password}")));
        }

        public static bool CheckUserInfo(this User user, StringBuilder message, SiemensApiControllerBase siemensApi)
        {
            if (user.LoginName.MustCharAndNotOnlyNumberOrSymbol(20, 4))
            {
                message.Append(siemensApi.MessageContext.InvaildUserNameFormat);
                return false;
            }

            if (user.UserName.MustCharAndNotOnlyNumberOrSymbol(20, 2) || Regex.IsMatch(user.UserName, "[\\$|#|!|@|\\^|\\*|\\(|\\)|\\||%|\\\\|/|<|>|;|\\[|\\]|\\{|\\}|\\?]+"))
            {
                message.Append(siemensApi.MessageContext.InvaildNameFormat);
                return false;
            }

            if (!string.IsNullOrEmpty(user.MobileNumber)
                && !Regex.IsMatch(user.MobileNumber, "^[\\d]{7,11}$"))
            {
                message.Append(siemensApi.MessageContext.InvaildMobileFormat);
                return false;
            }

            if (!string.IsNullOrEmpty(user.EmailAddress)
                && !Regex.IsMatch(user.EmailAddress, "^[\\w|\\.]+@$[\\w|\\.]+")
                && (user.EmailAddress.Length < 12
                || user.EmailAddress.Length > 30
                ))
            {
                message.Append(siemensApi.MessageContext.InvaildEmailFormat);
                return false;
            }

            return true;
        }

        public static bool CheckTopologyInfo(this TopologyInfo topoplogy, StringBuilder message, MessageContext messageContext)
        {
            if (topoplogy == null) return true;
            if (topoplogy.Name.MustCharAndNotOnlySymbol(51, 2))
            {
                message.AppendLine(messageContext.GetErrorValue("Topology_InvaildName"));
            }
            if (string.IsNullOrEmpty(topoplogy.Code))
            {
                message.AppendLine(messageContext.GetErrorValue("Topology_MissingCode"));
            }
            else if (topoplogy.Code.MustCharAndNotOnlySymbol(51, 2))
            {
                message.AppendLine(messageContext.GetErrorValue("Topology_InvaildCode"));
            }

            return message.Length > 0;
        }


        public static void InitYList(this Dictionary<int, decimal[]> data, int length, LineChartModel lineChart)
        {
            var max = data.Keys.Max();
            lineChart.Y1 = new decimal[length];
            if (max >= 2)
            {
                lineChart.Y2 = new decimal[length];
            }
            if (max >= 3)
            {
                lineChart.Y3 = new decimal[length];
            }
            if (max >= 4)
            {
                lineChart.Y4 = new decimal[length];
            }
            if (max >= 5)
            {
                lineChart.Y5 = new decimal[length];
            }
            if (max >= 6)
            {
                lineChart.Y6 = new decimal[length];
            }
            if (max >= 7)
            {
                lineChart.Y7 = new decimal[length];
            }
            if (max >= 8)
            {
                lineChart.Y8 = new decimal[length];
            }
            if (max >= 9)
            {
                lineChart.Y9 = new decimal[length];
            }
        }

        public static bool GetDateByDateType(this ChartDateType dateType, string startDateStr, string? endDateStr, ref DateTime startDate, ref DateTime endDate)
        {
            var isOk = true;
            switch (dateType)
            {
                case ChartDateType.Month:
                    {
                        if (startDateStr.Length >= 7)
                        {
                            var timeStr = startDateStr.Substring(0, 7);
                            var match = Regex.Match(timeStr, "^([\\d]{4})-([\\d]{2})$");
                            if (match.Success)
                            {
                                var year = int.Parse(match.Groups[1].Value);
                                var mouth = int.Parse(match.Groups[2].Value);
                                startDate = new DateTime(year, mouth, 1);
                                mouth++;
                                if (mouth > 12)
                                {
                                    year++;
                                    mouth = 1;
                                }
                                endDate = new DateTime(year, mouth, 1).AddDays(-1);
                                break;
                            }
                        }
                        isOk = false;
                        break;
                    }
                case ChartDateType.Year:
                    {
                        if (startDateStr.Length >= 4)
                        {
                            var timeStr = startDateStr.Substring(0, 4);
                            var match = Regex.Match(timeStr, "^([\\d]{4})$");
                            if (match.Success)
                            {
                                var year = int.Parse(match.Groups[1].Value);
                                startDate = new DateTime(year, 1, 1);
                                year++;
                                endDate = new DateTime(year, 1, 1).AddDays(-1);
                                break;
                            }
                        }
                        isOk = false;
                        break;
                    }
                case ChartDateType.Custom:
                    {
                        if (!(DateTime.TryParseExact(startDateStr, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out startDate)
                            && DateTime.TryParseExact(endDateStr, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out endDate)))
                        {
                            isOk = false;
                        }
                    }
                    break;
                default:
                    break;
            }

            return isOk;
        }

    }
}
