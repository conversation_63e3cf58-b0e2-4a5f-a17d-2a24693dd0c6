﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.DataFlow;
using Siemens.PanelManager.Server.DataPoint;
using SqlSugar;

namespace Siemens.PanelManager.Server.Asset
{
    public class DeviceInfoService
    {
        private static readonly Dictionary<string, Func<string, string>> _changeDataFuncs = new Dictionary<string, Func<string, string>>()
        {
            ["SlotFunc"] = SlotFunc
        };

        private static string SlotFunc(string arg)
        {
            const string Profibus = "7KM9300-0AB00-0AA0";
            const string ProfibusV40 = "7KM9300-0AB01-0AA0";
            const string Rs485 = "7KM9300-0AM00-0AA0";
            const string DiDo = "7KM9200-0AB00-0AA0";
            const string Profinet = "7KM9300-0AE00-0AA0";
            const string ProfinetV11 = "7KM9300-0AE01-0AA0";
            const string ProfinetV12 = "7KM9300-0AE02-0AA0";
            const string NeutralConductor = "7KM9200-0AD00-0AA0";

            switch (arg)
            {
                case null:
                case "":
                    return "0"; // PluggedModule_No
                case Rs485:
                    return "1"; // PluggedModule_PACRS485
                case Profibus:
                case ProfibusV40:
                    return "2"; // PluggedModule_PACPROFIBUSDB
                case DiDo:
                    return "3"; // PluggedModule_DIDO_4DI/2DO
                case Profinet:
                case ProfinetV11:
                case ProfinetV12:
                    return "4"; // PluggedModule_PROFINET
                case NeutralConductor:
                    return "5"; // PluggedModule_NCONDUCTOR
                default:
                    return "-1"; // Unknown
            }
        }

        private IServiceProvider _provider;
        public DeviceInfoService(IServiceProvider provider)
        {
            _provider = provider;
        }

        public async Task UpdateDeviceInfo(Dictionary<string, string> deviceInfo, AssetInfo asset, ISqlSugarClient? client = null)
        {
            var refObj = _provider.GetRequiredService<IAssetDataProxyRef>();

            if (deviceInfo == null || asset.Id <= 0) return;
            if (client == null)
            {
                client = _provider.GetRequiredService<SqlSugarScope>();
            }
            bool needUpdateByMLFB = deviceInfo.ContainsKey("MLFB");
            var deviceDetails = await client.Queryable<DeviceDetails>().FirstAsync(d => d.AssetId == asset.Id);
            if (deviceDetails == null)
            {
                deviceDetails = new DeviceDetails
                {
                    AssetId = asset.Id,
                    CreatedBy = "System",
                    CreatedTime = DateTime.Now,
                };
            }
            if (needUpdateByMLFB)
            {
                needUpdateByMLFB = !(deviceInfo["MLFB"].Equals(deviceDetails.MLFB));
            }

            if (asset.AssetModel == "3WL")
            {
                if (deviceInfo.TryGetValue("MainContantStatus", out var statusStr) && int.TryParse(statusStr, out var status))
                {
                    deviceInfo["MainContantStatus"] = (status + 1).ToString();
                }
            }

            var func = _provider.GetRequiredService<ObjectReflectFunc>();
            func.UpdateObjByDic(deviceDetails, deviceInfo);
            deviceDetails.UpdatedBy = "System";
            deviceDetails.UpdatedTime = DateTime.Now;

            if (needUpdateByMLFB)
            {
                asset.MLFB = deviceDetails.MLFB;
                var resolver = _provider.GetRequiredService<MLFBResolver>();
                await resolver.UpdateDeviceInfoByMLFB(asset, deviceDetails, deviceDetails.MLFB, client);
                asset.UpdatedBy = deviceDetails.UpdatedBy;
                asset.UpdatedTime = deviceDetails.UpdatedTime;
                await client.Updateable(asset).ExecuteCommandAsync();
            }

            #region 断路器
            var breakerModelList = new string[] { "3WA", "3WL", "3VA" };
            if (breakerModelList.Contains(asset.AssetModel))
            {
                var breakerService = _provider.GetRequiredService<BreakerHealthService>();
                breakerService.UpdateBreakerHealth(asset.AssetModel ?? string.Empty, deviceDetails);
                if (!string.IsNullOrEmpty(deviceDetails.RemainingLife))
                {
                    deviceInfo.TryAdd("HealthScore", deviceDetails.RemainingLife);
                    deviceInfo.TryAdd("RemainingLife", deviceDetails.RemainingLife);
                }
            }
            #endregion

            #region 发送设备详情
            {
                refObj.AssetDetail(new AssetDetailsData
                {
                    AssetId = asset.Id,
                    AssetName = asset.AssetName,
                    AssetType = asset.AssetType ?? string.Empty,
                    AssetModel = asset.AssetModel ?? string.Empty,
                    ChangeTime = DateTime.Now,
                    AssetLevel = asset.AssetLevel,
                    Details = new Dictionary<string, string>(deviceInfo)
                });
            }
            #endregion

            await client.Storageable<DeviceDetails>(deviceDetails).ExecuteCommandAsync();
        }

        public async Task InitDeviceInfoes(ILogger? logger = null)
        {
            DataPointServer dataPointServer = _provider.GetRequiredService<DataPointServer>();
            var refObj = _provider.GetRequiredService<IAssetDataProxyRef>();

            using (var sqlClient = _provider.GetRequiredService<ISqlSugarClient>())
            {
                var assets = await sqlClient.Queryable<AssetInfo>()
                    .Where(a => a.AssetLevel == AssetLevel.Device && a.ObjectId != null)
                    .ToArrayAsync();
                var items = await dataPointServer.GetDeviceItems();

                foreach (var asset in assets)
                {
                    if (string.IsNullOrEmpty(asset.ObjectId)) continue;
                    var item = items.FirstOrDefault(i => i.Id == asset.ObjectId);
                    if (item == null) continue;
                    if (!"connected".Equals(item.Monitoring?.State, StringComparison.OrdinalIgnoreCase)) continue;
                    var dataPoints = await dataPointServer.GetDataPointInfos(asset.AssetLevel, asset.AssetType, asset.AssetModel);
                    var maintains = dataPoints.Where(d => "Maintain".Equals(d.GroupName, StringComparison.OrdinalIgnoreCase)).ToArray();
                    if (maintains.Length == 0) continue;
                    var dataPointName = maintains.Select(d => d.UdcCode ?? string.Empty).Distinct().ToArray();

                    try
                    {
                        var values = await dataPointServer.GetDataByUDCApi(asset.ObjectId, dataPointName);
                        var panelManageValues = new Dictionary<string, string>();
                        foreach (var value in values)
                        {
                            var point = maintains.FirstOrDefault(d => d.UdcCode == value.Key);
                            if (point == null) continue;
                            var valueData = value.Value;
                            if (!string.IsNullOrEmpty(point.Extend))
                            {
                                var funcName = point.FuncName;
                                if (!string.IsNullOrEmpty(funcName)
                                    && _changeDataFuncs.TryGetValue(funcName, out var func))
                                {
                                    valueData = func(valueData);
                                }
                            }
                            panelManageValues.TryAdd(point.Code, valueData);
                        }

                        await UpdateDeviceInfo(panelManageValues, asset, sqlClient);
                    }
                    catch (Exception ex)
                    {
                        logger?.LogError(ex, $"同步设备维护信息异常 {asset.ObjectId}");
                    }

                    #region 保护定值
                    var ps = dataPoints.Where(d => "ProtectionSetting".Equals(d.GroupName, StringComparison.OrdinalIgnoreCase)).ToArray();
                    if (ps.Length == 0) continue;
                    dataPointName = ps.Select(d => d.UdcCode ?? string.Empty).Distinct().ToArray();
                    try
                    {
                        var values = await dataPointServer.GetDataByUDCApi(asset.ObjectId, dataPointName);

                        if (values.Count > 0)
                        {
                            var panelManageValues = new Dictionary<string, string>();
                            foreach (var value in values)
                            {
                                var point = ps.FirstOrDefault(d => d.UdcCode == value.Key);
                                if (point == null) continue;
                                panelManageValues.TryAdd(point.Code, value.Value);
                            }

                            refObj.AssetDetail(new AssetDetailsData
                            {
                                AssetId = asset.Id,
                                AssetName = asset.AssetName,
                                AssetType = asset.AssetType ?? string.Empty,
                                AssetModel = asset.AssetModel ?? string.Empty,
                                ChangeTime = DateTime.Now,
                                AssetLevel = asset.AssetLevel,
                                Details = new Dictionary<string, string>(panelManageValues)
                            });

                            var protectionSettingService = _provider.GetRequiredService<DeviceProtectionSettingService>();
                            await protectionSettingService.UpdateProtectionSettingToDB(asset.Id, sqlClient, "Job", panelManageValues);
                        }
                    }
                    catch (Exception ex)
                    {
                        logger?.LogError(ex, "同步设备维护信息异常");
                    }
                    #endregion
                }
            }
        }
    }
}
