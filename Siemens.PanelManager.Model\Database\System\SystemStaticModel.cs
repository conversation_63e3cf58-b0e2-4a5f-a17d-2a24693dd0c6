﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.System
{
    [SugarTable("sys_static_model")]
    public class SystemStaticModel : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "model_name", IsNullable = false, Length = 256)]
        public string Name { get; set; } = string.Empty;
        [UniquenessAttribute]
        [SugarColumn(ColumnName = "model_code", IsNullable = false, Length = 256)]
        public string Code { get; set; } = string.Empty;
        [UpperAttibute]
        [UniquenessAttribute]
        [SugarColumn(ColumnName = "model_type", IsNullable = false, Length = 256)]
        public string Type { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "model_extend", IsNullable = true, Length = 256)]
        public string? Extend { get; set; }
        [SugarColumn(ColumnName = "sort", IsNullable = false)]
        public int Sort { get; set; }
        [SugarColumn(ColumnName = "model_img", IsNullable = true, Length = 256)]
        public string? ImgCode { get; set; }
    }
}
