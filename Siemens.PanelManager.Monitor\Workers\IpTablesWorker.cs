﻿using Microsoft.AspNetCore.SignalR;
using Siemens.PanelManager.HubModel;
using Siemens.PanelManager.Monitor.Function;
using Siemens.PanelManager.Monitor.Hubs;
using Siemens.PanelManager.Monitor.StaticData;

namespace Siemens.PanelManager.Monitor.Workers
{
    public class IpTablesWorker : BackgroundService
    {
        private ILogger<IpTablesWorker> _logger { get; set; }
        private IpTableManager _tableManager { get; set; }
        
        private bool _isFirstRun = true;
        public IpTablesWorker(ILogger<IpTablesWorker> logger, IpTableManager manager)
        {
            _logger = logger;
            _tableManager = manager;
        }
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (true)
            {
                try
                {
                    if (_isFirstRun)
                    {
                        var rules = await IpTableManagerFunc.GetCurrentlyInputRules(_logger);
                        foreach (var configRule in _tableManager.ConfigRules)
                        {
                            var rule = rules.FirstOrDefault(r => r.Dport == configRule.Dport && r.ChainName == configRule.ChainName && r.Protocol == configRule.Protocol);
                            if (rule != null)
                            {
                                if (rule.Sport != configRule.Sport || rule.Target != configRule.Target)
                                {
                                    await IpTableManagerFunc.DeleteInputRule(_logger, rule);
                                    await IpTableManagerFunc.AppendInputRule(_logger, configRule);
                                }
                            }
                            else
                            {
                                await IpTableManagerFunc.AppendInputRule(_logger, configRule);
                            }
                        }
                    }
                    else
                    {
                        var expireRules = _tableManager.GetExpireRules(DateTime.Now);
                        if (expireRules.Length > 0)
                        {
                            foreach (var rule in expireRules)
                            {
                                await IpTableManagerFunc.DeleteInputRule(_logger, rule);
                                var configRule = _tableManager.ConfigRules.FirstOrDefault(r => r.Dport == rule.Dport && r.ChainName == rule.ChainName && r.Protocol == rule.Protocol);
                                if (configRule != null)
                                {
                                    await IpTableManagerFunc.AppendInputRule(_logger, configRule);
                                }
                            }
                        }
                    }
                    _isFirstRun = false;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "ip tables worker failed");
                }
                // 每分钟执行一次
                await Task.Delay(1000 * 60);
            }
        }
    }
}
