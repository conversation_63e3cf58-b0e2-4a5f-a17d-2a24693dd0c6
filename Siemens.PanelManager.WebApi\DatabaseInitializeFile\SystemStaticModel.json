[{"Name": "塑壳断路器", "Code": "MCCB", "Type": "DeviceType", "Sort": "100", "Extend": "{\"NoSupport\":false}", "Language": "zh-cn"}, {"Name": "3VA", "Code": "3VA", "Sort": 97, "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"MCCB\"}", "Language": "zh-cn"}, {"Name": "其他", "Code": "Other", "Sort": -1, "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"MCCB\"}", "Language": "zh-cn"}, {"Name": "空气断路器", "Code": "ACB", "Type": "DeviceType", "Extend": "{\"NoSupport\":false}", "Sort": 99, "Language": "zh-cn"}, {"Name": "3WA", "Code": "3WA", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"ACB\"}", "Language": "zh-cn", "Sort": 10}, {"Name": "3WL", "Code": "3WL", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"ACB\"}", "Language": "zh-cn", "Sort": 8}, {"Name": "3WT", "Code": "3WT", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"ACB\",\"NoSupport\":true}", "Language": "zh-cn", "Sort": 9}, {"Name": "其他", "Code": "Other", "Type": "DeviceModel", "Sort": -1, "Extend": "{\"ModelByType\":\"ACB\"}", "Language": "zh-cn"}, {"Name": "微型断路器", "Code": "MCB", "Type": "DeviceType", "Sort": 98, "Language": "zh-cn", "Extend": "{\"NoSupport\":true}"}, {"Name": "5SL", "Code": "5SL", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"MCB\"}", "Language": "zh-cn", "Sort": 3}, {"Name": "5SV", "Code": "5SV", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"MCB\"}", "Language": "zh-cn", "Sort": 2}, {"Name": "5ST", "Code": "5ST", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"MCB\"}", "Language": "zh-cn", "Sort": 1}, {"Name": "其他", "Code": "Other", "Type": "DeviceModel", "Sort": -1, "Extend": "{\"ModelByType\":\"MCB\"}", "Language": "zh-cn"}, {"Name": "马达保护", "Code": "MotorProtector", "Type": "DeviceType", "Sort": 97, "Language": "zh-cn", "Extend": "{\"NoSupport\":true}"}, {"Name": "3UF", "Code": "3UF", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"MotorProtector\"}", "Language": "zh-cn", "Sort": 10}, {"Name": "3UE", "Code": "3UE", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"MotorProtector\"}", "Language": "zh-cn", "Sort": 9}, {"Name": "其他", "Code": "Other", "Sort": -1, "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"MotorProtector\"}", "Language": "zh-cn"}, {"Name": "软启动器", "Code": "SoftStarter", "Type": "DeviceType", "Sort": 96, "Language": "zh-cn", "Extend": "{\"NoSupport\":true}"}, {"Name": "3RW", "Code": "3RW", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"SoftStarter\"}", "Language": "zh-cn", "Sort": 99}, {"Name": "其他", "Code": "Other", "Sort": -1, "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"SoftStarter\"}", "Language": "zh-cn"}, {"Name": "微机保护", "Code": "<PERSON><PERSON>", "Type": "DeviceType", "Sort": 95, "Language": "zh-cn", "Extend": "{\"NoSupport\":true}"}, {"Name": "7SJ68", "Code": "7SJ68", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"Relay\"}", "Language": "zh-cn", "Sort": 99}, {"Name": "7SJ62", "Code": "7SJ62", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"Relay\"}", "Language": "zh-cn", "Sort": 97}, {"Name": "7SJ63", "Code": "7SJ63", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"Relay\"}", "Language": "zh-cn", "Sort": 98}, {"Name": "其他", "Code": "Other", "Sort": -1, "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"Relay\"}", "Language": "zh-cn"}, {"Name": "双电源", "Code": "ATSE", "Type": "DeviceType", "Sort": 94, "Language": "zh-cn", "Extend": "{\"NoSupport\":true}"}, {"Name": "3KC2", "Code": "3KC2", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"ATSE\"}", "Language": "zh-cn", "Sort": 98}, {"Name": "3KC8", "Code": "3KC8", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"ATSE\"}", "Language": "zh-cn", "Sort": 99}, {"Name": "其他", "Code": "Other", "Sort": -1, "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"ATSE\"}", "Language": "zh-cn"}, {"Name": "网关", "Code": "Gateway", "Type": "DeviceType", "Sort": 93, "Language": "zh-cn", "Extend": "{\"NoSupport\":false}"}, {"Name": "POC1000", "Code": "POC1000", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"Gateway\"}", "Language": "zh-cn", "Sort": 99}, {"Name": "其他", "Code": "Other", "Sort": -1, "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"Gateway\"}", "Language": "zh-cn"}, {"Name": "PLC", "Code": "PLC", "Type": "DeviceType", "Sort": 92, "Language": "zh-cn", "Extend": "{\"NoSupport\":true}"}, {"Name": "PLC1200", "Code": "PLC1200", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"PLC\"}", "Language": "zh-cn", "Sort": 99}, {"Name": "其他", "Code": "Other", "Sort": -1, "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"PLC\"}", "Language": "zh-cn"}, {"Name": "测量仪表", "Code": "<PERSON>er", "Type": "DeviceType", "Sort": 91, "Language": "zh-cn"}, {"Name": "PAC1020", "Code": "PAC1020", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"Meter\"}", "Language": "zh-cn", "Sort": 5}, {"Name": "PAC3120", "Code": "PAC3120", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"Meter\"}", "Language": "zh-cn", "Sort": 8}, {"Name": "PAC3220", "Code": "PAC3220", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"Meter\"}", "Language": "zh-cn", "Sort": 10}, {"Name": "PAC3200", "Code": "PAC3200", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"Meter\"}", "Language": "zh-cn", "Sort": 9}, {"Name": "PAC4200", "Code": "PAC4200", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"Meter\"}", "Language": "zh-cn", "Sort": 11}, {"Name": "PAC5220", "Code": "PAC5220", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"Meter\",\"NoSupport\":true}", "Language": "zh-cn", "Sort": 12}, {"Name": "PAC1200", "Code": "PAC1200", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"Meter\",\"NoSupport\":true}", "Language": "zh-cn", "Sort": 6}, {"Name": "PAC1600", "Code": "PAC1600", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"Meter\",\"NoSupport\":true}", "Language": "zh-cn", "Sort": 7}, {"Name": "其他", "Code": "Other", "Type": "DeviceModel", "Sort": -1, "Extend": "{\"ModelByType\":\"Meter\"}", "Language": "zh-cn"}, {"Name": "测温系统", "Code": "TempMeasurement", "Type": "DeviceType", "Sort": 90, "Language": "zh-cn"}, {"Name": "西门子测温系统", "Code": "SiemensTempMeasurement", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"TempMeasurement\"}", "Language": "zh-cn", "Sort": 1}, {"Name": "其他", "Code": "Other", "Type": "DeviceModel", "Sort": -1, "Extend": "{\"ModelByType\":\"TempMeasurement\"}", "Language": "zh-cn"}, {"Name": "通用设备", "Code": "GeneralDevice", "Type": "DeviceType", "Sort": 0, "Language": "zh-cn"}, {"Name": "通用设备", "Code": "GeneralDevice", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"GeneralDevice\"}", "Language": "zh-cn"}, {"Name": "COM800", "Code": "COM800", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"Gateway\"}", "Language": "zh-cn"}, {"Name": "Modbus", "Code": "Modbus", "Type": "DeviceModel", "Extend": "{\"ModelByType\":\"Gateway\"}", "Language": "zh-cn"}, {"Name": "配电", "Code": "<PERSON>ede<PERSON>", "Type": "UseScene", "Language": "zh-cn", "Sort": 12}, {"Name": "照明", "Code": "Lighting", "Type": "UseScene", "Language": "zh-cn", "Sort": 8}, {"Name": "动力", "Code": "Dynamical", "Type": "UseScene", "Language": "zh-cn", "Sort": 11}, {"Name": "工艺", "Code": "Process", "Type": "UseScene", "Language": "zh-cn", "Sort": 10}, {"Name": "空调", "Code": "AirConditioner", "Type": "UseScene", "Language": "zh-cn", "Sort": 9}, {"Name": "母联", "Code": "BusCoupler", "Type": "UseScene", "Language": "zh-cn", "Sort": 3}, {"Name": "应急", "Code": "Emergency", "Type": "UseScene", "Language": "zh-cn", "Sort": 7}, {"Name": "补偿", "Code": "Compensation", "Type": "UseScene", "Language": "zh-cn", "Sort": 6}, {"Name": "无", "Code": "Incoming", "Type": "UseScene", "Language": "zh-cn", "Sort": 5}, {"Name": "出线", "Code": "Outgoing", "Type": "UseScene", "Language": "zh-cn", "Sort": 4}, {"Name": "备用", "Code": "Spare", "Type": "UseScene", "Language": "zh-cn", "Sort": 2}, {"Name": "其他", "Code": "Other", "Type": "UseScene", "Language": "zh-cn", "Sort": 1}, {"Name": "配电-框架", "Code": "<PERSON>ede<PERSON>", "Type": "CircuitType", "Language": "zh-cn", "Sort": 11}, {"Name": "电机-马保", "Code": "Motor", "Type": "CircuitType", "Language": "zh-cn", "Sort": 9}, {"Name": "进线", "Code": "Incoming", "Type": "CircuitType", "Language": "zh-cn", "Sort": 7}, {"Name": "母联", "Code": "BusCoupler", "Type": "CircuitType", "Language": "zh-cn", "Sort": 6}, {"Name": "备用", "Code": "Spare", "Type": "CircuitType", "Language": "zh-cn", "Sort": 3}, {"Name": "电机-热继", "Code": "Thermorelay", "Type": "CircuitType", "Language": "zh-cn", "Sort": 8}, {"Name": "有源滤波", "Code": "ActivePowerFilter", "Type": "CircuitType", "Language": "zh-cn", "Sort": 4}, {"Name": "电容补偿", "Code": "Capacitance", "Type": "CircuitType", "Language": "zh-cn", "Sort": 5}, {"Name": "其他", "Code": "Other", "Type": "CircuitType", "Language": "zh-cn", "Sort": 1}, {"Name": "配电-塑壳", "Code": "FeederMCCB", "Type": "CircuitType", "Language": "zh-cn", "Sort": 10}, {"Name": "空白", "Code": "Blank", "Type": "CircuitType", "Language": "zh-cn", "Sort": 2}, {"Name": "干式变压器", "Code": "DryTransformer", "Type": "TransformerType", "Language": "zh-cn"}, {"Name": "油㓎式变压器", "Code": "OilTransformer", "Type": "TransformerType", "Language": "zh-cn"}, {"Name": "SCB", "Code": "SCB", "Type": "TransformerModel", "Language": "zh-cn"}, {"Name": "通用", "Code": "Other", "Type": "TransformerModel", "Language": "zh-cn"}, {"Name": "进线柜", "Code": "IncomingCabinet", "Type": "PanelType", "Language": "zh-cn"}, {"Name": "出线柜", "Code": "OutletCabinet", "Type": "PanelType", "Language": "zh-cn"}, {"Name": "母联柜", "Code": "BusbarCabinet", "Type": "PanelType", "Language": "zh-cn"}, {"Name": "补充柜", "Code": "SupplementCabinet", "Type": "PanelType", "Language": "zh-cn"}, {"Name": "补充柜", "Code": "SupplementCabinet", "Type": "PanelType", "Language": "zh-cn"}, {"Name": "补充柜", "Code": "SupplementCabinet", "Type": "PanelType", "Language": "zh-cn"}, {"Name": "其他", "Code": "Other", "Type": "PanelType", "Language": "zh-cn"}, {"Name": "SIVACON 8PT", "Code": "Sivecon8PT", "Type": "PanelModel"}, {"Name": "SIVACON S8", "Code": "SiveconS8", "Type": "PanelModel"}, {"Name": "其他", "Code": "Other", "Type": "PanelModel", "Language": "zh-cn"}, {"Name": "400V", "Code": "400V", "Type": "NominalVoltage"}, {"Name": "PAC4200", "Code": "PAC4200", "Type": "DeviceModelForMessage"}, {"Name": "3WL", "Code": "3WL", "Type": "DeviceModelForMessage"}, {"Name": "3WA", "Code": "3WA", "Type": "DeviceModelForMessage"}, {"Name": "3VA", "Code": "3VA", "Type": "DeviceModelForMessage"}, {"Name": "COM800", "Code": "COM800", "Type": "DeviceModelForMessage"}, {"Name": "3WA", "Code": "3WA", "Extend": "{\"FrequencyCount\":30}", "Type": "DeviceModelForHarmonic"}, {"Name": "3WL", "Code": "3WL", "Extend": "{\"FrequencyCount\":29}", "Type": "DeviceModelForHarmonic"}, {"Name": "PAC4200", "Code": "PAC4200", "Extend": "{\"FrequencyCount\":64}", "Type": "DeviceModelForHarmonic"}, {"Name": "GeneralDevice", "Code": "GeneralDevice", "Extend": "{\"FrequencyCount\":64}", "Type": "DeviceModelForHarmonic"}]