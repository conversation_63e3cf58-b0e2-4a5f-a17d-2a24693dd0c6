﻿using Akka.Actor;
using Akka.Configuration;
using Akka.DependencyInjection;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.DeviceDataFlow.DataFlowActors;
using Siemens.PanelManager.DeviceDataFlow.MessageBus;
using Siemens.PanelManager.DeviceDataFlow.Model;
using Siemens.PanelManager.DeviceDataFlow.StaticData;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Database.Asset;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.DeviceDataFlow
{
    internal class ActorServer : IHostedService
    {
        
        private readonly IServiceProvider _provider;
        public ActorServer(IServiceProvider provider)
        {
            _provider = provider;
        }
        public async Task StartAsync(CancellationToken cancellationToken)
        {
            try
            {
                var configPath = Path.Combine(Directory.GetCurrentDirectory(), "akkaConfig.json");
                var config = await File.ReadAllTextAsync(configPath);
                var akkaConfig = ConfigurationFactory.ParseString(config);
                var di = DependencyResolverSetup.Create(_provider);
                var actorSystemSetup = BootstrapSetup
                    .Create()
                    .WithConfig(akkaConfig)
                    .And(di);

                var system = ActorSystem.Create(Constant.SystemName, actorSystemSetup);

                var simpleList = await UpdateAssetSimpleInfoToCache();
                var props = DependencyResolver.For(system).Props<AssetProxyActor>(new object[] { simpleList });
                var assetProxyRef = system.ActorOf(props);

                ActorManager.Initialization(system, assetProxyRef, _provider);

                MessageBusContext.MessageRegister.RegisterMessageAction("AssetDataFlow", (input) =>
                {
                    var refObj = ActorManager.GetActorManagerNoException();
                    if (refObj != null)
                    {
                        refObj.DataPointRef.Tell(input);
                    }
                });
            }
            catch (Exception ex)
            {
                
            }
        }

        public async Task StopAsync(CancellationToken cancellationToken)
        {
            await ActorManager.GetActorManager().ActorSystem.Terminate();
        }

        private async Task<AssetSimpleInfo[]> UpdateAssetSimpleInfoToCache()
        {
            using (var sqlClient = _provider.GetRequiredService<ISqlSugarClient>())
            {
                var cache = _provider.GetRequiredService<SiemensCache>();
                var assetInfos = await sqlClient.Queryable<AssetInfo>().OrderBy(a=>a.Id).ToArrayAsync();
                var simpleInfoes = new List<AssetSimpleInfo>();
                var refObj = _provider.GetService<IAssetDataProxyRef>();

                for(var i = 0;i< assetInfos.Length; i++)
                {
                    var assetInfo = assetInfos[i];
                    simpleInfoes.Add(await AssetInfoExtend.AddAsset(assetInfo, sqlClient, cache, refObj));
                }

                return simpleInfoes.ToArray();
            }
        }
    }
}
