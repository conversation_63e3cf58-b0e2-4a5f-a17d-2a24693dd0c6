﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.System
{
    [SugarTable("sys_job_schedule")]
    public class JobSchedule : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "job_name", IsNullable = false, Length = 256)]
        public string JobName { get; set; } = string.Empty;
        [UniquenessAttribute]
        [SugarColumn(ColumnName = "job_code", IsNullable = false, Length = 50)]
        public string JobCode { get; set; } = Guid.NewGuid().ToString();
        [SugarColumn(ColumnName = "is_main", IsNullable = false)]
        public bool IsMain { get; set; }
        [SugarColumn(ColumnName = "parent_job_code", IsNullable = true)]
        public string? ParentJobCode { get; set; }
        [SugarColumn(ColumnName = "cron", IsNullable = true)]
        public string? Cron { get; set; }
        [SugarColumn(ColumnName = "parameters", IsNullable = true, Length = 512)]
        public string? Parameters { get; set; }

        [SugarColumn(ColumnName = "record_runtime", IsNullable = true)]
        public bool NeedRecordRuntime { get; set; } = false;

        [SugarColumn(ColumnName = "need_run", IsNullable = true)]
        public bool? NeedRun { get; set; }

    }
}
