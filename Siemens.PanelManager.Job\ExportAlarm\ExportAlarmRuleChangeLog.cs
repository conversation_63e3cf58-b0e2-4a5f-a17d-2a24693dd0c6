﻿using Siemens.PanelManager.Common;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Model.Database.Alarm;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.ExportAlarm
{
    internal class ExportAlarmRuleChangeLog
    {
        public ExportAlarmRuleChangeLog(AlarmRuleChangeHistory history, IMessageContext messageContext) 
        {
            RuleId = history.RuleId.ToString();
            RuleName = history.RuleName.ToString();
            AlarmLevel = history.Severity.ToExportString(messageContext);
            Opt = history.Operation.ToExportString(messageContext);
            RuleInfo = history.RuleInfo;
            if (history.Operation != AlarmRuleOpt.Current)
            {
                ChangedTime = history.Timestamp.GetDateTimeBySec();
                ChangedBy = history.UpdatedBy;
            }
        }

        public string RuleId { get; set; } = string.Empty;
        public string RuleName { get; set; } = string.Empty;
        public string RuleInfo { get; set; } = string.Empty;
        public string AlarmLevel { get; set; } = string.Empty;
        public string Opt { get; set; } = string.Empty;
        public DateTime? ChangedTime { get; set; }
        public string ChangedBy { get; set; } = string.Empty;
    }
}
