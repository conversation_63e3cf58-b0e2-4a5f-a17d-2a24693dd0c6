﻿using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Common;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.Excel;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.Model.Job;
using Siemens.PanelManager.Server.Common;
using SqlSugar;

namespace Siemens.PanelManager.Job.ExportAlarm
{
    public class ExportAlarmJob : JobBase
    {
        const string TemplateFileName = "Alarm&Log-Template";
        private ILogger<ExportAlarmJob> _logger;
        private ISqlSugarClient _client;
        private SiemensExcelHelper _excelHelper;
        private SiemensCache _cache;
        TempFileManager _tempFileManager;
        private IMessageContextFactory _contextFactory;

        public ExportAlarmJob(ILogger<ExportAlarmJob> logger, 
            ISqlSugarClient client, 
            SiemensExcelHelper excelHelper,
            TempFileManager tempFileManager,
            SiemensCache cache, 
            IMessageContextFactory factory)
        {
            _logger = logger;
            _excelHelper = excelHelper;
            _cache = cache;
            _client = client;
            _contextFactory = factory;
            _tempFileManager = tempFileManager;
        }
        public override string Name => "ExportAlarmJob";

        public override async Task Execute()
        {
            if (!ContextData.ContainsKey("UseFilter")
                || !ContextData.ContainsKey("AlarmEndTime")
                || !ContextData.ContainsKey("AlarmStartTime")
                || !ContextData.ContainsKey("AlarmStatus")
                || !ContextData.ContainsKey("AlarmLevel")
                || !ContextData.ContainsKey("EventType")
                || !ContextData.ContainsKey("DeviceName")
                || !ContextData.ContainsKey("CircuitName")
                || !ContextData.ContainsKey("PanelName")
                || !ContextData.ContainsKey("SubstationName")
                || !ContextData.ContainsKey("UserName")
                || !ContextData.ContainsKey("JobId")
                || !ContextData.ContainsKey("Language")
                || !bool.TryParse(ContextData["UseFilter"], out bool useFilter))
            {
                _logger.LogInformation($"{Name} 启动失败, 参数缺失");
                return;
            }
            var jobId = ContextData["JobId"];
            var jobInfo = _cache.Get<JobInfo>($"JobId:{jobId}");

            if (jobInfo == null)
            {
                _logger.LogInformation($"{Name}:{jobId} 已过期");
                return;
            }
            var jobResult = new ExportAlarmJobResult();
            jobInfo.JobStatus = 10;
            jobInfo.Result = new JobResultModel();
            jobInfo.Result.Data = jobResult;
            _cache.Set($"JobId:{jobId}", jobInfo, TimeSpan.FromMinutes(1));

            var language = ContextData["Language"];
            try
            {
                var query = _client.Queryable<AlarmLog>();
                var changeHistoryQuery = _client.Queryable<AlarmRuleChangeHistory>();
                var queryStartTime = await _client.Queryable<AlarmQueryConfig>().Where(a => a.QueryType == AlarmQueryType.QueryStartTime).FirstAsync();
                if (queryStartTime != null && !string.IsNullOrEmpty(queryStartTime.QueryValue))
                {
                    DateTime startDate;
                    var success = DateTime.TryParseExact(queryStartTime.QueryValue, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.RoundtripKind, out startDate);
                    if (success)
                    {
                        query = query.Where(l => l.CreatedTime >= startDate);
                    }
                }
                if (useFilter)
                {
                    AlarmEventType alarmEventType = AlarmEventType.All;
                    if (!string.IsNullOrEmpty(ContextData["EventType"]))
                    {
                        var eventTypes = ContextData["EventType"].Split(",").Select(e =>
                        {
                            if (int.TryParse(e, out var intValue))
                            {
                                return intValue;
                            }
                            return -1;
                        }).ToArray();
                        query = query.Where(l => eventTypes.Contains((int)l.EventType));
                    }
                    if (!string.IsNullOrEmpty(ContextData["AlarmLevel"]))
                    {
                        var alarmLevels = ContextData["AlarmLevel"].Split(",").Select(e =>
                        {
                            if (int.TryParse(e, out var intValue))
                            {
                                return intValue;
                            }
                            return -1;
                        }).ToArray();
                        query = query.Where(l => alarmLevels.Contains((int)l.Severity));
                    }
                    if (!string.IsNullOrEmpty(ContextData["AlarmRuleId"]))
                    {
                        var alarmRuleIds = ContextData["AlarmRuleId"].Split(",").Select(e =>
                        {
                            if (int.TryParse(e, out var intValue))
                            {
                                return intValue;
                            }
                            return -1;
                        }).ToArray();
                        query = query.Where(l => l.RuleId!=null&& alarmRuleIds.Contains((int)l.RuleId));
                    }

                    if (!string.IsNullOrEmpty(ContextData["SubstationName"]) && alarmEventType != AlarmEventType.OperationLog)
                    {
                        var substationNames= ContextData["SubstationName"].Split(',');
                        query = query.Where(l => substationNames.Contains(l.SubstationName));
                    }
                    if (!string.IsNullOrEmpty(ContextData["PanelName"]) && alarmEventType != AlarmEventType.OperationLog)
                    {
                        var panelNames = ContextData["PanelName"].Split(',');
                        query = query.Where(l => panelNames.Contains(l.PanelName));
                    }
                    if (!string.IsNullOrEmpty(ContextData["CircuitName"]) && alarmEventType != AlarmEventType.OperationLog)
                    {
                        var circuitNames = ContextData["CircuitName"].Split(',');
                        query = query.Where(l => circuitNames.Contains(l.CircuitName));
                    }
                    if (!string.IsNullOrEmpty(ContextData["DeviceName"]) && alarmEventType != AlarmEventType.OperationLog)
                    {
                        var circuitNames = ContextData["CircuitName"].Split(',');
                        query = query.Where(l => circuitNames.Contains(l.CircuitName));
                        query = query.Where(l => l.DeviceName != null && l.DeviceName.Contains(ContextData["DeviceName"]));
                    }

                    if (long.TryParse(ContextData["AlarmStartTime"], out long alarmStartTime))
                    {
                        var startTime = alarmStartTime.GetDateTimeBySec();
                        query = query.Where(l => l.CreatedTime >= startTime);
                        changeHistoryQuery = changeHistoryQuery.Where(h => h.Timestamp >= alarmStartTime);
                    }
                    if (long.TryParse(ContextData["AlarmEndTime"], out long alarmEndTime))
                    {
                        var endTime = alarmEndTime.GetDateTimeBySec();
                        query = query.Where(l => l.CreatedTime < endTime);
                        changeHistoryQuery = changeHistoryQuery.Where(h => h.Timestamp < alarmEndTime);
                    }
                    if (!string.IsNullOrEmpty(ContextData["AlarmStatus"]) && alarmEventType != AlarmEventType.OperationLog)
                    {
                        var alarmStatus = ContextData["AlarmStatus"].Split(",").Select(e =>
                        {
                            if (int.TryParse(e, out var intValue))
                            {
                                return intValue;
                            }
                            return -1;
                        }).ToArray();
                        query = query.Where(l => alarmStatus.Contains((int)l.Status));
                    }

                    if (!string.IsNullOrEmpty(ContextData["UserName"]) && alarmEventType == AlarmEventType.OperationLog)
                    {
                        query = query.Where(l => l.CreatedBy.Contains(ContextData["UserName"]));
                    }
                   
                }

                var messageContext = _contextFactory.GetMessageContext(language);
                bool next = true;
                var rules = await _client.Queryable<AlarmRule>().ToArrayAsync();
                var pageNo = 0;
                var main = new ExportAlarmMain();
                do
                {
                    pageNo++;
                    var logs = await query.ToPageListAsync(pageNo, 1000);
                    if (logs.Count < 1000)
                    {
                        next = false;
                    }
                    foreach (var log in logs)
                    {
                        AlarmRule? rule = null;
                        if (log.RuleId.HasValue)
                        {
                            rule = rules.FirstOrDefault(r => r.Id == log.RuleId.Value);
                        }
                        main.AlarmModel.Add(new ExportAlarmModel(log, rule, messageContext));
                    }
                    jobResult.Percentage++;
                    if (jobResult.Percentage < 40)
                    {
                        _cache.Set($"JobId:{jobId}", jobInfo, TimeSpan.FromMinutes(1));
                    }
                    else
                    {
                        jobResult.Percentage = 40;
                        _cache.Set($"JobId:{jobId}", jobInfo, TimeSpan.FromMinutes(1));
                    }
                }
                while (next);
                jobResult.Percentage = 40;
                _cache.Set($"JobId:{jobId}", jobInfo, TimeSpan.FromMinutes(1));
                var history = new List<AlarmRuleChangeHistory>();
                foreach (var r in rules)
                {
                    if (!r.IsDelete)
                    {
                        history.Add(new AlarmRuleChangeHistory()
                        {
                            Id = long.MaxValue,
                            RuleId = r.Id,
                            RuleName = r.Name,
                            Operation = AlarmRuleOpt.Current,
                        });
                    }
                }
                pageNo = 0;
                do
                {
                    pageNo++;
                    var histories = await changeHistoryQuery.ToPageListAsync(pageNo, 1000);
                    if (histories.Count < 1000)
                    {
                        next = false;
                    }
                    history.AddRange(histories);
                    jobResult.Percentage++;
                    if (jobResult.Percentage < 88)
                    {
                        _cache.Set($"JobId:{jobId}", jobInfo, TimeSpan.FromMinutes(1));
                    }
                    else
                    {
                        jobResult.Percentage = 88;
                        _cache.Set($"JobId:{jobId}", jobInfo, TimeSpan.FromMinutes(1));
                    }
                }
                while (next);

                jobResult.Percentage = 89;
                _cache.Set($"JobId:{jobId}", jobInfo, TimeSpan.FromMinutes(1));
                history = history.OrderByDescending(r => r.Id).OrderBy(r => r.RuleId).ToList();
                foreach (var h in history)
                {
                    main.ChangeLogs.Add(new ExportAlarmRuleChangeLog(h, messageContext));
                }                      

                jobResult.Percentage = 90;
                _cache.Set($"JobId:{jobId}", jobInfo, TimeSpan.FromMinutes(10));
                var path = Path.Combine(_tempFileManager.GetTempFolder(), $"{jobId}.xlsx");
                try
                {
                    using (var fs = new FileStream(path, FileMode.CreateNew, FileAccess.ReadWrite))
                    {
                        await _excelHelper.SaveExcelAsync(fs, main.GetDictionary(), language, TemplateFileName);
                    }
                    jobResult.Percentage = 100;
                }
                catch (Exception saveExcelEx)
                {
                    _logger.LogError(saveExcelEx, $"导出告警报表失败。JobId：{jobId}");
                    jobInfo.JobStatus = 99;
                    jobInfo.Result.ErrorInfo.Add("Common_ServerException");
                    _cache.Set($"JobId:{jobId}", jobInfo, TimeSpan.FromMinutes(3));
                    return;
                }

                jobInfo.JobStatus = 20;
                jobInfo.Result.Data = path;
                _cache.Set($"JobId:{jobId}", jobInfo, TimeSpan.FromMinutes(3));
                return;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"导出告警报表失败。JobId：{jobId}");
                jobInfo.JobStatus = 99;
                jobInfo.Result.ErrorInfo.Add("Common_ServerException");
                _cache.Set($"JobId:{jobId}", jobInfo, TimeSpan.FromMinutes(3));
                return;
            }
        }
    }
}
