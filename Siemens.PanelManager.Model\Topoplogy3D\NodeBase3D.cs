﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.Model.Topology3D
{
    public class NodeBase3D : NodeBase3DGeneric<UserData>
    {
        protected NodeBase3D()
        {
            Id = Guid.NewGuid().ToString();
            Type = NodeType;
            UserData = new UserData()
            {
                Type = NodeType,
            };
        }

        public virtual void Move(decimal x, decimal z)
        {
            Position.X += x;
            Position.Z += z;
        }

        [JsonIgnore]
        public virtual string NodeType { get; } = string.Empty;
    }
}
