﻿using InfluxDB.Client.Api.Domain;

namespace Siemens.InfluxDB.Helper.Interface
{
    public interface IInfluxDBClient : IDisposable
    {
        string GetBucket();
        Task<string> GetBucketId();
        string GetUserName();
        string GetOrganizationName();
        Task<string> GetOrgId();

        Task<IQueryable<T>> GetQueryClient<T>() where T : IInfluxData;
        Task<IInsertable<T>> GetInsertClient<T>() where T : IInfluxData;

        Task<List<TaskType>> GetTaskListAsync();
        Task AddTaskAsync(TaskType taskType);
        Task UpdateTaskAsync(TaskType taskType);
    }
}
