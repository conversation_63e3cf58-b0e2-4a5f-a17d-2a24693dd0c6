[{"AssetModel": "3VA", "AssetType": "MCCB", "Breakdown": "反复跳闸", "PossibleCause": "ETU 的保护设置不正确", "Measure": "检查ETU的电流设置是否与电源系统的额定电流相匹配。", "BreakdownComment": null, "PossibleCauseComment": null, "MeasureComment": null}, {"AssetModel": "3VA", "AssetType": "MCCB", "Breakdown": "反复跳闸", "PossibleCause": "欠压释放电压的波动", "Measure": "检查欠压释放处的电压。电机操作器）到同一电源可能会导致电机操作器）到同一电源在某些情况下可能导致电压短暂下降。", "BreakdownComment": null, "PossibleCauseComment": null, "MeasureComment": null}, {"AssetModel": "3VA", "AssetType": "MCCB", "Breakdown": "反复跳闸", "PossibleCause": "环境温度过高", "Measure": "为特定指定保护设置参考点。如果环境温度温度高于指定的参考值，或者如果其他环境条件没有得到应有的考虑 （例如，安装时抽出技术降额 位置在2000米以上）。适当注意环境条件并适当降额。", "BreakdownComment": null, "PossibleCauseComment": null, "MeasureComment": null}, {"AssetModel": "3VA", "AssetType": "MCCB", "Breakdown": "反复跳闸", "PossibleCause": "分流跳闸已激活", "Measure": "该跳闸是由分路跳闸STL或STF发起的。 检查意外分流跳闸的原因。", "BreakdownComment": null, "PossibleCauseComment": null, "MeasureComment": null}, {"AssetModel": "3VA", "AssetType": "MCCB", "Breakdown": "反复跳闸", "PossibleCause": "剩余电流装置跳闸", "Measure": "检查低压网络中的绝缘和载货。", "BreakdownComment": null, "PossibleCauseComment": null, "MeasureComment": null}, {"AssetModel": "3VA", "AssetType": "MCCB", "Breakdown": "反复跳闸", "PossibleCause": "接地故障跳闸", "Measure": "检查低压网络中的绝缘和载货。", "BreakdownComment": null, "PossibleCauseComment": null, "MeasureComment": null}, {"AssetModel": "3VA", "AssetType": "MCCB", "Breakdown": "手柄或手动操作员无法打开塑壳断路器", "PossibleCause": "欠压释放没有电压供应  ", "Measure": "检查欠压释放的电源UVR。", "BreakdownComment": null, "PossibleCauseComment": null, "MeasureComment": null}, {"AssetModel": "3VA", "AssetType": "MCCB", "Breakdown": "手柄或手动操作员无法打开塑壳断路器", "PossibleCause": "分流跳闸 STL 或 STF 连接到电压", "Measure": "检查电压", "BreakdownComment": null, "PossibleCauseComment": null, "MeasureComment": null}, {"AssetModel": "3VA", "AssetType": "MCCB", "Breakdown": "手柄或手动操作员无法打开塑壳断路器", "PossibleCause": "塑壳断路器被锁定，例如通过手动操作中的滑动杆或气缸锁", "Measure": "解锁断路器。", "BreakdownComment": null, "PossibleCauseComment": null, "MeasureComment": null}, {"AssetModel": "3VA", "AssetType": "MCCB", "Breakdown": "手柄或手动操作员无法打开塑壳断路器", "PossibleCause": "塑壳断路器互锁，例如通过前联锁", "Measure": "松开断路器。", "BreakdownComment": null, "PossibleCauseComment": null, "MeasureComment": null}, {"AssetModel": "3VA", "AssetType": "MCCB", "Breakdown": "塑壳断路器无法通过电机操作员接通", "PossibleCause": "欠压释放没有电压供应  ", "Measure": "检查欠压释放的电源UVR。", "BreakdownComment": null, "PossibleCauseComment": null, "MeasureComment": null}, {"AssetModel": "3VA", "AssetType": "MCCB", "Breakdown": "塑壳断路器无法通过电机操作员接通", "PossibleCause": "分流跳闸 STL 或 STF 连接到电压", "Measure": "检查电压", "BreakdownComment": null, "PossibleCauseComment": null, "MeasureComment": null}, {"AssetModel": "3VA", "AssetType": "MCCB", "Breakdown": "塑壳断路器无法通过电机操作员接通", "PossibleCause": "塑壳断路器被锁定，例如通过电机操作装置中的气缸锁锁定", "Measure": "解锁断路器。", "BreakdownComment": null, "PossibleCauseComment": null, "MeasureComment": null}, {"AssetModel": "3VA", "AssetType": "MCCB", "Breakdown": "塑壳断路器无法通过电机操作员接通", "PossibleCause": "如果COM800上的写保护滑动开关设置为OFF，则无法通过通信接口打开或关闭塑壳断路器", "Measure": "检查COM800上写保护滑动开关的位置。", "BreakdownComment": null, "PossibleCauseComment": null, "MeasureComment": null}, {"AssetModel": "3VA", "AssetType": "MCCB", "Breakdown": "调试期间无法更改网络参数（IP 地址）。", "PossibleCause": "COM35 具有写保护，默认情况下处于激活状态。", "Measure": "要停用写保护，必须连接COM35上的端子1和2。有关详细信息，请参阅写保护一章（第 21 页）。", "BreakdownComment": null, "PossibleCauseComment": null, "MeasureComment": null}, {"AssetModel": "3VA", "AssetType": "MCCB", "Breakdown": "在powerconfig中调试期间无法更改网络参数（IP地址）。", "PossibleCause": "默认情况下，编辑模式在 powerconfig 的设备搜索 （F11） 中不处于活动状态。在此模式下无法更改参数。", "Measure": "必须在 powerconfig 的设备搜索中激活编辑模式 （unock）。", "BreakdownComment": null, "PossibleCauseComment": null, "MeasureComment": null}, {"AssetModel": "3VA", "AssetType": "MCCB", "Breakdown": "COM35无法正确启动或偶尔重新启动。", "PossibleCause": "24 V 电源的尺寸不够。3WL ETU 需要高启动电流。这可能导致 24 V 骤降，从而没有足够的功率可用于 COM35。", "Measure": "COM35 使用较大的 24 V 电源或单独的电源。请参阅表 2-7 电源电压或 3WL 操作手册，“外部电源”一章。", "BreakdownComment": null, "PossibleCauseComment": null, "MeasureComment": null}, {"AssetModel": "3VA", "AssetType": "MCCB", "Breakdown": "隔间总线指示灯 （CUB） 的偶发故障正在闪烁，或者总线指示灯 （CUB） 闪烁或偶尔呈绿色闪烁。", "PossibleCause": "CubicleBUS 是串行总线，因此如果发生故障，节点将不再可用。", "Measure": "检查CubicleBUS的接触点和连接器，并在必要时重新连接。", "BreakdownComment": null, "PossibleCauseComment": null, "MeasureComment": null}, {"AssetModel": "3VA", "AssetType": "MCCB", "Breakdown": "偶尔，并非所有断路器数据都可用。", "PossibleCause": "CubicleBUS是一种串行总线，必须在其末端端接一个电阻（120欧姆）。", "Measure": "将终端电阻连接到CubicleBUS的最后一个节点。", "BreakdownComment": null, "PossibleCauseComment": null, "MeasureComment": null}, {"AssetModel": "3VA", "AssetType": "MCCB", "Breakdown": "偶尔，并非所有断路器数据都可用。Cubicle-BUS LED（CUB）呈红色亮起或点亮红色。", "PossibleCause": "CubicleBUS可能会受到EMC干扰的干扰。这可能会导致数据丢失。", "Measure": "通过适当的措施减少EMC干扰。通过适当的措施减少EMC干扰。", "BreakdownComment": null, "PossibleCauseComment": null, "MeasureComment": null}, {"AssetModel": "3VA", "AssetType": "MCCB", "Breakdown": "COM35上的所有指示灯均呈红色闪烁。", "PossibleCause": "COM35 监视检测到故障，但尚未准备就绪。", "Measure": "尝试通过按按钮S35恢复COM1上的出厂设置。如果无法消除此错误，请更换COM35。", "BreakdownComment": null, "PossibleCauseComment": null, "MeasureComment": null}, {"AssetModel": "3VA", "AssetType": "MCCB", "Breakdown": "COM35不能再通过Modbus TCP寻址。", "PossibleCause": "COM35 具有可以阻止访问的安全功能。", "Measure": "检查 COM35 的端口地址以及网络设备上的设置。", "BreakdownComment": null, "PossibleCauseComment": null, "MeasureComment": null}]