﻿using Akka.Actor;
using InfluxDB.Client;
using InfluxDB.Client.Api.Domain;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Interface.Job;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Chart;
using Siemens.PanelManager.Model.Config;
using Siemens.PanelManager.Model.DataFlow;
using Siemens.PanelManager.Model.UDC;
using Siemens.PanelManager.Server.Asset;
using Siemens.PanelManager.Server.Topoplogy;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using System.Collections;
using System.Diagnostics.CodeAnalysis;
using System.Xml.Linq;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class MyTestController : ControllerBase
    {
        private AssetDashboardServer _server;
        private IServiceProvider _provider;
        private ILogger _logger;
        public MyTestController(AssetDashboardServer server, IServiceProvider provider, ILogger<MyTestController> logger)
        {
            _server = server;
            _provider = provider;
            _logger = logger;
        }

        [HttpGet("GetConfig")]
        public async Task<string> GetConfig(string name)
        {
            using (var client = _provider.GetRequiredService<ISqlSugarClient>()) 
            {
                var config = await _server.GetDashboardConfigsAsync(new string[] { name }, client);
                return JsonConvert.SerializeObject(config);
            }
        }

        [HttpGet("GetChart")]
        public async Task<IChart?> GetChart(string name,
            int assetId,
            [AllowNull] string? chartDateType,
            [AllowNull] string? startDate,
            [AllowNull] string? endDate)
        {
            using (var client = _provider.GetRequiredService<ISqlSugarClient>())
            {
                var messageFactory = _provider.GetRequiredService<IMessageContextFactory>();
                var messageContext = messageFactory.GetMessageContext(messageFactory.GetDefaultLanguage());
                var filter = new Dictionary<string, string>()
                {
                    ["ChartDateType"] = chartDateType ?? "0",
                    ["AssetId"] = assetId.ToString(),
                };

                if (!string.IsNullOrEmpty(startDate)) 
                {
                    filter.Add("StartDate", startDate);
                }
                if (!string.IsNullOrEmpty(endDate))
                {
                    filter.Add("EndDate", endDate);
                }

                try
                {
                    var chart = await _server.GetDashboard(name, messageContext, filter, client);
                    return chart;
                }
                catch(Exception ex) 
                {

                }

                return null;
            }
        }

        [HttpGet("{assetId}/GetCharts")]
        public async Task<Dictionary<string, IChart?>> GetCharts([FromQuery] AssetChartParam param)
        {
            var messageFactory = _provider.GetRequiredService<IMessageContextFactory>();
            var messageContext = messageFactory.GetMessageContext(messageFactory.GetDefaultLanguage());
            var filter = new Dictionary<string, string>()
            {
                ["ChartDateType"] = ((int)param.ChartDateType).ToString(),
                ["AssetId"] = param.AssetId.ToString(),
            };

            if (!string.IsNullOrEmpty(param.StartDate))
            {
                filter.Add("StartDate", param.StartDate);
            }
            if (!string.IsNullOrEmpty(param.EndDate))
            {
                filter.Add("EndDate", param.EndDate);
            }

            var result = new Dictionary<string, IChart?>();
            var nameList = param.ChartNames.Distinct().ToArray();
            using (var client = _provider.GetRequiredService<ISqlSugarClient>())
            {
                foreach (var n in nameList)
                {
                    IChart? chart = null;
                    try
                    {
                        var f = new Dictionary<string, string>(filter);
                        chart = await _server.GetDashboard(n, messageContext, f, client);
                    }
                    catch (Exception ex)
                    {

                    }
                    result.Add(n, chart);
                }
            }

            return result;
        }

        [HttpGet("{assetId}/InportData")]
        public bool InportData(string dataPointName, int assetId, string value)
        {
            var inputData = new AssetInputData
            {
                AssetId = assetId,
                InputTime = DateTime.Now,
                Datas = new Dictionary<string, string>
                {
                    [dataPointName] = value
                }
            };

            var refObj = _provider.GetRequiredService<IAssetDataProxyRef>();
            refObj.InputData(inputData);
            return true;
        }

        [HttpGet("UpdateSplx")]
        public async Task<string> UpdateSplx()
        {
            var server = _provider.GetRequiredService<SplxFileManager>();
            var result = await server.ChangeSplxFile("D:\\Temp\\Test\\", string.Empty);
            return result.Success ? "成功" : result.Message;
        }

        [HttpGet("GetOCacheInfo")]
        public object GetOCacheInfo(string objectId)
        {
            var cache = _provider.GetRequiredService<SiemensCache>();
            var data = cache.Get<AssetSimpleInfo>($"AssetObjectId:{objectId}");
            if (data ==null)
            {
                return "No Data";
            }
            return data;
        }

        [HttpGet("GetACacheInfo")]
        public object GetACacheInfo(int assetId)
        {
            var cache = _provider.GetRequiredService<SiemensCache>();
            var data = cache.Get<AssetSimpleInfo>($"Asset:SimpleInfo-{assetId}");
            if (data == null)
            {
                return "No Data";
            }
            return data;
        }


        [HttpGet("GetCacheInfo")]
        public object? GetCacheInfo(string action, string cacheKey)
        {
            var cache = _provider.GetRequiredService<SiemensCache>();
            switch (action)
            {
                case "ConnectStatus":
                    return cache.Get<Dictionary<int, bool>>("AssetConnectStatus:All");
                default:break;
            }
            return true;
        }

        [HttpGet("SetData")]
        public string SetData([FromQuery] string[] messages)
        {
            var data = new DeviceDataPointsResult()
            {
                ItemId = "1",
                Embedded = new Embedded<DeivceDataPointItem>() 
                {
                    Items = new List<DeivceDataPointItem>()
                    {
                        new DeivceDataPointItem
                        {
                            InternalName = "DP1",
                            Value = messages[0]
                        },
                        new DeivceDataPointItem
                        {
                            InternalName = "DP2",
                            Value = messages[1]
                        },
                    }
                }
            };

            var refObj = _provider.GetRequiredService<IMessageReceiver>();
            refObj.InputMessage(data);

            return "OK";
        }

        [HttpGet("StartJob")]
        public async Task StartJob()
        {
            var jobManager = _provider.GetRequiredService<JobManager>();
            await jobManager.TriggerJob("GetDeviceMessagesJob");
        }

        [HttpGet("ReloadJob")]
        public async Task<bool> ReloadJob(int s = 3)
        {
            var manager = _provider.GetRequiredService<IDeviceConenctStateWorkerManager>();
            await manager.StopAsync();
            await Task.Delay(s * 1000);
            await manager.StartAsync();
            return true;
        }

        [HttpGet("SplitPanenl")]
        public async Task<bool> SplitPanel()
        {
            var service = _provider.GetRequiredService<TopoplogySplitService>();
            var client = _provider.GetRequiredService<ISqlSugarClient>();
            await service.SplitPanelTopoplogy(client, DateTime.Today.AddYears(-1));
            return true;
        }


        [HttpGet("GetConfiguration")]
        public string GetConfigStr(string key)
        {
            var config = _provider.GetRequiredService<IConfiguration>();
            var result = config.GetValue<string>(key, "Null");
            if (string.IsNullOrEmpty(result))
            {
                return "Empty";
            }

            return result;
        }

        [HttpGet("GetInfluxDbClient")]
        public async Task<string> GetInfluxDbClient()
        {
            IConfiguration configuration = _provider.GetRequiredService<IConfiguration>();
            var session = configuration.GetSection(InfluxDBConfig.Influxdb);
            if (session != null)
            {
                var influxDbConfig = session.Get<InfluxDBConfig>();
                if (influxDbConfig != null)
                {
                    var influxClient = new InfluxDBClient(influxDbConfig.Url, influxDbConfig.UserName, influxDbConfig.Password);
                    var api = influxClient.GetTasksApi();
                    var tasks = await api.FindTasksAsync();
                    await api.CreateTaskAsync(new TaskType()
                    {
                        Org = influxDbConfig.OrgName,
                        Flux = "",
                        Every = "15m",
                    });
                    return JsonConvert.SerializeObject(tasks);
                }
                return influxDbConfig?.Url ?? string.Empty;
            }
            return string.Empty;
        }

        [HttpGet("DebugJsonFile")]
        public async Task<string> DebugJsonFile([FromQuery] int assetId)
        {
            var s = _provider.GetRequiredService<SplxFileManager>();
            using var sqlClient = _provider.GetRequiredService<ISqlSugarClient>();
            await s.ChangeSplxFileForModbus(assetId, string.Empty, "D:\\Temp\\", sqlClient);
            return string.Empty;
        }
    }
}
