﻿using Newtonsoft.Json.Linq;

namespace Siemens.PanelManager.Model.Topology
{
    public class TopologyDetails
    {
        public TopologyFrom From { get; set; }
        public int TopologyId { get; set; }
        public string TopologyName {  get; set; } = string.Empty;
        public string? Description { get; set; }
        public string Code { get; set; } = string.Empty;
        public JObject? Data { get; set; }
        public string Flag { get; set; } = string.Empty;
    }

    public enum TopologyFrom
    {
        Draft,
        DB,
    }
}
