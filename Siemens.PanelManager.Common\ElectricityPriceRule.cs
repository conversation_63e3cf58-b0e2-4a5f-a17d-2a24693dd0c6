﻿namespace Siemens.PanelManager.Common
{
    public class ElectricityPriceRule
    {
        // 计算电表的峰平谷电量以及电费
        // 苏州工业用电电价及峰平谷时间段：
        // 峰：8:00-11:00，17:00-22:00，电价：1.0347
        // 平：11:00-17:00，22:00-24:00，电价：0.6068
        // 谷：0:00-8:00，电价：0.2589
        //Peak-flat valley

        /// <summary>
        /// 峰时电价
        /// </summary>
        public const decimal PeakPrice = 1.0347M;

        /// <summary>
        /// 平时电价
        /// </summary>
        public const decimal FlatPrice = 0.6068M;

        /// <summary>
        /// 谷时电价
        /// </summary>
        public const decimal ValleyPrice = 0.2589M;
    }
}
