{"info": {"_postman_id": "c3e0dac3-d1e1-408c-89db-e798e495a036", "name": "W04使用超级管理员账号进入panel manager运维工单页面，点击新建工单，不选择设备or工单类型or处理时间or处理措施or不输入故障原因", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 6", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "获取所有资产详情 Copy 7", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "let Deviceid2 = pm.response.json().data[0].children[0].children[0].children[0].children[0].id//获取Areaid\r", "pm.environment.set('Deviceid2',Deviceid2)//把id保存到全局变量中\r", "\r", "pm.test(\"获取所有资产详情\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"id\",\"assetName\",\"assetNumber\",\"level\",\"location\",\"children\",\"type\",\"model\");\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Asset", "host": ["{{baseUrl}}"], "path": ["api", "v1", "<PERSON><PERSON>"]}}, "response": []}, {"name": "添加工单不选设备Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为40300\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(40300);\r", "});\r", "\r", "pm.test(\"设备添加失败\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"参数错误\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"device\": \"\",\r\n  \"workOrderType\": \"General\",\r\n  \"content\": \"string\",\r\n  \"processingTime\": \"2023-05-08T07:32:16.796Z\",\r\n  \"measure\": \"Check\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/workOrder/formData", "host": ["{{baseUrl}}"], "path": ["api", "v1", "workOrder", "formData"]}}, "response": []}, {"name": "添加工单不选工单类型Copy Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为40300\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(40300);\r", "});\r", "\r", "pm.test(\"设备添加失败\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"参数错误\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"device\": \"{{Deviceid2}}\",\r\n  \"workOrderType\": \"\",\r\n  \"content\": \"string\",\r\n  \"processingTime\": \"2023-05-08T07:32:16.796Z\",\r\n  \"measure\": \"Check\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/workOrder/formData", "host": ["{{baseUrl}}"], "path": ["api", "v1", "workOrder", "formData"]}}, "response": []}, {"name": "添加工单不选处理时间Copy Copy 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为40300\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(40300);\r", "});\r", "\r", "pm.test(\"设备添加失败\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"参数错误\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"device\": \"{{Deviceid2}}\",\r\n  \"workOrderType\": \"General\",\r\n  \"content\": \"string\",\r\n  \"processingTime\": \"\",\r\n  \"measure\": \"Check\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/workOrder/formData", "host": ["{{baseUrl}}"], "path": ["api", "v1", "workOrder", "formData"]}}, "response": []}, {"name": "添加工单不选处理措施Copy Copy 3", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为40300\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(40300);\r", "});\r", "\r", "pm.test(\"设备添加失败\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"参数错误\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"device\": \"{{Deviceid2}}\",\r\n  \"workOrderType\": \"General\",\r\n  \"content\": \"string\",\r\n  \"processingTime\": \"2023-05-08T07:32:16.796Z\",\r\n  \"measure\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/workOrder/formData", "host": ["{{baseUrl}}"], "path": ["api", "v1", "workOrder", "formData"]}}, "response": []}, {"name": "添加工单不输入故障原因Copy Copy 4", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为40300\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(40300);\r", "});\r", "\r", "pm.test(\"设备添加失败\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"参数错误\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"device\": \"{{Deviceid2}}\",\r\n  \"workOrderType\": \"General\",\r\n  \"content\": \"\",\r\n  \"processingTime\": \"2023-05-08T07:32:16.796Z\",\r\n  \"measure\": \"Check\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/workOrder/formData", "host": ["{{baseUrl}}"], "path": ["api", "v1", "workOrder", "formData"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}]}