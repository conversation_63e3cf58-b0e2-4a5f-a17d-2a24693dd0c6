﻿using SqlSugar;

namespace Siemens.PanelManager.Model.Database.Asset
{
    [SugarTable("asset_mqtt_data_point_config")]
    public class AssetMqttDataPointConfig : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public long Id { get; set; }

        [SugarColumn(ColumnName = "asset_id", IsNullable = false)]
        public int AssetId { get; set; }

        [SugarColumn(ColumnName = "config_type", IsNullable = false)]
        public GroupConfigType ConfigType { get; set; }

        [SugarColumn(ColumnName = "group_name", IsNullable = true, Length = 256)]
        public string? GroupName { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "code", IsNullable = true, Length = 256)]
        public string? Code { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "name", IsNullable = true, Length = 256)]
        public string? Name { get; set; } = string.Empty;

        [Uniqueness]
        [SugarColumn(ColumnName = "asset_model", Length = 50, IsNullable = true)]
        public string? AssetModel { get; set; }

        [SugarColumn(ColumnName = "sampling_period", IsNullable = true)]
        public int SamplingPeriod {  get; set; }

    }

    public enum GroupConfigType
    {
        Group = 1,
        Measurement
    }
}
