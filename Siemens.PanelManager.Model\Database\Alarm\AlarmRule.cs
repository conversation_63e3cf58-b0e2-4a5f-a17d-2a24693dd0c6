﻿using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Siemens.PanelManager.Model.Converter;
using Siemens.PanelManager.Model.Emun;
using SqlSugar;
using System.Text;

namespace Siemens.PanelManager.Model.Database.Alarm
{
    [SugarTable("alarm_rule")]
    public class AlarmRule : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "name", IsNullable = false, Length = 512)]
        public string Name { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "severity", IsNullable = false)]
        public AlarmSeverity Severity { get; set; }
        [SugarColumn(ColumnName = "details", IsNullable = true, Length = 1024)]
        public string Details { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "sections", ColumnDataType = "varchar(10240)", IsNullable = false)]
        public string SectionStr
        {
            get
            {
                return JsonConvert.SerializeObject(_sections);
            }
            set
            {
                try
                {
                    var sections = JsonConvert.DeserializeObject<List<RuleSection>>(value);
                    if (sections != null)
                    {
                        _sections = sections;
                    }
                    else
                    {
                        _sections = new List<RuleSection>();
                    }
                }
                catch
                {
                    _sections = new List<RuleSection>();
                }
            }
        }
        [SugarColumn(ColumnName = "target_type", IsNullable = false)]
        public AlarmTargetType TargetType { get; set; }
        [SugarColumn(ColumnName = "target_value", IsNullable = false, Length = 256)]
        public string TargetValue { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "is_enable", IsNullable = false)]
        public bool IsEnable { get; set; } = true;
        [SugarColumn(ColumnName = "is_delete", IsNullable = false)]
        public bool IsDelete { get; set; } = false;

        private List<RuleSection> _sections = new List<RuleSection>();
        [SugarColumn(IsIgnore = true)]
        public List<RuleSection> Sections
        {
            get
            {
                return _sections;
            }
            set
            {
                _sections = value;
            }
        }

        [SugarColumn(IsIgnore = true)]
        public string RuleInfo
        {
            get
            {
                var stringBuilder = new StringBuilder();
                foreach (var section in _sections)
                {
                    stringBuilder.Append(section.ToRuleStr());
                }

                return stringBuilder.ToString();
            }
        }
    }

    [JsonObject(NamingStrategyType = typeof(CamelCaseNamingStrategy))]
    public class RuleSection
    {
        public string? PointName { get; set; } 
        public string Point { get; set; }= string.Empty;
        [JsonConverter(typeof(CompareConverter))]
        public Compare Compare { get; set; }
        [JsonConverter(typeof(LogicalOperatorConverter))]
        [JsonProperty("andOr")]
        public LogicalOperator LogicalOperator { get; set; }
        public string DataValue { get; set; } = string.Empty;

        public string ToRuleStr()
        {
            return $"{Point} {Compare.ToCompareString()} {DataValue} {LogicalOperator.ToLogicalOperatorString()}";
        }
    }

    public enum AlarmSeverity : int
    { 
        Low = 0,
        Middle = 10,
        High = 20,
    }

    public enum AlarmTargetType : int
    {
        Device = 0,
        DeviceModel = 1,
        Circuit = 2,
        CircuitModel = 3,
        Panel = 4,
        PanelModel = 5,
        Substation = 6,
        Transformer = 7,
    }
}
