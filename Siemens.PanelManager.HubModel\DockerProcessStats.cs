﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.HubModel
{
    public class DockerProcessStats
    {
        public string BlockIO { get; set; }=string.Empty;
        public string CPUPerc { get; set; }=string.Empty;
        public string Container { get; set; }=string.Empty;
        public string ID { get; set; }= string.Empty;
        public string MemPerc { get; set; }=string.Empty;
        public string MemUsage { get; set; }=string.Empty;
        public string Name { get; set; } = string.Empty;
        public string NetIO { get; set; }=string.Empty;
        public string PIDs { get; set; } = string.Empty;
    }
}
