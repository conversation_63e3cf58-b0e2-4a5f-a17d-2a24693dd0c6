using Akka.Util.Internal;
using Mapster;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components.Server.Circuits;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.Excel;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Common.Http;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.Common.Model;
using Siemens.PanelManager.Common.mqtt;
using Siemens.PanelManager.Common.UdcService;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Job.ModbusDeviceWorker;
using Siemens.PanelManager.Job.Mqtt;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Chart;
using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.Model.Database.Topology;
using Siemens.PanelManager.Model.Database.UdcScanData;
using Siemens.PanelManager.Model.Job;
using Siemens.PanelManager.Model.Topology3D;
using Siemens.PanelManager.Model.UDC;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.Server.Asset;
using Siemens.PanelManager.Server.AssetDataPoint;
using Siemens.PanelManager.Server.BreakerProtect;
using Siemens.PanelManager.Server.Common;
using Siemens.PanelManager.Server.DataPoint;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;
using Swashbuckle.AspNetCore.SwaggerGen;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks.Sources;
using TouchSocket.Core;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public partial class AssetController : SiemensApiControllerBase, IDisposable
    {

        private DataPointServer _dataPointServer => _provider.GetRequiredService<DataPointServer>();
        private SiemensExcelHelper _excelHelper => _provider.GetRequiredService<SiemensExcelHelper>();
        private JobManager _jobManager => _provider.GetRequiredService<JobManager>();
        private AssetExtendServer _extendServer => _provider.GetRequiredService<AssetExtendServer>();
        private TempFileManager _tempFileManager => _provider.GetRequiredService<TempFileManager>();
        private ILogger<AssetController> _log;
        private ISqlSugarClient _client;
        private IServiceProvider _provider;
        private SiemensCache _cache;
        private IConfiguration _configuration;
        private AlarmExtendServer _alarmExtendServer => _provider.GetRequiredService<AlarmExtendServer>();
        private IHttpClientFactory _httpClientFactory => _provider.GetRequiredService<IHttpClientFactory>();
        private readonly UdcHttpService _udcHttpService;

        private const string AssetCurrentStatusCacheKey = "AssetStatus:Currently-{0}";

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="cache"></param>
        /// <param name="log"></param>
        /// <param name="provider"></param>
        /// <param name="configuration"></param>
        /// <param name="udcHttpService"></param>
        public AssetController(SiemensCache cache,
            ILogger<AssetController> log,
            IServiceProvider provider,
            IConfiguration configuration,
            UdcHttpService udcHttpService)
            : base(provider, cache)
        {
            _client = provider.GetService<ISqlSugarClient>()!;
            _log = log;
            _provider = provider;
            _cache = cache;
            _configuration = configuration;
            _udcHttpService = udcHttpService;
        }

        [HttpGet]
        [SwaggerOperation(Summary = "Swagger_Asset_SelectAll", Description = "Swagger_Asset_SelectAll_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<AssetInfoModel[]>> GetAll()
        {
            var assets = await _client.Queryable<AssetInfo>()
                .InnerJoin<AssetRelation>((a, ar) => a.Id == ar.ChildId)
                .Where((a, ar) => ar.ParentId == 0).Select<AssetInfo>().ToListAsync();

            assets = assets.OrderBy(a => a.SortNoNotNull).ToList();
            var assetInfoModels = new List<AssetInfoModel>(assets.Count);

            var connectStatus = _cache.Get<Dictionary<int, bool>>("AssetConnectStatus:All");

            foreach (var asset in assets)
            {
                var ar = await _client.Queryable<AssetRelation>().ToChildListAsync(a => a.ParentId, asset.Id);
                ar = ar.OrderByDescending(a => a.AssetLevel).ToList();
                var model = asset.GetAssetInfoModel(_client.Queryable<AssetInfo>(),
                    ar,
                    _client.Queryable<TopologyInfo>(),
                    connectStatus ?? new Dictionary<int, bool>(),
                    _client.Queryable<FileManager>());
                assetInfoModels.Add(model);

                if (connectStatus != null &&
                    connectStatus.TryGetValue(asset.Id, out bool hasConnect))
                {
                    model.HasConnect = hasConnect ? "1" : "0";
                }
            }

            return new ResponseBase<AssetInfoModel[]>()
            {
                Code = 20000,
                Data = assetInfoModels.ToArray()
            };
        }

        [HttpGet("AllDeviceConnectStatus")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<List<AssetConnectStatus>>> GetAllDeviceConnectStatus()
        {
            var assets = await _client.Queryable<AssetInfo>()
                .Where(a => a.AssetLevel == AssetLevel.Device)
                .Select(a => new AssetConnectStatus { Id = a.Id })
                .ToListAsync();

            if (assets != null)
            {
                var connectStatus = _cache.Get<Dictionary<int, bool>>("AssetConnectStatus:All");

                foreach (var asset in assets)
                {
                    if (connectStatus != null &&
                        connectStatus.TryGetValue(asset.Id, out bool hasConnect))
                    {
                        asset.HasConnect = hasConnect ? "1" : "0";
                    }
                }
            }
            else
            {
                assets = new List<AssetConnectStatus>();
            }


            return new ResponseBase<List<AssetConnectStatus>>()
            {
                Code = 20000,
                Data = assets
            };
        }

        [HttpGet("{assetId}")]
        [SwaggerOperation(Summary = "Swagger_Asset_Select", Description = "Swagger_Asset_Select_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<AssetInfoModel>> Get(int assetId)
        {
            if (assetId <= 0)
            {
                return new ResponseBase<AssetInfoModel>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var assetInfo = await _client.Queryable<AssetInfo>().Where(a => a.Id == assetId).FirstAsync();
            if (assetInfo == null)
            {
                return new ResponseBase<AssetInfoModel>()
                {
                    Code = 40400,
                    Message = MessageContext.AssetNotExists
                };
            }
            var children = await _client.Queryable<AssetRelation>().ToChildListAsync(a => a.ParentId, assetId);
            var fileQuery = _client.Queryable<FileManager>();
            var connectStatus = _cache.Get<Dictionary<int, bool>>("AssetConnectStatus:All");
            var model = assetInfo.GetAssetInfoModel(_client.Queryable<AssetInfo>(), children, _client.Queryable<TopologyInfo>(), connectStatus, fileQuery);

            return new ResponseBase<AssetInfoModel>()
            {
                Code = 20000,
                Data = model
            };
        }

        /// <summary>
        /// 发送树形结构到mqtt中的信息
        /// </summary>
        private async Task SendAssetTree()
        {
            try
            {
                var assetInfo = await _client.Queryable<AssetInfo>().Where(a => string.IsNullOrEmpty(a.AssetNumber) && a.AssetLevel == AssetLevel.Area).FirstAsync();
                if (assetInfo == null)
                {
                    throw new Exception($"资产信息为空");
                }

                var children = await _client.Queryable<AssetRelation>().ToChildListAsync(a => a.ParentId, assetInfo.Id);
                var fileQuery = _client.Queryable<FileManager>();
                var connectStatus = _cache.Get<Dictionary<int, bool>>("AssetConnectStatus:All");
                var treeData = assetInfo.GetAssetInfoModel(_client.Queryable<AssetInfo>(), children, _client.Queryable<TopologyInfo>(), connectStatus, fileQuery, false);

                //构建传输数据结构
                var jsonObj = new
                {
                    item_id = assetInfo.Id,
                    item_name = "",
                    timestamp = DateTime.Now,
                    dataType = "AssetTree",
                    count = 1,
                    total = 1,
                    _embedded = treeData
                };

                var action = _provider.GetRequiredService<ExternalMqttAction>();

                //查看传输数据是否成功
                action.SendDataAsync("AssetTree", JsonConvert.SerializeObject(jsonObj));
            }
            catch (Exception ex)
            {
                _log.LogError("SendAssetTree异常信息:" + ex.Message);
            }
        }

        [HttpPost]
        [SwaggerOperation(Summary = "Swagger_Asset_Insert", Description = "Swagger_Asset_Insert_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<SimpleAssetInfo>> Add(AssetOperation<AssetInfoModel, int> param)
        {
            if (param.AssetData == null
               || string.IsNullOrWhiteSpace(param.AssetData.Level)
               || !Enum.TryParse(param.AssetData.Level, out AssetLevel assetLevel))
            {
                return new ResponseBase<SimpleAssetInfo>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var asset = param.AssetData.ToAssetInfo();
            asset.CreatedBy = UserName;
            asset.CreatedTime = DateTime.Now;
            asset.UpdatedBy = UserName;
            asset.UpdatedTime = DateTime.Now;

            if (param.AssetData.Img != null)
            {
                var imgIds = new List<int>(param.AssetData.Img.Length);
                foreach (var i in param.AssetData.Img)
                {
                    imgIds.Add(i.Id);
                }
                asset.ImageIds = imgIds.ToArray();
            }

            asset.UpdatedBy = UserName;
            asset.UpdatedTime = DateTime.Now;

            int assetId = 0;
            var assetRelation = new AssetRelation();
            assetRelation.CreatedBy = UserName;
            assetRelation.AssetLevel = asset.AssetLevel;
            assetRelation.CreatedTime = DateTime.Now;
            assetRelation.ParentId = param.To.HasValue ? param.To.Value : 0;
            assetRelation.UpdatedBy = UserName;
            assetRelation.UpdatedTime = DateTime.Now;
            assetRelation.ChildId = assetId;
            var errorCode = new List<string>();
            if (!await _extendServer.VerifyAssetInfo(asset, errorCode, _client))
            {
                var message = new StringBuilder();
                errorCode.ForEach(e => message.AppendLine(MessageContext.GetErrorValue(e)));

                return new ResponseBase<SimpleAssetInfo>()
                {
                    Code = 40301,
                    Message = message.ToString()
                };
            }
            try
            {
                _client.Ado.BeginTran();
                if (await _extendServer.VerifyAssetRelation(assetRelation, client: _client))
                {
                    assetId = await _client.Insertable(asset).ExecuteReturnIdentityAsync();
                    asset.Id = assetId;
                    await _extendServer.AddPannelSystemHealthCheckList(asset, _client);
                    await _extendServer.AddDeviceInputOutputList(asset, _client);
                    assetRelation.ChildId = assetId;
                    await _client.Insertable(assetRelation).ExecuteCommandAsync();
                    if ("Modbus".Equals(param.AssetData.Model, StringComparison.OrdinalIgnoreCase) && param.AssetData.Type == "Gateway" && !string.IsNullOrEmpty(param.AssetData.Port) && param.AssetData.Port.Length > 0)
                    {
                        var portInfo = new AssetThirdGateway
                        {
                            Name = "串口1",
                            Port = param.AssetData.Port,
                            AssetId = asset.Id,
                            CreatedBy = UserName,
                            CreatedTime = DateTime.Now,
                            UpdatedBy = UserName,
                            UpdatedTime = DateTime.Now
                        };
                        await _client.Insertable(portInfo).ExecuteCommandAsync();

                    }

                    if (!string.IsNullOrEmpty(asset.ThirdPartCode)
                        && !string.IsNullOrEmpty(asset.AssetType)
                        && !string.IsNullOrEmpty(asset.AssetModel)
                        && asset.AssetLevel == AssetLevel.Device
                        && _extendServer.IsGeneralEquipment(asset.AssetModel, asset.AssetType))
                    {
                        await UpdateUniversalConfigByAsset(asset.ThirdPartCode, new List<AssetInfo>() { asset });
                    }

                    if (param.AssetData.RatedVoltage.HasValue || param.AssetData.RatedCurrent.HasValue)
                    {
                        var assetDeviceInfo = new DeviceDetails
                        {
                            AssetId = asset.Id,
                            RatedCurrent = param.AssetData.RatedCurrent,
                            RatedVoltage = param.AssetData.RatedVoltage,
                            RatedPower = param.AssetData.RatedPower,
                            CreatedBy = UserName,
                            CreatedTime = DateTime.Now,
                            UpdatedBy = UserName,
                            UpdatedTime = DateTime.Now,
                        };

                        await _client.Insertable(assetDeviceInfo).ExecuteCommandAsync();
                    }
                    await _alarmExtendServer.InsertOperationLog(UserName, "AddAsset", Model.Database.Alarm.AlarmSeverity.Middle, _client, asset.AssetName);
                    _client.Ado.CommitTran();

                    await SendAssetTree();
                }
                else
                {
                    _client.Ado.RollbackTran();
                    return new ResponseBase<SimpleAssetInfo>()
                    {
                        Code = 40302,
                        Message = MessageContext.ErrorInvaildAssetLevel
                    };
                }
            }
            catch (Exception ex)
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "更新资产失败");
                return new ResponseBase<SimpleAssetInfo>()
                {
                    Code = 50000,
                    Message = MessageContext.ServerException
                };
            }

            return new ResponseBase<SimpleAssetInfo>()
            {
                Code = 20000,
                Data = new SimpleAssetInfo()
                {
                    AssetId = assetId,
                }
            };
        }

        [HttpPut]
        [SwaggerOperation(Summary = "Swagger_Asset_Update", Description = "Swagger_Asset_Update_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> Update(AssetOperation<AssetInfoModel, int> param)
        {
            if (param.AssetData == null
                || param.AssetData.Id <= 0
                || string.IsNullOrWhiteSpace(param.AssetData.Level)
                || !Enum.TryParse(param.AssetData.Level, out AssetLevel assetLevel))
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var asset = await _client.Queryable<AssetInfo>()
                .Where(a => a.Id == param.AssetData.Id)
                .FirstAsync();

            if (asset == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.AssetNotExists
                };
            }

            AssetRelation? assetRelation = null;

            if (assetLevel != asset.AssetLevel)
            {
                asset.AssetLevel = assetLevel;
                assetRelation = await _client.Queryable<AssetRelation>()
                    .Where(ar => ar.ChildId == param.AssetData.Id)
                    .FirstAsync();
                bool canUpdate = false;
                if (assetRelation != null)
                {
                    assetRelation.AssetLevel = assetLevel;
                    assetRelation.UpdatedBy = UserName;
                    assetRelation.UpdatedTime = DateTime.Now;
                    if (await _extendServer.VerifyAssetRelation(assetRelation, client: _client))
                    {
                        canUpdate = true;
                    }
                }

                if (!canUpdate)
                {
                    return new ResponseBase<string>()
                    {
                        Code = 40302,
                        Message = MessageContext.ErrorInvaildAssetLevel
                    };
                }
            }
            param.AssetData.ToAssetInfo(asset);
            asset.UpdatedBy = UserName;
            asset.UpdatedTime = DateTime.Now;

            var errorCode = new List<string>();
            if (!await _extendServer.VerifyAssetInfo(asset, errorCode, _client))
            {
                var message = new StringBuilder();
                errorCode.ForEach(e => message.AppendLine(MessageContext.GetErrorValue(e)));

                return new ResponseBase<string>()
                {
                    Code = 40301,
                    Message = message.ToString()
                };
            }

            try
            {
                _client.Ado.BeginTran();
                await _client.Updateable(asset).ExecuteCommandAsync();
                if (asset.AssetLevel == AssetLevel.Device)
                {
                    if ("Modbus".Equals(param.AssetData.Model, StringComparison.OrdinalIgnoreCase) && param.AssetData.Type == "Gateway" && !string.IsNullOrEmpty(param.AssetData.Port) && param.AssetData.Port.Length > 0)
                    {
                        var portInfo = await _client.Queryable<AssetThirdGateway>().FirstAsync(dd => dd.AssetId == asset.Id);
                        if (portInfo == null)
                        {
                            portInfo = new AssetThirdGateway
                            {
                                Name = "串口1",
                                Port = param.AssetData.Port,
                                AssetId = asset.Id,
                                CreatedBy = UserName,
                                CreatedTime = DateTime.Now,
                                UpdatedBy = UserName,
                                UpdatedTime = DateTime.Now
                            };
                            await _client.Insertable(portInfo).ExecuteCommandAsync();
                        }
                        else
                        {
                            portInfo.Port = param.AssetData.Port;
                            portInfo.UpdatedTime = DateTime.Now;
                            portInfo.UpdatedBy = UserName;
                            await _client.Updateable(portInfo).ExecuteCommandAsync();
                        }
                    }
                    if (param.AssetData.RatedVoltage.HasValue
                        || param.AssetData.RatedCurrent.HasValue
                        || param.AssetData.RatedPower.HasValue
                        || param.AssetData.MeterSite.HasValue)
                    {
                        var assetDeviceInfo = await _client.Queryable<DeviceDetails>().FirstAsync(dd => dd.AssetId == asset.Id);
                        if (assetDeviceInfo == null)
                        {
                            assetDeviceInfo = new DeviceDetails
                            {
                                AssetId = asset.Id,
                                CreatedBy = UserName,
                                CreatedTime = DateTime.Now,
                            };
                        }

                        assetDeviceInfo.RatedVoltage = param.AssetData.RatedVoltage;
                        assetDeviceInfo.RatedPower = param.AssetData.RatedPower;
                        if (!assetDeviceInfo.RatedCurrent.HasValue && param.AssetData.RatedCurrent.HasValue)
                        {
                            assetDeviceInfo.RatedCurrent = param.AssetData.RatedCurrent;
                        }
                        assetDeviceInfo.MeterSite = param.AssetData.MeterSite;

                        assetDeviceInfo.UpdatedBy = UserName;
                        assetDeviceInfo.UpdatedTime = DateTime.Now;
                        await _client.Storageable(assetDeviceInfo).ExecuteCommandAsync();
                    }
                    else
                    {
                        var assetDeviceInfo = await _client.Queryable<DeviceDetails>().FirstAsync(dd => dd.AssetId == asset.Id);
                        if (assetDeviceInfo != null)
                        {
                            assetDeviceInfo.RatedVoltage = null;
                            assetDeviceInfo.UpdatedBy = UserName;
                            assetDeviceInfo.UpdatedTime = DateTime.Now;
                            await _client.Updateable(assetDeviceInfo).ExecuteCommandAsync();
                        }
                    }

                    if (!string.IsNullOrEmpty(asset.ThirdPartCode)
                        && !string.IsNullOrEmpty(asset.AssetType)
                        && !string.IsNullOrEmpty(asset.AssetModel)
                        && _extendServer.IsGeneralEquipment(asset.AssetModel, asset.AssetType))
                    {
                        await UpdateUniversalConfigByAsset(asset.ThirdPartCode, new List<AssetInfo>() { asset });
                    }

                    if (string.IsNullOrEmpty(asset.ThirdPartCode))
                    {
                        var bitConfigIds = await _client.Queryable<UniversalDeviceConfig>()
                            .Where(c => c.AssetId == asset.Id && c.IsBit).Select(c => c.Id)
                            .ToListAsync();

                        await _client.Deleteable<BitConfig>()
                            .Where(b => bitConfigIds.Contains(b.UniversalDeviceConfigId))
                            .ExecuteCommandAsync();

                        await _client.Deleteable<UniversalDeviceConfig>()
                            .Where(b => b.AssetId == asset.Id)
                            .ExecuteCommandAsync();
                    }
                }

                await _alarmExtendServer.InsertOperationLog(UserName, "UpdateAsset", Model.Database.Alarm.AlarmSeverity.Middle, _client, asset.AssetName);
                if (assetRelation != null)
                {
                    await _client.Updateable(assetRelation).ExecuteCommandAsync();
                }
                _client.Ado.CommitTran();

                await SendAssetTree();
            }
            catch (Exception ex)
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "更新资产失败");
                return new ResponseBase<string>()
                {
                    Code = 50000,
                    Message = MessageContext.ServerException
                };
            }

            return new ResponseBase<string>()
            {
                Code = 20000,
                Message = MessageContext.Success
            };
        }

        [HttpDelete("{assetId}")]
        [SwaggerOperation(Summary = "Swagger_Asset_Delete", Description = "Swagger_Asset_Delete_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> Delete(int assetId)
        {
            if (assetId <= 0)
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }
            var asset = await _client.Queryable<AssetInfo>().FirstAsync(a => a.Id == assetId);
            if (asset == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.AssetNotExists
                };
            }

            try
            {
                _client.Ado.BeginTran();
                await _extendServer.DeleteAsset(assetId, _client, UserName);
                await _alarmExtendServer.InsertOperationLog(UserName, "DeleteAsset", Model.Database.Alarm.AlarmSeverity.Middle, _client, asset.AssetName);
                _client.Ado.CommitTran();

                await SendAssetTree();
            }
            catch (Exception ex)
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "删除资源失败");
                return new ResponseBase<string>()
                {
                    Code = 50000,
                    Message = MessageContext.ServerException
                };
            }

            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = MessageContext.Success
            };
        }

        [HttpDelete("batch")]
        [SwaggerOperation(Summary = "Swagger_Asset_Delete", Description = "Swagger_Asset_Delete_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> Delete(string assetIds)
        {
            if (string.IsNullOrEmpty(assetIds))
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var ids = assetIds.Split(',').Select(o =>
            {
                int v = 0;
                int.TryParse(o, out v);
                return v;
            }).ToArray();

            try
            {
                _client.Ado.BeginTran();
                var server = _alarmExtendServer;
                foreach (var id in ids)
                {
                    var asset = await _client.Queryable<AssetInfo>().FirstAsync(a => a.Id == id);
                    if (asset != null)
                    {
                        await _extendServer.DeleteAsset(id, _client, UserName);
                        await server.InsertOperationLog(UserName, "DeleteAsset", Model.Database.Alarm.AlarmSeverity.Middle, _client, asset.AssetName);
                    }
                }
                _client.Ado.CommitTran();

                await SendAssetTree();
            }
            catch (Exception ex)
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "删除资源失败");
                return new ResponseBase<string>()
                {
                    Code = 50000,
                    Message = MessageContext.ServerException
                };
            }

            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = MessageContext.Success
            };
        }

        [HttpPost("copy")]
        [SwaggerOperation(Summary = "Swagger_Asset_Copy", Description = "Swagger_Asset_Copy_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> Copy(AssetOperation<string, int[]> param)
        {
            if (param == null
                || !param.To.HasValue
                || param.To <= 0
                || param.From == null
                || param.From.Length <= 0)
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var parant = await _client.Queryable<AssetRelation>().FirstAsync(ar => ar.ChildId == param.To.Value);
            if (parant == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.AssetNotExists
                };
            }

            param.From = param.From.Distinct().ToArray();

            var assets = await _client.Queryable<AssetInfo>().Where(a => param.From.Contains(a.Id)).ToArrayAsync();
            var assetRelations = await _client.Queryable<AssetRelation>().Where(ar => param.From.Contains(ar.ChildId)).ToListAsync();

            if (assetRelations.Count == 0
                || assets.Length == 0
                || assetRelations.Count != param.From.Length
                || assets.Length != param.From.Length)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.AssetNotExists
                };
            }

            #region 树形结构重排
            List<AssetRelation> relations = new List<AssetRelation>();
            for (var i = 0; i < assets.Length; i++)
            {
                var assetRelation = assetRelations[i];
                if (assetRelation.ParentId != 0)
                {
                    var parentAssetRelation = assetRelations.FirstOrDefault(ar => ar.ChildId == assetRelation.ParentId);
                    if (parentAssetRelation != null)
                    {
                        if (parentAssetRelation.Children == null)
                        {
                            parentAssetRelation.Children = new List<AssetRelation>();
                        }

                        parentAssetRelation.Children.Add(assetRelation);
                        continue;
                    }
                }
                assetRelation.ParentId = param.To.Value;
                relations.Add(assetRelation);
            }

            foreach (var assetRelation in relations)
            {
                if (!await _extendServer.VerifyAssetRelation(assetRelation, parant, new AssetRelation[0]))
                {
                    return new ResponseBase<string>()
                    {
                        Code = 40302,
                        Message = MessageContext.ErrorInvaildAssetLevel
                    };
                }
            }
            #endregion

            try
            {
                _client.Ado.BeginTran();
                int parentId = param.To.Value;
                await CopyAsset(assets, relations, parentId);
                await _alarmExtendServer.InsertOperationLog(UserName, "CopyAsset", Model.Database.Alarm.AlarmSeverity.Middle, _client);
                _client.Ado.CommitTran();

                await SendAssetTree();
            }
            catch (Exception ex)
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "拷贝失败");
                return new ResponseBase<string>()
                {
                    Code = 50000,
                    Message = MessageContext.ServerException,
                };
            }

            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = MessageContext.Success,
            };
        }

        private async Task CopyAsset(AssetInfo[] assets, List<AssetRelation> relations, int parentId)
        {
            foreach (var assetRelation in relations)
            {
                var assetInfo = assets.FirstOrDefault(a => a.Id == assetRelation.ChildId);

                if (assetInfo != null)
                {
                    var assetName = string.IsNullOrEmpty(assetInfo.AssetName) ? assetInfo.AssetName : assetInfo.AssetName + "-Copy";
                    var assetNumber = string.IsNullOrEmpty(assetInfo.AssetNumber) ? assetInfo.AssetNumber : assetInfo.AssetNumber + "-Copy";
                    int count = 0;
                    count = await _client.Queryable<AssetInfo>().Where(a => a.AssetName.Contains(assetName)).Select(a => SqlFunc.RowCount()).FirstAsync();
                    if (count > 0)
                    {
                        assetName = $"{assetName}{count + 1}";
                    }

                    count = await _client.Queryable<AssetInfo>().Where(a => a.AssetNumber.Contains(assetNumber)).Select(a => SqlFunc.RowCount()).FirstAsync();

                    if (count > 0)
                    {
                        assetNumber = $"{assetNumber}{count + 1}";
                    }

                    var newAssetInfo = new AssetInfo()
                    {
                        AssetLevel = assetInfo.AssetLevel,
                        AssetName = assetName,
                        AssetType = assetInfo.AssetType,
                        AssetMaker = assetInfo.AssetMaker,
                        AssetModel = assetInfo.AssetModel,
                        AssetNumber = assetNumber,
                        Description = assetInfo.Description,
                        InstallDate = assetInfo.InstallDate,
                        Location = assetInfo.Location,
                        MeterType = assetInfo.MeterType,
                        MLFB = assetInfo.MLFB,
                        ImageIdsStr = assetInfo.ImageIdsStr,
                        DrawingFileIdsStr = assetInfo.DrawingFileIdsStr,
                        TopologyId = assetInfo.TopologyId,
                        PointData = assetInfo.PointData,
                        UseScene = assetInfo.UseScene,
                        Principal = assetInfo.Principal,
                        Telephone = assetInfo.Telephone,
                        CircuitName = assetInfo.CircuitName,
                        CreatedBy = UserName,
                        CreatedTime = DateTime.Now,
                        UpdatedBy = UserName,
                        UpdatedTime = DateTime.Now,
                    };

                    var assetId = await _client.Insertable(newAssetInfo).ExecuteReturnIdentityAsync();

                    newAssetInfo.Id = assetId;
                    await _extendServer.AddPannelSystemHealthCheckList(newAssetInfo, _client);
                    var newAssetRelation = new AssetRelation()
                    {
                        ParentId = parentId,
                        ChildId = assetId,
                        AssetLevel = assetInfo.AssetLevel,
                        CreatedBy = UserName,
                        CreatedTime = DateTime.Now,
                        UpdatedBy = UserName,
                        UpdatedTime = DateTime.Now,
                    };
                    await _client.Insertable(newAssetRelation).ExecuteCommandAsync();

                    if (assetRelation.Children != null)
                    {
                        await CopyAsset(assets, assetRelation.Children, assetId);
                    }
                }
            }
        }

        [HttpPost("move")]
        [SwaggerOperation(Summary = "Swagger_Asset_Move", Description = "Swagger_Asset_Move_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> Move(AssetOperation<string, int> param)
        {
            if (param == null
                || !param.To.HasValue
                || param.To <= 0
                || param.From <= 0)
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var assets = await _client.Queryable<AssetInfo>().Where(a => a.Id == param.From || a.Id == param.To).ToArrayAsync();
            if (assets.Length != 2)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.AssetNotExists
                };
            }

            var currentAsset = assets.First(a => a.Id == param.From);

            List<AssetRelation> assetRelations = new List<AssetRelation>();
            var assetRelation = await _client.Queryable<AssetRelation>().Where(ar => ar.ChildId == param.From).FirstAsync();

            if (assetRelation == null)
            {
                assetRelation = new AssetRelation();
                assetRelation.ChildId = param.From;
                assetRelation.AssetLevel = currentAsset.AssetLevel;
                assetRelation.CreatedBy = UserName;
                assetRelation.CreatedTime = DateTime.Now;
            }

            var newParentId = param.To.Value;
            var children = await _client.Queryable<AssetRelation>().ToChildListAsync(ar => ar.ParentId, assetRelation.ChildId);
            var currentlyItem = children.FirstOrDefault(c => c.ChildId == assetRelation.ChildId);
            if (currentlyItem != null)
            {
                children.Remove(currentlyItem);
            }

            var parent = children.FirstOrDefault(ar => ar.ChildId == newParentId);
            // 向树下层节点移动
            // 仅仅移动自己本身，子节点不移动
            if (parent != null)
            {
                var needChangeAssetRelations = children.Where(ar => ar.ParentId == assetRelation.ChildId).ToArray();
                foreach (var ar in needChangeAssetRelations)
                {
                    ar.ParentId = assetRelation.ParentId;
                    ar.UpdatedBy = UserName;
                    ar.UpdatedTime = DateTime.Now;
                    assetRelations.Add(ar);
                }
                children = new List<AssetRelation>();
            }
            else
            {
                parent = await _client.Queryable<AssetRelation>().FirstAsync(ar => ar.ChildId == newParentId);
            }

            assetRelation.ParentId = newParentId;

            assetRelations.Add(assetRelation);
            if (await _extendServer.VerifyAssetRelation(assetRelation,
                parentRetaion: parent,
                children: children.ToArray()))
            {
                assetRelation.UpdatedBy = UserName;
                assetRelation.UpdatedTime = DateTime.Now;
                try
                {
                    _client.Ado.BeginTran();
                    await _client.Storageable(assetRelations).ExecuteCommandAsync();
                    await _alarmExtendServer.InsertOperationLog(UserName, "MoveAsset", Model.Database.Alarm.AlarmSeverity.Middle, _client);
                    _client.Ado.CommitTran();

                    await SendAssetTree();

                    return new ResponseBase<string>()
                    {
                        Code = 20000,
                        Data = MessageContext.Success
                    };
                }
                catch (Exception ex)
                {
                    _client.Ado.RollbackTran();
                    _log.LogError(ex, "移动失败");
                    return new ResponseBase<string>()
                    {
                        Code = 50000,
                        Message = MessageContext.ServerException,
                    };
                }
            }
            return new ResponseBase<string>()
            {
                Code = 40302,
                Message = MessageContext.ErrorInvaildAssetLevel
            };
        }
        [HttpPost("gateway/ports")]
        [SwaggerOperation(Summary = "Swagger_Asset_SaveModbusGatewayPorts", Description = "Swagger_Asset_SaveModbusGatewayPorts_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> SaveModbusGatewayPorts(List<ModbusGatewayPort> modbusGatewayList)
        {
            if (modbusGatewayList.Count == 0)
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }
            // 根据portid,是否已存在于数据库中，若不存在，则新增，若存在则修改，若db有，modbusGatewayList没有，则删除这个
            int assetId = modbusGatewayList.First().AssetId;
            var oldPortList = await _client.Queryable<AssetThirdGateway>().Where(a => a.AssetId == assetId).ToArrayAsync();
            var newIds = modbusGatewayList.Select(a => a.Id).ToList();
            try
            {
                _client.Ado.BeginTran();
                foreach (var old in oldPortList)
                {
                    if (!newIds.Contains(old.Id))
                    {
                        await _client.Deleteable(old).ExecuteCommandAsync();
                    }
                }
                foreach (var modbusGateway in modbusGatewayList)
                {
                    if (string.IsNullOrEmpty(modbusGateway.Port) || string.IsNullOrEmpty(modbusGateway.Name))
                    {
                        return new ResponseBase<string>()
                        {
                            Code = 40300,
                            Message = MessageContext.ErrorParam
                        };
                    }
                    var gatewayInfo = await _client.Queryable<AssetInfo>().Where(a => a.Id == modbusGateway.AssetId).SingleAsync();

                    if (gatewayInfo == null)
                    {
                        return new ResponseBase<string>()
                        {
                            Code = 40000,
                            Message = MessageContext.AssetNotExists
                        };
                    }
                    int portId = modbusGateway.Id;
                    var port = await _client.Queryable<AssetThirdGateway>().Where(a => a.Id == modbusGateway.Id).FirstAsync();

                    if (port != null)
                    {

                        port.Name = modbusGateway.Name;
                        port.Port = modbusGateway.Port;
                        port.UpdatedBy = UserName;
                        port.UpdatedTime = DateTime.Now;
                        await _client.Updateable(port).ExecuteCommandAsync();
                    }
                    else
                    {
                        AssetThirdGateway newPort = new AssetThirdGateway();
                        newPort.Port = modbusGateway.Port;
                        newPort.Name = modbusGateway.Name;
                        newPort.AssetId = modbusGateway.AssetId;
                        newPort.UpdatedBy = UserName;
                        newPort.UpdatedTime = DateTime.Now;
                        newPort.CreatedTime = DateTime.Now;
                        newPort.CreatedBy = UserName;
                        portId = await _client.Insertable(newPort).ExecuteReturnIdentityAsync();
                    }
                }
                _client.Ado.CommitTran();

            }
            catch (Exception)
            {
                _client.Ado.RollbackTran();
                return new ResponseBase<string>() { Code = 50000, Message = MessageContext.ServerException };
            }

            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = MessageContext.Success
            };
        }
        [HttpDelete("gateway")]
        [SwaggerOperation(Summary = "Swagger_Gateway_Port_Delete", Description = "Swagger_Gateway_Port_Delete_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> DeletePort(int portId)
        {
            if (portId <= 0)
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }
            var port = await _client.Queryable<AssetThirdGateway>().FirstAsync(a => a.Id == portId);
            if (port == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.AssetNotExists
                };
            }
            await _client.Deleteable(port).ExecuteCommandAsync();

            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = MessageContext.Success
            };
        }
        [HttpGet("gateway/{assetId}/ports")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetModbusGatewayPorts", Description = "Swagger_Asset_GetModbusGatewayPorts_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<List<ModbusGatewayPort>>> GetModbusGatewayPorts(int assetId)
        {
            List<ModbusGatewayPort> gatewayPorts = new List<ModbusGatewayPort>();
            var ports = await _client.Queryable<AssetThirdGateway>().Where(a => a.AssetId == assetId).ToListAsync();
            if (ports.Any())
            {
                foreach (var a in ports)
                {
                    var gatewayInfo = await _client.Queryable<AssetInfo>().Where(c => c.Id == assetId).FirstAsync();


                    ModbusGatewayPort port = new ModbusGatewayPort
                    {
                        Port = gatewayInfo.Port,
                        Name = a.Name,
                        Id = a.Id,
                        AssetId = a.AssetId ?? 0
                    };
                    var subAssets = await _client.Queryable<AssetInfo>()
                                        .Where(p => SqlFunc.Contains(p.IPAddress, gatewayInfo.IPAddress))
                                        .Where(p => p.Id != gatewayInfo.Id)
                                        .Where(p => !SqlFunc.IsNullOrEmpty(p.ObjectId))
                                        .Where(p => p.Port == gatewayInfo.Port)
                                        .ToListAsync();
                    var gatewaySubDevices = new List<GatewaySubDevice>();

                    if (subAssets.Any())
                    {
                        var connectStatus = _cache.Get<Dictionary<int, bool>>("AssetConnectStatus:All");

                        GatewaySubDevice subDevice;
                        subAssets.ForEach(s =>
                        {
                            subDevice = new();
                            if (connectStatus != null)
                            {
                                subDevice.ConnectStatus = connectStatus.GetValueOrDefault(s.Id) ? 1 : 0;
                            }
                            gatewaySubDevices.Add(subDevice);
                        });
                    }
                    if (gatewaySubDevices != null)
                    {
                        port.ConnectStatus = gatewaySubDevices.Where(a => a.ConnectStatus == 1).Any() ? 1 : 0;
                    }
                    gatewayPorts.Add(port);
                }
            }

            return new ResponseBase<List<ModbusGatewayPort>>()
            {
                Code = 20000,
                Data = gatewayPorts.OrderByDescending(a => a.ConnectStatus).ToList()
            };
        }

        [HttpGet("gateway/{assetId}/alarms")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetGatewayAlarms", Description = "Swagger_Asset_GetGatewayAlarms_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<SearchBase<AlarmLogListItemResult>> GetGatewayAlarms(int assetId, [AllowNull] int page = 1,
            [AllowNull] int pageSize = 10)
        {
            var gatewayInfo = await _client.Queryable<AssetInfo>().InSingleAsync(assetId);

            if (gatewayInfo == null)
            {
                return new SearchBase<AlarmLogListItemResult>()
                {
                    Code = 40000,
                    Message = MessageContext.AssetNotExists
                };
            }
            if (string.IsNullOrWhiteSpace(gatewayInfo.IPAddress) || string.IsNullOrWhiteSpace(gatewayInfo.ObjectId))
            {
                return new SearchBase<AlarmLogListItemResult>()
                {
                    Code = 20000,
                    Items = new List<AlarmLogListItemResult>()
                };
            }
            //select device info base on gateway 
            var subAssets = await _client.Queryable<AssetInfo>()
               .Where(a => SqlFunc.Contains(a.IPAddress, gatewayInfo.IPAddress + "/"))
               .WhereIF(!string.IsNullOrWhiteSpace(gatewayInfo.Port), a => a.Port == gatewayInfo.Port)
               .Where(a => !SqlFunc.IsNullOrEmpty(a.ObjectId))
               .Select(a => a.Id)
               .ToListAsync();
            RefAsync<int> totalNumber = new RefAsync<int>();
            RefAsync<int> totalPage = new RefAsync<int>();
            var db = await _client.Queryable<AlarmLog>().Where(a => a.EventType == AlarmEventType.CommunicationAlarm
            && subAssets.Contains(a.AssetId ?? 0)).
            OrderByDescending(l => l.CreatedTime).ToPageListAsync(page, pageSize, totalNumber, totalPage);
            var result = new List<AlarmLogListItemResult>();
            foreach (var item in db)
            {
                result.Add(new AlarmLogListItemResult(item, null));
            }
            return new SearchBase<AlarmLogListItemResult>()
            {
                Page = page,
                TotalCount = totalNumber.Value,
                Code = 20000,
                Items = result
            };
        }
        [HttpGet("gateway/{assetId}/devices")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetGatewaySubDevices", Description = "Swagger_Asset_GetGatewaySubDevices_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<List<GatewaySubDevice>>> GetGatewaySubDevices(int assetId, [FromQuery] string? port = null)
        {
            var gatewayInfo = await _client.Queryable<AssetInfo>().InSingleAsync(assetId);

            if (gatewayInfo == null)
            {
                return new ResponseBase<List<GatewaySubDevice>>()
                {
                    Code = 40000,
                    Message = MessageContext.AssetNotExists
                };
            }

            if (string.IsNullOrWhiteSpace(gatewayInfo.IPAddress) || string.IsNullOrWhiteSpace(gatewayInfo.ObjectId))
            {
                return new ResponseBase<List<GatewaySubDevice>>()
                {
                    Code = 20000,
                    Data = new List<GatewaySubDevice>()
                };
            }

            var assetRelationId = (await _client.Queryable<AssetRelation>().FirstAsync(a => a.ChildId == assetId))?.ParentId ?? 0;
            var circuitName = (await _client.Queryable<AssetInfo>().FirstAsync(a => a.Id == assetRelationId))?.AssetName ?? string.Empty;

            var subAssets = await _client.Queryable<AssetInfo>()
                 .Where(a => SqlFunc.Contains(a.IPAddress, gatewayInfo.IPAddress + "/"))
                .Where(a => a.Id != gatewayInfo.Id)
                .Where(a => !SqlFunc.IsNullOrEmpty(a.ObjectId))
                .WhereIF(!string.IsNullOrWhiteSpace(port), a => a.Port == port)
                .ToListAsync();

            var gatewaySubDevices = new List<GatewaySubDevice>();

            if (subAssets.Any())
            {
                var connectStatus = _cache.Get<Dictionary<int, bool>>("AssetConnectStatus:All");

                GatewaySubDevice subDevice;
                subAssets.ForEach(s =>
                {
                    subDevice = new();

                    subDevice.AssetId = s.Id;
                    subDevice.AssetName = s.AssetName;
                    subDevice.CircuitName = circuitName;
                    subDevice.OrderNo = s.MLFB ?? string.Empty;

                    if (!string.IsNullOrWhiteSpace(s.IPAddress))
                    {
                        var ipSpans = s.IPAddress.Split("/");
                        if (ipSpans != null && ipSpans.Length == 2)
                        {
                            subDevice.DeviceAddress = ipSpans[1];
                        }
                    }

                    if (connectStatus != null)
                    {
                        subDevice.ConnectStatus = connectStatus.GetValueOrDefault(s.Id) ? 1 : 0;
                    }

                    var currentlyStatus = _cache.GetHashAllData(string.Format("AssetStatus:Currently-{0}", s.Id));
                    if (currentlyStatus != null && currentlyStatus.TryGetValue("Switch", out var position))
                    {
                        subDevice.DevicePosition = position;
                    }

                    gatewaySubDevices.Add(subDevice);
                });
            }

            return new ResponseBase<List<GatewaySubDevice>>()
            {
                Code = 20000,
                Data = gatewaySubDevices.OrderByDescending(a => a.ConnectStatus).ToList()
            };
        }


        [HttpGet("model/{modelType}")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetStaticModel", Description = "Swagger_Asset_GetStaticModel_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<AssetStaticModelResult[]>> GetModel(string modelType)
        {
            modelType = modelType.ToUpper();
            var dataPointMath = Regex.Match(modelType, "^DATAPOINT_(?<Level>DEVICE|CIRCUIT|PANEL|SUBSTATION)(_(?<AssetType>[a-zA-Z0-9]+))?(_(?<AssetModel>[a-zA-Z0-9]+))?$");
            if (dataPointMath.Success && dataPointMath.Groups.TryGetValue("Level", out var levelObj) && levelObj != null)
            {
                var level = AssetLevel.Device;
                string? assetType = null;
                string? assetModel = null;

                switch (levelObj.Value)
                {
                    case "DEVICE":
                        level = AssetLevel.Device;
                        break;
                    case "CIRCUIT":
                        level = AssetLevel.Circuit;
                        break;
                    case "PANEL":
                        level = AssetLevel.Panel;
                        break;
                    case "SUBSTATION":
                        level = AssetLevel.Substation;
                        break;
                    default: break;
                }

                if (dataPointMath.Groups.TryGetValue("AssetType", out var typeObj) && typeObj != null)
                {
                    assetType = typeObj.Value;
                }

                if (dataPointMath.Groups.TryGetValue("AssetModel", out var modelObj) && modelObj != null)
                {
                    assetModel = modelObj.Value;
                }

                var dataPointServer = _dataPointServer;
                var models = await dataPointServer.GetDataPointToStaticModel(level, assetType, assetModel);
                List<AssetStaticModelResult> result = new List<AssetStaticModelResult>();
                if (models != null)
                {
                    result = models.Select(a => new AssetStaticModelResult()
                    {
                        Name = dataPointServer.GetDataPointName(a.Code, MessageContext),
                        Code = a.Code,
                    }).ToList();
                }

                return new ResponseBase<AssetStaticModelResult[]>()
                {
                    Code = 20000,
                    Data = result.ToArray()
                };
            }
            {
                var models = await _client.Queryable<SystemStaticModel>()
                    .Where(a => a.Type == modelType)
                    .OrderByDescending(a => a.Sort)
                    .WithCache($"SystemStaticModel:{modelType}", (int)TimeSpan.FromHours(1).TotalSeconds)
                    .ToArrayAsync();
                List<AssetStaticModelResult> result = new List<AssetStaticModelResult>();
                if (models != null)
                {
                    result = models.Select(a => new AssetStaticModelResult(a, MessageContext)).ToList();
                }

                return new ResponseBase<AssetStaticModelResult[]>()
                {
                    Code = 20000,
                    Data = result.ToArray()
                };
            }
        }

        [HttpGet("getSubstation")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetSubstation", Description = "Swagger_Asset_GetSubstation_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<AssetSimpleInfo>> GetSubstation()
        {
            var substationAsset = await _client.Queryable<AssetInfo>()
                .Where(a => a.AssetLevel == AssetLevel.Substation)
                .OrderBy(a => a.Id)
                .FirstAsync();

            AssetSimpleInfo substation = null;
            if (substationAsset != null)
            {
                substation = new AssetSimpleInfo(substationAsset);
            }

            return new ResponseBase<AssetSimpleInfo>()
            {
                Code = 20000,
                Data = substation
            };
        }
        [HttpGet("getSubstations")]
        [SwaggerOperation(Summary = "Swagger_Asset_getSubstations", Description = "Swagger_Asset_getSubstations_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<SearchBase<SimpleAsset>> getSubstations()
        {
            var substations = await _client.Queryable<AssetInfo>()
                .Where(a => a.AssetLevel == AssetLevel.Substation)
                .OrderBy(a => a.SortNo)
                .OrderBy(a => a.Id)
                .Select(a => new SimpleAsset()
                {
                    Id = a.Id,
                    AssetName = a.AssetName
                })
                .ToListAsync();

            return new SearchBase<SimpleAsset>()
            {
                TotalCount = substations.Count,
                Page = 1,
                Items = substations
            };
        }

        [HttpGet("uploadExcelreult/{jobId}")]
        [SwaggerOperation(Summary = "Swagger_Asset_UploadExcel", Description = "Swagger_Asset_UploadExcel_Desc")]
        [Authorize(policy: Permission.Default)]
        public ResponseBase<JobResult> UploadExcelResult(string jobId)
        {
            var jobInfo = _cache.Get<JobInfo>($"JobId:{jobId}");

            if (jobInfo == null)
            {
                return new ResponseBase<JobResult>()
                {
                    Code = 40400,
                    Message = MessageContext.GetErrorValue("Job_NotExists")
                };
            }

            object? result = null;
            string? message = null;
            string? sucessMessage = null;

            if (jobInfo.Result != null && jobInfo.Result.ErrorInfo != null && jobInfo.Result.ErrorInfo.Any())
            {
                var successStringBuilder = new StringBuilder();
                var messageStringBuilder = new StringBuilder();

                for (int i = 0; i < jobInfo.Result.ErrorInfo.Count; i++)
                {
                    string errMsg = MessageContext.GetErrorValue(jobInfo.Result.ErrorInfo[i]);

                    if (jobInfo.Result.ErrorInfo[i].Contains("Success"))
                    {
                        successStringBuilder.AppendLine(errMsg);
                    }
                    else
                    {
                        messageStringBuilder.AppendLine(errMsg);
                    }

                    jobInfo.Result.ErrorInfo[i] = errMsg;
                }

                message = messageStringBuilder.ToString();
                sucessMessage = successStringBuilder.ToString();
            }

            result = jobInfo.Result;

            return new ResponseBase<JobResult>()
            {
                Code = 20000,
                Data = new JobResult()
                {
                    Status = jobInfo.JobStatus,
                    Result = result,
                    ResultMessage = message,
                    SuccessMessage = sucessMessage
                }
            };
        }

        [HttpPost("uploadExcel")]
        [SwaggerOperation(Summary = "Swagger_Asset_UploadExcel", Description = "Swagger_Asset_UploadExcel_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> UploadExcel([FromForm] IFormCollection? form)
        {
            if (form == null || form.Files.Count <= 0)
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            if (form.Files.Any(f => !Regex.IsMatch(f.FileName, "\\.xlsx$")))
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var filePaths = new List<string>();
            foreach (var file in form.Files)
            {
                try
                {
                    var fileInfo = await _tempFileManager.CreatedTempFile(file.OpenReadStream(), file.FileName);
                    filePaths.Add(fileInfo.FullName);
                }
                catch (Exception ex)
                {
                    _log.LogError(ex, "文件保存失败");
                }
            }

            // 导入之前记录一下之前的assetIds集合
            var assetIds = await _client.Queryable<AssetInfo>().Select(p => p.Id).ToListAsync();

            if (assetIds != null && assetIds.Any())
            {
                _cache.Set("asset_info_assetIds", assetIds);
            }

            var param = new Dictionary<string, string>();
            param.Add("Files", string.Join(',', filePaths.ToArray()));
            param.Add("User", UserName);
            var jobInfo = new JobInfo();
            param.Add("JobId", jobInfo.JobId);
            _cache.Set($"JobId:{jobInfo.JobId}", jobInfo, TimeSpan.FromMinutes(1));
            await _jobManager.TriggerJob("UploadExcelJob", param);
            await _alarmExtendServer.InsertOperationLog(UserName, "UploadAssetFile", Model.Database.Alarm.AlarmSeverity.High, _client);
            await SendAssetTree();

            return new ResponseBase<string>()
            {
                Data = jobInfo.JobId
            };
        }

        [HttpGet("DownloadExcel")]
        [SwaggerOperation(Summary = "Swagger_Asset_DownloadExcel", Description = "Swagger_Asset_DownloadExcel_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> DownloadExcel()
        {
            var allAsset = await _client.Queryable<AssetInfo>().ToArrayAsync();
            var allAssetRelation = await _client.Queryable<AssetRelation>().ToArrayAsync();

            var str = "DeviceType".ToUpper();
            var deviceTypeList = await _client.Queryable<SystemStaticModel>()
                .Where(s => s.Type == str)
                .WithCache($"SystemStaticModel:{str}", (int)TimeSpan.FromHours(1).TotalSeconds)
                .ToArrayAsync();

            str = "DeviceModel".ToUpper();
            var deviceModelList = await _client.Queryable<SystemStaticModel>()
                .Where(s => s.Type == str)
                .WithCache($"SystemStaticModel:{str}", (int)TimeSpan.FromHours(1).TotalSeconds)
                .ToArrayAsync();

            str = "UseScene".ToUpper();
            var useSceneList = await _client.Queryable<SystemStaticModel>()
                .Where(s => s.Type == str)
                .WithCache($"SystemStaticModel:{str}", (int)TimeSpan.FromHours(1).TotalSeconds)
                .ToArrayAsync();

            str = "PanelType".ToUpper();
            var panelTypeList = await _client.Queryable<SystemStaticModel>()
                .Where(s => s.Type == str)
                .WithCache($"SystemStaticModel:{str}", (int)TimeSpan.FromHours(1).TotalSeconds)
                .ToArrayAsync();

            str = "TransformerType".ToUpper();
            var TransformerTypeList = await _client.Queryable<SystemStaticModel>()
                .Where(s => s.Type == str)
                .WithCache($"SystemStaticModel:{str}", (int)TimeSpan.FromHours(1).TotalSeconds)
                .ToArrayAsync();

            str = "CircuitType".ToUpper();
            var circuitTypeList = await _client.Queryable<SystemStaticModel>()
                .Where(s => s.Type == str)
                .WithCache($"SystemStaticModel:{str}", (int)TimeSpan.FromHours(1).TotalSeconds)
                .ToArrayAsync();

            str = "PanelModel".ToUpper();
            var panelModelList = await _client.Queryable<SystemStaticModel>()
                .Where(s => s.Type == str)
                .WithCache($"SystemStaticModel:{str}", (int)TimeSpan.FromHours(1).TotalSeconds)
                .ToArrayAsync();

            str = "TransformerModel".ToUpper();
            var transformerModelList = await _client.Queryable<SystemStaticModel>()
                .Where(s => s.Type == str)
                .WithCache($"SystemStaticModel:{str}", (int)TimeSpan.FromHours(1).TotalSeconds)
                .ToArrayAsync();

            str = "MeterType".ToUpper();
            var meterTypeList = await _client.Queryable<SystemStaticModel>()
                .Where(s => s.Type == str)
                .WithCache($"SystemStaticModel:{str}", (int)TimeSpan.FromHours(1).TotalSeconds)
                .ToArrayAsync();

            Dictionary<string, IList<AssetInfoExtend>> data = new Dictionary<string, IList<AssetInfoExtend>>();
            foreach (var obj in Enum.GetValues(typeof(AssetLevel)))
            {
                if (obj is AssetLevel level)
                {
                    data.Add(level.ToString(), new List<AssetInfoExtend>());
                }
            }

            foreach (var a in allAsset)
            {
                var assetLevel = a.AssetLevel.ToString();
                if (data.TryGetValue(assetLevel, out IList<AssetInfoExtend>? list) && list != null)
                {
                    var extend = new AssetInfoExtend(a,
                        deviceTypeList,
                        deviceModelList,
                        panelTypeList,
                        TransformerTypeList,
                        circuitTypeList,
                        panelModelList,
                        transformerModelList,
                        useSceneList,
                        meterTypeList,
                        MessageContext);

                    var relation = allAssetRelation.FirstOrDefault(ar => ar.ChildId == a.Id);
                    if (relation != null)
                    {
                        var parent = allAsset.FirstOrDefault(a => a.Id == relation.ParentId);
                        extend.ParentId = parent?.Id;
                        extend.ParentName = parent?.AssetName ?? string.Empty;
                    }

                    var relationPanel = allAsset.FirstOrDefault(i => i.Id == a.RelationPanelId);
                    if (relationPanel != null)
                    {
                        extend.RelationPanelName = relationPanel.AssetName;
                    }

                    list.Add(extend);
                }
            }

            #region 数据排序
            var areaSort = new List<int>();
            var substationSort = new List<int>();
            var panelSort = new List<int>();
            var transformerSort = new List<int>();
            var circuitSort = new List<int>();
            #region Area
            {
                var list = data[AssetLevel.Area.ToString()];

                list = list.OrderBy(l => l.SortNo).ToList();
                areaSort = list.Select(l => l.Id).ToList();

                data[AssetLevel.Area.ToString()] = list;
            }
            #endregion

            #region Substation
            {
                var list = data[AssetLevel.Substation.ToString()];

                var newList = new List<AssetInfoExtend>();
                foreach (var areaId in areaSort)
                {
                    newList.AddRange(list.Where(l => l.ParentId == areaId).OrderBy(l => l.SortNo).ToList());
                }

                newList.AddRange(list.Where(l => !l.ParentId.HasValue).OrderBy(l => l.SortNo).ToList());
                substationSort = newList.Select(l => l.Id).ToList();
                data[AssetLevel.Substation.ToString()] = newList;
            }
            #endregion

            #region Panel
            {
                var list = data[AssetLevel.Panel.ToString()];

                var newList = new List<AssetInfoExtend>();
                foreach (var assetId in substationSort)
                {
                    newList.AddRange(list.Where(l => l.ParentId == assetId).OrderBy(l => l.SortNo).ToList());
                }

                newList.AddRange(list.Where(l => !l.ParentId.HasValue).OrderBy(l => l.SortNo).ToList());
                panelSort = newList.Select(l => l.Id).ToList();
                data[AssetLevel.Panel.ToString()] = newList;
            }
            #endregion
            #region Transformer
            {
                var list = data[AssetLevel.Transformer.ToString()];

                var newList = new List<AssetInfoExtend>();
                foreach (var assetId in substationSort)
                {
                    newList.AddRange(list.Where(l => l.ParentId == assetId).OrderBy(l => l.SortNo).ToList());
                }

                newList.AddRange(list.Where(l => !l.ParentId.HasValue).OrderBy(l => l.SortNo).ToList());
                transformerSort = newList.Select(l => l.Id).ToList();
                data[AssetLevel.Transformer.ToString()] = newList;
            }
            #endregion

            #region Circuit
            {
                var list = data[AssetLevel.Circuit.ToString()];

                var newList = new List<AssetInfoExtend>();
                foreach (var assetId in panelSort)
                {
                    newList.AddRange(list.Where(l => l.ParentId == assetId).OrderBy(l => l.SortNo).ToList());
                }

                newList.AddRange(list.Where(l => !l.ParentId.HasValue).OrderBy(l => l.SortNo).ToList());
                circuitSort = newList.Select(l => l.Id).ToList();
                data[AssetLevel.Circuit.ToString()] = newList;
            }
            #endregion

            #region Device
            {
                var list = data[AssetLevel.Device.ToString()];

                var newList = new List<AssetInfoExtend>();
                foreach (var assetId in areaSort)
                {
                    newList.AddRange(list.Where(l => l.ParentId == assetId).OrderBy(l => l.SortNo).ToList());
                }

                foreach (var assetId in substationSort)
                {
                    newList.AddRange(list.Where(l => l.ParentId == assetId).OrderBy(l => l.SortNo).ToList());
                }

                foreach (var assetId in panelSort)
                {
                    newList.AddRange(list.Where(l => l.ParentId == assetId).OrderBy(l => l.SortNo).ToList());
                }
                foreach (var assetId in transformerSort)
                {
                    newList.AddRange(list.Where(l => l.ParentId == assetId).OrderBy(l => l.SortNo).ToList());
                }
                foreach (var assetId in circuitSort)
                {
                    newList.AddRange(list.Where(l => l.ParentId == assetId).OrderBy(l => l.SortNo).ToList());
                }

                newList.AddRange(list.Where(l => !l.ParentId.HasValue).OrderBy(l => l.SortNo).ToList());
                data[AssetLevel.Device.ToString()] = newList;
            }
            #endregion
            #endregion

            foreach (var obj in Enum.GetValues(typeof(AssetLevel)))
            {
                if (obj is AssetLevel level)
                {
                    if (data[level.ToString()].Count == 0)
                    {
                        data[level.ToString()].Add(new AssetInfoExtend());
                    }
                }
            }

            var ms = new MemoryStream();
            await _excelHelper.SaveExcelAsync(ms, data, UserLanguage, "Asset-Template");
            ms.Seek(0, SeekOrigin.Begin);
            return File(ms, "application/octet-stream", fileDownloadName: $"{MessageContext.AssetFileName}.xlsx", enableRangeProcessing: true);
        }

        [HttpGet("search")]
        [SwaggerOperation(Summary = "Swagger_Asset_Search", Description = "Swagger_Asset_Search_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<SearchBase<SimpleAsset>> Search([AllowNull] int? parentId,
            [AllowNull] string? assetName,
            [AllowNull] string? levels,
            [AllowNull] string? assetModel,
            [AllowNull] string? assetType)
        {
            var query = _client.Queryable<AssetInfo>();
            List<AssetRelation>? assetRelations = null;
            if (parentId.HasValue)
            {
                assetRelations = await _client.Queryable<AssetRelation>()
                    .Where(a => a.ChildId != parentId.Value)
                    .ToChildListAsync(a => a.ParentId, parentId.Value);

                var assetIds = assetRelations.Select(ar => ar.ChildId).ToArray();
                query = query.Where(a => assetIds.Contains(a.Id));
            }

            if (!string.IsNullOrEmpty(assetName))
            {
                query = query.Where(a => a.AssetName.Contains(assetName));
            }

            if (!string.IsNullOrEmpty(levels))
            {
                var levelStrArray = levels.Split(',');

                var assetLevels = new List<AssetLevel>(levelStrArray.Length);
                foreach (var level in levelStrArray)
                {
                    if (Enum.TryParse(level, out AssetLevel assetLevel))
                    {
                        assetLevels.Add(assetLevel);
                    }
                }

                query = query.Where(a => assetLevels.Contains(a.AssetLevel));
            }

            if (!string.IsNullOrEmpty(assetType))
            {
                query = query.Where(a => a.AssetType == assetType);
            }

            if (!string.IsNullOrEmpty(assetModel))
            {
                query = query.Where(a => a.AssetModel == assetModel);
            }

            var connectStatus = _cache.Get<Dictionary<int, bool>>("AssetConnectStatus:All");
            var simpleAssets = await query.Select(a => new SimpleAsset()
            {
                Id = a.Id,
                AssetName = a.AssetName,
                AssetLevel = a.AssetLevel,
                AssetModel = a.AssetModel,
                AssetType = a.AssetType,
            }).OrderBy(a => a.AssetName).ToListAsync();
            if (connectStatus != null)
            {
                foreach (var simpleInfo in simpleAssets)
                {
                    if (connectStatus.TryGetValue(simpleInfo.Id, out var hasConnected))
                    {
                        simpleInfo.HasConnect = hasConnected ? "1" : "0";
                    }
                }
            }

            if (assetRelations != null && parentId.HasValue)
            {
                var needRemoveItem = new List<SimpleAsset>();
                foreach (var ar in assetRelations)
                {
                    if (ar.ParentId != parentId.Value)
                    {
                        var item = simpleAssets.FirstOrDefault(a => a.Id == ar.ChildId);
                        if (item != null)
                        {
                            needRemoveItem.Add(item);
                            var parentItem = simpleAssets.FirstOrDefault(a => a.Id == ar.ParentId);
                            if (parentItem != null)
                            {
                                if (parentItem.Children == null)
                                {
                                    parentItem.Children = new List<SimpleAsset>();
                                }
                                parentItem.Children.Add(item);
                            }
                        }
                    }
                }

                needRemoveItem.ForEach(ra => simpleAssets.Remove(ra));
            }

            return new SearchBase<SimpleAsset>()
            {
                TotalCount = simpleAssets.Count,
                Page = 1,
                Items = simpleAssets
            };
        }
        [HttpGet("searchAssetByParent")]
        [SwaggerOperation(Summary = "Swagger_Asset_searchAssetByParent", Description = "Swagger_Asset_searchAssetByParent_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<SearchBase<SimpleAsset>> searchAssetByParent([AllowNull] string? parentId,
            [AllowNull] int? level,
            [AllowNull] string? assetModel,
            [AllowNull] string? assetType)
        {
            var query = _client.Queryable<AssetInfo>();
            if (!string.IsNullOrEmpty(parentId))
            {
                var parentIdArray = parentId.Split(",");
                List<int> parentsIds = new List<int>();
                foreach (var str in parentIdArray)
                {
                    int number;
                    bool success = int.TryParse(str, out number);
                    if (success)
                    {
                        parentsIds.Add(number);
                    }
                }
                var assetRelationList = await _client.Queryable<AssetRelation>().ToListAsync();

                var chirdAssetRelations = assetRelationList.Where(p => parentsIds.Contains(p.ParentId)).ToList();

                var deviceAssetIds = new List<int>();

                if (chirdAssetRelations != null && chirdAssetRelations.Any())
                {
                    var deviceAssetRelationsIds = new List<int>();

                    while (chirdAssetRelations.Count > 0)
                    {
                        deviceAssetRelationsIds = chirdAssetRelations.Select(p => p.ChildId).ToList();
                        if (chirdAssetRelations.Any(p => (int)p.AssetLevel == level))
                        {
                            deviceAssetIds.AddRange(chirdAssetRelations.Where(a => (int)a.AssetLevel == level).Select(p => p.ChildId).ToList());
                        }
                        chirdAssetRelations = assetRelationList.Where(p => deviceAssetRelationsIds.Contains(p.ParentId)).ToList();
                        if (chirdAssetRelations.Any(p => (int)p.AssetLevel == level))
                        {

                            deviceAssetIds.AddRange(chirdAssetRelations.Where(p => (int)p.AssetLevel == level).Select(p => p.ChildId).ToList());
                        }
                        chirdAssetRelations = chirdAssetRelations.Where(p => (int)p.AssetLevel < level).ToList();
                    }
                }
                query = query.Where(a => deviceAssetIds.Contains(a.Id));
            }
            else
            {
                query = query.Where(a => (int)a.AssetLevel == level);
            }
            if (!string.IsNullOrEmpty(assetType))
            {
                query = query.Where(a => a.AssetType == assetType);
            }

            if (!string.IsNullOrEmpty(assetModel))
            {
                query = query.Where(a => a.AssetModel == assetModel);
            }

            var connectStatus = _cache.Get<Dictionary<int, bool>>("AssetConnectStatus:All");
            var simpleAssets = await query.Select(a => new SimpleAsset()
            {
                Id = a.Id,
                AssetName = a.AssetName,
                AssetLevel = a.AssetLevel,
                AssetModel = a.AssetModel,
                AssetType = a.AssetType,
            }).OrderBy(a => a.AssetName).ToListAsync();

            if (connectStatus != null)
            {
                foreach (var simpleInfo in simpleAssets)
                {
                    if (connectStatus.TryGetValue(simpleInfo.Id, out var hasConnected))
                    {
                        simpleInfo.HasConnect = hasConnected ? "1" : "0";
                    }
                }
            }
            return new SearchBase<SimpleAsset>()
            {
                TotalCount = simpleAssets.Count,
                Page = 1,
                Items = simpleAssets
            };
        }
        [HttpGet("overviewlist")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetOverviewList", Description = "Swagger_Asset_GetOverviewList_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<Dictionary<string, IChart>>> GetOverviewList(string viewNames,
            [AllowNull] int? assetId)
        {
            if (string.IsNullOrEmpty(viewNames))
            {
                return new ResponseBase<Dictionary<string, IChart>>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var viewList = viewNames.Split(',');
            if (viewList.Length <= 0)
            {
                return new ResponseBase<Dictionary<string, IChart>>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var viewUpperNames = viewList.Select(v => v.ToUpper()).ToArray();
            var configs = await _client.Queryable<AssetDashboardConfig>().Where(c => viewUpperNames.Contains(c.ConfigName)).ToArrayAsync();
            var result = new Dictionary<string, IChart>();
            foreach (var config in configs)
            {
                if (config == null) continue;
                if (config.ConfigName == "BREAKERHEALTH" || "ASSETREPLACEMENTPART".Equals(config.ConfigName, StringComparison.OrdinalIgnoreCase))
                {
                    if (assetId > 0)
                    {
                        config.Sql = config.Sql.Replace("[[AssetId]]", assetId.ToString());
                    }
                    else
                    {
                        var subInfos = await _client.Queryable<AssetInfo>().Where(a => a.AssetLevel == AssetLevel.Substation).Select(a => a.Id).ToListAsync();
                        var assetIdQuery = new StringBuilder();
                        foreach (var id in subInfos)
                        {
                            if (assetIdQuery.Length > 0)
                            {
                                assetIdQuery.Append(",");
                            }
                            assetIdQuery.Append(id);
                        }
                        if (assetIdQuery.Length == 0)
                        {
                            assetIdQuery.Append("0");
                        }
                        config.Sql = config.Sql.Replace("[[AssetId]]", assetIdQuery.ToString());

                    }
                }
                else if (config.ConfigName == "ALARMCOUNT" || "ALARMEVENTTYPECOUNT".Equals(config.ConfigName, StringComparison.OrdinalIgnoreCase))
                {
                    var queryStartTime = await _client.Queryable<AlarmQueryConfig>().Where(a => a.QueryType == AlarmQueryType.QueryStartTime).FirstAsync();
                    if (queryStartTime != null && !string.IsNullOrEmpty(queryStartTime.QueryValue))
                    {

                        DateTime startDate;
                        var success = DateTime.TryParseExact(queryStartTime.QueryValue, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.RoundtripKind, out startDate);
                        if (success)
                        {
                            var strDate = String.Format(" and al.created_time >'{0}'", startDate.ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture));
                            config.Sql = config.Sql.Replace("[[QueryTime]]", strDate);
                        }
                    }
                    else
                    {
                        config.Sql = config.Sql.Replace("[[QueryTime]]", "");
                    }
                }
                var configName = viewList.FirstOrDefault(v => v.Equals(config.ConfigName, StringComparison.OrdinalIgnoreCase));
                if (string.IsNullOrEmpty(configName)) continue;
                if (result.ContainsKey(configName)) continue;
                var chart = await config.GetChartBySql(_client, MessageContext);
                if (chart == null) continue;
                result.Add(configName, chart);
            }
            return new ResponseBase<Dictionary<string, IChart>>()
            {
                Code = 20000,
                Data = result
            };
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="count"></param>
        /// <param name="page"></param>
        /// <param name="selectPinType">备品备件类型 1: 缓件 2: 急件</param>
        /// <param name="selectType"></param>
        /// <param name="selectModel"></param>
        /// <param name="assetId"></param>
        /// <returns></returns>
        [HttpGet("getorders")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetOrders", Description = "Swagger_Asset_GetOrders_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<SearchBase<DeviceOrderResult>> GetOrderList
            ([AllowNull] int? count,
            [AllowNull] int? page,
            [AllowNull] int? selectPinType,
            [AllowNull] string? selectType,
            [AllowNull] string? selectModel,
            [AllowNull] string? assetId)
        {
            if (!count.HasValue)
            {
                count = 10;
            }

            if (!page.HasValue)
            {
                page = 1;
            }

            var healthLevel = new List<string>();

            if (!selectPinType.HasValue)
            {
                healthLevel.Add("attention");
                healthLevel.Add("maintain");
                healthLevel.Add("rushRepair");
            }
            else if (selectPinType == 1)
            {
                healthLevel.Add("attention");
                healthLevel.Add("maintain");
            }
            else if (selectPinType == 2)
            {
                healthLevel.Add("rushRepair");
            }

            var query = _client.Queryable<AssetInfo>()
                .InnerJoin<DeviceDetails>((a, ad) => a.Id == ad.AssetId)
                .Where((a, ad) => a.AssetLevel == AssetLevel.Device && ad.HealthLevel != null && healthLevel.Contains(ad.HealthLevel));

            if (!string.IsNullOrEmpty(selectType))
            {
                var assetTypeList = selectType.Split(',');
                if (assetTypeList.Length > 0)
                {
                    query = query.Where((a, ad) => assetTypeList.Contains(a.AssetType));
                }
            }

            if (!string.IsNullOrEmpty(selectModel))
            {
                var assetModelList = new List<string>();
                assetModelList.AddRange(selectModel.Split(','));
                query = query.Where((a, ad) => a.AssetModel != null && assetModelList.Contains(a.AssetModel));
            }
            if (!string.IsNullOrEmpty(assetId) && int.TryParse(assetId, out int id))
            {

                var children = await _client.Queryable<AssetRelation>().ToChildListAsync(ar => ar.ParentId, id);

                var assetIds = children.Where(c => c.AssetLevel == AssetLevel.Device).Select(c => c.ChildId).ToList();
                query = query.Where((a, ad) => assetIds.Contains(a.Id));
            }

            RefAsync<int> totalCount = new RefAsync<int>();
            var result = await query.OrderBy((a, ad) => a.Id).Select((a, ad) => new DeviceOrderResult
            {
                AssetId = a.Id,
                Name = a.AssetName,
                BuyNo = ad.MLFB,
                Address = a.Location,
                AssetType = a.AssetType,
                AssetModel = a.AssetModel
            }).ToPageListAsync(page.Value, count.Value, totalCount);

            return new SearchBase<DeviceOrderResult>()
            {
                Code = 20000,
                Items = result,
                TotalCount = totalCount.Value,
                Page = page.Value
            };
        }

        [HttpGet("GetAbnormalBreaker")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetAbnormalBreaker", Description = "Swagger_Asset_GetAbnormalBreaker_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<SearchBase<DeviceOrderResult>> GetAbnormalBreaker()
        {
            var healthLevel = new List<string>
            {
                "attention",
                "maintain",
                "rushRepair"
            };

            var query = _client.Queryable<AssetInfo>()
                .InnerJoin<DeviceDetails>((a, ad) => a.Id == ad.AssetId)
                .Where((a, ad) => a.AssetLevel == AssetLevel.Device
                    && a.MLFB != null
                    && ad.HealthLevel != null
                    && healthLevel.Contains(ad.HealthLevel));

            var result = await query.OrderBy((a, ad) => a.Id)
                .Select((a, ad) => new DeviceOrderResult
                {
                    AssetId = a.Id,
                    Name = a.AssetName,
                    BuyNo = ad.MLFB,
                    Address = a.Location,
                    AssetType = a.AssetType,
                    AssetModel = a.AssetModel
                }).ToListAsync();

            return new SearchBase<DeviceOrderResult>()
            {
                Code = 20000,
                Items = result,
                TotalCount = result.Count,
                Page = 1
            };
        }

        [HttpGet("{assetId}/GetTopology")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetTopology", Description = "Swagger_Asset_GetTopology_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<JObject>> GetTopology(int assetId)
        {
            if (assetId <= 0)
            {
                return new ResponseBase<JObject>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var assetInfo = await _client.Queryable<AssetInfo>().FirstAsync(a => a.Id == assetId);
            if (assetInfo == null)
            {
                return new ResponseBase<JObject>()
                {
                    Code = 40400,
                    Message = MessageContext.AssetNotExists
                };
            }
            if (assetInfo.TopologyId != null && assetInfo.TopologyId > 0)
            {
                var topoplogyInfo = await _client.Queryable<TopologyInfo>().FirstAsync(t => t.Id == assetInfo.TopologyId);
                if (topoplogyInfo != null)
                {
                    var topoplogyObj = JObject.Parse(topoplogyInfo.Data);
                    topoplogyObj["id"] = assetInfo.TopologyId;
                    return new ResponseBase<JObject>()
                    {
                        Code = 20000,
                        Data = topoplogyObj
                    };
                }
            }
            return new ResponseBase<JObject>()
            {
                Code = 20000,
                Message = MessageContext.GetErrorValue("Common_NotExists")
            };
        }

        [HttpGet("{assetId}/GetTopology3D")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetTopology", Description = "Swagger_Asset_GetTopology_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<Topology3D>> GetTopology3D(int assetId)
        {
            if (assetId <= 0)
            {
                return new ResponseBase<Topology3D>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var assetInfo = await _client.Queryable<AssetInfo>().FirstAsync(a => a.Id == assetId);
            if (assetInfo == null)
            {
                return new ResponseBase<Topology3D>()
                {
                    Code = 40400,
                    Message = MessageContext.AssetNotExists
                };
            }
            if (assetInfo.Topology3DId != null && assetInfo.Topology3DId > 0)
            {
                var topoplogyInfo = await _client.Queryable<TopologyInfo>().FirstAsync(t => t.Id == assetInfo.Topology3DId);
                if (topoplogyInfo != null)
                {
                    Topology3D model = new Topology3D();
                    model.Id = topoplogyInfo.Id;
                    model.NodeObject = JArray.Parse(topoplogyInfo.Data);
                    model.Name = topoplogyInfo.Name;
                    model.Description = topoplogyInfo.Description;
                    model.Time = topoplogyInfo.CreatedTime;
                    model.Owner = topoplogyInfo.CreatedBy;
                    if (!string.IsNullOrEmpty(topoplogyInfo.Extend))
                    {
                        try
                        {
                            model.PanelList = JsonConvert.DeserializeObject<Dictionary<string, JToken>>(topoplogyInfo.Extend);
                        }
                        catch
                        {

                        }
                    }

                    if (model.PanelList != null && model.PanelList.Keys.Count > 0)
                    {
                        foreach (var item in model.PanelList)
                        {
                            if (item.Value.Type == JTokenType.Integer)
                            {
                                var subId = item.Value.Value<int>();
                                var subInfo = await _client.Queryable<TopologyInfo>().FirstAsync(t => t.Id == subId && t.Type == "3D");
                                if (subInfo != null)
                                {
                                    model.PanelList[item.Key] = JToken.Parse(subInfo.Data);
                                }
                            }
                        }
                    }
                    return new ResponseBase<Topology3D>()
                    {
                        Code = 20000,
                        Data = model
                    };
                }
            }
            return new ResponseBase<Topology3D>()
            {
                Code = 20000,
                Message = MessageContext.GetErrorValue("Common_NotExists")
            };
        }

        [HttpGet("{assetId}/GetTopologyInfo")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetTopology", Description = "Swagger_Asset_GetTopology_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<TopologySingleInfo>> GetTopologyInfo(int assetId)
        {
            if (assetId <= 0)
            {
                return new ResponseBase<TopologySingleInfo>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            TopologySingleInfo result = new TopologySingleInfo();
            var paramList = await _client.Queryable<AssetRelation>().ToParentListAsync(ar => ar.ParentId, assetId);
            var substation = paramList.FirstOrDefault(p => p.AssetLevel == AssetLevel.Substation);
            int substationId = assetId;
            if (substation != null)
            {
                substationId = substation.ChildId;
            }
            var assetInfo = await _client.Queryable<AssetInfo>().FirstAsync(a => a.Id == substationId && a.AssetLevel == AssetLevel.Substation);
            if (assetInfo != null && assetInfo.TopologyId.HasValue)
            {
                result = await _client.Queryable<TopologyInfo>().Where(t => t.Id == assetInfo.TopologyId).Select(t => new TopologySingleInfo()
                {
                    Id = t.Id,
                    Name = t.Name,
                    Code = t.Code,
                }).FirstAsync();
            }
            else
            {
                result.Id = -1;
                result.Name = string.Empty;
            }
            return new ResponseBase<TopologySingleInfo>
            {
                Code = 20000,
                Data = result
            };
        }

        [HttpGet("{assetId}/GetTopology3DInfo")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetTopology", Description = "Swagger_Asset_GetTopology_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<TopologySingleInfo>> GetTopology3DInfo(int assetId)
        {
            if (assetId <= 0)
            {
                return new ResponseBase<TopologySingleInfo>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            TopologySingleInfo result = new TopologySingleInfo();
            var paramList = await _client.Queryable<AssetRelation>().ToParentListAsync(ar => ar.ParentId, assetId);
            var substation = paramList.FirstOrDefault(p => p.AssetLevel == AssetLevel.Substation);
            int substationId = assetId;
            if (substation != null)
            {
                substationId = substation.ChildId;
            }
            var assetInfo = await _client.Queryable<AssetInfo>().FirstAsync(a => a.Id == substationId && a.AssetLevel == AssetLevel.Substation);
            if (assetInfo != null && assetInfo.Topology3DId.HasValue)
            {
                result = await _client.Queryable<TopologyInfo>().Where(t => t.Id == assetInfo.Topology3DId).Select(t => new TopologySingleInfo()
                {
                    Id = t.Id,
                    Name = t.Name,
                    Code = t.Code,
                }).FirstAsync();
            }
            else
            {
                result.Id = -1;
                result.Name = string.Empty;
            }
            return new ResponseBase<TopologySingleInfo>
            {
                Code = 20000,
                Data = result
            };
        }

        [HttpGet("{assetId}/GetDeviceInfo")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetDeviceInfo", Description = "Swagger_Asset_GetDeviceInfo_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<DeviceInfo>> GetDeviceInfo(int assetId)
        {
            var assetInfo = await _client.Queryable<AssetInfo>().FirstAsync(a => a.Id == assetId);

            if (assetInfo == null)
            {
                return new ResponseBase<DeviceInfo>()
                {
                    Code = 40400,
                    Message = MessageContext.AssetNotExists
                };
            }

            var details = new DeviceInfo();
            if (assetInfo.AssetLevel == AssetLevel.Device)
            {
                var deviceInfo = await _client.Queryable<DeviceDetails>().FirstAsync(a => a.AssetId == assetId);
                if (deviceInfo != null)
                {
                    deviceInfo.MLFB = deviceInfo.MLFB ?? assetInfo.MLFB ?? string.Empty;
                    var func = _provider.GetRequiredService<ObjectReflectFunc>();
                    details = func.UpdateObjByObj(details, deviceInfo);

                    details.MeterSite = deviceInfo.MeterSite;
                }

                if (details.OperatingHours.HasValue &&
                    (assetInfo.AssetModel == "3WA" || assetInfo.AssetType == "Meter"))
                {
                    details.OperatingHours /= 3600;
                }
            }

            return new ResponseBase<DeviceInfo>()
            {
                Code = 20000,
                Data = details
            };
        }

        [HttpGet("{assetId}/GetDeviceInputOutput")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetDeviceInputOutput", Description = "Swagger_Asset_GetDeviceInputOutput_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<DeviceInputOutput[]>> GetDeviceInputOutput(int assetId, int universalDeviceConfigId)
        {
            var assetInfo = await _client.Queryable<AssetInfo>().FirstAsync(a => a.Id == assetId && a.AssetLevel == AssetLevel.Device);

            if (assetInfo == null)
            {
                return new ResponseBase<DeviceInputOutput[]>()
                {
                    Code = 20000,
                    Data = Array.Empty<DeviceInputOutput>()
                };
            }

            //添加第三方设备得二进制制点位
            if (universalDeviceConfigId > 0)
            {
                await _client.Deleteable<DeviceInputOutput>().Where(p => p.AssetId == assetId).ExecuteCommandAsync();

                var bitConfigs = await _client.Queryable<BitConfig>().Where(p => p.UniversalDeviceConfigId == universalDeviceConfigId).ToListAsync();

                if (bitConfigs != null && bitConfigs.Any())
                {
                    var deviceInputOutputs = new List<DeviceInputOutput>();

                    foreach (var item in bitConfigs)
                    {
                        deviceInputOutputs.Add(new DeviceInputOutput()
                        {
                            AssetId = assetId,
                            Code = item.BitNumber.ToString(),
                            Name = item.BitName ?? "",
                            CreatedBy = this.UserName,
                            CreatedTime = DateTime.Now
                        });
                    }

                    await _client.Insertable(deviceInputOutputs).ExecuteCommandAsync();
                }
            }

            var result = await _client.Queryable<DeviceInputOutput>().Where(d => d.AssetId == assetId).ToArrayAsync();

            result?.ForEach(d =>
            {
                d.Name = MessageContext.GetString($"DataPoint_{d.Code}") ?? d.Name;
            });

            //从底层设备获取第三方设备开关状态值
            if (universalDeviceConfigId > 0)
            {
                string? data = "0000000000000000";
                var currentlyStatus = _cache.GetHashAllData(string.Format(AssetCurrentStatusCacheKey, assetId));
                var universalDeviceConfig = await _client.Queryable<UniversalDeviceConfig>().Where(p => p.Id == universalDeviceConfigId).FirstAsync();
                if (universalDeviceConfig != null)
                {
                    if (currentlyStatus.Any())
                    {
                        currentlyStatus.TryGetValue(universalDeviceConfig.PropertyEnName ?? "", out data);

                        data = string.IsNullOrWhiteSpace(data) ? "0000000000000000" : data;

                        // 是否有科学计数法
                        if (data.Contains("E") || data.Contains("e"))
                        {
                            data = Decimal.Parse(data, System.Globalization.NumberStyles.Float).ToString();
                        }

                        // 判断是否是16位二级制
                        if (!(data.Length == 16 && StringFunction.IsBinary(data)))
                        {
                            data = StringFunction.TenToBit(Convert.ToInt32(data));
                        }
                    }

                    if (!string.IsNullOrWhiteSpace(data))
                    {
                        List<string?> bits = StringFunction.BitToList(data);

                        result?.ForEach(p =>
                        {
                            int index = Convert.ToInt32(p.Code);
                            p.Value = bits[index] ?? "0";
                        });
                    }
                }

            }

            return new ResponseBase<DeviceInputOutput[]>()
            {
                Code = 20000,
                Data = result
            };
        }

        [HttpPut("{assetId}/UpdateDeviceInputOutput")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetDeviceInputOutput", Description = "Swagger_Asset_GetDeviceInputOutput_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<bool>> GetDeviceInputOutput(int assetId, DeviceInputOutput[] data)
        {
            var assetInfo = await _client.Queryable<AssetInfo>().FirstAsync(a => a.Id == assetId && a.AssetLevel == AssetLevel.Device);

            if (assetInfo == null)
            {
                return new ResponseBase<bool>()
                {
                    Code = 40400,
                    Message = MessageContext.AssetNotExists
                };
            }

            var result = await _client.Queryable<DeviceInputOutput>().Where(d => d.AssetId == assetId).OrderBy(a => a.Id).ToArrayAsync();

            foreach (var i in result)
            {
                var input = data.FirstOrDefault(d => d.Id == i.Id);
                if (input == null) continue;
                i.Name = input.Name;
                i.UpdatedBy = UserName;
                i.UpdatedTime = DateTime.Now;
            }

            await _client.Updateable(result).ExecuteCommandAsync();
            return new ResponseBase<bool>()
            {
                Code = 20000,
                Data = true
            };
        }

        [HttpGet("GetTemperatureDataPoints")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetTemperatureDataPoints", Description = "Swagger_Asset_GetTemperatureDataPoints_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<AssetStaticModelResult[]>> GetTemperatureDataPoints(string level,
            [AllowNull] string? assetModel,
            [AllowNull] string? assetType)
        {
            switch (level)
            {
                case "Device":
                    {
                        var dataPointServer = _dataPointServer;

                        var models = await dataPointServer.GetDataPointInfos(AssetLevel.Device, assetType, assetModel);
                        List<AssetStaticModelResult> result = new List<AssetStaticModelResult>();
                        if (models != null)
                        {
                            result = models.Where(m => m.FilterIds != null && m.FilterIds.Contains("[T]")).Select(a => new AssetStaticModelResult()
                            {
                                Name = dataPointServer.GetDataPointName(a.Code, MessageContext),
                                Code = a.Code,
                            }).ToList();
                        }
                        return new ResponseBase<AssetStaticModelResult[]>()
                        {
                            Code = 20000,
                            Data = result.ToArray()
                        };
                    }
                case "Circuit":
                    {
                        var dataPointServer = _dataPointServer;
                        var models = await dataPointServer.GetDataPointInfos(AssetLevel.Circuit);
                        List<AssetStaticModelResult> result = new List<AssetStaticModelResult>();
                        if (models != null)
                        {
                            result = models.Where(m => m.FilterIds != null && m.FilterIds.Contains("[T]")).Select(a => new AssetStaticModelResult()
                            {
                                Name = dataPointServer.GetDataPointName(a.Code, MessageContext),
                                Code = a.Code,
                            }).ToList();
                        }
                        return new ResponseBase<AssetStaticModelResult[]>()
                        {
                            Code = 20000,
                            Data = result.ToArray()
                        };
                    }
                case "Panel":
                    {
                        var dataPointServer = _dataPointServer;
                        var models = await dataPointServer.GetDataPointInfos(AssetLevel.Panel);
                        List<AssetStaticModelResult> result = new List<AssetStaticModelResult>();
                        if (models != null)
                        {
                            result = models.Where(m => m.FilterIds != null && m.FilterIds.Contains("[T]")).Select(a => new AssetStaticModelResult()
                            {
                                Name = dataPointServer.GetDataPointName(a.Code, MessageContext),
                                Code = a.Code,
                            }).ToList();
                        }
                        return new ResponseBase<AssetStaticModelResult[]>()
                        {
                            Code = 20000,
                            Data = result.ToArray()
                        };
                    }
                case "Substation":
                    {
                        var dataPointServer = _dataPointServer;
                        var models = await dataPointServer.GetDataPointInfos(AssetLevel.Substation);
                        List<AssetStaticModelResult> result = new List<AssetStaticModelResult>();
                        if (models != null)
                        {
                            result = models.Where(m => m.FilterIds != null && m.FilterIds.Contains("[T]")).Select(a => new AssetStaticModelResult()
                            {
                                Name = dataPointServer.GetDataPointName(a.Code, MessageContext),
                                Code = a.Code,
                            }).ToList();
                        }
                        return new ResponseBase<AssetStaticModelResult[]>()
                        {
                            Code = 20000,
                            Data = result.ToArray()
                        };
                    }
                default:
                    return new ResponseBase<AssetStaticModelResult[]>()
                    {
                        Code = 20000,
                        Data = new AssetStaticModelResult[0]
                    };
            }
        }

        [HttpGet("{assetId}/GetBreakerProtectionSetting")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetBreakerProtectionSetting", Description = "Swagger_Asset_GetBreakerProtectionSetting_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<ProtectionSetting>> GetBreakerProtectionSetting(int assetId)
        {
            var setting = await _client.Queryable<BreakerProtectionSetting>().FirstAsync(ps => ps.AssetId == assetId);
            var settingObj = new ProtectionSetting();
            if (setting != null)
            {
                var func = _provider.GetRequiredService<ObjectReflectFunc>();
                settingObj = func.UpdateObjByObj(settingObj, setting);
            }
            return new ResponseBase<ProtectionSetting>()
            {
                Code = 20000,
                Data = settingObj
            };
        }

        [HttpGet("GetBreakerProtectionSettings")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetBreakerProtectionSettings", Description = "Swagger_Asset_GetBreakerProtectionSettings_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<Dictionary<int, ProtectionSetting>>> GetBreakerProtectionSettings([FromQuery] string[] assetIds)
        {
            var assetIdInts = new List<int>();
            assetIds.ForEach(a =>
            {
                if (int.TryParse(a, out var value))
                {
                    assetIdInts.Add(value);
                }
            });

            var settings = await _client.Queryable<BreakerProtectionSetting>().Where(ps => assetIdInts.Contains(ps.AssetId)).ToArrayAsync();
            var data = new Dictionary<int, ProtectionSetting>();
            var func = _provider.GetRequiredService<ObjectReflectFunc>();
            foreach (var setting in settings)
            {
                if (setting == null) continue;

                var settingObj = new ProtectionSetting();
                settingObj = func.UpdateObjByObj(settingObj, setting);

                data.Add(setting.AssetId, settingObj);
            }
            return new ResponseBase<Dictionary<int, ProtectionSetting>>()
            {
                Code = 20000,
                Data = data
            };
        }

        [HttpGet("{assetId}/RefreshBreakerProtectionSetting")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetTemperatureDataPoints", Description = "Swagger_Asset_GetTemperatureDataPoints_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<ProtectionSetting>> RefreshBreakerProtectionSetting(int assetId)
        {
            var protectionSettingService = _provider.GetRequiredService<DeviceProtectionSettingService>();
            var syncResult = await protectionSettingService.SyncDeviceProtectionSetting(assetId, _client, UserName);

            if (!syncResult)
            {
                return new ResponseBase<ProtectionSetting>
                {
                    Code = 20000,
                    Message = MessageContext.ErrorParam
                };
            }

            var setting = await _client.Queryable<BreakerProtectionSetting>().FirstAsync(ps => ps.AssetId == assetId);
            var settingObj = new ProtectionSetting();
            if (setting != null)
            {
                var func = _provider.GetRequiredService<ObjectReflectFunc>();
                settingObj = func.UpdateObjByObj(settingObj, setting);
            }
            return new ResponseBase<ProtectionSetting>()
            {
                Code = 20000,
                Data = settingObj
            };
        }

        [HttpPost("Scan")]
        [SwaggerOperation(Summary = "Swagger_Asset_Scan", Description = "Swagger_Asset_Scan_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> Scan(ScanParam scanParam)
        {
            var scanJobId = _cache.Get<string>("ScanJobId");

            if (!string.IsNullOrWhiteSpace(scanJobId))
            {
                return new ResponseBase<string>()
                {
                    Data = string.Empty
                };
            }

            #region 扫描之前清空一下缓存

            //var assetIds = _cache.Get<List<int>>("asset_info_assetIds");

            //if (assetIds != null && assetIds.Any())
            //{
            //    foreach (var item in assetIds)
            //    {
            //        //清空缓存
            //        _cache.Clear(string.Format(AssetCurrentStatusCacheKey, item));
            //    }
            //}

            #endregion

            var param = new Dictionary<string, string>();
            var jobInfo = new JobInfo();
            param.Add("JobId", jobInfo.JobId);
            param.Add("InterfaceName", string.IsNullOrWhiteSpace(scanParam.InterfaceName) ? "X2P1" : scanParam.InterfaceName);
            param.Add("ParentIp", scanParam.ParentIp!);
            param.Add("UserName", UserName);

            _cache.Set($"JobId:{jobInfo.JobId}", jobInfo, TimeSpan.FromMinutes(1));
            _cache.Set("ScanJobId", jobInfo.JobId, TimeSpan.FromHours(5));

            await _jobManager.TriggerJob("DeviceScanJob", param);
            return new ResponseBase<string>()
            {
                Data = jobInfo.JobId
            };
        }

        [HttpPost("ScanBindAsset")]
        [SwaggerOperation(Summary = "Swagger_Asset_ScanBindAsset", Description = "Swagger_Asset_ScanBindAsset_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> ScanBindAsset(ScanBindAssetParam scanBindAssetParam)
        {
            var assetInfo = await _client.Queryable<AssetInfo>().FirstAsync(a => a.Id == scanBindAssetParam.AssetId && a.AssetLevel == AssetLevel.Device);

            if (assetInfo == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.AssetNotExists
                };
            }

            if (string.IsNullOrWhiteSpace(assetInfo.IPAddress))
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.ErrorParam
                };
            }

            var ipArray = assetInfo.IPAddress.Split("/");
            var udcScanDatas = await _client.Queryable<UdcScanData>()
                .Where(a => a.IpAddress == ipArray[0] && a.InterfaceName == scanBindAssetParam.InterfaceName)
                .ToListAsync();

            string? udcItemId = string.Empty;
            if (udcScanDatas != null && udcScanDatas.Any())
            {
                UdcAddDevice udcAddDevice;

                var udcScan = udcScanDatas.FirstOrDefault(a => (ipArray.Length == 1 && !a.UnitId.HasValue) || (ipArray.Length == 2 && a.UnitId.HasValue));

                if (udcScan == null || udcScan.TypeName != assetInfo.AssetModel)
                {
                    return new ResponseBase<string>()
                    {
                        Code = 40400,
                        Message = MessageContext.ErrorParam
                    };
                }

                if (string.IsNullOrWhiteSpace(udcScan?.ItemId))
                {
                    if (ipArray.Length == 1)
                    {
                        if (string.IsNullOrWhiteSpace(udcScan!.ItemId))
                        {
                            udcAddDevice = new();
                            udcAddDevice.Address = udcScan.IpAddress;
                            udcAddDevice.Name = $"{udcScan.TypeName} {udcScan.IpAddress}";
                            udcAddDevice.TypeName = udcScan.TypeName;

                            var currentAddResult = await _udcHttpService.AddDeviceToUdcProjectAsync(udcAddDevice);

                            udcItemId = currentAddResult?.Id;
                        }
                    }
                    else if (ipArray.Length == 2)
                    {
                        var parentDevice = udcScanDatas.FirstOrDefault(a => !a.UnitId.HasValue);
                        if (parentDevice == null)
                        {
                            return new ResponseBase<string>()
                            {
                                Code = 40400,
                                Message = "上级网关丢失"
                            };
                        }

                        if (string.IsNullOrWhiteSpace(parentDevice.ItemId))
                        {
                            udcAddDevice = new();
                            udcAddDevice.Address = parentDevice.IpAddress;
                            udcAddDevice.Name = $"{parentDevice.TypeName} {parentDevice.IpAddress}";
                            udcAddDevice.TypeName = parentDevice.TypeName;

                            var parentAddResult = await _udcHttpService.AddDeviceToUdcProjectAsync(udcAddDevice);

                            parentDevice.ItemId = parentAddResult?.Id;

                            await _client.Updateable<UdcScanData>().SetColumns(a => a.ItemId == parentDevice.ItemId).Where(a => a.Id == parentDevice.Id).ExecuteCommandAsync();
                        }

                        udcAddDevice = new();
                        udcAddDevice.Address = $"{udcScan!.IpAddress}/{udcScan.UnitId}";
                        udcAddDevice.Name = $"{udcScan.TypeName} {udcScan.IpAddress}/{udcScan.UnitId}";
                        udcAddDevice.TypeName = udcScan.TypeName;
                        udcAddDevice.ParentId = parentDevice.ItemId;
                        udcAddDevice.GwParentId = parentDevice.ItemId;

                        var udcNewAddResult = await _udcHttpService.AddDeviceToUdcProjectAsync(udcAddDevice);
                        udcItemId = udcNewAddResult?.Id;
                    }
                }
                else
                {
                    udcItemId = udcScan.ItemId;
                }

                if (string.IsNullOrWhiteSpace(udcItemId))
                {
                    return new ResponseBase<string>()
                    {
                        Code = 40400,
                        Data = MessageContext.ErrorParam
                    };
                }

                await _client.Updateable<AssetInfo>()
                    .SetColumns(a => a.ObjectId == udcItemId)
                    .Where(a => a.Id == assetInfo.Id)
                    .ExecuteCommandAsync();

                await _client.Updateable<UdcScanData>()
                    .SetColumns(a => new UdcScanData
                    {
                        AssetId = assetInfo.Id,
                        AssetIpAddress = assetInfo.IPAddress,
                        AssetName = assetInfo.AssetName,
                        AssetModel = assetInfo.AssetModel,
                        AssetNumber = assetInfo.AssetNumber,
                        AssetType = assetInfo.AssetType,
                        ObjectId = udcItemId,
                        ItemId = udcItemId,
                        ImportStatus = DeviceImportStatus.Imported.GetHashCode()
                    }).Where(a => a.Id == udcScan!.Id).ExecuteCommandAsync();

                return new ResponseBase<string>()
                {
                    Data = udcItemId
                };
            }


            return new ResponseBase<string>()
            {
                Code = 40400,
                Data = MessageContext.ErrorParam
            };
        }

        [HttpGet("ScanStatus/{jobId}")]
        [SwaggerOperation(Summary = "Swagger_Asset_ScanStatus", Description = "Swagger_Asset_ScanStatus_Desc")]
        [Authorize(policy: Permission.Default)]
        public ResponseBase<JobResult> ScanStatus(string jobId)
        {
            var jobInfo = _cache.Get<JobInfo>($"JobId:{jobId}");
            if (jobInfo == null)
            {
                return new ResponseBase<JobResult>()
                {
                    Code = 40400,
                    Message = MessageContext.GetErrorValue("Job_NotExists")
                };
            }

            object? result = null;
            string? message = null;
            string? sucessMessage = null;

            if (jobInfo.Result != null && jobInfo.Result.ErrorInfo != null && jobInfo.Result.ErrorInfo.Any())
            {
                var successStringBuilder = new StringBuilder();
                var messageStringBuilder = new StringBuilder();
                foreach (var errorCode in jobInfo.Result.ErrorInfo)
                {
                    if (errorCode.Contains("Success"))
                    {
                        successStringBuilder.AppendLine(MessageContext.GetErrorValue(errorCode));
                    }
                    else
                    {
                        messageStringBuilder.AppendLine(MessageContext.GetErrorValue(errorCode));
                    }
                }
                message = messageStringBuilder.ToString();
                sucessMessage = successStringBuilder.ToString();
            }
            else
            {
                result = jobInfo.Result;
            }

            return new ResponseBase<JobResult>()
            {
                Code = 20000,
                Data = new JobResult()
                {
                    Status = jobInfo.JobStatus,
                    Result = result,
                    ResultMessage = message,
                    SuccessMessage = sucessMessage
                }
            };
        }

        [HttpGet("CheckDeviceIsGateWay")]
        [SwaggerOperation(Summary = "Swagger_Asset_CheckDeviceIsGateWay", Description = "Swagger_Asset_CheckDeviceIsGateWay_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<bool>> CheckDeviceIsGateWay(int deviceId)
        {
            var deviceScanInfo = await _client.Queryable<UdcScanData>().Where(a => a.AssetId == deviceId).FirstAsync();
            if (deviceScanInfo != null)
            {
                return new ResponseBase<bool>()
                {
                    Code = 20000,
                    Data = deviceScanInfo.IsGateway
                };
            }
            else
            {
                return new ResponseBase<bool>()
                {
                    Code = 20000,
                    Data = false
                };
            }
        }

        [HttpGet("ScanResult")]
        [SwaggerOperation(Summary = "Swagger_Asset_ScanResult", Description = "Swagger_Asset_ScanResult_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<SearchBase<AssetScanResult>> ScanResult([FromQuery] ScanResultQuery scanResultQuery)
        {
            if (!int.TryParse(scanResultQuery.ImportStatus, out var tempImportStatus))
            {
                tempImportStatus = -1;
            }

            var udcScanData = await _client.Queryable<UdcScanData>()
                .ToListAsync();
            var assetDate = await _client.Queryable<AssetInfo>()
                .ToListAsync();
            if (string.IsNullOrWhiteSpace(scanResultQuery.InterfaceName))
            {
                scanResultQuery.InterfaceName = "X2P1";
            }

            if (udcScanData != null && udcScanData.Any())
            {
                List<AssetScanResult> assetScanResults = new List<AssetScanResult>();
                Dictionary<string, int> parentIndexs = new Dictionary<string, int>();

                AssetScanResult? assetScanResult = null;
                for (int i = 0; i < udcScanData.Count; i++)
                {
                    bool isExist = UniversalDeviceInfo.Instance._dicData.ContainsKey(udcScanData[i].AssetId.ToString() ?? "0");

                    assetScanResult = new AssetScanResult
                    {
                        Id = udcScanData[i].Id,
                        AssetId = udcScanData[i].AssetId,
                        AssetName = udcScanData[i].AssetName ?? udcScanData[i].TypeDisplayName,
                        AssetType = udcScanData[i].AssetType,
                        AssetModel = udcScanData[i].AssetModel ?? udcScanData[i].TypeName,
                        AssetNumber = udcScanData[i].AssetNumber,
                        AssetIpAddress = udcScanData[i].AssetIpAddress ?? udcScanData[i].IpAddress + (udcScanData[i].UnitId.HasValue && udcScanData[i].UnitId > 0 ? $"/{udcScanData[i].UnitId}" : string.Empty),
                        ObjectId = udcScanData[i].ObjectId,
                        UdcNetworkInterface = udcScanData[i].UdcNetworkInterface,
                        TypeName = udcScanData[i].TypeName,
                        TypeDisplayName = udcScanData[i].TypeDisplayName,
                        IpAddress = udcScanData[i].IpAddress,
                        Port = udcScanData[i].Port,
                        Netmask = udcScanData[i].Netmask,
                        Gateway = udcScanData[i].Gateway,
                        MacAddress = udcScanData[i].MacAddress,
                        PlantIdentifier = udcScanData[i].PlantIdentifier,
                        OrderNumber = udcScanData[i].OrderNumber,
                        FirmwareVersion = udcScanData[i].FirmwareVersion,
                        BootloaderVersion = udcScanData[i].BootloaderVersion,
                        ItemId = udcScanData[i].ItemId,
                        IsGateway = udcScanData[i].IsGateway,
                        UnitId = udcScanData[i].UnitId,
                        ImportStatus = isExist ? 3 : udcScanData[i].ImportStatus,
                        InterfaceName = udcScanData[i].InterfaceName,
                        DeviceIsGateway = udcScanData[i].IsGateway
                    };

                    var assetInfo = assetDate.Where(a => a.Id == assetScanResult.AssetId).FirstOrDefault();
                    string tcpPort = string.Empty;
                    if (assetInfo != null && !string.IsNullOrEmpty(assetInfo.Port))
                    {
                        tcpPort = udcScanData[i].Port.HasValue ? $"{udcScanData[i].Port}" : assetInfo.Port;
                        assetScanResult.Ip = assetInfo.IpAddressStr;
                        assetScanResult.SlaveId = assetInfo.SlaveId;
                        assetScanResult.DevicePort = tcpPort;
                        assetScanResult.DeviceIsGateway = assetInfo.AssetType == "Gateway";
                    }
                    else
                    {
                        if (udcScanData[i].Port.HasValue)
                        {
                            tcpPort = $"{udcScanData[i].Port}";
                            assetScanResult.Ip = udcScanData[i].IpAddress;
                            assetScanResult.SlaveId = udcScanData[i].UnitId;
                            assetScanResult.DevicePort = tcpPort;
                            assetScanResult.DeviceIsGateway = udcScanData[i].IsGateway;
                        }
                    }

                    if (!string.IsNullOrEmpty(tcpPort))
                    {
                        if (assetScanResult.AssetIpAddress.Contains("/"))
                        {
                            assetScanResult.AssetIpAddress = assetScanResult.AssetIpAddress.Split("/")[0] + "-" + tcpPort + "/" + assetScanResult.AssetIpAddress.Split("/")[1];
                        }
                        else
                        {
                            assetScanResult.AssetIpAddress = assetScanResult.AssetIpAddress + "-" + tcpPort;
                        }
                    }

                    if (udcScanData[i].IsGateway
                        || (!string.IsNullOrWhiteSpace(assetScanResult.AssetIpAddress) && !assetScanResult.AssetIpAddress.Contains("/"))
                        || string.IsNullOrWhiteSpace(assetScanResult.AssetIpAddress))
                    {
                        parentIndexs.TryAdd(assetScanResult.AssetIpAddress, i);
                    }

                    assetScanResults.Add(assetScanResult);
                }

                var ipAddresses = assetScanResults.Select(r => new { r.Ip, r.DevicePort }).ToList();

                #region Old
                //assetScanResults.ForEach(a =>
                //{
                //    if (a.UnitId.HasValue && !string.IsNullOrEmpty(a.AssetIpAddress))
                //    {
                //        if (parentIndexs.TryGetValue(a.AssetIpAddress.Split("/")[0], out var parentIndex) && (tempImportStatus == -1 || a.ImportStatus == tempImportStatus))
                //        {
                //            if (assetScanResults[parentIndex].Childs == null)
                //            {
                //                assetScanResults[parentIndex].Childs = new List<AssetScanResult>();
                //            }

                //            assetScanResults[parentIndex].Childs.Add(a);
                //        }
                //    }
                //    else
                //    {
                //        if (!string.IsNullOrWhiteSpace(a.AssetIpAddress) && a.AssetIpAddress.Contains("/"))
                //        {
                //            if (parentIndexs.TryGetValue(a.AssetIpAddress.Split("/")[0], out var parentIndex) && (tempImportStatus == -1 || a.ImportStatus == tempImportStatus))
                //            {
                //                if (assetScanResults[parentIndex].Childs == null)
                //                {
                //                    assetScanResults[parentIndex].Childs = new List<AssetScanResult>();
                //                }

                //                assetScanResults[parentIndex].Childs.Add(a);
                //            }
                //        }
                //    }

                //});
                #endregion

                foreach (var ipAddress in ipAddresses)
                {
                    var subScanResult = assetScanResults.Where(a => a.Ip == ipAddress.Ip && a.DevicePort == ipAddress.DevicePort).ToList();
                    var gateway = subScanResult.FirstOrDefault(a => a.DeviceIsGateway);
                    if (gateway != null)
                    {
                        gateway.IsGateway = true;
                        foreach (var item in subScanResult)
                        {
                            if (item.DeviceIsGateway) continue;

                            gateway.Childs.Add(item);
                            assetScanResults.Remove(item);
                        }
                    }
                }

                var finalResult = assetScanResults.Where(a => a.AssetId.HasValue // 系统性包含的资产
                                                        || a.Childs.Count > 0 // 网关设备
                );

                if (!scanResultQuery.NeedPage)
                {
                    return new SearchBase<AssetScanResult>()
                    {
                        Code = 20000,
                        TotalCount = finalResult.Count(),
                        Items = finalResult.ToList(),
                    };
                }

                if (!string.IsNullOrWhiteSpace(scanResultQuery.Name))
                {
                    finalResult = finalResult.Where(a => (!string.IsNullOrWhiteSpace(a.AssetName) && a.AssetName.Contains(scanResultQuery.Name, StringComparison.OrdinalIgnoreCase)) || (a.Childs != null && a.Childs.Any(c => !string.IsNullOrWhiteSpace(c.AssetName) && c.AssetName.Contains(scanResultQuery.Name, StringComparison.OrdinalIgnoreCase))));
                }

                if (!string.IsNullOrWhiteSpace(scanResultQuery.AssetType))
                {
                    finalResult = finalResult.Where(a => (!string.IsNullOrWhiteSpace(a.AssetType) && a.AssetType.Contains(scanResultQuery.AssetType, StringComparison.OrdinalIgnoreCase)) || (a.Childs != null && a.Childs.Any(c => !string.IsNullOrWhiteSpace(c.AssetType) && c.AssetType.Contains(scanResultQuery.AssetType, StringComparison.OrdinalIgnoreCase))));
                }

                if (!string.IsNullOrWhiteSpace(scanResultQuery.InterfaceName))
                {
                    finalResult = finalResult.Where(a => !string.IsNullOrWhiteSpace(a.InterfaceName) && a.InterfaceName.Contains(scanResultQuery.InterfaceName, StringComparison.OrdinalIgnoreCase));
                }

                if (!string.IsNullOrWhiteSpace(scanResultQuery.ImportStatus))
                {
                    finalResult = finalResult.Where(a => a.ImportStatus.HasValue
                    && (a.ImportStatus == tempImportStatus || (a.Childs != null && a.Childs.Any(c => c.ImportStatus.HasValue && c.ImportStatus == tempImportStatus))));
                }

                if (!string.IsNullOrWhiteSpace(scanResultQuery.IPAddress))
                {
                    finalResult = finalResult.Where(a => (!string.IsNullOrEmpty(a.AssetIpAddress) && a.AssetIpAddress.Contains(scanResultQuery.IPAddress)) || (a.Childs != null && a.Childs.Any(c => !string.IsNullOrWhiteSpace(c.AssetIpAddress) && c.AssetIpAddress.Contains(scanResultQuery.IPAddress))));
                }

                var totalCount = finalResult.Count();
                finalResult = finalResult.OrderByDescending(a => a.ImportStatus).ThenBy(a => a.AssetName).Skip((scanResultQuery.Page - 1) * scanResultQuery.PageSize).Take(scanResultQuery.PageSize);

                return new SearchBase<AssetScanResult>()
                {
                    Code = 20000,
                    Page = scanResultQuery.Page,
                    TotalCount = totalCount,
                    Items = finalResult.ToList(),
                };
            }

            return new SearchBase<AssetScanResult>()
            {
                Code = 20000,
            };
        }

        [HttpGet("NetworkDiagram")]
        [SwaggerOperation(Summary = "Swagger_Asset_NetworkDiagram", Description = "Swagger_Asset_NetworkDiagram_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<NetworkDiagramResult[]>> NetworkDiagram([FromQuery] string? interfaceName = "X2P1")
        {
            var udcScanData = await _client.Queryable<UdcScanData>().Where(a => a.InterfaceName == interfaceName).ToListAsync();
            var assetInfo = await _client.Queryable<AssetInfo>().Where(a => !string.IsNullOrEmpty(a.Port)).ToListAsync();
            if (udcScanData != null && udcScanData.Any())
            {
                var connectStatus = _cache.Get<Dictionary<int, bool>>("AssetConnectStatus:All");
                List<NetworkDiagramResult> networkDiagramList = new List<NetworkDiagramResult>();
                Dictionary<string, List<NetworkDiagramResult>> parentIndexs = new Dictionary<string, List<NetworkDiagramResult>>();

                NetworkDiagramResult? networkDiagram = null;
                for (int i = 0; i < udcScanData.Count; i++)
                {
                    int netStatus = 0;
                    if (udcScanData[i].ImportStatus == DeviceImportStatus.NotImported.GetHashCode()
                        && !udcScanData[i].IsGateway)
                    {
                        continue;
                    }

                    if (connectStatus != null && udcScanData[i].AssetId.GetValueOrDefault() > 0)
                    {
                        netStatus = connectStatus.GetValueOrDefault(udcScanData[i].AssetId ?? 0) ? 1 : 0;
                    }

                    networkDiagram = new NetworkDiagramResult
                    {
                        AssetId = udcScanData[i].AssetId,
                        AssetName = udcScanData[i].AssetName ?? udcScanData[i].TypeDisplayName,
                        AssetType = udcScanData[i].AssetType,
                        AssetModel = udcScanData[i].AssetModel ?? udcScanData[i].TypeName,
                        AssetIpAddress = udcScanData[i].AssetIpAddress ?? udcScanData[i].IpAddress + (udcScanData[i].UnitId.HasValue && udcScanData[i].UnitId > 0 ? $"/{udcScanData[i].UnitId}" : string.Empty),
                        ObjectId = udcScanData[i].ObjectId,
                        UdcNetworkInterface = udcScanData[i].UdcNetworkInterface,
                        IpAddress = udcScanData[i].IpAddress,
                        Port = udcScanData[i].Port,
                        Netmask = udcScanData[i].Netmask,
                        Gateway = udcScanData[i].Gateway,
                        MacAddress = udcScanData[i].MacAddress,
                        PlantIdentifier = udcScanData[i].PlantIdentifier,
                        OrderNumber = udcScanData[i].OrderNumber,
                        FirmwareVersion = udcScanData[i].FirmwareVersion,
                        BootloaderVersion = udcScanData[i].BootloaderVersion,
                        ItemId = udcScanData[i].ItemId,
                        IsGateway = udcScanData[i].IsGateway,
                        UnitId = udcScanData[i].UnitId,
                        ImportStatus = udcScanData[i].ImportStatus,
                        InterfaceName = udcScanData[i].InterfaceName,
                        NetStatus = netStatus,
                        DeviceIsGateway = udcScanData[i].IsGateway,
                    };

                    var asset = assetInfo.FirstOrDefault(a => a.Id == networkDiagram.AssetId);
                    if (asset != null)
                    {
                        if (!string.IsNullOrEmpty(asset.IpAddressStr))
                        {
                            int p = 0;

                            if (networkDiagram.Port.HasValue)
                            {
                                p = networkDiagram.Port.Value;
                            }
                            else
                            {
                                int.TryParse(asset.Port, out p);
                            }

                            var ipAddress = $"{asset.IpAddressStr}:{p}";

                            if (parentIndexs.TryGetValue(ipAddress, out var parentIndex))
                            {
                                if (parentIndex == null)
                                {
                                    parentIndex = new List<NetworkDiagramResult>();
                                    parentIndexs[ipAddress] = parentIndex;
                                }
                                parentIndex.Add(networkDiagram);
                            }
                            else
                            {
                                parentIndexs.Add(ipAddress, new List<NetworkDiagramResult> { networkDiagram });
                            }

                            networkDiagram.DeviceIsGateway = asset.AssetType == "Gateway";
                        }
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(networkDiagram.IpAddress) && networkDiagram.Port.HasValue)
                        {
                            var ipAddress = $"{networkDiagram.IpAddress}:{networkDiagram.Port}";


                            if (parentIndexs.TryGetValue(ipAddress, out var parentIndex))
                            {
                                if (parentIndex == null)
                                {
                                    parentIndex = new List<NetworkDiagramResult>();
                                    parentIndexs[ipAddress] = parentIndex;
                                }
                                parentIndex.Add(networkDiagram);
                            }
                            else
                            {
                                parentIndexs.Add(ipAddress, new List<NetworkDiagramResult> { networkDiagram });
                            }
                        }
                    }

                    networkDiagramList.Add(networkDiagram);
                }

                #region Old
                //networkDiagramList.ForEach(a =>
                //{
                //    var asset = assetInfo.FirstOrDefault(b => b.Id == a.AssetId);
                //    if (a.UnitId.HasValue && !string.IsNullOrEmpty(a.IpAddress))
                //    {
                //        string ipAddress = a.IpAddress;
                //        if (asset != null)
                //        {
                //            ipAddress = a.IpAddress + ":" + asset.Port;
                //        }
                //        if (parentIndexs.TryGetValue(ipAddress, out var parentIndex))
                //        {
                //            if (networkDiagramList[parentIndex].Childs == null)
                //            {
                //                networkDiagramList[parentIndex].Childs = new List<NetworkDiagramResult>();
                //            }

                //            networkDiagramList[parentIndex].Childs.Add(a);
                //        }
                //    }
                //    else
                //    {
                //        if (!string.IsNullOrWhiteSpace(a.AssetIpAddress) && a.AssetIpAddress.Contains("/"))
                //        {
                //            string ipAddress = a.AssetIpAddress.Split("/")[0];

                //            if (asset != null)
                //            {
                //                ipAddress = a.AssetIpAddress.Split("/")[0] + ":" + asset.Port;
                //            }
                //            if (parentIndexs.TryGetValue(ipAddress, out var parentIndex))
                //            {
                //                if (networkDiagramList[parentIndex].Childs == null)
                //                {
                //                    networkDiagramList[parentIndex].Childs = new List<NetworkDiagramResult>();
                //                }

                //                networkDiagramList[parentIndex].Childs.Add(a);
                //            }
                //        }
                //    }
                //});

                #endregion

                #region New
                foreach (var parentItem in parentIndexs)
                {
                    if (parentItem.Value.Count > 0)
                    {
                        var parent = parentItem.Value.FirstOrDefault(p => p.DeviceIsGateway);
                        if (parent != null)
                        {
                            var clients = parentItem.Value.Where(a => a != parent).ToList();

                            foreach (var c in clients)
                            {
                                if (parent.Childs == null)
                                {
                                    parent.Childs = new List<NetworkDiagramResult>();
                                }
                                parent.Childs.Add(c);
                                networkDiagramList.Remove(c);
                            }
                        }
                    }
                }
                #endregion

                //var finalResult = networkDiagramList.Where(a => (!string.IsNullOrWhiteSpace(a.AssetIpAddress)
                //&& !a.AssetIpAddress.Contains("/"))
                //|| (!a.UnitId.HasValue && !string.IsNullOrEmpty(a.IpAddress))
                //|| (string.IsNullOrWhiteSpace(a.AssetIpAddress) && string.IsNullOrWhiteSpace(a.IpAddress))).OrderBy(a => a.AssetIpAddress);

                var finalResult = networkDiagramList.Where(a => a.AssetId.HasValue // 系统性包含的资产
                                                        || a.Childs.Count > 0 // 网关设备
                );

                return new ResponseBase<NetworkDiagramResult[]>()
                {
                    Code = 20000,
                    Data = finalResult.ToArray()
                };
            }

            return new ResponseBase<NetworkDiagramResult[]>()
            {
                Code = 20000,
                Data = new NetworkDiagramResult[0]
            };
        }

        [HttpGet("SearchMLFB")]
        [SwaggerOperation(Summary = "Swagger_Asset_SearchMLFB", Description = "Swagger_Asset_SearchMLFB_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<SearchBase<string>> SearchMLFB()
        {
            var mlfbList = await _client.Queryable<AssetInfo>()
                .LeftJoin<DeviceDetails>((a, dd) => a.Id == dd.AssetId)
                .Where((a, dd) => !string.IsNullOrEmpty(dd.MLFB) || !string.IsNullOrEmpty(a.MLFB))
                .Select((a, dd) => dd.MLFB ?? a.MLFB ?? string.Empty)
                .ToListAsync();

            return new SearchBase<string>()
            {
                Items = mlfbList,
                Page = 1,
                Code = 20000,
                TotalCount = mlfbList.Count
            };
        }

        [HttpPost("BreakerProtectCheck")]
        [SwaggerOperation(Summary = "Swagger_Asset_BreakerProtectCheck", Description = "Swagger_Asset_BreakerProtectCheck_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<BreakerProtectCheckResult>> BreakerProtectCheck([FromBody] List<BreakerProtectCheckParam> checkParams)
        {
            var protectCheckResult = new BreakerProtectCheckResult();
            List<ProtectSingleCheck> allChecks = new List<ProtectSingleCheck>();

            if (checkParams == null || !checkParams.Any())
            {
                return new ResponseBase<BreakerProtectCheckResult>()
                {
                    Code = 20000,
                    Data = protectCheckResult
                };
            }

            var notCheckPassAssetIds = new List<int>();
            var breakerCheck = _provider.GetRequiredService<BreakerProtectionCheckServer>();
            var allFeeder = await _client.Queryable<AssetInfo>()
                .Where(a => a.AssetLevel == AssetLevel.Circuit)
                .Select(a => new { a.Id, a.AssetName, a.CircuitName, a.UseScene })
                .ToListAsync();

            var str = "UseScene".ToUpper();
            var useSceneList = await _client.Queryable<SystemStaticModel>()
                .Where(s => s.Type == str)
                .WithCache($"SystemStaticModel:{str}", (int)TimeSpan.FromHours(1).TotalSeconds)
                .ToArrayAsync();

            foreach (var checkParam in checkParams)
            {
                var breakers = await _client.Queryable<BreakerProtectionSetting>()
                    .Where(a => a.AssetId == checkParam.Parent || SqlFunc.ContainsArray(checkParam.Child, a.AssetId))
                    .ToListAsync();

                var assetDetails = await _client.Queryable<DeviceDetails>()
                    .Where(a => a.AssetId == checkParam.Parent || SqlFunc.ContainsArray(checkParam.Child, a.AssetId))
                    .ToListAsync();

                var assetRelations = await _client.Queryable<AssetRelation>()
                    .Where(a => a.ChildId == checkParam.Parent || SqlFunc.ContainsArray(checkParam.Child, a.ChildId))
                    .ToListAsync();

                var parentRelation = assetRelations.FirstOrDefault(a => a.ChildId == checkParam.Parent);
                var parentAsset = allFeeder.FirstOrDefault(a => a.Id == (parentRelation?.ParentId ?? 0));
                var parentCircuitName = parentAsset?.AssetName ?? string.Empty;
                var parentUseScene = useSceneList.FirstOrDefault(a => a.Code == (parentAsset?.UseScene ?? string.Empty))?.Name ?? string.Empty;

                if (checkParam.Child != null && checkParam.Child.Any())
                {
                    ProtectSingleCheck protectSingleCheck;
                    foreach (var assetChildId in checkParam.Child)
                    {
                        var childRelation = assetRelations.FirstOrDefault(a => a.ChildId == assetChildId);
                        var childAsset = allFeeder.FirstOrDefault(a => a.Id == (childRelation?.ParentId ?? 0));
                        var childCircuitName = childAsset?.AssetName ?? string.Empty;
                        var childUseScene = useSceneList.FirstOrDefault(a => a.Code == (childAsset?.UseScene ?? string.Empty))?.Name ?? string.Empty;

                        var tempCheck = breakerCheck.Check(checkParam.Parent, assetChildId, breakers, assetDetails);

                        if (tempCheck != null)
                        {
                            if (!tempCheck.CheckPass)
                            {
                                protectSingleCheck = new ProtectSingleCheck();

                                if (!notCheckPassAssetIds.Contains(checkParam.Parent))
                                {
                                    notCheckPassAssetIds.Add(checkParam.Parent);
                                }

                                notCheckPassAssetIds.Add(assetChildId);

                                protectSingleCheck.AssetId = assetChildId;
                                protectSingleCheck.BusBarId = checkParam.BusBarId ?? string.Empty;
                                protectSingleCheck.CheckPass = tempCheck.CheckPass;
                                protectSingleCheck.AbnormalProtection = tempCheck.ErrorSpan.ToArray();
                                protectSingleCheck.Charts.Add(new SingleChart
                                {
                                    UpAssetId = checkParam.Parent,
                                    UpCircuitName = parentCircuitName,
                                    UpModel = parentUseScene,
                                    UpProtectSettings = tempCheck.UpSettings,
                                    LowerProtectSettings = tempCheck.LowerSettings,
                                    LowerCircuitName = childCircuitName,
                                    LowerModel = childUseScene,
                                    LowerAssetId = assetChildId,
                                    UpXY = tempCheck.CheckData.Select(a => new decimal[] { a.x, a.y1 }).ToArray(),
                                    LowerXY = tempCheck.CheckData.Select(a => new decimal[] { a.x, a.y2 }).ToArray(),
                                    CrossSection = tempCheck.CrossSection
                                });

                                allChecks.Add(protectSingleCheck);
                            }
                            else
                            {
                                if (checkParam.ShowCheckPass)
                                {
                                    protectSingleCheck = new ProtectSingleCheck();

                                    protectSingleCheck.AssetId = assetChildId;
                                    protectSingleCheck.BusBarId = checkParam.BusBarId ?? string.Empty;
                                    protectSingleCheck.CheckPass = tempCheck.CheckPass;
                                    protectSingleCheck.AbnormalProtection = tempCheck.ErrorSpan.ToArray();
                                    protectSingleCheck.Charts.Add(new SingleChart
                                    {
                                        UpAssetId = checkParam.Parent,
                                        UpCircuitName = parentCircuitName,
                                        UpModel = parentUseScene,
                                        UpProtectSettings = tempCheck.UpSettings,
                                        LowerProtectSettings = tempCheck.LowerSettings,
                                        LowerCircuitName = childCircuitName,
                                        LowerModel = childUseScene,
                                        LowerAssetId = assetChildId,
                                        UpXY = tempCheck.CheckData.Select(a => new decimal[] { a.x, a.y1 }).ToArray(),
                                        LowerXY = tempCheck.CheckData.Select(a => new decimal[] { a.x, a.y2 }).ToArray(),
                                    });
                                    allChecks.Add(protectSingleCheck);
                                }
                            }
                        }
                    }

                    // current bus parent check
                    var currentBusChecks = allChecks.Where(a => a.BusBarId == checkParam.BusBarId && a.AssetId != checkParam.Parent).ToList();
                    if (currentBusChecks.Any())
                    {
                        protectSingleCheck = new ProtectSingleCheck();
                        protectSingleCheck.BusBarId = checkParam.BusBarId ?? string.Empty;
                        protectSingleCheck.AssetId = checkParam.Parent;
                        protectSingleCheck.CheckPass = !currentBusChecks.Any(a => !a.CheckPass);

                        var parentAbnormalProtection = new List<string>();
                        foreach (var checkItem in currentBusChecks)
                        {
                            if (checkItem.AbnormalProtection != null && checkItem.AbnormalProtection.Any())
                            {
                                checkItem.AbnormalProtection.ForEach(a =>
                                {
                                    if (!parentAbnormalProtection.Contains(a))
                                    {
                                        parentAbnormalProtection.Add(a);
                                    }
                                });
                            }

                            protectSingleCheck.Charts.AddRange(checkItem.Charts);
                        }

                        protectSingleCheck.AbnormalProtection = parentAbnormalProtection.ToArray();

                        allChecks.Add(protectSingleCheck);
                    }
                }
            }

            protectCheckResult.ProtectChecks = allChecks;
            protectCheckResult.NotCheckPassAssetIds = notCheckPassAssetIds;

            return new ResponseBase<BreakerProtectCheckResult>()
            {
                Code = 20000,
                Data = protectCheckResult
            };
        }

        /// <summary>
        /// 用户选择数据点
        /// </summary>
        /// <param name="assetId"></param>
        /// <returns></returns>
        [HttpGet("GetUserCheckGeneralDevicePoint")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetUserCheckGeneralDevicePoint", Description = "Swagger_Asset_GetUserCheckGeneralDevicePoint_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> GetUserCheckGeneralDevicePoint(int assetId)
        {
            ResponseBase<List<UserCheckPointEntity>> result;

            try
            {
                #region Old
                //var thirdModelConfig = await _client.Queryable<ThirdModelConfig>()
                //    .LeftJoin<AssetInfo>((t1, t2) => t1.Code == t2.ThirdPartCode)
                //    .Where((t1, t2) => t2.Id == assetId)
                //    .Select((t1, t2) => new
                //    {
                //        t1.Code,
                //        t1.JsonData

                //    }).FirstAsync();

                //if (thirdModelConfig == null)
                //{
                //    return Ok(new ResponseBase<List<UserCheckPointEntity>>() { Code = 20000, Data = new List<UserCheckPointEntity>() });
                //}

                //var jsonData = JsonConvert.DeserializeObject<ThreePartModelTemplate>(thirdModelConfig.JsonData ?? "");

                ////获取当前json文件点位配置集合
                //var universalDeviceConfigs = await _client.Queryable<UniversalDeviceConfig>()
                //    .Where(p => p.AssetId == assetId).ToListAsync();

                ////二进制点位
                //List<int> universalDeviceConfigIds =
                //    universalDeviceConfigs.Where(p => p.IsBit).Select(p => p.Id).ToList();

                //var bitConfigs = await _client.Queryable<BitConfig>().Where(p => universalDeviceConfigIds.Contains(p.UniversalDeviceConfigId))
                //    .ToListAsync();

                //var pointData = new List<UserCheckPointEntity>();

                //if (jsonData != null && jsonData.Treeview != null && jsonData.Treeview.Any())
                //{
                //    foreach (var item in jsonData.Treeview!)
                //    {
                //        //获取子集集合
                //        if (item.SubGroups != null && item.SubGroups!.Any())
                //        {
                //            foreach (var _item in item.SubGroups!)
                //            {
                //                if (_item.Properties != null && _item.Properties.Any())
                //                {
                //                    foreach (var secondItem in _item.Properties!)
                //                    {

                //                        var model = universalDeviceConfigs
                //                            .FirstOrDefault(p => p.PropertyEnName == secondItem.PropertyName);

                //                        if (model != null && model.IsBit)
                //                        {
                //                            var bitConfigList = bitConfigs.Where(p => p.UniversalDeviceConfigId == model.Id).ToList();

                //                            if (bitConfigList.Any())
                //                            {
                //                                foreach (var _bitItem in bitConfigList)
                //                                {
                //                                    pointData.Add(new UserCheckPointEntity()
                //                                    {
                //                                        Id = Guid.NewGuid().ToString(),
                //                                        PropertyEnName = secondItem.PropertyName,
                //                                        PropertyCnName = _bitItem.BitName,
                //                                        Unit = secondItem.Unit ?? "",
                //                                        IsBit = true
                //                                    });
                //                                }
                //                            }
                //                        }
                //                        else
                //                        {
                //                            pointData.Add(new UserCheckPointEntity()
                //                            {
                //                                Id = Guid.NewGuid().ToString(),
                //                                PropertyEnName = secondItem.PropertyName,
                //                                PropertyCnName = model?.PropertyCnName ?? secondItem.DescriptionInGerman ?? secondItem.DescriptionInEnglish,
                //                                Unit = secondItem.Unit ?? "",
                //                                IsBit = false
                //                            });
                //                        }
                //                    }
                //                }
                //            }
                //        }

                //        if (item.Properties != null && item.Properties.Any())
                //        {
                //            foreach (var _item in item.Properties)
                //            {
                //                var model = universalDeviceConfigs
                //                    .FirstOrDefault(p => p.PropertyEnName == _item.PropertyName);

                //                if (model != null && model.IsBit)
                //                {
                //                    var bitConfigList = bitConfigs.Where(p => p.UniversalDeviceConfigId == model.Id).ToList();

                //                    if (bitConfigList.Any())
                //                    {
                //                        foreach (var _bitItem in bitConfigList)
                //                        {
                //                            pointData.Add(new UserCheckPointEntity()
                //                            {
                //                                Id = Guid.NewGuid().ToString(),
                //                                PropertyEnName = _item.PropertyName,
                //                                PropertyCnName = _bitItem.BitName,
                //                                Unit = _item.Unit ?? "",
                //                                IsBit = true
                //                            });
                //                        }
                //                    }
                //                }
                //                else
                //                {
                //                    pointData.Add(new UserCheckPointEntity()
                //                    {
                //                        Id = Guid.NewGuid().ToString(),
                //                        PropertyEnName = _item.PropertyName,
                //                        PropertyCnName = model?.PropertyCnName ?? _item.DescriptionInGerman ?? _item.DescriptionInEnglish,
                //                        Unit = _item.Unit ?? "",
                //                        IsBit = false
                //                    });
                //                }
                //            }
                //        }
                //    }
                //}

                //result = new ResponseBase<List<UserCheckPointEntity>>() { Code = 20000, Data = pointData };
                #endregion

                var list = new List<UserCheckPointEntity>();

                var generalDevicePoints = await GetGeneralDevicePoint(assetId);

                foreach (var group in generalDevicePoints)
                {
                    if (group == null || group.UserCheckPoints == null || group.UserCheckPoints.Count <= 0) continue;

                    list.AddRange(group.UserCheckPoints);
                }

                result = new ResponseBase<List<UserCheckPointEntity>>()
                {
                    Code = 20000,
                    Data = list
                };
            }
            catch (Exception ex)
            {
                result = new ResponseBase<List<UserCheckPointEntity>>() { Code = 50000, Message = ex.Message };
            }

            return Ok(result);
        }

        /// <summary>
        /// 根据资产id获取点位配置信息
        /// </summary>
        /// <param name="assetId"></param>
        /// <param name="code"></param>
        /// <returns></returns>
        [HttpGet("GetPointConfigs")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetPointConfigs", Description = "Swagger_Asset_GetPointConfigs_Desc")]
        public async Task<IActionResult> GetPointConfigs(int assetId = 0, string code = "")
        {
            ResponseBase<List<UniversalDeviceConfigResult>> result;

            try
            {

                var thirdModelConfig = await _client.Queryable<ThirdModelConfig>()
                    .LeftJoin<AssetInfo>((t1, t2) => t1.Code == t2.ThirdPartCode)
                    .Where((t1, t2) => t2.Id == assetId)
                    .Select((t1, t2) => new
                    {
                        t1.Code,
                        t1.JsonData

                    }).FirstAsync();

                if (thirdModelConfig == null && !string.IsNullOrEmpty(code))
                {
                    thirdModelConfig = await _client.Queryable<ThirdModelConfig>().Where(a => a.Code == code)
                        .Select(a => new
                        {
                            a.Code,
                            a.JsonData

                        }).FirstAsync();
                    var asset = await _client.Queryable<AssetInfo>().Where(a => a.ThirdPartCode == code).FirstAsync();
                    if (asset != null)
                    {
                        assetId = asset.Id;
                    }

                }
                else if (thirdModelConfig == null && string.IsNullOrEmpty(code))
                {
                    return Ok(new ResponseBase<List<UniversalDeviceConfigResult>>() { Code = 20000, Data = new List<UniversalDeviceConfigResult>() });

                }

                var jsonData = JsonConvert.DeserializeObject<ThreePartModelTemplate>(thirdModelConfig.JsonData!);

                //获取当前json文件点位配置集合
                var universalDeviceConfigs = await _client.Queryable<UniversalDeviceConfig>()
                    .WhereIF(assetId > 0, p => p.AssetId == assetId)
                    .WhereIF(!string.IsNullOrEmpty(code), p => p.ThirdPartCode == code)
                    .OrderBy(p => p.Id)
                    .Select(p => new UniversalDeviceConfigResult()
                    {
                        Id = p.Id,
                        PropertyEnName = p.PropertyEnName,
                        PropertyCnName = p.PropertyCnName,
                        IsBit = p.IsBit,
                        AssetId = p.AssetId,
                        Coefficient = p.Coefficient
                    }).ToListAsync();
                var thirdModelConfigs = await _client.Queryable<ThirdModelConfigInfo>()
                   .WhereIF(!string.IsNullOrEmpty(code), p => p.ThirdPartCode == code).ToListAsync();
                foreach (var config in universalDeviceConfigs)
                {
                    var configInfo = thirdModelConfigs.Where(a => a.PropertyCode == config.PropertyEnName).FirstOrDefault();
                    //可以从ThirdModelConfigInfo取数据，但是刚开始从UniversalDeviceConfig取的，为了兼容以前的数据，只有在点位不是二进制的时候，才去覆盖
                    if (configInfo != null && config.IsBit == false && config.IsBit != configInfo.IsBit)
                    {
                        config.IsBit = configInfo.IsBit;
                    }
                }


                if (jsonData!.Treeview!.Any())
                {
                    foreach (var item in jsonData.Treeview!)
                    {
                        //获取子集集合
                        if (item.SubGroups != null && item.SubGroups!.Any())
                        {
                            foreach (var _item in item.SubGroups!)
                            {
                                if (_item.Properties != null && _item.Properties.Any())
                                {
                                    foreach (var secondItem in _item.Properties)
                                    {
                                        if (!universalDeviceConfigs.Exists(p => p.PropertyEnName == secondItem.PropertyName))
                                        {
                                            universalDeviceConfigs.Add(new UniversalDeviceConfigResult()
                                            {
                                                Id = 0,
                                                PropertyEnName = secondItem.PropertyName,
                                                PropertyCnName = secondItem.DescriptionInGerman,
                                                AssetId = assetId,
                                                IsBit = false,
                                                Coefficient = "1.0"
                                            });
                                        }
                                    }
                                }
                            }
                        }

                        if (item.Properties != null && item.Properties.Any())
                        {
                            foreach (var _item in item.Properties)
                            {
                                if (!universalDeviceConfigs.Exists(p => p.PropertyEnName == _item.PropertyName))
                                {
                                    universalDeviceConfigs.Add(new UniversalDeviceConfigResult()
                                    {
                                        Id = 0,
                                        PropertyEnName = _item.PropertyName,
                                        PropertyCnName = _item.DescriptionInGerman,
                                        AssetId = assetId,
                                        IsBit = false,
                                        Coefficient = "1.0"
                                    });
                                }
                            }
                        }
                    }
                }

                result = new ResponseBase<List<UniversalDeviceConfigResult>>() { Code = 20000, Data = universalDeviceConfigs };

            }
            catch (Exception ex)
            {
                result = new ResponseBase<List<UniversalDeviceConfigResult>>() { Code = 50000, Message = ex.Message };
            }

            return Ok(result);
        }

        /// <summary>
        /// 保存点位配置
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("AddPointConfigs")]
        [SwaggerOperation(Summary = "Swagger_Asset_AddPointConfigs", Description = "Swagger_Asset_AddPointConfigs_Desc")]
        public async Task<IActionResult> AddPointConfigs([FromBody] List<UniversalDeviceConfigParam> input)
        {
            ResponseBase<string> result;

            try
            {
                //对象映射为实体集合
                var universalDeviceConfigs = input.Adapt<List<UniversalDeviceConfig>>();

                if (universalDeviceConfigs.Any())
                {
                    int i = 0;

                    StringBuilder errorMessage = new StringBuilder();
                    var needRestartMonitor = false;

                    foreach (var item in universalDeviceConfigs)
                    {
                        i++;

                        if (string.IsNullOrEmpty(item.Coefficient))
                        {
                            errorMessage.AppendLine($"第{i}行的系数不能为空");
                        }

                        if (double.TryParse(item.Coefficient, out var tempCoefficient))
                        {
                            if (tempCoefficient == 0d)
                            {
                                errorMessage.AppendLine($"第{i}行的系数不能为0");
                            }

                            if (tempCoefficient != 1d)
                            {
                                needRestartMonitor = true;
                            }
                        }
                        else
                        {
                            errorMessage.AppendLine($"第{i}行的系数是非小数");
                        }

                        item.CreatedBy = this.UserName;
                        item.CreatedTime = DateTime.Now;
                        item.UpdatedBy = this.UserName;
                        item.UpdatedTime = DateTime.Now;
                    }

                    if (!string.IsNullOrEmpty(errorMessage.ToString()))
                    {
                        result = new ResponseBase<string>() { Code = 50000, Message = errorMessage.ToString() };
                        return Ok(result);
                    }

                    // 分组集合
                    var groupList = universalDeviceConfigs.GroupBy(p => new { p.AssetId, p.PropertyEnName, p.PropertyCnName })
                    .Select(o => new
                    {
                        o.Key,
                        Num = o.Count()
                    }).Where(p => p.Num > 1).ToList();

                    if (groupList != null && groupList.Any())
                    {
                        foreach (var item in groupList)
                        {
                            var objList = universalDeviceConfigs.Where(p => p.AssetId == item.Key.AssetId
                            && p.PropertyEnName == item.Key.PropertyEnName
                            && p.PropertyCnName == item.Key.PropertyCnName)
                            .ToList();

                            foreach (var _item in objList)
                            {
                                if (_item.Id == 0)
                                {
                                    universalDeviceConfigs.Remove(_item);
                                }
                            }
                        }
                    }

                    await _client.Ado.BeginTranAsync();

                    // 批量添加和修改
                    var storageable = _client.Storageable(universalDeviceConfigs).ToStorage();
                    await storageable.AsInsertable.ExecuteCommandAsync();
                    await storageable.AsUpdateable.IgnoreColumns(p => new { p.CreatedBy, p.CreatedTime, p.GroupName, p.Unit }).ExecuteCommandAsync();


                    // 获取二进制位的点位信息
                    var universalDeviceConfigIds = await _client.Queryable<UniversalDeviceConfig>()
                                               .Where(p => p.AssetId == input.FirstOrDefault()!.AssetId).Select(p => new { p.Id, p.IsBit, p.PropertyEnName, p.PropertyCnName }).ToListAsync();


                    var bitConfigList = new List<BitConfig>();

                    var bitConfigByList = new List<BitConfig>();

                    // 添加数据
                    if (universalDeviceConfigIds.Any())
                    {
                        foreach (var item in universalDeviceConfigIds)
                        {
                            var bitConfigs = await _client.Queryable<BitConfig>()
                                                          .Where(p => p.UniversalDeviceConfigId == item.Id).ToListAsync();

                            if (item.IsBit)
                            {
                                if (bitConfigs.Any())
                                {
                                    continue;
                                }

                                for (int k = 0; k < 16; k++)
                                {
                                    bitConfigList.Add(new BitConfig()
                                    {
                                        Id = 0,
                                        UniversalDeviceConfigId = item.Id,
                                        BitName = $"{item.PropertyCnName}.bit{k}",
                                        BitCode = $"{item.PropertyEnName}_bit_{k}",
                                        BitNumber = k,
                                        EventType = -1,
                                        AlarmLevel = -1,
                                        CreatedTime = DateTime.Now,
                                        CreatedBy = UserName,
                                        UpdatedBy = UserName,
                                        UpdatedTime = DateTime.Now,
                                    });
                                }
                            }
                            else
                            {
                                if (bitConfigs.Any())
                                {
                                    bitConfigByList.AddRange(bitConfigs);
                                }
                            }
                        }

                        if (bitConfigList.Any())
                        {
                            await _client.Insertable(bitConfigList).ExecuteCommandAsync();
                        }

                        if (bitConfigByList.Any())
                        {
                            await _client.Deleteable(bitConfigByList).ExecuteCommandAsync();
                        }
                    }

                    await _client.Ado.CommitTranAsync();

                    var assetId = input.FirstOrDefault()!.AssetId;
                    _cache.Clear($"AssetDataPointInfoServer_GetCacheDataPoints_{assetId}");
                    _cache.Clear($"AlarmLogServer_GetCacheBitConfigs_{assetId}");
                    _cache.Clear($"DataPoint_Device_{assetId}");

                    if (needRestartMonitor)
                    {
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                var panelModbusWorker = _provider.GetRequiredService<PanelModbusWorker>();
                                await panelModbusWorker.StopAsync(CancellationToken.None);
                                await Task.Delay(5000);
                                await panelModbusWorker.StartAsync(CancellationToken.None);
                            }
                            catch
                            {
                            }
                        });
                    }

                }

                result = new ResponseBase<string>() { Code = 20000, Message = MessageContext.Success };
            }
            catch (Exception)
            {
                await _client.Ado.RollbackTranAsync();

                result = new ResponseBase<string>() { Code = 50000, Message = MessageContext.ServerException };
            }

            return Ok(result);
        }

        /// <summary>
        /// 保存点位配置
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("AddPointConfigsByJson")]
        [SwaggerOperation(Summary = "Swagger_Asset_AddPointConfigs", Description = "Swagger_Asset_AddPointConfigs_Desc")]
        public async Task<IActionResult> AddPointConfigsByJson([FromBody] List<UniversalDeviceConfigParam> input)
        {
            ResponseBase<string> result;

            try
            {
                if (input.Count > 0)
                {
                    var assetIds = await _client.Queryable<AssetInfo>().Where(a => a.ThirdPartCode == input.First().ThirdPartCode)
                        .Select(a => a.Id).ToListAsync();
                    var thirdModelInfo = await _client.Queryable<ThirdModelConfigInfo>().ToListAsync();
                    var universalConfigs = await _client.Queryable<UniversalDeviceConfig>().ToListAsync();
                    var bitConfigs = await _client.Queryable<BitConfig>().ToListAsync();
                    List<UniversalDeviceConfig> universalData = new List<UniversalDeviceConfig>();
                    var bitConfigListData = new List<BitConfig>();
                    var needRestartMonitor = false;
                    var bitConfigByListData = new List<BitConfig>();
                    await _client.Ado.BeginTranAsync();
                    if (assetIds.Count > 0)
                    {
                        foreach (int id in assetIds)
                        {
                            var uDeviceConfig = universalConfigs.Where(a => a.AssetId == id).ToList();
                            foreach (var info in input)
                            {
                                var deviceConfig = uDeviceConfig.Where(a => a.PropertyEnName == info.PropertyEnName).FirstOrDefault();
                                info.AssetId = id;
                                info.Id = deviceConfig?.Id ?? 0;

                            }
                            //对象映射为实体集合
                            var universalDeviceConfigs = input.Adapt<List<UniversalDeviceConfig>>();

                            if (universalDeviceConfigs.Any())
                            {

                                int i = 0;

                                StringBuilder errorMessage = new StringBuilder();


                                foreach (var item in universalDeviceConfigs)
                                {
                                    i++;

                                    if (string.IsNullOrEmpty(item.Coefficient))
                                    {
                                        errorMessage.AppendLine($"第{i}行的系数不能为空");
                                    }

                                    if (double.TryParse(item.Coefficient, out var tempCoefficient))
                                    {
                                        if (tempCoefficient == 0d)
                                        {
                                            errorMessage.AppendLine($"第{i}行的系数不能为0");
                                        }

                                        if (tempCoefficient != 1d)
                                        {
                                            needRestartMonitor = true;
                                        }
                                    }
                                    else
                                    {
                                        errorMessage.AppendLine($"第{i}行的系数是非小数");
                                    }

                                    item.CreatedBy = this.UserName;
                                    item.CreatedTime = DateTime.Now;
                                    item.UpdatedBy = this.UserName;
                                    item.UpdatedTime = DateTime.Now;
                                    var modelConfigInfo = thirdModelInfo.Where(a => a.ThirdPartCode == item.ThirdPartCode
                                                                                         && a.PropertyCode == item.PropertyEnName && a.IsBit != item.IsBit).FirstOrDefault();
                                    if (modelConfigInfo != null)
                                    {
                                        modelConfigInfo.IsBit = item.IsBit;
                                        modelConfigInfo.CreatedBy = this.UserName;
                                        modelConfigInfo.CreatedTime = DateTime.Now;
                                        modelConfigInfo.UpdatedBy = this.UserName;
                                        modelConfigInfo.UpdatedTime = DateTime.Now;
                                        await _client.Updateable<ThirdModelConfigInfo>(modelConfigInfo).ExecuteCommandAsync();
                                    }

                                }

                                if (!string.IsNullOrEmpty(errorMessage.ToString()))
                                {
                                    result = new ResponseBase<string>() { Code = 50000, Message = errorMessage.ToString() };
                                    return Ok(result);
                                }

                                // 分组集合
                                var groupList = universalDeviceConfigs.GroupBy(p => new { p.AssetId, p.PropertyEnName, p.PropertyCnName })
                                .Select(o => new
                                {
                                    o.Key,
                                    Num = o.Count()
                                }).Where(p => p.Num > 1).ToList();

                                if (groupList != null && groupList.Any())
                                {
                                    foreach (var item in groupList)
                                    {
                                        var objList = universalDeviceConfigs.Where(p => p.AssetId == item.Key.AssetId
                                        && p.PropertyEnName == item.Key.PropertyEnName
                                        && p.PropertyCnName == item.Key.PropertyCnName)
                                        .ToList();

                                        foreach (var _item in objList)
                                        {
                                            if (_item.Id == 0)
                                            {
                                                universalDeviceConfigs.Remove(_item);
                                            }
                                        }
                                    }
                                }



                                universalData.AddRange(universalDeviceConfigs);
                                // 批量添加和修改
                                //var storageable = _client.Storageable(universalDeviceConfigs).ToStorage();
                                //await storageable.AsInsertable.ExecuteCommandAsync();
                                //await storageable.AsUpdateable.IgnoreColumns(p => new { p.CreatedBy, p.CreatedTime, p.GroupName, p.Unit }).ExecuteCommandAsync();


                                // 获取二进制位的点位信息
                                var universalDeviceConfigIds = universalConfigs.Where(p => p.AssetId == input.FirstOrDefault()!.AssetId)
                                    .Select(p => new { p.Id, p.IsBit, p.PropertyEnName, p.PropertyCnName }).ToList();


                                var bitConfigList = new List<BitConfig>();

                                var bitConfigByList = new List<BitConfig>();

                                // 添加数据
                                if (universalDeviceConfigIds.Any())
                                {

                                    foreach (var item in universalDeviceConfigIds)
                                    {

                                        var bitInfo = bitConfigs.Where(p => p.UniversalDeviceConfigId == item.Id).ToList();
                                        if (item.IsBit)
                                        {
                                            if (bitInfo.Any())
                                            {
                                                continue;
                                            }

                                            for (int k = 0; k < 16; k++)
                                            {
                                                bitConfigList.Add(new BitConfig()
                                                {
                                                    Id = 0,
                                                    UniversalDeviceConfigId = item.Id,
                                                    BitName = $"{item.PropertyCnName}.bit{k}",
                                                    BitCode = $"{item.PropertyEnName}_bit_{k}",
                                                    BitNumber = k,
                                                    EventType = -1,
                                                    AlarmLevel = -1,
                                                    CreatedTime = DateTime.Now,
                                                    CreatedBy = UserName,
                                                    UpdatedBy = UserName,
                                                    UpdatedTime = DateTime.Now,
                                                });
                                            }
                                        }
                                        else
                                        {
                                            if (bitInfo.Any())
                                            {
                                                bitConfigByList.AddRange(bitInfo);
                                            }
                                        }
                                    }

                                    if (bitConfigList.Any())
                                    {
                                        //await _client.Insertable(bitConfigList).ExecuteCommandAsync();
                                        bitConfigListData.AddRange(bitConfigList);
                                    }

                                    if (bitConfigByList.Any())
                                    {
                                        //await _client.Deleteable(bitConfigByList).ExecuteCommandAsync();
                                        bitConfigByListData.AddRange(bitConfigByList);
                                    }
                                }

                                var assetId = input.FirstOrDefault()!.AssetId;
                                _cache.Clear($"AssetDataPointInfoServer_GetCacheDataPoints_{assetId}");
                                _cache.Clear($"AlarmLogServer_GetCacheBitConfigs_{assetId}");
                                _cache.Clear($"DataPoint_Device_{assetId}");


                            }
                        }
                        // 批量添加和修改
                        var storageable = _client.Storageable(universalData).ToStorage();
                        await storageable.AsInsertable.ExecuteCommandAsync();
                        await storageable.AsUpdateable.IgnoreColumns(p => new { p.CreatedBy, p.CreatedTime, p.GroupName, p.Unit }).ExecuteCommandAsync();
                        if (bitConfigListData.Any())
                        {
                            await _client.Insertable(bitConfigListData).ExecuteCommandAsync();
                        }

                        if (bitConfigListData.Any())
                        {
                            await _client.Deleteable(bitConfigListData).ExecuteCommandAsync();
                        }
                        if (needRestartMonitor)
                        {
                            _ = Task.Run(async () =>
                            {
                                try
                                {
                                    var panelModbusWorker = _provider.GetRequiredService<PanelModbusWorker>();
                                    await panelModbusWorker.StopAsync(CancellationToken.None);
                                    await Task.Delay(5000);
                                    await panelModbusWorker.StartAsync(CancellationToken.None);
                                }
                                catch
                                {
                                }
                            });
                        }
                        await _client.Ado.CommitTranAsync();
                        result = new ResponseBase<string>() { Code = 20000, Message = MessageContext.Success };
                    }
                    else
                    {
                        result = new ResponseBase<string>() { Code = 50000, Message = MessageContext.ErrorAssetNotInJson };
                    }

                }
                else
                {
                    result = new ResponseBase<string>() { Code = 50000, Message = MessageContext.ErrorParam };
                }

            }
            catch (Exception)
            {
                await _client.Ado.RollbackTranAsync();

                result = new ResponseBase<string>() { Code = 50000, Message = MessageContext.ServerException };
            }

            return Ok(result);
        }
        [HttpGet("GetAnalyzingDataPoints")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetAnalyzingDataPoints", Description = "Swagger_Asset_GetAnalyzingDataPoints_Desc")]
        public async Task<IActionResult> GetAnalyzingDataPoints(int assetId)
        {
            ResponseBase<dynamic> result;

            try
            {
                var universalDeviceList = await _client.Queryable<UniversalDeviceConfig>()
                    .LeftJoin<AssetInfo>((t1, t2) => t1.AssetId == t2.Id)
                    .LeftJoin<ThirdModelConfig>((t1, t2, t3) => t2.ThirdPartCode == t3.Code)
                    .Where((t1, t2, t3) => t2.Id == assetId && t1.IsBit == true)
                    .Select((t1, t2, t3) => new
                    {
                        t1.Id,
                        t1.PropertyEnName,
                        PropertyCnName = string.IsNullOrEmpty(t1.PropertyCnName) ? t1.PropertyEnName : t1.PropertyCnName
                    })
                    .ToListAsync();

                result = new ResponseBase<dynamic>() { Code = 20000, Data = universalDeviceList };
            }
            catch (Exception ex)
            {
                result = new ResponseBase<dynamic>() { Code = 50000, Message = ex.Message };
            }

            return Ok(result);
        }
        /// <summary>
        /// 获取二进制点位属性
        /// </summary>
        /// <param name="jsonCode"></param>
        /// <returns></returns>
        [HttpGet("GetAnalyzingDataPointsByCode")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetAnalyzingDataPointsByCode", Description = "Swagger_Asset_GetAnalyzingDataPointsByCode")]
        public async Task<IActionResult> GetAnalyzingDataPointsByCode(string thirdPartCode)
        {
            ResponseBase<dynamic> result;

            try
            {
                var asset = await _client.Queryable<AssetInfo>().Where(a => a.ThirdPartCode == thirdPartCode).FirstAsync();
                int assetId = 0;
                if (asset != null)
                {
                    assetId = asset.Id;
                }
                var universalDeviceList = await _client.Queryable<UniversalDeviceConfig>()
                  .LeftJoin<AssetInfo>((t1, t2) => t1.AssetId == t2.Id)
                  .LeftJoin<ThirdModelConfig>((t1, t2, t3) => t2.ThirdPartCode == t3.Code)
                  .Where((t1, t2, t3) => t2.Id == assetId && t1.IsBit == true)
                  .Select((t1, t2, t3) => new
                  {
                      t1.Id,
                      t1.PropertyEnName,
                      PropertyCnName = string.IsNullOrEmpty(t1.PropertyCnName) ? t1.PropertyEnName : t1.PropertyCnName
                  })
                  .ToListAsync();

                result = new ResponseBase<dynamic>() { Code = 20000, Data = universalDeviceList };
            }
            catch (Exception ex)
            {
                result = new ResponseBase<dynamic>() { Code = 50000, Message = ex.Message };
            }

            return Ok(result);
        }

        /// <summary>
        /// 根据二进制属性id获取二级制点位说明
        /// </summary>
        /// <param name="id">UniversalDeviceConfig表的主键id</param>
        /// <returns></returns>
        [HttpGet("GetBitPoints")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetBitPoints", Description = "Swagger_Asset_GetBitPoints_Desc")]
        public async Task<IActionResult> GetBitPoints(int id)
        {
            ResponseBase<List<BitConfigResult>> result;

            try
            {
                var bitConfigs = await _client.Queryable<BitConfig>()
                    .Where(p => p.UniversalDeviceConfigId == id)
                    .Select(p => new BitConfigResult
                    {
                        Id = p.Id,
                        UniversalDeviceConfigId = p.UniversalDeviceConfigId,
                        BitName = p.BitName,
                        BitNumber = p.BitNumber,
                        EventType = p.EventType,
                        AlarmLevel = p.AlarmLevel
                    })
                    .ToListAsync();

                if (!bitConfigs.Any())
                {
                    for (int i = 0; i < 16; i++)
                    {
                        bitConfigs.Add(new BitConfigResult()
                        {
                            Id = 0,
                            UniversalDeviceConfigId = id,
                            BitName = $"bit{i}",
                            BitNumber = i,
                            EventType = -1,
                            AlarmLevel = -1
                        });
                    }
                }

                result = new ResponseBase<List<BitConfigResult>>() { Code = 20000, Data = bitConfigs };

            }
            catch (Exception)
            {
                result = new ResponseBase<List<BitConfigResult>>() { Code = 50000, Message = MessageContext.ServerException };
            }

            return Ok(result);
        }

        /// <summary>
        /// 保存二进制点位说明信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SaveBitPoints")]
        [SwaggerOperation(Summary = "Swagger_Asset_SaveBitPoints", Description = "Swagger_Asset_SaveBitPoints_Desc")]
        public async Task<IActionResult> SaveBitPoints([FromBody] List<BitConfigResult> input)
        {
            ResponseBase<string> result;

            try
            {
                if (input.Select(p => p.BitName).Distinct().ToList().Count() < input.Count)
                {
                    //throw new Exception(MessageContext.GetString("Error_Asset_BitPointIsExists"));

                    result = new ResponseBase<string>() { Code = 50000, Message = MessageContext.GetString("Error_Asset_BitPointIsExists") };

                    return Ok(result);
                }



                var universalDeviceConfigId = input.Select(i => i.UniversalDeviceConfigId).First();

                var universalConfig = await _client.Queryable<UniversalDeviceConfig>().FirstAsync(u => u.Id == universalDeviceConfigId);
                if (universalConfig == null)
                {
                    return Ok(new ResponseBase<string>()
                    {
                        Code = 40300,
                        Message = MessageContext.ErrorParam
                    });
                }

                var bitConfigs = await _client.Queryable<BitConfig>().Where(b => b.UniversalDeviceConfigId == universalDeviceConfigId).ToListAsync();

                #region Save
                var updateList = new List<BitConfig>();
                var insertList = new List<BitConfig>();
                foreach (var item in input)
                {
                    var exitsConfig = bitConfigs.FirstOrDefault(b => b.Id == item.Id);
                    if (exitsConfig != null)
                    {
                        bitConfigs.Remove(exitsConfig);
                        updateList.Add(exitsConfig);
                        exitsConfig.UpdatedBy = UserName;
                        exitsConfig.UpdatedTime = DateTime.Now;
                        exitsConfig.EventType = item.EventType;
                        exitsConfig.BitName = item.BitName;
                        exitsConfig.BitNumber = item.BitNumber;
                        exitsConfig.AlarmLevel = item.AlarmLevel;
                    }
                    else
                    {
                        var newBitConfig = new BitConfig()
                        {
                            UniversalDeviceConfigId = universalDeviceConfigId,
                            CreatedBy = UserName,
                            CreatedTime = DateTime.Now,
                            UpdatedBy = UserName,
                            UpdatedTime = DateTime.Now,
                            BitCode = $"{universalConfig.PropertyEnName}_bit_{item.BitNumber}",
                            AlarmLevel = item.AlarmLevel,
                            BitName = item.BitName,
                            BitNumber = item.BitNumber,
                            EventType = item.EventType,
                        };
                        insertList.Add(newBitConfig);
                    }
                }

                await _client.Ado.BeginTranAsync();
                if (updateList.Count > 0)
                {
                    await _client.Updateable(updateList).ExecuteCommandAsync();
                }

                if (insertList.Count > 0)
                {
                    await _client.Insertable(insertList).ExecuteCommandAsync();
                }

                if (bitConfigs.Count > 0)
                {
                    var bitIds = bitConfigs.Select(b => b.Id).ToList();
                    await _client.Deleteable<BitConfig>().Where(b => bitIds.Contains(b.Id)).ExecuteCommandAsync();
                }
                await _client.Ado.CommitTranAsync();
                #endregion

                #region 修复点位缓存数据
                var alarmLogProvider = _provider.GetService<AlarmLogServer>();

                if (alarmLogProvider != null)
                {
                    var assetIds = await _client.Queryable<UniversalDeviceConfig>()
                                                 .Where(p => p.Id == input.FirstOrDefault()!.UniversalDeviceConfigId)
                                                  .Distinct().Select(p => p.AssetId).ToListAsync();

                    alarmLogProvider.ClearCache(assetIds);

                    if (assetIds != null && assetIds.Any())
                    {
                        foreach (var assetId in assetIds)
                        {
                            // 设置缓存数据
                            alarmLogProvider.SetCacheBitConfigs();

                            //清空数据缓存
                            _cache.Clear(string.Format(AssetCurrentStatusCacheKey, assetId));
                        }
                    }
                }

                _cache.Clear($"DataPoint_Device_{universalConfig.AssetId}");
                #endregion

                result = new ResponseBase<string>() { Code = 20000, Message = MessageContext.Success };

            }
            catch (Exception)
            {
                _client.Ado.RollbackTran();

                result = new ResponseBase<string>() { Code = 50000, Message = MessageContext.ServerException };
            }

            return Ok(result);
        }
        /// <summary>
        /// 保存二进制点位说明信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SaveBitPointsByJson")]
        [SwaggerOperation(Summary = "Swagger_Asset_SaveBitPointsByJson", Description = "Swagger_Asset_SaveBitPointsByJson_Desc")]
        public async Task<IActionResult> SaveBitPointsByJson([FromBody] List<BitConfigResult> input)
        {
            ResponseBase<string> result;

            try
            {
                if (input.Select(p => p.BitName).Distinct().ToList().Count() < input.Count)
                {
                    //throw new Exception(MessageContext.GetString("Error_Asset_BitPointIsExists"));

                    result = new ResponseBase<string>() { Code = 50000, Message = MessageContext.GetString("Error_Asset_BitPointIsExists") };

                    return Ok(result);
                }
                if (input.Count > 0)
                {
                    var assetInfo = await _client.Queryable<AssetInfo>().Where(a => a.ThirdPartCode == input.First().ThirdPartCode).Select(a => a.Id).ToListAsync();

                    var universalInfo = await _client.Queryable<UniversalDeviceConfig>().Where(a => a.IsBit && assetInfo.Contains(a.AssetId)).ToListAsync();

                    foreach (var universal in universalInfo)
                    {
                        var universalDeviceConfigId = universal.Id;

                        var universalConfig = await _client.Queryable<UniversalDeviceConfig>().FirstAsync(u => u.Id == universalDeviceConfigId && u.PropertyEnName == input.First().SourceCode);
                        if (universalConfig == null)
                        {
                            continue;
                        }

                        var bitConfigs = await _client.Queryable<BitConfig>().Where(b => b.UniversalDeviceConfigId == universalDeviceConfigId).ToListAsync();

                        #region Save
                        var updateList = new List<BitConfig>();
                        var insertList = new List<BitConfig>();
                        foreach (var item in input)
                        {
                            var exitsConfig = bitConfigs.FirstOrDefault(b => b.Id == item.Id);
                            if (exitsConfig != null)
                            {
                                bitConfigs.Remove(exitsConfig);
                                updateList.Add(exitsConfig);
                                exitsConfig.UpdatedBy = UserName;
                                exitsConfig.UpdatedTime = DateTime.Now;
                                exitsConfig.EventType = item.EventType;
                                exitsConfig.BitName = item.BitName;
                                exitsConfig.BitNumber = item.BitNumber;
                                exitsConfig.AlarmLevel = item.AlarmLevel;
                            }
                            else
                            {
                                var newBitConfig = new BitConfig()
                                {
                                    UniversalDeviceConfigId = universalDeviceConfigId,
                                    CreatedBy = UserName,
                                    CreatedTime = DateTime.Now,
                                    UpdatedBy = UserName,
                                    UpdatedTime = DateTime.Now,
                                    BitCode = $"{universalConfig.PropertyEnName}_bit_{item.BitNumber}",
                                    AlarmLevel = item.AlarmLevel,
                                    BitName = item.BitName,
                                    BitNumber = item.BitNumber,
                                    EventType = item.EventType,
                                };
                                insertList.Add(newBitConfig);
                            }
                        }

                        await _client.Ado.BeginTranAsync();
                        if (updateList.Count > 0)
                        {
                            await _client.Updateable(updateList).ExecuteCommandAsync();
                        }

                        if (insertList.Count > 0)
                        {
                            await _client.Insertable(insertList).ExecuteCommandAsync();
                        }

                        if (bitConfigs.Count > 0)
                        {
                            var bitIds = bitConfigs.Select(b => b.Id).ToList();
                            await _client.Deleteable<BitConfig>().Where(b => bitIds.Contains(b.Id)).ExecuteCommandAsync();
                        }
                        await _client.Ado.CommitTranAsync();
                        #endregion
                    }
                }




                #region 修复点位缓存数据
                //var alarmLogProvider = _provider.GetService<AlarmLogServer>();

                //if (alarmLogProvider != null)
                //{
                //    var assetIds = await _client.Queryable<UniversalDeviceConfig>()
                //                                 .Where(p => p.Id == input.FirstOrDefault()!.UniversalDeviceConfigId)
                //                                  .Distinct().Select(p => p.AssetId).ToListAsync();

                //    alarmLogProvider.ClearCache(assetIds);

                //    if (assetIds != null && assetIds.Any())
                //    {
                //        foreach (var assetId in assetIds)
                //        {
                //            // 设置缓存数据
                //            alarmLogProvider.SetCacheBitConfigs();

                //            //清空数据缓存
                //            _cache.Clear(string.Format(AssetCurrentStatusCacheKey, assetId));
                //        }
                //    }
                //}
                #endregion

                result = new ResponseBase<string>() { Code = 20000, Message = MessageContext.Success };

            }
            catch (Exception)
            {
                _client.Ado.RollbackTran();

                result = new ResponseBase<string>() { Code = 50000, Message = MessageContext.ServerException };
            }

            return Ok(result);
        }

        /// <summary>
        ///  根据点位属性获取图表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetChartDatas")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetChartDatas", Description = "Swagger_Asset_GetChartDatas_Desc")]
        public async Task<IActionResult> GetChartDatas([FromBody] ChartDataParam input)
        {
            ResponseBase<List<ChartDataResult>> result;

            try
            {
                switch (input.TimeInterval)
                {
                    case "900":
                        input.TimeInterval = "15m";
                        break;
                    default:
                        input.TimeInterval = "10s";
                        break;
                }

                var helper = _provider.GetRequiredService<AssetInfluxHelper>();

                var chartDatas = new List<ChartDataResult>();

                //获取点位属性
                var universalDeviceList = await _client.Queryable<UniversalDeviceConfig>()
                    .LeftJoin<AssetInfo>((t1, t2) => t1.AssetId == t2.Id)
                    .LeftJoin<ThirdModelConfig>((t1, t2, t3) => t2.ThirdPartCode == t3.Code)
                    .Where((t1, t2, t3) => t2.Id == input.AssetId)
                    .Select((t1, t2, t3) => new
                    {
                        t1.Id,
                        t1.IsBit,
                        t1.PropertyEnName,
                        t1.PropertyCnName
                    })
                    .ToListAsync();

                //获取二进点位集合
                List<int> universalDeviceConfigIds = universalDeviceList.Where(p => p.IsBit).Select(p => p.Id).ToList();

                //二级制点位配置集合
                var bitConfigList = await _client.Queryable<BitConfig>()
                    .Where(p => universalDeviceConfigIds.Contains(p.UniversalDeviceConfigId))
                    .ToListAsync();

                if (input.ChartDatas != null && input.ChartDatas.Any())
                {
                    foreach (var item in input.ChartDatas)
                    {
                        var model = universalDeviceList.FirstOrDefault(p => p.PropertyEnName == item.PropertyEnName);

                        //是否二进制
                        bool isBit = model?.IsBit ?? false;

                        var entity = new ChartDataResult()
                        {
                            PropertyEnName = item.PropertyEnName,
                            PropertyCnName = item.PropertyCnName,
                            Unit = item.Unit,
                            IsBit = isBit
                        };

                        var dataList = await helper.GetDataAsync(input.AssetId, item.PropertyEnName, input.TimeInterval, isBit);

                        if (isBit)
                        {
                            //获取二进制点位
                            var bitConfig = bitConfigList.FirstOrDefault(p => p.BitName == item.PropertyCnName);

                            var yvalList = dataList.Select(p => p.Yval).ToList();

                            var strDatas = new List<string?>();

                            if (yvalList.Any())
                            {
                                foreach (var _item in yvalList)
                                {
                                    List<string?> bits = StringFunction.BitToList(_item);
                                    string? yVal = bitConfig != null ? bits[bitConfig.BitNumber] : "0";
                                    strDatas.Add(yVal);
                                }
                            }

                            entity.ChartDatas = new ChartDto()
                            {
                                Xval = dataList.Select(p => p.Xval).ToList(),
                                Yval = strDatas
                            };
                        }
                        else
                        {
                            entity.ChartDatas = new ChartDto()
                            {
                                Xval = dataList.Select(p => p.Xval).ToList(),
                                Yval = dataList.Select(p => p.Yval).ToList(),
                            };
                        }

                        chartDatas.Add(entity);
                    }
                }

                result = new ResponseBase<List<ChartDataResult>>() { Code = 20000, Data = chartDatas };
            }
            catch (Exception)
            {
                result = new ResponseBase<List<ChartDataResult>>() { Code = 50000, Message = MessageContext.ServerException };
            }

            return Ok(result);
        }

        /// <summary>
        ///  获取单个点位属性图表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpGet("GetTimedChartDatas")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetTimedChartDatas", Description = "Swagger_Asset_GetTimedChartDatas_Desc")]
        public async Task<IActionResult> GetTimedChartDatas([FromQuery] TimeChartParam input)
        {
            ResponseBase<ChartDataResult> result;

            try
            {
                switch (input.TimeInterval)
                {
                    case "900":
                        input.TimeInterval = "15m";
                        break;
                    default:
                        input.TimeInterval = "10s";
                        break;
                }

                var helper = _provider.GetRequiredService<AssetInfluxHelper>();

                //获取点位属性
                var universalDeviceList = await _client.Queryable<UniversalDeviceConfig>()
                    .LeftJoin<AssetInfo>((t1, t2) => t1.AssetId == t2.Id)
                    .LeftJoin<ThirdModelConfig>((t1, t2, t3) => t2.ThirdPartCode == t3.Code)
                    .Where((t1, t2, t3) => t2.Id == input.AssetId)
                    .Select((t1, t2, t3) => new
                    {
                        t1.Id,
                        t1.IsBit,
                        t1.PropertyEnName,
                        t1.PropertyCnName
                    })
                    .ToListAsync();

                //获取二进点位集合
                List<int> universalDeviceConfigIds = universalDeviceList.Where(p => p.IsBit).Select(p => p.Id).ToList();

                //二级制点位配置集合
                var bitConfigList = await _client.Queryable<BitConfig>()
                    .Where(p => universalDeviceConfigIds.Contains(p.UniversalDeviceConfigId))
                    .ToListAsync();

                var model = universalDeviceList.FirstOrDefault(p => p.PropertyEnName == input.PropertyEnName);

                //是否二进制
                bool isBit = model?.IsBit ?? false;

                var entity = new ChartDataResult()
                {
                    PropertyEnName = input.PropertyEnName,
                    PropertyCnName = input.PropertyCnName,
                    Unit = input.Unit,
                    IsBit = isBit
                };

                var dataList = await helper.GetDataAsync(input.AssetId, input.PropertyEnName, input.TimeInterval, isBit);

                if (isBit)
                {
                    //获取二进制点位
                    var bitConfig = bitConfigList.FirstOrDefault(p => p.BitName == input.PropertyCnName);

                    var yvalList = dataList.Select(p => p.Yval).ToList();

                    var strDatas = new List<string?>();

                    if (yvalList.Any())
                    {
                        foreach (var item in yvalList)
                        {
                            List<string?> bits = StringFunction.BitToList(item);
                            string? yVal = bitConfig != null ? bits[bitConfig.BitNumber] : "0";
                            strDatas.Add(yVal);
                        }
                    }

                    entity.ChartDatas = new ChartDto()
                    {
                        Xval = dataList.Select(p => p.Xval).ToList(),
                        Yval = strDatas
                    };
                }
                else
                {
                    entity.ChartDatas = new ChartDto()
                    {
                        Xval = dataList.Select(p => p.Xval).ToList(),
                        Yval = dataList.Select(p => p.Yval).ToList(),
                    };
                }

                result = new ResponseBase<ChartDataResult>() { Code = 20000, Data = entity };
            }
            catch (Exception)
            {
                result = new ResponseBase<ChartDataResult>() { Code = 50000, Message = MessageContext.ServerException };
            }

            return Ok(result);
        }

        /// <summary>
        /// 获取第三方通用设备信息
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetGeneralDevices")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetGeneralDevices", Description = "Swagger_Asset_GetGeneralDevices_Desc")]
        public async Task<IActionResult> GetGeneralDevices()
        {
            ResponseBase<dynamic> result;

            try
            {
                var generalDevices = await _client.Queryable<AssetInfo>()
                    .Where(p => (p.AssetType == "GeneralDevice" && p.AssetType == "GeneralDevice") || p.AssetModel == "Other")
                    //.Where(p => p.AssetModel == "Other" || p.AssetType == "GeneralDevice" || (p.AssetModel == "Modbus" && p.AssetType == "Gateway"))
                    .Select(p => new
                    {
                        p.Id,
                        p.ThirdPartCode,
                        p.AssetName
                    })
                    .ToListAsync();

                result = new ResponseBase<dynamic>() { Code = 20000, Data = generalDevices };

            }
            catch (Exception)
            {
                result = new ResponseBase<dynamic>() { Code = 50000, Message = MessageContext.ServerException };
            }

            return Ok(result);
        }

        /// <summary>
        /// 批量给通用设备指定json文件
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetGeneralDevices")]
        [SwaggerOperation(Summary = "Swagger_Asset_BatchSpecifyByAsset", Description = "Swagger_Asset_BatchSpecifyByAsset_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> BatchSpecifyByAsset([FromBody] List<BatchAssetParam> input)
        {
            ResponseBase<string> result;

            try
            {
                List<int> assetIds = input.Select(p => p.AssetId).ToList();

                var thirdPartCodes = input.Select(i => i.ThirdPartCode).Distinct().ToList();

                if (assetIds.Any())
                {
                    foreach (var code in thirdPartCodes)
                    {
                        var configAssetIds = input.Where(i => i.ThirdPartCode == code).Select(i => i.AssetId).ToList();
                        var assetInfoes = await _client.Queryable<AssetInfo>().Where(a => a.ThirdPartCode == code || configAssetIds.Contains(a.Id)).ToListAsync();

                        var exitsAssets = assetInfoes.ToList();
                        exitsAssets.ForEach(a =>
                        {
                            a.ThirdPartCode = code;
                            a.UpdatedBy = UserName;
                            a.UpdatedTime = DateTime.Now;
                        });

                        await UpdateUniversalConfigByAsset(code ?? string.Empty, exitsAssets);

                        var removeAssets = assetInfoes.Where(a => !configAssetIds.Contains(a.Id)).ToList();
                        removeAssets.ForEach(a =>
                        {
                            a.ThirdPartCode = string.Empty;
                            a.UpdatedBy = UserName;
                            a.UpdatedTime = DateTime.Now;
                        });

                        await RemoveUniversalConfigByAsset(removeAssets);
                        await _client.Updateable(assetInfoes).ExecuteCommandAsync();
                    }
                }

                result = new ResponseBase<string>() { Code = 20000, Message = MessageContext.Success };

            }
            catch (Exception)
            {
                result = new ResponseBase<string>() { Code = 50000, Message = MessageContext.ServerException };
            }

            return Ok(result);
        }

        private async Task UpdateUniversalConfigByAsset(string thirdPartCode, List<AssetInfo> assetInfos)
        {
            var assetIds = assetInfos.Select(a => a.Id).ToList();
            var allConfigs = await _client.Queryable<UniversalDeviceConfig>().Where(u => assetIds.Contains(u.AssetId)).ToListAsync();
            var thirdModelConfig = await _client.Queryable<ThirdModelConfig>().Where(t => t.Code == thirdPartCode).FirstAsync();
            if (thirdModelConfig == null)
            {
                return;
            }

            var jsonData = JsonConvert.DeserializeObject<ThreePartModelTemplate>(thirdModelConfig.JsonData!);

            if (jsonData == null || jsonData.Treeview == null)
            {
                return;
            }

            var propertiesGroup = new Dictionary<string, PropertyInfo[]>();
            var subProertyInfo = new Dictionary<string, List<PropertyInfo>>();
            foreach (var view in jsonData.Treeview)
            {
                var groupName = view.Name;
                if (view.SubGroups != null)
                {
                    foreach (var subGroup in view.SubGroups)
                    {
                        if (!string.IsNullOrEmpty(subGroup.Name))
                        {
                            groupName = subGroup.Name;
                        }

                        if (subGroup.Properties != null)
                        {
                            AddOrSet(propertiesGroup, subGroup.Name ?? Guid.NewGuid().ToString(), subGroup.Properties.ToArray(), subProertyInfo);
                        }
                    }
                }

                if (view.Properties != null)
                {
                    AddOrSet(propertiesGroup, groupName ?? Guid.NewGuid().ToString(), view.Properties.ToArray(), subProertyInfo);
                }
            }

            var allList = new List<UniversalDeviceConfig>();

            var insertList = new List<UniversalDeviceConfig>();
            var updateList = new List<UniversalDeviceConfig>();
            var deleteList = new List<UniversalDeviceConfig>();

            foreach (var asset in assetInfos)
            {
                var configs = allConfigs.Where(c => c.AssetId == asset.Id).ToList();
                foreach (var pg in propertiesGroup)
                {
                    foreach (var p in pg.Value)
                    {
                        if (string.IsNullOrEmpty(p.Register))
                        {
                            continue;
                        }

                        if (!(p.IsDigitalInputChecked.HasValue && p.IsDigitalInputChecked.Value))
                        {
                            var config = configs.FirstOrDefault(c => c.PropertyEnName == p.PropertyName);
                            if (config != null)
                            {
                                configs.Remove(config);
                                if (config.PropertyCnName != p.DescriptionInGerman
                                    || config.GroupName != pg.Key
                                    || config.Unit != p.Unit)
                                {
                                    config.PropertyCnName = p.DescriptionInGerman;
                                    config.GroupName = pg.Key;
                                    config.Unit = p.Unit;
                                    config.ThirdPartCode = thirdPartCode;
                                    config.UpdatedBy = UserName;
                                    config.UpdatedTime = DateTime.Now;

                                    updateList.Add(config);
                                }

                                config.Register = p.Register;
                                allList.Add(config);
                            }
                            else
                            {
                                config = new UniversalDeviceConfig()
                                {
                                    Id = 0,
                                    PropertyEnName = p.PropertyName,
                                    PropertyCnName = p.DescriptionInGerman,
                                    AssetId = asset.Id,
                                    IsBit = false,
                                    Coefficient = "1",
                                    GroupName = pg.Key,
                                    Unit = p.Unit,
                                    CreatedBy = UserName,
                                    ThirdPartCode = thirdPartCode,
                                    CreatedTime = DateTime.Now,
                                    UpdatedBy = UserName,
                                    UpdatedTime = DateTime.Now,
                                    Register = p.Register
                                };

                                allConfigs.Add(config);
                                insertList.Add(config);
                            }
                        }
                    }
                }

                if (configs.Count > 0)
                {
                    deleteList.AddRange(configs);
                }
            }

            if (insertList.Count > 0)
            {
                var newIds = await _client.Insertable(insertList).ExecuteReturnPkListAsync<int>();
                for (var i = 0; i < insertList.Count; i++)
                {
                    insertList[i].Id = newIds[i];
                }
            }

            if (updateList.Count > 0)
            {
                await _client.Updateable(updateList).ExecuteCommandAsync();
            }

            if (subProertyInfo.Count > 0)
            {
                var registers = subProertyInfo.Keys.ToList();
                var saveBitConfigs = new List<BitConfig>();
                foreach (var asset in assetInfos)
                {
                    foreach (var register in registers)
                    {
                        var parentConfig = allConfigs.FirstOrDefault(c => c.Register == register && c.AssetId == asset.Id);

                        if (parentConfig != null)
                        {
                            var bitConfigs = _client.Queryable<BitConfig>()
                                .Where(b => b.UniversalDeviceConfigId == parentConfig.Id)
                                .ToList();

                            var subProertys = subProertyInfo[register];

                            foreach (var sub in subProertys)
                            {
                                if (!string.IsNullOrEmpty(sub.SubIndex) && int.TryParse(sub.SubIndex, out var subIndex) && subIndex >= 0)
                                {
                                    var config = bitConfigs.FirstOrDefault(b => b.BitCode == sub.PropertyName);
                                    if (config != null)
                                    {
                                        config.BitName = sub.DescriptionInEnglish;
                                        config.BitNumber = subIndex;
                                        config.UpdatedBy = UserName;
                                        config.UpdatedTime = DateTime.Now;
                                        saveBitConfigs.Add(config);
                                    }
                                    else
                                    {
                                        var newBitConfig = new BitConfig()
                                        {
                                            UniversalDeviceConfigId = parentConfig.Id,
                                            CreatedBy = UserName,
                                            CreatedTime = DateTime.Now,
                                            UpdatedBy = UserName,
                                            UpdatedTime = DateTime.Now,
                                            BitCode = sub.PropertyName,
                                            BitName = sub.DescriptionInEnglish,
                                            BitNumber = subIndex,
                                            EventType = -1,
                                            AlarmLevel = -1,
                                        };
                                        saveBitConfigs.Add(newBitConfig);
                                    }
                                }
                            }

                            if (parentConfig.IsBit == false)
                            {
                                parentConfig.IsBit = true;
                                await _client.Updateable(parentConfig).ExecuteCommandAsync();
                            }
                        }
                    }
                }

                if (saveBitConfigs.Count > 0)
                {
                    await _client.Storageable(saveBitConfigs).ExecuteCommandAsync();
                }
            }

            if (deleteList.Count > 0)
            {
                var bitConfigIds = deleteList.Where(c => c.IsBit).Select(c => c.Id).ToList();
                await _client.Deleteable<BitConfig>().Where(b => bitConfigIds.Contains(b.UniversalDeviceConfigId)).ExecuteCommandAsync();
                await _client.Deleteable(deleteList).ExecuteCommandAsync();
            }

            if (assetInfos.Count > 0)
            {
                var server = _provider.GetRequiredService<AlarmLogServer>();
                server.ClearCache(assetInfos.Select(a => a.Id).ToList());
            }
        }

        private void AddOrSet(Dictionary<string, PropertyInfo[]> dic,
            string groupName,
            PropertyInfo[] properties,
            Dictionary<string, List<PropertyInfo>> subProertyInfo)
        {
            foreach (var p in properties)
            {
                if (p.SubIndex != null
                    && p.IsDigitalInputCheckBoxVisible == 0
                    && int.TryParse(p.SubIndex, out _)
                    && !string.IsNullOrEmpty(p.Register))
                {
                    if (subProertyInfo.TryGetValue(p.Register, out var subProperties))
                    {
                        subProperties.Add(p);
                    }
                    else
                    {
                        subProertyInfo.Add(p.Register, new List<PropertyInfo>() { p });
                    }
                }
            }

            if (dic.TryGetValue(groupName, out var propertyInfos))
            {
                var newList = new List<PropertyInfo>();
                newList.AddRange(propertyInfos);
                newList.AddRange(properties);
                dic[groupName] = newList.ToArray();
            }
            else
            {
                dic.Add(groupName, properties);
            }
        }

        private async Task RemoveUniversalConfigByAsset(List<AssetInfo> assetInfos)
        {
            var assetIds = assetInfos.Select(a => a.Id).ToList();
            var allConfigs = await _client.Queryable<UniversalDeviceConfig>().Where(u => assetIds.Contains(u.AssetId)).ToListAsync();

            if (allConfigs.Count > 0)
            {
                var bitConfigIds = allConfigs.Where(c => c.IsBit).Select(c => c.Id).ToList();
                await _client.Deleteable<BitConfig>().Where(b => bitConfigIds.Contains(b.UniversalDeviceConfigId)).ExecuteCommandAsync();
                await _client.Deleteable(allConfigs).ExecuteCommandAsync();
            }
        }
        /// <summary>
        /// 获取手动挂载数据点
        /// </summary>
        /// <param name="assetId">资产Id</param>
        /// <returns></returns>
        [HttpGet("GetCustomDataPointsNew")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetCustomDataPoints", Description = "Swagger_Asset_GetCustomDataPoints_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<List<CustomDataPointViewModel>>> GetCustomDataPointsNew(int assetId)
        {
            var customDataPoints = await _client.Queryable<CustomDataPoint, AssetInfo, UniversalDeviceConfig>((c, a, u) => new object[] {
                                                JoinType.Inner, c.RealAssetId == a.Id,
                                                JoinType.Inner, c.RealAssetId==u.AssetId&&c.RealDataPointName==u.PropertyEnName
                                            }).Where((c, a, u) => c.TargetAssetId == assetId)
                                            .Select((c, a, u) => new CustomDataPointViewModel()
                                            {
                                                Id = c.Id,
                                                RealAssetId = c.RealAssetId,
                                                RealDataPoint = c.RealDataPointName,
                                                TargetAssetId = c.TargetAssetId,
                                                TargetDataPoint = c.TargetDataPointName,
                                                RealAssetModel = a.AssetModel ?? string.Empty,
                                                RealAssetType = a.AssetType ?? string.Empty,
                                                RealAssetName = a.AssetName ?? string.Empty,
                                                PropertyEnName = u.PropertyEnName,
                                                PropertyCnName = u.PropertyCnName,
                                                IsBit = u.IsBit,
                                                Unit = u.Unit
                                            })
                                            .ToListAsync();


            return new ResponseBase<List<CustomDataPointViewModel>>()
            {
                Code = 20000,
                Data = customDataPoints
            };
        }
        /// <summary>
        /// 获取手动挂载数据点
        /// </summary>
        /// <param name="assetId">资产Id</param>
        /// <returns></returns>
        [HttpGet("GetCustomDataPoints")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetCustomDataPoints", Description = "Swagger_Asset_GetCustomDataPoints_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<List<CustomDataPointViewModel>>> GetCustomDataPoints(int assetId)
        {
            var customDataPoints = await _client.Queryable<CustomDataPoint>()
                .InnerJoin<AssetInfo>((c, a) => c.RealAssetId == a.Id)
                .Where((c, a) => c.TargetAssetId == assetId)
                .Select((c, a) => new CustomDataPointViewModel()
                {
                    Id = c.Id,
                    RealAssetId = c.RealAssetId,
                    RealDataPoint = c.RealDataPointName,
                    TargetAssetId = c.TargetAssetId,
                    TargetDataPoint = c.TargetDataPointName,
                    RealAssetModel = a.AssetModel ?? string.Empty,
                    RealAssetType = a.AssetType ?? string.Empty,
                    RealAssetName = a.AssetName ?? string.Empty,
                }).ToListAsync();

            return new ResponseBase<List<CustomDataPointViewModel>>()
            {
                Code = 20000,
                Data = customDataPoints
            };
        }

        /// <summary>
        /// 获取手动挂载数据点
        /// </summary>
        /// <param name="assetId">资产Id</param>
        /// <param name="dataPointName">数据点位名</param>
        /// <returns></returns>
        [HttpGet("GetCustomDataPoint")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetCustomDataPoint", Description = "Swagger_Asset_GetCustomDataPoint_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<CustomDataPointViewModel>> GetCustomDataPoint(int assetId, string dataPointName)
        {
            var customDataPoint = await _client.Queryable<CustomDataPoint>()
                .InnerJoin<AssetInfo>((c, a) => c.RealAssetId == a.Id)
                .Where((c, a) => c.TargetAssetId == assetId && c.TargetDataPointName == dataPointName)
                .Select((c, a) => new CustomDataPointViewModel()
                {
                    Id = c.Id,
                    RealAssetId = c.RealAssetId,
                    RealDataPoint = c.RealDataPointName,
                    TargetAssetId = c.TargetAssetId,
                    TargetDataPoint = c.TargetDataPointName,
                    RealAssetModel = a.AssetModel ?? string.Empty,
                    RealAssetType = a.AssetType ?? string.Empty,
                    RealAssetName = a.AssetName ?? string.Empty,
                }).FirstAsync();
            return new ResponseBase<CustomDataPointViewModel>()
            {
                Code = 20000,
                Data = customDataPoint
            };
        }

        /// <summary>
        /// 更新手动挂载数据点
        /// </summary>
        /// <param name="customDataViewModel"></param>
        /// <returns></returns>
        [HttpPost("SaveCustomDataPoint")]
        [SwaggerOperation(Summary = "Swagger_Asset_SaveCustomDataPoint", Description = "Swagger_Asset_SaveCustomDataPoint_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<int>> SaveCustomDataPoint(CustomDataPointViewModel customDataViewModel)
        {
            var id = customDataViewModel.Id;

            bool hasRemove = false;
            var targetAssetId = customDataViewModel.TargetAssetId;
            var proxyRef = _provider.GetRequiredService<IAssetDataProxyRef>();

            #region 删除
            if (customDataViewModel.RealAssetId == 0)
            {
                CustomDataPoint[] existsDataPoints;
                if (id > 0)
                {
                    existsDataPoints = await _client.Queryable<CustomDataPoint>().Where(c => c.Id == id).ToArrayAsync();
                }
                else
                {
                    existsDataPoints = await _client.Queryable<CustomDataPoint>().Where(c => c.TargetAssetId == customDataViewModel.TargetAssetId && c.TargetDataPointName == customDataViewModel.TargetDataPoint).ToArrayAsync();
                }

                var keys = new List<string>();
                foreach (var dp in existsDataPoints)
                {
                    targetAssetId = dp.TargetAssetId;
                    keys.Add(dp.TargetDataPointName);
                    _cache.Clear($"CustomDataPoint-{dp.RealAssetId}");
                }

                hasRemove = true;
                if (existsDataPoints.Length > 0)
                {
                    var ids = existsDataPoints.Select(e => e.Id).ToArray();
                    var count = await _client.Deleteable<CustomDataPoint>().Where(d => ids.Contains(d.Id)).ExecuteCommandAsync();

                    _cache.RemoveHashData($"AssetStatus:Currently-{targetAssetId}", keys.ToArray());
                    _log.LogDebug($"删除CustomDataPoint个数:{count}");

                    var data = new Dictionary<string, string>();
                    foreach (var k in keys)
                    {
                        switch (k)
                        {
                            case "Switch":
                                data.TryAdd("Switch", "0");
                                break;
                            case "BreakerPosition":
                                data.TryAdd("BreakerPosition", "3");
                                break;
                            default: break;
                        }
                    }

                    proxyRef.DataChanged(new Model.DataFlow.AssetChangeData()
                    {
                        AssetId = customDataViewModel.TargetAssetId,
                        ChangeDatas = data,
                        ChangeTime = DateTime.Now
                    });
                }
            }
            #endregion

            if (!hasRemove)
            {
                var valueMapping = GetValueMapping(customDataViewModel.RealAssetModel, customDataViewModel.RealAssetType, customDataViewModel.TargetDataPoint);
                if (id <= 0)
                {
                    var customData = new CustomDataPoint
                    {
                        TargetAssetId = customDataViewModel.TargetAssetId,
                        TargetDataPointName = customDataViewModel.TargetDataPoint,
                        RealAssetId = customDataViewModel.RealAssetId,
                        RealDataPointName = customDataViewModel.RealDataPoint,
                        ValueMappingStr = valueMapping == null ? string.Empty : JsonConvert.SerializeObject(valueMapping),
                        CreatedBy = UserName,
                        UpdatedBy = UserName,
                        UpdatedTime = DateTime.Now,
                        CreatedTime = DateTime.Now,
                    };
                    id = await _client.Insertable(customData).ExecuteReturnIdentityAsync();
                }
                else
                {
                    var oldConfig = await _client.Queryable<CustomDataPoint>().FirstAsync(c => c.Id == id);
                    if (oldConfig == null)
                    {
                        var customData = new CustomDataPoint
                        {
                            Id = 0,
                            TargetAssetId = customDataViewModel.TargetAssetId,
                            TargetDataPointName = customDataViewModel.TargetDataPoint,
                            RealAssetId = customDataViewModel.RealAssetId,
                            RealDataPointName = customDataViewModel.RealDataPoint,
                            ValueMappingStr = valueMapping == null ? string.Empty : JsonConvert.SerializeObject(valueMapping),
                            CreatedBy = UserName,
                            UpdatedBy = UserName,
                            UpdatedTime = DateTime.Now,
                            CreatedTime = DateTime.Now,
                        };

                        id = await _client.Insertable(customData).ExecuteReturnIdentityAsync();
                    }
                    else
                    {
                        _cache.Clear($"CustomDataPoint-{oldConfig.RealAssetId}");

                        oldConfig.TargetAssetId = customDataViewModel.TargetAssetId;
                        oldConfig.TargetDataPointName = customDataViewModel.TargetDataPoint;
                        oldConfig.RealAssetId = customDataViewModel.RealAssetId;
                        oldConfig.RealDataPointName = customDataViewModel.RealDataPoint;
                        oldConfig.ValueMappingStr = valueMapping == null ? string.Empty : JsonConvert.SerializeObject(valueMapping);
                        oldConfig.UpdatedBy = UserName;
                        oldConfig.UpdatedTime = DateTime.Now;

                        await _client.Updateable(oldConfig).ExecuteCommandAsync();
                    }
                }
                _cache.Clear($"CustomDataPoint-{customDataViewModel.RealAssetId}");

                var currentlyData = _cache.GetHashData(string.Format(AssetCurrentStatusCacheKey, customDataViewModel.RealAssetId), new string[] { customDataViewModel.RealDataPoint });
                if (currentlyData != null && currentlyData.ContainsKey(customDataViewModel.RealDataPoint))
                {
                    proxyRef.DataChanged(new Model.DataFlow.AssetChangeData()
                    {
                        AssetId = customDataViewModel.TargetAssetId,
                        ChangeDatas = new Dictionary<string, string>()
                        {
                            [customDataViewModel.TargetDataPoint] = valueMapping == null ? currentlyData[customDataViewModel.RealDataPoint] : GetValue(currentlyData[customDataViewModel.RealDataPoint], valueMapping)
                        },
                        ChangeTime = DateTime.Now
                    });
                }
            }

            _cache.Clear($"BreakerCustomDataPoint-{targetAssetId}");

            return new ResponseBase<int>()
            {
                Data = id
            };
        }

        private string GetValue(string value, Dictionary<string, string> valueMapping)
        {
            if (valueMapping.ContainsKey(value))
            {
                return valueMapping[value];
            }

            return value;
        }

        /// <summary>
        /// 删除手动挂载数据点
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("DeleteCustomDataPoint")]
        [SwaggerOperation(Summary = "Swagger_Asset_DelCustomDataPoint", Description = "Swagger_Asset_DelCustomDataPoint_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<int>> DeleteCustomDataPoint(int id)
        {
            var result = await _client.Deleteable<CustomDataPoint>().Where(d => d.Id == id).ExecuteCommandAsync();

            return new ResponseBase<int>()
            {
                Data = result
            };
        }

        /// <summary>
        /// 获取开关位点位列表
        /// </summary>
        /// <param name="assetModel"></param>
        /// <param name="assetType"></param>
        /// <param name="assetId"></param>
        /// <param name="code">点位模糊查询</param>
        /// <returns></returns>
        [HttpGet("GetBitPointList")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetBitPointList", Description = "Swagger_Asset_GetBitPointList_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<Dictionary<string, string>>> GetBitPointList(string assetModel, string assetType, int? assetId, [AllowNull] string? code)
        {
            if (string.IsNullOrEmpty(assetModel) || string.IsNullOrEmpty(assetType))
            {
                return new ResponseBase<Dictionary<string, string>>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var result = new Dictionary<string, string>();
            var service = _provider.GetRequiredService<AssetDataPointInfoServer>();
            if ("Other".Equals(assetModel, StringComparison.OrdinalIgnoreCase)
                || "GeneralDevice".Equals(assetType, StringComparison.OrdinalIgnoreCase) || ("Modbus".Equals(assetModel, StringComparison.OrdinalIgnoreCase) && assetType == "Gateway"))
            {
                if (assetId == null)
                {
                    return new ResponseBase<Dictionary<string, string>>()
                    {
                        Code = 40300,
                        Message = MessageContext.ErrorParam
                    };
                }

                var bitConfigs = await service.GetBitConfigByThirdPartDevice(assetId.Value);

                foreach (var bitConfig in bitConfigs)
                {
                    string key = bitConfig.BitCode ?? bitConfig.BitName ?? string.Empty;
                    if (string.IsNullOrEmpty(key))
                    {
                        continue;
                    }

                    if (!result.ContainsKey(key))
                    {
                        result.Add(key, bitConfig.BitName ?? string.Empty);
                    }
                }
            }

            var dataPoints = await service.GetAssetDataPointsForSiemens(assetType, assetModel);

            foreach (var dataPoint in dataPoints)
            {
                if (!result.ContainsKey(dataPoint.Code))
                {
                    result.Add(dataPoint.Code, MessageContext.GetDataPointName(dataPoint.Code));
                }
            }

            if (!string.IsNullOrEmpty(code))
            {
                var temp = new Dictionary<string, string>();
                foreach (var item in result)
                {
                    if (!string.IsNullOrEmpty(item.Value) && item.Value.Contains(code))
                    {
                        temp.Add(item.Key, item.Value);
                    }
                }

                result = temp;
            }

            return new ResponseBase<Dictionary<string, string>>()
            {
                Code = 20000,
                Data = result
            };
        }

        private Dictionary<string, string>? GetValueMapping(string assetModel, string assetType, string dataPoint)
        {
            if ("Other".Equals(assetModel, StringComparison.OrdinalIgnoreCase)
                || "GeneralDevice".Equals(assetType, StringComparison.OrdinalIgnoreCase)
                || ("Modbus".Equals(assetModel, StringComparison.OrdinalIgnoreCase)
                && "Gateway".Equals(assetType, StringComparison.OrdinalIgnoreCase)))
            {
                switch (dataPoint)
                {
                    case "Switch":
                        return new Dictionary<string, string>()
                        {
                            ["0"] = "1",
                            ["1"] = "2",
                        };
                    case "BreakerPosition":
                        return new Dictionary<string, string>()
                        {
                            ["0"] = "0",
                            ["1"] = "1",
                        };
                    default: break;
                }
            }
            return null;
        }

        [HttpGet("GetGeneralDevicePoints")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetGeneralDevicePoints", Description = "Swagger_Asset_GetGeneralDevicePoints_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<List<UserCheckPointResult>>> GetGeneralDevicePoints(int assetId)
        {
            return new ResponseBase<List<UserCheckPointResult>>()
            {
                Code = 20000,
                Data = await GetGeneralDevicePoint(assetId)
            };
        }

        /// <summary>
        /// 通用设备数据
        /// </summary>
        /// <param name="assetId"></param>
        /// <returns></returns>
        private async Task<List<UserCheckPointResult>> GetGeneralDevicePoint(int assetId)
        {
            var pointData = new List<UserCheckPointResult>();

            var universalDeviceConfigs = await _client.Queryable<UniversalDeviceConfig>()
                                                      .Where(p => p.AssetId == assetId).ToListAsync();

            if (universalDeviceConfigs == null || universalDeviceConfigs.Count <= 0)
            {
                return pointData;
            }

            //获取二进点位集合
            List<int> universalDeviceConfigIds = universalDeviceConfigs.Where(p => p.IsBit).Select(p => p.Id).ToList();

            //二级制点位配置集合
            var bitConfigList = await _client.Queryable<BitConfig>()
                .Where(p => universalDeviceConfigIds.Contains(p.UniversalDeviceConfigId))
                .ToListAsync();

            #region Old
            //if (jsonData != null && jsonData.Treeview != null && jsonData.Treeview.Any())
            //{
            //    foreach (var item in jsonData.Treeview!)
            //    {
            //        //获取子集集合
            //        if (item.SubGroups != null && item.SubGroups.Any())
            //        {
            //            foreach (var _item in item.SubGroups)
            //            {
            //                if (_item.Properties != null && _item.Properties.Any())
            //                {
            //                    UniversalDeviceInfo.Instance._dicPoint.TryGetValue(_item.Name ?? "", out string? value);

            //                    var entity = new UserCheckPointResult()
            //                    {
            //                        EnParentName = _item.Name,
            //                        CnParentName = value
            //                    };

            //                    foreach (var secondItem in _item.Properties)
            //                    {
            //                        dicData.TryGetValue(secondItem.PropertyName ?? "", out string? data);

            //                        var exEntity = universalDeviceConfigs.FirstOrDefault(p =>
            //                                  p.PropertyEnName == secondItem.PropertyName);

            //                        if (exEntity != null && exEntity.IsBit)
            //                        {
            //                            var model = new UserCheckPointEntity()
            //                            {
            //                                PropertyEnName = exEntity?.PropertyEnName ?? secondItem.PropertyName,
            //                                PropertyCnName = exEntity?.PropertyCnName,
            //                                Value = data ?? "0000000000000000",
            //                                Unit = secondItem.Unit ?? "",
            //                                IsBit = true
            //                            };

            //                            //获取二进制点位
            //                            var bitConfigs = bitConfigList.Where(p => p.UniversalDeviceConfigId == exEntity?.Id).ToList();

            //                            if (bitConfigs.Any())
            //                            {
            //                                // 是否有科学计数法
            //                                if (model.Value.Contains("E") || model.Value.Contains("e"))
            //                                {
            //                                    model.Value = Decimal.Parse(model.Value, System.Globalization.NumberStyles.Float).ToString();
            //                                }

            //                                // 判断是否是16位二级制
            //                                if (!(model.Value.Length == 16 && StringFunction.IsBinary(model.Value)))
            //                                {
            //                                    model.Value = StringFunction.TenToBit(Convert.ToInt32(model.Value));
            //                                }

            //                                List<string?> bits = StringFunction.BitToList(model.Value);

            //                                foreach (var bitConfig in bitConfigs)
            //                                {
            //                                    if (model.BitDatas != null && !model.BitDatas.ContainsKey(bitConfig.BitNumber.ToString()))
            //                                    {
            //                                        model.BitDatas?.Add(bitConfig.BitNumber.ToString(), new BitDataDto()
            //                                        {
            //                                            Name = bitConfig.BitName,
            //                                            Value = bits[bitConfig.BitNumber]
            //                                        });
            //                                    }
            //                                }
            //                            }
            //                            else
            //                            {
            //                                for (int i = 0; i < 16; i++)
            //                                {
            //                                    model.BitDatas?.Add(i.ToString(), new BitDataDto()
            //                                    {
            //                                        Name = $"bit{i}",
            //                                        Value = "0"
            //                                    });
            //                                }
            //                            }

            //                            entity.UserCheckPoints?.Add(model);
            //                        }
            //                        else
            //                        {
            //                            string strData = string.IsNullOrWhiteSpace(data) ? "0" : data;
            //                            decimal val = Decimal.Parse(strData, System.Globalization.NumberStyles.Float);

            //                            entity.UserCheckPoints?.Add(new UserCheckPointEntity()
            //                            {
            //                                PropertyEnName = exEntity?.PropertyEnName ?? secondItem.PropertyName,
            //                                PropertyCnName = exEntity?.PropertyCnName ?? secondItem.DescriptionInGerman,
            //                                Value = string.IsNullOrWhiteSpace(data) ? "" : val.ToString("0.00"),
            //                                Unit = secondItem.Unit ?? "",
            //                                IsBit = false
            //                            });
            //                        }
            //                    }

            //                    pointData.Add(entity);
            //                }
            //            }
            //        }

            //        if (item.Properties != null && item.Properties.Any())
            //        {
            //            foreach (var _item in item.Properties)
            //            {
            //                UniversalDeviceInfo.Instance._dicPoint.TryGetValue(_item.GroupName!, out string? value);

            //                var entity = new UserCheckPointResult()
            //                {
            //                    EnParentName = _item.GroupName,
            //                    CnParentName = value
            //                };

            //                dicData.TryGetValue(_item.PropertyName ?? "", out string? data);

            //                var exEntity = universalDeviceConfigs.FirstOrDefault(p =>
            //                                  p.PropertyEnName == _item.PropertyName);

            //                if (exEntity != null && exEntity.IsBit)
            //                {
            //                    var model = new UserCheckPointEntity()
            //                    {
            //                        PropertyEnName = exEntity?.PropertyEnName ?? _item.PropertyName,
            //                        PropertyCnName = exEntity?.PropertyCnName ?? _item.DescriptionInGerman,
            //                        Value = data ?? "0000000000000000",
            //                        Unit = _item.Unit ?? "",
            //                        IsBit = true
            //                    };

            //                    //获取二进制点位
            //                    var bitConfigs = bitConfigList.Where(p => p.UniversalDeviceConfigId == exEntity?.Id).ToList();

            //                    if (bitConfigs.Any())
            //                    {
            //                        // 是否有科学计数法
            //                        if (model.Value.Contains("E") || model.Value.Contains("e"))
            //                        {
            //                            model.Value = Decimal.Parse(model.Value, System.Globalization.NumberStyles.Float).ToString();
            //                        }

            //                        // 判断是否是16位二级制
            //                        if (!(model.Value.Length == 16 && StringFunction.IsBinary(model.Value)))
            //                        {
            //                            model.Value = StringFunction.TenToBit(Convert.ToInt32(model.Value));
            //                        }

            //                        List<string?> bits = StringFunction.BitToList(model.Value);

            //                        foreach (var bitConfig in bitConfigs)
            //                        {
            //                            if (model.BitDatas != null && !model.BitDatas.ContainsKey(bitConfig.BitNumber.ToString()))
            //                            {
            //                                model.BitDatas?.Add(bitConfig.BitNumber.ToString(), new BitDataDto()
            //                                {
            //                                    Name = bitConfig.BitName,
            //                                    Value = (bitConfig.BitNumber > bits.Count - 1) ? "0" : bits[bitConfig.BitNumber]
            //                                });
            //                            }
            //                        }
            //                    }
            //                    else
            //                    {
            //                        for (int i = 0; i < 16; i++)
            //                        {
            //                            model.BitDatas?.Add(i.ToString(), new BitDataDto()
            //                            {
            //                                Name = $"bit{i}",
            //                                Value = "0"
            //                            });
            //                        }
            //                    }

            //                    entity.UserCheckPoints?.Add(model);
            //                }
            //                else
            //                {
            //                    string strData = string.IsNullOrWhiteSpace(data) ? "0" : data;
            //                    decimal val = Decimal.Parse(strData, System.Globalization.NumberStyles.Float);

            //                    entity.UserCheckPoints?.Add(new UserCheckPointEntity()
            //                    {
            //                        PropertyEnName = exEntity?.PropertyEnName ?? _item.PropertyName,
            //                        PropertyCnName = exEntity?.PropertyCnName ?? _item.DescriptionInGerman,
            //                        Value = string.IsNullOrWhiteSpace(data) ? "" : val.ToString("0.00"),
            //                        Unit = _item.Unit ?? "",
            //                        IsBit = false
            //                    });
            //                }

            //                pointData.Add(entity);
            //            }
            //        }
            //    }
            //}
            #endregion

            #region New

            foreach (var config in universalDeviceConfigs)
            {
                var item = new UserCheckPointEntity
                {
                    PropertyEnName = config.PropertyEnName,
                    PropertyCnName = config.PropertyCnName,
                    IsBit = config.IsBit,
                    Unit = config.Unit,
                    BitDatas = new Dictionary<string, BitDataDto>(),
                };

                if (config.IsBit)
                {
                    var bitConfigs = bitConfigList.Where(b => b.UniversalDeviceConfigId == config.Id).ToArray();
                    foreach (var bitItem in bitConfigs)
                    {
                        item.BitDatas.TryAdd(bitItem.BitNumber.ToString(), new BitDataDto
                        {
                            Code = bitItem.BitCode,
                            Name = bitItem.BitName,
                        });
                    }
                }

                var groupName = config.GroupName ?? "State";

                var point = pointData.FirstOrDefault(p => p.EnParentName == groupName);
                if (point == null)
                {
                    point = new UserCheckPointResult
                    {
                        EnParentName = groupName,
                        CnParentName = MessageContext.GetString($"DataPointGroupName_{groupName}") ?? groupName,
                        UserCheckPoints = new List<UserCheckPointEntity>(),
                    };
                    pointData.Add(point);
                }

                if (point.UserCheckPoints == null)
                {
                    point.UserCheckPoints = new List<UserCheckPointEntity>();
                }

                point.UserCheckPoints.Add(item);
            }
            #endregion

            #region Sort
            pointData = SortPointResult(pointData);
            #endregion

            return pointData;
        }

        private readonly Dictionary<string, int> PointKeySort = new Dictionary<string, int>()
        {
            ["Voltage"] = 1,
            ["Current"] = 2,
            ["Power"] = 3,
            ["Power Factor"] = 4,
            ["Cos Phi"] = 5,
            ["Power Period"] = 6,
            ["Frequency"] = 7,
            ["THD"] = 8,
            ["Counter"] = 9,
            ["Flicker"] = 10,
            ["Temperature"] = 11,
            ["Voltage Harmonics"] = 15,
            ["Current Harmonics"] = 16,
            ["Others"] = 98,
            ["State"] = 1000,
        };

        private List<UserCheckPointResult> SortPointResult(List<UserCheckPointResult> pointResults)
        {
            var result = pointResults.OrderBy(p =>
            {
                var sort = 999;
                if (!string.IsNullOrEmpty(p.EnParentName)
                    && PointKeySort.TryGetValue(p.EnParentName, out var newSort))
                {
                    sort = newSort;
                }

                return sort;
            }).ToList();
            return result;
        }


        /// <summary>
        /// 数据库连接池释放
        /// </summary>
        public void Dispose()
        {
            _client.Close();
        }
    }
}
