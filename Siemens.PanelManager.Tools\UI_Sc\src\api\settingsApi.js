import axios from "axios";
import { initAxios } from "./apiSetting";

function getSettings(setSettings, failCallback) {
  initAxios();

  axios
    .get("api/getSettings")
    .then((resp) => {
      setSettings(resp.data);
    })
    .catch((e) => {
      if (!!failCallback) failCallback();
    });
}

function updateSettings(settings, successCallback, failCallback) {
  initAxios();

  axios
    .post("api/setSettings", settings)
    .then((resp) => {
      if (!!successCallback) successCallback(resp.data);
    })
    .catch((e) => {
      if (!!failCallback) failCallback();
    });
}

export { getSettings, updateSettings };
