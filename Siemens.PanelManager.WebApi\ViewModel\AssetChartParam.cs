﻿using Microsoft.AspNetCore.Mvc;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class AssetChartParam
    {
        [FromRoute(Name = "assetId")]
        public int AssetId { get; set; }
        [FromQuery(Name = "ChartName")]
        public string[] ChartNames { get; set; } = new string[0];
        [FromQuery(Name = "ChartDateType")]
        public ChartDateType ChartDateType { get; set; } = ChartDateType.Get24Hour;
        [FromQuery(Name = "StartDate")]
        public string? StartDate { get; set; }
        [FromQuery(Name = "EndDate")]
        public string? EndDate { get; set;}
    }

    /// <summary>
    /// 批量修改资产信息入参
    /// </summary>
    public class BatchAssetParam
    {   
        /// <summary>
        /// 资产主键id
        /// </summary>
        public int AssetId { get; set; }

        /// <summary>
        /// json文件的Code
        /// </summary>
        public string? ThirdPartCode { get; set; }
    }
}
