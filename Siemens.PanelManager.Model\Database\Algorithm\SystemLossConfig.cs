﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Algorithm
{
    [SugarTable("systemloss_config")]
    public class SystemLossConfig : LogicDataBase
    {
        [Uniqueness]
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsNullable = false)]
        public int Id { get; set; }

        [SugarColumn(ColumnName = "refer_loss", IsNullable = false)]
        public decimal ReferLoss { get; set; }
    }
}
