﻿using Newtonsoft.Json;
using Siemens.PanelManager.Model.Database.Asset;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class DeviceInfo
    {
        /// <summary>
        /// 额定电流
        /// </summary>
        public decimal? RatedCurrent { get; set; }
        /// <summary>
        /// 额定功率
        /// </summary>
        public decimal? RatedPower { get; set; }
        /// <summary>
        /// 额定电压
        /// </summary>
        public decimal? RatedVoltage { get; set; }
        /// <summary>
        /// 运行小时数
        /// </summary>
        public decimal? OperatingHours { get; set; }
        /// <summary>
        /// 当前费率
        /// </summary>
        public string? ActualTariff { get; set; }
        /// <summary>
        /// 硬件写保护
        /// </summary>
        public string? HarwareWriteProtectionStatus { get; set; }
        /// <summary>
        /// 插槽 1 是否激活
        /// </summary>
        public int? Slot_1 { get; set; }
        /// <summary>
        /// 插槽 2 是否激活
        /// </summary>
        public int? Slot_2 { get; set; }
        /// <summary>
        /// 插槽 3 是否激活
        /// </summary>
        public int? Slot_3 { get; set; }
        /// <summary>
        /// 插槽 4 是否激活
        /// </summary>
        public int? Slot_4 { get; set; }
        /// <summary>
        /// 是否使用电压互感器
        /// </summary>
        public bool? UseVoltageTransformer { get; set; }

        /// <summary>
        /// MLFB
        /// </summary>
        public string MLFB { get; set; } = string.Empty;
        /// <summary>
        /// 断路器订单号
        /// </summary>
        public string? BreakerOrderNo { get; set; }
        /// <summary>
        /// 设备序列号
        /// </summary>
        public string? SerialNumber { get; set; }
        /// <summary>
        /// 设备识别号
        /// </summary>
        public string? IdentNumber { get; set; }
        /// <summary>
        /// 固件版本号 1
        /// </summary>
        public string? FirmwareRevision_1 { get; set; }
        /// <summary>
        /// 固件版本号 2
        /// </summary>
        public string? FirmwareRevision_2 { get; set; }
        /// <summary>
        /// 固件版本号 3
        /// </summary>
        public string? FirmwareRevision_3 { get; set; }
        /// <summary>
        /// 固件版本号 4
        /// </summary>
        public string? FirmwareRevision_4 { get; set; }
        /// <summary>
        /// 固件版本号 5
        /// </summary>
        public string? FirmwareRevision_5 { get; set; }
        /// <summary>
        /// 总脱扣数
        /// </summary>
        public int? AllTrips { get; set; }
        /// <summary>
        /// 过载保护 脱扣次数
        /// </summary>
        public int? LTTrips { get; set; }
        /// <summary>
        /// 短时间延迟短路保护 脱扣次数
        /// </summary>
        public int? STTrips { get; set; }
        /// <summary>
        /// 瞬时保护 脱扣次数
        /// </summary>
        public int? INSTTrips { get; set; }
        /// <summary>
        /// 接地保护 脱扣次数
        /// </summary>
        public int? GFTrips { get; set; }
        /// <summary>
        /// 中性线 (N) 过载保护 脱扣次数
        /// </summary>
        public int? NTrips { get; set; }
        /// <summary>
        /// 反向功率保护 脱扣次数
        /// </summary>
        public int? RPTrips { get; set; }
        /// <summary>
        /// 机械操作循环次数
        /// 不带载
        /// </summary>
        public int? MechanicalSwitchCycles { get; set; }
        /// <summary>
        /// 电气操作循环次数
        /// 带载
        /// </summary>
        public int? ElectricalSwitchCycles { get; set; }
        /// <summary>
        /// 机械操作循环次数（样本值）
        /// 不带载
        /// </summary>
        public int? MechanicalSwitchSampleCycles { get; set; }
        /// <summary>
        /// 电气操作循环次数（样本值）
        /// 带载
        /// </summary>
        public int? ElectricalSwitchSampleCycles { get; set; }
        public decimal? Icw { get; set; }
        /// <summary>
        /// 一次侧电压
        /// </summary>
        public decimal? PrimaryVoltage { get; set; }
        /// <summary>
        /// 二次侧电压
        /// </summary>
        public decimal? SecondaryVoltage { get; set; }

        /// <summary>
        /// 一次侧电流
        /// </summary>
        public decimal? PrimaryCurrent { get; set; }
        /// <summary>
        /// 二次侧电流
        /// </summary>
        public decimal? SecondaryCurrent { get; set; }

        /// <summary>
        /// 主触头状态
        /// </summary>
        public string? MainContantStatus { get; set; }

        /// <summary>
        /// 剩余寿命
        /// </summary>
        public string? RemainingLife { get; set; }

        /// <summary>
        /// 断路器温度
        /// </summary>
        public string? BreakerTemp { get; set; }

        /// <summary>
        /// 隔室温度
        /// </summary>
        public string? ComTemp { get; set; }

        /// <summary>
        /// 健康级别
        /// normal
        /// attention
        /// maintain
        /// rushRepair
        /// </summary>
        public string? HealthLevel { get; set; }

        /// <summary>
        /// 健康评分
        /// </summary>
        public string? HealthScore { get; set; }
        /// <summary>
        /// 触头磨损率
        /// </summary>
        public double? ContactWearRate { get; set; }

        /// <summary>
        /// 电表位置
        /// </summary>
        public MeterSite? MeterSite { get; set; }
    }

    #region ProtectionSetting
    /// <summary>
    /// 保护定值
    /// </summary>
    public class ProtectionSetting
    {
        ///
        /// LT保护功能投退
        ///
        [JsonProperty("LT_ONOFF")]
        public string? LT_ONOFF { get; set; }

        ///
        /// LT电流设定值
        ///
        [JsonProperty("LT_IR")]
        public string? LT_IR { get; set; }

        ///
        /// LT脱扣时间 6xIr
        ///
        [JsonProperty("LT_TR")]
        public string? LT_TR { get; set; }

        ///
        /// LT特性曲线
        ///
        [JsonProperty("LT_PARA_CURVE")]
        public string? LT_PARA_CURVE { get; set; }

        ///
        /// LT保护功能投入，e.SET
        ///
        [JsonProperty("LT_ONOFF_REMOTE")]
        public string? LT_ONOFF_REMOTE { get; set; }

        ///
        /// LT电流设定值，e.SET
        ///
        [JsonProperty("LT_IR_REMOTE")]
        public string? LT_IR_REMOTE { get; set; }

        ///
        /// LT脱扣时间 6xIr，e.SET
        ///
        [JsonProperty("LT_TR_REMOTE")]
        public string? LT_TR_REMOTE { get; set; }

        ///
        /// LT特性曲线，e.SET
        ///
        [JsonProperty("LT_PARA_CURVE_REMOTE")]
        public string? LT_PARA_CURVE_REMOTE { get; set; }

        ///
        /// 冷却时间常数
        ///
        [JsonProperty("LT_TAU")]
        public string? LT_TAU { get; set; }

        ///
        /// 热记忆
        ///
        [JsonProperty("LT_THERMAL_MEM_ONOFF")]
        public string? LT_THERMAL_MEM_ONOFF { get; set; }

        ///
        /// 相不平衡保护
        ///
        [JsonProperty("LT_PHASE_LOSS_SENSITIV_ONOFF")]
        public string? LT_PHASE_LOSS_SENSITIV_ONOFF { get; set; }

        ///
        /// PAL过载预告警
        ///
        [JsonProperty("PAL_ONOFF")]
        public string? PAL_ONOFF { get; set; }

        ///
        /// PAL过载告警电流定值
        ///
        [JsonProperty("PAL_IR")]
        public string? PAL_IR { get; set; }

        ///
        /// PAL过载告警时间
        ///
        [JsonProperty("PAL_TR")]
        public string? PAL_TR { get; set; }

        ///
        /// LT N保护功能投退
        ///
        [JsonProperty("LTN_ONOFF")]
        public string? LTN_ONOFF { get; set; }

        ///
        /// LT N电流设定值
        ///
        [JsonProperty("LTN_IN")]
        public string? LTN_IN { get; set; }

        ///
        /// PAL电流设定值
        ///
        [JsonProperty("PAL_IN")]
        public string? PAL_IN { get; set; }

        ///
        /// ST保护功能投退
        ///
        [JsonProperty("ST_ONOFF")]
        public string? ST_ONOFF { get; set; }

        ///
        /// ST电流设定值
        ///
        [JsonProperty("ST_ISD")]
        public string? ST_ISD { get; set; }

        ///
        /// ST脱扣时间
        ///
        [JsonProperty("ST_TSD")]
        public string? ST_TSD { get; set; }

        ///
        /// ST特性曲线
        ///
        [JsonProperty("ST_I2t_ON_OFF")]
        public string? ST_I2t_ON_OFF { get; set; }

        ///
        /// ST保护功能投入，e.SET
        ///
        [JsonProperty("ST_ONOFF_REMOTE")]
        public string? ST_ONOFF_REMOTE { get; set; }

        ///
        /// ST电流设定值，e.SET
        ///
        [JsonProperty("ST_ISD_REMOTE")]
        public string? ST_ISD_REMOTE { get; set; }

        ///
        /// ST脱扣时间，e.SET
        ///
        [JsonProperty("ST_TSD_REMOTE")]
        public string? ST_TSD_REMOTE { get; set; }

        ///
        /// ST特性曲线，e.SET
        ///
        [JsonProperty("ST_I2t_ON_OFF_REMOTE")]
        public string? ST_I2t_ON_OFF_REMOTE { get; set; }

        ///
        /// ST参考点
        ///
        [JsonProperty("ST_ISD_REF_I2TSD")]
        public string? ST_ISD_REF_I2TSD { get; set; }

        ///
        /// ST间隙保护功能投入
        ///
        [JsonProperty("ST_INTERMITTENT_ONOFF")]
        public string? ST_INTERMITTENT_ONOFF { get; set; }

        ///
        /// DST保护功能投退
        ///
        [JsonProperty("DST_ONOFF")]
        public string? DST_ONOFF { get; set; }

        ///
        /// DST正向电流设定值
        ///
        [JsonProperty("DST_ISD_FW")]
        public string? DST_ISD_FW { get; set; }

        ///
        /// DST反向电流设定值
        ///
        [JsonProperty("DST_ISD_REV")]
        public string? DST_ISD_REV { get; set; }

        ///
        /// DST正向跳闸时间
        ///
        [JsonProperty("DST_TSD_FW")]
        public string? DST_TSD_FW { get; set; }

        ///
        /// DST反向跳闸时间
        ///
        [JsonProperty("DST_TSD_REV")]
        public string? DST_TSD_REV { get; set; }

        ///
        /// INST保护功能投退
        ///
        [JsonProperty("INST_ONOFF")]
        public string? INST_ONOFF { get; set; }

        ///
        /// INST电流设定值
        ///
        [JsonProperty("INST_II")]
        public string? INST_II { get; set; }

        ///
        /// INST电流设定值，e.SET
        ///
        [JsonProperty("INST_ONOFF_REMOTE")]
        public string? INST_ONOFF_REMOTE { get; set; }

        ///
        /// INST电流设定值，e.SET
        ///
        [JsonProperty("INST_II_REMOTE")]
        public string? INST_II_REMOTE { get; set; }

        ///
        /// GF接地保护功能投退
        ///
        [JsonProperty("GF_PROTECTION_ONOFF")]
        public string? GF_PROTECTION_ONOFF { get; set; }

        ///
        /// GF间隙性检测
        ///
        [JsonProperty("GF_INTERMITTENT_ONOFF")]
        public string? GF_INTERMITTENT_ONOFF { get; set; }

        ///
        /// GF保护特性曲线
        ///
        [JsonProperty("GF_STD_PARA_CURVE")]
        public string? GF_STD_PARA_CURVE { get; set; }

        ///
        /// GF合成零序电流设定值
        ///
        [JsonProperty("GF_STD_IG_RESIDUAL")]
        public string? GF_STD_IG_RESIDUAL { get; set; }

        ///
        /// GF合成零序脱扣时间
        ///
        [JsonProperty("GF_STD_TG_RESIDUAL")]
        public string? GF_STD_TG_RESIDUAL { get; set; }

        ///
        /// GF外接零序电流设定值
        ///
        [JsonProperty("GF_STD_IG_DIRECT")]
        public string? GF_STD_IG_DIRECT { get; set; }

        ///
        /// GF外接零序脱扣时间
        ///
        [JsonProperty("GF_STD_TG_DIRECT")]
        public string? GF_STD_TG_DIRECT { get; set; }

        ///
        /// GF Dual限制性零序电流设定值
        ///
        [JsonProperty("GF_STD_IG_DM_REF")]
        public string? GF_STD_IG_DM_REF { get; set; }

        ///
        /// GF Dual限制性零序脱扣时间
        ///
        [JsonProperty("GF_STD_TG_DM_REF")]
        public string? GF_STD_TG_DM_REF { get; set; }

        ///
        /// GF Dual非限制性零序电流设定值
        ///
        [JsonProperty("GF_STD_IG_DM_UREF")]
        public string? GF_STD_IG_DM_UREF { get; set; }

        ///
        /// GF Dual非限制性零序脱扣时间
        ///
        [JsonProperty("GF_STD_TG_DM_UREF")]
        public string? GF_STD_TG_DM_UREF { get; set; }

        ///
        /// GF限制性零序电流设定值
        ///
        [JsonProperty("GF_STD_IG_HIZ_REF_SEC")]
        public string? GF_STD_IG_HIZ_REF_SEC { get; set; }

        ///
        /// GF限制性零序脱扣时间
        ///
        [JsonProperty("GF_STD_TG_HIZ_REF")]
        public string? GF_STD_TG_HIZ_REF { get; set; }

        ///
        /// GF非限制性零序电流设定值
        ///
        [JsonProperty("GF_STD_IG_HIZ_UREF")]
        public string? GF_STD_IG_HIZ_UREF { get; set; }

        ///
        /// GF非限制性零序脱扣时间
        ///
        [JsonProperty("GF_STD_TG_HIZ_UREF")]
        public string? GF_STD_TG_HIZ_UREF { get; set; }

        ///
        /// GF告警功能投退
        ///
        [JsonProperty("GF_ALARM_ONOFF")]
        public string? GF_ALARM_ONOFF { get; set; }

        ///
        /// GF合成零序告警电流设定值
        ///
        [JsonProperty("GF_ALARM_IG_RESIDUAL")]
        public string? GF_ALARM_IG_RESIDUAL { get; set; }

        ///
        /// GF外接零序告警电流设定值
        ///
        [JsonProperty("GF_ALARM_IG_DIRECT")]
        public string? GF_ALARM_IG_DIRECT { get; set; }

        ///
        /// GF Dual非限制性接地告警电流设定值
        ///
        [JsonProperty("GF_ALARM_IG_DM_UREF")]
        public string? GF_ALARM_IG_DM_UREF { get; set; }

        ///
        /// GF 高阻非限制性接地告警电流设定值
        ///
        [JsonProperty("GF_ALARM_IG_HIZ_UREF")]
        public string? GF_ALARM_IG_HIZ_UREF { get; set; }

        ///
        /// GF告警时间
        ///
        [JsonProperty("GF_ALARM_TG")]
        public string? GF_ALARM_TG { get; set; }

        ///
        /// RP保护投退
        ///
        [JsonProperty("RP_ONOFF")]
        public string? RP_ONOFF { get; set; }

        ///
        /// RP功率设定值
        ///
        [JsonProperty("RP_PICKUP")]
        public string? RP_PICKUP { get; set; }

        ///
        /// RP脱扣时间
        ///
        [JsonProperty("RP_DELAY")]
        public string? RP_DELAY { get; set; }
        /// <summary>
        /// Tc/Tp 选择
        /// </summary>
        [JsonProperty("MP_TcTp_SELECTOR")]
        public string? MP_TcTp_SELECTOR { get; set; }
        /// <summary>
        /// 脱扣等级 Tc
        /// </summary>
        [JsonProperty("MP_Tc")]
        public string? MP_Tc { get; set; }
        /// <summary>
        /// 脱扣时间 Tp (s)
        /// </summary>
        [JsonProperty("MP_Tp")]
        public string? MP_Tp { get; set; }
        /// <summary>
        /// 瞬时保护（弧闪模式）
        /// </summary>
        [JsonProperty("INST_II_ARC")]
        public string? INST_II_ARC { get; set; }
        /// <summary>
        /// 接地故障（弧闪模式）
        /// </summary>
        [JsonProperty("GF_IG_ARC")]
        public string? GF_IG_ARC { get; set; }

        /// <summary>
        /// 外接零序接地保护时间设定值
        /// </summary>
        [JsonProperty("GF_XTD_TG")]
        public string? GF_XTD_TG { get; set; }

        /// <summary>
        /// 外接零序接地保护电流设定值
        /// </summary>
        [JsonProperty("GF_XTD_IG")]
        public string? GF_XTD_IG { get; set; }
        /// <summary>
        /// 接地故障告警电流设定值g
        /// </summary>
        [JsonProperty("GF_ALARM_IG")]
        public string? GF_ALARM_IG { get; set; }
        /// <summary>
        /// 合成零序接地保护电流设定值
        /// </summary>
        [JsonProperty("GF_IG")]
        public string? GF_IG { get; set; }
        /// <summary>
        /// 合成零序接地保护时间设定值
        /// </summary>
        [JsonProperty("GF_TG")]
        public string? GF_TG { get; set; }

        /// <summary>
        /// GF 脱扣开/关
        /// </summary>
        [JsonProperty("GF_ONOFF")]
        public string? GF_ONOFF { get; set; }

        /// <summary>
        /// GF 特性曲线的特征
        /// </summary>
        [JsonProperty("GF_I2t_ON_OFF")]
        public string? GF_PARA_CURVE { get; set; }

        /// <summary>
        /// GF 故障的方法
        /// </summary>
        [JsonProperty("GF_TYPE")]
        public string? GF_TYPE { get; set; }
    }
    #endregion

}

