﻿using Siemens.PanelManager.Job.UploadExcel.Diagram.TemplateModel.NodeTemplate;
using Siemens.PanelManager.Model.Topology;

namespace Siemens.PanelManager.Job.UploadExcel.Diagram.TemplateModel
{
    /// <summary>
    /// 单线图中出线回路
    /// </summary>
    internal class OutletCircuit : CircuitTemplateModelBase
    {
        private List<NodeData> _nodes;
        private List<LineData> _lineDatas;
        private int _height = 0;
        private int _width = 80;
        public OutletCircuit(string? busBarName, CircuitModel model, Dictionary<string, GroupKeyMappingInfo> idTable)
            : this(busBarName, model, 0, idTable)
        {
        }
        public OutletCircuit(string? busBarName, CircuitModel model, int direction, Dictionary<string, GroupKeyMappingInfo> idTable)
            : base(busBarName, model, idTable)
        {
            if (model == null)
            {
                throw new CreateTemplateException("回路缺失信息");
            }
            if (model.AssetInfo == null)
            {
                throw new CreateTemplateException("回路缺失资产信息");
            }

            if (model.SubDevices == null || model.SubDevices.Count == 0)
            {
                throw new CreateTemplateException($"回路{model.AssetInfo.AssetNumber}-{model.AssetInfo.AssetName}缺失子设备");
            }

            if (model.SubDevices == null || !model.SubDevices.Any(d => d.AssetType == "MCCB" || d.AssetType == "ACB" || d.AssetType == "MCB"))
            {
                throw new CreateTemplateException($"回路{model.AssetInfo.AssetNumber}-{model.AssetInfo.AssetName}缺失断路器");
            }

            Direction = direction;
            _nodes = new List<NodeData>();
            _lineDatas = new List<LineData>();

            // 添加出线
            _nodes.Add(new OutletNode(busBarName)
            {
                Angle = OutletAngle,
                Key = GetNewId(),
            });

            var meterId = GetNewId();
            var breakerId = GetNewId();
            var labelId = GetNewId();

            var breakerAsset = model.SubDevices.FirstOrDefault(d => d.AssetType == "MCCB" || d.AssetType == "ACB" || d.AssetType == "MCB");
            if (breakerAsset != null)
            {
                var breaker = new SwitchNode(busBarName)
                {
                    Model = breakerAsset.AssetModel ?? string.Empty,
                    AssetId = breakerAsset.Id,
                    AssetName = breakerAsset.AssetName ?? string.Empty,
                    DeviceType = breakerAsset.AssetType,
                    Angle = SwitchAngle,
                    CircuitId = model.AssetInfo.Id,
                    CircuitName = model.AssetInfo.CircuitName ?? string.Empty,
                    Key = breakerId,
                };
                _nodes.Add(breaker);

                if ("3WA".Equals(breakerAsset.AssetModel) || "3WL".Equals(breakerAsset.AssetModel))
                {
                    breaker.BreakerPosition = "2";
                }
            }

            //var breakerAssets = model.SubDevices.Where(d => d.AssetType == "MCCB" || d.AssetType == "ACB" || d.AssetType == "MCB" || d.AssetType == "GeneralDevice").ToList();

            //if (breakerAssets != null && breakerAssets.Any())
            //{
            //    foreach (var item in breakerAssets)
            //    {
            //        var breakerId = GetNewId();

            //        var breaker = new SwitchNode(busBarName)
            //        {
            //            Model = item.AssetModel ?? string.Empty,
            //            AssetId = item.Id,
            //            AssetName = item.AssetName ?? string.Empty,
            //            Angle = SwitchAngle,
            //            CircuitId = model.AssetInfo.Id,
            //            CircuitName = model.AssetInfo.CircuitName ?? string.Empty,
            //            Key = breakerId,
            //        };

            //        if ("3WA".Equals(item.AssetModel) || "3WL".Equals(item.AssetModel) || "GeneralDevice".Equals(item.AssetModel))
            //        {
            //            breaker.BreakerPosition = "0";
            //        }

            //        _nodes.Add(breaker);
            //    }
            //}

            var meter = model.SubDevices.FirstOrDefault(d => d.AssetType == "Meter");
            if (meter == null)
            {
                meter = model.SubDevices.FirstOrDefault(d => d.AssetType == "GeneralDevice" && d.AssetModel == "GeneralDevice");
            }

            _nodes.Add(new MeterNode(busBarName)
            {
                Model = meter?.AssetModel ?? string.Empty,
                AssetId = meter?.Id ?? 0,
                AssetName = meter?.AssetName ?? string.Empty,
                Key = meterId
            });

            _nodes.Add(new LabelNode()
            {
                Text = model.AssetInfo.CircuitName ?? string.Empty,
                Key = labelId
            });

            #region 数据点位
            var dataPointId = GetNewId();
            _nodes.Add(new DataPointNode()
            {
                Name = model.AssetInfo.AssetName ?? string.Empty,
                ElectricalType = "BPhaseCurrent",
                ElectricalName = "B 相电流",
                Key = dataPointId,
                BindAssetId = model.AssetInfo.Id,
                BindPointName = "Ib"
            });
            #endregion

            #region 计算坐标
            CalculateLocation();
            #endregion

            #region 添加线
            LinkLine(_nodes, _lineDatas);
            #endregion
        }

        /// <summary>
        /// 计算回路中设备对应的相对位置
        /// </summary>
        private void CalculateLocation()
        {
            switch (Direction)
            {
                case 1:
                    {
                        var switchModel = _nodes.First(d => d.NodeType == NodeType.Switch);
                        switchModel.LocationX = 0;
                        switchModel.LocationY = 20;
                        var label = _nodes.First(d => d.NodeType == NodeType.Label);
                        label.LocationX = 80;
                        label.LocationY = 20;
                        var dataPoint = _nodes.First(d => d.NodeType == NodeType.DataPoint);
                        dataPoint.LocationX = 100;
                        dataPoint.LocationY = 100;
                        var meterModel = _nodes.First(d => d.NodeType == NodeType.Meter);
                        meterModel.LocationX = 0;
                        meterModel.LocationY = 150;
                        var source = _nodes.First(d => d.NodeType == NodeType.Outlet);
                        source.LocationX = 0;
                        source.LocationY = 250;

                        _width = 100;
                        _height = 320;
                    }
                    break;
                default:
                    {
                        var switchModel = _nodes.First(d => d.NodeType == NodeType.Switch);
                        switchModel.LocationX = 20;
                        switchModel.LocationY = 0;
                        var label = _nodes.First(d => d.NodeType == NodeType.Label);
                        label.LocationX = 30;
                        label.LocationY = -100;
                        var dataPoint = _nodes.First(d => d.NodeType == NodeType.DataPoint);
                        dataPoint.LocationX = 30;
                        dataPoint.LocationY = -55;
                        var meterModel = _nodes.First(d => d.NodeType == NodeType.Meter);
                        meterModel.LocationX = 120;
                        meterModel.LocationY = 0;
                        var source = _nodes.First(d => d.NodeType == NodeType.Outlet);
                        source.LocationX = 220;
                        source.LocationY = 0;

                        _height = 150;
                        _width = 350;
                    }
                    break;
            }
        }

        public override void Rotation(int action = -1)
        {
            bool needCalculate = false;
            switch (action)
            {
                // 90度翻转
                case -1:
                    {
                        if (Direction == 0)
                        {
                            Direction = 1;
                            needCalculate = true;
                        }
                        else if (Direction == 1)
                        {
                            Direction = 0;
                            needCalculate = true;
                        }
                        break;
                    }
                // 强制横排
                case 0:
                    {
                        if (Direction != 0)
                        {
                            Direction = 0;
                            needCalculate = true;
                        }
                        break;
                    }
                // 强制纵排
                case 1:
                    {
                        if (Direction != 1)
                        {
                            Direction = 1;
                            needCalculate = true;
                        }
                        break;
                    }
                default: break;
            }

            if (needCalculate)
            {
                RotationNodes(_nodes);
                CalculateLocation();
            }
        }

        public override int Height => _height;
        public override int Width => _width;
        public override NodeData[] NodeDatas => _nodes.ToArray();
        public override LineData[] LineDatas => _lineDatas.ToArray();
        public override LineData[] Connectors => _lineDatas.Where(l => l.IsConnector).ToArray();
    }
}
