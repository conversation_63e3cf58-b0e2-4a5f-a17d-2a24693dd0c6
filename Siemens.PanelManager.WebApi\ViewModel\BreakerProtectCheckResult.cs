﻿using Siemens.PanelManager.Server.BreakerProtect;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class BreakerProtectCheckResult
    {
        public List<int> NotCheckPassAssetIds { get; set; } = new List<int>();
        public List<ProtectSingleCheck> ProtectChecks = new List<ProtectSingleCheck>();
    }

    public class ProtectSingleCheck
    {
        public int AssetId { get; set; }
        public string BusBarId { get; set; } = string.Empty;
        public bool CheckPass { get; set; }
        public string[] AbnormalProtection { get; set; } = new string[0];
        public List<SingleChart> Charts { get; set; } = new List<SingleChart>();
    }

    public class SingleChart
    {
        public int UpAssetId { get; set; }
        public string UpCircuitName { get; set; } = string.Empty;
        public string UpModel { get; set; } = string.Empty;
        public List<string> UpProtectSettings { get; set; } = new List<string>();
        public int LowerAssetId { get; set; }
        public string LowerCircuitName { get; set; } = string.Empty;
        public string LowerModel { get; set; } = string.Empty;
        public List<string> LowerProtectSettings { get; set; } = new List<string>();
        public decimal[][] UpXY { get; set; } = new decimal[0][];
        public decimal[][] LowerXY { get; set; } = new decimal[0][];
        //public decimal[] X { get; set; } = new decimal[0];
        //public decimal[] UpY { get; set; } = new decimal[0];
        //public decimal[] LowerY { get; set; } = new decimal[0];
        public decimal[] CrossSection { get; set; } = new decimal[0];
    }
}
