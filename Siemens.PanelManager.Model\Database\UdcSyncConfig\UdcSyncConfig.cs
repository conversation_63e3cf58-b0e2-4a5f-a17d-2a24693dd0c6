﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.UdcSyncConfig
{
    [SugarTable("udc_sync_config")]
    public class UdcSyncConfig : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [SugarColumn(ColumnName = "name", IsNullable = false, Length = 256)]
        public string Name { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "value", IsNullable = false, Length = 256)]
        public string Value { get; set; } = string.Empty;
    }
}
