﻿using MiniExcelLibs.Attributes;
using Siemens.PanelManager.Model.Database.Asset;

namespace Siemens.PanelManager.Model.MeterRead
{
    /// <summary>
    /// 抄表入参
    /// </summary>
    public class MeterReadParam
    {

        /// <summary>
        ///  回路名称集合
        /// </summary>
        public List<int> CircuitIdList { get; set; } = new List<int>();

        /// <summary>
        /// 属性集合
        /// </summary>
        public List<string> PropertyList { get; set; } = new List<string>();

        /// <summary>
        ///  下载excel的指定日期
        /// </summary>
        public string? Date { get; set; }

        /// <summary>
        /// 配电房
        /// </summary>
        public string? Substation { get; set; }

        /// <summary>
        /// 回路名称
        /// </summary>
        public string? FilterCircuitName { get; set; }

        /// <summary>
        /// 页码大小
        /// </summary>
        public int PageSize { get; set; } = 0;

        /// <summary>
        /// 软加载最后一行的行号
        /// </summary>
        public int LastRowNumber { get; set; } = 0;
    }

    /// <summary>
    ///  抄表返回值
    /// </summary>
    public class MeterReadResult
    {
        /// <summary>
        /// 总条数
        /// </summary>
        public int TotalSize { get; set; } = 0;

        /// <summary>
        /// 数据集合
        /// </summary>
        public List<MeterReadByDto> MeterReadList { get; set; } = new List<MeterReadByDto>();
    }

    /// <summary>
    ///  抄表返回零时值
    /// </summary>
    public class MeterReadByDto
    {
        /// <summary>
        /// 回路id
        /// </summary>
        public int CircuitId { get; set; }

        /// <summary>
        /// 回路名称
        /// </summary>
        public string? CircuitName { get; set; }

        /// <summary>
        /// 属性名称
        /// </summary>
        public string? PropertyName { get; set; }

        /// <summary>
        /// 实时值
        /// </summary>
        public decimal RealTimeValue { get; set; }

        /// <summary>
        /// 排序字段
        /// </summary>
        public int SortNum { get; set; }

        /// <summary>
        /// 时刻0
        /// </summary>
        public decimal Val0 { get; set; }

        /// <summary>
        /// 时刻1
        /// </summary>
        public decimal Val1 { get; set; }

        /// <summary>
        /// 时刻2
        /// </summary>
        public decimal Val2 { get; set; }

        /// <summary>
        /// 时刻3
        /// </summary>
        public decimal Val3 { get; set; }

        /// <summary>
        /// 时刻4
        /// </summary>
        public decimal Val4 { get; set; }

        /// <summary>
        /// 时刻5
        /// </summary>
        public decimal Val5 { get; set; }

        /// <summary>
        /// 时刻6
        /// </summary>
        public decimal Val6 { get; set; }

        /// <summary>
        /// 时刻7
        /// </summary>
        public decimal Val7 { get; set; }

        /// <summary>
        /// 时刻8
        /// </summary>
        public decimal Val8 { get; set; }

        /// <summary>
        /// 时刻9
        /// </summary>
        public decimal Val9 { get; set; }

        /// <summary>
        /// 时刻10
        /// </summary>
        public decimal Val10 { get; set; }

        /// <summary>
        /// 时刻11
        /// </summary>
        public decimal Val11 { get; set; }

        /// <summary>
        /// 时刻12
        /// </summary>
        public decimal Val12 { get; set; }

        /// <summary>
        /// 时刻13
        /// </summary>
        public decimal Val13 { get; set; }

        /// <summary>
        /// 时刻14
        /// </summary>
        public decimal Val14 { get; set; }

        /// <summary>
        /// 时刻15
        /// </summary>
        public decimal Val15 { get; set; }

        /// <summary>
        /// 时刻16
        /// </summary>
        public decimal Val16 { get; set; }

        /// <summary>
        /// 时刻17
        /// </summary>
        public decimal Val17 { get; set; }

        /// <summary>
        /// 时刻18
        /// </summary>
        public decimal Val18 { get; set; }

        /// <summary>
        /// 时刻19
        /// </summary>
        public decimal Val19 { get; set; }

        /// <summary>
        /// 时刻20
        /// </summary>
        public decimal Val20 { get; set; }

        /// <summary>
        /// 时刻21
        /// </summary>
        public decimal Val21 { get; set; }

        /// <summary>
        /// 时刻22
        /// </summary>
        public decimal Val22 { get; set; }

        /// <summary>
        /// 时刻23
        /// </summary>
        public decimal Val23 { get; set; }
    }

    /// <summary>
    ///  抄表实时返回值
    /// </summary>
    public class RealMeterReadResult
    {
        /// <summary>
        /// 回路id
        /// </summary>
        public int CircuitId { get; set; }

        /// <summary>
        /// 回路名称
        /// </summary>
        public string? CircuitName { get; set; }

        /// <summary>
        /// 属性名称
        /// </summary>
        public string? PropertyName { get; set; }

        /// <summary>
        /// 实时值
        /// </summary>
        public decimal RealTimeValue { get; set; }

    }

    /// <summary>
    ///  抄表实时返回值
    /// </summary>
    public class MeterReadDto
    {
        /// <summary>
        /// 回路id
        /// </summary>
        public int CircuitId { get; set; }

        /// <summary>
        ///  回路名称
        /// </summary>
        public string? CircuitName { get; set; }

        /// <summary>
        /// 资产设备id
        /// </summary>
        public List<int> AssetIds { get; set; } = new List<int>();

    }

    /// <summary>
    ///  导出excel实体
    /// </summary>
    public class MeterReadExcelDto
    {
        /// <summary>
        /// 回路名称
        /// </summary>
        [ExcelColumnName("回路名称")]
        public string? CircuitName { get; set; }

        /// <summary>
        /// 属性名称
        /// </summary>
        [ExcelColumnName("属性")]
        public string? PropertyName { get; set; }

        /// <summary>
        /// 实时值
        /// </summary>
        [ExcelColumnName("实时值")]
        public decimal RealTimeValue { get; set; }

        /// <summary>
        /// 时刻0
        /// </summary>
        [ExcelColumnName("0:00")]
        public decimal Val0 { get; set; }

        /// <summary>
        /// 时刻1
        /// </summary>
        [ExcelColumnName("1:00")]
        public decimal Val1 { get; set; }

        /// <summary>
        /// 时刻2
        /// </summary>
        [ExcelColumnName("2:00")]
        public decimal Val2 { get; set; }

        /// <summary>
        /// 时刻3
        /// </summary>
        [ExcelColumnName("3:00")]
        public decimal Val3 { get; set; }

        /// <summary>
        /// 时刻4
        /// </summary>
        [ExcelColumnName("4:00")]
        public decimal Val4 { get; set; }

        /// <summary>
        /// 时刻5
        /// </summary>
        [ExcelColumnName("5:00")]
        public decimal Val5 { get; set; }

        /// <summary>
        /// 时刻6
        /// </summary>
        [ExcelColumnName("6:00")]
        public decimal Val6 { get; set; }

        /// <summary>
        /// 时刻7
        /// </summary>
        [ExcelColumnName("7:00")]
        public decimal Val7 { get; set; }

        /// <summary>
        /// 时刻8
        /// </summary>
        [ExcelColumnName("8:00")]
        public decimal Val8 { get; set; }

        /// <summary>
        /// 时刻9
        /// </summary>
        [ExcelColumnName("9:00")]
        public decimal Val9 { get; set; }

        /// <summary>
        /// 时刻10
        /// </summary>
        [ExcelColumnName("10:00")]
        public decimal Val10 { get; set; }

        /// <summary>
        /// 时刻11
        /// </summary>
        [ExcelColumnName("11:00")]
        public decimal Val11 { get; set; }

        /// <summary>
        /// 时刻12
        /// </summary>
        [ExcelColumnName("12:00")]
        public decimal Val12 { get; set; }

        /// <summary>
        /// 时刻13
        /// </summary>
        [ExcelColumnName("13:00")]
        public decimal Val13 { get; set; }

        /// <summary>
        /// 时刻14
        /// </summary>
        [ExcelColumnName("14:00")]
        public decimal Val14 { get; set; }

        /// <summary>
        /// 时刻15
        /// </summary>
        [ExcelColumnName("15:00")]
        public decimal Val15 { get; set; }

        /// <summary>
        /// 时刻16
        /// </summary>
        [ExcelColumnName("16:00")]
        public decimal Val16 { get; set; }

        /// <summary>
        /// 时刻17
        /// </summary>
        [ExcelColumnName("17:00")]
        public decimal Val17 { get; set; }

        /// <summary>
        /// 时刻18
        /// </summary>
        [ExcelColumnName("18:00")]
        public decimal Val18 { get; set; }

        /// <summary>
        /// 时刻19
        /// </summary>
        [ExcelColumnName("19:00")]
        public decimal Val19 { get; set; }

        /// <summary>
        /// 时刻20
        /// </summary>
        [ExcelColumnName("20:00")]
        public decimal Val20 { get; set; }

        /// <summary>
        /// 时刻21
        /// </summary>
        [ExcelColumnName("21:00")]
        public decimal Val21 { get; set; }

        /// <summary>
        /// 时刻22
        /// </summary>
        [ExcelColumnName("22:00")]
        public decimal Val22 { get; set; }

        /// <summary>
        /// 时刻23
        /// </summary>
        [ExcelColumnName("23:00")]
        public decimal Val23 { get; set; }
    }


    public class AssetByInfoDto
    {
        /// <summary>
        /// 资产id
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 父级id
        /// </summary>
        public int ParentId { get; set; }

        /// <summary>
        ///  资产名称
        /// </summary>
        public string? AssetName { get; set; }

        /// <summary>
        /// 级别
        /// </summary>
        public AssetLevel AssetLevel { get; set; }
    }
}
