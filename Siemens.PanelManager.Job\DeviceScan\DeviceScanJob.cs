using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.Common.UdcService;
using Siemens.PanelManager.Interface.Job;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.UdcScanData;
using Siemens.PanelManager.Model.Job;
using Siemens.PanelManager.Model.UDC;
using Siemens.PanelManager.Server.Asset;
using Siemens.PanelManager.Server.DataPoint;
using Siemens.PanelManager.Job.ModbusDeviceWorker;
using SqlSugar;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.Job.DeviceScan
{
    public class DeviceScanJob : JobBase
    {
        private readonly ILogger<DeviceScanJob> _logger;
        private readonly IConfiguration _configuration;
        private readonly ISqlSugarClient _client;
        private readonly SiemensCache _cache;
        private readonly UdcHttpService _udcHttpService;
        private readonly DataPointServer _dataPointServer;
        private readonly IServiceProvider _provider;

        private string _jobId = string.Empty;
        private string _interfaceName = string.Empty;
        private string _parentIp = string.Empty;
        private string _userName = "System";

        private readonly Dictionary<string, string> _panelAssetTypes = new Dictionary<string, string>
        {
            { "PAC1020", "Meter" },
            { "PAC3120", "Meter" },
            { "PAC3220", "Meter" },
            { "PAC4200", "Meter" },
            { "PAC3200", "Meter" },
            { "3VA", "MCCB" },
            { "3WL", "ACB" },
            { "3WA", "ACB" },
            { "COM800", "Gateway" }
        };

        private readonly Dictionary<string, string> _udcConfig = new Dictionary<string, string>
        {
            {"TimeZone","Asia/Shanghai" },
            {"MqttServiceStartType","1" },
            {"MqttBroker","**********" },
            {"MqttPort","1883" },
            {"MqttTlsEnabled","0" },
            {"MqttClientId","paneludc" },
            {"MqttPublishValues","topic_1" },
        };

        public override string Name => "DeviceScanJob";

        public DeviceScanJob(ILogger<DeviceScanJob> logger,
            IServiceProvider provider,
            IConfiguration configuration,
            ISqlSugarClient client,
            SiemensCache cache,
            UdcHttpService udcHttpService,
            DataPointServer dataPointServer)
        {
            _logger = logger;
            _configuration = configuration;
            _client = client;
            _cache = cache;
            _udcHttpService = udcHttpService;
            _dataPointServer = dataPointServer;
            _provider = provider;
        }

        public override async Task Execute()
        {
            if (!ContextData.ContainsKey("JobId"))
            {
                _logger.LogInformation($"{Name} 启动失败, 参数缺失");
                return;
            }

            _jobId = ContextData["JobId"];
            _interfaceName = ContextData["InterfaceName"]?.ToUpper() ?? "X2P1";
            _parentIp = ContextData["ParentIp"];
            _userName = ContextData["UserName"];

            var jobInfo = _cache.Get<JobInfo>($"JobId:{_jobId}");
            if (jobInfo == null)
            {
                _logger.LogInformation($"{Name}:{_jobId} 已过期");
                return;
            }

            if (string.IsNullOrWhiteSpace(_parentIp))
            {
                await ScanAllDeviceAsync(jobInfo);

                var panelModbusWorker = _provider.GetRequiredService<PanelModbusWorker>();
                await panelModbusWorker.StopAsync(CancellationToken.None);
                await Task.Delay(5000);
                await panelModbusWorker.StartAsync(CancellationToken.None);
            }
            else
            {
                await ScanGatewayAsync(jobInfo);

                var panelModbusWorker = _provider.GetRequiredService<PanelModbusWorker>();
                await panelModbusWorker.StopAsync(CancellationToken.None);
                await Task.Delay(5000);
                await panelModbusWorker.StartAsync(CancellationToken.None);
            }
        }

        private async Task ScanAllDeviceAsync(JobInfo jobInfo)
        {
            jobInfo.Result = new JobResultModel();
            jobInfo.JobStatus = 10;
            _cache.Set($"JobId:{_jobId}", jobInfo, TimeSpan.FromMinutes(5));

            string udcDeviceId = string.Empty;
            string udcJobId = string.Empty;

            udcDeviceId = await _udcHttpService.GetUdcProjectItemIdAsync();

            if (!string.IsNullOrWhiteSpace(udcDeviceId))
            {
                try
                {
                    await _udcHttpService.ClearProject();

                    // 检查udc 配置并修改错误的配置
                    await ChangeUdcConfig(udcDeviceId);

                    // 取消当前udc的扫描任务
                    await _udcHttpService.CancelUdcScanJobAsync(udcDeviceId);

                    DateTime startTime = DateTime.Now;
                    int getResultCount = 1;

                    // 新建扫描任务并获取udc job id
                    udcJobId = await _udcHttpService.CreateUdcScanJobAndReturnIdAsync(udcDeviceId, _interfaceName.ToUpper() == "X2P1" ? "internal" : "external");

                    if (!string.IsNullOrWhiteSpace(udcJobId))
                    {
                        List<UdcScanResult> udcScanResults = new List<UdcScanResult>();
                        UdcScanProcess? scanProcess = null;
                        while (startTime.AddMinutes(2) >= DateTime.Now)
                        {
                            // 获取扫描的进度信息
                            scanProcess = await _udcHttpService.GetUdcScanProcessStatusAsync(udcDeviceId, udcJobId);

                            // 扫描状态完成，终止整个循环
                            if (scanProcess != null && scanProcess.Status?.Progress == 100)
                            {
                                break;
                            }

                            // "ResultFrom"字段有值时代表第一次有结果返回
                            if (scanProcess != null && (scanProcess.Status?.ResultFrom.HasValue ?? false) && getResultCount <= 1)
                            {
                                // 获取结果
                                udcScanResults = await _udcHttpService.GetUdcScanResultAsync(udcDeviceId, udcJobId);
                                jobInfo.JobStatus = 10;
                                _cache.Set($"JobId:{_jobId}", jobInfo, TimeSpan.FromMinutes(5));

                                getResultCount++;
                            }

                            await Task.Delay(2000);
                        }

                        udcScanResults = await _udcHttpService.GetUdcScanResultAsync(udcDeviceId, udcJobId);

                        List<UdcScanResult> gwScanResults = new List<UdcScanResult>();
                        UdcScanRequestOptions opts = new UdcScanRequestOptions
                        {
                            Address = string.Empty,
                            TypeName = string.Empty,
                            Result = new UdcScanRequestResult
                            {
                                Format = "json"
                            }
                        };

                        // 执行网关设备扫描
                        var tempGwDevices = udcScanResults.Where(a => a.IsGateway).ToList();
                        foreach (var item in tempGwDevices)
                        {
                            opts.Address = $"{item.IpAddress}:{item.Port}";
                            opts.TypeName = item.TypeName;
                            var gwJobId = await _udcHttpService.CreateUdcScanJobAndReturnIdAsync(udcDeviceId, _interfaceName.ToUpper() == "X2P1" ? "internal" : "external", UdcScanType.Scan_Gw, opts);
                            DateTime gwDateTime = DateTime.Now;

                            while (gwDateTime.AddSeconds(30) >= DateTime.Now)
                            {
                                // 获取扫描的进度信息
                                scanProcess = await _udcHttpService.GetUdcScanProcessStatusAsync(udcDeviceId, gwJobId);
                                // 扫描状态完成，终止整个循环
                                if (scanProcess != null && scanProcess.Status?.Progress == 100)
                                {
                                    break;
                                }

                                await Task.Delay(2000);
                            }

                            var tempGwScans = await _udcHttpService.GetUdcScanResultAsync(udcDeviceId, gwJobId);

                            if (scanProcess != null && scanProcess.Status?.Progress != 100)
                            {
                                await _udcHttpService.CancelUdcScanSingleJobAsync(udcDeviceId, gwJobId);
                            }

                            if (tempGwScans != null && tempGwScans.Any())
                            {
                                udcScanResults.AddRange(tempGwScans);
                            }

                            jobInfo.JobStatus = 10;
                            _cache.Set($"JobId:{_jobId}", jobInfo, TimeSpan.FromMinutes(5));
                        }

                        // 自动绑定设备
                        var assets = await _client.Queryable<AssetInfo>()
                            .Where(a => a.AssetLevel == AssetLevel.Device)
                            .ToListAsync();

                        var assetAndUdcDevices = (from a in assets
                                                  from u in udcScanResults
                                                  where a.AssetModel == u.TypeName
                                                  && a.IPAddress == (u.UnitId.HasValue ? $"{u.IpAddress}/{u.UnitId}" : u.IpAddress)
                                                  && (string.IsNullOrWhiteSpace(u.ItemId) || a.ObjectId != u.ItemId)
                                                  select new
                                                  {
                                                      a.Id,
                                                      a.ObjectId,
                                                      a.AssetModel,
                                                      UdcDevice = u
                                                  }).ToList();

                        List<AssetInfo> needUpdates = new List<AssetInfo>();
                        UdcAddDevice udcAddDevice = new UdcAddDevice();

                        foreach (var asset in assetAndUdcDevices)
                        {
                            if ((asset.UdcDevice.UnitId == null || asset.UdcDevice.UnitId.Value <= 0) && !asset.UdcDevice.IsGateway)
                            {
                                //判断是不是普通设备，普通设备如果没添加则直接添加
                                if (string.IsNullOrWhiteSpace(asset.UdcDevice.ItemId))
                                {
                                    udcAddDevice = new UdcAddDevice
                                    {
                                        Address = asset.UdcDevice.IpAddress,
                                        Name = $"{asset.UdcDevice.TypeName} {asset.UdcDevice.IpAddress}",
                                        TypeName = asset.UdcDevice.TypeName,
                                    };
                                    var udcNewAddResult = await _udcHttpService.AddDeviceToUdcProjectAsync(udcAddDevice);
                                    if (udcNewAddResult != null)
                                    {
                                        needUpdates.Add(new AssetInfo { Id = asset.Id, ObjectId = udcNewAddResult.Id });
                                    }
                                }
                                else
                                {
                                    needUpdates.Add(new AssetInfo { Id = asset.Id, ObjectId = asset.UdcDevice.ItemId });
                                }
                            }
                            else
                            {
                                UdcAddDeviceResult? udcNewAddResult;
                                var tempGwDevice = tempGwDevices.FirstOrDefault(a => a.IpAddress == asset.UdcDevice.IpAddress);
                                if (tempGwDevice != null)
                                {
                                    if (string.IsNullOrWhiteSpace(tempGwDevice.ItemId))
                                    {
                                        // 添加网关设备
                                        udcAddDevice = new UdcAddDevice
                                        {
                                            Address = tempGwDevice.IpAddress,
                                            Name = $"{tempGwDevice.TypeName} {tempGwDevice.IpAddress}",
                                            TypeName = tempGwDevice.TypeName,
                                        };
                                        udcNewAddResult = await _udcHttpService.AddDeviceToUdcProjectAsync(udcAddDevice);
                                        tempGwDevice.ItemId = udcNewAddResult?.Id;
                                        if (udcNewAddResult != null && asset.AssetModel == tempGwDevice.TypeName)
                                        {
                                            needUpdates.Add(new AssetInfo { Id = asset.Id, ObjectId = udcNewAddResult.Id });
                                        }
                                    }

                                    if (!asset.UdcDevice.IsGateway)
                                    {
                                        if (string.IsNullOrWhiteSpace(asset.UdcDevice.ItemId))
                                        {
                                            // 添加网关关联设备
                                            udcAddDevice = new UdcAddDevice
                                            {
                                                Address = $"{asset.UdcDevice.IpAddress}/{asset.UdcDevice.UnitId}",
                                                Name = $"{asset.UdcDevice.TypeName} {asset.UdcDevice.IpAddress}/{asset.UdcDevice.UnitId}",
                                                TypeName = asset.UdcDevice.TypeName,
                                                ParentId = tempGwDevice.ItemId,
                                                GwParentId = tempGwDevice.ItemId
                                            };
                                            udcNewAddResult = await _udcHttpService.AddDeviceToUdcProjectAsync(udcAddDevice);
                                            if (udcNewAddResult != null)
                                            {
                                                needUpdates.Add(new AssetInfo { Id = asset.Id, ObjectId = udcNewAddResult.Id });
                                            }
                                        }
                                        else
                                        {
                                            needUpdates.Add(new AssetInfo { Id = asset.Id, ObjectId = asset.UdcDevice.ItemId });
                                        }
                                    }
                                    else
                                    {
                                        needUpdates.Add(new AssetInfo { Id = asset.Id, ObjectId = tempGwDevice.ItemId });
                                    }
                                }
                            }

                            jobInfo.JobStatus = 10;
                            _cache.Set($"JobId:{_jobId}", jobInfo, TimeSpan.FromMinutes(5));
                        }

                        // 更新数据库绑定关系
                        if (needUpdates.Any())
                        {
                            await _client.Updateable(needUpdates).UpdateColumns(a => a.ObjectId).ExecuteCommandAsync();
                        }

                        var allPanelDevices = await _client.Queryable<AssetInfo>()
                            .Where(a => a.AssetLevel == AssetLevel.Device)
                            .Select(a => new UdcScanData
                            {
                                AssetId = a.Id,
                                ObjectId = a.ObjectId,
                                AssetIpAddress = a.IPAddress,
                                AssetName = a.AssetName,
                                AssetModel = a.AssetModel,
                                AssetNumber = a.AssetNumber,
                                AssetType = a.AssetType,
                            })
                            .ToListAsync();

                        var notInPanelDevices = (from u in udcScanResults
                                                 join p in allPanelDevices on new { IpAddress = u.UnitId.HasValue ? $"{u.IpAddress}/{u.UnitId.Value}" : u.IpAddress, u.TypeName } equals new { IpAddress = p.AssetIpAddress, TypeName = p.AssetModel } into pJoin
                                                 from j in pJoin.DefaultIfEmpty()
                                                 where j == null
                                                 select new UdcScanData
                                                 {
                                                     AssetId = 0,
                                                     TypeDisplayName = u.TypeName,
                                                     TypeName = u.TypeName,
                                                     BootloaderVersion = u.BootloaderVersion,
                                                     FirmwareVersion = u.FirmwareVersion,
                                                     Gateway = u.Gateway,
                                                     IpAddress = u.IpAddress,
                                                     Port = u.Port,
                                                     PlantIdentifier = u.PlantIdentifier,
                                                     MacAddress = u.MacAddress,
                                                     ItemId = u.ItemId,
                                                     Netmask = u.Netmask,
                                                     IsGateway = u.IsGateway,
                                                     UnitId = u.UnitId,
                                                     OrderNumber = u.OrderNumber,
                                                     AssetType = _panelAssetTypes.GetValueOrDefault(u.TypeName ?? string.Empty),
                                                 }).ToList();

                        var inPanelDevices = (from p in allPanelDevices
                                              join u in udcScanResults on new { IpAddress = p.AssetIpAddress, TypeName = p.AssetModel } equals new { IpAddress = u.UnitId.HasValue ? $"{u.IpAddress}/{u.UnitId.Value}" : u.IpAddress, u.TypeName } into uJoin
                                              from j in uJoin.DefaultIfEmpty()
                                              select new UdcScanData
                                              {
                                                  AssetId = p.AssetId,
                                                  ObjectId = p.ObjectId,
                                                  AssetIpAddress = p.AssetIpAddress,
                                                  AssetName = p.AssetName,
                                                  AssetModel = p.AssetModel,
                                                  AssetNumber = p.AssetNumber,
                                                  AssetType = p.AssetType,
                                                  TypeDisplayName = j?.TypeName,
                                                  TypeName = j?.TypeName,
                                                  BootloaderVersion = j?.BootloaderVersion,
                                                  FirmwareVersion = j?.FirmwareVersion,
                                                  Gateway = j?.Gateway,
                                                  IpAddress = j?.IpAddress,
                                                  Port = j?.Port,
                                                  PlantIdentifier = j?.PlantIdentifier,
                                                  MacAddress = j?.MacAddress,
                                                  ItemId = j?.ItemId,
                                                  Netmask = j?.Netmask,
                                                  IsGateway = j?.IsGateway ?? false,
                                                  UnitId = j?.UnitId,
                                                  OrderNumber = j?.OrderNumber,
                                              }).ToList();

                        var finalData = inPanelDevices.Union(notInPanelDevices).ToList();

                        if (finalData.Any())
                        {
                            finalData.ForEach(a =>
                            {
                                if (!string.IsNullOrWhiteSpace(a.ObjectId))
                                {
                                    a.ImportStatus = DeviceImportStatus.Imported.GetHashCode();
                                }
                                else if (!a.AssetId.HasValue || a.AssetId.Value <= 0)
                                {
                                    a.ImportStatus = DeviceImportStatus.NotImported.GetHashCode();
                                }
                                else
                                {
                                    a.ImportStatus = DeviceImportStatus.NotFound.GetHashCode();
                                }
                                a.InterfaceName = _interfaceName;
                                a.CreatedTime = DateTime.Now;
                                a.UpdatedTime = DateTime.Now;
                            });

                            await _client.Deleteable<UdcScanData>().Where(a => a.InterfaceName == _interfaceName).ExecuteCommandAsync();
                            await _client.Insertable(finalData).ExecuteCommandAsync();
                        }

                        await ChangeSplxFileDataPoints();

                        await GeneralModbusDeviceScan();

                        var service = _provider.GetRequiredService<DeviceInfoService>();
                        await service.InitDeviceInfoes(_logger);

                        jobInfo.JobStatus = 20;
                        _cache.Set($"JobId:{_jobId}", jobInfo, TimeSpan.FromMinutes(5));
                        _cache.Clear("ScanJobId");
                        return;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "DeviceScanJob ScanAllDeviceAsync error.");
                    _cache.Clear("ScanJobId");
                }
            }
            else
            {
                try
                {
                    var allPanelDevices = await _client.Queryable<AssetInfo>().Where(a => a.AssetLevel == AssetLevel.Device).Select(a => new UdcScanData
                    {
                        AssetId = a.Id,
                        ObjectId = a.ObjectId,
                        AssetIpAddress = a.IPAddress,
                        AssetName = a.AssetName,
                        AssetType = a.AssetType,
                        AssetModel = a.AssetModel,
                    }).ToListAsync();


                    if (allPanelDevices.Any())
                    {
                        allPanelDevices.ForEach(a =>
                        {
                            if (!string.IsNullOrWhiteSpace(a.ObjectId))
                            {
                                a.ImportStatus = DeviceImportStatus.Imported.GetHashCode();
                            }
                            else if (!a.AssetId.HasValue || a.AssetId.Value <= 0)
                            {
                                a.ImportStatus = DeviceImportStatus.NotImported.GetHashCode();
                            }
                            else
                            {
                                a.ImportStatus = DeviceImportStatus.NotFound.GetHashCode();
                            }
                            a.InterfaceName = _interfaceName;
                            a.CreatedTime = DateTime.Now;
                            a.UpdatedTime = DateTime.Now;
                        });

                        await _client.Deleteable<UdcScanData>().Where(a => a.InterfaceName == _interfaceName).ExecuteCommandAsync();
                        await _client.Insertable(allPanelDevices).ExecuteCommandAsync();
                    }

                    await GeneralModbusDeviceScan();

                    jobInfo.JobStatus = 20;
                    _cache.Set($"JobId:{_jobId}", jobInfo, TimeSpan.FromMinutes(5));
                    _cache.Clear("ScanJobId");
                    return;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "DeviceScanJob ScanGatewayAsync error.");
                    _cache.Clear("ScanJobId");
                }
            }

            jobInfo.JobStatus = 99;
            jobInfo.Result.ErrorInfo.Add("Common_Param");
            _cache.Set($"JobId:{_jobId}", jobInfo, TimeSpan.FromMinutes(5));
            _cache.Clear("ScanJobId");
        }

        private async Task ScanGatewayAsync(JobInfo jobInfo)
        {
            jobInfo.Result = new JobResultModel();
            jobInfo.JobStatus = 10;
            _cache.Set($"JobId:{_jobId}", jobInfo, TimeSpan.FromMinutes(5));

            string udcDeviceId = string.Empty;
            udcDeviceId = await _udcHttpService.GetUdcProjectItemIdAsync();

            if (!string.IsNullOrWhiteSpace(udcDeviceId))
            {
                try
                {
                    await _udcHttpService.CancelUdcScanJobAsync(udcDeviceId);

                    List<UdcScanResult> gwScanResults = new List<UdcScanResult>();
                    UdcScanRequestOptions opts = new UdcScanRequestOptions
                    {
                        Address = string.Empty,
                        TypeName = string.Empty,
                        Result = new UdcScanRequestResult
                        {
                            Format = "json"
                        }
                    };
                    UdcScanProcess? scanProcess = null;

                    // 执行网关设备扫描
                    var tempGwDevice = await _client.Queryable<UdcScanData>().Where(a => a.IpAddress == _parentIp && a.IsGateway).FirstAsync();

                    opts.Address = $"{tempGwDevice.IpAddress}:{tempGwDevice.Port}";
                    opts.TypeName = tempGwDevice.TypeName;
                    var gwJobId = await _udcHttpService.CreateUdcScanJobAndReturnIdAsync(udcDeviceId, _interfaceName.ToUpper() == "X2P1" ? "internal" : "external", UdcScanType.Scan_Gw, opts);
                    DateTime gwDateTime = DateTime.Now;

                    // 真实环境测试PAC4200作为网关时，其扫描下级设备用时较长，此处做特殊处理
                    int scanSecond = tempGwDevice.TypeName == "PAC4200" ? 120 : 30;
                    while (gwDateTime.AddSeconds(scanSecond) >= DateTime.Now)
                    {
                        // 获取扫描的进度信息
                        scanProcess = await _udcHttpService.GetUdcScanProcessStatusAsync(udcDeviceId, gwJobId);
                        // 扫描状态完成，终止整个循环
                        if (scanProcess != null && scanProcess.Status?.Progress == 100)
                        {
                            break;
                        }

                        await Task.Delay(2000);
                    }

                    var tempGwScans = await _udcHttpService.GetUdcScanResultAsync(udcDeviceId, gwJobId);

                    if (scanProcess != null && scanProcess.Status?.Progress != 100)
                    {
                        await _udcHttpService.CancelUdcScanSingleJobAsync(udcDeviceId, gwJobId);
                    }

                    if (tempGwScans != null && tempGwScans.Any())
                    {
                        gwScanResults.AddRange(tempGwScans);
                    }

                    jobInfo.JobStatus = 10;
                    _cache.Set($"JobId:{_jobId}", jobInfo, TimeSpan.FromMinutes(5));

                    // 自动绑定设备
                    var assets = await _client.Queryable<AssetInfo>()
                        .Where(a =>
                            a.AssetLevel == AssetLevel.Device
                            && a.AssetType != "Gateway"
                            && a.ObjectId != tempGwDevice.ObjectId
                            && SqlFunc.Contains(a.IPAddress, tempGwDevice.IpAddress)
                            )
                        .ToListAsync();

                    var assetAndUdcDevices = (from a in assets
                                              from u in gwScanResults
                                              where a.AssetModel == u.TypeName
                                              && a.IPAddress == (u.UnitId.HasValue ? $"{u.IpAddress}/{u.UnitId.Value}" : u.IpAddress)
                                              && (string.IsNullOrWhiteSpace(u.ItemId) || a.ObjectId != u.ItemId)
                                              select new
                                              {
                                                  a.Id,
                                                  a.ObjectId,
                                                  UdcDevice = u
                                              }).ToList();

                    List<AssetInfo> needUpdates = new List<AssetInfo>();
                    UdcAddDevice udcAddDevice = new UdcAddDevice();

                    foreach (var asset in assetAndUdcDevices)
                    {
                        if (tempGwDevice != null)
                        {
                            if (string.IsNullOrWhiteSpace(tempGwDevice.ItemId))
                            {
                                // 添加网关设备
                                udcAddDevice = new UdcAddDevice
                                {
                                    Address = tempGwDevice.IpAddress,
                                    Name = $"{tempGwDevice.TypeName} {tempGwDevice.IpAddress}",
                                    TypeName = tempGwDevice.TypeName,
                                };
                                var udcNewAddResult = await _udcHttpService.AddDeviceToUdcProjectAsync(udcAddDevice);
                                tempGwDevice.ItemId = udcNewAddResult?.Id;
                            }

                            if (string.IsNullOrWhiteSpace(asset.UdcDevice.ItemId))
                            {
                                // 添加网关关联设备
                                udcAddDevice = new UdcAddDevice
                                {
                                    Address = $"{asset.UdcDevice.IpAddress}/{asset.UdcDevice.UnitId}",
                                    Name = $"{asset.UdcDevice.TypeName} {asset.UdcDevice.IpAddress}/{asset.UdcDevice.UnitId}",
                                    TypeName = asset.UdcDevice.TypeName,
                                    ParentId = tempGwDevice.ItemId,
                                    GwParentId = tempGwDevice.ItemId
                                };
                                var udcNewAddResult = await _udcHttpService.AddDeviceToUdcProjectAsync(udcAddDevice);
                                if (udcNewAddResult != null)
                                {
                                    needUpdates.Add(new AssetInfo { Id = asset.Id, ObjectId = udcNewAddResult.Id });
                                }
                            }
                            else
                            {
                                needUpdates.Add(new AssetInfo { Id = asset.Id, ObjectId = asset.UdcDevice.ItemId });
                            }
                        }
                    }

                    // 更新数据库绑定关系
                    if (needUpdates.Any())
                    {
                        await _client.Updateable(needUpdates).UpdateColumns(a => a.ObjectId).ExecuteCommandAsync();
                    }

                    var allPanelDevices = await _client.Queryable<AssetInfo>()
                        .Where(a => a.AssetLevel == AssetLevel.Device && a.AssetType != "Gateway" && a.ObjectId != tempGwDevice.ObjectId && SqlFunc.Contains(a.IPAddress, tempGwDevice.IpAddress))
                        .Select(a => new UdcScanData
                        {
                            AssetId = a.Id,
                            ObjectId = a.ObjectId,
                            AssetIpAddress = a.IPAddress,
                            AssetName = a.AssetName,
                            AssetType = a.AssetType,
                            AssetNumber = a.AssetNumber,
                            AssetModel = a.AssetModel,
                        })
                        .ToListAsync();

                    var notInPanelDevices = (from u in gwScanResults
                                             join p in allPanelDevices on new { IpAddress = u.UnitId.HasValue ? $"{u.IpAddress}/{u.UnitId.Value}" : u.IpAddress, u.TypeName } equals new { IpAddress = p.AssetIpAddress, TypeName = p.AssetModel } into uJoin
                                             from j in uJoin.DefaultIfEmpty()
                                             where j == null
                                             select new UdcScanData
                                             {
                                                 AssetId = 0,
                                                 TypeDisplayName = u.TypeName,
                                                 TypeName = u.TypeName,
                                                 BootloaderVersion = u.BootloaderVersion,
                                                 FirmwareVersion = u.FirmwareVersion,
                                                 Gateway = u.Gateway,
                                                 IpAddress = u.IpAddress,
                                                 Port = u.Port,
                                                 PlantIdentifier = u.PlantIdentifier,
                                                 MacAddress = u.MacAddress,
                                                 ItemId = u.ItemId,
                                                 Netmask = u.Netmask,
                                                 IsGateway = u.IsGateway,
                                                 UnitId = u.UnitId,
                                                 OrderNumber = u.OrderNumber,
                                                 AssetType = _panelAssetTypes.GetValueOrDefault(u.TypeName ?? string.Empty),
                                             }).ToList();

                    var inPanelDevices = (from p in allPanelDevices
                                          join u in gwScanResults on new { IpAddress = p.AssetIpAddress, TypeName = p.AssetModel } equals new { IpAddress = u.UnitId.HasValue ? $"{u.IpAddress}/{u.UnitId.Value}" : u.IpAddress, u.TypeName } into uJoin
                                          from j in uJoin.DefaultIfEmpty()
                                          select new UdcScanData
                                          {
                                              AssetId = p.AssetId,
                                              ObjectId = p.ObjectId,
                                              AssetIpAddress = p.AssetIpAddress,
                                              AssetName = p.AssetName,
                                              AssetModel = p.AssetModel,
                                              AssetNumber = p.AssetNumber,
                                              AssetType = p.AssetType,
                                              TypeDisplayName = j?.TypeName,
                                              TypeName = j?.TypeName,
                                              BootloaderVersion = j?.BootloaderVersion,
                                              FirmwareVersion = j?.FirmwareVersion,
                                              Gateway = j?.Gateway,
                                              IpAddress = j?.IpAddress,
                                              Port = j?.Port,
                                              PlantIdentifier = j?.PlantIdentifier,
                                              MacAddress = j?.MacAddress,
                                              ItemId = j?.ItemId,
                                              Netmask = j?.Netmask,
                                              IsGateway = j?.IsGateway ?? false,
                                              UnitId = j?.UnitId,
                                              OrderNumber = j?.OrderNumber,
                                          }).ToList();

                    var finalData = inPanelDevices.Union(notInPanelDevices).ToList();

                    if (finalData.Any())
                    {
                        finalData.ForEach(a =>
                        {
                            if (!string.IsNullOrWhiteSpace(a.ObjectId))
                            {
                                a.ImportStatus = DeviceImportStatus.Imported.GetHashCode();
                            }
                            else if (!a.AssetId.HasValue || a.AssetId.Value <= 0)
                            {
                                a.ImportStatus = DeviceImportStatus.NotImported.GetHashCode();
                            }
                            else
                            {
                                a.ImportStatus = DeviceImportStatus.NotFound.GetHashCode();
                            }
                            a.InterfaceName = _interfaceName;
                            a.CreatedTime = DateTime.Now;
                            a.UpdatedTime = DateTime.Now;
                        });

                        await _client.Deleteable<UdcScanData>().Where(a => a.InterfaceName == _interfaceName
                        && !a.IsGateway && (SqlFunc.Contains(a.AssetIpAddress, tempGwDevice.IpAddress) || SqlFunc.Contains(a.IpAddress, tempGwDevice.IpAddress))).ExecuteCommandAsync();
                        await _client.Insertable(finalData).ExecuteCommandAsync();

                        await ChangeSplxFileDataPoints();

                        var service = _provider.GetRequiredService<DeviceInfoService>();
                        await service.InitDeviceInfoes(_logger);
                    }

                    jobInfo.JobStatus = 20;
                    _cache.Set($"JobId:{_jobId}", jobInfo, TimeSpan.FromMinutes(5));
                    _cache.Clear("ScanJobId");
                    return;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "DeviceScanJob ScanGatewayAsync error.");
                    _cache.Clear("ScanJobId");
                }
            }

            jobInfo.JobStatus = 99;
            jobInfo.Result.ErrorInfo.Add("Common_Param");
            _cache.Set($"JobId:{_jobId}", jobInfo, TimeSpan.FromMinutes(5));
            _cache.Clear("ScanJobId");
        }

        private async Task GeneralModbusDeviceScan()
        {
            //第三方设备直接修改objectid
            var needUpdateObjectIds = await _client.Queryable<AssetInfo>()
                .Where(a => a.AssetModel == "Other" || a.AssetType == "GeneralDevice" || ("Modbus".Equals(a.AssetModel) && "Gateway".Equals(a.AssetType)))
                .Where(a => SqlFunc.IsNullOrEmpty(a.ObjectId))
                .ToListAsync();

            foreach (var data in needUpdateObjectIds)
            {
                data.ObjectId = Guid.NewGuid().ToString();
            }

            await _client.Updateable(needUpdateObjectIds).UpdateColumns(a => a.ObjectId).ExecuteCommandAsync();

            var otherDevices = await _client.Queryable<AssetInfo>()
                .Where(a => a.AssetModel == "Other" || a.AssetType == "GeneralDevice" || ("Modbus".Equals(a.AssetModel) && "Gateway".Equals(a.AssetType)))
                .Where(a => !SqlFunc.IsNullOrEmpty(a.ObjectId))
                .ToListAsync();

            if (otherDevices.Any())
            {
                var assetIds = otherDevices.Select(a => a.Id).ToArray();

                var dataList = await _client.Queryable<UdcScanData>().Where(d => assetIds.Contains(d.AssetId ?? -1)).ToListAsync();

                foreach (var asset in otherDevices)
                {
                    if (string.IsNullOrEmpty(asset.IPAddress)) continue;
                    var ipMatch = Regex.Match(asset.IPAddress, "^(?<ip>[\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3})(?<port>:[\\d]{2,5})?(?<modbus>[\\|/]{1}[\\d]{1,5})?$");
                    if (!ipMatch.Success) continue;
                    var port = 0;
                    var modbusAddress = 0;
                    int.TryParse(ipMatch.Groups["port"].Value, out port);
                    int.TryParse(ipMatch.Groups["modbus"].Value, out modbusAddress);
                    var data = dataList.FirstOrDefault(d => d.AssetId == asset.Id);
                    if (data == null)
                    {
                        data = new UdcScanData
                        {
                            AssetId = asset.Id,
                            ObjectId = asset.ObjectId,
                            AssetIpAddress = asset.IPAddress,
                            AssetName = asset.AssetName,
                            AssetModel = asset.AssetModel,
                            AssetNumber = asset.AssetNumber,
                            AssetType = asset.AssetType,
                            TypeDisplayName = "第三方设备",
                            TypeName = "ModbusDevice",
                            BootloaderVersion = string.Empty,
                            FirmwareVersion = string.Empty,
                            Gateway = string.Empty,
                            IpAddress = ipMatch.Groups["ip"].Value,
                            Port = port,
                            PlantIdentifier = string.Empty,
                            MacAddress = string.Empty,
                            ItemId = asset.ObjectId,
                            Netmask = string.Empty,
                            IsGateway = false,
                            UnitId = modbusAddress,
                            OrderNumber = string.Empty,
                        };

                        dataList.Add(data);
                    }

                    data.ImportStatus = DeviceImportStatus.Imported.GetHashCode();
                }

                await _client.Storageable(dataList).ExecuteCommandAsync();

                var workerManager = _provider.GetRequiredService<IDeviceConenctStateWorkerManager>();
                await workerManager.StopAsync();
                await Task.Delay(3000);
                await workerManager.StartAsync();
            }
        }

        private async Task ChangeSplxFileDataPoints()
        {
            string udcDataPath = _configuration["UdcDataPath"] ?? "/data";
            var server = _provider.GetRequiredService<SplxFileManager>();
            var result = await server.ChangeSplxFile(udcDataPath, _userName);

            if (result != null && result.Success)
            {
                if (result.ModbusDevices.Count > 0)
                {
                    var assetIds = result.ModbusDevices.Select(m => m.Id).ToArray();
                    var dataList = await _client.Queryable<UdcScanData>().Where(d => assetIds.Contains(d.AssetId ?? -1)).ToListAsync();
                    foreach (var asset in result.ModbusDevices)
                    {
                        if (string.IsNullOrEmpty(asset.IPAddress)) continue;
                        var ipMatch = Regex.Match(asset.IPAddress, "^(?<ip>[\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3})(?<port>:[\\d]{2,5})?(?<modbus>[\\|/]{1}[\\d]{1,5})?$");
                        if (!ipMatch.Success) continue;
                        var port = 0;
                        var modbusAddress = 0;
                        int.TryParse(ipMatch.Groups["port"].Value, out port);
                        int.TryParse(ipMatch.Groups["modbus"].Value, out modbusAddress);
                        var data = dataList.FirstOrDefault(d => d.AssetId == asset.Id);
                        if (data == null)
                        {
                            data = new UdcScanData
                            {
                                AssetId = asset.Id,
                                ObjectId = asset.ObjectId,
                                AssetIpAddress = asset.IPAddress,
                                AssetName = asset.AssetName,
                                AssetModel = asset.AssetModel,
                                AssetNumber = asset.AssetNumber,
                                AssetType = asset.AssetType,
                                TypeDisplayName = "第三方设备",
                                TypeName = "ModbusDevice",
                                BootloaderVersion = string.Empty,
                                FirmwareVersion = string.Empty,
                                Gateway = string.Empty,
                                IpAddress = ipMatch.Groups["ip"].Value,
                                Port = port,
                                PlantIdentifier = string.Empty,
                                MacAddress = string.Empty,
                                ItemId = asset.ObjectId,
                                Netmask = string.Empty,
                                IsGateway = false,
                                UnitId = modbusAddress,
                                OrderNumber = string.Empty,
                            };

                            dataList.Add(data);
                        }

                        data.ImportStatus = DeviceImportStatus.Imported.GetHashCode();
                    }

                    await _client.Storageable(dataList).ExecuteCommandAsync();
                }
            }

            var workerManager = _provider.GetRequiredService<IDeviceConenctStateWorkerManager>();
            await workerManager.StopAsync();
            await Task.Delay(3000);
            await workerManager.StartAsync();
        }

        private async Task ChangeUdcConfig(string udcDeviceId)
        {
            try
            {
                // MqttServiceState:2代表开启
                List<DeivceDataPointItem> needUpdates = new List<DeivceDataPointItem>();
                DeivceDataPointItem deivceDataPointItem;

                var dataPoints = await _udcHttpService.GetUdcConfigAsync(udcDeviceId);

                if (dataPoints == null || !dataPoints.Any())
                {
                    return;
                }

                dataPoints.ForEach(x =>
                {
                    var configValue = _udcConfig.GetValueOrDefault(x.InternalName);
                    if (!string.IsNullOrWhiteSpace(configValue) && configValue != x.Value)
                    {
                        deivceDataPointItem = new DeivceDataPointItem
                        {
                            InternalName = x.InternalName,
                            Value = configValue
                        };

                        needUpdates.Add(deivceDataPointItem);
                    }
                });

                if (needUpdates.Any())
                {
                    await _udcHttpService.UpdateUdcConfigAsync(udcDeviceId, needUpdates);
                }

                var currentMqttState = dataPoints.FirstOrDefault(x => x.InternalName == "MqttServiceState");
                if (currentMqttState != null && currentMqttState.Value != "2")
                {
                    await _udcHttpService.StartUdcMqttServiceAsync(udcDeviceId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "DeviceScanJob ChangeUdcConfig error.");
            }
        }
    }
}
