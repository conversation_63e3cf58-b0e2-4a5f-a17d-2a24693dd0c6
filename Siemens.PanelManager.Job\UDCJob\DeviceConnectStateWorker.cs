﻿using Akka.Util.Internal;
using InfluxDB.Client.Api.Domain;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Job.ModbusDeviceWorker;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.DataFlow;
using Siemens.PanelManager.Model.UDC;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.Server.DataPoint;
using Siemens.PanelManager.Server.UDC;
using SqlSugar;
using System.Collections.Concurrent;
using System.Data;
using System.Reflection.Metadata;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Channels;

namespace Siemens.PanelManager.Job.UDCJob
{
    public class DeviceConnectStateWorker : BackgroundService
    {
        private readonly ILogger<DeviceConnectStateWorker> _logger;
        private readonly IConfiguration _configuration;
        private readonly IServiceProvider _provider;
        private readonly SiemensCache _cache;
        private HubConnection? _connect = null;
        private ConcurrentDictionary<ChannelReader<string>, string> _channelList = new ConcurrentDictionary<ChannelReader<string>, string>();
        private CancellationTokenSource? _source;
        private List<AssetInfo> _thirdDevices = new();
        private bool _needUpdateOid = false;
        private IUDCApiRef? _refApi;
        private IUDCApiRef? RefApi
        {
            get
            {
                if (_refApi == null)
                {
                    _refApi = _provider.GetService<IUDCApiRef>();
                }

                return _refApi;
            }
        }

        public DeviceConnectStateWorker(ILogger<DeviceConnectStateWorker> logger,
            IConfiguration configuration,
            IServiceProvider provider,
            SiemensCache cache)
        {
            UdcServiceContent.DeviceConnectStateWorker = this;
            _logger = logger;
            _configuration = configuration;
            _provider = provider;
            _cache = cache;
        }

        public override Task StartAsync(CancellationToken cancellationToken)
        {
            if (_source == null || _source.IsCancellationRequested)
            {
                _source = new CancellationTokenSource();
                return base.StartAsync(_source.Token);
            }
            return Task.CompletedTask;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            await Task.Delay(5000);
            _logger.LogInformation("DeviceConnectStateWorker starting");
            var url = _configuration.GetSection("UdcApiPath").Value;
            if (string.IsNullOrEmpty(url)) return;

            var match = Regex.Match(url, "^http[s]?://([\\w|\\.]*):([\\d]+)[/]?");

            if (match.Success)
            {
                await GetOldMessages();

                var connect = new HubConnectionBuilder()
                    .WithUrl($"ws://{match.Groups[1].Value}:{match.Groups[2].Value}/hub/v1/notification")
                    .Build();

                connect.Closed += OnDisconnecd;
                connect.On<UdcMessageModel>("MessageReceived", OnMessageReceived);
                for (var i = 0; i < 3; i++)
                {
                    try
                    {
                        await connect.StartAsync(stoppingToken);
                        break;
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(e, $"连接失败 第{i + 1}次");

                        await Task.Delay(5000);
                        if (i >= 3)
                        {
                            _logger.LogInformation("UDC Device connect status ws 连接失败");
                            await OnDisconnecd(null);

                            return;
                        }
                    }
                }

                _logger.LogInformation("UDC Device connected");
                _connect = connect;

                var register = _provider.GetRequiredService<IAssetChangeRegister>();
                register.OnAssetChanged("DeviceConnectStateWorker", OnAssetChanged);

                var sqlClient = _provider.GetRequiredService<SqlSugarScope>();
                var assets = await sqlClient.Queryable<AssetInfo>().Where(a => a.AssetLevel == AssetLevel.Device && a.ObjectId != null).ToArrayAsync();
                foreach (var asset in assets)
                {
                    if (string.IsNullOrEmpty(asset.ObjectId)) continue;
                    try
                    {
                        await GetChannelByObjectId(connect, asset.ObjectId);
                    }
                    catch (Exception channelException)
                    {
                        _logger.LogError(channelException, message: $"资产 {asset.Id} 连接失败");
                    }
                }

                var refObj = _provider.GetService<IAssetDataProxyRef>();

                InitThirdDevices();

                var alarmRef = _provider.GetRequiredService<IAlarmRef>();

                while (true)
                {
                    if (stoppingToken.IsCancellationRequested)
                    {
                        return;
                    }

                    if (refObj == null)
                    {
                        await Task.Delay(TimeSpan.FromSeconds(2));
                        refObj = _provider.GetService<IAssetDataProxyRef>();
                        continue;
                    }

                    var keys = _channelList.Keys.ToArray();

                    var connectStatus = new Dictionary<int, bool>();
                    foreach (var c in keys)
                    {
                        var deviceStatus = string.Empty;
                        while (true)
                        {
                            if (c.TryRead(out var status))
                            {
                                deviceStatus = status;
                            }
                            else
                            {
                                break;
                            }
                        }

                        switch (deviceStatus.ToLower())
                        {
                            case "online":
                                {
                                    var objectId = _channelList[c];
                                    var assetInfo = _cache.Get<AssetSimpleInfo>($"AssetObjectId:{objectId}");
                                    if (assetInfo == null) break;
                                    connectStatus.TryAdd(assetInfo.AssetId, true);
                                    var input = new AssetInputData
                                    {
                                        AssetId = assetInfo.AssetId,
                                        AssetName = assetInfo.AssetName,
                                        ObjectId = assetInfo.ObjectId,
                                        InputTime = DateTime.Now,
                                        Datas = new Dictionary<string, string>
                                        {
                                            ["IsConnected"] = "1"
                                        }
                                    };
                                    refObj.InputData(input);
                                    break;
                                }
                            case "offline":
                                {
                                    var objectId = _channelList[c];
                                    var assetInfo = _cache.Get<AssetSimpleInfo>($"AssetObjectId:{objectId}");
                                    if (assetInfo == null) break;
                                    // device status changed from online to offline,recording alarm log
                                    var simgleInfo = _cache.Get<AssetSimpleInfo>(string.Format("Asset:SimpleInfo-{0}", assetInfo.AssetId));
                                    var oldConnectStatus = _cache.Get<Dictionary<int, bool>>("AssetConnectStatus:All");
                                    if (oldConnectStatus != null && oldConnectStatus.ContainsKey(assetInfo.AssetId))
                                    {
                                        var oldStatus = oldConnectStatus[assetInfo.AssetId];

                                        if (oldStatus)
                                        {
                                            var log = new AlarmLog()
                                            {
                                                Severity = AlarmSeverity.Middle,
                                                AssetStatusStr = JsonConvert.SerializeObject(AlarmLogStatus.None),
                                                AssetId = assetInfo.AssetId,
                                                Status = AlarmLogStatus.None,
                                                DeviceName = assetInfo.AssetName,
                                                CircuitName = simgleInfo?.CircuitSimpleInfo?.AssetName,
                                                PanelName = simgleInfo?.PanelSimpleInfo?.AssetName,
                                                SubstationName = simgleInfo?.SubstationSimpleInfo?.AssetName,
                                                Message = "设备通信连接中断," + assetInfo.AssetName,
                                                EventType = AlarmEventType.CommunicationAlarm,
                                                CreatedBy = "System",
                                                CreatedTime = DateTime.Now,
                                                UpdatedBy = "System",
                                                UpdatedTime = DateTime.Now,
                                            };
                                            alarmRef.AppendAlarmLog(log);
                                        }
                                    }


                                    connectStatus.TryAdd(assetInfo.AssetId, false);
                                    var input = new AssetInputData
                                    {
                                        AssetId = assetInfo.AssetId,
                                        AssetName = assetInfo.AssetName,
                                        ObjectId = assetInfo.ObjectId,
                                        InputTime = DateTime.Now,
                                        Datas = new Dictionary<string, string>
                                        {
                                            ["IsConnected"] = "0"
                                        }
                                    };
                                    refObj.InputData(input);
                                    break;
                                }
                            default: break;
                        }
                    }


                    //通用modbus设备
                    foreach (var thirdDevice in _thirdDevices)
                    {
                        string deviceStatus = PanelModbusClientStatus.GetInstance().GetClientStatus(thirdDevice.Id);

                        switch (deviceStatus)
                        {
                            case "1":
                                {

                                    connectStatus.TryAdd(thirdDevice.Id, true);
                                    var input = new AssetInputData
                                    {
                                        AssetId = thirdDevice.Id,
                                        AssetName = thirdDevice.AssetName,
                                        ObjectId = thirdDevice.ObjectId,
                                        InputTime = DateTime.Now,
                                        Datas = new Dictionary<string, string>
                                        {
                                            ["IsConnected"] = "1"
                                        }
                                    };
                                    refObj.InputData(input);
                                    break;
                                }
                            case "0":
                                {
                                    var oldConnectStatus = _cache.Get<Dictionary<int, bool>>("AssetConnectStatus:All");
                                    if (oldConnectStatus != null && oldConnectStatus.ContainsKey(thirdDevice.Id))
                                    {
                                        var oldStatus = oldConnectStatus[thirdDevice.Id];

                                        if (oldStatus)
                                        {
                                            var simgleInfo = _cache.Get<AssetSimpleInfo>(string.Format("Asset:SimpleInfo-{0}", thirdDevice.Id));

                                            var log = new AlarmLog()
                                            {
                                                Severity = AlarmSeverity.Middle,
                                                AssetStatusStr = JsonConvert.SerializeObject(AlarmLogStatus.None),
                                                AssetId = thirdDevice.Id,
                                                Status = AlarmLogStatus.None,
                                                DeviceName = thirdDevice.AssetName,
                                                CircuitName = simgleInfo?.CircuitSimpleInfo?.AssetName,
                                                PanelName = simgleInfo?.PanelSimpleInfo?.AssetName,
                                                SubstationName = simgleInfo?.SubstationSimpleInfo?.AssetName,
                                                Message = "设备通信连接中断," + thirdDevice.AssetName,
                                                EventType = AlarmEventType.CommunicationAlarm,
                                                CreatedBy = "System",
                                                CreatedTime = DateTime.Now,
                                                UpdatedBy = "System",
                                                UpdatedTime = DateTime.Now,
                                            };
                                            alarmRef.AppendAlarmLog(log);
                                        }
                                    }


                                    connectStatus.TryAdd(thirdDevice.Id, false);
                                    var input = new AssetInputData
                                    {
                                        AssetId = thirdDevice.Id,
                                        AssetName = thirdDevice.AssetName,
                                        ObjectId = thirdDevice.ObjectId,
                                        InputTime = DateTime.Now,
                                        Datas = new Dictionary<string, string>
                                        {
                                            ["IsConnected"] = "0"
                                        }
                                    };
                                    refObj.InputData(input);
                                    break;
                                }
                            default: break;
                        }
                    }

                    if (connectStatus == null || connectStatus.Count <= 0)
                    {
                        _cache.Clear("AssetConnectStatus:All");
                    }
                    else
                    {
                        _cache.Set("AssetConnectStatus:All", connectStatus);
                    }

                    await Task.Delay(TimeSpan.FromSeconds(1));
                }
            }
            else
            {
                _logger.LogInformation("UDC Device connect status ws 启动失败，配置错误");
            }
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            if (_source != null)
            {
                _logger.LogInformation("DeviceConnectStateWorker stopping");
                if (_connect != null)
                {
                    await _connect.DisposeAsync();
                }
                _source.Cancel();
                var register = _provider.GetRequiredService<IAssetChangeRegister>();
                register.RemoveRegister("DeviceConnectStateWorker");
                _channelList.Clear();

                await base.StopAsync(_source.Token);
            }
        }

        private async Task GetOldMessages()
        {
            var udcContext = await GetUDCContext();

            using (var sqlClient = _provider.GetRequiredService<ISqlSugarClient>())
            {
                var oid = await sqlClient.Queryable<AssetMessagesConfig>().Where(a => a.AssetId == -1).Select(a => a.Oid).FirstAsync();

                int waitTime = 0;
                _needUpdateOid = false;
                while (true)
                {
                    try
                    {
                        if (RefApi != null)
                        {
                            var messages = await RefApi.GetDeviceMessages(udcContext.PocObjectId, oid);
                            if (messages == null || messages.Embedded == null
                                || messages.Embedded.Items == null || messages.Embedded.Items.Count == 0)
                            {
                                break;
                            }

                            messages.Embedded.Items = messages.Embedded.Items.Where(m => m.Oid != oid).ToList();
                            if (messages.Embedded.Items.Count <= 0)
                            {
                                break;
                            }

                            oid = messages.Embedded.Items.First().Oid;

                            await UpdateOid(sqlClient, oid);
                            foreach (var message in messages.Embedded.Items)
                            {
                                await OnMessageReceived(message);
                            }
                        }
                        else
                        {
                            waitTime++;
                            await Task.Delay(200);

                            if (waitTime > 10)
                            {
                                break;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "获取历史消息失败");
                        break;
                    }
                }

                _needUpdateOid = true;
            }
        }

        private async Task UpdateOid(ISqlSugarClient sqlClient, string? oid)
        {
            var storage = await sqlClient.Storageable<AssetMessagesConfig>(new AssetMessagesConfig
            {
                AssetId = -1,
                Oid = oid,
                CreatedBy = "UDC",
                CreatedTime = DateTime.Now,
                UpdatedBy = "UDC",
                UpdatedTime = DateTime.Now,
            })
                .WhereColumns(a => a.AssetId)
                .ToStorageAsync();

            await storage.AsInsertable.ExecuteCommandAsync();
            await storage.AsUpdateable.IgnoreColumns(a => new { a.CreatedBy, a.CreatedTime }).ExecuteCommandAsync();
        }

        private UDCContext? _udcContext;
        private async Task<UDCContext> GetUDCContext()
        {
            if (_udcContext == null)
            {
                _udcContext = _provider.GetService<UDCContext>();
            }

            if (_udcContext == null)
            {
                var service = _provider.GetRequiredService<UDCContextInit>();
                _udcContext = await service.GetUDCContext();
            }

            return _udcContext;
        }

        private async Task GetChannelByObjectId(HubConnection connect, string objectId)
        {
            var channel = await connect.StreamAsChannelAsync<string>("ConnectionState", objectId);
            _channelList.TryAdd(channel, objectId);
        }

        private async Task OnDisconnecd(Exception? ex)
        {
            _logger.LogError(ex, "连接失败");
            await StopAsync(CancellationToken.None);

            await Task.Delay(TimeSpan.FromMinutes(1));

            await StartAsync(CancellationToken.None);
        }

        private async Task OnMessageReceived(UdcMessageModel model)
        {
            var udcContext = await GetUDCContext();
            _logger.LogDebug(JsonConvert.SerializeObject(model));
            if (RefApi != null && udcContext != null && !string.IsNullOrEmpty(udcContext.PocObjectId))
            {
                try
                {
                    if (_needUpdateOid)
                    {
                        var sqlClient = _provider.GetRequiredService<SqlSugarScope>();
                        await UpdateOid(sqlClient, model.Oid);
                    }

                    var messageModel = await RefApi.GetMessageByOid(udcContext.PocObjectId, int.Parse(model.Oid));
                    if (messageModel == null)
                    {
                        _logger.LogDebug("message 是空");
                        return;
                    }

                    _logger.LogDebug(JsonConvert.SerializeObject(messageModel));
                    if (!string.IsNullOrEmpty(messageModel.DisplayText) && messageModel.DisplayText.Contains("Received event with OID")) return;
                    if (!string.IsNullOrEmpty(messageModel.DisplayText) && messageModel.DisplayText.Contains("Mqtt", StringComparison.OrdinalIgnoreCase)) return;

                    if (!string.IsNullOrEmpty(messageModel.DisplayText)
                       && (messageModel.DisplayText.Contains("trip") || (messageModel.DisplayText.Contains("脱扣")
                       && !messageModel.DisplayText.Contains("恢复")))
                       && messageModel.Details != null)
                    {
                        var category = messageModel.Details.FirstOrDefault(d => "Slave device".Equals(d.DisplayCategory) || "从站设备".Equals(d.DisplayCategory));
                        if (category != null)
                        {
                            var itemIdEntry = category.Entries.FirstOrDefault(e => "Item ID".Equals(e.DisplayName) || "项目 ID".Equals(e.DisplayName));
                            if (itemIdEntry != null)
                            {
                                var trips = await RefApi.GetBreakerTrips(itemIdEntry.DisplayValue, 1);
                                _logger.LogDebug(JsonConvert.SerializeObject(trips));

                                var tripInfo = trips?.Embedded?.Items?.FirstOrDefault();

                                if (tripInfo != null)
                                {
                                    await AddDeviceTripLog(itemIdEntry.DisplayValue, tripInfo);
                                }
                                else
                                {
                                    await AddDeviceTripLog(itemIdEntry.DisplayValue, new UdcMessageModel
                                    {
                                        DisplayText = messageModel.DisplayText,
                                        Timestamp = messageModel.Timestamp
                                    });
                                }
                            }
                        }
                    }
                    else
                    {
                        string? objectId = null;
                        if (messageModel.Details != null)
                        {
                            UdcMessageDetailsEntryModel? itemIdEntry = null;
                            foreach (var category in messageModel.Details)
                            {
                                itemIdEntry = category.Entries.FirstOrDefault(e => "Item ID".Equals(e.DisplayName) || "项目 ID".Equals(e.DisplayName));
                                if (itemIdEntry != null) break;
                            }
                            if (itemIdEntry != null)
                            {
                                objectId = itemIdEntry.DisplayValue;
                            }
                        }

                        var severity = GetAlarmSeverity(messageModel.Severity ?? string.Empty);

                        await AddUdcLogAlarm(messageModel.DisplayText, severity, objectId, messageModel.Time);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "获取message失败");
                }
            }
        }

        private void OnAssetChanged(AssetInfoOptionParam param)
        {
            InitThirdDevices();
            if (_connect == null || _connect.State != HubConnectionState.Connected) return;
            if (param == null || param.SimpleInfo == null) return;
            if (param.SimpleInfo.AssetLevel == AssetLevel.Device && !string.IsNullOrEmpty(param.SimpleInfo.ObjectId))
            {
                var values = _channelList.Values.ToArray();

                if (!values.Any(v => param.SimpleInfo.ObjectId.Equals(v)))
                {
                    _ = GetChannelByObjectId(_connect, param.SimpleInfo.ObjectId);
                }
            }
        }

        private void InitThirdDevices()
        {
            try
            {
                var sqlClient = _provider.GetRequiredService<SqlSugarScope>();

                _thirdDevices = sqlClient.Queryable<AssetInfo>()
                     .Where(a => (a.AssetModel == "Other") || (a.AssetType == "GeneralDevice") || ("Modbus".Equals(a.AssetModel) && "Gateway".Equals(a.AssetType)))
                     .Where(a => !SqlFunc.IsNullOrEmpty(a.IPAddress) && !SqlFunc.IsNullOrEmpty(a.ObjectId))
                     .ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "DeviceConnectStateWorker InitThirdDevices error");
            }
        }

        private async Task AddUdcLogAlarm(string message, AlarmSeverity severity, string? objectId, DateTime time)
        {
            var status = severity == AlarmSeverity.High ? AlarmLogStatus.New : AlarmLogStatus.None;
            var eventType = severity == AlarmSeverity.High ? AlarmEventType.UdcAlarm : AlarmEventType.DeviceLog;

            var proxy = _provider.GetRequiredService<IAlarmRef>();

            var alarm = new AlarmLog
            {
                CreatedBy = "Udc",
                CreatedTime = time,
                UpdatedBy = "Udc",
                UpdatedTime = time,
                EventType = eventType,
                Severity = severity,
                Status = status,
                Message = message,
            };

            var dataPointServer = _provider.GetRequiredService<DataPointServer>();
            var alarmRuleServer = _provider.GetRequiredService<AlarmRuleServer>();
            var sqlClient = _provider.GetRequiredService<SqlSugarScope>();
            if (!string.IsNullOrEmpty(objectId))
            {
                int i = 0;
                while (i < 3)
                {
                    var assetId = await sqlClient.Queryable<AssetInfo>().Where(a => a.ObjectId == objectId).Select(a => a.Id).FirstAsync();
                    if (assetId > 0)
                    {
                        var simpleInfo = _cache.Get<AssetSimpleInfo>(string.Format("Asset:SimpleInfo-{0}", assetId));
                        if (simpleInfo != null)
                        {
                            alarm.AssetId = simpleInfo.AssetId;

                            var data = _cache.GetHashAllData(string.Format("AssetStatus:Currently-{0}", simpleInfo.AssetId));
                            var dataPointList = await dataPointServer.GetDataPointInfos(simpleInfo.AssetLevel, simpleInfo.AssetType, simpleInfo.AssetModel, simpleInfo.AssetId);
                            var statusStr = alarmRuleServer.GetAssetStatusStr(data, dataPointList, false);
                            alarm.AssetStatusStr = statusStr;

                            if (simpleInfo.SubstationSimpleInfo != null)
                            {
                                alarm.SubstationName = simpleInfo.SubstationSimpleInfo.AssetName;
                            }

                            if (simpleInfo.CircuitSimpleInfo != null)
                            {
                                alarm.CircuitName = simpleInfo.CircuitSimpleInfo.AssetName;
                            }

                            if (simpleInfo.PanelSimpleInfo != null)
                            {
                                alarm.PanelName = simpleInfo.PanelSimpleInfo.AssetName;
                            }

                            alarm.DeviceName = simpleInfo.AssetName;
                        }
                        else
                        {
                            i++;
                            continue;
                        }
                        proxy.AppendAlarmLog(alarm);
                    }
                    break;
                }
            }
        }

        private async Task AddDeviceTripLog(string objectId, UdcMessageModel messageModel)
        {
            if (string.IsNullOrEmpty(objectId)) return;
            var sqlClient = _provider.GetRequiredService<SqlSugarScope>();
            var assetId = await sqlClient.Queryable<AssetInfo>().Where(a => a.ObjectId == objectId).Select(a => a.Id).FirstAsync();
            if (assetId <= 0) return;

            AssetSimpleInfo? assetInfo = null;
            int retry = 0;
            while (retry < 3)
            {
                assetInfo = _cache.Get<AssetSimpleInfo>(string.Format("Asset:SimpleInfo-{0}", assetId));
                if (assetInfo != null) break;
                retry++;
            }

            if (assetInfo == null) return;

            var statusDic = new Dictionary<string, string>();
            var settingsDic = new Dictionary<string, string>();
            var messageBuilder = new StringBuilder(messageModel.DisplayText);
            var reasonCategory = messageModel.Details?.FirstOrDefault(d => "Reason".Equals(d.DisplayCategory) || "原因".Equals(d.DisplayCategory)); //原因
            if (reasonCategory != null)
            {
                messageBuilder.AppendLine();
                messageBuilder.AppendLine("Reason:");
                foreach (var i in reasonCategory.Entries)
                {
                    messageBuilder.Append(i.DisplayName);
                    messageBuilder.Append(':');
                    messageBuilder.AppendLine(i.DisplayValue);
                }
            }

            var settingsCategory = messageModel.Details?.FirstOrDefault(d => "Settings".Equals(d.DisplayCategory) || "设置".Equals(d.DisplayCategory)); //设置
            if (settingsCategory != null)
            {
                foreach (var i in settingsCategory.Entries)
                {
                    settingsDic.TryAdd(i.DisplayName, i.DisplayValue);
                }
            }

            var generalCategory = messageModel.Details?.FirstOrDefault(d => "General".Equals(d.DisplayCategory) || "常规".Equals(d.DisplayCategory)); //设置
            if (generalCategory != null)
            {
                foreach (var i in generalCategory.Entries)
                {
                    settingsDic.TryAdd(i.DisplayName, i.DisplayValue);
                }
            }

            var parameterCategory = messageModel.Details?.FirstOrDefault(d => "Parameter".Equals(d.DisplayCategory) || "参数".Equals(d.DisplayCategory)); //设置
            if (parameterCategory != null)
            {
                foreach (var i in parameterCategory.Entries)
                {
                    settingsDic.TryAdd(i.DisplayName, i.DisplayValue);
                }
            }

            var currentlyCategory = messageModel.Details?.FirstOrDefault(d => "Current".Equals(d.DisplayCategory) || "电流".Equals(d.DisplayCategory)); // 电流
            if (currentlyCategory != null)
            {
                foreach (var i in currentlyCategory.Entries)
                {
                    statusDic.TryAdd(i.DisplayName, i.DisplayValue);
                }
            }

            var alarmLog = new AlarmLog
            {
                DeviceName = assetInfo.AssetName,
                SubstationName = assetInfo.SubstationSimpleInfo?.AssetName,
                CircuitName = assetInfo.CircuitSimpleInfo?.AssetName,
                PanelName = assetInfo.PanelSimpleInfo?.AssetName,
                AssetId = assetInfo.AssetId,
                EventType = AlarmEventType.BreakerTrip,
                Severity = AlarmSeverity.High,
                Status = AlarmLogStatus.New,
                Message = messageBuilder.ToString(),
                AssetSettingsStr = JsonConvert.SerializeObject(settingsDic, formatting: Formatting.None),
                AssetStatusStr = JsonConvert.SerializeObject(statusDic, formatting: Formatting.None),
                CreatedBy = "Udc",
                CreatedTime = messageModel.Time,
                UpdatedBy = "Udc",
                UpdatedTime = messageModel.Time,
            };

            var proxy = _provider.GetRequiredService<IAlarmRef>();

            proxy.AppendAlarmLog(alarmLog);
        }

        private AlarmSeverity GetAlarmSeverity(string severity)
        {
            switch (severity)
            {
                case "information":
                    return AlarmSeverity.Low;
                case "alarm":
                    return AlarmSeverity.High;
                case "warning":
                    return AlarmSeverity.Middle;
                default: return AlarmSeverity.Middle;
            }
        }
    }
}
