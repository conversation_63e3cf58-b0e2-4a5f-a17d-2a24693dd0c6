﻿using Microsoft.Extensions.Logging;
using MiniExcelLibs;
using Siemens.PanelManager.Model.Excel;

namespace Siemens.PanelManager.Common.Excel
{
    public class SiemensExcelHelper
    {
        private ILogger<SiemensExcelHelper> _logger;
        private readonly string TemplatePath;
        private readonly string BasePath;
        public SiemensExcelHelper(ILogger<SiemensExcelHelper> logger) 
        {
            _logger = logger;
            BasePath = Directory.GetCurrentDirectory();
            TemplatePath = "ExcelTemplate";
        }

        public async Task SaveExcelAsync(Stream stream, IDictionary<string, object> datas,
            string language, string templateName)
        {
            var templateExcel = Path.Combine(BasePath, TemplatePath, language, templateName + ".xlsx");
            if (!File.Exists(templateExcel)) return;

            using var ms = new MemoryStream();
            try
            {
                await MiniExcel.SaveAsByTemplateAsync(stream, templateExcel, datas);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"生成{templateName}模板失败");
            }
        }


        public async Task SaveExcelAsync<T>(Stream stream, IDictionary<string, IList<T>> datas,
            string language, string templateName)
        {
            var saveData = new Dictionary<string, object>();
            foreach (var k in datas.Keys)
            {
                saveData.Add(k, datas[k]);
            }

            await SaveExcelAsync(stream, saveData, language, templateName);
        }

        /// <summary>
        /// 回调返回值描述
        /// 1: 成功
        /// 0: 当前sheet结束
        /// -1: 读取结束
        /// </summary>
        /// <param name="fileStream"></param>
        /// <param name="reader"></param>
        /// <param name="sheet"></param>
        /// <param name="startCell"></param>
        /// <param name="pageCount"></param>
        /// <returns></returns>
        public async Task QueryAsync(string file, 
            Func<ExcelReaderResult, Task<int>> reader,
            string? sheet = null,
            string startCell = "A1",
            int pageCount = 100)
        {
            if (!File.Exists(file)) return;
            using var fs = new FileStream(file, FileMode.Open);
            await QueryAsync(fs, reader, sheet, startCell, pageCount);
        }

        /// <summary>
        /// 回调返回值描述
        /// 1: 成功
        /// 0: 当前sheet结束
        /// -1: 读取结束
        /// </summary>
        /// <param name="fileStream"></param>
        /// <param name="reader"></param>
        /// <param name="sheet"></param>
        /// <param name="startCell"></param>
        /// <param name="pageCount"></param>
        /// <returns></returns>
        public async Task QueryAsync(Stream fileStream,
            Func<ExcelReaderResult, Task<int>> reader,
            string? sheet = null,
            string startCell = "A1",
            int pageCount = 100)
        {
            List<string> sheets = new List<string>();
            if (string.IsNullOrEmpty(sheet))
            {
                sheets.AddRange(MiniExcel.GetSheetNames(fileStream));
            }
            else
            {
                sheets.Add(sheet);
            }

            foreach (var s in sheets)
            {
                var sheetQuery = await MiniExcel.QueryAsync(fileStream, sheetName: s, startCell: startCell);
                int pageIndex = 1;
                while (true)
                {
                    var rows = sheetQuery.Skip(pageCount * (pageIndex - 1)).Take(pageCount)
                        .Cast<IDictionary<string, object>>()
                        .ToArray();

                    if (rows == null || rows.Length <= 0) break;

                    var result = await reader(new ExcelReaderResult(s, pageIndex, rows));
                    if (result == -1) return;
                    if (result == 0) break;
                    if (rows.Length < pageCount)
                    {
                        break;
                    }
                    pageIndex++;
                }
            }
        }
    }
}
