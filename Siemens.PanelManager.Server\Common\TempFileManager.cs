﻿using System.Text.RegularExpressions;

namespace Siemens.PanelManager.Server.Common
{
    public class TempFileManager
    {
        private string _tempDirectory;
        public TempFileManager()
        {
            var basePath = AppContext.BaseDirectory;
            _tempDirectory = Path.Combine(basePath, "temp");

            if (!Directory.Exists(_tempDirectory))
            {
                Directory.CreateDirectory(_tempDirectory);
            }
        }

        public string GetTempFolder()
        {
            return _tempDirectory;
        }

        public async Task<FileInfo> CreatedTempFile(Stream stream, string? oldFileName = null)
        {
            var postfix = string.Empty;
            if (!string.IsNullOrEmpty(oldFileName))
            {
                var match = Regex.Match(oldFileName, "\\.[\\w]+$");
                if (match.Success)
                {
                    postfix = match.Groups[0].Value;
                }
            }
            var newFileName = $"{Guid.NewGuid()}{postfix}";
            var filePath = Path.Combine(_tempDirectory, newFileName);
            using (var fs = new FileStream(filePath, FileMode.CreateNew))
            {
                await stream.CopyToAsync(fs);
            }
            var fileInfo = new FileInfo(filePath);
            return fileInfo;
        }

        public FileInfo[] GetTempFile(DateTime? startDate = null, DateTime? endDate = null)
        {
            if (startDate == null)
            {
                startDate = DateTime.MinValue;
            }

            if (endDate == null)
            {
                endDate = DateTime.MaxValue;
            }

            var files = Directory.GetFiles(_tempDirectory);

            var filePaths = new List<FileInfo>();
            foreach (var f in files)
            {
                var fileInfo = new FileInfo(f);
                if (fileInfo.LastWriteTime > startDate && fileInfo.LastWriteTime < endDate)
                {
                    filePaths.Add(fileInfo);
                }
            }

            return filePaths.ToArray();
        }
    }
}
