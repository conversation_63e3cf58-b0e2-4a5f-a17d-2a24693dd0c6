﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.Model.UDC
{
    public class DeviceDataPointsResult : UdcListResulBase<DeivceDataPointItem>
    {
        [JsonIgnore]
        public string From { get; set; } = string.Empty;

        [JsonProperty(PropertyName = "item_id")]
        public string? ItemId { get; set; } = string.Empty;

        [JsonProperty(PropertyName = "item_name")]
        public string? ItemName { get; set; } = string.Empty;

        [JsonProperty(PropertyName = "timestamp")]
        public string? TimeStamp { get; set; }

        [JsonProperty(PropertyName = "dataType")]
        public string? DataType { get; set; }

    }

    public class DeivceDataPointItem : IUdcData
    {
        [JsonProperty(PropertyName = "internal_name")]
        public string? InternalName { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "name")]
        public string? Name { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "id")]
        public int Id { get; set; }
        [JsonProperty(PropertyName = "display_name")]
        public string? DisplayName { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "display_value")]
        public string? DisplayValue { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "value")]
        public string Value { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "unit")]
        public string? Unit { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "quality")]
        public string? Quality { get; set; } = string.Empty;

        [JsonProperty(PropertyName = "timestamp")]
        public string? TimeStamp { get; set; }
    }

    public class AlarmResult : UdcListResulBase<AlarmItem>
    {
        [JsonProperty(PropertyName = "item_id")]
        public string? ItemId { get; set; } = string.Empty;

        [JsonProperty(PropertyName = "item_name")]
        public string? ItemName { get; set; } = string.Empty;

        [JsonProperty(PropertyName = "timestamp")]
        public string? TimeStamp { get; set; }

        [JsonProperty(PropertyName = "dataType")]
        public string? DataType { get; set; }
    }

    public class AlarmItem : IUdcData
    {
        [JsonProperty(PropertyName = "internal_name")]
        public string? InternalName { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "name")]
        public string? Name { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "value")]
        public string Value { get; set; } = string.Empty;
    }
}
