﻿
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Quartz;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.Database;
using Siemens.PanelManager.Common.DependencyInjection;
using Siemens.PanelManager.Common.Excel;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.Common.Log;
using Siemens.PanelManager.Common.Model;
using Siemens.PanelManager.Common.mqtt;
using Siemens.PanelManager.Common.Mqtt;
using Siemens.PanelManager.Model.Config;
using Siemens.PanelManager.Model.Database;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.Auth;
using Siemens.PanelManager.Model.Database.ElectricityCharge;
using Siemens.PanelManager.Model.Database.System;
using SqlSugar;
using System.Data;
using System.Reflection;
using System.Text.RegularExpressions;
using InfluxDBClientFactory = Siemens.PanelManager.Common.InfluxDB.InfluxDBClientFactory;
using TaskType = InfluxDB.Client.Api.Domain.TaskType;

namespace Siemens.PanelManager.Common
{
    public static class Statup
    {
        public const string UpdatedBy = "InitServer";

        public static void AddService(IServiceCollection services)
        {
            services.AddSingleton<SiemensCache>();
            services.AddTransient<ApiLogger>();
            services.AddSingleton<SiemensExcelHelper>();
            services.AddSingleton<ObjectReflectFunc>();
            services.AddTransient<JobManager>();
            services.AddTransient<LocalSecurityFunc>();
            services.AddSingleton<ConnectionConfig>((p) =>
            {
                var configuration = p.GetService<IConfiguration>();
                var connStr = configuration?.GetConnectionString("default");
                if (connStr == null)
                {
                    LogHelper.Error("数据库缺少配置");
                    throw new Exception("初始化失败: 缺少数据库配置");
                }
                return DatabaseHelper.GetConnectionConfig(connStr);
            });
            services.AddTransient<ISqlSugarClient>((p) =>
            {
                return DatabaseHelper.GetSqlSugarClient(p.GetRequiredService<ConnectionConfig>(), p);
            });
            services.AddSingleton<SqlSugarScope>((p) => DatabaseHelper.GetSqlSugarScope(p.GetRequiredService<ConnectionConfig>(), p));
            services.AddSingleton(async (p) =>
            {
                await LoadInfluxDBConfig(p);
                return InfluxDBClientFactory.CreateClient();
            });

            services.AddTransient(async (p) =>
            {
                await LoadInfluxDBConfig(p);
                return InfluxDBClientFactory.GetConfig();
            });

            services.AddQuartz(c =>
            {
                c.UseMicrosoftDependencyInjectionJobFactory();
                c.UseInMemoryStore();
                c.UseDefaultThreadPool(tp =>
                {
                    tp.MaxConcurrency = 5;
                });
                c.AddJobListener<JobListener>();
            });

            services.AddQuartzHostedService(c => { c.WaitForJobsToComplete = true; });

            LoadDI(services);
            services.AddHostedService<JobHostedService>();
        }

        private static async Task LoadInfluxDBConfig(IServiceProvider p)
        {
            if (InfluxDBClientFactory.NeedConfig())
            {
                var configuration = p.GetService<IConfiguration>();
                var session = configuration?.GetSection("InfluxDb");
                if (session != null && session.Exists())
                {
                    InfluxDBConfig config = new InfluxDBConfig();
                    var clients = session.GetChildren();
                    foreach (var client in clients)
                    {
                        switch (client.Key)
                        {
                            case "Url":
                                config.Url = client.Value ?? string.Empty;
                                break;
                            case "UserName":
                                config.UserName = client.Value ?? string.Empty;
                                break;
                            case "Password":
                                config.Password = client.Value ?? string.Empty;
                                break;
                            case "OrgName":
                                config.OrgName = client.Value ?? string.Empty;
                                break;
                            case "Bucket":
                                config.Bucket = client.Value ?? string.Empty;
                                break;
                            default: break;
                        }
                    }

                    if (!string.IsNullOrEmpty(config.Url)
                        && !string.IsNullOrEmpty(config.UserName)
                        && !string.IsNullOrEmpty(config.Password)
                        && !string.IsNullOrEmpty(config.Bucket)
                        && !string.IsNullOrEmpty(config.OrgName))
                    {
                        await InfluxDBClientFactory.LoadConfig(config);
                    }
                    else
                    {
                        LogHelper.Error("InfluxDB 配置错误");
                    }
                }
            }
        }

        private static void LoadDI(IServiceCollection services)
        {
            var basePath = AppContext.BaseDirectory;
            var files = Directory.GetFiles(basePath, "*.dll");

            Type serviceInjectionType = typeof(IServiceInjection);
            foreach (var f in files)
            {
                var match = Regex.IsMatch(f, "Siemens\\.PanelManager\\.[\\w|.]*\\.dll$");
                if (!match) continue;
                var a = Assembly.LoadFrom(f);
                var types = a.GetTypes();
                foreach (var t in types)
                {
                    var interfaceType = t.GetInterface(serviceInjectionType.FullName ?? string.Empty);
                    if (interfaceType != null)
                    {
                        try
                        {
                            var obj = a.CreateInstance(t.FullName ?? string.Empty);
                            if (obj != null && obj is IServiceInjection injection)
                            {
                                injection.AddService(services);
                            }
                        }
                        catch (Exception ex)
                        {
                            LogHelper.Error($"{t.Name}-{t.FullName} Instance失败", ex);
                            continue;
                        }
                    }
                }
            }
        }

        public static void AddLog(ILoggingBuilder builder)
        {
            builder.ClearProviders();
            builder.AddProvider(new LoggerProvider());
        }

        #region Init Server
        public static async Task InitServer(IConfiguration configuration, string version)
        {
            var assembly = Assembly.Load("Siemens.PanelManager.Model");
            var types = assembly.GetTypes();
            Dictionary<string, DatabaseModelInfo> dataTypeDicionary = new Dictionary<string, DatabaseModelInfo>();
            foreach (var dataType in types)
            {
                var attr = dataType.GetCustomAttribute<SugarTable>();

                if (attr != null)
                {
                    dataTypeDicionary.TryAdd(dataType.Name.ToLower(), new DatabaseModelInfo(dataType, attr.TableName));
                }
            }
            var runCount = 0;
            var isFinish = false;
            var connStr = configuration?.GetConnectionString("default");
            if (connStr == null)
            {
                LogHelper.Error("数据库缺少配置");
                return;
            }

            do
            {
                try
                {
                    var configObj = DatabaseHelper.GetConnectionConfig(connStr);
                    using (var client = new SqlSugarClient(configObj))
                    {
                        if (client != null)
                        {
                            client.Aop.OnError = (sqlException) =>
                            {
                                LogHelper.Error($"Error sql: {sqlException.Sql}");
                            };
                            client.DbMaintenance.CreateDatabase();
                            client.CodeFirst.InitTables<InstallLog>();
                            var basePath = Directory.GetCurrentDirectory();
                            var dirPath = Path.Combine(basePath, "DatabaseInitializeFile");

                            var influxDBConfigPath = Path.Combine(basePath, "InfluxDBConfigFile");
                            bool needUpdateInfluxDB = false;

                            var files = Directory.GetFiles(dirPath);
                            var md5 = System.Security.Cryptography.MD5.Create();
                            var installLog = await client.Queryable<InstallLog>().OrderByDescending(o => o.Id).FirstAsync();
                            var oldFileHash = new Dictionary<string, string>();
                            var newFileHash = new Dictionary<string, string>();
                            bool needUpdated = false;
                            if (installLog != null && !string.IsNullOrEmpty(installLog.FileHash))
                            {
                                var fileHashList = JsonConvert.DeserializeObject<Dictionary<string, string>>(installLog.FileHash);
                                if (fileHashList != null)
                                {
                                    oldFileHash = fileHashList;
                                }

                                if (installLog.Version != version)
                                {
                                    needUpdated = true;
                                }
                            }
                            else
                            {
                                installLog = new InstallLog()
                                {
                                    LogTime = DateTime.Now,
                                    Version = version,
                                    FileHash = string.Empty
                                };

                                needUpdated = true;
                            }
                            InitTables(client);
                            var sqlFile = files.Where(f => Regex.IsMatch(f, "\\.sql$")).ToArray();
                            foreach (var sql in sqlFile)
                            {
                                var fileInfo = new FileInfo(sql);
                                var fileReadStream = fileInfo.OpenRead();
                                var currentHash = Convert.ToBase64String(md5.ComputeHash(fileReadStream));

                                bool needRead = true;
                                if (oldFileHash.TryGetValue(fileInfo.Name, out string? hash) && !string.IsNullOrEmpty(hash))
                                {
                                    needRead = hash != currentHash;
                                    if (needRead)
                                    {
                                        needUpdated = true;
                                    }
                                }
                                else
                                {
                                    needRead = true;
                                    needUpdated = true;
                                }
                                newFileHash.TryAdd(fileInfo.Name, currentHash);
                                if (needRead)
                                {
                                    var sqlStr = System.IO.File.ReadAllText(sql);
                                    try
                                    {
                                        client.Ado.ExecuteCommand(sqlStr);
                                    }
                                    catch (Exception ex)
                                    {
                                        LogHelper.Error(ex);
                                    }
                                }
                            }

                            var jsonFiles = Directory.GetFiles(influxDBConfigPath, "*.json");
                            var influxDBList = new List<string>();
                            foreach (var jsonFile in jsonFiles)
                            {
                                var fileInfo = new FileInfo(jsonFile);
                                var fileReadStream = fileInfo.OpenRead();
                                var currentHash = Convert.ToBase64String(md5.ComputeHash(fileReadStream));

                                var influxDBFileName = $"influx/{fileInfo.Name}";
                                newFileHash.TryAdd(influxDBFileName, currentHash);
                                if (oldFileHash.TryGetValue(influxDBFileName, out string? hash) && !string.IsNullOrEmpty(hash))
                                {
                                    if (hash != currentHash)
                                    {
                                        influxDBList.Add(jsonFile);
                                        needUpdateInfluxDB = true;
                                    }
                                }
                                else
                                {
                                    needUpdateInfluxDB = true;
                                    influxDBList.Add(jsonFile);
                                }
                            }

                            

                            Dictionary<int, int> userIdMapping = new Dictionary<int, int>();
                            Dictionary<int, int> roleIdMapping = new Dictionary<int, int>();
                            Dictionary<int, int> pageIdMapping = new Dictionary<int, int>();

                            List<UserRoleMapping> userRoleActions = new List<UserRoleMapping>();
                            List<RolePageMapping> rolePageActions = new List<RolePageMapping>();
                            files = files.Where(f => Regex.IsMatch(f, "\\.json$")).ToArray();
                            foreach (var file in files)
                            {
                                var fileInfo = new FileInfo(file);
                                var fileReadStream = fileInfo.OpenRead();
                                var currentHash = Convert.ToBase64String(md5.ComputeHash(fileReadStream));

                                bool needRead = true;
                                if (oldFileHash.TryGetValue(fileInfo.Name, out string? hash) && !string.IsNullOrEmpty(hash))
                                {
                                    needRead = hash != currentHash;
                                    if (needRead)
                                    {
                                        needUpdated = true;
                                    }
                                }
                                else
                                {
                                    needRead = true;
                                    needUpdated = true;
                                }

                                newFileHash.TryAdd(fileInfo.Name, currentHash);

                                var fileName = fileInfo.Name;
                                var match = Regex.Match(fileInfo.Name, "^([(\\w)]+)(-([\\d]*))?");
                                if (match.Success)
                                {
                                    fileName = match.Groups[1].Value;
                                }
                                switch (fileName.ToLower())
                                {
                                    case "user":
                                        {
                                            var str = System.IO.File.ReadAllText(file);
                                            var users = JsonConvert.DeserializeObject<List<User>>(str);
                                            if (users != null)
                                            {
                                                users.ForEach(u => u.LoginName = u.LoginName.ToLower());
                                                var userLoginNames = users.Select(x => x.LoginName).ToArray();
                                                var existUser = await client.Queryable<User>().Where(u => userLoginNames.Contains(u.LoginName)).ToArrayAsync();
                                                if (existUser.Length > 0)
                                                {
                                                    foreach (var user in existUser)
                                                    {
                                                        var fileUser = users.FirstOrDefault(u => u.LoginName == user.LoginName);
                                                        if (fileUser != null)
                                                        {
                                                            userIdMapping.TryAdd(fileUser.Id, user.Id);
                                                            users.Remove(fileUser);
                                                        }
                                                    }
                                                }

                                                if (needRead)
                                                {
                                                    foreach (var user in users)
                                                    {
                                                        var oldId = user.Id;
                                                        user.CreatedBy = UpdatedBy;
                                                        user.UpdatedBy = UpdatedBy;
                                                        user.CreatedTime = DateTime.Now;
                                                        user.UpdatedTime = DateTime.Now;
                                                        var newId = client.Insertable(user).ExecuteReturnIdentity();
                                                        userIdMapping.TryAdd(oldId, newId);
                                                    }
                                                }
                                            }
                                            break;
                                        }
                                    case "electricityconfig":
                                        {
                                            var str = File.ReadAllText(file);
                                            var electricityConfigs = JsonConvert.DeserializeObject<List<ElectricityConfig>>(str);
                                            if (electricityConfigs!=null&&needUpdated)
                                            {
                                                var existData = await client.Queryable<ElectricityConfig>().ToArrayAsync();
                                                if(!existData.Any())
                                                {
                                                    foreach(var d in electricityConfigs)
                                                    {
                                                        d.CreatedBy = UpdatedBy;
                                                        d.UpdatedBy = UpdatedBy;
                                                        d.CreatedTime = DateTime.Now;
                                                        d.UpdatedTime = DateTime.Now;
                                                        var newId = client.Insertable(d).ExecuteReturnIdentity();
                                                    }
                                                }
                                            }
                                            break;
                                        }
                                    case "billingscheme":
                                        {
                                            
                                            var str = File.ReadAllText(file);
                                            var billingConfigs = JsonConvert.DeserializeObject<List<BillingScheme>>(str);
                                            if (billingConfigs != null && needUpdated)
                                            {
                                                var existData = await client.Queryable<BillingScheme>().ToArrayAsync();
                                                if (!existData.Any())
                                                {
                                                    foreach (var d in billingConfigs)
                                                    {
                                                        d.CreatedBy = UpdatedBy;
                                                        d.UpdatedBy = UpdatedBy;
                                                        d.CreatedTime = DateTime.Now;
                                                        d.UpdatedTime = DateTime.Now;
                                                        var newId = client.Insertable(d).ExecuteReturnIdentity();
                                                    }
                                                }
                                            }
                                            break;
                                        }
                                    case "role":
                                        {
                                            var str = File.ReadAllText(file);
                                            var roles = JsonConvert.DeserializeObject<List<Role>>(str);
                                            if (roles != null)
                                            {
                                                var roleNames = roles.Select(x => x.RoleCode).ToArray();
                                                var existRole = await client.Queryable<Role>().Where(u => roleNames.Contains(u.RoleCode)).ToArrayAsync();
                                                if (existRole.Length > 0)
                                                {
                                                    foreach (var role in existRole)
                                                    {
                                                        var fileRole = roles.FirstOrDefault(u => u.RoleCode == role.RoleCode);
                                                        if (fileRole != null)
                                                        {
                                                            roleIdMapping.TryAdd(fileRole.Id, role.Id);
                                                        }
                                                    }
                                                }

                                                if (needUpdated)
                                                {
                                                    foreach (var role in roles)
                                                    {
                                                        var oldRole = existRole.FirstOrDefault(u => u.RoleCode == role.RoleCode);
                                                        if (oldRole != null)
                                                        {
                                                            oldRole.UpdatedBy = UpdatedBy;
                                                            oldRole.UpdatedTime = DateTime.Now;
                                                            oldRole.RoleName = role.RoleName;
                                                            await client.Updateable(oldRole).ExecuteCommandAsync();
                                                        }
                                                        else
                                                        {
                                                            var oldId = role.Id;
                                                            role.CreatedBy = UpdatedBy;
                                                            role.UpdatedBy = UpdatedBy;
                                                            role.CreatedTime = DateTime.Now;
                                                            role.UpdatedTime = DateTime.Now;
                                                            var newId = client.Insertable(role).ExecuteReturnIdentity();
                                                            roleIdMapping.TryAdd(oldId, newId);
                                                        }
                                                    }
                                                }
                                            }
                                            break;
                                        }
                                    case "page":
                                        {
                                            var str = File.ReadAllText(file);
                                            var pages = JsonConvert.DeserializeObject<List<Page>>(str);
                                            if (pages != null && pages.Count > 0)
                                            {
                                                foreach (var page in pages)
                                                {
                                                    var existPage = await client.Queryable<Page>().Where(u => page.Id == u.Id).FirstAsync();
                                                    if (existPage != null)
                                                    {
                                                        existPage.PageLevel = page.PageLevel;
                                                        existPage.PageName = page.PageName;
                                                        existPage.PageCode = page.PageCode;
                                                        existPage.OrderNo = page.OrderNo;
                                                        existPage.PageDescription = page.PageDescription;
                                                        existPage.IsSystemConfig = page.IsSystemConfig;
                                                        existPage.UpdatedBy = UpdatedBy;
                                                        existPage.UpdatedTime = DateTime.Now;
                                                        client.Updateable(existPage).ExecuteCommand();
                                                    }
                                                    else
                                                    {
                                                        page.CreatedBy = UpdatedBy;
                                                        page.UpdatedBy = UpdatedBy;
                                                        page.CreatedTime = DateTime.Now;
                                                        page.UpdatedTime = DateTime.Now;
                                                        client.Insertable(page).ExecuteCommand();
                                                    }
                                                }
                                            }
                                            break;
                                        }
                                    case "rolepage":
                                        {
                                            if (needUpdated)
                                            {
                                                var str = File.ReadAllText(file);
                                                var rolePages = JsonConvert.DeserializeObject<List<RolePageMapping>>(str);
                                                if (rolePages != null)
                                                {
                                                    rolePageActions.AddRange(rolePages);
                                                }
                                            }
                                            break;
                                        }
                                    case "userrole":
                                        {
                                            if (needUpdated)
                                            {
                                                var str = File.ReadAllText(file);
                                                var rolePages = JsonConvert.DeserializeObject<List<UserRoleMapping>>(str);
                                                if (rolePages != null)
                                                {
                                                    userRoleActions.AddRange(rolePages);
                                                }
                                            }
                                            break;
                                        }
                                    case "systemstaticmodel":
                                        {
                                            if (needRead)
                                            {
                                                var str = File.ReadAllText(file);
                                                var staticModels = JsonConvert.DeserializeObject<List<SystemStaticModel>>(str);
                                                if (staticModels != null)
                                                {
                                                    foreach (var m in staticModels)
                                                    {
                                                        m.Type = m.Type.ToUpper();
                                                        var query = client.Queryable<SystemStaticModel>().Where(a => a.Code == m.Code && a.Type == m.Type);
                                                        SystemStaticModel? model = null;
                                                        if (!string.IsNullOrEmpty(m.Extend))
                                                        {
                                                            var extendObject = JObject.Parse(m.Extend);
                                                            if (extendObject.ContainsKey("ModelByType"))
                                                            {
                                                                var value = extendObject.GetValue("ModelByType")!.Value<string>();
                                                                if (!string.IsNullOrEmpty(value))
                                                                {
                                                                    var list = await query.ToListAsync();
                                                                    model = list.FirstOrDefault(m => m.Extend?.Contains($"{value}", StringComparison.CurrentCultureIgnoreCase) ?? false);
                                                                }
                                                            }
                                                            else
                                                            {
                                                                model = await query.FirstAsync();
                                                            }
                                                        }
                                                        else
                                                        {
                                                            model = await query.FirstAsync();
                                                        }

                                                        if (model != null)
                                                        {
                                                            model.Name = m.Name;
                                                            model.Extend = m.Extend;
                                                            model.Sort = m.Sort;
                                                            model.UpdatedBy = UpdatedBy;
                                                            model.UpdatedTime = DateTime.Now;
                                                            await client.Updateable(model).ExecuteCommandAsync();
                                                        }
                                                        else
                                                        {
                                                            m.CreatedBy = UpdatedBy;
                                                            m.CreatedTime = DateTime.Now;
                                                            m.UpdatedBy = UpdatedBy;
                                                            m.UpdatedTime = DateTime.Now;
                                                            await client.Insertable(m).ExecuteCommandAsync();
                                                        }
                                                    }
                                                }
                                            }
                                            break;
                                        }
                                    case "assetdashboardconfig":
                                        {
                                            if (needRead)
                                            {
                                                var str = File.ReadAllText(file);
                                                var assetDashboardConfigs = JsonConvert.DeserializeObject<List<AssetDashboardConfig>>(str);
                                                if (assetDashboardConfigs != null)
                                                {
                                                    assetDashboardConfigs.ForEach(a => a.ConfigName = a.ConfigName.ToUpper());
                                                    foreach (var c in assetDashboardConfigs)
                                                    {
                                                        var existsConfig = await client.Queryable<AssetDashboardConfig>().FirstAsync(e => e.ConfigName == c.ConfigName && e.SubKey == c.SubKey);
                                                        if (existsConfig != null)
                                                        {
                                                            existsConfig.Sql = c.Sql;
                                                            existsConfig.Extend = c.Extend;
                                                            existsConfig.DashboardType = c.DashboardType;
                                                            existsConfig.UpdatedBy = UpdatedBy;
                                                            existsConfig.UpdatedTime = DateTime.Now;
                                                            await client.Updateable(existsConfig).ExecuteCommandAsync();
                                                        }
                                                        else
                                                        {
                                                            c.CreatedBy = UpdatedBy;
                                                            c.CreatedTime = DateTime.Now;
                                                            c.UpdatedBy = UpdatedBy;
                                                            c.UpdatedTime = DateTime.Now;
                                                            await client.Insertable(c).ExecuteCommandAsync();
                                                        }
                                                    }
                                                }
                                            }
                                            break;
                                        }
                                    case "assetknowledge":
                                        {
                                            if (needRead)
                                            {
                                                var str = File.ReadAllText(file);
                                                var assetKnowledgeList = JsonConvert.DeserializeObject<List<AssetKnowledge>>(str);

                                                if (assetKnowledgeList != null && assetKnowledgeList.Any())
                                                {
                                                    await client.Deleteable<AssetKnowledge>()
                                                        .Where(a => a.AssetModel == assetKnowledgeList[0].AssetModel).ExecuteCommandAsync();
                                                    assetKnowledgeList.ForEach(a =>
                                                    {
                                                        a.CreatedBy = UpdatedBy;
                                                        a.CreatedTime = DateTime.Now;
                                                        a.UpdatedBy = UpdatedBy;
                                                        a.UpdatedTime = DateTime.Now;
                                                    });

                                                    await client.Insertable(assetKnowledgeList).ExecuteCommandAsync();
                                                }

                                            }
                                        }
                                        break;
                                    case "assetdatapointdirectory":
                                        {
                                            if (needRead)
                                            {
                                                var str = File.ReadAllText(file);
                                                var assetDataPointDirectorys = JsonConvert.DeserializeObject<List<AssetDataPointDirectory>>(str);

                                                if (assetDataPointDirectorys != null && assetDataPointDirectorys.Any())
                                                {
                                                    if (assetDataPointDirectorys[0].AssetLevel == AssetLevel.Device)
                                                    {
                                                        await client.Deleteable<AssetDataPointDirectory>()
                                                            .Where(a => a.AssetModel == assetDataPointDirectorys[0].AssetModel)
                                                            .ExecuteCommandAsync();
                                                    }
                                                    else
                                                    {

                                                        await client.Deleteable<AssetDataPointDirectory>()
                                                            .Where(a => a.AssetLevel == assetDataPointDirectorys[0].AssetLevel)
                                                            .ExecuteCommandAsync();
                                                    }

                                                    assetDataPointDirectorys.ForEach(a =>
                                                    {
                                                        a.CreatedBy = UpdatedBy;
                                                        a.CreatedTime = DateTime.Now;
                                                        a.UpdatedBy = UpdatedBy;
                                                        a.UpdatedTime = DateTime.Now;
                                                    });

                                                    await client.Insertable(assetDataPointDirectorys).ExecuteCommandAsync();
                                                }
                                            }
                                        }
                                        break;
                                    case "assetdatapointinfo":
                                        {
                                            if (needRead)
                                            {
                                                var str = File.ReadAllText(file);
                                                var assetDataPointInfos = JsonConvert.DeserializeObject<List<AssetDataPointInfo>>(str);

                                                if (assetDataPointInfos != null && assetDataPointInfos.Any())
                                                {
                                                    if (assetDataPointInfos[0].AssetLevel == AssetLevel.Device)
                                                    {
                                                        await client.Deleteable<AssetDataPointInfo>()
                                                            .Where(a => a.AssetModel == assetDataPointInfos[0].AssetModel)
                                                            .ExecuteCommandAsync();
                                                    }
                                                    else
                                                    {

                                                        await client.Deleteable<AssetDataPointInfo>()
                                                            .Where(a => a.AssetLevel == assetDataPointInfos[0].AssetLevel)
                                                            .ExecuteCommandAsync();
                                                    }

                                                    assetDataPointInfos.ForEach(a =>
                                                    {
                                                        a.CreatedBy = UpdatedBy;
                                                        a.CreatedTime = DateTime.Now;
                                                        a.UpdatedBy = UpdatedBy;
                                                        a.UpdatedTime = DateTime.Now;
                                                    });

                                                    await client.Insertable(assetDataPointInfos).ExecuteCommandAsync();
                                                }
                                            }
                                        }
                                        break;
                                    default:
                                        if (needRead)
                                        {
                                            await LoadData(client, fileInfo, dataTypeDicionary);
                                        }
                                        break;
                                }
                            }

                            if (userRoleActions.Count > 0)
                            {
                                foreach (var userRole in userRoleActions)
                                {
                                    if (userIdMapping.TryGetValue(userRole.UserId, out var userId))
                                    {
                                        userRole.UserId = userId;
                                    }

                                    if (roleIdMapping.TryGetValue(userRole.RoleId, out var roleId))
                                    {
                                        userRole.RoleId = roleId;
                                    }
                                    if (userRole.UserId > 0 && userRole.RoleId > 0)
                                    {
                                        if (!await client.Queryable<UserRoleMapping>().AnyAsync(urm => urm.UserId == userRole.UserId && urm.RoleId == userRole.RoleId))
                                        {
                                            userRole.CreatedBy = UpdatedBy;
                                            userRole.UpdatedBy = UpdatedBy;
                                            userRole.CreatedTime = DateTime.Now;
                                            userRole.UpdatedTime = DateTime.Now;
                                            await client.Insertable(userRole).ExecuteCommandAsync();
                                        }
                                        else
                                        {
                                            LogHelper.Debug($"UerRole 插入失败 UserId:{userRole.UserId}  RoleId:{userRole.RoleId}");
                                        }
                                    }
                                    else
                                    {
                                        LogHelper.Debug($"UerRole 插入失败 UserId:{userRole.UserId}  RoleId:{userRole.RoleId}");
                                    }
                                }
                            }

                            if (rolePageActions.Count > 0)
                            {
                                foreach (var rolePage in rolePageActions)
                                {
                                    if (roleIdMapping.TryGetValue(rolePage.RoleId, out var roleId))
                                    {
                                        rolePage.RoleId = roleId;
                                    }

                                    if (pageIdMapping.TryGetValue(rolePage.PageId, out var pageId))
                                    {
                                        rolePage.PageId = pageId;
                                    }
                                    if (rolePage.RoleId > 0 && rolePage.PageId > 0)
                                    {
                                        if (!await client.Queryable<RolePageMapping>().AnyAsync(rpm => rpm.RoleId == rolePage.RoleId && rpm.PageId == rolePage.PageId))
                                        {
                                            rolePage.CreatedBy = UpdatedBy;
                                            rolePage.UpdatedBy = UpdatedBy;
                                            rolePage.CreatedTime = DateTime.Now;
                                            rolePage.UpdatedTime = DateTime.Now;
                                            await client.Insertable(rolePage).ExecuteCommandAsync();
                                        }
                                        else
                                        {
                                            LogHelper.Debug($"RolePage 插入失败 RoleId:{rolePage.RoleId}  PageId:{rolePage.PageId}");
                                        }
                                    }
                                    else
                                    {
                                        LogHelper.Debug($"RolePage 插入失败 RoleId:{rolePage.RoleId}  PageId:{rolePage.PageId}");
                                    }
                                }
                            }

                            if (needUpdated || needUpdateInfluxDB)
                            {
                                await client.Insertable(new InstallLog()
                                {
                                    LogTime = DateTime.Now,
                                    FileHash = JsonConvert.SerializeObject(newFileHash),
                                    Version = version
                                }).ExecuteCommandAsync();
                            }

                            #region 系统自带文件加载
                            await LoadSystemFile(UpdatedBy, client, basePath);
                            #endregion

                            #region 加载测试数据
                            if (configuration != null)
                            {
                                var section = configuration.GetSection("TestEnv");
                                if (section.Exists() && "True".Equals(section.Value, StringComparison.OrdinalIgnoreCase))
                                {
                                    var dataFilePath = Path.Combine(basePath, "DatabaseInitializeFile", "TestData");
                                    if (Directory.Exists(dataFilePath))
                                    {
                                        var testDataFiles = Directory.GetFiles(dataFilePath, "*.json");
                                        foreach (var file in testDataFiles)
                                        {
                                            var fileInfo = new FileInfo(file);
                                            await LoadData(client, fileInfo, dataTypeDicionary);
                                        }
                                    }
                                }
                            }
                            #endregion

                            #region 加载Influx db Task
                            if (needUpdateInfluxDB && configuration != null)
                            {
                                var influxConfig = configuration.GetSection(InfluxDBConfig.Influxdb).Get<InfluxDBConfig>();
                                if (influxConfig != null)
                                {
                                    await InitInfluxDBTask(influxConfig, influxDBList);
                                }
                            }
                            #endregion
                        }

                        isFinish = true;
                    }
                }
                catch (Exception ex)
                {
                    runCount++;
                    if (runCount >= 3)
                    {
                        LogHelper.Error(ex);
                    }
                    else
                    {
                        await Task.Delay(500);
                    }
                }
            }
            while (!isFinish);
        }

        private static async Task LoadData(ISqlSugarClient client, FileInfo fileInfo, Dictionary<string, DatabaseModelInfo> dataTypeDicionary)
        {
            var match = Regex.Match(fileInfo.Name, "^([(\\w)]+)(-([\\d]*))?");
            if (match.Success)
            {
                var fileName = match.Groups[1].Value;
                fileName = fileName.ToLower();
                if (dataTypeDicionary.TryGetValue(fileName, out DatabaseModelInfo? modelInfo)
                    && modelInfo != null)
                {
                    string fileStr;
                    using (var sr = new StreamReader(fileInfo.FullName))
                    {
                        fileStr = sr.ReadToEnd();
                    }

                    var listType = typeof(List<>);
                    listType = listType.MakeGenericType(modelInfo.DataType);
                    var obj = JsonConvert.DeserializeObject(fileStr, listType);

                    if (obj != null && obj is System.Collections.IList listData)
                    {
                        foreach (var d in listData)
                        {
                            try
                            {
                                var item = await modelInfo.CheckIsExists(d, client);
                                if (item == null)
                                {
                                    await modelInfo.InsertValues(d, client);
                                }
                                else
                                {
                                    await modelInfo.UpdateValue(d, item, client);
                                }
                            }
                            catch (Exception ex)
                            {
                                LogHelper.Error("加载数据保错", ex);
                            }
                        }
                    }
                }
            }
        }

        private static async Task LoadSystemFile(string UpdatedBy, ISqlSugarClient client, string basePath)
        {
            var systemFilePath = Path.Combine(basePath, "wwwroot", "uploadfiles", "systemfile");
            var fs = Directory.GetFiles(systemFilePath);
            var fileManagers = new List<FileManager>();
            if (fs.Length > 0)
            {
                foreach (var f in fs)
                {
                    var fileInfo = new FileInfo(f);
                    var fileName = fileInfo.Name.Substring(0, fileInfo.Name.Length - fileInfo.Extension.Length);
                    var fileExtension = fileInfo.Extension.Substring(1, fileInfo.Extension.Length - 1);
                    var fileCode = fileName.ToUpper();
                    if (!await client.Queryable<FileManager>().AnyAsync(fm => fm.IsSystemFile && fm.Code == fileCode))
                    {
                        fileManagers.Add(new FileManager()
                        {
                            Name = fileName,
                            Code = fileCode,
                            FileType = fileExtension,
                            IsSystemFile = true,
                            Url = $"/uploadfiles/systemfile/{fileName}.{fileExtension}",
                            CreatedBy = UpdatedBy,
                            UpdatedBy = UpdatedBy,
                            CreatedTime = DateTime.Now,
                            UpdatedTime = DateTime.Now
                        });
                    }
                }
            }

            var languageList = Directory.GetDirectories(systemFilePath);
            foreach (var languagePath in languageList)
            {
                var dir = new DirectoryInfo(languagePath);
                var language = dir.Name;
                if (!string.IsNullOrEmpty(language))
                {
                    var lfs = Directory.GetFiles(languagePath);

                    foreach (var f in lfs)
                    {
                        var fileInfo = new FileInfo(f);
                        var fileName = fileInfo.Name.Substring(0, fileInfo.Name.Length - fileInfo.Extension.Length);
                        var fileExtension = fileInfo.Extension.Substring(1, fileInfo.Extension.Length - 1);
                        var fileCode = fileName.ToUpper();
                        if (!await client.Queryable<FileManager>().AnyAsync(fm => fm.IsSystemFile
                        && fm.Code == fileCode && fm.Language == language))
                        {
                            fileManagers.Add(new FileManager()
                            {
                                Name = fileName,
                                Code = fileCode,
                                FileType = fileExtension,
                                IsSystemFile = true,
                                Url = $"/uploadfiles/systemfile/{language}/{fileName}.{fileExtension}",
                                Language = language,
                                CreatedBy = UpdatedBy,
                                UpdatedBy = UpdatedBy,
                                CreatedTime = DateTime.Now,
                                UpdatedTime = DateTime.Now
                            });
                        }
                    }
                }
            }


            if (fileManagers.Count > 0)
            {
                await client.Insertable(fileManagers).ExecuteCommandAsync();
            }
        }

        private static async Task InitInfluxDBTask(InfluxDBConfig config, List<string> influxDBList)
        {
            await InfluxDBClientFactory.LoadConfig(config);
            var client = InfluxDBClientFactory.CreateClient();

            var existTasks = await client.GetTaskListAsync();
            foreach (var path in influxDBList)
            {
                var str = File.ReadAllText(path);

                var taskType = JsonConvert.DeserializeObject<TaskType>(str);
                if (taskType != null)
                {
                    var existTask = existTasks.FirstOrDefault(t => t.Name == taskType.Name);
                    if (existTask != null)
                    {
                        existTask.Status = taskType.Status;
                        existTask.Flux = taskType.Flux;
                        existTask.Every = taskType.Every;
                        existTask.Cron = taskType.Cron;
                        existTask.Offset = taskType.Offset;
                        await client.UpdateTaskAsync(existTask);
                    }
                    else
                    {
                        await client.AddTaskAsync(taskType);
                    }
                }
            }
        }
        #endregion

        private static void InitTables(SqlSugarClient client)
        {
            var modelAssembly = Assembly.Load("Siemens.PanelManager.Model");

            var databaseModel = modelAssembly.GetTypes();
            var baseType = typeof(IPanelDataTable);
            var logicDataBaseType = typeof(LogicDataBase);
            var typeList = new List<Type>();
            foreach (var type in databaseModel)
            {
                if (type == baseType
                    || type == logicDataBaseType
                    || !baseType.IsAssignableFrom(type))
                    continue;
                if ("PowerBreaker".Equals(type.Name))
                    continue;
                typeList.Add(type);
            }

            client.CodeFirst.InitTables(typeList.ToArray());
        }
    }
}

