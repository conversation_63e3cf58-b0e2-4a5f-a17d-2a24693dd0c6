﻿using Siemens.PanelManager.Model.Chart;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class CurrentPannelHealthResult
    {
        public decimal? Score { get; set; }
        public string? Grade { get; set; }
        public int? Count { get; set; }
        public AbnormalIndicatorResult[]? AbnormalIndicators { get; set; }
        public PanelHealthModel[]? IntercatorData { get; set; }
        public List<string> Sugsstions { get; set; }
        public string ReportTime { get; set; }
    }

    public class AbnormalIndicatorResult
    {
        public string Name { get; set; } = string.Empty;
        public decimal Percentage { get; set; } = 0m;
    }
    public class PanelHealthStatisticsResult
    {
        public PieChartModel chartModel { get; set; }
        public Dictionary<int, string> repairedList { get; set; }
    }
}
