﻿using Newtonsoft.Json;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.UDC;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.UdcScanData
{
    [SugarTable("udc_scan_data")]
    public class UdcScanData : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [SugarColumn(ColumnName = "asset_id", IsNullable = true)]
        public int? AssetId { get; set; }

        [SugarColumn(ColumnName = "asset_name", IsNullable = true, Length = 256)]
        public string? AssetName { get; set; }

        [SugarColumn(ColumnName = "asset_type", IsNullable = true, Length = 50)]
        public string? AssetType { get; set; }

        [SugarColumn(ColumnName = "asset_model", IsNullable = true, Length = 50)]
        public string? AssetModel { get; set; }

        [SugarColumn(ColumnName = "asset_number", IsNullable = true, Length = 256)]
        public string? AssetNumber { get; set; }

        [SugarColumn(ColumnName = "asset_ip_address", IsNullable = true, Length = 50)]
        public string? AssetIpAddress { get; set; }

        [SugarColumn(ColumnName = "object_id", IsNullable = true, Length = 50)]
        public string? ObjectId { get; set; }

        [SugarColumn(ColumnName = "udc_network_interface", IsNullable = true, Length = 50)]
        public string? UdcNetworkInterface { get; set; }

        [SugarColumn(ColumnName = "type_name", IsNullable = true, Length = 256)]
        public string? TypeName { get; set; }

        [SugarColumn(ColumnName = "type_display_name", IsNullable = true, Length = 256)]
        public string? TypeDisplayName { get; set; }

        [SugarColumn(ColumnName = "ip_address", IsNullable = true, Length = 256)]
        public string? IpAddress { get; set; }

        [SugarColumn(ColumnName = "port", IsNullable = true)]
        public int? Port { get; set; }

        [SugarColumn(ColumnName = "netmask", IsNullable = true, Length = 256)]
        public string? Netmask { get; set; }

        [SugarColumn(ColumnName = "gateway", IsNullable = true, Length = 256)]
        public string? Gateway { get; set; }

        [SugarColumn(ColumnName = "mac_address", IsNullable = true, Length = 256)]
        public string? MacAddress { get; set; }

        [SugarColumn(ColumnName = "plant_identifier", IsNullable = true, Length = 256)]
        public string? PlantIdentifier { get; set; }

        [SugarColumn(ColumnName = "order_number", IsNullable = true, Length = 256)]
        public string? OrderNumber { get; set; }

        [SugarColumn(ColumnName = "firmware_version", IsNullable = true, Length = 256)]
        public string? FirmwareVersion { get; set; }

        [SugarColumn(ColumnName = "bootloader_version", IsNullable = true, Length = 256)]
        public string? BootloaderVersion { get; set; }

        [SugarColumn(ColumnName = "item_id", IsNullable = true, Length = 256)]
        public string? ItemId { get; set; }

        [SugarColumn(ColumnName = "is_gateway", IsNullable = true)]
        public bool IsGateway { get; set; }

        [SugarColumn(ColumnName = "unit_id", IsNullable = true)]
        public int? UnitId { get; set; }

        [SugarColumn(ColumnName = "import_status", IsNullable = true)]
        public int? ImportStatus { get; set; }

        [SugarColumn(ColumnName = "interface_name", IsNullable = true, Length = 256)]
        public string? InterfaceName { get; set; }
    }
}
