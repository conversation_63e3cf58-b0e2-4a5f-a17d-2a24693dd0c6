﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.Model.UDC
{
    public class UdcMessageModelListResult : UdcListResulBase<UdcMessageModel>
    {

    }

    public class UdcMessageModel : IUdcData
    {
        private const string OidJsonName = "oid";
        private const string TypeIdJsonName = "type_id";
        private const string CultureJsonName = "culture";
        private const string CategoryJsonName = "category";
        private const string DisplayCategoryJsonName = "display_category";
        private const string SeverityJsonName = "severity";
        private const string DisplaySeverityJsonName = "display_severity";
        private const string TimestampJsonName = "timestamp";
        private const string DisplayTimestampJsonName = "display_timestamp";
        private const string TimestampQualityJsonName = "timestamp_quality";
        private const string DisplayTextJsonName = "display_text";
        private const string DetailsJsonName = "details";

        [JsonProperty(PropertyName = OidJsonName, NullValueHandling = NullValueHandling.Ignore)]
        public string Oid { get; set; } = string.Empty;

        [JsonProperty(PropertyName = TypeIdJsonName, NullValueHandling = NullValueHandling.Ignore)]
        public int? EventId { get; set; }

        [JsonProperty(PropertyName = CultureJsonName, NullValueHandling = NullValueHandling.Ignore)]
        public string Culture { get; set; } = string.Empty;

        [JsonProperty(PropertyName = CategoryJsonName, NullValueHandling = NullValueHandling.Ignore)]
        public string? EventCategory { get; set; }

        [JsonProperty(PropertyName = DisplayCategoryJsonName, NullValueHandling = NullValueHandling.Ignore)]
        public string? DisplayEventCategory { get; set; }

        [JsonProperty(PropertyName = SeverityJsonName, NullValueHandling = NullValueHandling.Ignore)]
        public string? Severity { get; set; }

        [JsonProperty(PropertyName = DisplaySeverityJsonName, NullValueHandling = NullValueHandling.Ignore)]
        public string DisplaySeverity { get; set; } = string.Empty;

        [JsonProperty(PropertyName = TimestampJsonName, NullValueHandling = NullValueHandling.Ignore)]
        public string Timestamp { get; set; } = string.Empty;

        [JsonProperty(PropertyName = DisplayTimestampJsonName, NullValueHandling = NullValueHandling.Ignore)]
        public string DisplayTimestamp { get; set; } = string.Empty;

        [JsonProperty(PropertyName = TimestampQualityJsonName, NullValueHandling = NullValueHandling.Ignore)]
        public string? TimestampQuality { get; set; }

        [JsonProperty(PropertyName = DisplayTextJsonName, NullValueHandling = NullValueHandling.Ignore)]
        public string DisplayText { get; set; } = string.Empty;

        [JsonProperty(PropertyName = DetailsJsonName, NullValueHandling = NullValueHandling.Ignore)]
        public List<UdcMessageDetailsModel>? Details { get; set; }

        [JsonIgnore]
        public DateTime Time
        {
            get
            {
                if (string.IsNullOrEmpty(Timestamp))
                {
                    return DateTime.MinValue;
                }
                if (DateTime.TryParse(Timestamp, out DateTime time))
                {
                    return time;
                }
                return DateTime.MinValue;
            }
        }
    }

    public class UdcMessageDetailsModel
    {
        [JsonProperty(PropertyName = "display_category")]
        public string DisplayCategory { get; set; } = string.Empty;

        [JsonProperty(PropertyName = "entries")]
        public List<UdcMessageDetailsEntryModel> Entries { get; set; } = new List<UdcMessageDetailsEntryModel>();
    }

    public class UdcMessageDetailsEntryModel
    {
        [JsonProperty(PropertyName = "display_name")]
        public string DisplayName { get; set; } = string.Empty;

        [JsonProperty(PropertyName = "display_value")]
        public string DisplayValue { get; set; } = string.Empty;
    }
}
