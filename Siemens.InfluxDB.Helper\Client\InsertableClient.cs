﻿using InfluxDB.Client;
using InfluxDB.Client.Api.Domain;
using Siemens.InfluxDB.Helper.Interface;
using IInfluxDBClient = InfluxDB.Client.IInfluxDBClient;

namespace Siemens.InfluxDB.Helper.Client
{
    public class InsertableClient<T> : IInsertable<T>
        where T : IInfluxData
    {
        private IWriteApi _writeApi;
        private Bucket _bucket;
        private Organization _organization;

        internal InsertableClient(IInfluxDBClient client, Organization organization, Bucket bucket)
        {
            _writeApi = client.GetWriteApi();
            _organization = organization;
            _bucket = bucket;
        }
        public void Dispose()
        {
            _writeApi.Dispose();
        }

        public void Insert(T data, WritePrecision precision = WritePrecision.Ms)
        {
            _writeApi.WriteMeasurement<T>(data, precision, _bucket.Name, _organization.Id);
        }

        public void Insert(List<T> data, WritePrecision precision = WritePrecision.Ms)
        {
            _writeApi.WriteMeasurements<T>(data, precision, _bucket.Name, _organization.Id);
        }
    }
}
