﻿using Akka.Util.Internal;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using Org.BouncyCastle.Tls;
using RestSharp;
using Siemens.PanelManager.Common;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.Model.Alarm;
using Siemens.PanelManager.Model.Chart;
using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Job;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.WebApi.Models;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Collections;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;
using System.Text;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class AlarmController : SiemensApiControllerBase
    {
        private ILogger<AlarmController> _log;
        private ISqlSugarClient _client;
        private IServiceProvider _provider;
        private SiemensCache _cache;
        private JobManager _jobManager => _provider.GetRequiredService<JobManager>();

        public AlarmController(SiemensCache cache,
            SqlSugarScope client,
            IServiceProvider provider,
            ILogger<AlarmController> logger)
            : base(provider, cache)
        {
            _log = logger;
            _client = client;
            _provider = provider;
            _cache = cache;
        }

        #region Get
        [HttpGet("GetCurrentInfo")]
        [SwaggerOperation(Summary = "Swagger_Alam_GetCurrentInfo", Description = "Swagger_Alam_GetCurrentInfo_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<AlarmCurrentInfoResult>> GetCurrentInfo([AllowNull] long? startTime,
            [AllowNull] long? endTime,
            [AllowNull] string? panelName,
            [AllowNull] string? circuitName,
            [AllowNull] string? substationName,
            [AllowNull] int? substationId,
            [AllowNull] bool filterUserOpt = true)
        {
            var query = _client.Queryable<AlarmLog>().Where(p => p.EventType == AlarmEventType.Alarm
            || p.EventType == AlarmEventType.UdcAlarm
            || p.EventType == AlarmEventType.BreakerTrip
            ||p.EventType==AlarmEventType.CommunicationAlarm);

            if (startTime.HasValue)
            {
                var startDate = startTime.Value.GetDateTimeBySec();
                query = query.Where(l => l.CreatedTime >= startDate);
            }

            if (endTime.HasValue)
            {
                var endDate = endTime.Value.GetDateTimeBySec();
                query = query.Where(l => l.CreatedTime < endDate);
            }

            if (!string.IsNullOrEmpty(panelName))
            {
                query = query.Where(l => panelName == l.PanelName);
            }

            if (!string.IsNullOrEmpty(circuitName))
            {
                query = query.Where(l => circuitName == l.CircuitName);
            }

            if (substationId.HasValue)
            {
                substationName = await _client.Queryable<AssetInfo>().Where(a => a.Id == substationId).Select(a => a.AssetName).FirstAsync() ?? "Null";
            }

            if (!string.IsNullOrEmpty(substationName))
            {
                query = query.Where(l => substationName == l.SubstationName);
            }

            if (filterUserOpt)
            {
                query = query.Where(l => l.EventType != AlarmEventType.OperationLog && l.EventType != AlarmEventType.DeviceLog);
            }
            else
            {
                query = query.Where(l => l.EventType != AlarmEventType.OperationLog && l.EventType != AlarmEventType.DeviceLog);
            }
            var queryStartTime = await _client.Queryable<AlarmQueryConfig>().Where(a => a.QueryType == AlarmQueryType.QueryStartTime).FirstAsync();
            if (queryStartTime!=null &&!string.IsNullOrEmpty(queryStartTime.QueryValue))
            {
                DateTime startDate;
                var success = DateTime.TryParseExact(queryStartTime.QueryValue, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.RoundtripKind, out startDate);
                if (success)
                {
                    query = query.Where(l => l.CreatedTime >= startDate);
                }
            }

            var tempQuery = query.Clone();

            var result = await tempQuery.GroupBy(l => l.Severity)
                .Select(l => new SqlGroupResult()
                {
                    GroupColumn = l.Severity.ToString(),
                    TotalCount = SqlFunc.AggregateCount(l.Severity)
                }).ToArrayAsync();

            var curentInfo = new AlarmCurrentInfoResult()
            {
                LowCount = result.FirstOrDefault(p => p.GroupColumn == "0" || p.GroupColumn == "Low")?.TotalCount ?? 0,
                MiddleCount = result.FirstOrDefault(p => p.GroupColumn == "10" || p.GroupColumn == "Middle")?.TotalCount ?? 0,
                HighCount = result.FirstOrDefault(p => p.GroupColumn == "20" || p.GroupColumn == "High")?.TotalCount ?? 0
            };

            curentInfo.TotalCount = curentInfo.LowCount + curentInfo.MiddleCount + curentInfo.HighCount;


            var newResult = query.Clone().Where(p => p.Status == AlarmLogStatus.New).GroupBy(p => p.Severity).Select(p => new
            {
                GroupColumn = p.Severity,
                TotalCount = SqlFunc.AggregateCount(p.Severity)

            }).ToList();

            curentInfo.NewLowCount = newResult.FirstOrDefault(p => p.GroupColumn == AlarmSeverity.Low)?.TotalCount ?? 0;
            curentInfo.NewMiddleCount = newResult.FirstOrDefault(p => p.GroupColumn == AlarmSeverity.Middle)?.TotalCount ?? 0;
            curentInfo.NewHighCount = newResult.FirstOrDefault(p => p.GroupColumn == AlarmSeverity.High)?.TotalCount ?? 0;
            var alarmReminderConfig = await _client.Queryable<AlarmQueryConfig>().Where(p => p.QueryType == AlarmQueryType.AlarmReminder && p.QueryValue == "1").ToListAsync();
            if (alarmReminderConfig.Where(a => a.ParamCode == "highReminder").FirstOrDefault() != null)
            {
                curentInfo.NewCount += curentInfo.NewHighCount;
            }
            if (alarmReminderConfig.Where(a => a.ParamCode == "midReminder").FirstOrDefault() != null)
            {
                curentInfo.NewCount += curentInfo.NewMiddleCount;
            }
            if (alarmReminderConfig.Where(a => a.ParamCode == "lowReminder").FirstOrDefault() != null)
            {
                curentInfo.NewCount += curentInfo.NewLowCount;
            }
            //curentInfo.NewCount = curentInfo.NewLowCount + curentInfo.NewMiddleCount + curentInfo.NewHighCount;

            result = await query.GroupBy(l => l.Status)
                .Select(l => new SqlGroupResult()
                {
                    GroupColumn = l.Status.ToString(),
                    TotalCount = SqlFunc.AggregateCount(l.Status)
                }).ToArrayAsync();

            curentInfo.InProcessCount = result.FirstOrDefault(p => p.GroupColumn == "10" || p.GroupColumn == "InProcess")?.TotalCount ?? 0;
            curentInfo.FinishCount = result.FirstOrDefault(p => p.GroupColumn == "20" || p.GroupColumn == "Finish")?.TotalCount ?? 0;
            curentInfo.IgnoreCount = result.FirstOrDefault(p => p.GroupColumn == "-1" || p.GroupColumn == "Ignore")?.TotalCount ?? 0;

            return new ResponseBase<AlarmCurrentInfoResult>()
            {
                Code = 20000,
                Data = curentInfo
            };
        }

        [HttpGet()]
        [SwaggerOperation(Summary = "Swagger_Alam_GetAll", Description = "Swagger_Alam_GetAll_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<SearchBase<AlarmLogListItemResult>> GetAll(
            [AllowNull] string? substationName,
            [AllowNull] string? panelName,
            [AllowNull] string? circuitName,
            [AllowNull] string? deviceName,
            [AllowNull] string? alarmInfo,
            [AllowNull] string? eventType,
            [AllowNull] int? alarmLevel,
            [AllowNull] int? alarmStatus,
            [AllowNull] string? user,
            [AllowNull] long? alarmStartTime,
            [AllowNull] long? alarmEndTime,
            [AllowNull] int page = 1,
            [AllowNull] int pageSize = 10)
        {
            if (pageSize <= 0 || page <= 0)
            {
                return new SearchBase<AlarmLogListItemResult>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var query = _client.Queryable<AlarmLog>();
            var isOperationLog = false;
            if (!string.IsNullOrEmpty(eventType))
            {
                var eventTypes = eventType.Split(",").Select(e =>
                {
                    if (int.TryParse(e, out var intValue))
                    {
                        return intValue;
                    }
                    return -1;
                }).ToArray();

                query = query.Where(l => eventTypes.Contains((int)l.EventType));
                if (eventTypes.Length == 1)
                {
                    isOperationLog = eventTypes[0] == (int)AlarmEventType.OperationLog;
                }
            }
            if (!string.IsNullOrEmpty(substationName) && !isOperationLog)
            {
                query = query.Where(l => l.SubstationName != null && l.SubstationName == substationName);
            }
            if (!string.IsNullOrEmpty(panelName) && !isOperationLog)
            {
                query = query.Where(l => l.PanelName != null && l.PanelName == panelName);
            }
            if (!string.IsNullOrEmpty(circuitName) && !isOperationLog)
            {
                query = query.Where(l => l.CircuitName != null && l.CircuitName == circuitName);
            }
            if (!string.IsNullOrEmpty(deviceName) && !isOperationLog)
            {
                query = query.Where(l => l.DeviceName != null && l.DeviceName.Contains(deviceName));
            }

            if (!string.IsNullOrEmpty(alarmInfo))
            {
                query = query.Where(l => l.Message.Contains(alarmInfo));
            }

            if (alarmLevel.HasValue)
            {
                query = query.Where(l => l.Severity == (AlarmSeverity)alarmLevel.Value);
            }
            if (alarmStartTime.HasValue)
            {
                var startTime = alarmStartTime.Value.GetDateTimeBySec();
                query = query.Where(l => l.CreatedTime >= startTime);
            }
            if (alarmEndTime.HasValue)
            {
                var endTime = alarmEndTime.Value.GetDateTimeBySec();
                query = query.Where(l => l.CreatedTime < endTime);
            }
            if (alarmStatus.HasValue && !isOperationLog)
            {
                query = query.Where(l => l.Status == (AlarmLogStatus)alarmStatus.Value);
            }

            if (!string.IsNullOrEmpty(user) && isOperationLog)
            {
                query = query.Where(l => l.CreatedBy.Contains(user));
            }
            var queryStartTime = await _client.Queryable<AlarmQueryConfig>().Where(a => a.QueryType == AlarmQueryType.QueryStartTime).FirstAsync();
            if (queryStartTime != null && !string.IsNullOrEmpty(queryStartTime.QueryValue))
            {
                DateTime startDate;
                var success = DateTime.TryParseExact(queryStartTime.QueryValue, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.RoundtripKind, out startDate);
                if (success)
                {
                    query = query.Where(l => l.CreatedTime >= startDate);
                }

            }

            RefAsync<int> totalNumber = new RefAsync<int>();
            RefAsync<int> totalPage = new RefAsync<int>();
            var dbItems = await query.OrderByDescending(l => l.CreatedTime).ToPageListAsync(page, pageSize, totalNumber, totalPage);
            var ruleId = dbItems.Select(l => l.RuleId).ToArray();
            var rules = await _client.Queryable<AlarmRule>().Where(a => ruleId.Contains(a.Id)).ToArrayAsync();
            var result = new List<AlarmLogListItemResult>();
            foreach (var item in dbItems)
            {
                var rule = rules.FirstOrDefault(r => r.Id == item.Id);
                result.Add(new AlarmLogListItemResult(item, rule));
            }

            return new SearchBase<AlarmLogListItemResult>()
            {
                Page = page,
                TotalCount = totalNumber.Value,
                Code = 20000,
                Items = result
            };
        }

        [HttpPost("QueryAlarm")]
        [SwaggerOperation(Summary = "Swagger_Alam_GetAll", Description = "Swagger_Alam_GetAll_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<SearchBase<AlarmLogListItemResult>> GetAllAlarm([FromBody]AlarmQueryParam queryParam)
        {
            if (queryParam.pageSize <= 0 || queryParam.page <= 0)
            {
                return new SearchBase<AlarmLogListItemResult>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var query = _client.Queryable<AlarmLog>();
            var isOperationLog = false;
            if (queryParam.eventType!=null&&queryParam.eventType.Length > 0)
            {

                query = query.Where(l => queryParam.eventType.Contains((int)l.EventType));
                if (queryParam.eventType.Length == 1)
                {
                    isOperationLog = queryParam.eventType[0] == (int)AlarmEventType.OperationLog;
                }
            }
            if (queryParam.substationName != null && queryParam.substationName.Length > 0 && !isOperationLog)
            {
                query = query.Where(l => l.SubstationName != null && queryParam.substationName.Contains(l.SubstationName));
            }
            if (queryParam.panelName != null && queryParam.panelName.Length > 0 && !isOperationLog)
            {
                query = query.Where(l => l.PanelName != null && queryParam.panelName.Contains(l.PanelName));
            }
            if (queryParam.circuitName != null && queryParam.circuitName.Length > 0 && !isOperationLog)
            {
                query = query.Where(l => l.CircuitName != null && queryParam.circuitName.Contains(l.CircuitName));
            }
            if (queryParam.deviceName != null && queryParam.deviceName.Length > 0 && !isOperationLog)
            {
                query = query.Where(l => l.DeviceName != null && queryParam.deviceName.Contains(l.DeviceName));
            }
            if (queryParam.alarmRuleId != null && queryParam.alarmRuleId.Length > 0)
            {
                query = query.Where(l => l.RuleId != null&& queryParam.alarmRuleId.Contains((int)l.RuleId));
            }
            if (queryParam.alarmLevel != null && queryParam.alarmLevel.Length > 0)
            {
                query = query.Where(l => queryParam.alarmLevel.Contains((int)l.Severity));
            }
            if (queryParam.alarmStartTime.HasValue)
            {
                var startTime = queryParam.alarmStartTime.Value.GetDateTimeBySec();
                query = query.Where(l => l.CreatedTime >= startTime);
            }
            if (queryParam.alarmEndTime.HasValue)
            {
                var endTime = queryParam.alarmEndTime.Value.GetDateTimeBySec();
                query = query.Where(l => l.CreatedTime < endTime);
            }
            if (queryParam.alarmStatus != null && queryParam.alarmStatus.Length > 0 && !isOperationLog)
            {
                query = query.Where(l => queryParam.alarmStatus.Contains((int)l.Status));
            }

            if (!string.IsNullOrEmpty(queryParam.user) && isOperationLog)
            {
                query = query.Where(l => l.CreatedBy.Contains(queryParam.user));
            }
            var queryStartTime = await _client.Queryable<AlarmQueryConfig>().Where(a => a.QueryType == AlarmQueryType.QueryStartTime).FirstAsync();
            if (queryStartTime != null && !string.IsNullOrEmpty(queryStartTime.QueryValue))
            {
                DateTime startDate;
                var success = DateTime.TryParseExact(queryStartTime.QueryValue, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.RoundtripKind, out startDate);
                if (success)
                {
                    query = query.Where(l => l.CreatedTime >= startDate);
                }

            }

            RefAsync<int> totalNumber = new RefAsync<int>();
            RefAsync<int> totalPage = new RefAsync<int>();
            var sql = query.ToSql();
            var dbItems = await query.OrderByDescending(l => l.CreatedTime).ToPageListAsync(queryParam.page, queryParam.pageSize, totalNumber, totalPage);
            var ruleId = dbItems.Select(l => l.RuleId).ToArray();
            var rules = await _client.Queryable<AlarmRule>().Where(a => ruleId.Contains(a.Id)).ToArrayAsync();
            var result = new List<AlarmLogListItemResult>();
            foreach (var item in dbItems)
            {
                var rule = rules.FirstOrDefault(r => r.Id == item.Id);
                result.Add(new AlarmLogListItemResult(item, rule));
            }

            return new SearchBase<AlarmLogListItemResult>()
            {
                Page = queryParam.page,
                TotalCount = totalNumber.Value,
                Code = 20000,
                Items = result
            };
        }
        [HttpGet("AlarmReminder")]
        [SwaggerOperation(Summary = "Swagger_AlarmReminder_Get", Description = "Swagger_AlarmReminder_Get_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<List<AlarmQueryConfigInfo>>> GetAlarmReminder(int queryType=0)
        {
            var info = await _client.Queryable<AlarmQueryConfig>().Where(a=>a.QueryType== (AlarmQueryType)queryType)
                .Select(a=>new AlarmQueryConfigInfo
                {
                    Id= a.Id,
                    QueryValue = a.QueryValue,
                    ParamCode = a.ParamCode,
                    QueryType = (int)a.QueryType
                })
                .ToListAsync();
            return new ResponseBase<List<AlarmQueryConfigInfo>>()
            {
                Code = 20000,
                Data = info
            };
        }
        [HttpPost("SaveAlarmReminder")]
        [SwaggerOperation(Summary = "Swagger_AlarmReminder_Post", Description = "Swagger_AlarmReminder_Post_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> SaveAlarmReminder(List<AlarmQueryConfigInfo> alarmQueries)
        {
            var server = _provider.GetRequiredService<AlarmExtendServer>();
            foreach (var item in alarmQueries)
            {
                var dbInfo = await _client.Queryable<AlarmQueryConfig>().Where(a => a.Id == item.Id).FirstAsync();
                if (item.QueryType == (int)AlarmQueryType.QueryStartTime)
                {
                    DateTime startDate;
                    var success = DateTime.TryParseExact(item.QueryValue, "yyyy-MM-ddTHH:mm:ss.fffZ", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.RoundtripKind, out startDate);
                    if (success)
                    {
                        var loaclTimeZone = TimeZoneInfo.Local;
                        startDate = TimeZoneInfo.ConvertTimeFromUtc(startDate, loaclTimeZone);
                        item.QueryValue = startDate.ToString("yyyy-MM-dd HH:mm:ss");
                        await server.FinishAlarmByAlarmStartTime(startDate, _client, UserName);
                    }
                }
                if (dbInfo != null)
                {
                    dbInfo.QueryValue = item.QueryValue;
                    dbInfo.UpdatedBy = UserName;
                    dbInfo.UpdatedTime = DateTime.Now;
                    await _client.Updateable(dbInfo).ExecuteCommandAsync();
                }
                else
                {
                    var saveInfo = new AlarmQueryConfig()
                    {
                        QueryValue = item.QueryValue,
                        QueryType = (AlarmQueryType)item.QueryType,
                        ParamCode = item.ParamCode
                    };
                    await _client.Insertable<AlarmQueryConfig>(saveInfo).ExecuteCommandAsync();
                }
            }
            return new ResponseBase<string>()
            {
                Code = 20000,
                Message = MessageContext.Success
            };
        }

        [HttpGet("{id}")]
        [SwaggerOperation(Summary = "Swagger_Alam_Get", Description = "Swagger_Alam_Get_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<AlarmLogInfoResult>> Get(int id)
        {
            if (id <= 0)
            {
                return new ResponseBase<AlarmLogInfoResult>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }
            var log = await _client.Queryable<AlarmLog>().FirstAsync(a => a.Id == id);
            if (log == null)
            {
                return new ResponseBase<AlarmLogInfoResult>()
                {
                    Code = 40400,
                    Message = MessageContext.GetErrorValue("Common_NotExists")
                };
            }

            AlarmRule? rule = null;
            if (log.RuleId.HasValue)
            {
                rule = await _client.Queryable<AlarmRule>().FirstAsync(a => a.Id == log.RuleId);
            }
            var histories = await _client.Queryable<AlarmLogChangeLog>().Where(l => l.LogId == log.Id).ToListAsync();

            return new ResponseBase<AlarmLogInfoResult>()
            {
                Code = 20000,
                Data = new AlarmLogInfoResult(log, rule, histories, MessageContext)
            };
        }

        [HttpGet("ExportAlarm")]
        [SwaggerOperation(Summary = "Swagger_Alam_Export", Description = "Swagger_Alam_Export_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> ExportAlarm(
            [AllowNull] string? substationName,
            [AllowNull] string? panelName,
            [AllowNull] string? circuitName,
            [AllowNull] string? deviceName,
            [AllowNull] string? eventType,
            [AllowNull] int? alarmLevel,
            [AllowNull] int? alarmStatus,
            [AllowNull] long? alarmStartTime,
            [AllowNull] long? alarmEndTime,
            [AllowNull] string? user,
            [AllowNull] bool useFilter = false)
        {
            var param = new Dictionary<string, string>
            {
                { "UseFilter", useFilter.ToString() },
                { "AlarmEndTime", alarmEndTime?.ToString() ?? string.Empty },
                { "AlarmStartTime", alarmStartTime?.ToString() ?? string.Empty },
                { "AlarmStatus", alarmStatus?.ToString() ?? string.Empty },
                { "AlarmLevel", alarmLevel?.ToString() ?? string.Empty },
                { "EventType", eventType ?? string.Empty },
                { "DeviceName", deviceName ?? string.Empty },
                { "CircuitName", circuitName ?? string.Empty },
                { "PanelName", panelName ?? string.Empty },
                { "SubstationName", substationName ?? string.Empty },
                { "UserName", user ?? string.Empty },
                { "Language", UserLanguage }
            };
            var jobInfo = new JobInfo();
            param.Add("JobId", jobInfo.JobId);
            _cache.Set($"JobId:{jobInfo.JobId}", jobInfo, TimeSpan.FromMinutes(1));
            await _jobManager.TriggerJob("ExportAlarmJob", param);

            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = jobInfo.JobId
            };
        }
        [HttpPost("ExportAlarm")]
        [SwaggerOperation(Summary = "Swagger_Alam_Export", Description = "Swagger_Alam_Export_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> ExportAlarm([FromBody] AlarmQueryParam queryParam)
        {
            string deviceNameStr = string.Empty;
            string circuitNameStr = string.Empty;
            string panelNameStr = string.Empty;
            string substationNameStr = string.Empty;
            string AlarmLevelStr = string.Empty;
            string eventTypeStr = string.Empty;
            string assetStatusStr = string.Empty;
            string alarmRuleIdStr = string.Empty;
            if (queryParam.deviceName!=null) { deviceNameStr = String.Join(", ", queryParam.deviceName); }
            if (queryParam.circuitName != null) { circuitNameStr = String.Join(", ", queryParam.circuitName); }
            if (queryParam.panelName != null) { panelNameStr = String.Join(", ", queryParam.panelName); }
            if (queryParam.substationName != null) { substationNameStr = String.Join(", ", queryParam.substationName); }
            if (queryParam.alarmLevel != null) { AlarmLevelStr = String.Join(", ", queryParam.alarmLevel.Select(i => i.ToString()).ToArray()); }
            if (queryParam.eventType != null) { eventTypeStr = String.Join(", ", queryParam.eventType.Select(i => i.ToString()).ToArray()); }
            if (queryParam.alarmStatus != null) { assetStatusStr = String.Join(", ", queryParam.alarmStatus.Select(i => i.ToString()).ToArray()); }
            if (queryParam.alarmRuleId != null) { alarmRuleIdStr = String.Join(", ", queryParam.alarmRuleId.Select(i => i.ToString()).ToArray()); }
            var param = new Dictionary<string, string>
            {
                { "UseFilter", queryParam.useFilter.ToString() },
                { "AlarmEndTime", queryParam.alarmEndTime?.ToString() ?? string.Empty },
                { "AlarmStartTime", queryParam.alarmStartTime?.ToString() ?? string.Empty },
                { "AlarmStatus",assetStatusStr},
                { "AlarmLevel", AlarmLevelStr },
                { "EventType", eventTypeStr },
                { "DeviceName", deviceNameStr},
                { "CircuitName", circuitNameStr },
                { "PanelName", panelNameStr},
                { "SubstationName", substationNameStr },
                { "AlarmRuleId", alarmRuleIdStr },
                { "UserName", queryParam.user ?? string.Empty },
                { "Language", UserLanguage }
            };
            var jobInfo = new JobInfo();
            param.Add("JobId", jobInfo.JobId);
            _cache.Set($"JobId:{jobInfo.JobId}", jobInfo, TimeSpan.FromMinutes(1));
            await _jobManager.TriggerJob("ExportAlarmJob", param);

            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = jobInfo.JobId
            };
        }
        [HttpGet("ExportAlarmResult/{jobId}")]
        [SwaggerOperation(Summary = "Swagger_Alam_ExportResult", Description = "Swagger_Alam_ExportResult_Desc")]
        [Authorize(policy: Permission.Default)]
        public ResponseBase<JobResult> ExportAlarmResult(string jobId)
        {
            var jobInfo = _cache.Get<JobInfo>($"JobId:{jobId}");
            if (jobInfo == null)
            {
                return new ResponseBase<JobResult>()
                {
                    Code = 40400,
                    Message = MessageContext.GetErrorValue("Job_NotExists")
                };
            }
            string? message = null;
            object? result = null;
            if (jobInfo.Result != null)
            {
                if (jobInfo.Result.ErrorInfo != null)
                {
                    var messageStringBuilder = new StringBuilder();
                    foreach (var errorCode in jobInfo.Result.ErrorInfo)
                    {
                        messageStringBuilder.AppendLine(MessageContext.GetErrorValue(errorCode));
                    }
                    message = messageStringBuilder.ToString();
                }
                if (jobInfo.Result.Data != null && jobInfo.Result.Data is string path)
                {
                    var fileModel = new TemporaryFileModel(path, $"{MessageContext.GetString("Download_Alarm_FileName") ?? "AlarmLog"}.xlsx");
                    var key = Guid.NewGuid().ToString().Replace("-", "");
                    _cache.Set($"TemporaryFile:{key}", fileModel, TimeSpan.FromMinutes(1));
                    result = $"/api/v1/filemanager/temporary/{key}";
                }
                else
                {
                    result = jobInfo.Result.Data;
                }
            }

            return new ResponseBase<JobResult>()
            {
                Code = 20000,
                Data = new JobResult()
                {
                    Status = jobInfo.JobStatus,
                    Result = result,
                    ResultMessage = message
                }
            };
        }

        [HttpGet("{id}/retrospect")]
        [SwaggerOperation(Summary = "Swagger_Alam_ExportResult", Description = "Swagger_Alam_ExportResult_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<List<AlarmLogListItemResult>>> Retrospect(long id, [AllowNull] int time = 60)
        {
            if (id <= 0)
            {
                return new ResponseBase<List<AlarmLogListItemResult>>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var log = await _client.Queryable<AlarmLog>().FirstAsync(x => x.Id == id);
            if (log == null)
            {
                return new ResponseBase<List<AlarmLogListItemResult>>()
                {
                    Code = 40400,
                    Message = MessageContext.GetErrorValue("Common_NotExists")
                };
            }
            var result = new List<AlarmLogListItemResult>();
            var endTime = log.CreatedTime;
            var startTime = endTime.AddMinutes(0 - time);
            var query = _client.Queryable<AlarmLog>().Where(l => l.CreatedTime >= startTime && l.CreatedTime < endTime);
            var targetName = string.Empty;
            if (!string.IsNullOrEmpty(log.DeviceName))
            {
                targetName = log.DeviceName;
                query = query.Where(l => l.DeviceName == log.DeviceName);
            }
            else if (!string.IsNullOrEmpty(log.CircuitName))
            {
                targetName = log.CircuitName;
                query = query.Where(l => l.CircuitName == log.CircuitName);
            }
            else if (!string.IsNullOrEmpty(log.PanelName))
            {
                targetName = log.PanelName;
                query = query.Where(l => l.PanelName == log.PanelName);
            }
            else if (!string.IsNullOrEmpty(log.SubstationName))
            {
                targetName = log.SubstationName;
                query = query.Where(l => l.SubstationName == log.SubstationName);
            }

            if (string.IsNullOrEmpty(targetName))
            {
                return new ResponseBase<List<AlarmLogListItemResult>>()
                {
                    Code = 20000,
                    Data = result
                };
            }
            var data = await query.ToArrayAsync();
            if (data == null)
            {
                return new ResponseBase<List<AlarmLogListItemResult>>()
                {
                    Code = 20000,
                    Data = result
                };
            }

            var ruleIds = data.Where(l => l.RuleId.HasValue).Select(l => l.RuleId ?? 0).ToArray();
            var rules = await _client.Queryable<AlarmRule>().Where(r => ruleIds.Contains(r.Id)).ToArrayAsync();
            foreach (var item in data)
            {
                AlarmRule? rule = null;
                if (item.RuleId.HasValue)
                {
                    rule = rules.FirstOrDefault(r => r.Id == item.RuleId.Value);
                }
                result.Add(new AlarmLogListItemResult(item, rule));
            }

            return new ResponseBase<List<AlarmLogListItemResult>>()
            {
                Code = 20000,
                Data = result
            };
        }
        #endregion

        #region Post
        [HttpPost()]
        public async Task<ResponseBase<int>> Add(string message, int ruleId, string? eventType)
        {
            var s = ruleId % 3;
            var ss = AlarmSeverity.Low;
            switch (s)
            {
                case 0:
                    ss = AlarmSeverity.High;
                    break;
                case 1:
                    ss = AlarmSeverity.Middle;
                    break;
                default:
                    break;
            }

            var targetType = AlarmEventType.Alarm;
            if (!string.IsNullOrEmpty(eventType) && Enum.TryParse(typeof(AlarmEventType), eventType, out object? eventTypeObj) && eventTypeObj != null && eventTypeObj is AlarmEventType newValue)
            {
                targetType = newValue;
            }

            var assetStatus = new Dictionary<string, string>()
            {
                ["Ua"] = "340",
                ["Uc"] = "370",
                ["Ub"] = "240",
            };

            var alarmLog = new AlarmLog()
            {
                RuleId = ruleId,
                Severity = ss,
                EventType = targetType,
                Status = AlarmLogStatus.New,
                Message = message,
                AssetStatusStr = JsonConvert.SerializeObject(assetStatus),
                CreatedBy = UserName,
                UpdatedBy = UserName,
                UpdatedTime = DateTime.Now,
                CreatedTime = DateTime.Now,
            };
            await _client.Insertable(alarmLog).ExecuteCommandAsync();
            _client.Ado.CommitTran();
            return new ResponseBase<int>()
            {
                Code = 20000,
                Data = ruleId
            };
        }
        #endregion

        #region Put
        [HttpPut("{id}/ChangeLogStatus")]
        [SwaggerOperation(Summary = "Swagger_Alam_ChangeLogStatus", Description = "Swagger_Alam_ChangeLogStatus_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<bool>> ChangeLogStatus(int id, string action)
        {
            const string ignoreAction = "ignore";
            const string inprocessAction = "inprocess";
            const string finishAction = "finish";
            if (string.IsNullOrEmpty(action)
                || id <= 0)
            {
                return new ResponseBase<bool>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            Expression<Func<AlarmLog, bool>> expression;

            string remark;
            AlarmLogStatus status;
            switch (action.ToLower())
            {
                case ignoreAction:
                    status = AlarmLogStatus.Ignore;
                    expression = l => l.Id == id && (l.Status == AlarmLogStatus.New || l.Status == AlarmLogStatus.InProcess);
                    remark = "This alarm is ignored";
                    break;
                case inprocessAction:
                    status = AlarmLogStatus.InProcess;
                    expression = l => l.Id == id && l.Status == AlarmLogStatus.New;
                    remark = "The alarm is being processed";
                    break;
                case finishAction:
                    status = AlarmLogStatus.Finish;
                    expression = l => l.Id == id && (l.Status == AlarmLogStatus.New || l.Status == AlarmLogStatus.InProcess);
                    remark = "The alarm is being processed";
                    break;
                default:
                    return new ResponseBase<bool>()
                    {
                        Code = 40300,
                        Message = MessageContext.ErrorParam
                    };
            }
            var alarmLog = new AlarmLog()
            {
                Id = id,
                Status = status,
                UpdatedBy = UserName,
                UpdatedTime = DateTime.Now,
            };

            bool isExists = false;
            try
            {
                _client.Ado.BeginTran();
                var q = _client.Updateable(alarmLog).Where(expression).UpdateColumns(l => new { l.Status, l.UpdatedBy, l.UpdatedTime });
                //var sql = q.ToSqlString();
                var result = await q.ExecuteCommandAsync();

                isExists = result > 0;

                if (isExists)
                {
                    var server = _provider.GetRequiredService<AlarmExtendServer>();
                    server.FinishAlarmLog(id);

                    var changeHistory = new AlarmLogChangeLog()
                    {
                        LogId = alarmLog.Id,
                        ChangedBy = alarmLog.UpdatedBy,
                        ChangeTime = alarmLog.UpdatedTime,
                        Source = AlarmChangeSource.Person,
                        Remark = remark
                    };

                    await _client.Insertable(changeHistory).ExecuteCommandAsync();
                }

                _client.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "修改状态失败");
                return new ResponseBase<bool>()
                {
                    Code = 50000,
                    Message = MessageContext.ServerException
                };
            }

            if (isExists)
            {
                return new ResponseBase<bool>()
                {
                    Code = 20000,
                    Data = isExists
                };
            }
            else
            {
                return new ResponseBase<bool>()
                {
                    Code = 40400,
                    Message = MessageContext.GetErrorValue("Common_NotExists")
                };
            }
        }

        /// <summary>
        /// 批量修改日志状态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPut("BatchChangeLogStatus")]
        [SwaggerOperation(Summary = "Swagger_Alam_BatchChangeLogStatus", Description = "Swagger_Alam_BatchChangeLogStatus_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<bool>> BatchChangeLogStatus([FromBody] AlarmParam input)
        {
            const string ignoreAction = "ignore";
            const string inprocessAction = "inprocess";
            const string finishAction = "finish";
            var server = _provider.GetRequiredService<AlarmExtendServer>();
            var isFinish = false;

            if (string.IsNullOrEmpty(input.Action) || input.Ids == null || !input.Ids.Any())
            {
                return new ResponseBase<bool>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            string remark;
            AlarmLogStatus status;

            switch (input.Action.ToLower())
            {
                case ignoreAction:
                    status = AlarmLogStatus.Ignore;
                    remark = "This alarm is ignored";
                    isFinish = true;
                    break;

                case inprocessAction:
                    status = AlarmLogStatus.InProcess;
                    remark = "The alarm is being processed";
                    break;

                case finishAction:
                    status = AlarmLogStatus.Finish;
                    remark = "The alarm is being processed";
                    isFinish = true;
                    break;

                default:

                    return new ResponseBase<bool>()
                    {
                        Code = 40300,
                        Message = MessageContext.ErrorParam
                    };
            }

            var isExists = false;
            try
            {
                var finishLogIds = new List<long>();
                _client.Ado.BeginTran();

                #region Old
                //var alarmLogs = new List<AlarmLog>();

                //if (input.Ids != null && input.Ids.Any())
                //{
                //    foreach (var item in input.Ids)
                //    {
                //        alarmLogs.Add(new AlarmLog
                //        {
                //            Id = item,
                //            Status = status,
                //            UpdatedBy = UserName,
                //            UpdatedTime = DateTime.Now,
                //        });
                //    }
                //}

                //var q = _client.Updateable(alarmLogs).UpdateColumns(l => new { l.Status, l.UpdatedBy, l.UpdatedTime });

                //var result = await q.Where(a=>a.Id == ).ExecuteCommandAsync();

                //isExists = result > 0;

                //if (isExists)
                //{
                //    var server = _provider.GetRequiredService<AlarmExtendServer>();

                //    if (input.Ids != null && input.Ids.Any())
                //    {
                //        foreach (var item in input.Ids)
                //        {
                //            server.FinishAlarmLog(item);
                //        }
                //    }

                //    var changeHistorys = new List<AlarmLogChangeLog>();

                //    if (alarmLogs != null && alarmLogs.Any())
                //    {
                //        foreach (var item in alarmLogs)
                //        {
                //            changeHistorys.Add(new AlarmLogChangeLog
                //            {
                //                LogId = item.Id,
                //                ChangedBy = item.UpdatedBy,
                //                ChangeTime = item.UpdatedTime,
                //                Source = AlarmChangeSource.Person,
                //                Remark = remark
                //            });
                //        }
                //    }

                //    await _client.Insertable(changeHistorys).ExecuteCommandAsync();
                //}
                #endregion

                if (input.Ids.Count == 1 && input.Ids[0] == -1) {

                    var len = 1000;
                    while (true)
                    {
                        var changeHistories = new List<AlarmLogChangeLog>();
                        var logs = await _client.Queryable<AlarmLog>()
                            .Where(a => (a.EventType != AlarmEventType.DeviceLog || a.EventType != AlarmEventType.OperationLog)
                                && (a.Status == AlarmLogStatus.InProcess || a.Status == AlarmLogStatus.New))
                            .OrderBy(a => a.Id)
                            .Take(len)
                            .ToListAsync();

                        if (logs != null && logs.Count > 0)
                        {
                            isExists = true;
                            foreach (var log in logs)
                            {
                                log.UpdatedBy = UserName;
                                log.UpdatedTime = DateTime.Now;
                                log.Status = status;

                                changeHistories.Add(new AlarmLogChangeLog()
                                {
                                    LogId = log.Id,
                                    ChangedBy = UserName,
                                    ChangeTime = DateTime.Now,
                                    Source = AlarmChangeSource.Person,
                                    Remark = remark,
                                });

                                if (isFinish)
                                {
                                    finishLogIds.Add(log.Id);
                                }
                            }

                            await _client.Updateable(logs).ExecuteCommandAsync();
                            await _client.Insertable(changeHistories).ExecuteCommandAsync();
                        }

                        if (logs == null || logs.Count < len)
                        {
                            break;
                        }
                    }
                }
                else
                {
                    var logs = await _client.Queryable<AlarmLog>()
                        .Where(a => input.Ids.Contains(a.Id) 
                            && (a.EventType != AlarmEventType.DeviceLog || a.EventType != AlarmEventType.OperationLog)
                            && (a.Status == AlarmLogStatus.InProcess || a.Status == AlarmLogStatus.New))
                        .ToListAsync();

                    if (logs != null && logs.Count > 0)
                    {
                        isExists = true;
                        var changeHistories = new List<AlarmLogChangeLog>();
                        foreach (var log in logs)
                        {
                            log.UpdatedBy = UserName;
                            log.UpdatedTime = DateTime.Now;
                            log.Status = status;

                            changeHistories.Add(new AlarmLogChangeLog()
                            {
                                LogId = log.Id,
                                ChangedBy = UserName,
                                ChangeTime = DateTime.Now,
                                Source = AlarmChangeSource.Person,
                                Remark = remark,
                            });

                            if (isFinish)
                            {
                                finishLogIds.Add(log.Id);
                            }
                        }

                        await _client.Updateable(logs).ExecuteCommandAsync();
                        await _client.Insertable(changeHistories).ExecuteCommandAsync();
                    }
                }

                _client.Ado.CommitTran();

                if (finishLogIds.Count > 0)
                {
                    server.FinishAlarmLog(finishLogIds.ToArray());
                }
            }
            catch (Exception ex)
            {
                _client.Ado.RollbackTran();

                _log.LogError(ex, "修改状态失败");

                return new ResponseBase<bool>()
                {
                    Code = 50000,
                    Message = MessageContext.ServerException
                };
            }

            if (isExists)
            {
                return new ResponseBase<bool>()
                {
                    Code = 20000,
                    Data = isExists
                };
            }
            else
            {
                return new ResponseBase<bool>()
                {
                    Code = 40400,
                    Message = MessageContext.GetErrorValue("Common_NotExists")
                };
            }
        }

        #endregion

        [HttpGet("getPie")]
        [SwaggerOperation(Summary = "Swagger_Alarm_GetPie", Description = "Swagger_Alarm_GetPie_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<IChart?>> GetPie(
            [AllowNull] string? substationName,
            [AllowNull] string? panelName,
            [AllowNull] string? circuitName,
            [AllowNull] string? deviceName,
            [AllowNull] bool filterUserOpt = true)
        {
            var configName = "alarmCount_query".ToUpper();

            var config = await _client.Queryable<AssetDashboardConfig>().FirstAsync(c => c.ConfigName == configName);

            if (config == null)
            {
                return new ResponseBase<IChart?>()
                {
                    Code = 20000,
                    Data = null,
                };
            }

            var queryBuilder = new StringBuilder();
            if (!string.IsNullOrEmpty(substationName))
            {
                queryBuilder.Append("al.substation_name = '");
                queryBuilder.Append(substationName);
                queryBuilder.Append('\'');
            }

            if (!string.IsNullOrEmpty(panelName))
            {
                if (queryBuilder.Length > 0)
                {
                    queryBuilder.Append(" and ");
                }
                queryBuilder.Append("al.panel_name = '");
                queryBuilder.Append(panelName);
                queryBuilder.Append('\'');
            }

            if (!string.IsNullOrEmpty(circuitName))
            {
                if (queryBuilder.Length > 0)
                {
                    queryBuilder.Append(" and ");
                }
                queryBuilder.Append("al.circuit_name = '");
                queryBuilder.Append(circuitName);
                queryBuilder.Append('\'');
            }

            if (!string.IsNullOrEmpty(deviceName))
            {
                if (queryBuilder.Length > 0)
                {
                    queryBuilder.Append(" and ");
                }
                queryBuilder.Append("al.device_name = '");
                queryBuilder.Append(deviceName);
                queryBuilder.Append('\'');
            }
            var queryStartTime = await _client.Queryable<AlarmQueryConfig>().Where(a => a.QueryType == AlarmQueryType.QueryStartTime).FirstAsync();
            if (queryStartTime != null && !string.IsNullOrEmpty(queryStartTime.QueryValue))
            {
                DateTime startDate;
                var success = DateTime.TryParseExact(queryStartTime.QueryValue, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.RoundtripKind, out startDate);
                if (success)
                {
                    if (queryBuilder.Length > 0)
                    {
                        queryBuilder.Append(" and ");
                    }
                    queryBuilder.Append("al.created_time >= '");
                    queryBuilder.Append(startDate);
                    queryBuilder.Append('\'');
                }
            }

            if (filterUserOpt)
            {
                if (queryBuilder.Length > 0)
                {
                    queryBuilder.Append(" and ");
                }
                queryBuilder.Append("al.event_type != '");
                queryBuilder.Append('2');
                queryBuilder.Append('\'');
            }

            if (queryBuilder.Length <= 0)
            {
                return new ResponseBase<IChart?>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }

            config.Sql = config.Sql.Replace("[[Query]]", queryBuilder.ToString());

            var chart = await config.GetChartBySql(_client, MessageContext);

            return new ResponseBase<IChart?>()
            {
                Code = 20000,
                Data = chart
            };
        }

    }
}
