﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class LineImpedanceViewModel
    {
        [JsonProperty("LineTypeId")]
        public string LineTypeId { get; set; }

        [JsonProperty("Standard")]
        public string Standard { get; set; }
        [JsonProperty("ReactancePerMeter")]
        public decimal ReactancePerMeter { get; set; }
        [JsonProperty("ResistancePerMeter")]
        public decimal ResistancePerMeter { get; set; }
        [JsonProperty("Ampacity")]
        public decimal Ampacity { get; set; }
        
    }
}
