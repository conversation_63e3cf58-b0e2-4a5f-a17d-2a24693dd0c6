﻿using Microsoft.OpenApi.Models;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.WebApi.StaticContent;
using Swashbuckle.AspNetCore.Annotations;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Siemens.PanelManager.WebApi.Filter
{
    public class SwaggerOperationFilter : IOperationFilter
    {
        private IMessageContext _messageContext;
        public SwaggerOperationFilter(IMessageContextFactory staticMessage) 
        {
            var factory = (MessageContextFactory)staticMessage;
            _messageContext = factory.GetMessageContext(factory.DefaultLanguage);
        }

        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            IEnumerable<object> first = Array.Empty<object>();
            IEnumerable<object> enumerable = Array.Empty<object>();
            IEnumerable<object> second = Array.Empty<object>();
            if (context.MethodInfo != null)
            {
                first = context.MethodInfo.DeclaringType!.GetCustomAttributes(inherit: true);
                enumerable = context.MethodInfo.GetCustomAttributes(inherit: true);
            }

            if (context.ApiDescription?.ActionDescriptor?.EndpointMetadata != null)
            {
                second = context.ApiDescription.ActionDescriptor.EndpointMetadata;
            }

            IEnumerable<object> controllerAndActionAttributes = first.Union(enumerable).Union(second).Distinct();
            IEnumerable<object> actionAttributes = enumerable.Union(second).Distinct();
            ApplySwaggerOperationAttribute(operation, actionAttributes);
            ApplySwaggerOperationFilterAttributes(operation, context, controllerAndActionAttributes);
            ApplySwaggerResponseAttributes(operation, context, controllerAndActionAttributes);
        }

        private void ApplySwaggerOperationAttribute(OpenApiOperation operation, IEnumerable<object> actionAttributes)
        {
            SwaggerOperationAttribute? swaggerOperationAttribute = actionAttributes.OfType<SwaggerOperationAttribute>().FirstOrDefault();
            if (swaggerOperationAttribute == null)
            {
                return;
            }

            if (swaggerOperationAttribute.Summary != null)
            {
                operation.Summary = _messageContext.GetString(swaggerOperationAttribute.Summary) ?? swaggerOperationAttribute.Summary;
            }

            if (swaggerOperationAttribute.Description != null)
            {
                operation.Description = _messageContext.GetString(swaggerOperationAttribute.Description)?? swaggerOperationAttribute.Description;
            }

            if (swaggerOperationAttribute.OperationId != null)
            {
                operation.OperationId = swaggerOperationAttribute.OperationId;
            }

            if (swaggerOperationAttribute.Tags != null)
            {
                operation.Tags = swaggerOperationAttribute.Tags.Select((string tagName) => new OpenApiTag
                {
                    Name = tagName
                }).ToList();
            }
        }

        private void ApplySwaggerOperationFilterAttributes(OpenApiOperation operation, OperationFilterContext context, IEnumerable<object> controllerAndActionAttributes)
        {
            foreach (SwaggerOperationFilterAttribute item in controllerAndActionAttributes.OfType<SwaggerOperationFilterAttribute>())
            {
                var instance = Activator.CreateInstance(item.FilterType);
                if (instance != null)
                {
                    ((IOperationFilter)instance)?.Apply(operation, context);
                }
            }
        }

        private void ApplySwaggerResponseAttributes(OpenApiOperation operation, OperationFilterContext context, IEnumerable<object> controllerAndActionAttributes)
        {
            foreach (SwaggerResponseAttribute item in controllerAndActionAttributes.OfType<SwaggerResponseAttribute>())
            {
                string key = item.StatusCode.ToString();
                if (operation.Responses == null)
                {
                    operation.Responses = new OpenApiResponses();
                }

                if (!operation.Responses.TryGetValue(key, out var value))
                {
                    value = new OpenApiResponse();
                }

                if (item.Description != null)
                {
                    value.Description = item.Description;
                }

                operation.Responses[key] = value;
                if (item.ContentTypes != null)
                {
                    value.Content.Clear();
                    string[] contentTypes = item.ContentTypes;
                    foreach (string key2 in contentTypes)
                    {
                        OpenApiSchema? schema = (item.Type != null && item.Type != typeof(void)) ? context.SchemaGenerator.GenerateSchema(item.Type, context.SchemaRepository) : null;
                        if (schema != null)
                        {
                            value.Content.Add(key2, new OpenApiMediaType
                            {
                                Schema = schema
                            });
                        }
                    }
                }
            }
        }

    }
}
