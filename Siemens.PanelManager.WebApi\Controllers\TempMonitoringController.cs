using Akka.Util.Internal;
using MathNet.Numerics.Distributions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore.ValueGeneration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.OpenXmlFormats.Shared;
using Siemens.PanelManager.Common;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.Excel;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.DataPoint;
using Siemens.PanelManager.Model.Emun;
using Siemens.PanelManager.Model.UDC;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.Server.DataPoint;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Security.Claims;
using System.Text.RegularExpressions;
using TouchSocket.Core;
using TouchSocket.Sockets;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TempMonitoringController : SiemensApiControllerBase
    {
        private const string AssetCurrentStatusCacheKey = "AssetStatus:Currently-{0}";
        private SiemensExcelHelper _excelHelper => _provider.GetRequiredService<SiemensExcelHelper>();
        private DataPointServer _dataPointServer => _provider.GetRequiredService<DataPointServer>();
        private const string LastUpdatedTimeCacheKey = "Alarm:LastUpdatedTime";
        private readonly ILogger _log;
        private readonly ISqlSugarClient _client;
        private readonly IServiceProvider _provider;
        private readonly SiemensCache _cache;
        private AlarmExtendServer _alarmExtendServer;

        public TempMonitoringController(SiemensCache cache,
            SqlSugarScope client,
            IServiceProvider provider,
            ILogger<TempMonitoringController> logger,
            AlarmExtendServer alarmExtendServer)
        : base(provider, cache)
        {
            _log = logger;
            _client = client;
            _provider = provider;
            _cache = cache;
            _alarmExtendServer = alarmExtendServer;

        }

        [HttpPost("{assetId}/UploadExcel")]
        [SwaggerOperation(Summary = "Swagger_TempMonitoring_UploadExcel", Description = "Swagger_TempMonitoring_UploadExcel_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<bool>> UploadExcel(int assetId, [FromForm] IFormCollection form)
        {
            if (form == null || form.Files == null || form.Files.Count <= 0)
            {
                return new ResponseBase<bool>()
                {
                    Code = 40300,
                    Data = false,
                    Message = MessageContext.ErrorParam
                };
            }

            if (form.Files.Any(f => !Regex.IsMatch(f.FileName, "\\.xlsx$")))
            {
                return new ResponseBase<bool>()
                {
                    Code = 40300,
                    Data = false,
                    Message = MessageContext.ErrorParam
                };
            }

            var fileInfo = form.Files.First();
            try
            {
                var tempMonitorList = new List<TemperatureMonitorInfo>();
                using (var reader = fileInfo.OpenReadStream())
                {
                    await _excelHelper.QueryAsync(reader, (result) =>
                    {
                        foreach (var item in result.Datas)
                        {
                            var deviceId = string.Empty;
                            var location = string.Empty;
                            decimal? alarmTemp = null;
                            int? modbusPoint = null;
                            object? itemValue = null;
                            if (item.TryGetValue("A", out itemValue) && itemValue != null)
                            {
                                deviceId = itemValue.ToString();
                            }
                            if (item.TryGetValue("B", out itemValue) && itemValue != null)
                            {
                                location = itemValue.ToString();
                            }
                            if (item.TryGetValue("E", out itemValue) && itemValue != null)
                            {
                                if (decimal.TryParse(itemValue.ToString(), out decimal decimalValue))
                                {
                                    alarmTemp = decimalValue;
                                }
                            }
                            if (item.TryGetValue("I", out itemValue) && itemValue != null)
                            {
                                if (int.TryParse(itemValue.ToString(), out int intValue))
                                {
                                    modbusPoint = intValue;
                                }
                            }
                            if (modbusPoint != null)
                            {
                                tempMonitorList.Add(new TemperatureMonitorInfo()
                                {
                                    AssetId = assetId,
                                    DeviceId = deviceId,
                                    Location = location,
                                    ModbusPoint = modbusPoint,
                                    AlarmTemperature = alarmTemp,
                                    CreatedBy = UserName,
                                    CreatedTime = DateTime.Now,
                                    UpdatedBy = UserName,
                                    UpdatedTime = DateTime.Now,
                                });
                            }
                        }
                        return Task.FromResult(1);
                    }, startCell: "A2", sheet: "device-config");
                }

                var storageable = await _client.Storageable<TemperatureMonitorInfo>(tempMonitorList).WhereColumns(t => new { t.DeviceId, t.AssetId }).ToStorageAsync();

                var instertable = storageable.AsInsertable;
                var updateable = storageable.AsUpdateable;
                await updateable.IgnoreColumns(t => new { t.CreatedBy, t.CreatedTime }).ExecuteCommandAsync();

                await instertable.ExecuteCommandAsync();
                return new ResponseBase<bool>()
                {
                    Code = 20000,
                    Data = true
                };
            }
            catch (Exception ex)
            {
                _log.LogError(ex, "上传文件失败");
                return new ResponseBase<bool>()
                {
                    Code = 20000,
                    Data = false,
                    Message = MessageContext.GetErrorValue("Common_IncorrectFileFormat")
                };
            }

        }

        [HttpPost("{assetId}/UploadThirdExcel")]
        [SwaggerOperation(Summary = "Swagger_TempMonitoring_UploadThirdExcel", Description = "Swagger_TempMonitoring_UploadThirdExcel_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<bool>> UploadThirdExcel(int assetId, [FromForm] IFormCollection form)
        {
            if (form == null || form.Files == null || form.Files.Count <= 0)
            {
                return new ResponseBase<bool>()
                {
                    Code = 40300,
                    Data = false,
                    Message = MessageContext.ErrorParam
                };
            }

            if (form.Files.Any(f => !Regex.IsMatch(f.FileName, "\\.xlsx$")))
            {
                return new ResponseBase<bool>()
                {
                    Code = 40300,
                    Data = false,
                    Message = MessageContext.ErrorParam
                };
            }

            var fileInfo = form.Files.First();
            _client.Ado.BeginTran();
            try
            {
                var tempMonitorList = new List<TemperatureMonitorInfo>();
                var customDataPoint = new List<CustomDataPoint>();
                using (var reader = fileInfo.OpenReadStream())
                {
                    var assetList = await _client.Queryable<AssetInfo>().ToListAsync();
                    await _excelHelper.QueryAsync(reader, async (result) =>
                    {
                        foreach (var item in result.Datas)
                        {
                            var deviceId = string.Empty;
                            var location = string.Empty;
                            decimal? alarmTemp = null;
                            int? modbusPoint = null;
                            object? itemValue = null;
                            var dataPointName = string.Empty;
                            int? bindAssetId = null;
                            string? bindDataPoint = null;
                            AlarmSeverity? alarmSeverity = null;
                            string? bindDataPintName = null;

                            if (item.TryGetValue("A", out itemValue) && itemValue != null)
                            {
                                dataPointName = itemValue.ToString();
                            }
                            if (item.TryGetValue("C", out itemValue) && itemValue != null)
                            {
                                deviceId = itemValue.ToString();
                            }
                            if (item.TryGetValue("B", out itemValue) && itemValue != null)
                            {
                                location = itemValue.ToString();
                            }
                            #region 告警产生先注释
                            //if (item.TryGetValue("G", out itemValue) && itemValue != null)
                            //{
                            //    if (decimal.TryParse(itemValue.ToString(), out decimal decimalValue))
                            //    {
                            //        alarmTemp = decimalValue;
                            //    }
                            //}
                            #endregion
                            if (item.TryGetValue("D", out itemValue) && itemValue != null)
                            {
                                if (int.TryParse(itemValue.ToString(), out int intValue))
                                {
                                    modbusPoint = intValue;
                                }
                            }
                            #region 告警产生先注释
                            //if (item.TryGetValue("H", out itemValue) && itemValue != null)
                            //{
                            //    var  value = itemValue.ToString();

                            //    switch (value)
                            //    {
                            //        case "Low":
                            //        case "低":
                            //            alarmSeverity = AlarmSeverity.Low;
                            //            break;
                            //        case "Middle":
                            //        case "中":
                            //            alarmSeverity = AlarmSeverity.Middle;
                            //            break;
                            //        case "High":
                            //        case "高":
                            //            alarmSeverity = AlarmSeverity.High;
                            //            break;

                            //    }
                            //}
                            #endregion
                            if (item.TryGetValue("F", out itemValue) && itemValue != null)
                            {
                                bindDataPintName = itemValue.ToString();
                            }
                            if (item.TryGetValue("E", out itemValue) && itemValue != null)
                            {
                                var assetName = itemValue.ToString();
                                var asset = assetList.Where(a => a.AssetName == assetName).FirstOrDefault();
                                if (asset != null)
                                {
                                    bindAssetId = asset.Id;
                                    var dataPointServer = _dataPointServer;
                                    var models = await dataPointServer.GetDataPointInfos(asset.AssetLevel, asset.AssetType, asset.AssetModel);
                                    List<AssetStaticModelResult> assetStaticModels = new List<AssetStaticModelResult>();
                                    if (models != null && models.Count > 0)
                                    {
                                        assetStaticModels = models.Where(m => m.FilterIds != null && m.FilterIds.Contains("[T]")).Select(a => new AssetStaticModelResult()
                                        {
                                            Name = dataPointServer.GetDataPointName(a.Code, MessageContext),
                                            Code = a.Code,
                                        }).ToList();
                                        if (assetStaticModels != null && assetStaticModels.Count > 0)
                                        {
                                            var assetStaticModel = assetStaticModels.Where(a => a.Name == bindDataPintName).FirstOrDefault();
                                            if (assetStaticModel != null)
                                            {
                                                bindDataPoint = assetStaticModel.Code;
                                            }

                                        }
                                    }
                                }

                            }
                            if (!string.IsNullOrEmpty(dataPointName))
                            {
                                if (tempMonitorList.Any(a => a.BindAssetId == bindAssetId && a.DataPoint == bindDataPoint))
                                {
                                    bindDataPoint = null;
                                }
                                tempMonitorList.Add(new TemperatureMonitorInfo()
                                {
                                    AssetId = assetId,
                                    DeviceId = deviceId,
                                    Location = location,
                                    ModbusPoint = modbusPoint,
                                    AlarmTemperature = alarmTemp,
                                    CreatedBy = UserName,
                                    CreatedTime = DateTime.Now,
                                    UpdatedBy = UserName,
                                    UpdatedTime = DateTime.Now,
                                    AlarmSeverity = alarmSeverity,
                                    DataPoint = bindDataPoint ?? "",
                                    BindAssetId = bindAssetId ?? 0,
                                    DataPointName = dataPointName
                                });
                                customDataPoint.Add(new CustomDataPoint
                                {
                                    TargetAssetId = bindAssetId ?? 0,
                                    TargetDataPointName = bindDataPoint ?? string.Empty,
                                    RealAssetId = assetId,
                                    RealDataPointName = dataPointName ?? string.Empty,
                                    ValueMappingStr = string.Empty,
                                    CreatedBy = UserName,
                                    UpdatedBy = UserName,
                                    UpdatedTime = DateTime.Now,
                                    CreatedTime = DateTime.Now,
                                });

                                #region 告警产生先注释 功能可用，如果需要重新放开
                                //var assetInfo = assetList.Where(a => a.Id == assetId).FirstOrDefault();
                                //var alramName = MessageContext.TemperatureAlarm + "：" + assetInfo?.AssetName + "，" + dataPointName;
                                //var exists = await _client.Queryable<AlarmRule>().FirstAsync(a => a.TargetValue == assetId.ToString()
                                //                && a.Name == alramName && !a.IsDelete);
                                //RuleSection section = new RuleSection
                                //{
                                //    Point = dataPointName,
                                //    Compare = Model.Emun.Compare.GreaterThan,
                                //    DataValue = alarmTemp.ToString(),
                                //    LogicalOperator = LogicalOperator.None

                                //};
                                //List<RuleSection> sections = new List<RuleSection>();
                                //sections.Add(section);
                                //string logAction = "";
                                //int ruleId = 0;
                                //string ruleInfo = string.Empty;
                                //var severity = AlarmSeverity.Low;
                                //bool updateAlarmRule = false;
                                //AlarmRuleOpt alarmRuleOpt = AlarmRuleOpt.Current;
                                ////如果有告警级别，且之前不存在数据则新增
                                //if (alarmSeverity != null && exists == null) {
                                //    AlarmRule alarm = new AlarmRule()
                                //    {
                                //        Name = MessageContext.TemperatureAlarm + "：" + assetInfo?.AssetName + "，" + dataPointName,
                                //        Severity = (AlarmSeverity)alarmSeverity,
                                //        IsEnable = true,
                                //        TargetType = AlarmTargetType.Device,
                                //        TargetValue = assetId.ToString(),
                                //        Details = "threshold range",
                                //        Sections = sections,
                                //        IsDelete = false,
                                //        CreatedBy = UserName,
                                //        CreatedTime = DateTime.Now,
                                //        UpdatedBy = UserName,
                                //        UpdatedTime = DateTime.Now,
                                //    };
                                //    ruleId = await _client.Insertable<AlarmRule>(alarm).ExecuteReturnIdentityAsync();
                                //    logAction = "AddAlarmRule";
                                //    ruleInfo = alarm.RuleInfo;
                                //    updateAlarmRule = true;
                                //    alarmRuleOpt = AlarmRuleOpt.Add;
                                //}
                                ////如果有告警级别，且之前有告警规则，则对比下规则是否相同，不同就修改
                                //if (alarmSeverity != null && exists != null&& JsonConvert.SerializeObject(exists.Sections) != JsonConvert.SerializeObject(sections)) {
                                //    severity = exists.Severity;
                                //    ruleInfo = exists.RuleInfo;
                                //    exists.Sections = sections;
                                //    exists.Severity = (AlarmSeverity)alarmSeverity;
                                //    exists.UpdatedBy = UserName;
                                //    exists.UpdatedTime = DateTime.Now;
                                //    await _client.Updateable(exists).ExecuteCommandAsync();
                                //    ruleId = exists.Id;
                                //    logAction = "UpdateAlarmRule";
                                //    updateAlarmRule = true;
                                //    alarmRuleOpt = AlarmRuleOpt.Update;
                                //}
                                ////如果无告警级别，且之前有告警级别，则删除
                                //if (alarmSeverity == null && exists != null) {
                                //    severity = exists.Severity;
                                //    exists.IsDelete = true;
                                //    exists.UpdatedBy = UserName;
                                //    exists.UpdatedTime = DateTime.Now;
                                //    await _client.Updateable(exists).ExecuteCommandAsync();
                                //    ruleId = exists.Id;
                                //    logAction = "DeleteAlarmRule";
                                //    ruleInfo = exists.RuleInfo;
                                //    updateAlarmRule = true;
                                //    alarmRuleOpt = AlarmRuleOpt.Delete;
                                //}
                                //if (updateAlarmRule)
                                //{
                                //    await _alarmExtendServer.InsertOperationLog(UserName, logAction, AlarmSeverity.Middle, _client, alramName);
                                //    var history = new AlarmRuleChangeHistory()
                                //    {
                                //        RuleId = ruleId,
                                //        Timestamp = DateTime.Now.GetTimestampForSec(),
                                //        Operation = alarmRuleOpt,
                                //        RuleName = alramName,
                                //        RuleInfo = ruleInfo,
                                //        Severity = severity,
                                //        UpdatedBy = UserName,
                                //    };
                                //    await _client.Insertable(history).ExecuteCommandAsync();
                                //    _cache.Clear(LastUpdatedTimeCacheKey);
                                //    if (exists != null)
                                //    {
                                //        var server = _provider.GetRequiredService<AlarmRuleServer>();
                                //        await server.CheckAlarm(exists);
                                //    }
                                //}
                                #endregion

                            }
                        }
                        return 1;
                    }, startCell: "A2", sheet: "device-config");
                }
                var oldTempList = await _client.Queryable<TemperatureMonitorInfo>().Where(a => a.AssetId == assetId).ToListAsync();
                foreach (var temp in oldTempList)
                {
                    //if(!tempMonitorList.Any(a=>a.AssetId==assetId && a.DataPointName == temp.DataPointName
                    //                            &&a.BindAssetId==temp.BindAssetId&&a.DataPoint==temp.DataPoint))
                    //{
                    await _client.Deleteable<CustomDataPoint>().Where(c => c.RealAssetId == assetId && c.TargetAssetId == temp.BindAssetId && c.RealDataPointName == temp.DataPointName && c.TargetDataPointName == temp.DataPoint).ExecuteCommandAsync();

                    var result = await _client.Deleteable<TemperatureMonitorInfo>().Where(a => a.AssetId == assetId && a.DataPointName == temp.DataPointName
                                                && a.BindAssetId == temp.BindAssetId && a.DataPoint == temp.DataPoint).ExecuteCommandAsync();
                    //}
                }
                var storageable = await _client.Storageable<TemperatureMonitorInfo>(tempMonitorList).WhereColumns(t => new { t.AssetId, t.DataPointName, t.BindAssetId, t.DataPoint }).ToStorageAsync();
                var instertable = storageable.AsInsertable;
                var updateable = storageable.AsUpdateable;
                await updateable.IgnoreColumns(t => new { t.CreatedBy, t.CreatedTime }).ExecuteCommandAsync();
                await instertable.ExecuteCommandAsync();

                var customDataPointStorageable = await _client.Storageable<CustomDataPoint>(customDataPoint).WhereColumns(t => new { t.RealAssetId, t.RealDataPointName, t.TargetAssetId, t.TargetDataPointName }).ToStorageAsync();
                var customDataPointInstertable = customDataPointStorageable.AsInsertable;
                var customDataPointUpdateable = customDataPointStorageable.AsUpdateable;
                await customDataPointUpdateable.IgnoreColumns(t => new { t.CreatedBy, t.CreatedTime }).ExecuteCommandAsync();
                await customDataPointInstertable.ExecuteCommandAsync();
                var customerIds = customDataPoint.Select(a => a.TargetAssetId).ToList();
                var detailsAssetInfos = await _client.Queryable<AssetInfo>().Where(a => customerIds.Contains(a.Id)).ToListAsync();
                var dataPointServer = Provider.GetRequiredService<DataPointServer>();
                foreach ( var item in customDataPoint)
                {
                    var details = new SubstationDataPointConfigDetails()
                    {
                        BindAssetId = item.RealAssetId,
                        BindDataPoint = item.RealDataPointName ?? "",
                        DataPointCode = item.TargetDataPointName ?? "",
                    };
                    var detailsAssetInfo = detailsAssetInfos.FirstOrDefault(a => a.Id == item.TargetAssetId);
                    _cache.RemoveHashData($"AssetStatus:Currently-{item.TargetAssetId}");
                    if (detailsAssetInfo != null)
                    {
                        dataPointServer.UpdateCurrentlyData(item.TargetAssetId, details, _cache, detailsAssetInfo);
                    }
                }
                _client.Ado.CommitTran();
                _cache.Clear($"CustomDataPoint-{assetId}");
                return new ResponseBase<bool>()
                {
                    Code = 20000,
                    Data = true
                };
            }
            catch (Exception ex)
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "上传文件失败");
                return new ResponseBase<bool>()
                {
                    Code = 20000,
                    Data = false,
                    Message = MessageContext.GetErrorValue("Common_IncorrectFileFormat")
                };
            }

        }

        [HttpGet("export/{assetId}/ExportUploadTemplate")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_ExportUploadTemplate", Description = "Swagger_ExportUploadTemplate_Desc")]
        public async Task<IActionResult> ExportUploadTemplate(int assetId)
        {
            var thirdModelConfig = await _client.Queryable<ThirdModelConfig>()
                    .LeftJoin<AssetInfo>((t1, t2) => t1.Code == t2.ThirdPartCode)
                    .Where((t1, t2) => t2.Id == assetId)
                    .Select((t1, t2) => new
                    {
                        t1.Code,
                        t1.JsonData

                    }).FirstAsync();
            List<TemperatureMonitorResponse> universalDeviceConfigs = new List<TemperatureMonitorResponse>();
            if (thirdModelConfig != null)
            {
                var jsonData = JsonConvert.DeserializeObject<ThreePartModelTemplate>(thirdModelConfig.JsonData!);
                if (jsonData!.Treeview!.Any())
                {
                    foreach (var item in jsonData.Treeview!)
                    {
                        //获取子集集合
                        if (item.SubGroups != null && item.SubGroups!.Any())
                        {
                            foreach (var _item in item.SubGroups!)
                            {
                                if (_item.Properties != null && _item.Properties.Any())
                                {
                                    foreach (var secondItem in _item.Properties)
                                    {
                                        if (!universalDeviceConfigs.Exists(p => p.DataPoint == secondItem.PropertyName))
                                        {
                                            universalDeviceConfigs.Add(new TemperatureMonitorResponse()
                                            {
                                                DataPoint = secondItem.PropertyName ?? string.Empty
                                            });
                                        }
                                    }
                                }
                            }
                        }

                        if (item.Properties != null && item.Properties.Any())
                        {
                            foreach (var _item in item.Properties)
                            {
                                if (!universalDeviceConfigs.Exists(p => p.DataPoint == _item.PropertyName))
                                {
                                    universalDeviceConfigs.Add(new TemperatureMonitorResponse()
                                    {
                                        DataPoint = _item.PropertyName ?? string.Empty
                                    });
                                }
                            }
                        }
                    }
                }
            }

            var assets = await _client.Queryable<AssetInfo>().OrderBy(a => a.Id).ToListAsync();
            var data = new Dictionary<string, object>()
            {
                ["Data"] = universalDeviceConfigs,
                ["Assets"] = assets
            };
            var ms = new MemoryStream();
            await _excelHelper.SaveExcelAsync(ms, data, UserLanguage, "ThirdTemperatureDeviceTemplate");
            ms.Seek(0, SeekOrigin.Begin);
            return File(ms, "application/octet-stream", fileDownloadName: "ThirdTemperatureDeviceTemplate.xlsx", enableRangeProcessing: true);
        }

        [HttpGet("export/{assetId}/ExportTempMonitorInfo")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_ExportTempMonitorInfo", Description = "Swagger_ExportTempMonitorInfo_Desc")]
        public async Task<IActionResult> ExportTempMonitorInfo(int assetId)
        {

            var info = await _client.Queryable<TemperatureMonitorInfo>().LeftJoin<AssetInfo>((temp, asset) => temp.BindAssetId == asset.Id)
                .Where((temp, asset) => temp.AssetId == assetId).Select((temp, asset) => new TemperatureMonitorResponse
                {
                    Id = temp.Id,
                    //AlarmTemperature = temp.AlarmTemperature,
                    DataPoint = temp.DataPointName ?? string.Empty,
                    DeviceId = temp.DeviceId ?? string.Empty,
                    Location = temp.Location ?? string.Empty,
                    ModbusPoint = temp.ModbusPoint,
                    BindAssetName = asset.AssetName ?? string.Empty,
                    DataPointDesc = temp.DataPoint ?? string.Empty,
                    DataPointName = temp.DataPointName ?? string.Empty,
                    //AlarmSeverity = temp.AlarmSeverity!=null?(int)temp.AlarmSeverity==20?"高":(int)temp.AlarmSeverity == 10 ?"中": (int)temp.AlarmSeverity == 0?"低":"无":"无"
                }).ToListAsync();
            info = info.OrderBy(x => x.Id).ToList();
            info.ForEach(a => a.DataPointDesc = !string.IsNullOrEmpty(a.DataPointDesc) ? MessageContext.GetDataPointName(a.DataPointDesc) : "");
            var assets = await _client.Queryable<AssetInfo>().OrderBy(a => a.Id).ToListAsync();
            var data = new Dictionary<string, object>()
            {
                ["Data"] = info,
                ["Assets"] = assets
            };
            var ms = new MemoryStream();
            await _excelHelper.SaveExcelAsync(ms, data, UserLanguage, "ThirdTemperatureDeviceExport");
            ms.Seek(0, SeekOrigin.Begin);
            return File(ms, "application/octet-stream", fileDownloadName: "ThirdTemperatureDeviceExport.xlsx", enableRangeProcessing: true);
        }
        [HttpGet("{assetId}/getTempList")]
        [SwaggerOperation(Summary = "Swagger_TempMonitoring_GetTempList", Description = "Swagger_TempMonitoring_GetTempList_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<List<TemperatureDeviceStatus>>> GetTempList(int assetId)
        {
            if (assetId <= 0)
            {
                return new ResponseBase<List<TemperatureDeviceStatus>>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }
            List<TemperatureMonitorInfo> monitorList = await _client.Queryable<TemperatureMonitorInfo>()
                .Where(t => t.AssetId == assetId).OrderBy(t => t.Id).ToListAsync();
            List<TemperatureDeviceStatus> tempList = new List<TemperatureDeviceStatus>();
            var temperatureStatus = _cache.GetHashAllData(string.Format(AssetCurrentStatusCacheKey, assetId));
            foreach (var monitor in monitorList)
            {
                var tempStr = string.Empty;
                if (!string.IsNullOrEmpty(monitor.DataPointName))
                {
                    temperatureStatus.TryGetValue(monitor.DataPointName, out tempStr);

                }
                if (!string.IsNullOrEmpty(tempStr))
                {
                    //安科瑞温度范围，-50到125，所以暂时都按这种做，大于这些认为是无效温度
                    if (decimal.TryParse(tempStr, out decimal temp))
                    {
                        if (temp > 125 || temp < -50)
                        {
                            continue;
                        }
                    }
                    if (decimal.TryParse(tempStr, out decimal number))
                    {
                        tempStr = Math.Round(number, 1).ToString();
                    }
                    TemperatureDeviceStatus temperature = new TemperatureDeviceStatus
                    {
                        Location = monitor.Location ?? string.Empty,
                        Temperature = tempStr,
                        DeviceId = monitor.DeviceId ?? string.Empty,
                    };
                    tempList.Add(temperature);
                }
            }
            var returnData = tempList.OrderByDescending(a => Convert.ToDecimal(a.Temperature)).ThenBy(a => a.DeviceId).ToList();
            return new ResponseBase<List<TemperatureDeviceStatus>>()
            {
                Code = 20000,
                Data = returnData
            };

        }
        [HttpGet("{assetId}")]
        [SwaggerOperation(Summary = "Swagger_TempMonitoring_Get", Description = "Swagger_TempMonitoring_Get_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<SearchBase<TemperatureMonitorResponse>> GetMonitorList(int assetId,
            [AllowNull] string? location = null,
            [AllowNull] int page = 1,
            [AllowNull] int pageSize = 10)
        {
            RefAsync<int> totalNumber = new RefAsync<int>();
            RefAsync<int> totalPage = new RefAsync<int>();

            var query = _client.Queryable<TemperatureMonitorInfo>().Where(t => t.AssetId == assetId).OrderBy(t => t.Id);

            if (!string.IsNullOrEmpty(location))
            {
                query.Where(t => t.Location != null && t.Location.Contains(location));
            }

            var result = await query.ToPageListAsync(page, pageSize, totalNumber, totalPage);
            var assetIds = result.Where(r => r.BindAssetId.HasValue).Select(r => r.BindAssetId ?? 0).ToArray();
            var assetInfos = await _client.Queryable<AssetInfo>().Where(a => assetIds.Contains(a.Id)).ToArrayAsync();
            var responseDatas = new List<TemperatureMonitorResponse>();
            foreach (var info in result)
            {
                var assetInfo = assetInfos.FirstOrDefault(a => a.Id == (info.BindAssetId ?? 0));

                responseDatas.Add(new TemperatureMonitorResponse()
                {
                    Id = info.Id,
                    AssetId = info.AssetId,
                    //AlarmTemperature = info.AlarmTemperature,
                    AlarmTemperature = info.AlarmTemperature.HasValue ? Math.Round(info.AlarmTemperature.Value, 2) : null,
                    BindAssetId = info.BindAssetId > 0 ? info.BindAssetId : null,
                    DataPoint = info.DataPoint ?? string.Empty,
                    DeviceId = info.DeviceId ?? string.Empty,
                    DeviceName = info.DeviceName,
                    Location = info.Location ?? string.Empty,
                    ModbusPoint = info.ModbusPoint,
                    BindAssetName = assetInfo?.AssetName ?? string.Empty,
                    DataPointDesc = assetInfo != null ? !string.IsNullOrEmpty(info.DataPoint) ? MessageContext.GetDataPointName(info.DataPoint) : string.Empty : string.Empty,
                    DataPointName = info.DataPointName ?? string.Empty,
                    AlarmSeverity = info.AlarmSeverity == null ? "-1" : ((int)info.AlarmSeverity).ToString()
                });
            }
            return new SearchBase<TemperatureMonitorResponse>()
            {
                Page = page,
                TotalCount = totalNumber.Value,
                Code = 20000,
                Items = responseDatas
            };
        }

        [HttpPut("{assetId}/update")]
        [SwaggerOperation(Summary = "Swagger_TempMonitoring_Update", Description = "Swagger_TempMonitoring_Update_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<bool>> UpdateTempratureMonitorInfo(int assetId, TemperatureMonitorInfo monitorInfo)
        {
            var oldMonitor = await _client.Queryable<TemperatureMonitorInfo>().FirstAsync(a => a.Id == monitorInfo.Id);
            if (assetId <= 0 || monitorInfo == null || monitorInfo.Id <= 0 || oldMonitor == null)
            {
                return new ResponseBase<bool>()
                {
                    Code = 40300,
                    Data = false,
                    Message = MessageContext.ErrorParam
                };
            }
            if (!string.IsNullOrEmpty(monitorInfo.DataPoint) && monitorInfo.BindAssetId > 0)
            {
                var dataPointVerify = await _client.Queryable<TemperatureMonitorInfo>().FirstAsync(a => a.DataPoint == monitorInfo.DataPoint
                                                                                            && a.BindAssetId == monitorInfo.BindAssetId && a.Id != monitorInfo.Id);
                if (dataPointVerify != null)
                {
                    return new ResponseBase<bool>()
                    {
                        Code = 40300,
                        Data = false,
                        Message = MessageContext.TempMonitor_Verify_DataPoint
                    };
                }
            }

            monitorInfo.AssetId = assetId;
            monitorInfo.UpdatedBy = UserName;
            monitorInfo.UpdatedTime = DateTime.Now;
            _client.Ado.BeginTran();
            try
            {

                if (monitorInfo.BindAssetId.HasValue && (string.IsNullOrEmpty(monitorInfo.DataPoint) && monitorInfo.BindAssetId.Value <= 0))
                {
                    return new ResponseBase<bool>()
                    {
                        Code = 40300,
                        Data = false,
                        Message = MessageContext.ErrorParam
                    };
                }
                if (!monitorInfo.BindAssetId.HasValue)
                {
                    monitorInfo.BindAssetId = 0;
                }
                var result = await _client.Updateable(monitorInfo).IgnoreColumns(t => new { t.CreatedBy, t.CreatedTime, t.DataPointName }).ExecuteCommandAsync();
                if (result <= 0)
                {
                    return new ResponseBase<bool>()
                    {
                        Code = 40400,
                        Data = false,
                        Message = MessageContext.GetErrorValue("Common_NotExists")
                    };
                }

                //update  

                var assetInfo = await _client.Queryable<AssetInfo>().Where(a => a.Id == assetId).FirstAsync();
                if (assetInfo != null && assetInfo.AssetModel == "Other")
                {
                    #region 更新告警 先注释
                    //var alramName = MessageContext.TemperatureAlarm + "：" + assetInfo?.AssetName + "，" + oldMonitor.DataPointName;
                    //var exists = await _client.Queryable<AlarmRule>().FirstAsync(a => a.TargetValue == assetId.ToString()
                    //                               && a.Name == alramName && !a.IsDelete);
                    //int ruleId = 0;
                    //string ruleInfo = string.Empty;
                    //var severity = AlarmSeverity.Low;
                    //bool updateAlarmRule = false;
                    //string logAction = string.Empty;
                    //AlarmRuleOpt alarmRuleOpt = AlarmRuleOpt.Current;
                    //RuleSection section = new RuleSection
                    //{
                    //    Point = oldMonitor.DataPointName ?? "",
                    //    Compare = Model.Emun.Compare.GreaterThan,
                    //    DataValue = monitorInfo.AlarmTemperature?.ToString() ?? "",
                    //    LogicalOperator = LogicalOperator.None
                    //};
                    //List<RuleSection> sections = new List<RuleSection>();
                    //sections.Add(section);
                    ////如果有告警级别，且之前不存在数据则新增
                    //if ((monitorInfo.AlarmSeverity != null&& monitorInfo.AlarmSeverity>=0) && exists == null)
                    //{
                    //    AlarmRule alarm = new AlarmRule()
                    //    {
                    //        Name = alramName,
                    //        Severity = (AlarmSeverity)monitorInfo.AlarmSeverity,
                    //        IsEnable = true,
                    //        TargetType = AlarmTargetType.Device,
                    //        TargetValue = assetId.ToString(),
                    //        Details = "threshold range",
                    //        Sections = sections,
                    //        IsDelete = false,
                    //        CreatedBy = UserName,
                    //        CreatedTime = DateTime.Now,
                    //        UpdatedBy = UserName,
                    //        UpdatedTime = DateTime.Now,
                    //    };
                    //    ruleId = await _client.Insertable<AlarmRule>(alarm).ExecuteReturnIdentityAsync();
                    //    logAction = "AddAlarmRule";
                    //    ruleInfo = alarm.RuleInfo;
                    //    updateAlarmRule = true;
                    //    alarmRuleOpt = AlarmRuleOpt.Add;
                    //}
                    //if (exists != null&&monitorInfo.AlarmSeverity != null && monitorInfo.AlarmSeverity >= 0
                    //    && (JsonConvert.SerializeObject(exists.Sections) != JsonConvert.SerializeObject(sections) || exists.Severity != monitorInfo.AlarmSeverity))
                    //{
                    //    severity = (AlarmSeverity)monitorInfo?.AlarmSeverity;
                    //    ruleInfo = exists.RuleInfo;
                    //    exists.Sections = sections;
                    //    exists.Severity = (AlarmSeverity)monitorInfo.AlarmSeverity;
                    //    exists.UpdatedBy = UserName;
                    //    exists.UpdatedTime = DateTime.Now;
                    //    await _client.Updateable(exists).ExecuteCommandAsync();
                    //    ruleId = exists.Id;
                    //    logAction = "UpdateAlarmRule";
                    //    updateAlarmRule = true;
                    //    alarmRuleOpt = AlarmRuleOpt.Update;
                    //} //如果无告警级别，且之前有告警级别，则删除
                    //if ((monitorInfo.AlarmSeverity == null || monitorInfo.AlarmSeverity < 0) && exists != null)
                    //{
                    //    severity = exists.Severity;
                    //    exists.IsDelete = true;
                    //    exists.UpdatedBy = UserName;
                    //    exists.UpdatedTime = DateTime.Now;
                    //    await _client.Updateable(exists).ExecuteCommandAsync();
                    //    ruleId = exists.Id;
                    //    logAction = "DeleteAlarmRule";
                    //    ruleInfo = exists.RuleInfo;
                    //    updateAlarmRule = true;
                    //    alarmRuleOpt = AlarmRuleOpt.Delete;
                    //}
                    //if (updateAlarmRule)
                    //{
                    //    await _alarmExtendServer.InsertOperationLog(UserName, logAction, AlarmSeverity.Middle, _client, alramName);
                    //    var history = new AlarmRuleChangeHistory()
                    //    {
                    //        RuleId = ruleId,
                    //        Timestamp = DateTime.Now.GetTimestampForSec(),
                    //        Operation = alarmRuleOpt,
                    //        RuleName = alramName,
                    //        RuleInfo = ruleInfo,
                    //        Severity = severity,
                    //        UpdatedBy = UserName,
                    //    };
                    //    await _client.Insertable(history).ExecuteCommandAsync();
                    //    _cache.Clear(LastUpdatedTimeCacheKey);
                    //    if (exists != null)
                    //    {
                    //        var server = _provider.GetRequiredService<AlarmRuleServer>();
                    //        await server.CheckAlarm(exists);
                    //    }
                    //}
                    #endregion
                    //同步更新asset_custom_data_point表
                    var oldConfig = await _client.Queryable<CustomDataPoint>().FirstAsync(c => c.RealAssetId == assetId
                    && c.RealDataPointName == oldMonitor.DataPointName && c.TargetAssetId == oldMonitor.BindAssetId && c.TargetDataPointName == oldMonitor.DataPoint);
                    if (oldConfig == null)
                    {
                        var customData = new CustomDataPoint
                        {
                            TargetAssetId = monitorInfo.BindAssetId ?? 0,
                            TargetDataPointName = monitorInfo.DataPoint ?? "",
                            RealAssetId = assetId,
                            RealDataPointName = oldMonitor.DataPointName ?? "",
                            ValueMappingStr = string.Empty,
                            CreatedBy = UserName,
                            UpdatedBy = UserName,
                            UpdatedTime = DateTime.Now,
                            CreatedTime = DateTime.Now,
                        };
                        await _client.Insertable(customData).ExecuteReturnIdentityAsync();
                    }
                    else
                    {
                        var configs = Cache.Get<CustomDataPoint[]>($"CustomDataPoint-{assetId}");
                        _cache.Clear($"CustomDataPoint-{oldConfig.RealAssetId}");
                        oldConfig.TargetAssetId = monitorInfo.BindAssetId ?? 0;
                        oldConfig.TargetDataPointName = monitorInfo.DataPoint ?? "";
                        oldConfig.RealAssetId = assetId;
                        oldConfig.ValueMappingStr = string.Empty;
                        oldConfig.UpdatedBy = UserName;
                        oldConfig.UpdatedTime = DateTime.Now;

                        await _client.Updateable(oldConfig).ExecuteCommandAsync();
                    }
                    var dataPointServer = Provider.GetRequiredService<DataPointServer>();
                    var details = new SubstationDataPointConfigDetails()
                    {
                        BindAssetId = assetId,
                        BindDataPoint = oldMonitor.DataPointName ?? "",
                        DataPointCode = monitorInfo.DataPoint ?? "",
                    };
                    var detailsAssetInfo = await _client.Queryable<AssetInfo>().Where(a => a.Id == monitorInfo.BindAssetId).FirstAsync();
                    string[] hashKeys = new string[] { oldMonitor.DataPoint ?? "" };
                    _cache.RemoveHashData($"AssetStatus:Currently-{monitorInfo.BindAssetId}", hashKeys);
                    dataPointServer.UpdateCurrentlyData(monitorInfo.BindAssetId ?? 0, details, _cache, detailsAssetInfo);
                    _client.Ado.CommitTran();
                    
                }
               
            }
            catch (Exception ex)
            {
                _client.Ado.RollbackTran();
                _log.LogError(ex, "更新温度点位绑定失败");
                return new ResponseBase<bool>()
                {
                    Code = 20000,
                    Data = false,
                };
            }
            return new ResponseBase<bool>()
            {
                Code = 20000,
                Data = true,
            };
        }

        [HttpDelete("{assetId}/delete")]
        [SwaggerOperation(Summary = "Swagger_TempMonitoring_Delete", Description = "Swagger_TempMonitoring_Delete_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<bool>> DeleteTempratureMonitorInfo(int assetId, string ids)
        {
            if (assetId <= 0 || string.IsNullOrEmpty(ids))
            {
                return new ResponseBase<bool>()
                {
                    Code = 40300,
                    Data = false,
                    Message = MessageContext.ErrorParam
                };
            }

            var idStrs = ids.Split(',');
            var idList = new List<int>();
            foreach (var str in idStrs)
            {
                if (int.TryParse(str, out int id))
                {
                    idList.Add(id);
                }
            }

            if (idList.Count <= 0)
            {
                return new ResponseBase<bool>()
                {
                    Code = 40300,
                    Data = false,
                    Message = MessageContext.ErrorParam
                };
            }
            _client.Ado.BeginTran();

            var assetInfo = await _client.Queryable<AssetInfo>().Where(a => a.Id == assetId).FirstAsync();
            if (assetInfo != null && assetInfo.AssetModel == "Other")
            {
                foreach (int id in idList)
                {
                    var oldMonitor = await _client.Queryable<TemperatureMonitorInfo>().FirstAsync(a => a.Id == id);
                    if (oldMonitor != null)
                    {
                        #region 注释告警
                        //删除告警规则
                        //var alramName = MessageContext.TemperatureAlarm + "：" + assetInfo?.AssetName + "，" + oldMonitor.DataPointName;
                        //await _client.Deleteable<AlarmRule>().Where(c => c.TargetValue == assetId.ToString() && c.Name == alramName).ExecuteCommandAsync();
                        #endregion
                        //删除asset_custom_data_point表数据
                        await _client.Deleteable<CustomDataPoint>().Where(c => c.RealAssetId == assetId && c.TargetAssetId == oldMonitor.BindAssetId && c.RealDataPointName == oldMonitor.DataPointName && c.TargetDataPointName == oldMonitor.DataPoint).ExecuteCommandAsync();

                        var actorRef = _provider.GetRequiredService<IAssetDataProxyRef>();
                        var datas = new Dictionary<string, string>();
                        var customAssets = await _client.Queryable<CustomDataPoint>()
                                                .Where(c => oldMonitor.AssetId == c.RealAssetId)
                                                .ToListAsync();
                        var configDps = customAssets.Select(c => c.TargetDataPointName).ToArray();
                        foreach (var item in configDps)
                        {
                            if (!datas.ContainsKey(item))
                            {
                                datas.Add(item, string.Empty);
                            }
                            
                        }
                        var assetInfoMoinitor = await _client.Queryable<AssetInfo>().Where(a => a.Id == oldMonitor.BindAssetId).FirstAsync();
                        actorRef.DataChanged(new Model.DataFlow.AssetChangeData
                        {
                            AssetId = oldMonitor.BindAssetId??0,
                            AssetLevel = assetInfoMoinitor.AssetLevel,
                            AssetModel = assetInfoMoinitor.AssetModel ?? string.Empty,
                            AssetType = assetInfoMoinitor.AssetType ?? string.Empty,
                            AssetName = assetInfoMoinitor.AssetName,
                            ChangeDatas = datas,
                            ChangeTime = DateTime.Now,
                        });
                        string[] hashKeys = new string[] { oldMonitor.DataPoint??""};
                        _cache.RemoveHashData($"AssetStatus:Currently-{oldMonitor.BindAssetId}", hashKeys);
                    }

                }
            }
            var result = await _client.Deleteable<TemperatureMonitorInfo>().Where(d => d.AssetId == assetId && idList.Contains(d.Id)).ExecuteCommandAsync();
            _client.Ado.CommitTran();
            _cache.Clear($"CustomDataPoint-{assetId}");
            return new ResponseBase<bool>()
            {
                Code = 20000,
                Data = result > 0
            };
        }

        [HttpGet("{assetId}/currently")]
        [SwaggerOperation(Summary = "Swagger_TempMonitoring_GetCurrently", Description = "Swagger_TempMonitoring_GetCurrently_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<TemperatureCurrentlyResult>> GetCurrentlyStatusList(int assetId)
        {
            if (assetId <= 0)
            {
                return new ResponseBase<TemperatureCurrentlyResult>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            List<TemperatureMonitorInfo> monitorList = await _client.Queryable<TemperatureMonitorInfo>().Where(t => t.AssetId == assetId).OrderBy(t => t.Id).ToListAsync();
            AssetRelation[]? assetRelations = null;
            var result = new TemperatureCurrentlyResult();
            var temperatureStatus = _cache.GetHashAllData(string.Format(AssetCurrentStatusCacheKey, assetId));
            var panelList = new List<TemperaturePanelResult>();
            var circuitList = new List<TempratureCircuitResult>();
            foreach (var monitor in monitorList)
            {
                var isOnline = string.Empty;
                var tempStr = string.Empty;
                DateTime? time = DateTime.Now;
                temperatureStatus.TryGetValue($"Sensor_{monitor.ModbusPoint - 40001}_connect", out isOnline);
                if (!string.IsNullOrEmpty(monitor.DataPointName))
                {
                    temperatureStatus.TryGetValue(monitor.DataPointName, out tempStr);
                    //安科瑞温度范围，-50到125，所以暂时都按这种做，大于这些认为是无效温度
                    if (decimal.TryParse(tempStr, out decimal temp))
                    {
                        if (temp > 125 || temp < -50)
                        {
                            tempStr = string.Empty;
                        }
                        //第三方测温如果没有温度数据，认为是离线
                        isOnline = string.IsNullOrEmpty(tempStr) ? "0" : "1";
                    }
                    //第三方测温如果没有温度数据，认为是离线
                    isOnline = string.IsNullOrEmpty(tempStr) ? "0" : "1";
                }
                else
                {
                    temperatureStatus.TryGetValue($"Sensor_{monitor.ModbusPoint - 40001}", out tempStr);
                }
                if (!string.IsNullOrEmpty(tempStr))
                {
                    if (decimal.TryParse(tempStr, out decimal number))
                    {
                        tempStr = Math.Round(number, 1).ToString();
                    }
                }

                if (!"1".Equals(isOnline))
                {
                    var timeStr = string.Empty;
                    temperatureStatus.TryGetValue($"Sensor_{monitor.ModbusPoint - 40001}_offline_time", out timeStr);
                    if (!string.IsNullOrEmpty(timeStr))
                    {
                        time = DateTime.ParseExact(timeStr, "yyyymmddHHMMss", null);
                    }
                    else
                    {
                        time = null;
                    }
                }
                if (monitor.BindAssetId.HasValue && !string.IsNullOrEmpty(monitor.DataPoint))
                {
                    assetRelations ??= await _client.Queryable<AssetRelation>().ToArrayAsync();

                    var relation = assetRelations.FirstOrDefault(ar => ar.ChildId == monitor.BindAssetId);
                    if (relation != null)
                    {
                        switch (relation.AssetLevel)
                        {
                            case AssetLevel.Panel:
                                {
                                    var panelInfo = panelList.FirstOrDefault(p => p.PanelId == monitor.BindAssetId);
                                    if (panelInfo == null)
                                    {
                                        panelInfo = new TemperaturePanelResult()
                                        {
                                            PanelId = monitor.BindAssetId.Value
                                        };

                                        panelList.Add(panelInfo);
                                    }

                                    var match = Regex.Match(monitor.DataPoint ?? string.Empty, "^(?<phase>[A|B|C|N]{1})ConnectPoint_(?<index>[\\d]{1,3})_(?<number>[\\d]{1,3})$");

                                    if (match.Success)
                                    {
                                        var index = int.Parse(match.Groups["index"].Value);
                                        var number = int.Parse(match.Groups["number"].Value);
                                        var busTemperatureItem = panelInfo.BusList.FirstOrDefault(b => b.Index == index && b.Number == number);
                                        if (busTemperatureItem == null)
                                        {
                                            busTemperatureItem = new CurrentlyTemperatureItem()
                                            {
                                                Index = index,
                                                Number = number
                                            };

                                            panelInfo.BusList.Add(busTemperatureItem);
                                        }

                                        switch (match.Groups["phase"].Value)
                                        {
                                            case "A":
                                                busTemperatureItem.APhase = new TemperatureDeviceStatus
                                                {
                                                    DeviceId = monitor.DeviceId ?? string.Empty,
                                                    Location = monitor.Location ?? string.Empty,
                                                    OnlineStatus = "1".Equals(isOnline) ? "on" : "off",
                                                    Temperature = tempStr ?? string.Empty,
                                                    MeasuringTime = time
                                                };
                                                break;
                                            case "B":
                                                busTemperatureItem.BPhase = new TemperatureDeviceStatus
                                                {
                                                    DeviceId = monitor.DeviceId ?? string.Empty,
                                                    Location = monitor.Location ?? string.Empty,
                                                    OnlineStatus = "1".Equals(isOnline) ? "on" : "off",
                                                    Temperature = tempStr ?? string.Empty,
                                                    MeasuringTime = time
                                                };
                                                break;
                                            case "C":
                                                busTemperatureItem.CPhase = new TemperatureDeviceStatus
                                                {
                                                    DeviceId = monitor.DeviceId ?? string.Empty,
                                                    Location = monitor.Location ?? string.Empty,
                                                    OnlineStatus = "1".Equals(isOnline) ? "on" : "off",
                                                    Temperature = tempStr ?? string.Empty,
                                                    MeasuringTime = time
                                                };
                                                break;
                                            case "N":
                                                busTemperatureItem.NPhase = new TemperatureDeviceStatus
                                                {
                                                    DeviceId = monitor.DeviceId ?? string.Empty,
                                                    Location = monitor.Location ?? string.Empty,
                                                    OnlineStatus = "1".Equals(isOnline) ? "on" : "off",
                                                    Temperature = tempStr ?? string.Empty,
                                                    MeasuringTime = time
                                                };
                                                break;
                                            default: break;
                                        }
                                    }

                                    match = Regex.Match(monitor.DataPoint ?? string.Empty, "^(?<phase>[A|B|C|N]{1})Connector_(?<direction>Left|Right)(?<index>[\\d]{1,3})$");

                                    if (match.Success)
                                    {
                                        var index = int.Parse(match.Groups["index"].Value);

                                        CurrentlyTemperatureItem temperatureItem;
                                        if ("Left".Equals(match.Groups["direction"].Value))
                                        {
                                            var connectPoint = panelInfo.LeftConnectPointList.FirstOrDefault(connect => connect.Index == index);
                                            if (connectPoint == null)
                                            {
                                                temperatureItem = new CurrentlyTemperatureItem
                                                {
                                                    Index = index,
                                                };
                                                panelInfo.LeftConnectPointList.Add(temperatureItem);
                                            }
                                            else
                                            {
                                                temperatureItem = connectPoint;
                                            }
                                        }
                                        else
                                        {
                                            var connectPoint = panelInfo.RightConnectPointList.FirstOrDefault(connect => connect.Index == index);
                                            if (connectPoint == null)
                                            {
                                                temperatureItem = new CurrentlyTemperatureItem
                                                {
                                                    Index = index,
                                                };
                                                panelInfo.RightConnectPointList.Add(temperatureItem);
                                            }
                                            else
                                            {
                                                temperatureItem = connectPoint;
                                            }
                                        }

                                        switch (match.Groups["phase"].Value)
                                        {
                                            case "A":
                                                temperatureItem.APhase = new TemperatureDeviceStatus
                                                {
                                                    DeviceId = monitor.DeviceId ?? string.Empty,
                                                    Location = monitor.Location ?? string.Empty,
                                                    OnlineStatus = "1".Equals(isOnline) ? "on" : "off",
                                                    Temperature = tempStr ?? string.Empty,
                                                    MeasuringTime = time
                                                };
                                                break;
                                            case "B":
                                                temperatureItem.BPhase = new TemperatureDeviceStatus
                                                {
                                                    DeviceId = monitor.DeviceId ?? string.Empty,
                                                    Location = monitor.Location ?? string.Empty,
                                                    OnlineStatus = "1".Equals(isOnline) ? "on" : "off",
                                                    Temperature = tempStr ?? string.Empty,
                                                    MeasuringTime = time
                                                };
                                                break;
                                            case "C":
                                                temperatureItem.CPhase = new TemperatureDeviceStatus
                                                {
                                                    DeviceId = monitor.DeviceId ?? string.Empty,
                                                    Location = monitor.Location ?? string.Empty,
                                                    OnlineStatus = "1".Equals(isOnline) ? "on" : "off",
                                                    Temperature = tempStr ?? string.Empty,
                                                    MeasuringTime = time
                                                };
                                                break;
                                            case "N":
                                                temperatureItem.NPhase = new TemperatureDeviceStatus
                                                {
                                                    DeviceId = monitor.DeviceId ?? string.Empty,
                                                    Location = monitor.Location ?? string.Empty,
                                                    OnlineStatus = "1".Equals(isOnline) ? "on" : "off",
                                                    Temperature = tempStr ?? string.Empty,
                                                    MeasuringTime = time
                                                };
                                                break;
                                            default: break;
                                        }
                                    }
                                }
                                break;
                            case AssetLevel.Circuit:
                                {
                                    var circuit = circuitList.FirstOrDefault(p => p.CircuitId == monitor.BindAssetId);

                                    if (circuit == null)
                                    {
                                        circuit = new TempratureCircuitResult
                                        {
                                            CircuitId = monitor.BindAssetId.Value
                                        };

                                        circuitList.Add(circuit);
                                    }

                                    var match = Regex.Match(monitor.DataPoint ?? string.Empty, "^(?<phase>[A|B|C|N]{1})PhaseTemp(?<index>[\\d]{1})$");
                                    if (match.Success)
                                    {
                                        var index = int.Parse(match.Groups["index"].Value);
                                        switch (match.Groups["phase"].Value)
                                        {
                                            case "A":
                                                if (1 == index)
                                                {
                                                    circuit.Income ??= new CurrentlyTemperatureItem();
                                                    circuit.Income.APhase = new TemperatureDeviceStatus
                                                    {
                                                        DeviceId = monitor.DeviceId ?? string.Empty,
                                                        Location = monitor.Location ?? string.Empty,
                                                        OnlineStatus = "1".Equals(isOnline) ? "on" : "off",
                                                        Temperature = tempStr ?? string.Empty,
                                                        MeasuringTime = time
                                                    };
                                                }
                                                else
                                                {
                                                    circuit.Outcome ??= new CurrentlyTemperatureItem();
                                                    circuit.Outcome.APhase = new TemperatureDeviceStatus
                                                    {
                                                        DeviceId = monitor.DeviceId ?? string.Empty,
                                                        Location = monitor.Location ?? string.Empty,
                                                        OnlineStatus = "1".Equals(isOnline) ? "on" : "off",
                                                        Temperature = tempStr ?? string.Empty,
                                                        MeasuringTime = time
                                                    };
                                                }
                                                break;
                                            case "B":
                                                if (1 == index)
                                                {
                                                    circuit.Income ??= new CurrentlyTemperatureItem();
                                                    circuit.Income.BPhase = new TemperatureDeviceStatus
                                                    {
                                                        DeviceId = monitor.DeviceId ?? string.Empty,
                                                        Location = monitor.Location ?? string.Empty,
                                                        OnlineStatus = "1".Equals(isOnline) ? "on" : "off",
                                                        Temperature = tempStr ?? string.Empty,
                                                        MeasuringTime = time
                                                    };
                                                }
                                                else
                                                {
                                                    circuit.Outcome ??= new CurrentlyTemperatureItem();
                                                    circuit.Outcome.BPhase = new TemperatureDeviceStatus
                                                    {
                                                        DeviceId = monitor.DeviceId ?? string.Empty,
                                                        Location = monitor.Location ?? string.Empty,
                                                        OnlineStatus = "1".Equals(isOnline) ? "on" : "off",
                                                        Temperature = tempStr ?? string.Empty,
                                                        MeasuringTime = time
                                                    };
                                                }
                                                break;
                                            case "C":
                                                if (1 == index)
                                                {
                                                    circuit.Income ??= new CurrentlyTemperatureItem();
                                                    circuit.Income.CPhase = new TemperatureDeviceStatus
                                                    {
                                                        DeviceId = monitor.DeviceId ?? string.Empty,
                                                        Location = monitor.Location ?? string.Empty,
                                                        OnlineStatus = "1".Equals(isOnline) ? "on" : "off",
                                                        Temperature = tempStr ?? string.Empty,
                                                        MeasuringTime = time
                                                    };
                                                }
                                                else
                                                {
                                                    circuit.Outcome ??= new CurrentlyTemperatureItem();
                                                    circuit.Outcome.CPhase = new TemperatureDeviceStatus
                                                    {
                                                        DeviceId = monitor.DeviceId ?? string.Empty,
                                                        Location = monitor.Location ?? string.Empty,
                                                        OnlineStatus = "1".Equals(isOnline) ? "on" : "off",
                                                        Temperature = tempStr ?? string.Empty,
                                                        MeasuringTime = time
                                                    };
                                                }
                                                break;
                                            case "N":
                                                if (1 == index)
                                                {
                                                    circuit.Income ??= new CurrentlyTemperatureItem();
                                                    circuit.Income.NPhase = new TemperatureDeviceStatus
                                                    {
                                                        DeviceId = monitor.DeviceId ?? string.Empty,
                                                        Location = monitor.Location ?? string.Empty,
                                                        OnlineStatus = "1".Equals(isOnline) ? "on" : "off",
                                                        Temperature = tempStr ?? string.Empty,
                                                        MeasuringTime = time
                                                    };
                                                }
                                                else
                                                {
                                                    circuit.Outcome ??= new CurrentlyTemperatureItem();
                                                    circuit.Outcome.NPhase = new TemperatureDeviceStatus
                                                    {
                                                        DeviceId = monitor.DeviceId ?? string.Empty,
                                                        Location = monitor.Location ?? string.Empty,
                                                        OnlineStatus = "1".Equals(isOnline) ? "on" : "off",
                                                        Temperature = tempStr ?? string.Empty,
                                                        MeasuringTime = time
                                                    };
                                                }
                                                break;
                                            default: break;
                                        }
                                    }
                                }
                                break;
                            default: break;
                        }
                    }
                }
                else
                {
                    result.UnknownList.Add(new TemperatureDeviceStatus
                    {
                        DeviceId = monitor.DeviceId ?? string.Empty,
                        Location = monitor.Location ?? string.Empty,
                        OnlineStatus = "1".Equals(isOnline) ? "on" : "off",
                        Temperature = tempStr ?? string.Empty,
                        MeasuringTime = time
                    });
                }
            }

            if (assetRelations != null)
            {
                var assetIds = panelList.Select(p => p.PanelId).ToList();
                assetIds.AddRange(circuitList.Select(c => c.CircuitId).ToList());
                var assetList = await _client.Queryable<AssetInfo>()
                    .Where(a => assetIds.Contains(a.Id))
                    .Select(a => new
                    {
                        AssetId = a.Id,
                        AssetName = a.AssetName
                    })
                    .ToArrayAsync();

                foreach (var panel in panelList)
                {
                    var panelAsset = assetList.FirstOrDefault(a => a.AssetId == panel.PanelId);
                    var children = assetRelations.Where(ar => ar.ParentId == panel.PanelId && ar.AssetLevel == AssetLevel.Circuit).ToList();
                    foreach (var child in children)
                    {
                        var circuit = circuitList.FirstOrDefault(c => c.CircuitId == child.ChildId);
                        if (circuit != null)
                        {
                            var circuitAsset = assetList.FirstOrDefault(a => a.AssetId == circuit.CircuitId);
                            circuit.Name = circuitAsset?.AssetName ?? string.Empty;
                            panel.CircuitList.Add(circuit);
                            circuitList.Remove(circuit);
                        }
                    }
                    panel.BusList = panel.BusList.OrderBy(a => a.Index).ThenBy(a => a.Number).ToList();
                    panel.Name = panelAsset?.AssetName ?? string.Empty;
                    result.PanelList.Add(panel);
                }

                foreach (var circuit in circuitList)
                {
                    var circuitAsset = assetList.FirstOrDefault(a => a.AssetId == circuit.CircuitId);
                    if (circuitAsset == null) continue;

                    var child = assetRelations.FirstOrDefault(ar => ar.ChildId == circuit.CircuitId && ar.AssetLevel == AssetLevel.Circuit);
                    if (child == null) continue;

                    var panelAsset = await _client.Queryable<AssetInfo>()
                        .Where(a => a.Id == child.ParentId)
                        .Select(a => new
                        {
                            AssetId = a.Id,
                            AssetName = a.AssetName
                        }).FirstAsync();

                    if (panelAsset == null) continue;

                    var panel = new TemperaturePanelResult
                    {
                        Name = panelAsset.AssetName,
                    };
                    circuit.Name = circuitAsset.AssetName;
                    panel.CircuitList.Add(circuit);
                    result.PanelList.Add(panel);
                }
            }

            return new ResponseBase<TemperatureCurrentlyResult>()
            {
                Code = 20000,
                Data = result,
            };
        }

        [HttpGet("{assetId}/newcurrently")]
        [SwaggerOperation(Summary = "Swagger_TempMonitoring_GetCurrently", Description = "Swagger_TempMonitoring_GetCurrently_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<CurrentlyTemperatureInfo[]>> GetNewCurrentlyStatusList(int assetId)
        {
            if (assetId <= 0)
            {
                return new ResponseBase<CurrentlyTemperatureInfo[]>()
                {
                    Code = 20000,
                    Data = new CurrentlyTemperatureInfo[0],
                };
            }
            List<TemperatureMonitorInfo> result = await _client.Queryable<TemperatureMonitorInfo>().Where(t => t.AssetId == assetId).OrderBy(t => t.Id).ToListAsync();
            if (result == null)
            {
                return new ResponseBase<CurrentlyTemperatureInfo[]>()
                {
                    Code = 20000,
                    Data = new CurrentlyTemperatureInfo[0],
                };
            }
            var temperatureStatus = _cache.GetHashAllData(string.Format(AssetCurrentStatusCacheKey, assetId));
            TemperatureMonitorInfo[] hasBinds = result.Where(r => r.BindAssetId.HasValue && r.BindAssetId.Value > 0 && !string.IsNullOrEmpty(r.DataPoint)).ToArray();
            hasBinds.ForEach(b => result.Remove(b));
            var currentlyResults = new List<CurrentlyTemperatureInfo>();

            if (result.Count > 0)
            {
                var unknownInfo = new CurrentlyTemperatureInfo();
                unknownInfo.Level = "Unknown";
                unknownInfo.UnknownList = new List<TemperatureDeviceStatus>();
                foreach (var r in result)
                {
                    var isOnline = string.Empty;
                    var tempStr = string.Empty;
                    temperatureStatus.TryGetValue($"Sensor_{r.ModbusPoint - 40001}_connect", out isOnline);
                    temperatureStatus.TryGetValue($"Sensor_{r.ModbusPoint - 40001}", out tempStr);
                    if (string.IsNullOrEmpty(tempStr))
                    {
                        if (decimal.TryParse(tempStr, out decimal number))
                        {
                            tempStr = number.ToString("F2");
                        }
                    }
                    unknownInfo.UnknownList.Add(new TemperatureDeviceStatus()
                    {
                        DeviceId = r.DeviceId ?? string.Empty,
                        Location = r.Location ?? string.Empty,
                        OnlineStatus = "1".Equals(isOnline) ? "on" : "off",
                        Temperature = tempStr ?? string.Empty,
                    });
                }

                currentlyResults.Add(unknownInfo);
            }

            if (hasBinds.Length == 0)
            {
                currentlyResults.Add(GetEmptyBusBar());
            }
            else
            {
                var busbarSubItems = new List<CurrentlyTemperatureInfo>();

                var assetIds = hasBinds.Select(b => b.BindAssetId ?? 0).ToArray();
                var assetInfoes = await _client.Queryable<AssetInfo>().Where(a => assetIds.Contains(a.Id)).ToArrayAsync();

                var substation = assetInfoes.FirstOrDefault(a => a.AssetLevel == AssetLevel.Substation);
                if (substation != null)
                {
                    currentlyResults.Add(GetSubstationInfo(temperatureStatus, hasBinds, substation));
                }
                else
                {
                    currentlyResults.Add(GetEmptyBusBar());
                }

                var assets = assetInfoes.Where(a => a.AssetLevel != AssetLevel.Substation).ToArray();
                AssetRelation[] assetRelations = await _client.Queryable<AssetRelation>().ToArrayAsync();
                if (assetRelations != null && assetRelations.Length > 0)
                {
                    var panels = assetRelations.Where(ar => ar.AssetLevel == AssetLevel.Panel).ToArray();
                    foreach (var panel in panels)
                    {
                        var circuitList = assetRelations.Where(ar => ar.AssetLevel == AssetLevel.Circuit && ar.ParentId == panel.ChildId).ToArray();
                        var deviceList = assetRelations.Where(ar => ar.AssetLevel == AssetLevel.Device && circuitList.Any(c => c.ChildId == ar.ParentId)).ToArray();

                        TemperatureMonitorInfo[] mappingCircuits = hasBinds.Where(b => circuitList.Any(c => c.ChildId == b.BindAssetId)).ToArray();
                        TemperatureMonitorInfo[] mappingDevices = hasBinds.Where(b => deviceList.Any(d => d.ChildId == b.BindAssetId)).ToArray();
                        if (mappingCircuits.Length > 0 || mappingDevices.Length > 0)
                        {
                            var circuitIds = mappingCircuits.Select(c => c.BindAssetId ?? 0).ToList();

                            if (mappingDevices.Length > 0)
                            {
                                var mappingDeviceCircuitIds = deviceList.Where(d => mappingDevices.Any(m => m.BindAssetId == d.ChildId)).Select(d => d.ParentId).ToArray();
                                circuitIds.AddRange(mappingDeviceCircuitIds);
                            }

                            var assetInfos = await _client.Queryable<AssetInfo>().Where(a => a.Id == panel.ChildId || circuitIds.Contains(a.Id)).ToArrayAsync();
                            var panelAsset = assetInfos.FirstOrDefault(a => a.AssetLevel == AssetLevel.Panel);
                            if (assetInfos.Length >= 2 && panelAsset != null)
                            {
                                var panelTempInfo = new CurrentlyTemperatureInfo()
                                {
                                    Name = panelAsset.AssetName,
                                    Level = "Panel",
                                    SubItems = new List<CurrentlyTemperatureInfo>()
                                };
                                currentlyResults.Add(panelTempInfo);

                                var circuitAssets = assetInfos.Where(a => a.AssetLevel == AssetLevel.Circuit).ToArray();
                                foreach (var c in circuitAssets)
                                {
                                    var temps = mappingCircuits.Where(m => m.BindAssetId == c.Id).ToList();

                                    var devices = deviceList.Where(d => d.ParentId == c.Id).ToList();
                                    temps.AddRange(mappingDevices.Where(m => devices.Any(d => d.ChildId == m.BindAssetId)));

                                    var circuitItem = new CurrentlyTemperatureInfo()
                                    {
                                        Level = "Circuit",
                                        Name = c.CircuitName,
                                        DataList = new List<CurrentlyTemperatureItem>()
                                    };

                                    panelTempInfo.SubItems.Add(circuitItem);

                                    foreach (var t in temps)
                                    {
                                        var m = Regex.Match(t.DataPoint ?? string.Empty, "^([A|B|C|N])PhaseTemp([\\d]+)$");
                                        if (m.Success)
                                        {
                                            var phase = m.Groups[1].Value;
                                            var index = int.Parse(m.Groups[2].Value);

                                            var data = circuitItem.DataList.FirstOrDefault(d => d.Index == index);
                                            if (data == null)
                                            {
                                                data = new CurrentlyTemperatureItem();
                                                data.Index = index;
                                                circuitItem.DataList.Add(data);
                                            }

                                            TemperatureDeviceStatus tempInfo = GetTemperatureDeviceStatus(temperatureStatus, t);
                                            SetValueByPhase(phase, data, tempInfo);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return new ResponseBase<CurrentlyTemperatureInfo[]>
            {
                Code = 20000,
                Data = currentlyResults.ToArray()
            };
        }
        [HttpGet("{assetId}/GetAlarm")]
        [SwaggerOperation(Summary = "Swagger_TempMonitoring_GetAlarm", Description = "Swagger_TempMonitoring_GetAlarm_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<SearchBase<JObject>> GetAlarm(int assetId, int page, int count, [AllowNull] bool needCount = false)
        {
            var ip = await _client.Queryable<AssetInfo>().Where(a => a.Id == assetId).Select(a => a.IPAddress).FirstAsync();
            if (string.IsNullOrEmpty(ip))
            {
                return new SearchBase<JObject>
                {
                    Code = 20000,
                    TotalCount = 0,
                    Page = page,
                    Items = new List<JObject>()
                };
            }

            var connectStatus = _cache.Get<Dictionary<int, bool>>("AssetConnectStatus:All");

            if (connectStatus == null)
            {
                return new SearchBase<JObject>
                {
                    Code = 20000,
                    TotalCount = 0,
                    Page = page,
                    Items = new List<JObject>()
                };
            }

            if (!connectStatus.TryGetValue(assetId, out var connected) || !connected)
            {
                return new SearchBase<JObject>
                {
                    Code = 20000,
                    TotalCount = 0,
                    Page = page,
                    Items = new List<JObject>()
                };
            }
            try
            {
                var httpClient = new HttpClient();
                httpClient.BaseAddress = new Uri($"http://{ip}:3000");
                var token = await GetToken(httpClient);
                if (string.IsNullOrEmpty(token))
                {
                    return new SearchBase<JObject>
                    {
                        Code = 20000,
                        TotalCount = 0,
                        Page = page,
                        Items = new List<JObject>()
                    };
                }

                httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {token}");
                var list = await GetAlarmList(httpClient, page, count);

                var result = new SearchBase<JObject>
                {
                    Code = 20000,
                    TotalCount = 0,
                    Page = page,
                    Items = list
                };

                if (needCount)
                {
                    result.TotalCount = await GetAlarmNumber(httpClient);
                }
                return result;
            }
            catch (Exception ex)
            {
                _log.LogError(ex, $"{assetId}:GetAlarm失败");
                return new SearchBase<JObject>
                {
                    Code = 20000,
                    TotalCount = 0,
                    Page = page,
                    Items = new List<JObject>()
                };
            }

        }

        private CurrentlyTemperatureInfo GetSubstationInfo(Dictionary<string, string> temperatureStatus, TemperatureMonitorInfo[] hasBinds, AssetInfo substation)
        {
            var tempList = hasBinds.Where(b => b.BindAssetId == substation.Id).ToArray();
            var dataPoints = tempList.Select(a => a.DataPoint ?? string.Empty).OrderBy(a => a).ToArray();

            var substationInfo = new CurrentlyTemperatureInfo();
            substationInfo.Level = "Substation";
            substationInfo.SubItems = new List<CurrentlyTemperatureInfo>();
            var busbarInfo = new CurrentlyTemperatureInfo();
            foreach (var point in dataPoints)
            {
                var match = Regex.Match(point, "^BusbarTemp([\\d]+)([A|B|C|N])_([\\d]+)$");
                if (!match.Success) continue;
                var index = int.Parse(match.Groups[3].Value);
                var busbarId = $"Asset_Busbar{match.Groups[1].Value}";
                var phase = match.Groups[2].Value;
                busbarInfo = substationInfo.SubItems.FirstOrDefault(s => s.Code == busbarId);
                if (busbarInfo == null)
                {
                    busbarInfo = new CurrentlyTemperatureInfo();
                    busbarInfo.Level = "BusBar";
                    busbarInfo.Code = busbarId;
                    busbarInfo.Name = MessageContext.GetString(busbarId) ?? busbarId;
                    busbarInfo.DataList = new List<CurrentlyTemperatureItem>();
                    substationInfo.SubItems.Add(busbarInfo);
                }

                if (busbarInfo.DataList == null) continue;

                var item = busbarInfo.DataList.FirstOrDefault(d => d.Index == index);
                if (item == null)
                {
                    item = new CurrentlyTemperatureItem();
                    item.Index = index;
                    busbarInfo.DataList.Add(item);
                }

                var temp = tempList.FirstOrDefault(t => t.DataPoint == point);

                if (temp == null) continue;

                TemperatureDeviceStatus tempInfo = GetTemperatureDeviceStatus(temperatureStatus, temp);

                SetValueByPhase(phase, item, tempInfo);
            }

            return substationInfo;
        }

        private static TemperatureDeviceStatus GetTemperatureDeviceStatus(Dictionary<string, string> temperatureStatus, TemperatureMonitorInfo temp)
        {
            var tempInfo = new TemperatureDeviceStatus();
            tempInfo.MeasuringTime = DateTime.Now;
            tempInfo.DeviceId = temp.DeviceId ?? string.Empty;
            tempInfo.Location = temp.Location ?? string.Empty;
            temperatureStatus.TryGetValue($"Sensor_{temp.ModbusPoint - 40001}_connect", out var connectStatus);
            tempInfo.OnlineStatus = connectStatus == "1" ? "1" : "0";
            tempInfo.Temperature = "-";
            if (temperatureStatus.TryGetValue($"Sensor_{temp.ModbusPoint - 40001}", out var valueStr) && decimal.TryParse(valueStr, out var decimalValue))
            {
                tempInfo.Temperature = decimalValue.ToString();
            }

            return tempInfo;
        }

        private void SetValueByPhase(string phase, CurrentlyTemperatureItem item, TemperatureDeviceStatus statusValue)
        {
            switch (phase)
            {
                case "A":
                    item.APhase = statusValue;
                    break;
                case "B":
                    item.BPhase = statusValue;
                    break;
                case "C":
                    item.CPhase = statusValue;
                    break;
                case "N":
                    item.NPhase = statusValue;
                    break;
                default: break;
            }
        }

        private CurrentlyTemperatureInfo GetEmptyBusBar()
        {
            return new CurrentlyTemperatureInfo()
            {
                Level = "Substation",
                SubItems = new List<CurrentlyTemperatureInfo>()
                    {
                        new CurrentlyTemperatureInfo()
                        {
                            Name = MessageContext.GetString($"Asset_Busbar1"),
                            Level = "BusBar",
                            DataList = new List<CurrentlyTemperatureItem>()
                        },
                        new CurrentlyTemperatureInfo()
                        {
                            Name = MessageContext.GetString($"Asset_Busbar2"),
                            Level = "BusBar",
                            DataList = new List<CurrentlyTemperatureItem>()
                        },
                    }
            };
        }
        [HttpGet("{assetId}/ThirdMonitorDetails")]
        [SwaggerOperation(Summary = "Swagger_TempMonitoring_GetThirdMonitorDetails", Description = "Swagger_TempMonitoring_GetThirdMonitorDetails_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<TemperatureMonitorDetails>> GetThirdTemperatureMonitorDetails(int assetId)
        {
            //还没更新

            var currentlyStatus = _cache.GetHashAllData(string.Format(AssetCurrentStatusCacheKey, assetId));

            var modbusIds = new List<int>();
            var maxValue = new TemperatureDeviceStatus();
            var offlineTimes = new Dictionary<int, string>();
            foreach (var status in currentlyStatus)
            {
                var match = Regex.Match(status.Key, "^Sensor_([\\d]+)_connect$");
                if (match.Success)
                {
                    if ("1".Equals(status.Value))
                    {
                        modbusIds.Add(int.Parse(match.Groups[1].Value) + 40001);
                    }
                    continue;
                }
                match = Regex.Match(status.Key, "^Sensor_([\\d]+)$");
                if (match.Success)
                {
                    if (decimal.TryParse(status.Value, out var decimalValue))
                    {
                        var keyId = int.Parse(match.Groups[1].Value) + 40001;
                        if (decimalValue > maxValue.TemperatureValue)
                        {
                            maxValue.ModbusId = keyId;
                            maxValue.Temperature = status.Value;
                        }
                    }
                    continue;
                }
                match = Regex.Match(status.Key, "^Sensor_([\\d]+)_offline_time$");
                if (match.Success)
                {
                    offlineTimes.Add(int.Parse(match.Groups[1].Value) + 40001, status.Value);
                    continue;
                }
            }

            var offlineDevices = new List<TemperatureDeviceStatus>();
            var onlineDeivceIds = new List<string>();
            var monitorList = await _client.Queryable<TemperatureMonitorInfo>().Where(a => a.AssetId == assetId).ToArrayAsync();

            if (modbusIds.Count > 0)
            {
                foreach (var info in monitorList)
                {
                    if (!info.ModbusPoint.HasValue) continue;
                    if (maxValue.ModbusId == info.ModbusPoint.Value)
                    {
                        maxValue.DeviceId = info.DeviceId ?? string.Empty;
                        if (info.BindAssetId.HasValue)
                        {
                            maxValue.AssociatedAsset = await GetAssociatedAsset(info.BindAssetId.Value, info.DataPoint ?? string.Empty);
                        }
                    }

                    if (!modbusIds.Contains(info.ModbusPoint.Value))
                    {
                        offlineDevices.Add(new TemperatureDeviceStatus()
                        {
                            DeviceId = info.DeviceId ?? string.Empty,
                            Location = info.Location ?? string.Empty,
                            OnlineStatus = "off",
                            MeasuringTime = DateTime.Now,
                        });
                    }
                    else
                    {
                        var offlineTime = DateTime.Now;
                        if (offlineTimes.TryGetValue(info.ModbusPoint.Value, out var timeStr))
                        {
                            offlineTime = DateTime.ParseExact(timeStr, "yyyymmddHHMMss", null);
                            offlineDevices.Add(new TemperatureDeviceStatus()
                            {
                                DeviceId = info.DeviceId ?? string.Empty,
                                Location = info.Location ?? string.Empty,
                                OnlineStatus = "off",
                                MeasuringTime = offlineTime,
                            });
                        }
                        else
                        {
                            onlineDeivceIds.Add(info.DeviceId ?? string.Empty);
                        }
                    }
                }
            }

            var alarms = await _client.Queryable<AlarmLog>().Where(al => al.AssetId == assetId && al.RuleId > 0).ToArrayAsync();
            var alarmCount = alarms.GroupBy(al => new { al.RuleId, al.Message }).ToList().Count;
            return new ResponseBase<TemperatureMonitorDetails>()
            {
                Code = 20000,
                Data = new TemperatureMonitorDetails()
                {
                    OnlineCount = onlineDeivceIds.Count,
                    OfflineCount = offlineDevices.Count,
                    AlarmCount = alarms.Length,
                    AlarmDeviceCount = alarmCount,
                    MaxTemperatureDevice = maxValue,
                    OfflineDevices = offlineDevices,
                    AlarmDevices = new List<TemperatureDeviceStatus>()
                }
            };
        }

        [HttpGet("{assetId}/MonitorDetails")]
        [SwaggerOperation(Summary = "Swagger_TempMonitoring_GetMonitorDetails", Description = "Swagger_TempMonitoring_GetMonitorDetails_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<TemperatureMonitorDetails>> GetTemperatureMonitorDetails(int assetId)
        {
            var currentlyStatus = _cache.GetHashAllData(string.Format(AssetCurrentStatusCacheKey, assetId));

            var modbusIds = new List<int>();
            var maxValue = new TemperatureDeviceStatus();
            var offlineTimes = new Dictionary<int, string>();
            foreach (var status in currentlyStatus)
            {
                var match = Regex.Match(status.Key, "^Sensor_([\\d]+)_connect$");
                if (match.Success)
                {
                    if ("1".Equals(status.Value))
                    {
                        modbusIds.Add(int.Parse(match.Groups[1].Value) + 40001);
                    }
                    continue;
                }
                match = Regex.Match(status.Key, "^Sensor_([\\d]+)$");
                if (match.Success)
                {
                    if (decimal.TryParse(status.Value, out var decimalValue))
                    {
                        var keyId = int.Parse(match.Groups[1].Value) + 40001;
                        if (decimalValue > maxValue.TemperatureValue)
                        {
                            maxValue.ModbusId = keyId;
                            maxValue.Temperature = status.Value;
                        }
                    }
                    continue;
                }
                match = Regex.Match(status.Key, "^Sensor_([\\d]+)_offline_time$");
                if (match.Success)
                {
                    offlineTimes.Add(int.Parse(match.Groups[1].Value) + 40001, status.Value);
                    continue;
                }
            }

            var offlineDevices = new List<TemperatureDeviceStatus>();
            var onlineDeivceIds = new List<string>();
            var monitorList = await _client.Queryable<TemperatureMonitorInfo>().Where(a => a.AssetId == assetId).ToArrayAsync();

            if (modbusIds.Count > 0)
            {
                foreach (var info in monitorList)
                {
                    if (!info.ModbusPoint.HasValue) continue;
                    if (maxValue.ModbusId == info.ModbusPoint.Value)
                    {
                        maxValue.DeviceId = info.DeviceId ?? string.Empty;
                        if (info.BindAssetId.HasValue)
                        {
                            maxValue.AssociatedAsset = await GetAssociatedAsset(info.BindAssetId.Value, info.DataPoint ?? string.Empty);
                        }
                    }

                    if (!modbusIds.Contains(info.ModbusPoint.Value))
                    {
                        offlineDevices.Add(new TemperatureDeviceStatus()
                        {
                            DeviceId = info.DeviceId ?? string.Empty,
                            Location = info.Location ?? string.Empty,
                            OnlineStatus = "off",
                            MeasuringTime = DateTime.Now,
                        });
                    }
                    else
                    {
                        var offlineTime = DateTime.Now;
                        if (offlineTimes.TryGetValue(info.ModbusPoint.Value, out var timeStr))
                        {
                            offlineTime = DateTime.ParseExact(timeStr, "yyyymmddHHMMss", null);
                            offlineDevices.Add(new TemperatureDeviceStatus()
                            {
                                DeviceId = info.DeviceId ?? string.Empty,
                                Location = info.Location ?? string.Empty,
                                OnlineStatus = "off",
                                MeasuringTime = offlineTime,
                            });
                        }
                        else
                        {
                            onlineDeivceIds.Add(info.DeviceId ?? string.Empty);
                        }
                    }
                }
            }

            var alarms = await _client.Queryable<AlarmLog>().Where(al => al.AssetId == assetId).ToArrayAsync();
            return new ResponseBase<TemperatureMonitorDetails>()
            {
                Code = 20000,
                Data = new TemperatureMonitorDetails()
                {
                    OnlineCount = onlineDeivceIds.Count,
                    OfflineCount = offlineDevices.Count,
                    AlarmCount = alarms.Length,
                    AlarmDeviceCount = alarms.Length > 0 ? 1 : 0,
                    MaxTemperatureDevice = maxValue,
                    OfflineDevices = offlineDevices,
                    AlarmDevices = new List<TemperatureDeviceStatus>()
                }
            };
        }

        private async Task<string> GetAssociatedAsset(int assetId, string dataPoint)
        {
            if (string.IsNullOrEmpty(dataPoint)) return string.Empty;
            var assetName = await _client.Queryable<AssetInfo>().Where(a => a.Id == assetId).Select(a => a.AssetName).FirstAsync();
            if (string.IsNullOrEmpty(assetName)) return string.Empty;

            return $"{assetName}-{MessageContext.GetDataPointName(dataPoint)}";
        }

        #region 测温接口
        private async Task<string> GetToken(HttpClient client)
        {
            var result = await client.PostAsJsonAsync("/api/v1/account/login", new
            {
                name = "0",
                password = "0"
            });
            if (!result.IsSuccessStatusCode) return string.Empty;
            var str = await result.Content.ReadAsStringAsync();
            var obj = JObject.Parse(str);

            if (obj.TryGetValue("data", out var dataToken) && dataToken is JObject dataObj)
            {
                if (dataObj.TryGetValue("token", out var tokenToken) && tokenToken.Type == JTokenType.String)
                {
                    return tokenToken.ToString();
                }
            }

            return string.Empty;
        }

        private async Task<List<JObject>> GetAlarmList(HttpClient client, int page, int count)
        {
            var resultStr = await client.GetStringAsync($"/api/v1/alert?page={page}&sizes={count}");

            var obj = JObject.Parse(resultStr);
            if (obj.TryGetValue("data", out var alarmListToken) && alarmListToken is JArray alarmList)
            {
                var list = new List<JObject>();
                foreach (var item in alarmList)
                {
                    if (item is JObject alarmObj)
                    {
                        var time = alarmObj.Value<DateTime>("ts");
                        time = time.AddHours(8);
                        alarmObj["ts"] = time;
                        list.Add(alarmObj);
                    }
                }

                return list;
            }

            return new List<JObject>();
        }

        private async Task<int> GetAlarmNumber(HttpClient client)
        {
            var resultStr = await client.GetStringAsync($"/api/v1/alert/number");

            var obj = JObject.Parse(resultStr);
            if (obj.TryGetValue("data", out var countToken) && countToken is JObject countObj)
            {
                return countObj.Value<int>("count");
            }

            return 0;
        }
        #endregion
    }
}
