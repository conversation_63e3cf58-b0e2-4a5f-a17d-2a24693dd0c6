{"info": {"_postman_id": "b681ba79-8b82-4a4c-9052-13199a5a5639", "name": "W01使用超级管理员账号进入panel manager运维工单页面，查看工单号、设备、工单状态、处理措施字段及下拉框数据", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 3", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "获取设备详情 Copy 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"包含设备信息\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"id\");\r", "    pm.expect(pm.response.text()).to.include(\"assetName\");\r", "    pm.expect(pm.response.text()).to.include(\"level\");\r", "    pm.expect(pm.response.text()).to.include(\"assetType\");\r", "    pm.expect(pm.response.text()).to.include(\"assetModel\");\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Asset/search?levels=Device", "host": ["{{baseUrl}}"], "path": ["api", "v1", "<PERSON><PERSON>", "search"], "query": [{"key": "levels", "value": "<PERSON><PERSON>"}]}}, "response": []}, {"name": "获取工单状态详情 Copy 3", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "pm.test(\"包含工单状态信息\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"ALL\");\r", "    pm.expect(pm.response.text()).to.include(\"待处理\");\r", "    pm.expect(pm.response.text()).to.include(\"处理中\");\r", "    pm.expect(pm.response.text()).to.include(\"已完成\");\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/SystemDict/work_order_status?appendAll=true", "host": ["{{baseUrl}}"], "path": ["api", "v1", "SystemDict", "work_order_status"], "query": [{"key": "appendAll", "value": "true"}]}}, "response": []}, {"name": "获取处理措施详情 Copy 4", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "pm.test(\"包含处理措施信息\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"ALL\");\r", "    pm.expect(pm.response.text()).to.include(\"检查\");\r", "    pm.expect(pm.response.text()).to.include(\"维护\");\r", "    pm.expect(pm.response.text()).to.include(\"维修\");\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/SystemDict/work_order_measure?appendAll=true", "host": ["{{baseUrl}}"], "path": ["api", "v1", "SystemDict", "work_order_measure"], "query": [{"key": "appendAll", "value": "true"}]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}]}