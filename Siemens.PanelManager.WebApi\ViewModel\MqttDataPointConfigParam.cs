﻿using Siemens.PanelManager.Model.Database.Asset;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    /// <summary>
    /// 目录重命名
    /// </summary>
    public class MqttDataPointConfigParam
    {
        /// <summary>
        /// 主键id
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 分组名称
        /// </summary>
        public string? GroupName { get; set; }
    }

    /// <summary>
    /// 配置点位中间结构体
    /// </summary>
    public class MqttPointConfigDto
    {
       
        /// <summary>
        /// assetId
        /// </summary>
        public int AssetId { get; set; }

        /// <summary>
        /// 分组类型(1:分组,2:点位)
        /// </summary>
        public GroupConfigType ConfigType { get; set; }

        /// <summary>
        /// 分组名称
        /// </summary>
        public string? GroupName { get; set; }

        /// <summary>
        /// 定位Code
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 定位或者分组名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 点位类型
        /// </summary>
        public string? AssetModel { get; set; }

        /// <summary>
        /// 采样周期
        /// </summary>
        public int SamplingPeriod { get; set; }

        /// <summary>
        ///  文件夹名称
        /// </summary>
        public string? Directory { get; set; }

        /// <summary>
        /// 点位集合
        /// </summary>
        public List<MqttPointConfigDto>? Children { get; set; }

    }

    /// <summary>
    /// 配置点位返回值结构体
    /// </summary>
    public class MqttPointConfigParam
    {
        /// <summary>
        /// assetId
        /// </summary>
        public int AssetId { get; set; }

        /// <summary>
        ///  配置点位中间结构体集合
        /// </summary>
        public List<MqttPointConfigDto>?  DataList { get; set; }

    }

    /// <summary>
    /// 新增分组
    /// </summary>
    public class MqttGroupPointConfigParam
    {

        /// <summary>
        /// assetId
        /// </summary>
        public int AssetId { get; set; }

        /// <summary>
        /// 分组类型(1:分组,2:点位)
        /// </summary>
        public GroupConfigType ConfigType { get; set; }

        /// <summary>
        /// 分组名称
        /// </summary>
        public string? GroupName { get; set; }

        /// <summary>
        /// 定位Code
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 定位或者分组名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 点位类型
        /// </summary>
        public string? AssetModel { get; set; }

        /// <summary>
        /// 采样周期
        /// </summary>
        public int SamplingPeriod { get; set; }

        /// <summary>
        ///  文件夹名称
        /// </summary>
        public string? Directory { get; set; }
    }

}
