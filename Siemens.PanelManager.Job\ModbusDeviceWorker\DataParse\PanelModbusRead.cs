﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.ModbusDeviceWorker.DataParse
{
    public abstract class PanelModbusRead
    {
        public bool IsBigEndian { get; set; }
        public string? ParseMode { get; set; }
        public float Factor { get; set; }
        public float CustomFactor { get; set; }
        public float Intercept { get; set; }

        public PanelModbusRead(bool isBigEndian, string? parseMode, float factor, float customFactor, float intercept)
        {
            IsBigEndian = isBigEndian;
            ParseMode = parseMode;
            Factor = factor;
            CustomFactor = customFactor;
            Intercept = intercept;
        }

        // (Factor * value + Intercept) * CustomFactor
        protected string Calculate(short value)
        {
            return ((Factor * value + Intercept) * CustomFactor).ToString();
        }

        protected string Calculate(ushort value)
        {
            return ((Factor * value + Intercept) * CustomFactor).ToString();
        }

        protected string Calculate(int value)
        {
            return ((Factor * value + Intercept) * CustomFactor).ToString();
        }

        protected string Calculate(uint value)
        {
            return ((Factor * value + Intercept) * CustomFactor).ToString();
        }

        protected string Calculate(long value)
        {
            return ((Factor * value + Intercept) * CustomFactor).ToString();
        }

        protected string Calculate(ulong value)
        {
            return ((Factor * value + Intercept) * CustomFactor).ToString();
        }

        protected string Calculate(float value)
        {
            return ((Factor * value + Intercept) * CustomFactor).ToString();
        }

        protected string Calculate(double value)
        {
            return ((Factor * value + Intercept) * CustomFactor).ToString();
        }

        protected string Calculate(decimal value)
        {
            return (((decimal)Factor * value + (decimal)Intercept) * (decimal)CustomFactor).ToString();
        }

        public abstract string ReadData(ReadOnlySpan<byte> source);
    }
}
