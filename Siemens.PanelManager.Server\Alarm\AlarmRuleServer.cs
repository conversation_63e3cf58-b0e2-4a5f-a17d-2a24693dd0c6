﻿using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.DataFlow;
using Siemens.PanelManager.Model.Emun;
using Siemens.PanelManager.Server.DataPoint;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection.Metadata;
using System.Text;
using System.Threading.Tasks;
using TouchSocket.Core;

namespace Siemens.PanelManager.Server.Alarm
{
    public class AlarmRuleServer
    {
        private SiemensCache _cache;
        private IServiceProvider _provider;
        public AlarmRuleServer(SiemensCache cache, IServiceProvider provider)
        {
            _cache = cache;
            _provider = provider;
        }

        public async Task CheckAlarm(AlarmRule rule)
        {
            if (string.IsNullOrEmpty(rule.TargetValue) || !rule.IsEnable || rule.IsDelete) return;

            switch (rule.TargetType)
            {
                case AlarmTargetType.Panel:
                case AlarmTargetType.Device:
                case AlarmTargetType.Circuit:
                case AlarmTargetType.Transformer:
                case AlarmTargetType.Substation:
                    {
                        if (int.TryParse(rule.TargetValue, out var assetId))
                        {
                            var alarmLog = await CheckAlarmLog(rule, assetId);

                            if (alarmLog != null)
                            {
                                var refObj = _provider.GetRequiredService<IAlarmRef>();
                                refObj.AppendAlarmLog(alarmLog);
                            }
                        }
                        break;
                    }
                case AlarmTargetType.DeviceModel:
                case AlarmTargetType.CircuitModel:
                case AlarmTargetType.PanelModel:
                    {
                        var types = rule.TargetValue.Split('|');
                        if (types.Length == 2)
                        {
                            var assetType = types[0];
                            var assetModel = types[1];

                            var sqlClient = _provider.GetRequiredService<SqlSugarScope>();

                            var assetIds = await sqlClient.Queryable<AssetInfo>()
                                .Where(a => a.AssetType == assetType && a.AssetModel == assetModel)
                                .Select(a => a.Id)
                                .ToArrayAsync();

                            var alarmLogs = new List<AlarmLog>();
                            foreach (var assetId in assetIds)
                            {
                                var alarmLog = await CheckAlarmLog(rule, assetId);

                                if (alarmLog != null)
                                {
                                    alarmLogs.Add(alarmLog);
                                }
                            }

                            if (alarmLogs.Count > 0)
                            {
                                var refObj = _provider.GetRequiredService<IAlarmRef>();
                                refObj.AppendAlarmLog(alarmLogs.ToArray());
                            }
                        }
                        break;
                    }
                default:
                    break;
            }
        }

        private async Task<AlarmLog?> CheckAlarmLog(AlarmRule rule, int assetId)
        {
            if (!rule.IsEnable) return null;

            var exists = _cache.Get<bool>($"AlarmRoleCheck-{rule.Id}|{assetId}");
            if (exists) return null;

            var cacheKey = string.Format("AssetStatus:Currently-{0}", assetId);
            var currentStatus = _cache.GetHashAllData(cacheKey);
            var simgleInfo = _cache.Get<AssetSimpleInfo>(string.Format("Asset:SimpleInfo-{0}", assetId));
            if (simgleInfo != null && CheckData(rule, currentStatus))
            {
                var alarmLog = await SaveAlarmLog(rule, currentStatus, simgleInfo);
                return alarmLog;
            }
            return null;
        }

        private async Task<AlarmLog> SaveAlarmLog(AlarmRule rule, Dictionary<string, string> currentStatus, AssetSimpleInfo simpleInfo)
        {
            var alarmLog = new AlarmLog();

            var dataPointServer = _provider.GetRequiredService<DataPointServer>();
            var dataPointList = await dataPointServer.GetDataPointInfos(simpleInfo.AssetLevel, simpleInfo.AssetType, simpleInfo.AssetModel, simpleInfo.AssetId);
            var newStatusInfo = new Dictionary<string, string>();
            var isGeneralEquipment = false;
            if (simpleInfo.AssetLevel == AssetLevel.Device)
            {
                isGeneralEquipment = IsGeneralEquipment(simpleInfo.AssetModel, simpleInfo.AssetType);
            }

            alarmLog.AssetStatusStr = AlarmInternalFunc.GetAssetStatusStr(currentStatus, dataPointList, isGeneralEquipment);

            string? deviceName = null;

            if (simpleInfo.AssetLevel == AssetLevel.Device)
            {
                deviceName = simpleInfo.AssetName;
            }

            var factory = _provider.GetRequiredService<IMessageContextFactory>();
            var messageContext = factory.GetMessageContext(factory.GetDefaultLanguage());

            var ruleInfo = GetRuleInfo(rule, dataPointList, messageContext);

            alarmLog.AssetId = simpleInfo.AssetId;
            alarmLog.RuleId = rule.Id;
            alarmLog.Status = AlarmLogStatus.New;
            alarmLog.CreatedBy = "System";
            alarmLog.CreatedTime = DateTime.Now;
            alarmLog.UpdatedBy = "System";
            alarmLog.UpdatedTime = DateTime.Now;
            alarmLog.EventType = AlarmEventType.Alarm;
            alarmLog.Severity = rule.Severity;
            alarmLog.Message = ruleInfo;
            alarmLog.SubstationName = simpleInfo.SubstationSimpleInfo?.AssetName;
            alarmLog.PanelName = simpleInfo.PanelSimpleInfo?.AssetName;
            alarmLog.CircuitName = simpleInfo.CircuitSimpleInfo?.AssetName;
            alarmLog.DeviceName = deviceName;

            _cache.Set<bool>($"AlarmRoleCheck-{rule.Id}|{simpleInfo.AssetId}", true);

            return alarmLog;
        }

        /// <summary>
        /// 是否是通用设备
        /// </summary>
        /// <param name="assetModel"></param>
        /// <param name="assetType"></param>
        /// <returns></returns>
        public bool IsGeneralEquipment(string assetModel, string assetType)
        {
            return "Other".Equals(assetModel, StringComparison.OrdinalIgnoreCase)
                || "GeneralDevice".Equals(assetType, StringComparison.OrdinalIgnoreCase)
                || ("Modbus".Equals(assetModel, StringComparison.OrdinalIgnoreCase)
                && "Gateway".Equals(assetType, StringComparison.OrdinalIgnoreCase));
        }

        public string GetRuleInfo(AlarmRule rule, List<AssetDataPointInfo> dataPointInfos, IMessageContext messageContext)
        {
            var stringBuilder = new StringBuilder();
            foreach (var section in rule.Sections)
            {
                var dataPoint = section.Point;

                var pointInfo = dataPointInfos.FirstOrDefault(d => d.Code == section.Point);
                if (pointInfo != null)
                {
                    var name = pointInfo.Name;
                    if (name.StartsWith("||"))
                    {
                        name = name.Substring(2);
                        dataPoint = name;
                    }
                    else
                    {
                        dataPoint = messageContext.GetString($"DataPoint_{pointInfo.Code}");
                    }
                }

                stringBuilder.Append($"{dataPoint} {section.Compare.ToCompareString()} {section.DataValue} {section.LogicalOperator.ToLogicalOperatorString()} ");
            }

            return stringBuilder.ToString();
        }

        public bool CheckData(AlarmRule rule, Dictionary<string, string> changeData)
        {
            var sections = rule.Sections;
            var result = true;

            LogicalOperator lastOperator = LogicalOperator.None;

            foreach (var section in sections)
            {
                if (lastOperator == LogicalOperator.Or)
                {
                    if (result)
                    {
                        break;
                    }
                    result = true;
                }

                lastOperator = section.LogicalOperator;

                if (changeData.ContainsKey(section.Point))
                {
                    var data = changeData[section.Point];
                    decimal.TryParse(data, out var decimalValue);
                    decimal.TryParse(section.DataValue, out var threshold);

                    switch (section.Compare)
                    {
                        case Compare.Equal:
                            {
                                result = result && section.DataValue.Equals(data);
                                break;
                            }
                        case Compare.NotEqual:
                            {
                                result = result && !section.DataValue.Equals(data);
                                break;
                            }
                        case Compare.GreaterThan:
                            {
                                result = result && threshold < decimalValue;
                                break;
                            }
                        case Compare.GreaterThanOrEqual:
                            {
                                result = result && threshold <= decimalValue;
                                break;
                            }
                        case Compare.LessThan:
                            {
                                result = result && threshold > decimalValue;
                                break;
                            }
                        case Compare.LessThanOrEqual:
                            {
                                result = result && threshold >= decimalValue;
                                break;
                            }
                        default: break;
                    }
                }
                else
                {
                    result = false;
                }
            }
            return result;
        }

        public string GetAssetStatusStr(Dictionary<string, string> values, List<AssetDataPointInfo> pointInfos, bool isGeneralEquipment)
        {
            return AlarmInternalFunc.GetAssetStatusStr(values, pointInfos, isGeneralEquipment);
        }
    }
}
