﻿using Siemens.PanelManager.Model.Chart;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class EnergyHarmonicChartResult : IChart
    {
        public LineChartModel? LineChartModel { get; set; }

        public decimal? APhaseVfund { get; set; }
        public decimal? BPhaseVfund { get; set; }
        public decimal? CPhaseVfund { get; set; }
        public bool CanShow { get; set; } = false;
        public int FrequencyCount { get; set; }
    }
}
