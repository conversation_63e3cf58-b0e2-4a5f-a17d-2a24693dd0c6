﻿namespace Siemens.PanelManager.Model.DataFlow
{
    public class AssetInputData
    {
        public AssetInputData() 
        {
        }

        public AssetInputData(AssetInputData inputData)
        {
            ParentId = inputData.ParentId;
            ObjectId = inputData.ObjectId;
            AssetId = inputData.AssetId;
            AssetName = inputData.AssetName;
            InputTime = inputData.InputTime;
            GroupName = inputData.GroupName;
            SamplingPeriods = inputData.SamplingPeriods;
            DataSources = inputData.DataSources;
            Datas = new Dictionary<string, string>(inputData.Datas);
        }

        public int? ParentId { get; set; }
        public string? ObjectId { get; set; }
        public int? AssetId { get; set; }
        public string? AssetName { get; set; }
        public DateTime InputTime { get; set; } = DateTime.Now;
        public string? GroupName { get; set; }
        public int SamplingPeriods { get; set; }
        public string DataSources { get; set; } = string.Empty;
        public Dictionary<string, string> Datas { get; set; } = new Dictionary<string, string>();
    }
}
