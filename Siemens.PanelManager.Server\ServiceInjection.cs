﻿using Microsoft.Extensions.DependencyInjection;
using Org.BouncyCastle.Asn1.Cms;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.DependencyInjection;
using Siemens.PanelManager.Model.UDC;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.Server.Algorithm;
using Siemens.PanelManager.Server.Asset;
using Siemens.PanelManager.Server.AssetDataPoint;
using Siemens.PanelManager.Server.BreakerProtect;
using Siemens.PanelManager.Server.Common;
using Siemens.PanelManager.Server.DataPoint;
using Siemens.PanelManager.Server.ElectricityCharge;
using Siemens.PanelManager.Server.Energy;
using Siemens.PanelManager.Server.MeterRead;
using Siemens.PanelManager.Server.Topology;
using Siemens.PanelManager.Server.Topoplogy;
using Siemens.PanelManager.Server.UDC;

namespace Siemens.PanelManager.Server
{
    public class ServiceInjection : IServiceInjection
    {
        public void AddService(IServiceCollection services)
        {
            services.AddTransient<TopologyExtendFunc>();
            services.AddTransient<AssetExtendServer>();
            services.AddTransient<TempFileManager>();
            services.AddTransient<AlarmExtendServer>();
            services.AddTransient<DataPointServer>();
            services.AddTransient<DeviceInfoService>();
            services.AddTransient<AlarmRuleServer>();
            services.AddSingleton<LossDiagnose>();
            services.AddSingleton<ElectricityChargeServer>();
            services.AddTransient<AssetDashboardServer>();
            services.AddTransient<DeviceProtectionSettingService>();
            services.AddTransient<UDCContextInit>();
            services.AddTransient<AssetInfluxHelper>();
            services.AddTransient<MLFBResolver>();
            services.AddTransient<BreakerHealthService>();
            services.AddTransient<TopoplogySplitService>();
            services.AddTransient<TopologyDraftService>();
            services.AddTransient<UDCContext>(p => 
            {
                var cache = p.GetRequiredService<SiemensCache>();
                var context = cache.Get<UDCContext>("UDCContext");
                return context;
            });

            services.AddTransient<SplxFileManager>();
            services.AddTransient<EnergyManagementServer>();
            services.AddTransient<BreakerProtectionCheckServer>();
            services.AddTransient<AssetDataPointInfoServer>();
            services.AddTransient<AlarmLogServer>();
            services.AddTransient<MeterReadServer>();
            services.AddTransient<SplxServer>();
            services.AddTransient<ThirdJsonServer>();
            services.AddTransient<AssetUpwardServer>();
            services.AddSingleton<TimerServer>();
            services.AddHostedService<TimerServer>();
            services.AddTransient<AcbInUseServer>();
        }
    }
}
