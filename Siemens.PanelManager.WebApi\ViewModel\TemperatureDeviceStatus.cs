﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class TemperatureDeviceStatus
    {
        public string DeviceId { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public string Temperature { get; set; } = string.Empty;
        public string AssociatedAsset { get; set; } = string.Empty;
        public DateTime? MeasuringTime { get; set; }
        public string OnlineStatus { get; set; } = "off";

        [JsonIgnore]
        public decimal TemperatureValue
        {
            get
            {
                if (decimal.TryParse(Temperature, out var value))
                {
                    return value;
                }
                return 0m;
            }
        }

        [JsonIgnore]
        public int ModbusId { get; set; }
    }

    public class TemperatureCurrentlyResult
    {
        public List<TemperatureDeviceStatus> UnknownList { get; set; } = new List<TemperatureDeviceStatus>();
        public List<TemperaturePanelResult> PanelList { get; set; } = new List<TemperaturePanelResult>();
    }

    public class TemperaturePanelResult
    {
        [JsonIgnore]
        public int PanelId { get; set; }
        public string Name { get; set; } = string.Empty;
        public List<TempratureCircuitResult> CircuitList { get; set; } = new List<TempratureCircuitResult>();
        public List<CurrentlyTemperatureItem> BusList { get; set; } = new List<CurrentlyTemperatureItem>();
        public List<CurrentlyTemperatureItem> LeftConnectPointList { get; set; } = new List<CurrentlyTemperatureItem>();
        public List<CurrentlyTemperatureItem> RightConnectPointList { get; set; } = new List<CurrentlyTemperatureItem>();
    }

    public class TempratureCircuitResult
    {
        [JsonIgnore]
        public int CircuitId { get; set; }
        public string Name { get; set; } = string.Empty;
        public CurrentlyTemperatureItem? Income { get; set; }
        public CurrentlyTemperatureItem? Outcome { get; set; }
    }
}
