﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database
{
    public abstract class LogicDataBase: IPanelDataTable
    {
        [SqlSugar.SugarColumn(ColumnName = "created_by", Length = 256, IsNullable = false)]
        public string CreatedBy { get; set; } = string.Empty;
        [SqlSugar.SugarColumn(ColumnName = "created_time", IsNullable = false)]
        public DateTime CreatedTime { get; set; }
        [SqlSugar.SugarColumn(ColumnName = "updated_by", IsNullable = false, Length = 256)]
        public string UpdatedBy { get; set; } = string.Empty;
        [SqlSugar.SugarColumn(ColumnName = "updated_time", IsNullable = false)]
        public DateTime UpdatedTime { get; set; }
    }

    public interface IPanelDataTable
    {
        
    }
}
