﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.Model.UDC
{
    public abstract class UdcListResulBase<T> : IUdcData
        where T : IUdcData
    {
        [JsonProperty(PropertyName = "count")]
        public int? Count { get; set; }
        [JsonProperty(PropertyName = "total")]
        public int? Total { get; set; }
        [JsonProperty(PropertyName = "_embedded")]
        public Embedded<T> Embedded { get; set; } = new Embedded<T>();
    }

    public class Embedded<T>
    {
        [JsonProperty(PropertyName = "item")]
        public List<T> Items { get; set; } = new List<T>();
    }
}
