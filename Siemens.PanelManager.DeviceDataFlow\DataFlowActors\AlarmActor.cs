﻿using Akka.Actor;
using Akka.DependencyInjection;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NPOI.HSSF.Record;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.DeviceDataFlow.StaticData;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Interface.Extend;
using Siemens.PanelManager.Model.Alarm;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.DataFlow;
using Siemens.PanelManager.Model.Emun;
using Siemens.PanelManager.Server.Alarm;
using SqlSugar;
using System.Data;
using System.Diagnostics;
using System.Linq.Expressions;

namespace Siemens.PanelManager.DeviceDataFlow.DataFlowActors
{
    internal class AlarmActor : ReceiveActor
    {
        private readonly ILogger<AlarmActor> _logger;
        private readonly IServiceProvider _provider;

        private IActorRef _saveActor => ActorManager.GetActorManager().AlarmSaveRef;
        private readonly SiemensCache _cache;

        private Func<AssetChangeData, AlarmRule[]> _logicFunc = (input) => new AlarmRule[0];
        private DateTime _lastAlarmTime = DateTime.MinValue;
        private AlarmRuleServer _server;

        public AlarmActor(ILogger<AlarmActor> logger, IServiceProvider provider, SiemensCache cache)
        {
            _logger = logger;
            _provider = provider;
            _server = _provider.GetRequiredService<AlarmRuleServer>();
            _cache = cache;

            ReceiveAsync<AssetChangeData>(async (p) =>
            {
                var logger = AkkaRuntimeLoggerManager.GetLogger("AlarmActor");
                if (logger != null)
                {
                    var k = logger.Start();
                    var newChangeData = GetRealChangeData(p);
                    if (newChangeData != null)
                    {
                        await CheckChangeData(newChangeData);
                    }
                    logger.Stop(k);
                    return;
                }
                else
                {
                    var newChangeData = GetRealChangeData(p);
                    if (newChangeData != null)
                    {
                        await CheckChangeData(newChangeData);
                    }
                }
            });
            ReceiveAsync<AlarmLogAction>(ChangeAlarmLogAction);
        }

        private AssetChangeData? GetRealChangeData(AssetChangeData p)
        {
            Dictionary<string, string> changeDatas;
            if (p.ChangeDataCode == null)
            {
                changeDatas = p.ChangeDatas;
            }
            else
            {
                changeDatas = new Dictionary<string, string>();
                foreach (var code in p.ChangeDataCode)
                {
                    if (p.ChangeDatas.TryGetValue(code, out var value))
                    {
                        changeDatas.TryAdd(code, value);
                    }
                }
            }

            if (changeDatas.Count == 0)
            {
                return null;
            }


            var newChangeData = new AssetChangeData
            {
                AssetId = p.AssetId,
                AssetLevel = p.AssetLevel,
                AssetType = p.AssetType,
                AssetModel = p.AssetModel,
                ChangeDatas = changeDatas,
                ChangeTime = p.ChangeTime,
                AssetName = p.AssetName
            };

            return newChangeData;
        }

        private async Task ChangeAlarmLogAction(AlarmLogAction arg)
        {
            var sqlClient = _provider.GetRequiredService<SqlSugarScope>();
            var alarmLog = await sqlClient.Queryable<AlarmLog>().Where(x => x.Id == arg.AlarmLogId).FirstAsync();

            if (alarmLog != null && alarmLog.AssetId.HasValue)
            {
                if (alarmLog.RuleId.HasValue)
                {
                    _cache.Clear($"AlarmRoleCheck-{alarmLog.RuleId.Value}|{alarmLog.AssetId.Value}");
                }

                #region Circuit
                if (!string.IsNullOrEmpty(alarmLog.CircuitName))
                {
                    var modelDic = _cache.GetHashData<AlarmCountModel>($"AlarmCount-{AssetLevel.Circuit}", alarmLog.CircuitName);
                    if (modelDic != null && modelDic.TryGetValue(alarmLog.CircuitName, out var model) && model !=null)
                    {
                        switch (alarmLog.Severity)
                        {
                            case AlarmSeverity.High:
                                model.HighCount -= 1;
                                if (model.HighCount < 0)
                                {
                                    model.HighCount = 0;
                                }
                                break;
                            case AlarmSeverity.Middle:
                                model.MiddleCount -= 1;
                                if (model.MiddleCount < 0)
                                {
                                    model.MiddleCount = 0;
                                }
                                break;
                            case AlarmSeverity.Low:
                                model.LowCount -= 1;
                                if (model.LowCount < 0)
                                {
                                    model.LowCount = 0;
                                }
                                break;
                            default: break;
                        }

                        switch (alarmLog.EventType)
                        {
                            case AlarmEventType.Alarm:
                                model.AlarmCount -= 1;
                                if (model.AlarmCount < 0)
                                {
                                    model.AlarmCount = 0;
                                }
                                break;
                            case AlarmEventType.UdcAlarm:
                                model.UDCAlarmCount -= 1;
                                if (model.UDCAlarmCount < 0)
                                {
                                    model.UDCAlarmCount = 0;
                                }
                                break;
                            case AlarmEventType.BreakerTrip:
                                model.BreakerTripCount -= 1;
                                if (model.BreakerTripCount < 0)
                                {
                                    model.BreakerTripCount = 0;
                                }
                                break;
                            default: break;
                        }

                        _cache.SetHashData($"AlarmCount-{AssetLevel.Circuit}", alarmLog.CircuitName, model);
                    }
                }
                #endregion

                #region Panel
                if (!string.IsNullOrEmpty(alarmLog.PanelName))
                {
                    var modelDic = _cache.GetHashData<AlarmCountModel>($"AlarmCount-{AssetLevel.Panel}", alarmLog.PanelName);
                    if (modelDic != null && modelDic.TryGetValue(alarmLog.PanelName, out var model) && model != null)
                    {
                        switch (alarmLog.Severity)
                        {
                            case AlarmSeverity.High:
                                model.HighCount -= 1;
                                if (model.HighCount < 0)
                                {
                                    model.HighCount = 0;
                                }
                                break;
                            case AlarmSeverity.Middle:
                                model.MiddleCount -= 1;
                                if (model.MiddleCount < 0)
                                {
                                    model.MiddleCount = 0;
                                }
                                break;
                            case AlarmSeverity.Low:
                                model.LowCount -= 1;
                                if (model.LowCount < 0)
                                {
                                    model.LowCount = 0;
                                }
                                break;
                            default: break;
                        }

                        switch (alarmLog.EventType)
                        {
                            case AlarmEventType.Alarm:
                                model.AlarmCount -= 1;
                                if (model.AlarmCount < 0)
                                {
                                    model.AlarmCount = 0;
                                }
                                break;
                            case AlarmEventType.UdcAlarm:
                                model.UDCAlarmCount -= 1;
                                if (model.UDCAlarmCount < 0)
                                {
                                    model.UDCAlarmCount = 0;
                                }
                                break;
                            case AlarmEventType.BreakerTrip:
                                model.BreakerTripCount -= 1;
                                if (model.BreakerTripCount < 0)
                                {
                                    model.BreakerTripCount = 0;
                                }
                                break;
                            default: break;
                        }

                        _cache.SetHashData($"AlarmCount-{AssetLevel.Panel}", alarmLog.PanelName, model);
                    }
                }
                #endregion

                #region Substation
                if (!string.IsNullOrEmpty(alarmLog.SubstationName))
                {
                    var modelDic = _cache.GetHashData<AlarmCountModel>($"AlarmCount-{AssetLevel.Substation}", alarmLog.SubstationName);
                    if (modelDic != null && modelDic.TryGetValue(alarmLog.SubstationName, out var model) && model != null)
                    {
                        switch (alarmLog.Severity)
                        {
                            case AlarmSeverity.High:
                                model.HighCount -= 1;
                                if (model.HighCount < 0)
                                {
                                    model.HighCount = 0;
                                }
                                break;
                            case AlarmSeverity.Middle:
                                model.MiddleCount -= 1;
                                if (model.MiddleCount < 0)
                                {
                                    model.MiddleCount = 0;
                                }
                                break;
                            case AlarmSeverity.Low:
                                model.LowCount -= 1;
                                if (model.LowCount < 0)
                                {
                                    model.LowCount = 0;
                                }
                                break;
                            default: break;
                        }

                        switch (alarmLog.EventType)
                        {
                            case AlarmEventType.Alarm:
                                model.AlarmCount -= 1;
                                if (model.AlarmCount < 0)
                                {
                                    model.AlarmCount = 0;
                                }
                                break;
                            case AlarmEventType.UdcAlarm:
                                model.UDCAlarmCount -= 1;
                                if (model.UDCAlarmCount < 0)
                                {
                                    model.UDCAlarmCount = 0;
                                }
                                break;
                            case AlarmEventType.BreakerTrip:
                                model.BreakerTripCount -= 1;
                                if (model.BreakerTripCount < 0)
                                {
                                    model.BreakerTripCount = 0;
                                }
                                break;
                            default: break;
                        }

                        _cache.SetHashData($"AlarmCount-{AssetLevel.Substation}", alarmLog.SubstationName, model);
                    }
                }
                #endregion

                if (!string.IsNullOrEmpty(alarmLog.DeviceName))
                {
                    var existsAlarm = await sqlClient.Queryable<AlarmLog>()
                        .Where(a => a.AssetId == alarmLog.AssetId
                            && a.Id != arg.AlarmLogId
                            && a.EventType != AlarmEventType.OperationLog
                            && a.EventType != AlarmEventType.DeviceLog
                            && a.Status >= AlarmLogStatus.New
                            && a.Status < AlarmLogStatus.Finish)
                        .CountAsync();

                    if (existsAlarm == 0)
                    {
                        var assetStatusRef = _provider.GetRequiredService<IAssetDataProxyRef>();
                        assetStatusRef.InputData(new AssetInputData
                        {
                            AssetId = alarmLog.AssetId.Value,
                            Datas = new Dictionary<string, string>
                            {
                                ["HaveAlarm"] = "0"
                            },
                            InputTime = DateTime.Now,
                        });
                    }
                }

                #region Send Extend Service
                {
                    var action = _provider.GetRequiredService<IExtendAction>();
                    await action.PublishAlarmAsync(alarmLog);
                }
                #endregion
            }
        }

        private async Task CheckChangeData(AssetChangeData changeData)
        {
            if (!StartWorker()) return;

            await CheckAndCreateLogicFunc();
            var alarms = _logicFunc(changeData);
            if (alarms.Any())
            {
                var needSaveAlarms = new List<AlarmRule>();
                foreach (var alarmRule in alarms)
                {
                    var exists = _cache.Get<bool>($"AlarmRoleCheck-{alarmRule.Id}|{changeData.AssetId}");
                    if (!exists)
                    {
                        needSaveAlarms.Add(alarmRule);   
                    }
                }

                if (needSaveAlarms.Any())
                {
                    _saveActor.Tell((needSaveAlarms.ToArray(), changeData));
                }
            }

            AddBreakerUntripedLog(changeData);
        }

        private void AddBreakerUntripedLog(AssetChangeData changeData)
        {
            if (changeData.AssetLevel == AssetLevel.Device
                && (changeData.AssetType == "MCCB" || changeData.AssetType == "ACB" || changeData.AssetType == "MCB")
                && changeData.ChangeDatas.ContainsKey("HasTriped"))
            {
                var hasTriped = changeData.ChangeDatas["HasTriped"];
                if ("0".Equals(hasTriped))
                {
                    var hasTripedCache = _cache.Get<bool>($"HasTriped-{changeData.AssetId}");

                    if (hasTripedCache)
                    {
                        _cache.Set($"HasTriped-{changeData.AssetId}", false);
                        var simgleInfo = _cache.Get<AssetSimpleInfo>(string.Format(Constant.AssetSimpleInfoCacheKey, changeData.AssetId));
                        if (simgleInfo != null)
                        {
                            var newAlarmLog = new AlarmLog
                            {
                                AssetId = changeData.AssetId,
                                Severity = AlarmSeverity.Middle,
                                Status = AlarmLogStatus.None,
                                DeviceName = simgleInfo.AssetName,
                                CircuitName = simgleInfo.CircuitSimpleInfo?.AssetName,
                                PanelName = simgleInfo.PanelSimpleInfo?.AssetName,
                                SubstationName = simgleInfo.SubstationSimpleInfo?.AssetName,
                                EventType = AlarmEventType.DeviceLog,
                                Message = $"{simgleInfo.AssetName}脱扣恢复。",
                                CreatedBy = "System",
                                CreatedTime = DateTime.Now,
                                UpdatedBy = "System",
                                UpdatedTime = DateTime.Now,
                            };

                            _saveActor.Tell(newAlarmLog);
                        }
                    }
                }
                else
                {
                    _cache.Set<bool>($"HasTriped-{changeData.AssetId}", true);
                }
            }
        }

        protected override bool AroundReceive(Receive receive, object message)
        {
            try
            {
                return base.AroundReceive(receive, message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AlarmActor Failed");
                return true;
            }
        }

        #region 缓存相关
        private async Task CheckAndCreateLogicFunc()
        {
            var sqlClient = _provider.GetRequiredService<SqlSugarScope>();
            {
                var lastAlarmTime = _cache.GetOrCreate(Constant.LastUpdatedTimeCacheKey, () => sqlClient.Queryable<AlarmRule>().Select(r => SqlFunc.AggregateMax(r.UpdatedTime)).First(), TimeSpan.FromMinutes(30));
                if (lastAlarmTime != _lastAlarmTime)
                {
                    _lastAlarmTime = lastAlarmTime;
                    var allAlarms = await GetAllEnableAlarms(sqlClient);
                    Expression<Func<AssetChangeData, AlarmRule[]>> expression = (input) => GetAlarmFunc(allAlarms, input);
                    _logicFunc = expression.Compile();
                }
            }
        }

        private async Task<AlarmRule[]> GetAllEnableAlarms(ISqlSugarClient sqlClient)
        {
            var alarms = await sqlClient.Queryable<AlarmRule>().Where(r => r.IsEnable && !r.IsDelete).ToArrayAsync();
            return alarms;
        }

        private AlarmRule[] GetAlarmFunc(AlarmRule[] alarmRules, AssetChangeData changeData)
        {
            switch(changeData.AssetLevel) 
            {
                case AssetLevel.Panel:
                    {
                        var targetTypeValue = $"{changeData.AssetType}";
                        var hitRules = alarmRules.Where(r => (r.TargetType == AlarmTargetType.Panel && r.TargetValue == changeData.AssetId.ToString()) || (r.TargetType == AlarmTargetType.PanelModel && r.TargetValue.Contains(targetTypeValue, StringComparison.OrdinalIgnoreCase))).ToArray();
                        if (hitRules != null)
                        {
                            hitRules = hitRules.Where(r => _server.CheckData(r, changeData.ChangeDatas)).ToArray();
                            return hitRules;
                        }
                        break;
                    }
                case AssetLevel.Transformer:
                    {
                        var targetTypeValue = $"{changeData.AssetType}";
                        var hitRules = alarmRules.Where(r => (r.TargetType == AlarmTargetType.Transformer && r.TargetValue == changeData.AssetId.ToString()) || (r.TargetType == AlarmTargetType.Transformer && r.TargetValue.Contains(targetTypeValue, StringComparison.OrdinalIgnoreCase))).ToArray();
                        if (hitRules != null)
                        {
                            hitRules = hitRules.Where(r => _server.CheckData(r, changeData.ChangeDatas)).ToArray();
                            return hitRules;
                        }
                        break;
                    }
                case AssetLevel.Circuit:
                    {
                        var targetTypeValue = $"{changeData.AssetType}";
                        var hitRules = alarmRules.Where(r => (r.TargetType == AlarmTargetType.Circuit && r.TargetValue == changeData.AssetId.ToString()) || (r.TargetType == AlarmTargetType.CircuitModel && r.TargetValue.Contains(targetTypeValue, StringComparison.OrdinalIgnoreCase))).ToArray();
                        if (hitRules != null)
                        {
                            hitRules = hitRules.Where(r => _server.CheckData(r, changeData.ChangeDatas)).ToArray();
                            return hitRules;
                        }
                        break;
                    }
                case AssetLevel.Substation:
                    {
                        var hitRules = alarmRules.Where(r => r.TargetType == AlarmTargetType.Substation && r.TargetValue == changeData.AssetId.ToString()).ToArray();
                        if (hitRules != null)
                        {
                            hitRules = hitRules.Where(r => _server.CheckData(r, changeData.ChangeDatas)).ToArray();
                            return hitRules;
                        }
                        break;
                    }
                case AssetLevel.Device:
                    {
                        var targetTypeValue = $"{changeData.AssetType}|{changeData.AssetModel}";
                        var hitRules = alarmRules.Where(r => (r.TargetType == AlarmTargetType.Device && r.TargetValue == changeData.AssetId.ToString()) || (r.TargetType == AlarmTargetType.DeviceModel && targetTypeValue.Contains(r.TargetValue))).ToArray();
                        if (hitRules != null)
                        {
                            hitRules = hitRules.Where(r => _server.CheckData(r, changeData.ChangeDatas)).ToArray();
                            return hitRules;
                        }
                        break;
                    }
                default:break;
            }
            return new AlarmRule[0];
        }

        private bool StartWorker()
        {
            var status = _cache.Get<int>("Status:AlarmWorker");
            if (status >= 10) 
            {
                return true;
            }
            return false;
        }
        #endregion
    }
}
