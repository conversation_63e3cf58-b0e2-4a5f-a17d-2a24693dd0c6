﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Topoplogy
{
    [SugarTable("topology_draft_info")]
    public class TopologyDraftInfo : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id {  get; set; }
        [SugarColumn(ColumnName = "topology_type", Length = 50, IsNullable = false)]
        public string TopologyType { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "topology_action", IsNullable = false)]
        public TopologyAction TopologyAction { get; set; }
        [SugarColumn(ColumnName = "user_id", IsNullable = false)]
        public int UserId {  get; set; }
        [SugarColumn(ColumnName = "topology_id", IsNullable = true)]
        public int? TopologyId { get; set; }
        /// <summary>
        /// 组态图 更新标识
        /// 使用guid，当值不同代表被修改
        /// </summary>
        [SugarColumn(ColumnName = "topology_flag", IsNullable = true, Length = 50)]
        public string? TopologyFlag { get; set; }
        [SugarColumn(ColumnName = "topology_name", IsNullable = false, Length = 256)]
        public string TopologyName { get; set; } = string.Empty;
        
        /// <summary>
        /// 长度为10M
        /// </summary>
        [SugarColumn(ColumnName = "data", ColumnDataType = "varchar(10485760)", IsNullable = false)]
        public string Data { get; set; } = string.Empty;
    }

    public enum TopologyAction : int
    {
        New = 10,
        Edit = 11
    }
}
