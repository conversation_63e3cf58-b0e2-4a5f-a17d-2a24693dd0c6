import axios from "axios";
import { initAxios } from "./apiSetting";

function getMonitorList(setMonitorList, failCallback) {
  initAxios();

  axios
    .get("api/getMonitorList")
    .then((resp) => {
      console.log(resp.data);
      setMonitorList(resp.data);
    })
    .catch((e) => {
      if (!!failCallback) failCallback();
    });

  // setMonitorList({
  //   xAxis: [
  //     {
  //       data: [
  //         "10:05:50",
  //         "10:05:40",
  //         "10:05:30",
  //         "10:05:20",
  //         "10:05:10",
  //         "10:05:00",
  //         "10:04:50",
  //         "10:04:40",
  //         "10:04:30",
  //         "10:04:20",
  //         "10:04:10",
  //         "10:04:00",
  //         "10:03:50",
  //         "10:03:40",
  //         "10:03:30",
  //         "10:03:20",
  //         "10:03:10",
  //         "10:03:00",
  //         "10:02:50",
  //         "10:02:40",
  //         "10:02:30",
  //         "10:02:20",
  //         "10:02:10",
  //         "10:02:00",
  //         "10:01:50",
  //         "10:01:40",
  //         "10:01:30",
  //         "10:01:20",
  //         "10:01:10",
  //         "10:01:00",
  //         "10:00:50",
  //         "10:00:40",
  //         "10:00:30",
  //         "10:00:20",
  //         "10:00:10",
  //         "10:00:00",
  //         "09:59:50",
  //         "09:59:40",
  //         "09:59:30",
  //         "09:59:20",
  //         "09:59:10",
  //         "09:59:00",
  //         "09:58:50",
  //         "09:58:40",
  //         "09:58:30",
  //         "09:58:20",
  //         "09:58:10",
  //         "09:58:00",
  //         "09:57:50",
  //         "09:57:40",
  //         "09:57:30",
  //         "09:57:20",
  //         "09:57:10",
  //         "09:57:00",
  //         "09:56:50",
  //         "09:56:40",
  //         "09:56:30",
  //         "09:56:20",
  //         "09:56:10",
  //         "09:56:00",
  //       ],
  //       type: "category",
  //     },
  //   ],
  //   yAxis: { type: "value" },
  //   series: [
  //     {
  //       name: "CPU(%)",
  //       data: [
  //         95.53, 67.9, 17.22, 35.24, 56.18, 88.48, 50.74, 48.26, 95.86, 57.87,
  //         92.99, 70.65, 40.57, 90.41, 17.38, 64.12, 80.47, 40.14, 40.49, 56.68,
  //         42.46, 40.23, 53.83, 14.46, 29.48, 45.83, 65.53, 49.78, 26.47, 81.12,
  //         75.42, 77.72, 33.31, 73.39, 46.88, 64.42, 77.31, 92.03, 16.19, 70.74,
  //         77.32, 11.57, 90.11, 63.98, 56.85, 79.65, 91.79, 38.91, 49.18, 26.81,
  //         84.29, 54.42, 88.59, 86.4, 20.37, 25.37, 66.28, 62.67, 62.05, 19.39,
  //       ],
  //       type: "line",
  //     },
  //     {
  //       name: "MEM(%)",
  //       data: [
  //         36.69, 44.78, 42.49, 49.33, 41.05, 24.08, 12.13, 49.89, 23.86, 36.76,
  //         21.58, 27.96, 10.69, 30.24, 44.47, 30.81, 33.65, 31.58, 39.05, 42.53,
  //         49.44, 15.1, 15.78, 21.88, 28.92, 14.07, 21.92, 42.39, 13.52, 37.25,
  //         29.97, 46.91, 12.71, 47.5, 21.28, 37.86, 37.13, 37.58, 33.05, 20.89,
  //         21.06, 42.52, 11.75, 42.3, 41.64, 27.64, 31.82, 17.36, 18.4, 42.33,
  //         40.2, 26.55, 26.38, 30.99, 27.4, 27.74, 33.68, 24.0, 29.81, 11.82,
  //       ],
  //       type: "line",
  //     },
  //   ],
  // });
}

function getProcessList(setProcessList, failCallback) {
  initAxios();

  axios
    .get("api/getProcessList")
    .then((resp) => {
      setProcessList(resp.data);
    })
    .catch((e) => {
      if (!!failCallback) failCallback();
    });
}

function currentStatus(setCurrentStatus, failCallback) {
  initAxios();

  axios
    .get("api/currentStatus")
    .then((resp) => {
      setCurrentStatus(resp.data);
    })
    .catch((e) => {
      if (!!failCallback) failCallback();
    });
}

export { getMonitorList, getProcessList, currentStatus };
