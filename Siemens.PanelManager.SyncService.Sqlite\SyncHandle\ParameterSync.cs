﻿using InfluxDB.Client;
using InfluxDB.Client.Api.Domain;
using InfluxDB.Client.Writes;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.SyncService.UdcModels;
using SqlSugar;

namespace Siemens.PanelManager.SyncService.Sqlite
{
    public class ParameterSync : SyncEntity, ISyncArchiveDataHandle
    {
        private readonly ILogger<ParameterSync> _logger;

        public ParameterSync(ILogger<ParameterSync> logger,
            IDbContextFactory<UdcContext> contextFactory,
            IConfiguration configuration,
            ISqlSugarClient client,
            IAssetDataProxyRef assetDataProxyRef) : base(contextFactory, configuration, client, assetDataProxyRef)
        {
            _logger = logger;

            ConfigName = nameof(Parameter);
        }

        public async Task SyncWorkAsync()
        {
            try
            {
                using var udcContext = await _contextFactory.CreateDbContextAsync();
                 using var client = new InfluxDBClient(InfluxdbUrl, InfluxdbUserName, InfluxdbPassword);

                var influxdbPing = await client.PingAsync();
                if (!influxdbPing)
                {
                    _logger.LogError($"InfluxDB can't connect.");
                    return;
                }

                long lastTimestamp = GetLastTimestamp(0);
                if (lastTimestamp <= 0)
                {
                    lastTimestamp = udcContext.Parameters.AsNoTracking()
                        .Select(a => a.UnacknowledgedMessagesTimestamp)
                        .DefaultIfEmpty()
                        .Min();
                }

                var data = await (from a in udcContext.Parameters.AsNoTracking()
                                  join l in (
                                      from r in udcContext.Parameters.AsNoTracking()
                                      where r.UnacknowledgedMessagesTimestamp >= lastTimestamp
                                      orderby r.UnacknowledgedMessagesTimestamp
                                      select new
                                      {
                                          r.Id,
                                          r.LastMessagesOid,
                                          r.UnacknowledgedMessagesTimestamp,
                                      }).Take(_perQueryCount)
                                     on new { a.Id, a.LastMessagesOid, a.UnacknowledgedMessagesTimestamp } equals new { l.Id, l.LastMessagesOid, l.UnacknowledgedMessagesTimestamp }
                                  orderby a.UnacknowledgedMessagesTimestamp
                                  select a).ToListAsync();

                if (data.Any())
                {
                    lastTimestamp = data.LastOrDefault()?.UnacknowledgedMessagesTimestamp ?? 0;
                    SaveLastTimestamp(lastTimestamp);
                }

                using var writeApi = client.GetWriteApi();

                var typeParameter = typeof(Parameter);
                var typeParameterProperties = typeParameter.GetProperties();
                var entityType = udcContext.Model.FindEntityType(typeParameter);

                PointData point;
                foreach (var archiveData in data)
                {
                    point = PointData.Measurement(nameof(Parameter).ToLower());
                    foreach (var item in typeParameterProperties)
                    {
                        var columnName = entityType.FindProperty(item.Name).GetColumnName();

                        if (item.Name == nameof(archiveData.Id) || item.Name == nameof(archiveData.LastMessagesOid))
                        {
                            point = point.Tag(columnName.ToLower(), item.GetValue(archiveData)?.ToString());
                        }
                        else if (item.Name == nameof(archiveData.UnacknowledgedMessagesTimestamp))
                        {
                            point = point.Timestamp(Convert.ToInt64(item.GetValue(archiveData)), WritePrecision.S);
                        }
                        else
                        {
                            point = point.Field(columnName?.ToLower(), Convert.ChangeType(item.GetValue(archiveData), item.PropertyType));
                        }
                    }

                    writeApi.WritePoint(point, InfluxdbBucket, InfluxdbOrgName);
                }

                _logger.LogDebug($"{nameof(Parameter)} Last Timestamp: {lastTimestamp}; Data Count: {data.Count}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.ToString());
                throw;
            }
        }
    }
}
