import axios from "axios";
import { initAxios } from "./apiSetting";

function getUpgradeStatus(setUpgradeStatus, failCallback) {
  initAxios();
  axios
    .get("/api/UpgradeStatus")
    .then((resp) => {
      setUpgradeStatus(resp.data);
    })
    .catch((e) => {
      if (!!failCallback) failCallback();
    });
}

function getUpgradeMessages(lastIndex, setUpgradeMessage, failCallback) {
  initAxios();
  axios
    .get("/api/upgrademessage?lastIndex=" + lastIndex)
    .then((resp) => {
      setUpgradeMessage(resp.data);
    })
    .catch((e) => {
      if (!!failCallback) failCallback();
    });
}

function packageUpload(file, successCallback, failCallback) {
  initAxios();

  const fromData = new FormData();
  fromData.append("file", file);
  axios
    .post("/api/uploadPackage", fromData)
    .then((resp) => {
      if (!!successCallback) successCallback(resp.data);
    })
    .catch((e) => {
      if (!!failCallback) failCallback();
    });
}

export { getUpgradeStatus, getUpgradeMessages, packageUpload };
