﻿using SqlSugar;

namespace Siemens.PanelManager.Model.Database.Asset
{
    [SugarTable("asset_general_data_point")]
    public class AssetGeneralDataPoints : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "asset_id", IsNullable = false)]
        public int AssetId { get; set; }
        [SugarColumn(ColumnName = "index", IsNullable = false)]
        public int Index { get; set; }
        [SugarColumn(ColumnName = "udc_code", IsNullable = false, Length = 50)]
        public string UdcCode { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "is_active", IsNullable = false)]
        public bool IsActive { get; set; }
        [SugarColumn(ColumnName = "code", IsNullable = false, Length = 50)]
        public string Code { get; set; } = string.Empty;
    }
}
