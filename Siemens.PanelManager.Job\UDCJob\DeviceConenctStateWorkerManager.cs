﻿using Siemens.PanelManager.Interface.Job;

namespace Siemens.PanelManager.Job.UDCJob
{
    public class DeviceConenctStateWorkerManager : IDeviceConenctStateWorkerManager
    {
        public async Task StartAsync()
        {
            if (UdcServiceContent.DeviceConnectStateWorker != null)
            {
                await UdcServiceContent.DeviceConnectStateWorker.StartAsync(CancellationToken.None);
            }
        }

        public async Task StopAsync()
        {
            if (UdcServiceContent.DeviceConnectStateWorker != null)
            {
                await UdcServiceContent.DeviceConnectStateWorker.StopAsync(CancellationToken.None);
            }
        }
    }
}
