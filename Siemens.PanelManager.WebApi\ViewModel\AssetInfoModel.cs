﻿using Newtonsoft.Json.Linq;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.Model.Database.Topology;
using Siemens.PanelManager.Model.Topology3D;
using SqlSugar;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class AssetInfoModel
    {
        public AssetInfoModel()
        { 
        }

        public AssetInfoModel(AssetInfo assetInfo, ISugarQueryable<TopologyInfo> topoplogyQuery, string hasConnect)
        {
            Id = assetInfo.Id;
            HasConnect = hasConnect;
            AssetName = assetInfo.AssetName;
            AssetNumber = assetInfo.AssetNumber;

            Level = assetInfo.AssetLevel.ToString();
            AssetLevel = (int)assetInfo.AssetLevel;
            Model = assetInfo.AssetModel;
            Mlfb = assetInfo.MLFB;
            Type = assetInfo.AssetType;
            Factory = assetInfo.AssetMaker;
            Location = assetInfo.Location;
            PointData = assetInfo.PointData;
            UseScene = assetInfo.UseScene;
            MeterType = assetInfo.MeterType;

            Description = assetInfo.Description;
            Person = assetInfo.Principal;
            Tel = assetInfo.Telephone;
            PrefixTel = assetInfo.PrefixTelephone;
            InstallDate = assetInfo.InstallDate;
            CircuitName = assetInfo.CircuitName;
            IpAddress = assetInfo.IPAddress;
            Port=assetInfo.Port;
            Port = assetInfo.Port;
            BusBarId = assetInfo.BusBarId;
            ThirdPartCode = assetInfo.ThirdPartCode;
            EnableMqtt = assetInfo.EnableMqtt ?? false;
            SortNo = assetInfo.SortNoNotNull;
            RelationPanelId = assetInfo.RelationPanelId;

            if (assetInfo.TopologyId != null && assetInfo.TopologyId > 0)
            {
                TopologyId = assetInfo.TopologyId;
            }

            if (assetInfo.Topology3DId != null && assetInfo.Topology3DId > 0)
            {
                Topology3DId = assetInfo.Topology3DId;
            }
        }

        public AssetInfoModel(AssetInfo assetInfo, ISugarQueryable<FileManager> fileManagers, ISugarQueryable<TopologyInfo> topoplogyQuery, string hasConnect)
            : this(assetInfo, topoplogyQuery, hasConnect)
        {
            var drawingFileIds = assetInfo.DrawingFileIds;
            if (drawingFileIds != null && drawingFileIds.Length > 0)
            {
                List<FileInfoModel> fileInfos = new List<FileInfoModel>();

                foreach (var draw in drawingFileIds)
                {
                    var file = fileManagers.First(f => f.Id == draw);
                    if (file != null)
                    {
                        fileInfos.Add(new FileInfoModel(file));
                    }
                }

                Drawing = fileInfos.ToArray();
            }

            var imageIds = assetInfo.ImageIds;
            if (imageIds != null && imageIds.Length > 0)
            {
                var images = new List<FileInfoModel>();
                foreach (var imgId in imageIds)
                {
                    var file = fileManagers.First(f => f.Id == imgId);
                    if (file != null)
                    {
                        images.Add(new FileInfoModel(file));
                    }
                }

                Img = images.ToArray();
            }
        }

        public int Id { get; set; }
        public string? AssetName { get; set; }
        public string? Person { get; set; }
        public string HasConnect { get; set; } = string.Empty;
        public string? Tel { get; set; }
        public string? PrefixTel { get; set; }
        public string? Description { get; set; }
        public string? AssetNumber { get; set; }
        public string? Level { get; set; }
        public string? Type { get; set; }
        public string? Model { get; set; }
        public string? Mlfb { get; set; }
        public string? Factory { get; set; }
        public string? Location { get; set; }
        public string? PointData { get; set; }
        public DateTime? InstallDate { get; set; }
        public string? UseScene { get; set;}
        public string? MeterType { get; set; }
        public string? CircuitName { get; set; }
        public string? IpAddress { get; set; }
        public string? Port { get; set; }  
        public string? BusBarId { get; set; }
        public FileInfoModel[]? Drawing { get; set; }
        public FileInfoModel[]? Img { get; set; }
        public List<AssetInfoModel>? Children { get; set;}
        public int? TopologyId { get; set; }
        public int? Topology3DId { get; set; }
        public JObject? Topology { get; set; }
        public Topology3D? Topology3D { get; set; }
        public AssetInfo ToAssetInfo()
        {
            var asset = new AssetInfo();
            ToAssetInfo(asset);
            return asset;
        }

        public decimal? RatedVoltage { get; set; }
        public decimal? RatedCurrent { get; set; }
        public decimal? RatedPower { get; set; }
        public MeterSite? MeterSite { get; set; }
        public string? ThirdPartCode { get; set; }
        public int? RelationPanelId { get; set; }

        //是否启用mqtt
        public bool? EnableMqtt { get; set; }

        public int SortNo { get; set; }
        public int AssetLevel { get; set; }

        public void ToAssetInfo(AssetInfo asset)
        {
            if (!string.IsNullOrEmpty(Level)
                && Enum.TryParse<AssetLevel>(Level, out AssetLevel assetLevel))
            {
                asset.AssetLevel = assetLevel;
            }
            asset.AssetMaker = Factory;
            asset.AssetNumber = AssetNumber ?? string.Empty;
            asset.AssetName = AssetName ?? string.Empty;
            asset.AssetType = Type;
            asset.PointData = PointData;
            asset.MeterType = MeterType;
            asset.AssetModel = Model;
            asset.MLFB = Mlfb;
            asset.IPAddress = IpAddress;
            asset.Port = Port;
            asset.BusBarId = BusBarId;
            asset.RelationPanelId = RelationPanelId;

            asset.Location = Location;
            asset.UseScene = UseScene;
            if (Drawing != null)
            {
                asset.DrawingFileIds = Drawing.Select(f => f.Id).ToArray();
            }
            else
            {
                asset.DrawingFileIds = null;
            }
            asset.Description = Description;
            asset.Principal = Person;
            asset.Telephone = Tel;
            asset.PrefixTelephone = PrefixTel;
            asset.InstallDate = InstallDate;
            asset.CircuitName = CircuitName;
            asset.ThirdPartCode = ThirdPartCode;
            asset.EnableMqtt = EnableMqtt ?? false;
            if (Img != null)
            {
                asset.ImageIds = Img.Select(i => i.Id).ToArray();
            }
            else
            {
                asset.ImageIds = null;
            }
            if (TopologyId != null)
            {
                asset.TopologyId = TopologyId;
            }
            else if (Topology != null)
            {
                if (Topology.TryGetValue("id", out var idToken)
                    && idToken != null)
                {
                    asset.TopologyId = idToken.Value<int>();
                }
            }
            else
            {
                asset.TopologyId = null;
            }
            if (Topology3DId != null)
            {
                asset.Topology3DId = Topology3DId;
            }
            else if (Topology3D != null)
            {
                asset.Topology3DId = Topology3D.Id;
            }
            else
            {
                asset.Topology3DId = null;
            }
        }
    }

    public class SimpleAssetInfo
    {
        public int AssetId { get; set; }
    }
}
