[{"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 2, "Begin": 6, "KeyRule": "06", "Value": "630"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 2, "Begin": 6, "KeyRule": "08", "Value": "800"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 2, "Begin": 6, "KeyRule": "10", "Value": "1000"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 2, "Begin": 6, "KeyRule": "12", "Value": "1250"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 2, "Begin": 6, "KeyRule": "16", "Value": "1600"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 2, "Begin": 6, "KeyRule": "20", "Value": "2000"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 2, "Begin": 6, "KeyRule": "25", "Value": "2500"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 2, "Begin": 6, "KeyRule": "30", "Value": "3000"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 2, "Begin": 6, "KeyRule": "32", "Value": "3200"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 2, "Begin": 6, "KeyRule": "40", "Value": "4000"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 2, "Begin": 6, "KeyRule": "50", "Value": "5000"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 2, "Begin": 6, "KeyRule": "60", "Value": "6000"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 2, "Begin": 6, "KeyRule": "63", "Value": "6300"}, {"Id": 0, "GroupCode": "MechanicalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 4, "Begin": 5, "KeyRule": "1..2", "Value": "15000"}, {"Id": 0, "GroupCode": "MechanicalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 4, "Begin": 5, "KeyRule": "1..3", "Value": "15000"}, {"Id": 0, "GroupCode": "MechanicalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 4, "Begin": 5, "KeyRule": "1..4", "Value": "10000"}, {"Id": 0, "GroupCode": "MechanicalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 4, "Begin": 5, "KeyRule": "1..8", "Value": "10000"}, {"Id": 0, "GroupCode": "MechanicalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 4, "Begin": 5, "KeyRule": "2..3", "Value": "10000"}, {"Id": 0, "GroupCode": "MechanicalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 4, "Begin": 5, "KeyRule": "2..4", "Value": "10000"}, {"Id": 0, "GroupCode": "MechanicalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 4, "Begin": 5, "KeyRule": "2..8", "Value": "10000"}, {"Id": 0, "GroupCode": "MechanicalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 4, "Begin": 5, "KeyRule": "2..5", "Value": "10000"}, {"Id": 0, "GroupCode": "MechanicalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 4, "Begin": 5, "KeyRule": "2..6", "Value": "5000"}, {"Id": 0, "GroupCode": "MechanicalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 4, "Begin": 5, "KeyRule": "3..8", "Value": "5000"}, {"Id": 0, "GroupCode": "MechanicalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 4, "Begin": 5, "KeyRule": "3..5", "Value": "7500"}, {"Id": 0, "GroupCode": "MechanicalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 4, "Begin": 5, "KeyRule": "3..6", "Value": "5000"}, {"Id": 0, "GroupCode": "ElectricalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 3, "Begin": 5, "KeyRule": "1(06|08|10|12|16)", "Value": "10000"}, {"Id": 0, "GroupCode": "ElectricalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 3, "Begin": 5, "KeyRule": "120", "Value": "7500"}, {"Id": 0, "GroupCode": "ElectricalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 3, "Begin": 5, "KeyRule": "125", "Value": "5000"}, {"Id": 0, "GroupCode": "ElectricalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 3, "Begin": 5, "KeyRule": "2(20|25)", "Value": "7500"}, {"Id": 0, "GroupCode": "ElectricalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 3, "Begin": 5, "KeyRule": "232", "Value": "4000"}, {"Id": 0, "GroupCode": "ElectricalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 3, "Begin": 5, "KeyRule": "240", "Value": "2000"}, {"Id": 0, "GroupCode": "ElectricalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 4, "Begin": 5, "KeyRule": "3..8", "Value": "1000"}, {"Id": 0, "GroupCode": "ElectricalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 4, "Begin": 5, "KeyRule": "3..5", "Value": "2000"}, {"Id": 0, "GroupCode": "ElectricalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WA", "Length": 4, "Begin": 5, "KeyRule": "3..6", "Value": "1000"}]