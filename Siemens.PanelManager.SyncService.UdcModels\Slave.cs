﻿using System;
using System.Collections.Generic;

namespace Siemens.PanelManager.SyncService.UdcModels
{
    public partial class Slave
    {
        public Slave()
        {
            SlaveMessages = new HashSet<SlaveMessages>();
        }

        public string ObjectId { get; set; } = null!;
        public long TypeId { get; set; }

        public virtual ICollection<SlaveMessages> SlaveMessages { get; set; }
    }
}
