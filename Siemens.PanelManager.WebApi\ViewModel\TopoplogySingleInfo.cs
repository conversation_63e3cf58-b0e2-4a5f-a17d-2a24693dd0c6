﻿namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class TopologySingleInfo
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime Time { get; set; }
        public string Owner { get; set; } = string.Empty;
        public bool DashboardFirst { get; set; }
    }
}
