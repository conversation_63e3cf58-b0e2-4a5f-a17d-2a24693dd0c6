﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class PowerBreakerViewModel
    {
        [JsonProperty("id")]
        public int id { get; set; }

        [JsonProperty("model")]
        public string model { get; set; }
        [JsonProperty("power")]
        public decimal power { get; set; }
        [JsonProperty("RatedCurrent")]
        public decimal RatedCurrent { get; set; }

    }
}
