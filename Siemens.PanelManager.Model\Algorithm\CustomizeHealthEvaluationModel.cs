﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.Model.Algorithm
{
    public class CustomizeHealthEvaluationModel
    {
        [JsonProperty(PropertyName = "familyDefectsMsg")]
        public string familyMsg { get; set; }
        [JsonProperty(PropertyName = "enviromentMsg")]
        public string enviromentMsg { get; set; }
        [JsonProperty(PropertyName = "cabinetMsg")]
        public string cabinetMsg { get; set; }
        [JsonProperty(PropertyName = "meterMsg")]
        public string meterMsg { get; set; }
        [JsonProperty(PropertyName = "lampMsg")]
        public string lampMsg { get; set; }
        [JsonProperty(PropertyName = "isolatedMsg")]
        public string isolatedMsg { get; set; }
        [JsonProperty(PropertyName = "protectionMsg")]
        public string protectionMsg { get; set; }

        [JsonProperty(PropertyName = "unwindingMethod")]
        public string unwindingMethodMsg { get; set; }
        [JsonProperty(PropertyName = "modelSelection")]
        public string modelSelectionMsg { get; set; }
        [JsonProperty(PropertyName = "transformerConfiguration")]
        public string transformerConfigurationMsg { get; set; }
        [JsonProperty(PropertyName = "airSwitchBreakingCapacity")]
        public string airSwitchBreakingCapacityMsg { get; set; }
        [JsonProperty(PropertyName = "cabinetAppearance")]
        public string cabinetAppearanceMsg { get; set; }
        [JsonProperty(PropertyName = "cabinetConnection")]
        public string cabinetConnectionMsg { get; set; }
        [JsonProperty(PropertyName = "cabinetGrounding")]
        public string cabinetGroundingMsg { get; set; }
        [JsonProperty(PropertyName = "cabinetLocking")]
        public string cabinetLockingMsg { get; set; }
        [JsonProperty(PropertyName = "airSwitchAppearance")]
        public string airSwitchAppearanceMsg { get; set; }
        [JsonProperty(PropertyName = "airSwitchOperation")]
        public string airSwitchOperationMsg { get; set; }
        [JsonProperty(PropertyName = "airSwitchPosition")]
        public string airSwitchPositionMsg { get; set; }
        [JsonProperty(PropertyName = "contactorSuctionCondition")]
        public string contactorSuctionConditionMsg { get; set; }
        [JsonProperty(PropertyName = "contactorInsulationComponents")]
        public string contactorInsulationComponentsMsg { get; set; }
        [JsonProperty(PropertyName = "contactorAuxiliaryContacts")]
        public string contactorAuxiliaryContactsMsg { get; set; }
        [JsonProperty(PropertyName = "contactorExtinguishingCover")]
        public string contactorExtinguishingCoverMsg { get; set; }
        [JsonProperty(PropertyName = "contactorClosingCoil")]
        public string contactorClosingCoilMsg { get; set; }
        [JsonProperty(PropertyName = "lightningArresterAppearance")]
        public string lightningArresterAppearanceMsg { get; set; }
        [JsonProperty(PropertyName = "voice")]
        public string voiceMsg { get; set; }
        [JsonProperty(PropertyName = "operationTest")]
        public string operationTestMsg { get; set; }
        [JsonProperty(PropertyName = "infraredTest")]
        public string infraredTestMsg { get; set; }
        [JsonProperty(PropertyName = "contactorActionTest")]
        public string contactorActionTestMsg { get; set; }
        [JsonProperty(PropertyName = "lightningArresterTest")]
        public string lightningArresterTestMsg { get; set; }


        [JsonProperty(PropertyName = "evaluationResult")]
        public evaluationResult evaluationResult { get; set; }
        [JsonProperty(PropertyName = "historicalResult")]
        public historicalResult historicalResult { get; set; }

    }

}
