using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.AspNetCore.WebSockets;
using Microsoft.Extensions.Options;
using Siemens.PanelManager.Monitor.Function;
using Siemens.PanelManager.Monitor.Hubs;
using Siemens.PanelManager.Monitor.StaticData;
using Siemens.PanelManager.Monitor.Workers;

var param = new string[] { "--urls", "http://0.0.0.0:51030" };
var builder = WebApplication.CreateBuilder(param);

builder.Services.AddControllers();
builder.WebHost.ConfigureKestrel(opt =>
{
    opt.Limits.MaxRequestBodySize = 1024L * 1024L * 1024L ;
});
builder.Services.AddSignalR(opt =>
{
    opt.EnableDetailedErrors = true;
}).AddJsonProtocol();
builder.Services.AddWebSockets(opt =>
{
    opt.KeepAliveInterval = TimeSpan.FromMinutes(2);
});
builder.Services.AddLogging(opt =>
{
    opt.ClearProviders();
    opt.AddLog4Net();
});
builder.Services.AddHostedService<ClearLogFileWorker>();
builder.Services.AddHostedService<MonitorWorker>();
builder.Services.AddHostedService<ClearUnknowUserWorker>();
builder.Services.AddSingleton<IpTableManager>();
builder.Services.AddSingleton<UpgradeWorker>();
builder.Services.AddSingleton<AutoTestWorker>();
builder.Services.AddSingleton<UpgradeWorkerStatus>();
builder.Services.AddTransient<LocalSecurityFunc>();
builder.Services.Configure<FormOptions>(option =>
{
    option.MultipartBodyLengthLimit = 1024L * 1024L * 1024L;
});
var app = builder.Build();

app.UseExceptionHandler("/Error");

app.UseRouting();
app.UseWebSockets();
app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");
app.UseEndpoints(endpoints =>
{
    endpoints.MapHub<MonitorHub>("/monitor", opt => { opt.Transports = Microsoft.AspNetCore.Http.Connections.HttpTransportType.WebSockets; });
    endpoints.MapHub<AutoTestHub>("/AutoTest", opt => { opt.Transports = Microsoft.AspNetCore.Http.Connections.HttpTransportType.WebSockets; });
});

app.Run();
