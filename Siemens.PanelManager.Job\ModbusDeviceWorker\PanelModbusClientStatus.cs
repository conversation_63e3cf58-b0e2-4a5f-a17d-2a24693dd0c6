﻿using System.Collections.Concurrent;

namespace Siemens.PanelManager.Job.ModbusDeviceWorker
{
    public class PanelModbusClientStatus
    {
        private static readonly Lazy<PanelModbusClientStatus> _instance = new Lazy<PanelModbusClientStatus>(() => new PanelModbusClientStatus());

        private ConcurrentDictionary<int, string> _modbusConnectStatus;
        private ConcurrentDictionary<int, int> _modbusConnectFail;
        private ConcurrentDictionary<string, decimal> _modbusDataPointCustomFactor;

        private int _failCount = 2;

        private PanelModbusClientStatus()
        {
            _modbusConnectStatus = new ConcurrentDictionary<int, string>();
            _modbusConnectFail = new ConcurrentDictionary<int, int>();
            _modbusDataPointCustomFactor = new ConcurrentDictionary<string, decimal>();
        }

        public static PanelModbusClientStatus GetInstance()
        {
            return _instance.Value;
        }

        public void SetClientStatus(int assetId, string status)
        {
            if (assetId < 0) return;

            if (_modbusConnectStatus.ContainsKey(assetId))
            {
                if (status == "1")
                {
                    string currentValue = _modbusConnectStatus[assetId];
                    _modbusConnectStatus.TryUpdate(assetId, status, currentValue);

                    if (_modbusConnectFail.ContainsKey(assetId))
                    {
                        int tempFailCount = _modbusConnectFail[assetId];

                        _modbusConnectFail.TryUpdate(assetId, 0, tempFailCount);
                    }
                }
                else
                {
                    if (_modbusConnectFail.ContainsKey(assetId))
                    {
                        int tempFailCount = _modbusConnectFail[assetId];

                        if (tempFailCount >= _failCount)
                        {
                            string currentValue = _modbusConnectStatus[assetId];
                            _modbusConnectStatus.TryUpdate(assetId, status, currentValue);
                            _modbusConnectFail.TryUpdate(assetId, 0, tempFailCount);
                        }
                        else
                        {
                            var tempUpdateCount = tempFailCount + 1;
                            _modbusConnectFail.TryUpdate(assetId, tempUpdateCount, tempFailCount);
                        }
                    }
                    else
                    {
                        _modbusConnectFail.TryAdd(assetId, 1);
                    }
                }
            }
            else
            {
                _modbusConnectStatus.TryAdd(assetId, status);
            }
        }

        public string GetClientStatus(int assetId)
        {
            if (_modbusConnectStatus.TryGetValue(assetId, out var status))
            {
                return status;
            }

            return "0";
        }

        public void SetDataPointCustomFactor(int assetId, string dataPointName, decimal value)
        {
            if (assetId <= 0 || string.IsNullOrEmpty(dataPointName))
            {
                return;
            }

            string keyName = string.Concat(assetId.ToString(), "-", dataPointName);
            if (_modbusDataPointCustomFactor.ContainsKey(keyName))
            {
                var currentValue = _modbusDataPointCustomFactor[keyName];
                _modbusDataPointCustomFactor.TryUpdate(keyName, value, currentValue);
            }
            else
            {
                _modbusDataPointCustomFactor.TryAdd(keyName, value);
            }
        }

        public decimal GetDataPointCustomFactor(int assetId, string dataPointName)
        {
            string keyName = string.Concat(assetId.ToString(), "-", dataPointName);
            if (_modbusDataPointCustomFactor.TryGetValue(keyName, out var value))
            {
                return value;
            }

            return 1.0m;
        }

        public void ClearAllStatus() => _modbusConnectStatus.Clear();

        public void ClearDataPointCustomFactor() => _modbusDataPointCustomFactor.Clear();
    }
}
