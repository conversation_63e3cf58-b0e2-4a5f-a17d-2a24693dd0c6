﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.DeviceDataFlow.MessageBus
{
    static class MessageBusContext
    {
        public static MessageRegister MessageRegister { get; private set; }
        public static AssetChangeRegister AssetChangeRegister { get; private set; }
        static MessageBusContext()
        {
            MessageRegister = new MessageRegister();
            AssetChangeRegister = new AssetChangeRegister();
        }
    }
}
