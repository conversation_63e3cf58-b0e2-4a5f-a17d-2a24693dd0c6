﻿using Microsoft.AspNetCore.Mvc;
using System.Xml.Linq;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class EnergyPowerQueryParam : EnergyQueryParamBase
    {
        [FromQuery(Name = "chartDateType")]
        public int ChartDateType { get; set; }

        [FromQuery(Name = "startDate")]
        public string? StartDate { get; set; }

        /// <summary>
        /// ActivePower(有功功率):0,ReactivePower(无功功率):1,ApparentPower(视在功率):2,Default:0,1,2
        /// </summary>
        [FromQuery(Name = "powerType")]
        public List<ThreePhasePowerType>? PowerType { get; set; }
    }

    public enum ThreePhasePowerType
    {
        /// <summary>
        /// 有功功率
        /// </summary>
        ActivePower,

        /// <summary>
        /// 无功功率
        /// </summary>
        ReactivePower,

        /// <summary>
        /// 视在功率
        /// </summary>
        ApparentPower,
    }
}
