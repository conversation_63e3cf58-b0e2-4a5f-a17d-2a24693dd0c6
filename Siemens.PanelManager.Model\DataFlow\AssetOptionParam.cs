﻿namespace Siemens.PanelManager.Model.DataFlow
{
    public class AssetOptionParam
    {
        public AssetOptionParam(AssetOpt opt, int assetId)
        {
            Opt = opt;
            AssetId = assetId;
        }
        public AssetOptionParam(AssetOpt opt, string assetName)
        {
            Opt = opt;
            AssetName = assetName;
        }

        public AssetOpt Opt { get; private set; }
        public int AssetId { get; private set; }
        public string? AssetName { get; private set; }
    }
}
