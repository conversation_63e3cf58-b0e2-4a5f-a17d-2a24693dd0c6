# Dependency Injection Job

## How to DI job

The Project need have one ServiceInjection

~~~ C#
    public class ServiceInjection : IServiceInjection
    {
        public void AddService(IServiceCollection services)
        {
            // we can DI Job by ServiceCollection
            services.AddTransient<JobBase, ClearLogFileJob>();
            ....
        }
    }
~~~

The Job need dependency by JobBase
~~~ C#
    public class ClearLogFileJob : JobBase
    {
        public override string Name => "ClearLogFileJob";
        public override Task Execute()
        {
            ....
        }
    }
~~~

## How to schedule run once job

Get the JobManager
- ServiceProvider:
~~~ C#
    private AssetExtendServer _extendServer => _provider.GetRequiredService<AssetExtendServer>();
~~~
- Constructor
~~~ C#
    public AssetController(JobManager jobManager)
~~~

Use JobManager tigger job

~~~ C#
    await _jobManager.TriggerJob("UploadExcelJob", param);
~~~

## How to schedule periodically Job

Currently job server only support <PERSON><PERSON> Job, we need and job config into sys_job_schedule

|Column Name|Description|
|-----------|-----------|
|job_name|job name need same as code|
|job_code|job uniqueness key|
|is_main|only schedule true job|
|parent_job_code|if we need schedule some job after some job run finish, use this column can run job after parent job run finish|
|cron|job schedule periodically|
|parameters|we can set job parameters, it is json string|