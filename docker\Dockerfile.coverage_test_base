#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.
FROM mcr.microsoft.com/dotnet/sdk:6.0

RUN dotnet tool install -g altcover.global
RUN dotnet tool install -g dotnet-reportgenerator-globaltool
RUN dotnet tool install --global gensummary --version *******

# 准备newman环境
RUN curl -sL https://deb.nodesource.com/setup_16.x | bash -
RUN apt install -y nodejs zip procps net-tools docker.io vim
RUN curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
RUN chmod +x /usr/local/bin/docker-compose
RUN npm install -g newman
RUN npm install -g newman-reporter-htmlextra

# # Prepare custome onereport tool
# RUN curl -L "https://github.com/EasonRen/pm/archive/refs/tags/1.0.tar.gz" -o /tmp/1.0.tar.gz
# RUN tar -zxvf /tmp/1.0.tar.gz -C /tmp
# RUN chmod 777 /tmp/pm-1.0/onereport
