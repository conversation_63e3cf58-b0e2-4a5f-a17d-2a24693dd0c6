﻿using System.Text;

namespace Siemens.InfluxDB.Helper.FluxModel
{
    internal class RangeModel
    {
        public RangeModel() 
        {
            End = DateTime.Now;
            Start = End.AddHours(-5);
        }
        public DateTime Start { get; set; }
        public DateTime End { get; set; }

        public void AppendFlux(StringBuilder flux)
        {
            flux.AppendLine($"|> range(start:{Start.GetTimestampForSec()}, stop:{End.GetTimestampForSec()})");
        }
    }
}
