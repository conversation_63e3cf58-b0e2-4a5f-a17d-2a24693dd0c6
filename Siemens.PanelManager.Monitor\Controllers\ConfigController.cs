﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Siemens.PanelManager.Monitor.Function;

namespace Siemens.PanelManager.Monitor.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ConfigController : ControllerBase
    {
        private ILogger<ConfigController> _logger;
        private IServiceProvider _provider;


        public ConfigController(ILogger<ConfigController> logger, IServiceProvider provider)
        {
            _logger = logger;
            _provider = provider;
        }

        [HttpGet("ntp_address")]
        public async Task<string> GetNTPAddress()
        {
            string ipAddress = string.Empty;
            try
            {
                ipAddress = await NTPServerFunc.GetCurrentNTPServer(_logger);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetNTPAddress");
            }

            return ipAddress;
        }

        [HttpPost("ntp_address")]
        public async Task<string> SetNTPAddress([FromBody] string ipAddress)
        {
            string setResult = string.Empty;

            try
            {
                await NTPServerFunc.SetNTPServer(ipAddress, _logger);
            }
            catch (Exception ex)
            {
                setResult = ex.Message;
                _logger.LogError(ex, "SetNTPAddress");
            }

            return setResult;
        }

        [HttpGet("x2p1_address")]
        public async Task<string[]> GetX2P1Config()
        {
            try
            {
                var config = await ChangeIPFunc.GetX2p1Config(_logger);
                return config;

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetX2P1Config");
                return new string[0];
            }
        }

        [HttpPost("x2p1_address")]
        public async Task<string> SetX2P1Config([FromBody] List<string> configs)
        {
            string setResult = string.Empty;
            try
            {
                await ChangeIPFunc.SetX2p1Config(configs, _logger);

            }
            catch (Exception ex)
            {
                setResult = ex.Message;
                _logger.LogError(ex, "SetX2P1Config");
            }

            return setResult;
        }

        [HttpPost("system_reboot")]
        public async Task<string> RebootSystem()
        {
            string setResult = string.Empty;
            try
            {
                await ChangeIPFunc.Reboot(_logger);

            }
            catch (Exception ex)
            {
                setResult = ex.Message;
                _logger.LogError(ex, "RebootSystem");
            }

            return setResult;
        }
    }
}