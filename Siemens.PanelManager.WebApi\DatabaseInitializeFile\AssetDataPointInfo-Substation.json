[{"Code": "Total_P", "Name": "Total_P", "GroupName": "Power", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "W", "CollectMode": "PowerCompute", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 1, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Total_Q", "Name": "Total_Q", "GroupName": "Power", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "var", "CollectMode": "PowerCompute", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 2, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Total_S", "Name": "Total_S", "GroupName": "Power", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "VA", "CollectMode": "PowerCompute", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 3, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Total_ActiveEnergy", "Name": "Total_ActiveEnergy", "GroupName": "Energy", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "Wh", "CollectMode": "EnergyCompute", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 4, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "SubstationTemp", "Name": "SubstationTemp", "GroupName": "BasicInfo", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 5, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "SubstationHumidness", "Name": "SubstationHumidness", "GroupName": "BasicInfo", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "[H]", "UdcCode": "", "Unit": "%", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 6, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Alarm_Severity", "Name": "Alarm_Severity", "GroupName": "Warnings", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Alarm/Value/AlarmSeverity", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 7, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Status", "Name": "Alarm_Status", "GroupName": "Warnings", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Alarm/Value/AlarmStatus", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 8, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Path", "Name": "Alarm_Path", "GroupName": "Warnings", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Alarm/Value/AlarmPath", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Info", "Name": "Alarm_Info", "GroupName": "Warnings", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Alarm/Value/AlarmInfo", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 10, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Time", "Name": "Alarm_Time", "GroupName": "Warnings", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Alarm/Value/AlarmTime", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 11, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "SubStation_Loss", "Name": "SubStation_Loss", "GroupName": "Others", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "SubStation_Loss", "Unit": "KWh", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 12, "ParentName": "Others"}, {"Code": "SubStation_Percentage", "Name": "SubStation_Percentage", "GroupName": "Others", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "SubStation_Percentage", "Unit": "%", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 13, "ParentName": "Others"}, {"Code": "SubStation_Safety_Scope", "Name": "SubStation_Safety_Scope", "GroupName": "Others", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "SubStation_Safety_Scope", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 14, "ParentName": "Others"}, {"Code": "SubStation_Day_Energy_ loss", "Name": "SubStation_Day_Energy_ loss", "GroupName": "Others", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "SubStation_Day_Energy_ loss", "Unit": "MWh", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 15, "ParentName": "Others"}, {"Code": "SubStation_Month_Energy_ loss", "Name": "SubStation_Month_Energy_ loss", "GroupName": "Others", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "SubStation_Month_Energy_ loss", "Unit": "GWh", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 17, "ParentName": "Others"}, {"Code": "SubStation_Year_Energy_ loss", "Name": "SubStation_Year_Energy_ loss", "GroupName": "Others", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "SubStation_Year_Energy_ loss", "Unit": "GWh", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 18, "ParentName": "Others"}, {"Code": "SubStation_Day_Cost_ loss", "Name": "SubStation_Day_Cost_ loss", "GroupName": "Others", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "SubStation_Day_Cost_ loss", "Unit": "￥", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19, "ParentName": "Others"}, {"Code": "SubStation_Month_Cost_ loss", "Name": "SubStation_Month_Cost_ loss", "GroupName": "Others", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "SubStation_Month_Cost_ loss", "Unit": "￥", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 20, "ParentName": "Others"}, {"Code": "SubStation_Year_Cost_ loss", "Name": "SubStation_Year_Cost_ loss", "GroupName": "Others", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "SubStation_Year_Cost_ loss", "Unit": "￥", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 21, "ParentName": "Others"}, {"Code": "Health_Status", "Name": "Health_Status", "GroupName": "Others", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Health_Status", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 22, "ParentName": "Others"}, {"Code": "SubStation_Day_Efficiency", "Name": "SubStation_Day_Efficiency", "GroupName": "Others", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "SubStation_Day_Efficiency", "Unit": "%", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 23, "ParentName": "Others"}, {"Code": "SubStation_Month_Efficiency", "Name": "SubStation_Month_Efficiency", "GroupName": "Others", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "SubStation_Month_Efficiency", "Unit": "%", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 24, "ParentName": "Others"}, {"Code": "SubStation_Year_Efficiency", "Name": "SubStation_Year_Efficiency", "GroupName": "Others", "AssetLevel": 20, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "SubStation_Year_Efficiency", "Unit": "%", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 25, "ParentName": "Others"}]