{"info": {"_postman_id": "bbf47286-341a-4d5f-837c-b81e6ca54958", "name": "20使用管理员账号进入panel manager告警管理中的告警列表菜单，筛选事件类型为告警or日志or操作日志查看告警内容", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 7", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "获取事件类型为告警的告警列表 Copy 5", "event": [{"listen": "test", "script": {"exec": ["\r", "\r", "pm.test(\"显示事件类型为告警的告警内容\", function () {\r", "    var jsonData = pm.response.json().items[0];\r", "    pm.expect(jsonData[\"eventType\"]).to.eql(0);\r", "    pm.expect(pm.response.text()).to.include(\"alarmInfo\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm?eventType=0&page=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm"], "query": [{"key": "eventType", "value": "0"}, {"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}]}}, "response": []}, {"name": "获取事件类型为日志的告警列表 Copy 6", "event": [{"listen": "test", "script": {"exec": ["\r", "\r", "pm.test(\"显示事件类型为日志的告警内容\", function () {\r", "    var jsonData = pm.response.json().items[0];\r", "    pm.expect(jsonData[\"eventType\"]).to.eql(1);\r", "    pm.expect(pm.response.text()).to.include(\"alarmInfo\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm?eventType=1&page=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm"], "query": [{"key": "eventType", "value": "1"}, {"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}]}}, "response": []}, {"name": "获取事件类型为操作的告警列表 Copy 8", "event": [{"listen": "test", "script": {"exec": ["\r", "\r", "pm.test(\"显示事件类型为操作日志的告警内容\", function () {\r", "    var jsonData = pm.response.json().items[0];\r", "    pm.expect(jsonData[\"eventType\"]).to.eql(2);\r", "    pm.expect(pm.response.text()).to.include(\"alarmInfo\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm?eventType=2&page=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm"], "query": [{"key": "eventType", "value": "2"}, {"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 1000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", "pm.test(\"code码为20000\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData[\"code\"]).to.eql(20000);", "});", ""]}}]}