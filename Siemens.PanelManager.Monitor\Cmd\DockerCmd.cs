﻿using CliWrap;
using Newtonsoft.Json;
using Siemens.PanelManager.HubModel;
using System;
using System.Text;
using System.Text.Json.Serialization;

namespace Siemens.PanelManager.Monitor.Cmd
{
    static class DockerCmd
    {
        public static async Task Load(string tarFile, ILogger logger)
        {
            Func<string, Task> func = (message) =>
            {
                logger.LogInformation(message);
                return Task.CompletedTask;
            };

            var result = await Cli.Wrap("docker")
                    .WithValidation(CommandResultValidation.None)
                    .WithArguments(new string[] { "load", "-i", tarFile })
                    .WithStandardOutputPipe(PipeTarget.ToDelegate(func, Encoding.UTF8))
                    .WithStandardErrorPipe(PipeTarget.ToDelegate(func, Encoding.UTF8))
                    .ExecuteAsync();
        }

        public static async Task<DockerProcessStats[]> Stats(ILogger logger)
        {
            var statsList = new List<DockerProcessStats>();
            Func<string, Task> func = (message) =>
            {
                try
                {
                    message = message.Trim('\"');
                    var stats = JsonConvert.DeserializeObject<DockerProcessStats>(message);
                    if (stats != null)
                    {
                        statsList.Add(stats);
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, $"读取失败 - {message}");
                }
               return Task.CompletedTask;
            };
            Func<string, Task> errorFunc = (message) => 
            {
                logger.LogError(message);
                return Task.CompletedTask; 
            };

            var result = await Cli.Wrap("docker")
                .WithValidation(CommandResultValidation.None)
                .WithArguments(new string[] {"stats", "--no-stream", "--format", "\"{{json .}}\"" })
                .WithStandardOutputPipe(PipeTarget.ToDelegate(func, Encoding.UTF8))
                .WithStandardErrorPipe(PipeTarget.ToDelegate(errorFunc, Encoding.UTF8))
                .ExecuteAsync();

            logger.LogDebug($"Cmd docker stats run {result.RunTime}");
            return statsList.ToArray();
        }

        public static async Task ImageRM(ILogger logger, string[] imageIds) 
        {
            if (imageIds.Length == 0)
            {
                logger.LogInformation("imageIds no data");
                return;
            }
            var arguments = new List<string>();
            arguments.Add("rmi");
            foreach (var imageId in imageIds)
            {
                arguments.Add(imageId);
            }

            Func<string, Task> errorFunc = (message) =>
            {
                logger.LogError(message);
                return Task.CompletedTask;
            };

            var result = await Cli.Wrap("docker")
                .WithValidation(CommandResultValidation.None)
                .WithArguments(arguments.ToArray())
                .WithStandardErrorPipe(PipeTarget.ToDelegate(errorFunc))
                .ExecuteAsync();
        }

        public static async Task<DockerImageInfo[]> Images(ILogger logger, string? imageName = null)
        {
            var arguments = new List<string>();
            arguments.Add("images");
            if (!string.IsNullOrEmpty(imageName))
            {
                arguments.Add(imageName);
            }
            arguments.Add("--format");
            arguments.Add("\"{{json .}}\"");

            var resultList = new List<DockerImageInfo>();
            Func<string, Task> func = (message) =>
            {
                try
                {
                    message = message.Trim('\"');
                    var imageInfo = JsonConvert.DeserializeObject<DockerImageInfo>(message);
                    if (imageInfo != null)
                    {
                        resultList.Add(imageInfo);
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, $"读取失败 - {message}");
                }

                return Task.CompletedTask;
            };

            Func<string, Task> errorFunc = (message) =>
            {
                logger.LogError(message);
                return Task.CompletedTask;
            };

            var result = await Cli.Wrap("docker")
                .WithValidation(CommandResultValidation.None)
                .WithArguments(arguments.ToArray())
                .WithStandardOutputPipe(PipeTarget.ToDelegate(func, Encoding.UTF8))
                .WithStandardErrorPipe(PipeTarget.ToDelegate(errorFunc, Encoding.UTF8))
                .ExecuteAsync();

            return resultList.ToArray();
        }

        public static async Task<DockerProcessStatus[]> PS(ILogger logger, bool includeAll =false)
        {
            var arguments = new List<string>();
            arguments.Add("ps");
            if (includeAll)
            {
                arguments.Add("-a");
            }
            arguments.Add("--format");
            arguments.Add("\"{{json .}}\"");

            var resultList = new List<DockerProcessStatus>();
            Func<string, Task> func = (message) =>
            {
                try
                {
                    message = message.Trim('\"');
                    var statusInfo = JsonConvert.DeserializeObject<DockerProcessStatus>(message);
                    if (statusInfo != null)
                    {
                        resultList.Add(statusInfo);
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, $"读取失败 - {message}");
                }

                return Task.CompletedTask;
            };

            Func<string, Task> errorFunc = (message) =>
            {
                logger.LogError(message);
                return Task.CompletedTask;
            };

            var result = await Cli.Wrap("docker")
                .WithValidation(CommandResultValidation.None)
                .WithArguments(arguments.ToArray())
                .WithStandardOutputPipe(PipeTarget.ToDelegate(func, Encoding.UTF8))
                .WithStandardErrorPipe(PipeTarget.ToDelegate(errorFunc, Encoding.UTF8))
                .ExecuteAsync();

            return resultList.ToArray();
        }

        public static async Task Tag(ILogger logger, string imageId, string tag)
        {
            var arguments = new string[]
            {
                "tag",
                imageId,
                tag
            };

            Func<string, Task> func = (message) =>
            {
                logger.LogInformation(message);
                return Task.CompletedTask;
            };

            var result = await Cli.Wrap("docker")
                .WithValidation(CommandResultValidation.None)
                .WithArguments(arguments)
                .WithStandardOutputPipe(PipeTarget.ToDelegate(func, Encoding.UTF8))
                .WithStandardErrorPipe(PipeTarget.ToDelegate(func, Encoding.UTF8))
                .ExecuteAsync();
        }
    }

    internal class DockerImageInfo
    {
        public string Containers { get; set; } = string.Empty;
        public string CreatedAt { get; set; } = string.Empty;
        public string CreatedSince { get; set; } = string.Empty;
        public string Digest { get; set; } = string.Empty;
        public string ID { get; set; } = string.Empty;
        public string Repository { get; set; } = string.Empty;
        public string SharedSize { get; set; } = string.Empty;
    }

    internal class DockerProcessStatus
    {
        public string Command { get; set; } = string.Empty;
        public string CreateAt { get; set; } = string.Empty;
        public string ID { get; set; } = string.Empty;
        public string Image { get; set; } = string.Empty;
        public string Labels { get; set; } = string.Empty;
        public string LocalVolumes { get; set; } = string.Empty;
        public string Mounts { get; set; } = string.Empty;
        public string Names { get; set; } = string.Empty;
        public string Networks { get; set; } = string.Empty;
        public string Ports { get; set; } = string.Empty;
        public string RunningFor { get; set; } = string.Empty;
        public string Size { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
    }
}

