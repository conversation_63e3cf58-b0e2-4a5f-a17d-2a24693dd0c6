{"info": {"_postman_id": "13ae8c74-f8ae-4b7f-8ba8-fe282cb4a3cc", "name": "03使用管理员账号进入panel manager告警管理中的告警列表菜单，查看告警统计：未确认告警，已确认告警，告警总数，高级告警，中级告警，低级告警", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 3", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "添加新的告警 Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm?message=test01&ruleId=0&eventType=0", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm"], "query": [{"key": "message", "value": "test01"}, {"key": "ruleId", "value": "0"}, {"key": "eventType", "value": "0"}]}}, "response": []}, {"name": "添加新的告警 Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm?message=test02&ruleId=0&eventType=1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm"], "query": [{"key": "message", "value": "test02"}, {"key": "ruleId", "value": "0"}, {"key": "eventType", "value": "1"}]}}, "response": []}, {"name": "添加新的告警 Copy 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm?message=test03&ruleId=0&eventType=2", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm"], "query": [{"key": "message", "value": "test03"}, {"key": "ruleId", "value": "0"}, {"key": "eventType", "value": "2"}]}}, "response": []}, {"name": "添加新的告警 Copy 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm?message=test04&ruleId=1&eventType=0", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm"], "query": [{"key": "message", "value": "test04"}, {"key": "ruleId", "value": "1"}, {"key": "eventType", "value": "0"}]}}, "response": []}, {"name": "添加新的告警 Copy 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm?message=test05&ruleId=1&eventType=1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm"], "query": [{"key": "message", "value": "test05"}, {"key": "ruleId", "value": "1"}, {"key": "eventType", "value": "1"}]}}, "response": []}, {"name": "添加新的告警 Copy 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm?message=test06&ruleId=1&eventType=2", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm"], "query": [{"key": "message", "value": "test06"}, {"key": "ruleId", "value": "1"}, {"key": "eventType", "value": "2"}]}}, "response": []}, {"name": "添加新的告警 Copy 3", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm?message=test04&ruleId=2&eventType=0", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm"], "query": [{"key": "message", "value": "test04"}, {"key": "ruleId", "value": "2"}, {"key": "eventType", "value": "0"}]}}, "response": []}, {"name": "添加新的告警 Copy 3", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm?message=test05&ruleId=2&eventType=1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm"], "query": [{"key": "message", "value": "test05"}, {"key": "ruleId", "value": "2"}, {"key": "eventType", "value": "1"}]}}, "response": []}, {"name": "添加新的告警 Copy 3", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm?message=test06&ruleId=2&eventType=2", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm"], "query": [{"key": "message", "value": "test06"}, {"key": "ruleId", "value": "2"}, {"key": "eventType", "value": "2"}]}}, "response": []}, {"name": "获取告警当日信息总览 Copy 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"包含未确认告警，已确认告警，告警总数，高级告警，中级告警，低级告警\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"highCount\");\r", "    pm.expect(pm.response.text()).to.include(\"lowCount\");\r", "    pm.expect(pm.response.text()).to.include(\"middleCount\");\r", "    pm.expect(pm.response.text()).to.include(\"totalCount\");\r", "    pm.expect(pm.response.text()).to.include(\"finishCount\");\r", "    pm.expect(pm.response.text()).to.include(\"ignoreCount\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "language", "value": "CN", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/v1/Alarm/GetCurrentInfo", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm", "GetCurrentInfo"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}