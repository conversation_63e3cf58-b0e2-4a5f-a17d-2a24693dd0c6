{"name": "sysloss_month", "status": "active", "flux": "import \"date\"\n\noption task = {\n    name: \"sysloss_month\",\n    every: 1mo,\n}\n\nfrom(bucket: \"panel\")\n    |> range(start: -1d)\n    |> filter(fn: (r) => r[\"_measurement\"] == \"sysloss_day\")\n    |> sum()\n    |> map(fn: (r) => ({r with _measurement: \"sysloss_month\", _time: date.truncate(t: now(), unit: 1d)}))\n    |> to(bucket: \"panel\", tagColumns: [\"topology_id\"])", "every": "1mo"}