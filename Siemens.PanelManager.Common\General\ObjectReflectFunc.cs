﻿namespace Siemens.PanelManager.Common.General
{
    public class ObjectReflectFunc
    {
        public T UpdateObjByDic<T>(T obj, Dictionary<string, string> dic) where T : class
        {
            var type = obj.GetType();
            foreach ( var kv in dic) 
            {

                var property = type.GetProperty(kv.Key);
                if (property == null) continue;
                if (property.PropertyType == typeof(string))
                {
                    property.SetValue(obj, kv.Value, null);
                }
                else if (property.PropertyType.IsGenericType && property.PropertyType.GetGenericTypeDefinition().Equals(typeof(Nullable<>)))
                {
                    var newValueType = Nullable.GetUnderlyingType(property.PropertyType);
                    if (newValueType != null)
                    {
                        try
                        {
                            var newValue = Convert.ChangeType(kv.Value, newValueType);
                            property.SetValue(obj, newValue, null);
                        }
                        catch
                        {
                        }
                    }
                }
                else
                {
                    try
                    {
                        var newValue = Convert.ChangeType(kv.Value, property.PropertyType);
                        property.SetValue(obj, newValue, null);
                    }
                    catch
                    {
                    }
                }
            }

            return obj;
        }

        public T UpdateObjByObj<T, R>(T obj, R obj2) 
            where T : class 
            where R : class
        {
            var type = obj.GetType();
            var type2 = obj2.GetType();
            var properties = type2.GetProperties();
            foreach (var p in properties)
            {
                var property = type.GetProperty(p.Name);
                if (property == null) continue;
                var value = p.GetValue(obj2);
                if (property.PropertyType == p.PropertyType)
                {
                    property.SetValue(obj, value, null);
                }
                else if (property.PropertyType.IsGenericType && property.PropertyType.GetGenericTypeDefinition().Equals(typeof(Nullable<>)))
                {
                    var newValueType = Nullable.GetUnderlyingType(property.PropertyType);
                    if (newValueType != null)
                    {
                        try
                        {
                            var newValue = Convert.ChangeType(value, newValueType);
                            property.SetValue(obj, newValue, null);
                        }
                        catch
                        {
                        }
                    }
                }
                else
                {
                    try
                    {
                        var newValue = Convert.ChangeType(value, property.PropertyType);
                        property.SetValue(obj, newValue, null);
                    }
                    catch
                    {
                    }
                }
            }

            return obj;
        }
    }
}
