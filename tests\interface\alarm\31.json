{"info": {"_postman_id": "aff85e73-fe5a-45d1-883d-bae6e454f950", "name": "31使用管理员账号进入panel manager告警管理中的告警配置菜单，新建告警规则告警名称输入纯特殊字符or纯数字or特殊字符加数字", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 14", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "添加告警规则Copy Copy 3", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为40300\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(40300);\r", "});\r", "\r", "pm.test(\"告警规则创建失败\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"message\"]).to.eql(\"参数错误\");\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n\t\"name\": \"@#￥\",\r\n\t\"rule\": \"电压Ua > 10  \",\r\n\t\"isEnable\": true,\r\n\t\"level\": 0,\r\n\t\"model\": \"\",\r\n\t\"type\": \"\",\r\n\t\"targetValue\": \"断路器1\",\r\n\t\"targetType\": \"Device\",\r\n\t\"sections\": [{\r\n\t\t\"point\": \"电压Ua\",\r\n\t\t\"compare\": \">\",\r\n\t\t\"dataValue\": 10,\r\n\t\t\"andOr\": \"\"\r\n\t}],\r\n\t\"detail\": \"threshold range\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/AlarmRule", "host": ["{{baseUrl}}"], "path": ["api", "v1", "AlarmRule"]}}, "response": []}, {"name": "添加告警规则Copy Copy 4", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为40300\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(40300);\r", "});\r", "\r", "pm.test(\"告警规则创建失败\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"message\"]).to.eql(\"参数错误\");\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n\t\"name\": \"123123\",\r\n\t\"rule\": \"电压Ua > 10  \",\r\n\t\"isEnable\": true,\r\n\t\"level\": 0,\r\n\t\"model\": \"\",\r\n\t\"type\": \"\",\r\n\t\"targetValue\": \"断路器1\",\r\n\t\"targetType\": \"Device\",\r\n\t\"sections\": [{\r\n\t\t\"point\": \"电压Ua\",\r\n\t\t\"compare\": \">\",\r\n\t\t\"dataValue\": 10,\r\n\t\t\"andOr\": \"\"\r\n\t}],\r\n\t\"detail\": \"threshold range\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/AlarmRule", "host": ["{{baseUrl}}"], "path": ["api", "v1", "AlarmRule"]}}, "response": []}, {"name": "添加告警规则Copy Copy 4", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为40300\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(40300);\r", "});\r", "\r", "pm.test(\"告警规则创建失败\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"message\"]).to.eql(\"参数错误\");\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n\t\"name\": \"123@#￥\",\r\n\t\"rule\": \"电压Ua > 10  \",\r\n\t\"isEnable\": true,\r\n\t\"level\": 0,\r\n\t\"model\": \"\",\r\n\t\"type\": \"\",\r\n\t\"targetValue\": \"断路器1\",\r\n\t\"targetType\": \"Device\",\r\n\t\"sections\": [{\r\n\t\t\"point\": \"电压Ua\",\r\n\t\t\"compare\": \">\",\r\n\t\t\"dataValue\": 10,\r\n\t\t\"andOr\": \"\"\r\n\t}],\r\n\t\"detail\": \"threshold range\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/AlarmRule", "host": ["{{baseUrl}}"], "path": ["api", "v1", "AlarmRule"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 1000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", ""]}}]}