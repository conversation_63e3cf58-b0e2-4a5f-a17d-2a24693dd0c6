﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.DeviceDataFlow.UDC
{
    internal class GetMessageParams
    {
        public string ObjectId { get; set; } = string.Empty;
        public int Oid { get; set; }
    }

    internal class GetDeviceMessageParams
    {
        public string ObjectId { get; set; } = string.Empty;
        public int Count { get; set; } = 10;
        public string? Oid { get; set; }
    }

    internal class GetUdcLicensesParams
    {
        
    }
}
