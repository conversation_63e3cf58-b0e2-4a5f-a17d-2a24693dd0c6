{"info": {"_postman_id": "157d8b54-8fe9-489e-8a94-5422e72ee55d", "name": "39使用管理员账号进入panel manager资产管理中的资产列表菜单，点击设备查看设备类型和设备型号是否正确匹配", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "获取静态数据 Copy 3", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "\r", "pm.test(\"设备类型正确\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"MCCB\",\"塑壳断路器\",\"ACB\",\"空气断路器\",\"MCB\",\"微型断路器\",\"MotorProtector\",\"马达保护\",\"SoftStarter\",\"软启动器\",\"Relay\",\"微机保护\",\"ATSE\",\"双电源\",\"Gateway\",\"网关\",\"PLC\",\"Meter\",\"测量仪表\",\"GeneralDevice\",\"通用设备\");\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Asset/model/DeviceType", "host": ["{{baseUrl}}"], "path": ["api", "v1", "<PERSON><PERSON>", "model", "DeviceType"]}}, "response": []}, {"name": "获取静态数据 Copy 3", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"设备类型正确\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"MCCB\",\"3VA\",\"3VA2\",\"ACB\",\"3WA\",\"3WL\",\"3WT\",\"MCB\",\"5SL\",\"5SV\",\"5ST\",\"3UF\",\"MotorProtector\",\"3UE\",\"3RW\",\"SoftStarter\",\"7SJ68\",\"Relay\",\"7SJ62\",\"7SJ63\",\"3KC2\",\"ATSE\",\"3KC8\",\"POC1000\",\"Gateway\",\"PLC1200\",\"PLC\",\"3KC8\",\"Meter\",\"PAC1020\",\"PAC3120\",\"PAC3220\",\"PAC4220\",\"PAC4200\",\"PAC5220\",\"PAC1200\",\"PAC1600\",\"P35\",\"P36\",\"GeneralDevice\",\"GENERALDEVICE\");\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Asset/model/DeviceModel", "host": ["{{baseUrl}}"], "path": ["api", "v1", "<PERSON><PERSON>", "model", "DeviceModel"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}]}