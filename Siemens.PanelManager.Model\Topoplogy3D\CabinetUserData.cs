﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.Model.Topology3D
{
    public class CabinetUserData : UserData
    {
        [JsonProperty(propertyName: "uuid", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public string Id { get; set; } = Guid.NewGuid().ToString();
        [JsonProperty(propertyName: "assetId")]
        public int? AssetId { get; set; }
        [JsonProperty(propertyName: "busbarStructure")]
        public string? BusbarStructure { get; set; }
        [JsonProperty(propertyName: "panelType")]
        public string PanelType { get; set; } = string.Empty;
        [JsonProperty(propertyName: "circuitList")]
        public List<CircuitItem> CircuitList { get; set; } = new List<CircuitItem>();
    }
}
