﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Server.Algorithm;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using Swashbuckle.AspNetCore.Annotations;
using System.Text;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class VersionController : SiemensApiControllerBase
    {
        public const string MAIN_VERSION_FILE_NAME = "panel_version.txt";
        //此文件目前由gitlab-runner自动生成，如果手工编译，请在根目录添加此文件
        public const string BUILD_VERSION_FILE_NAME = "panel_build.txt";

        public const string VERSION_CACHE_KEY = "Version_Key";

        private ILogger _logger;
        private SiemensCache _cache;
        private readonly IServiceProvider _provider;

        public VersionController(IServiceProvider provider, SiemensCache cache, ILogger<VersionController> logger)
            : base(provider, cache)
        {
            _provider = provider;
            _cache = cache;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ResponseBase<PanelVersion>> GetSystemVersion()
        {
            var _lossDiagnose = _provider.GetRequiredService<LossDiagnose>();

            string algorithmVersion = string.Empty;
            string algorithmBuild = string.Empty;

            try
            {
                var response = await _lossDiagnose.GetAlgorithmVersion();

                JObject jObject = JObject.Parse(response);

                algorithmVersion = jObject["data"]["version"].ToString();
                algorithmBuild = jObject["data"]["build"].ToString();
            }
            catch { }

            PanelVersion? panelVersion = null;

            panelVersion = _cache.Get<PanelVersion>(VERSION_CACHE_KEY);

            if (panelVersion != null)
            {
                panelVersion.AlgorithmVersion = algorithmVersion;
                panelVersion.AlgorithmBuild = algorithmBuild;

                return new ResponseBase<PanelVersion>()
                {
                    Code = 20000,
                    Data = panelVersion
                };
            }

            string rootDirectory = AppContext.BaseDirectory;

            var mainVersion = await System.IO.File.ReadAllTextAsync(Path.Combine(rootDirectory, MAIN_VERSION_FILE_NAME));

            var buildVersion = string.Empty;
            var buildFile = Path.Combine(rootDirectory, BUILD_VERSION_FILE_NAME);
            if (System.IO.File.Exists(buildFile))
            {
                buildVersion = await System.IO.File.ReadAllTextAsync(buildFile);
            }

            panelVersion = new PanelVersion
            {
                Version = mainVersion,
                Build = buildVersion,
                AlgorithmVersion = algorithmVersion,
                AlgorithmBuild = algorithmBuild,
            };

            _cache.Set(VERSION_CACHE_KEY, panelVersion, TimeSpan.FromDays(30));

            return new ResponseBase<PanelVersion>()
            {
                Code = 20000,
                Data = panelVersion
            };
        }

        [HttpPost("/api/v1/upgrade")]
        [RequestSizeLimit(5L * 1024L * 1024L * 1024L)]
        [RequestFormLimits(MultipartBodyLengthLimit = 5L * 1024L * 1024L * 1024L)]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_System_Upgrade", Description = "Swagger_System_Upgrade_Desc")]
        public async Task<ResponseBase<UpgradeResult>> Upgrade([FromForm] IFormCollection form)
        {
            var config = _provider.GetRequiredService<IConfiguration>();
            var monitorApi = config.GetValue<string>("MonitorApi");
            if (!string.IsNullOrEmpty(monitorApi))
            {
                _logger.LogDebug(monitorApi);
                _logger.LogDebug($"{form.Files.Count}");
                var file = form.Files.FirstOrDefault(f => Regex.IsMatch(f.FileName, "\\.zip$"));
                if (file != null)
                {
                    _logger.LogDebug($"{file.FileName}");

                    if (monitorApi.Last() != '/')
                    {
                        monitorApi += '/';
                    }

                    var localSecurityFunc = _provider.GetRequiredService<LocalSecurityFunc>();
                    var key = Guid.NewGuid().ToString();
                    var code = localSecurityFunc.GetSecurityKey(key);
                    var token = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{key}:{code}"));
                    using (HttpClient client = new HttpClient())
                    {
                        client.Timeout = TimeSpan.FromMinutes(5);
                        client.DefaultRequestHeaders.Add("Authorization", $"Basic {token}");

                        var multipartContext = new MultipartFormDataContent();
                        var fileContext = new StreamContent(file.OpenReadStream());
                        multipartContext.Add(fileContext, "file", file.FileName);
                        var response = await client.PostAsync($"{monitorApi}api/upload/upgrade", multipartContext);
                        if (response != null && response.IsSuccessStatusCode)
                        {
                            var resultStr = await response.Content.ReadAsStringAsync();
                            _logger.LogDebug(resultStr);
                            var result = JsonConvert.DeserializeObject<UpgradeResult>(resultStr);
                            return new ResponseBase<UpgradeResult>
                            {
                                Code = 20000,
                                Data = result
                            };
                        }
                    }
                }
            }

            return new ResponseBase<UpgradeResult>
            {
                Code = 20000,
                Data = new UpgradeResult
                {
                    ValidatedPass = false,
                }
            };
        }
    }
}
