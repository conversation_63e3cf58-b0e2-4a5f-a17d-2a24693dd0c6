[{"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "096", "Value": "16"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "020", "Value": "20"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "025", "Value": "25"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "032", "Value": "32"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "040", "Value": "40"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "050", "Value": "50"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "063", "Value": "63"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "080", "Value": "80"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "010", "Value": "100"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "108", "Value": "8"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "192", "Value": "12.5"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "196", "Value": "16"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "120", "Value": "20"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "125", "Value": "25"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "132", "Value": "32"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "140", "Value": "40"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "150", "Value": "50"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "163", "Value": "63"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "180", "Value": "80"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "110", "Value": "100"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "112", "Value": "125"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "116", "Value": "160"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "220", "Value": "200"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "225", "Value": "250"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "216", "Value": "160"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "325", "Value": "250"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "232", "Value": "320"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "240", "Value": "400"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "440", "Value": "400"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "450", "Value": "500"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "463", "Value": "630"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "563", "Value": "630"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "580", "Value": "800"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "MCCB", "DeviceModel": "3VA", "Length": 3, "Begin": 5, "KeyRule": "510", "Value": "1000"}]