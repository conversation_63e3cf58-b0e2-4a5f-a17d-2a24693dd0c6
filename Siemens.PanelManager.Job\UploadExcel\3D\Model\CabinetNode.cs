﻿using Newtonsoft.Json;
using Siemens.PanelManager.Model.Topology3D;

namespace Siemens.PanelManager.Job.UploadExcel._3D.Model
{
    /// <summary>
    /// 柜子的3D模型
    /// </summary>
    internal class CabinetNode: ICabinetNode
    {
        [JsonIgnore]
        public PanelModel PanelModel { get; private set; }

        public override string NodeType => "panel";

        public CabinetNode(PanelModel panelModel) 
        {
            AssetInfo = panelModel.AssetInfo;
            Id = Guid.NewGuid().ToString();
            Type = NodeType;
            Name = panelModel.AssetInfo.AssetName;
            PanelModel = panelModel;
            Size.Width = panelModel.Width;
            Size.Height = panelModel.Height;
            Size.Depth = 800;
            var circuitList = GetCircuits(panelModel.SubCircuits);
            UserData = new CabinetUserData()
            {
                BusbarStructure = panelModel.BusbarStructure,
                Type = NodeType,
                AssetId = panelModel.AssetInfo.Id,
                PanelType = panelModel.AssetInfo.AssetModel ?? "Other",
                CircuitList = circuitList
            };
        }

        private CabinetNode() 
        { }

        /// <summary>
        /// 添加回路模型进入柜子
        /// </summary>
        /// <param name="circuitModels"></param>
        /// <returns></returns>
        private List<CircuitItem> GetCircuits(List<CircuitModel> circuitModels) 
        {
            var circuitList = new List<CircuitItem>();
            foreach (var circuitModel in circuitModels) 
            {
                string devicetype = "";
                var device = circuitModel.SubDevices.FirstOrDefault(a => a.AssetType == "ACB");
                if (device != null)
                {
                    devicetype = device.AssetModel ?? "";
                }
                circuitList.Add(new CircuitItem()
                {
                    AssetId = circuitModel.AssetInfo.Id,
                    AssetName = circuitModel.AssetInfo.AssetName,
                    AssetNumber = circuitModel.AssetInfo.AssetNumber,
                    CircuitName = circuitModel.AssetInfo.CircuitName ?? string.Empty,
                    AssetType = circuitModel.AssetInfo.AssetType ?? string.Empty,
                    Description = circuitModel.AssetInfo.Description ?? string.Empty,
                    UseScene = circuitModel.AssetInfo.UseScene ?? string.Empty,
                    Height = circuitModel.Heigth,
                    Width = circuitModel.Width,
                    DeviceType = devicetype
                });
            }
            return circuitList;
        }

        /// <summary>
        /// 获取简易的柜子模型
        /// </summary>
        /// <returns></returns>
        public override SingalCabinetNode GetSingalNode() 
        {
            return new SingalCabinetNode(Id, Position, Size);
        }

        public override ICabinetNode GetNew()
        {
            return new CabinetNode()
            {
                Id = Id,
                Name = Name,
                Type = NodeType,
                Position = new Position(),
                Rotation= Rotation,
                Size = Size,
                UserData = UserData
            };
        }
    }
}
