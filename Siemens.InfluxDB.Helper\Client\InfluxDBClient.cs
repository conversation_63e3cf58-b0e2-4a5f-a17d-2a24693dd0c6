﻿using InfluxDB.Client.Api.Domain;
using Siemens.InfluxDB.Helper.General;
using Siemens.InfluxDB.Helper.Interface;
using IInfluxDBClient = InfluxDB.Client.IInfluxDBClient;

namespace Siemens.InfluxDB.Helper.Client
{
    public class InfluxDBClient : Interface.IInfluxDBClient
    {
        private DBConfig _config;
        private IInfluxDBClient? _client;
        private Bucket? _bucket;
        private Organization? _organization;

        public InfluxDBClient(DBConfig config)
        {
            if (config == null
                || string.IsNullOrEmpty(config.Url)
                || string.IsNullOrEmpty(config.Bucket)
                || string.IsNullOrEmpty(config.OrgName)
                || string.IsNullOrEmpty(config.UserName)
                || string.IsNullOrEmpty(config.Password))
                throw new ArgumentNullException("缺少配置");

            _config = config;
        }

        public void Dispose()
        {
            _client?.Dispose();
        }

        public string GetBucket()
        {
            return _config.Bucket;
        }

        public async Task<IInsertable<T>> GetInsertClient<T>() where T : IInfluxData
        {
            if (_client == null || _bucket == null || _organization == null)
            {
                await InitClient();
            }
            return new InsertableClient<T>(_client!, _organization!, _bucket!);
        }

        public string GetOrganizationName()
        {
            return _config.OrgName;
        }

        public async Task<Interface.IQueryable<T>> GetQueryClient<T>() where T : IInfluxData
        {
            if (_client == null || _bucket == null || _organization == null)
            {
                await InitClient();
            }
            return new QueryableClient<T>(_client!, _organization!, _bucket!);
        }

        private async Task InitClient()
        {
            var result = await StaticFunction.InitClient(_config.Url, _config.UserName, _config.Password, _config.Bucket, _config.OrgName);
            _client = result.Client;
            _bucket = result.Bucket;
            _organization = result.Organization;
        }

        public async Task<List<TaskType>> GetTaskListAsync()
        {
            if (_client == null || _bucket == null || _organization == null)
            {
                await InitClient();
            }
            var taskApi = _client!.GetTasksApi();
            var tasks = await taskApi.FindTasksByOrganizationIdAsync(_organization!.Id);
            return tasks;
        }

        public async Task AddTaskAsync(TaskType taskType)
        {
            if (_client == null || _bucket == null || _organization == null)
            {
                await InitClient();
            }
            var taskApi = _client!.GetTasksApi();
            taskType.OrgID = _organization!.Id;
            await taskApi.CreateTaskAsync(taskType);
        }

        public async Task UpdateTaskAsync(TaskType taskType)
        {
            if (_client == null || _bucket == null || _organization == null)
            {
                await InitClient();
            }
            var taskApi = _client!.GetTasksApi();
            taskType.OrgID = _organization!.Id;
            await taskApi.UpdateTaskAsync(taskType);
        }

        public string GetUserName()
        {
            return _config.UserName;
        }

        public async Task<string> GetBucketId()
        {
            if (_client == null || _bucket == null || _organization == null)
            {
                var result = await StaticFunction.InitClient(_config.Url, _config.UserName, _config.Password, _config.Bucket, _config.OrgName);
                _client = result.Client;
                _bucket = result.Bucket;
                _organization = result.Organization;
            }

            return _bucket.Id;
        }

        public async Task<string> GetOrgId()
        {
            if (_client == null || _bucket == null || _organization == null)
            {
                var result = await StaticFunction.InitClient(_config.Url, _config.UserName, _config.Password, _config.Bucket, _config.OrgName);
                _client = result.Client;
                _bucket = result.Bucket;
                _organization = result.Organization;
            }

            return _organization.Id;
        }
    }
}
