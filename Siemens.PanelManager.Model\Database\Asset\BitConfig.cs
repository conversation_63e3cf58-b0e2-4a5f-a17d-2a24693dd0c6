﻿using SqlSugar;

namespace Siemens.PanelManager.Model.Database.Asset
{
    /// <summary>
    /// 二进制对应点位配置
    /// </summary>
    [SugarTable("bit_config", TableDescription = "二进制对应点位配置表")]
    public class BitConfig : LogicDataBase
    {
        /// <summary>
        /// 主键id
        /// </summary>
        [SugarColumn(ColumnDescription = "主键id", ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// UniversalDeviceConfig表的主键id
        /// </summary>
        [SugarColumn(ColumnDescription = "UniversalDeviceConfig表的主键id", ColumnName = "universal_device_config_id", IsNullable = false)]
        public int UniversalDeviceConfigId { get; set; }

        /// <summary>
        /// 二进制点位中文名称
        /// </summary>
        [SugarColumn(ColumnDescription = "二进制点位中文名称", ColumnName = "bit_name", IsNullable = false, Length = 128)]
        public string? BitName { get; set; }

        /// <summary>
        /// bit位序号(一共16位,索引从0-15)
        /// </summary>
        [SugarColumn(ColumnDescription = " bit位序号(一共16位,索引从0-15)", ColumnName = "bit_number", IsNullable = false)]
        public int BitNumber { get; set; }

        [SugarColumn(ColumnDescription = "二进制点位的标识", ColumnName = "bit_code", IsNullable = true, Length = 128)]
        public string? BitCode { get; set; }

        /// <summary>
        /// bit分组序号
        /// </summary>
        [SugarColumn(ColumnDescription = " bit分组序号", ColumnName = "bit_group_number", IsNullable = true)]
        public int BitGroupNumber { get; set; } = 0;

        /// <summary>
        /// 事件类型
        /// </summary>
        [SugarColumn(ColumnDescription = "事件类型", ColumnName = "event_type", IsNullable = true)]
        public int EventType { get; set; } = -1;


        /// <summary>
        /// 告警级别
        /// </summary>
        [SugarColumn(ColumnDescription = "告警级别", ColumnName = "alarm_level", IsNullable = true)]
        public int AlarmLevel { get; set; } = -1;

    }
}
