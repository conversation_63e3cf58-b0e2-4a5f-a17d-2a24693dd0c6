{"info": {"_postman_id": "5fea53a7-4d8e-4c89-a087-a216e2ffba91", "name": "34使用管理员账号进入panel manager告警管理中的告警配置菜单，选中要删除节点点击批量删除按钮批量删除", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 12", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "获取全部告警规则 Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"包含规则ID，告警名称，告警规则，告警级别，是否启用\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"id\");\r", "    pm.expect(pm.response.text()).to.include(\"name\");\r", "    pm.expect(pm.response.text()).to.include(\"rule\");\r", "    pm.expect(pm.response.text()).to.include(\"level\");\r", "    pm.expect(pm.response.text()).to.include(\"isEnable\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/AlarmRule?page=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["api", "v1", "AlarmRule"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}]}}, "response": []}, {"name": "添加告警规则Copy Copy", "event": [{"listen": "test", "script": {"exec": ["let ID1 = pm.response.json().data\r", "pm.environment.set(\"ID1\", ID1);\r", "console.log(ID1)"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n\t\"name\": \"test01\",\r\n\t\"rule\": \"电压Ua > 10  \",\r\n\t\"isEnable\": true,\r\n\t\"level\": 0,\r\n\t\"model\": \"\",\r\n\t\"type\": \"\",\r\n\t\"targetValue\": \"断路器1\",\r\n\t\"targetType\": \"Device\",\r\n\t\"sections\": [{\r\n\t\t\"point\": \"电压Ua\",\r\n\t\t\"compare\": \">\",\r\n\t\t\"dataValue\": 10,\r\n\t\t\"andOr\": \"\"\r\n\t}],\r\n\t\"detail\": \"threshold range\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/AlarmRule", "host": ["{{baseUrl}}"], "path": ["api", "v1", "AlarmRule"]}}, "response": []}, {"name": "添加告警规则Copy Copy 2", "event": [{"listen": "test", "script": {"exec": ["let ID2 = pm.response.json().data\r", "pm.environment.set(\"ID2\", ID2);\r", "console.log(ID2)"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n\t\"name\": \"test01\",\r\n\t\"rule\": \"电压Ua > 10  \",\r\n\t\"isEnable\": true,\r\n\t\"level\": 0,\r\n\t\"model\": \"\",\r\n\t\"type\": \"\",\r\n\t\"targetValue\": \"断路器1\",\r\n\t\"targetType\": \"Device\",\r\n\t\"sections\": [{\r\n\t\t\"point\": \"电压Ua\",\r\n\t\t\"compare\": \">\",\r\n\t\t\"dataValue\": 10,\r\n\t\t\"andOr\": \"\"\r\n\t}],\r\n\t\"detail\": \"threshold range\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/AlarmRule", "host": ["{{baseUrl}}"], "path": ["api", "v1", "AlarmRule"]}}, "response": []}, {"name": "删除告警规则Copy Copy 2", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n\t\"name\": \"test01\",\r\n\t\"rule\": \"电压Ua > 10  \",\r\n\t\"isEnable\": true,\r\n\t\"level\": 0,\r\n\t\"model\": \"\",\r\n\t\"type\": \"\",\r\n\t\"targetValue\": \"断路器1\",\r\n\t\"targetType\": \"Device\",\r\n\t\"sections\": [{\r\n\t\t\"point\": \"电压Ua\",\r\n\t\t\"compare\": \">\",\r\n\t\t\"dataValue\": 10,\r\n\t\t\"andOr\": \"\"\r\n\t}],\r\n\t\"detail\": \"threshold range\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/AlarmRule/batch?ids={{ID1}},{{ID2}}", "host": ["{{baseUrl}}"], "path": ["api", "v1", "AlarmRule", "batch"], "query": [{"key": "ids", "value": "{{ID1}},{{ID2}}"}]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 1000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", "pm.test(\"code码为20000\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData[\"code\"]).to.eql(20000);", "});", ""]}}]}