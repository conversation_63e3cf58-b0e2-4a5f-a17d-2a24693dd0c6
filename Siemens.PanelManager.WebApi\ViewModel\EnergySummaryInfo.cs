﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class EnergySummaryInfo
    {
        [JsonConverter(typeof(StringEnumConverter))]
        public TotalType TotalType { get; set; }
        public decimal Electricity { get; set; }
        public string? ElectricityUnit { get; set; }
        public decimal Fee { get; set; }
        public string? FeeUnit { get; set; }
        public int SortIndex { get; set; }
    }

    public enum TotalType
    {
        Day = 0,
        Month,
        Year,
        All
    }
}
