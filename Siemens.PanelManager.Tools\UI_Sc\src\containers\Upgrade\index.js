import { getUpgradeStatus, getUpgradeMessages } from "../../api/upgradeApi";
import UpgradeStatus from "./UpgradeStatus";
import UpgradeWorkFlow from "./UpgradeWorkFlow";
import React, { useState } from "react";
import "./index.css";

export default function UpgradeUI() {
  const [upgradeStatus, setUpgradeStatus] = useState(null);
  const [logText, setLogText] = useState({ lastIndex: 0, message: "" });
  if (upgradeStatus == null) {
    getUpgradeStatus(setUpgradeStatus);
  }

  if (upgradeStatus?.status === 1) {
    setTimeout(() => {
      getUpgradeStatus(setUpgradeStatus);
      getUpgradeMessages(logText.lastIndex, setLogText);
    }, 1000);
  }
  return (
    <div className="upgradeUI-body">
      <div className="upgradeUI-status">
        <UpgradeStatus
          upgradeStatus={upgradeStatus?.status}
          func={{ getUpgradeStatus }}
        />
      </div>
      <div className="upgradeUI-workflow">
        <UpgradeWorkFlow
          upgradeStatus={upgradeStatus?.status}
          currentStatus={JSON.stringify(upgradeStatus?.currentStatus)}
          time={Date.now()}
          logText={logText.message}
        ></UpgradeWorkFlow>
      </div>
    </div>
  );
}
