{"info": {"_postman_id": "7b17bec9-a5e2-4705-939e-dc0511bdffef", "name": "15使用管理员账号进入panel manager资产管理中的资产列表菜单，点击设备拖到到其他层级", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "获取所有资产详情 Copy 8", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "let Areaid2 = pm.response.json().data[0].id//获取Areaid\r", "pm.environment.set('Areaid2',Areaid2)//把id保存到全局变量中\r", "\r", "let Substationid2 = pm.response.json().data[1].id//获取Areaid\r", "pm.environment.set('Substationid2',Substationid2)//把id保存到全局变量中\r", "\r", "let Panelid2 = pm.response.json().data[2].id//获取Areaid\r", "pm.environment.set('Panelid2',Panelid2)//把id保存到全局变量中\r", "\r", "let Circuitid2 = pm.response.json().data[3].id//获取Areaid\r", "pm.environment.set('Circuitid2',Circuitid2)//把id保存到全局变量中\r", "\r", "let Deviceid2 = pm.response.json().data[4].id//获取Areaid\r", "pm.environment.set('Deviceid2',Deviceid2)//把id保存到全局变量中\r", "\r", "pm.test(\"获取所有资产详情\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"id\",\"assetName\",\"assetNumber\",\"level\",\"location\",\"children\",\"type\",\"model\");\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Asset", "host": ["{{baseUrl}}"], "path": ["api", "v1", "<PERSON><PERSON>"]}}, "response": []}, {"name": "资产转移 Copy 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"type\": \"string\",\r\n  \"to\": {{Circuitid2}},\r\n  \"from\": {{Deviceid2}}\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/Asset/move", "host": ["{{baseUrl}}"], "path": ["api", "v1", "<PERSON><PERSON>", "move"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}]}