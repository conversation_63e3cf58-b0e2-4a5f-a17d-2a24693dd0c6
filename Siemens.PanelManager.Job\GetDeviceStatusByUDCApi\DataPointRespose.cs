﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.GetDeviceStatusByUDCApi
{
    internal class DataPointRespose
    {
        /// <summary>
        /// Object Id
        /// </summary>
        [JsonProperty("item_id")]
        public string ObjectId { get; set; } = string.Empty;
        /// <summary>
        /// Object Name
        /// </summary>
        [JsonProperty("item_name")]
        public string ObjectName { get; set; } = string.Empty;
        [JsonProperty("_embedded")]
        public Embedded? Embedded { get; set; }
    }

    public class ItemItem
    {
        [JsonProperty("internal_name")]
        public string InternalName { get; set; } = string.Empty;
        [JsonProperty("id")]
        public int Id { get; set; }
        [JsonProperty("display_name")]
        public string DisplayName { get; set; } = string.Empty;
        [JsonProperty("display_value")]
        public string DisplayValue { get; set; } = string.Empty;
        [JsonProperty("value")]
        public string Value { get; set; } = string.Empty;
        [JsonProperty("unit")]
        public string Unit { get; set; } = string.Empty;
        [JsonProperty("quality")]
        public string Quality { get; set; } = string.Empty;
    }

    public class Embedded
    {
        [JsonProperty("item")]
        public List<ItemItem>? Items { get; set; }
    }
}
