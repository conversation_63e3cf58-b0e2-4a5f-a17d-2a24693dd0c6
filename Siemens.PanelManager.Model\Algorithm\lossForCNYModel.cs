﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.Model.Algorithm
{
    public class lossForCNYModel
    {
        [JsonProperty(PropertyName = "times")]
        public string[] times { get; set; }
        [JsonProperty(PropertyName = "rmb")]
        public decimal[] rmb { get; set; }
        [JsonProperty(PropertyName = "refer")]
        public decimal[] refer { get; set; }
        [JsonProperty(PropertyName = "referLossP")]
        public decimal[] referLossP { get; set; }
        [JsonProperty(PropertyName = "line")]
        public decimal[] line { get; set; }
        [JsonProperty(PropertyName = "lossP")]
        public decimal[] lossP { get; set; }
    }

    /// <summary>
    /// LossDiagnosisKPIDto
    /// </summary>
    public class LossDiagnosisByKpiDto
    {
        [JsonProperty("currentDate")]
        public LossDiagnosisByKpiItemDto Current { get; set; } = new LossDiagnosisByKpiItemDto();
        [JsonProperty("yearDate")]
        public LossDiagnosisByKpiItemDto Year { get; set; } = new LossDiagnosisByKpiItemDto();
        [JsonProperty("monthDate")]
        public LossDiagnosisByKpiItemDto Month { get; set; } = new LossDiagnosisByKpiItemDto();
        [JsonProperty("sum")]
        public LossDiagnosisByKpiItemDto Sum { get; set; } = new LossDiagnosisByKpiItemDto();
    }

    /// <summary>
    /// LossDiagnosisByKpiItemDto
    /// </summary>
    public class LossDiagnosisByKpiItemDto
    {
        [JsonProperty("loss", NullValueHandling = NullValueHandling.Include)]
        public decimal? Loss { get; set; }
        [JsonProperty("loss_unit")]
        public string LossUnit => "KWH";
        [JsonProperty("loss_equivalent_electricity", NullValueHandling = NullValueHandling.Include)]
        public decimal? Electricity { get; set; }
        [JsonProperty("loss_equivalent_electricity_unit")]
        public string ElectricityUnit => "CNY";
        [JsonProperty("loss_pct", NullValueHandling = NullValueHandling.Include)]
        public decimal? Percentage { get; set; }
        [JsonProperty("loss_pct_unit")]
        public string PercentageUnit => "%";
        [JsonProperty("system_efficiency", NullValueHandling = NullValueHandling.Include)]
        public decimal? SystemEfficiency { get; set; }
        [JsonProperty("system_efficiency_unit")]
        public string SystemEfficiencyUnit => "%";
    }
}
