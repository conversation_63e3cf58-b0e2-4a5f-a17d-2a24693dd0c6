﻿using Siemens.PanelManager.Model.UDC;

namespace Siemens.PanelManager.Interface.ActorRefs
{
    public interface IUDCApiRef
    {
        Task<DeviceItemsResult?> GetItems();
        Task<DeviceDataPointsResult?> GetDataPoints(string objectId, string[] internalNames);
        Task<bool> ImportProject(Stream stream);

        Task<UdcMessageModel?> GetMessageByOid(string objectId, int oid);
        Task<UdcMessageModelListResult?> GetBreakerTrips(string objectId, int count = 5, string? oid = null);
        Task<UdcMessageModelListResult?> GetDeviceMessages(string objectId, string? oid = null, int count = 100);
        Task<UdcLicenses?> GetUdcLicensesAsync();
    }
}
