﻿using Akka.Event;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Siemens.InfluxDB.Helper.Client;
using Siemens.PanelManager.Common;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.Algorithm;
using Siemens.PanelManager.Model.Chart;
using Siemens.PanelManager.Model.Config;
using Siemens.PanelManager.Model.Database.Algorithm;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.Topology;
using Siemens.PanelManager.Server.Algorithm;
using Siemens.PanelManager.Server.Asset;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;
using System.ComponentModel;
using System.Diagnostics.CodeAnalysis;
using static ICSharpCode.SharpZipLib.Zip.ZipEntryFactory;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class SystemHealthController : SiemensApiControllerBase
    {
        private IServiceProvider _provider;
        private LossDiagnose _lossDiagnose => _provider.GetRequiredService<LossDiagnose>();
        private ILogger _logger;
        private ISqlSugarClient _client;
        public SystemHealthController(SqlSugarScope client, IServiceProvider provider, SiemensCache cache, ILogger<SystemHealthController> logger)
            : base(provider, cache)
        {
            _client = client;
            _provider = provider;
            _logger = logger;
        }
        [HttpGet("{substationId}/getHealthDataSimple")]
        [SwaggerOperation(Summary = "Swagger_SystemHealth_GetHealthDataSimple", Description = "Swagger_SystemHealth_GetHealthDataSimple_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<GetHealthDataResult>> GetHealthDataSimple(int substationId, string dateTime) 
        {

            if (substationId <= 0 || string.IsNullOrEmpty(dateTime))
            {
                return new ResponseBase<GetHealthDataResult>()
                {
                    Code = 20000,
                    Message = MessageContext.ErrorParam,
                    Data= new GetHealthDataResult()
                };
            }
            DateTime dt;
            bool success = DateTime.TryParse(dateTime, out dt);
            if (!success)
            {
                return new ResponseBase<GetHealthDataResult>()
                {
                    Code = 20000,
                    Message = MessageContext.ErrorParam,
                    Data = new GetHealthDataResult()
                };
            }
            int topoId = 0;
            var assetInfo = await _client.Queryable<AssetInfo>().FirstAsync(a => a.Id == substationId);
            if (assetInfo != null && assetInfo.TopologyId.HasValue)
            {
                topoId = (int)assetInfo.TopologyId;
            }
            else
            {
                return new ResponseBase<GetHealthDataResult>
                {
                    Code = 20000,
                    Message = MessageContext.ErrorParam,
                    Data = new GetHealthDataResult()
                };
            }
            var returnData = new GetHealthDataResult();
            try
            {
               
                var service = _provider.GetRequiredService<AssetDashboardServer>();
                long currentYearStart = new DateTime(DateTime.Now.Year, 1, 1).AddYears(-1).GetTimestampForSec();
                var result = await service.GetHealthData(currentYearStart.ToString(), dt.AddSeconds(1).GetTimestampForSec().ToString(), topoId, _client);
                foreach (var table in result)
                {
                    foreach (var item in table.Records)
                    {
                        returnData.EvaluationResult.Value = Convert.ToDecimal(item.GetValue());
                        returnData.ReportTime = TimeZoneInfo.ConvertTimeFromUtc(item.GetTimeInDateTime().Value, TimeZoneInfo.Local).ToString();
                    }
                }
            }
            catch(Exception ex)
            {
                _logger.LogError(ex.Message, ex);
                return new ResponseBase<GetHealthDataResult>()
                {
                    Code = 20000,
                    Data = new GetHealthDataResult()
                };
            }
            return new ResponseBase<GetHealthDataResult>()
            {
                Code = 20000,
                Data = returnData
            };
        }
        [HttpGet("{assetId}/getHealthData")]
        [SwaggerOperation(Summary = "Swagger_SystemHealth_GetHealthData", Description = "Swagger_SystemHealth_GetHealthData_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<GetHealthDataResult>> GetHealthData(int assetId, string dateTime)
        {
            if (assetId <= 0 || string.IsNullOrEmpty(dateTime))
            {
                return new ResponseBase<GetHealthDataResult>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            int topoId = 0;
            var assetInfo = await _client.Queryable<AssetInfo>().FirstAsync(a => a.Id == assetId);
            if (assetInfo != null && assetInfo.TopologyId.HasValue)
            {
                topoId = (int)assetInfo.TopologyId;
            }
            else
            {
                return new ResponseBase<GetHealthDataResult>
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            Dictionary<string, object> requestBody = new Dictionary<string, object>();
            requestBody.Add("searchTime", dateTime);
            requestBody.Add("topo", topoId);
            requestBody.Add("assetId", assetId);
            var data = new GetHealthDataResult();
            try
            {
                var response = await _lossDiagnose.GetSysLossdataApi(requestBody, "AlgorithmEvaluationApiPath", "search");
                var dataAPI = JsonConvert.DeserializeObject<HealthEvaluationModel>(response);

                if (dataAPI != null)
                {
                    data.CBsResult.Value = dataAPI.CBs;
                    data.CBsResult.Abnormal = dataAPI.CBsMsg;
                    data.BranchCurrentResult.Value = Math.Round(dataAPI.branchCurrent, 2, MidpointRounding.AwayFromZero);
                    data.BranchCurrentResult.Abnormal = dataAPI.branchCurrentMsg;
                    data.TemperatureResult.Value = Math.Round(Convert.ToDecimal(dataAPI.temperature), 2, MidpointRounding.AwayFromZero);
                    data.TemperatureResult.Abnormal = dataAPI.temperatureMsg;
                    data.EvaluationResult.Value = dataAPI.evaluationResult?.result??0;
                    data.BasicsResult.X = dataAPI.historicalResult?.time??[];
                    data.BasicsResult.Y1 = dataAPI.historicalResult?.values??[];
                    Dictionary<string, decimal> resultLines = new Dictionary<string, decimal>();
                    resultLines.Add("excellent", dataAPI.historicalResult?.excellent??0);
                    resultLines.Add("good", dataAPI.historicalResult?.good??0);
                    resultLines.Add("medium", dataAPI.historicalResult?.medium??0);
                    resultLines.Add("bad", dataAPI.historicalResult?.bad??0);
                    data.maxLine_id = dataAPI.maxLine_id;
                    data.BasicsResult.Lines = resultLines;
                    //获取拓扑图
                    var topoplogyInfo = await _client.Queryable<TopologyInfo>().FirstAsync(t => t.Id == topoId);
                    var topoInfo = new JObject();
                    if (topoplogyInfo != null)
                    {
                        topoInfo = JObject.Parse(topoplogyInfo.Data);
                        var nodes = topoInfo.SelectToken("topology.linkDataArray");
                        if (nodes != null)
                        {
                            foreach (var info in nodes)
                            {
                                foreach (var tp in dataAPI.topology)
                                {
                                    bool hasKey = false;
                                    if (info is JObject jobject)
                                    {
                                        hasKey = jobject.ContainsKey("key");
                                    }
                                    if (hasKey && tp.line_id.ToString().Equals(info["key"]?.ToString()??""))
                                    {
                                        info["current"] = tp.current;
                                        info["temperature"] = tp.temperature;
                                        info["temperatureCalculate"] = tp.temperatureCalculate;
                                    }
                                }
                            }
                        }
                    }
                    data.ReportTime = dataAPI.ReportTime;
                    data.topology = topoInfo;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetHealthData 错误");
            }

            return new ResponseBase<GetHealthDataResult>()
            {
                Code = 20000,
                Data = data
            };
        }

        [HttpGet("{substationId}/GetCabinetHealthData")]
        [SwaggerOperation(Summary = "Swagger_SystemHealth_GetCabinetHealthData", Description = "Swagger_SystemHealth_GetCabinetHealthData_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<CurrentPannelHealthResult>> GetCabinetHealthData(int substationId, int cabinetId, string dateTime)
        {
            if (substationId <= 0 || string.IsNullOrEmpty(dateTime) || cabinetId <= 0)
            {
                return new ResponseBase<CurrentPannelHealthResult>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            int topoId = 0;
            var assetInfo = await _client.Queryable<AssetInfo>().FirstAsync(a => a.Id == substationId);
            if (assetInfo != null && assetInfo.TopologyId.HasValue && await _client.Queryable<AssetInfo>().AnyAsync(a => a.Id == cabinetId && a.AssetLevel == AssetLevel.Panel))
            {
                topoId = (int)assetInfo.TopologyId;
            }
            else
            {
                return new ResponseBase<CurrentPannelHealthResult>
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            Dictionary<string, object> requestBody = new Dictionary<string, object>();
            requestBody.Add("searchTime", dateTime);
            requestBody.Add("topo", topoId);
            requestBody.Add("topoLevel", AssetLevel.Panel);
            requestBody.Add("assetId", cabinetId);
            var data = new GetHealthDataResult();
            var panelHealthModels = new List<PanelHealthModel>();
            var panelHealths = await _client.Queryable<PanelHealth>().Where(p => p.AssetId == cabinetId).OrderBy(p => p.Id).ToArrayAsync();
            List<AbnormalIndicatorResult> abnormalIndicator = new List<AbnormalIndicatorResult>();
            Dictionary<string, object> customizeParams = new Dictionary<string, object>();
            foreach (var panelHealth in panelHealths.Where(i=>i.IsSystem==false))
            {
                var param= new Dictionary<string, decimal>();
                var limit = JsonConvert.DeserializeObject<int[]>(panelHealth.Limit);
                if (limit!=null &&limit.Length > 1)
                {
                    param.Add("max", limit[1]);
                    param.Add("min", limit[0]);
                }
                else
                {
                    param.Add("max", 10);
                    param.Add("min", 0);
                }
              
                param.Add("value", panelHealth.Value);
                param.Add("weight", panelHealth.Weight);
                customizeParams.Add(panelHealth.Code, param);
            }
            if (customizeParams != null)
            {
                requestBody.Add("param", customizeParams);
            }
            decimal Score = 0;
            string result = "unknown";
            List<string> sugsstions = new List<string>();
            string reportTime = string.Empty;
            try
            {
                string response = "";
                if (customizeParams.Count>0)
                {
                    response = await _lossDiagnose.GetSysLossdataApi(requestBody, "AlgorithmEvaluationApiPath", "customize");
                }
                else
                {
                    response = await _lossDiagnose.GetSysLossdataApi(requestBody, "AlgorithmEvaluationApiPath", "search");
                }
                
                var dataAPI = JsonConvert.DeserializeObject<HealthEvaluationModel>(response);
                var sugInfo = await _client.Queryable<HealthSuggestion>().ToListAsync();
                if (dataAPI != null)
                {
                    reportTime = dataAPI.ReportTime;
                    foreach (var sug in sugInfo)
                    {
                        if (!string.IsNullOrEmpty(dataAPI.CBsMsg) && sug.indicatorName == "CBs")
                        {
                            sugsstions.Add(sug.suggestion);
                        }
                        if (!string.IsNullOrEmpty(dataAPI.temperatureMsg) && sug.indicatorName == "temperature")
                        {
                            sugsstions.Add(sug.suggestion);
                        }
                        if (!string.IsNullOrEmpty(dataAPI.branchCurrentMsg) && sug.indicatorName == "branchCurrent")
                        {
                            sugsstions.Add(sug.suggestion);
                        }
                        if (!string.IsNullOrEmpty(dataAPI.alarmsPerWeekMsg) && sug.indicatorName == "alarmPerWeek")
                        {
                            sugsstions.Add(sug.suggestion);
                        }
                    }
                    if (panelHealths != null)
                    {
                        foreach (var panelHealth in panelHealths)
                        {
                            if (panelHealth == null) continue;
                            int? limitOpt = null;
                            if (panelHealth.Limit != null)
                            {
                                switch (panelHealth.Limit.Replace(" ", ""))
                                {
                                    case "[1,10]":
                                        limitOpt = 0;
                                        break;
                                    case "[1,100]":
                                        limitOpt = 1;
                                        break;
                                    default: break;
                                }
                            }
                            var name = MessageContext.GetOverviewValue(panelHealth.Code ?? string.Empty);
                            if (string.IsNullOrEmpty(name)|| name== panelHealth.Code)
                            {
                                name = panelHealth.Name;
                            }
                            var model = new PanelHealthModel()
                            {
                                Id = panelHealth.Id,
                                IsSystem = panelHealth.IsSystem,
                                Code = panelHealth.Code ?? string.Empty,
                                LimitOpt = limitOpt,
                                Message = string.Empty,
                                Name = name,
                                Score = string.Empty,
                                Value = panelHealth.Value,
                                Weight = panelHealth.Weight,
                                Status = MessageContext.GetString("PanelHealth_normal"),
                                Limit = JsonConvert.DeserializeObject<int[]>(panelHealth.Limit)
                            };
                           
                            switch (panelHealth.Code)
                            {
                                case "MaxElectricity":
                                    model.Value = Math.Round(dataAPI.branchCurrent, 2, MidpointRounding.AwayFromZero);
                                    model.Message = dataAPI.branchCurrentMsg;
                                    model.Name = "本柜最高电流测点(A)";
                                    break;
                                case "RemainingLife":
                                    model.Value = dataAPI.CBs;
                                    model.Message = dataAPI.CBsMsg;
                                    model.Name = "本柜断路器剩余寿命(%)";
                                    break;
                                case "MaxTemperature":
                                    model.Value = Math.Round(Convert.ToDecimal(dataAPI.temperature), 2, MidpointRounding.AwayFromZero);
                                    model.Message = dataAPI.temperatureMsg;
                                    model.Name = "本柜最高温度测点(℃)";
                                    break;
                                //case "AlarmCount":
                                //    model.Value = (dataAPI.alarmPerWeek.medium + dataAPI.alarmPerWeek.low + dataAPI.alarmPerWeek.high);
                                //    model.Message = dataAPI.alarmsPerWeekMsg;
                                //    break;
                             
                                //case "HighLevelAlarmCount":
                                //    model.Value = dataAPI.alarmPerWeek.high;
                                //    if (model.Value > 0) model.Message = dataAPI.alarmsPerWeekMsg;
                                //    //model.Message = dataAPI.alarmsPerWeekMsg;
                                //    break;
                                //case "MiddleLevelAlarmCount":
                                //    model.Value = dataAPI.alarmPerWeek.medium;
                                //    if (model.Value > 0 && model.Value > 0 && !string.IsNullOrEmpty(dataAPI.alarmsPerWeekMsg)) model.Message = dataAPI.alarmsPerWeekMsg;
                                //    //model.Message = dataAPI.alarmsPerWeekMsg;
                                //    break;
                                //case "LowLevelAlarmCount":
                                //    model.Value = dataAPI.alarmPerWeek.low;
                                //    //model.Message = dataAPI.alarmsPerWeekMsg;
                                //    break;
                            }
                            if (dataAPI.IndicatorResult!=null && dataAPI.IndicatorResult.ContainsKey(panelHealth.Code))
                            {
                                //model.Message = dataAPI.IndicatorResult[panelHealth.Code].msg;
                                //model.Status = Enum.GetName(typeof(EvaluationType), Convert.ToInt32(dataAPI.IndicatorResult[panelHealth.Code].result)) ?? string.Empty;
                                int indicatorResult = Convert.ToInt32(dataAPI.IndicatorResult[panelHealth.Code].result);
                                switch (indicatorResult)
                                {
                                     case 1:
                                        model.Status = MessageContext.GetString("PanelHealth_abnormal");
                                        model.Message = MessageContext.GetString("PanelHealth_abnormalMsg");
                                        break;
                                     default:
                                        model.Status = MessageContext.GetString("PanelHealth_normal");
                                        break;
                                }
                            }
                            if (!string.IsNullOrEmpty(model.Message))
                            {
                                model.Status= MessageContext.GetString("PanelHealth_abnormal");
                                model.Message = MessageContext.GetString("PanelHealth_abnormalMsg");
                                AbnormalIndicatorResult abnormal = new AbnormalIndicatorResult
                                {
                                    Name = model.Name,
                                    Percentage = Convert.ToDecimal(model.Value)
                                };
                                abnormalIndicator.Add(abnormal);
                            }
                            if (panelHealth.IsSystem)
                            {
                                if (!string.IsNullOrEmpty(panelHealth.ParentCode))
                                {
                                    var panertPanelHealth = panelHealths.FirstOrDefault(p => p.Code == panelHealth.ParentCode);
                                    if (panertPanelHealth != null)
                                    {
                                        var panelHealthModel = panelHealthModels.FirstOrDefault(pm => pm.Id == panertPanelHealth.Id);
                                        if (panelHealthModel != null)
                                        {
                                            if (panelHealthModel.Children == null)
                                            {
                                                panelHealthModel.Children = new List<PanelHealthModel>();
                                            }

                                            panelHealthModel.Children.Add(model);
                                            continue;
                                        }
                                    }
                                }
                                else
                                {
                                    var children = panelHealths.Where(p => p.ParentCode == panelHealth.Code).ToArray();
                                    if (children != null && children.Length > 0)
                                    {
                                        model.Children = new List<PanelHealthModel>();
                                        var models = panelHealthModels.Where(pm => children.Any(c => c.Id == pm.Id)).ToArray();
                                        if (models != null && models.Length > 0)
                                        {
                                            foreach (var m in models)
                                            {
                                                if (m == null) continue;
                                                model.Children.Add(m);
                                            }
                                        }
                                    }
                                }
                            }
                            var alarmList =new List<string>();
                            alarmList.Add("LowLevelAlarmCount");
                            alarmList.Add("MiddleLevelAlarmCount");
                            alarmList.Add("HighLevelAlarmCount");
                            alarmList.Add("AlarmCount");
                            if (!alarmList.Contains(model.Code))
                            {
                                panelHealthModels.Add(model);
                            }
                            
                        }
                    }

                    Score = Math.Round(dataAPI.score, 2, MidpointRounding.AwayFromZero);
                    int reValue = Convert.ToInt32(dataAPI.evaluationResult.result);
                    result = Enum.GetName(typeof(EvaluationType), reValue) ?? string.Empty;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetCabinetHealthData 错误");
            }

            return new ResponseBase<CurrentPannelHealthResult>()
            {
                Code = 20000,
                Data = new CurrentPannelHealthResult()
                {
                    Score = Score,
                    ReportTime = reportTime,
                    Grade = MessageContext.GetPanelHealthValue(result),
                    Count = abnormalIndicator.Count,
                    AbnormalIndicators = abnormalIndicator.ToArray(),
                    IntercatorData = panelHealthModels.ToArray(),
                    Sugsstions = sugsstions
                }
            };
        }

        [HttpGet("{assetId}/getHealthDataChart")]
        [SwaggerOperation(Summary = "Swagger_SystemHealth_GetHealthChart", Description = "Swagger_SystemHealth_GetHealthChart_Desc")]
        [Authorize(policy: Permission.Default)]
        public ResponseBase<GetHealthChartResult> GetHealthDataChart(int assetId, [AllowNull] long? searchTime)
        {
            if (assetId <= 0 || (searchTime.HasValue && searchTime.Value < 0))
            {
                return new ResponseBase<GetHealthChartResult>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }

            var time = DateTime.Now;
            if (searchTime.HasValue && searchTime.Value > 0)
            {
                time = searchTime.Value.GetDateTimeBySec();
            }

            if (time > DateTime.Now)
            {
                return new ResponseBase<GetHealthChartResult>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }

            #region mock 数据（测试）
            var minute = time.Minute;
            var remaind = minute % 15;
            if (remaind > 0)
            {
                time = time.AddMinutes(-remaind);
            }

            var dataList = new decimal[90] { 9.23m, 9.03m, 8.73m, 8.94m, 8.73m, 8.7m, 8.9m, 9.08m, 9.28m, 9.46m, 9.57m, 9.70m, 9.50m, 9.63m, 9.40m, 9.63m, 9.74m, 9.51m, 9.34m, 9.21m, 9.01m, 8.90m, 8.7m, 8.9m, 9.11m, 9.34m, 9.64m, 9.82m, 9.62m, 9.51m, 9.40m, 9.51m, 9.68m, 9.55m, 9.65m, 9.76m, 9.58m, 9.40m, 9.70m, 9.49m, 9.19m, 8.99m, 8.86m, 8.99m, 9.22m, 8.99m, 9.10m, 9.40m, 9.17m, 9.06m, 9.23m, 9.06m, 8.96m, 8.7m, 8.8m, 8.9m, 9.03m, 9.21m, 9.08m, 8.88m, 8.78m, 8.96m, 8.79m, 8.99m, 8.89m, 9.07m, 9.20m, 8.97m, 8.74m, 8.84m, 9.04m, 8.91m, 8.73m, 8.96m, 8.78m, 8.7m, 8.93m, 9.06m, 9.29m, 9.52m, 9.69m, 9.80m, 9.70m, 9.90m, 10m, 9.8m, 9.69m, 9.48m, 9.35m, 9.53m, };

            var lineList = new Dictionary<string, decimal>()
            {
                ["excellent"] = 8m,
                ["good"] = 6m,
                ["medium"] = 4m
            };

            var basics = new LineChartModel();
            basics.X = new string[dataList.Length];
            basics.Y1 = dataList;
            basics.Lines = lineList;
            //var custom = new LineChartModel();
            //custom.X = new string[dataList.Length];
            //custom.Y1 = data2List;
            //custom.Lines = lineList;
            for (var i = 0; i < dataList.Length; i++)
            {
                var tempTime = time.AddMinutes(-i * 15);
                var timeStr = tempTime.ToString("yyyy-MM-dd HH:mm");
                basics.X[dataList.Length - i - 1] = timeStr;
                //custom.X[dataList.Length - i - 1] = timeStr;
            }

            var result = new GetHealthChartResult(basics, null);
            #endregion

            return new ResponseBase<GetHealthChartResult>()
            {
                Code = 20000,
                Data = result
            };
        }

        [HttpPost("{assetId}/recheck")]
        [SwaggerOperation(Summary = "Swagger_SystemHealth_Recheck", Description = "Swagger_SystemHealth_Recheck_Desc")]
        [Authorize(policy: Permission.Default)]
        public ResponseBase<string> Recheck(int assetId, SystemHealthRecheckParam param)
        {
            // TODO
            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = Guid.NewGuid().ToString(),
            };
        }

        [HttpPost("ExportHealthResult")]
        [SwaggerOperation(Summary = "Swagger_SystemHealth_ExportHealthResult", Description = "Swagger_SystemHealth_ExportHealthResult_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> ExportHealthResult(int assetId=0)
        {
            Stream data= null;
            if (assetId > 0)
            {
                Dictionary<string, object> requestBody = new Dictionary<string, object>();
                requestBody.Add("assetId", assetId);
                data =await _lossDiagnose.ReportExport(requestBody, "report_cabinet_export");
            }
            else
            {
                data =await _lossDiagnose.ReportExport(null,"report_export");
            }
             
            // TODO
            return File(data, "application/octet-stream", fileDownloadName: "evaluationResult.xlsx", enableRangeProcessing: true);
        }

        [HttpPost("{assetId}/getCustomizeHealthData")]
        [SwaggerOperation(Summary = "Swagger_SystemHealth_getCustomizeHealthData", Description = "Swagger_SystemHealth_getCustomizeHealthData_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<CustomizeHealthResult>> GetCustomizeHealthData(int assetId, HealthEvaluationQueryParams queryParams)
        {
            if (assetId <= 0 || string.IsNullOrEmpty(queryParams.searchTime))
            {
                return new ResponseBase<CustomizeHealthResult>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            var assetInfo = await _client.Queryable<AssetInfo>().FirstAsync(a => a.Id == assetId);
            if (assetInfo != null && assetInfo.TopologyId.HasValue)
            {
                queryParams.topo = assetInfo.TopologyId.Value.ToString();
            }
            else
            {
                return new ResponseBase<CustomizeHealthResult>
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            string body = JsonConvert.SerializeObject(queryParams);
            var curData = new Indicators();
            foreach (System.Reflection.PropertyInfo r in queryParams.param.GetType().GetProperties())
            {
                var d = r.GetValue(queryParams.param, null);
                string str = JsonConvert.SerializeObject(d);
                curData = JsonConvert.DeserializeObject<Indicators>(str);
                var db = await _client.Queryable<PanelHealth>()
                .Where(p => p.AssetId == assetId && p.Code == r.Name)
                .OrderBy(p => p.Id)
                .FirstAsync();
                if (db==null)
                {
                    if (curData.isEnable)
                    {
                        var id = await _client.Insertable<PanelHealth>(new PanelHealth()
                        {
                            AssetId = assetId,
                            Code = r.Name,
                            Name = string.Empty,
                            Limit = JsonConvert.SerializeObject(curData.Limit),
                            Weight = curData.weight,
                            Value = curData.value,
                            CreatedBy = UserName,
                            CreatedTime = DateTime.Now,
                            UpdatedBy = UserName,
                            UpdatedTime = DateTime.Now,
                        }).ExecuteReturnIdentityAsync();
                    }
                    
                }
                else
                {
                    if(curData.isEnable)
                    {
                        db.Weight = curData.weight;
                        db.Value = curData.value;
                        db.Limit = JsonConvert.SerializeObject(curData.Limit);
                        db.UpdatedTime = DateTime.Now;
                        db.UpdatedBy = UserName;
                        await _client.Updateable(db).ExecuteCommandAsync();
                    }
                    else
                    {
                        await _client.Deleteable(db).ExecuteCommandAsync();
                    }
                    
                }
                
            }
            CustomizeHealthResult data = new CustomizeHealthResult();
            try
            {
                var response = await _lossDiagnose.GetCustomizHealthApi(body, "customize");
                var dataAPI = JsonConvert.DeserializeObject<CustomizeHealthEvaluationModel>(response);
                if (dataAPI != null)
                {
                    data.evaluationResult = dataAPI.evaluationResult;
                    data.familyMsg = dataAPI.familyMsg;
                    data.enviromentMsg = dataAPI.enviromentMsg;
                    data.cabinetMsg = dataAPI.cabinetMsg;
                    data.meterMsg = dataAPI.meterMsg;
                    data.lampMsg = dataAPI.lampMsg;
                    data.isolatedMsg = dataAPI.isolatedMsg;
                    data.protectionMsg = dataAPI.protectionMsg;
                    data.unwindingMethodMsg = dataAPI.unwindingMethodMsg;
                    data.modelSelectionMsg = dataAPI.modelSelectionMsg;
                    data.transformerConfigurationMsg = dataAPI.transformerConfigurationMsg;
                    data.airSwitchBreakingCapacityMsg = dataAPI.airSwitchBreakingCapacityMsg;
                    data.cabinetAppearanceMsg = dataAPI.cabinetAppearanceMsg;
                    data.cabinetConnectionMsg = dataAPI.cabinetConnectionMsg;
                    data.cabinetGroundingMsg = dataAPI.cabinetGroundingMsg;
                    data.cabinetLockingMsg = dataAPI.cabinetLockingMsg;
                    data.airSwitchAppearanceMsg = dataAPI.airSwitchAppearanceMsg;
                    data.airSwitchOperationMsg = dataAPI.airSwitchOperationMsg;
                    data.airSwitchPositionMsg = dataAPI.airSwitchPositionMsg;
                    data.contactorSuctionConditionMsg = dataAPI.contactorSuctionConditionMsg;
                    data.contactorInsulationComponentsMsg = dataAPI.contactorInsulationComponentsMsg;
                    data.contactorAuxiliaryContactsMsg = dataAPI.contactorAuxiliaryContactsMsg;
                    data.contactorExtinguishingCoverMsg = dataAPI.contactorExtinguishingCoverMsg;
                    data.contactorClosingCoilMsg = dataAPI.contactorClosingCoilMsg;
                    data.lightningArresterAppearanceMsg = dataAPI.lightningArresterAppearanceMsg;
                    data.voiceMsg = dataAPI.voiceMsg;
                    data.operationTestMsg = dataAPI.operationTestMsg;
                    data.infraredTestMsg = dataAPI.infraredTestMsg;
                    data.contactorActionTestMsg = dataAPI.contactorActionTestMsg;
                    data.lightningArresterTestMsg = dataAPI.lightningArresterTestMsg;
                    data.CustomResult.X = dataAPI.historicalResult.time;
                    data.CustomResult.Y1 = dataAPI.historicalResult.values;
                    Dictionary<string, decimal> resultLines = new Dictionary<string, decimal>();
                    resultLines.Add("excellent", dataAPI.historicalResult.excellent);
                    resultLines.Add("good", dataAPI.historicalResult.good);
                    resultLines.Add("medium", dataAPI.historicalResult.medium);
                    resultLines.Add("bad", dataAPI.historicalResult.bad);
                    data.CustomResult.Lines = resultLines;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetCustomizeHealthData 错误");
            }


            return new ResponseBase<CustomizeHealthResult>()
            {
                Code = 20000,
                Data = data
            };
        }

        [HttpGet("{assetId}/CabinetHealthStatistics")]
        [SwaggerOperation(Summary = "Swagger_SystemHealth_CabinetHealthStatistics", Description = "Swagger_SystemHealth_CabinetHealthStatistics_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<PanelHealthStatisticsResult>> CabinetHealthStatistics(int assetId, string dateTime)
        {
            if (string.IsNullOrEmpty(dateTime))
            {
                return new ResponseBase<PanelHealthStatisticsResult>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            int topoId = 0;
            if (assetId != 0) {
                var assetInfo = await _client.Queryable<AssetInfo>().FirstAsync(a => a.Id == assetId);
                if (assetInfo != null && assetInfo.TopologyId.HasValue)
                {
                    topoId = (int)assetInfo.TopologyId;
                }
                else
                {
                    return new ResponseBase<PanelHealthStatisticsResult>
                    {
                        Code = 40301,
                        Message = MessageContext.ErrorParam
                    };
                }
            }
           
            PanelHealthStatisticsResult returnResult = new PanelHealthStatisticsResult();
            Dictionary<int, string> toBeRepairedList = new Dictionary<int, string>();
            Dictionary<string, object> requestBody = new Dictionary<string, object>();
            requestBody.Add("searchTime", dateTime);
            requestBody.Add("topo", topoId);
            var dataAPI = new CabinetHealthStatistics();
            PieChartModel result = new PieChartModel
            {
                Summary = new PieInfo[0]
            };
            try
            {
                var response = await _lossDiagnose.GetSysLossdataApi(requestBody, "AlgorithmEvaluationApiPath", "searchCabinet");
                dataAPI = JsonConvert.DeserializeObject<CabinetHealthStatistics>(response);
                if (dataAPI != null)
                {
                    var assetInfos = await _client.Queryable<AssetInfo>().Where(a => dataAPI.badAssetList.Contains(a.Id)).ToListAsync();
                    foreach (var info in assetInfos)
                    {
                        toBeRepairedList.Add(info.Id, info.AssetName);
                    }
                    PieInfo pie1 = new PieInfo
                    {
                        Code = "excellent",
                        Name = MessageContext.GetString("PanelHealth_excellent"),
                        Value = dataAPI.excellent,
                        Percentage = Math.Round(dataAPI.excellent / dataAPI.total, 2, MidpointRounding.AwayFromZero)
                    };
                    PieInfo pie2 = new PieInfo
                    {
                        Code = "good",
                        Name = MessageContext.GetString("PanelHealth_good"),
                        Value = dataAPI.good,
                        Percentage = Math.Round(dataAPI.good / dataAPI.total, 2, MidpointRounding.AwayFromZero)
                    };
                    PieInfo pie3 = new PieInfo
                    {
                        Code = "medium",
                        Name = MessageContext.GetString("PanelHealth_medium"),
                        Value = dataAPI.medium,
                        Percentage = Math.Round(dataAPI.medium / dataAPI.total, 2, MidpointRounding.AwayFromZero)
                    };
                    PieInfo pie4 = new PieInfo
                    {
                        Code = "bad",
                        Name = MessageContext.GetString("PanelHealth_bad"),
                        Value = dataAPI.bad,
                        Percentage = Math.Round(dataAPI.bad / dataAPI.total, 2, MidpointRounding.AwayFromZero)
                    };
                    result.Summary = new PieInfo[] { pie1, pie2, pie3, pie4 };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "CabinetHealthStatistics 错误");
            }

            returnResult.chartModel = result;
            returnResult.repairedList = toBeRepairedList;

            return new ResponseBase<PanelHealthStatisticsResult>()
            {
                Code = 20000,
                Data = returnResult
            };
        }

        [HttpGet("check")]
        [SwaggerOperation(Summary = "Swagger_SystemHealth_Check", Description = "Swagger_SystemHealth_Check_Desc")]
        public async Task<ResponseBase<bool>> CheckSystemHealth([AllowNull] string? opt)
        {
            var isOk = true;
            opt ??= string.Empty;
            switch (opt.ToLower())
            {
                case "completeness-check":
                    {
                        using var sqlClient = _provider.GetService<ISqlSugarClient>();
                        if (sqlClient == null)
                        {
                            isOk = false;
                        }

                        using var influxDbClient = _provider.GetService<InfluxDBClient>();
                        if (influxDbClient == null)
                        {
                            isOk = false;
                        }
                        else
                        {
                            var bucketId = await influxDbClient.GetBucketId();
                            if (string.IsNullOrEmpty(bucketId))
                            {
                                isOk = false;
                            }
                        }

                        var refObj = _provider.GetService<IUDCApiRef>();
                        var licenses = await refObj!.GetUdcLicensesAsync();
                        if (licenses == null)
                        {
                            isOk = false;
                        }
                    }
                    break;
                default: break;
            }

            return new ResponseBase<bool>
            {
                Code = 20000,
                Data = isOk
            };
        }
    }
    public enum EvaluationType
    {
        [Description("优")]
        excellent = 4,
        [Description("良")]
        good = 3,
        [Description("中")]
        medium = 2,
        [Description("差")]
        bad = 1
    }
}