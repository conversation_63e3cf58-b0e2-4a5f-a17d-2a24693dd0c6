﻿using Microsoft.AspNetCore.SignalR;
using Newtonsoft.Json;
using Siemens.PanelManager.Monitor.Function;
using Siemens.PanelManager.Monitor.Logger;
using Siemens.PanelManager.Monitor.StaticData;
using System.IO;
using System.IO.Compression;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.Monitor.Workers
{
    public class UpgradeWorker : BackgroundService
    {
        private ILogger<UpgradeWorker> _logger;
        private UpgradeWorkerStatus _status;
        private bool _isRunning = false;
        public UpgradeWorker(ILogger<UpgradeWorker> logger, UpgradeWorkerStatus status)
        {
            _logger = logger;
            _status = status;
        }

        public override Task StartAsync(CancellationToken cancellationToken)
        {
            if (!_isRunning)
            {
                _isRunning = true;
                return base.StartAsync(cancellationToken);
            }
            return Task.CompletedTask;
        }

        public override Task StopAsync(CancellationToken cancellationToken)
        {
            _isRunning = false;
            return base.StopAsync(cancellationToken);
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            await Task.Delay(3000);
            try
            {
                await Working();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "UpgradeWorker failed");
            }
            finally
            {
                _status.Finish();
                _isRunning = false;
            }
        }


        private async Task Working()
        {
            if (_status.Status > 0 && _status.Proxy != null)
            {
                using (var customLogger = new CustomLogger(_status.Proxy, _logger, "Upgrade"))
                {
                    customLogger.LogInformation("upload file [Step-1][Begin]");
                    if (_status.AutoReset.WaitOne(TimeSpan.FromMinutes(30)))
                    {
                        var path = _status.UploadFilePath;
                        var fileName = _status.FileName;
                        if (string.IsNullOrEmpty(path) || string.IsNullOrEmpty(fileName))
                        {
                            customLogger.LogInformation("upload file [Step-1][Fail]");
                            await _status.Proxy.SendAsync("FinishUpgrade", false);
                            _status.Finish();
                            return;
                        }
                        customLogger.LogInformation("upload file [Step-1][Finish]");
                        try
                        {
                            var result = await UpgradePackageHandle(path, fileName, customLogger, 0, new Dictionary<string, string>());
                            if (result)
                            {
                                var monitorModel = await MonitorFunc.GetMonitorModel(customLogger);
                                await _status.Proxy.SendAsync("MonitorInfo", monitorModel);
                            }
                        }
                        catch (Exception ex)
                        {
                            customLogger.LogError(ex, "Upgrade failed");
                        }

                        #region 7 清除临时文件
                        var basePath = AppContext.BaseDirectory;
                        var tempFolder = Path.Combine(basePath, UpgradePackageMananger.TempFolderName, fileName);
                        customLogger.LogInformation("Remove temp file [Step-7][Begin]");
                        File.Delete(path);
                        Directory.Delete(tempFolder, true);
                        customLogger.LogInformation("Remove temp file [Step-7][Finish]");
                        #endregion

                        await _status.Proxy.SendAsync("FinishUpgrade", true);
                        _status.Finish();
                        return;
                    }
                    else
                    {
                        customLogger.Log(LogLevel.Information, new EventId(1), "未收到上传文件", null, (message, ex) => message);
                        await _status.Proxy.SendAsync("FinishUpgrade", false);
                        _status.Finish();
                        return;
                    }
                }
            }
            else if (_status.Status == 10 && _status.Proxy == null)
            {
                var path = _status.UploadFilePath;
                var fileName = _status.FileName;
                var result = await UpgradePackageHandle(path, fileName, _logger, 1, _status.Params);

                #region 7 清除临时文件
                var basePath = AppContext.BaseDirectory;
                var tempFolder = Path.Combine(basePath, UpgradePackageMananger.TempFolderName, fileName);
                _logger.LogInformation("Remove temp file [Step-7][Begin]");
                Directory.Delete(tempFolder, true);
                _logger.LogInformation("Remove temp file [Step-7][Finish]");
                #endregion

                _status.Finish();
            }
        }

        /// <summary>
        /// 1. 将文件保存至临时文件
        /// 2. 验证文件
        /// 3. 清理将保存的文件夹
        /// 4. 清除过去的docker image
        /// 5. 将升级包拷贝至 文件夹
        /// 6. 启动 服务（暂时先不考虑失败回滚）
        /// 7. 清除临时文件
        /// </summary>
        /// <param name="fileStream"></param>
        /// <returns></returns>
        private async Task<bool> UpgradePackageHandle(string tempFile, string fileName, ILogger logger, int step, Dictionary<string, string> dataParams)
        {
            var fileInfos = new List<FileInfo>();
            var pathParams = new Dictionary<string, string>();

            if (step == 0)
            {
                var isOk = await UpgradePackageMananger.CheckZip(tempFile, fileName, logger, fileInfos, pathParams);

                if (!isOk)
                {
                    return false;
                }
            }
            else
            {
                foreach (var kv in dataParams)
                {
                    switch(kv.Key) 
                    {
                        case "tarFileInfo": pathParams.TryAdd(kv.Key,kv.Value); continue;
                        case "dockerMappingFile": pathParams.TryAdd(kv.Key, kv.Value); continue;
                        default:break;
                    }

                    fileInfos.Add(new FileInfo(kv.Value));
                }
            }

            #region 3 清除将保存的文件夹
            logger.LogInformation("clear folder [Step-3][Begin]");
            var oldYamlFile = UpgradePackageMananger.CurrentlyYamlFile;
            var newFolder = UpgradePackageMananger.InitUpgradePath();
            logger.LogInformation("clear folder [Step-3][Finish]");
            #endregion

            #region 4 清除过去的docker image
            logger.LogInformation("remove docker images [Step-4][Begin]");
            await DockerFunc.RemoveImages(logger);
            logger.LogInformation("remove docker images [Step-4][Finish]");
            #endregion

            #region 5 将升级包拷贝至文件夹
            logger.LogInformation("copy file to new folder [Step-5][Begin]");
            var ymlFilePath = string.Empty;
            foreach(var file in fileInfos) 
            {
                var thisFileName = file.Name.ToLower();
                if (Regex.IsMatch(thisFileName, "[\\w|\\W]+\\.tar$"))
                {
                    File.Copy(file.FullName, Path.Combine(newFolder, file.Name), true);
                }
                else if (Regex.IsMatch(thisFileName, "[\\w|\\W]+\\.(yml|yaml)$"))
                {
                    ymlFilePath = Path.Combine(newFolder, file.Name);
                    File.Copy(file.FullName, ymlFilePath, true);
                }
                else if (Regex.IsMatch(thisFileName, "[\\w|\\W]+\\.txt$"))
                {
                    File.Copy(file.FullName, Path.Combine(newFolder, file.Name), true);
                }
            }
            logger.LogInformation("copy file to new folder [Step-5][Finish]");
            #endregion

            #region 6 启动 服务（暂时先不考虑失败回滚）
            logger.LogInformation("start dockers [Step-6][Begin]");
            Dictionary<string, string>? dockerMapping = null;
            if (pathParams.ContainsKey("dockerMappingFile"))
            {
                dockerMapping = JsonConvert.DeserializeObject<Dictionary<string, string>>(File.ReadAllText(pathParams["dockerMappingFile"]));
            }
            await DockerFunc.Upgrade(pathParams["tarFileInfo"], dockerMapping, ymlFilePath, oldYamlFile, logger);
            logger.LogInformation("start dockers [Step-6][Finish]");
            #endregion

            return true;
        }
    }
}
