<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Folder Include="UpgradePackage\Base\" />
    <Folder Include="UpgradePackage\Part1\" />
    <Folder Include="UpgradePackage\Part2\" />
    <Folder Include="wwwroot\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CliWrap" Version="3.6.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Log4Net.AspNetCore" Version="6.1.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Siemens.PanelManager.HubModel\Siemens.PanelManager.HubModel.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="log4net.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <None Update="PanelManagerMonitor.service">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="UpgradePackage\Base\docker-compos.yml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="UpgradePackage\Base\t.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="UpgradePackage\Base\test2.tar">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
