﻿using Akka.Util.Internal;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.IO;
using Siemens.PanelManager.Common.Model;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.Model.MeterRead;
using Siemens.PanelManager.Server.Asset;
using Siemens.PanelManager.Server.MeterRead;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;
using System.Data;

namespace Siemens.PanelManager.WebApi.Controllers
{
    /// <summary>
    /// 抄表后端服务
    /// </summary>
    [Route("api/v1/[controller]/[action]")]
    [ApiController]
    public class MeterReadController : SiemensApiControllerBase, IDisposable
    {
        private ISqlSugarClient? _dbClient;

        private ISqlSugarClient _db 
        {
            get
            {
                if (_dbClient == null)
                {
                    _dbClient = _provider.GetRequiredService<ISqlSugarClient>();
                }
                return _dbClient;
            }
        }

        private readonly IServiceProvider _provider;

        private readonly SiemensCache _cache;

        /// <summary>
        /// 构造函数初始化
        /// </summary>
        /// <param name="provider"></param>
        /// <param name="cache"></param>
        public MeterReadController(IServiceProvider provider, SiemensCache cache) : base(provider, cache)
        {
            _provider = provider;
            _cache = cache;
        }

        /// <summary>
        /// 添加缓存属性
        /// </summary>
        /// <param name="attributes"></param>
        /// <returns></returns>
        [HttpPost]
        [SwaggerOperation(Summary = "Swagger_MeterRead_AddAttributes", Description = "Swagger_MeterRead_AddAttributes_Desc")]
        [Authorize(policy: Permission.Default)]
        public IActionResult AddAttributes([FromBody] List<string> attributes)
        {
            ResponseBase<string> result;

            try
            {
                _cache.Set<List<string>>("MeterReadAddAttributes", attributes);

                result = new ResponseBase<string>() { Code = 20000, Message = MessageContext.Success };
            }
            catch (Exception ex)
            {
                result = new ResponseBase<string>() { Code = 50000, Message = ex.Message};
            }

            return Ok(result);
        }

        /// <summary>
        /// 获取缓存属性
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [SwaggerOperation(Summary = "Swagger_MeterRead_GetAttributes", Description = "Swagger_MeterRead_GetAttributes_Desc")]
        [Authorize(policy: Permission.Default)]
        public IActionResult GetAttributes()
        {
            ResponseBase<List<string>> result;

            try
            {
                var attributes = _cache.Get<List<string>>("MeterReadAddAttributes");

                result = new ResponseBase<List<string>>() { Code = 20000, Data = attributes };
            }
            catch (Exception ex)
            {
                result = new ResponseBase<List<string>>() { Code = 50000, Message = ex.Message };
            }

            return Ok(result);
        }

        /// <summary>
        /// 获取抄表属性
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [SwaggerOperation(Summary = "Swagger_MeterRead_GetAllAttributes", Description = "Swagger_MeterRead_GetAllAttributes_Desc")]
        [Authorize(policy: Permission.Default)]
        public IActionResult GetAllAttributes()
        {
            ResponseBase<List<string>> result;

            try
            {
                var propertyList = UniversalDeviceInfo.Instance._dicProperty.Keys.ToList();

                result = new ResponseBase<List<string>>() { Code = 20000, Data = propertyList };
            }
            catch (Exception ex)
            {
                result = new ResponseBase<List<string>>() { Code = 50000, Message = ex.Message };
            }

            return Ok(result);
        }

        /// <summary>
        /// 获取所有的配电房
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [SwaggerOperation(Summary = "Swagger_MeterRead_GetAllSubstation", Description = "Swagger_MeterRead_GetAllSubstation_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> GetAllSubstation()
        {
            ResponseBase<dynamic> result;

            try
            {
                var substations = await _db.Queryable<AssetInfo>()
                    .Where(p => p.AssetLevel == AssetLevel.Substation)
                    .Select(p => new
                    {
                        Key = p.Id,
                        Val = p.AssetName
                    }).ToListAsync();

                substations.Add(new
                {
                    Key = -1,
                    Val = "所有回路",
                });

                result = new ResponseBase<dynamic>() { Code = 20000, Data = substations.OrderBy(p => p.Key).ToList() };
            }
            catch (Exception ex)
            {
                result = new ResponseBase<dynamic>() { Code = 50000, Message = ex.Message };
            }

            return Ok(result);
        }

        /// <summary>
        /// 获取抄表表格数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [SwaggerOperation(Summary = "Swagger_MeterRead_GetTabChartData", Description = "Swagger_MeterRead_GetTabChartData_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> GetTabChartData([FromQuery] MeterReadParam input)
        {
            ResponseBase<MeterReadResult> result;

            try
            {
                // 定义全局所有返回值
                var meterRead = new MeterReadResult();

                // 资产数据为空
                if (!input.PropertyList.Any())
                {
                    return Ok(new ResponseBase<List<MeterReadResult>>()
                    {
                        Code = 50000,
                        Message = MessageContext.GetString("MeterRead_Err_AttributeByEmpty")
                    });
                }

                var meterReadHelper = _provider.GetService<MeterReadServer>();

                // 获取资产设备信息
                var assetInfos = await meterReadHelper!.GetAssetInfoList(input);

                // 不存在回路的设备
                if (!assetInfos.Any())
                {
                    return Ok(new ResponseBase<List<MeterReadResult>>()
                    {
                        Code = 20000,
                        Data = new List<MeterReadResult>()
                    });
                }

                var assetIds = new List<int>();

                assetInfos.Select(p => p.AssetIds).ForEach(item =>
                {
                    assetIds.AddRange(item);
                });

                assetIds = assetIds.Distinct().ToList();

                // 获取属性的交集
                var propertyIntersects = UniversalDeviceInfo.Instance._dicProperty.Keys
                                                  .ToList().Intersect(input.PropertyList).ToList();

                //总值
                meterRead.TotalSize = assetIds.Count;

                var searchAssetInfos = assetInfos.OrderBy(a=>a.CircuitName).Skip(input.LastRowNumber).Take(input.PageSize).ToList();

                assetIds = new List<int>();
                searchAssetInfos.Select(p => p.AssetIds).ForEach(item =>
                {
                    assetIds.AddRange(item);
                });

                // 获取influxDb中的数据
                var meterReadByDtoList = await meterReadHelper.GetMeterReadByDtoList(propertyIntersects, assetIds, assetInfos, input.Date ?? "");

                //分页返回值
                meterRead.MeterReadList = meterReadByDtoList;

                //返回值
                result = new ResponseBase<MeterReadResult>() { Code = 20000, Data = meterRead };
            }
            catch (Exception ex)
            {
                result = new ResponseBase<MeterReadResult>() { Code = 50000, Message = ex.Message };
            }

            return Ok(result);
        }

        /// <summary>
        /// 获取抄表表格实时数据
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [SwaggerOperation(Summary = "Swagger_MeterRead_GetRealTabChartData", Description = "Swagger_MeterRead_GetRealTabChartData_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> GetRealTabChartData([FromBody] MeterReadParam input)
        {
            ResponseBase<List<RealMeterReadResult>> result;

            try
            {
                // 定义全局所有返回值
                var realmeterReadList = new List<RealMeterReadResult>();

                // 属性和回路是否为空
                if (!input.PropertyList.Any())
                {
                    return Ok(new ResponseBase<List<RealMeterReadResult>>()
                    {
                        Code = 50000,
                        Message = MessageContext.GetString("MeterRead_Err_AttributeByEmpty")
                    });
                }

                var meterReadHelper = _provider.GetService<MeterReadServer>();

                // 获取资产设备信息
                var assetInfos = await meterReadHelper!.GetAssetInfoList(input);

                // 不存在回路的设备
                if (!assetInfos.Any())
                {
                    return Ok(new ResponseBase<List<RealMeterReadResult>>()
                    {
                        Code = 20000,
                        Data = new List<RealMeterReadResult>()
                    });
                }

                var assetIds = new List<int>();

                assetInfos.Select(p => p.AssetIds).ForEach(item =>
                {
                    assetIds.AddRange(item);
                });

                assetIds = assetIds.Distinct().ToList();

                // 获取属性的交集
                var propertyIntersects = UniversalDeviceInfo.Instance._dicProperty.Keys
                                                  .ToList().Intersect(input.PropertyList).ToList();

                var influxHelper = _provider.GetRequiredService<AssetInfluxHelper>();

                var coefficient = 1000.0M;

                var systemConfig = await _db.Queryable<SystemConfig>().FirstAsync(c => c.Type == "SpecialConfig" && c.Name == "EnergyCoefficient");
                if (systemConfig != null && decimal.TryParse(systemConfig.Value, out var coefficientDecimal))
                {
                    coefficient = coefficientDecimal;
                }

                var realTabChartDtoList = await influxHelper.GetRealTabChartDataAsync(input.PropertyList, assetIds);
                foreach (var item in assetInfos)
                {
                    var realTimeChirdList = realTabChartDtoList.Where(p => item.AssetIds.Contains(p.AssetId) && p.Val > 0).ToList();

                    foreach (var itemProPerty in propertyIntersects)
                    {
                        var realTimeVal = realTimeChirdList.Where(p => p.Field == itemProPerty.ToLower()).FirstOrDefault()?.Val ?? 0;

                        if (itemProPerty == "active_energy_import" || itemProPerty == "active_power")
                        {
                            realTimeVal = realTimeVal / coefficient;
                        }

                        realTimeVal = Convert.ToDecimal(realTimeVal.ToString("0.00"));

                        UniversalDeviceInfo.Instance._dicProperty.TryGetValue(itemProPerty, out string? propertyName);

                        var entity = new RealMeterReadResult()
                        {
                            CircuitId = item.CircuitId,
                            CircuitName = item.CircuitName,
                            PropertyName = propertyName,
                            RealTimeValue =  realTimeVal
                        };

                        realmeterReadList.Add(entity);
                    }
                }

                result = new ResponseBase<List<RealMeterReadResult>>() { Code = 20000, Data = realmeterReadList.ToList() };
            }
            catch (Exception ex)
            {
                result = new ResponseBase<List<RealMeterReadResult>>() { Code = 50000, Message = ex.Message };
            }

            return Ok(result);
        }

        /// <summary>
        /// 导出抄表表格excel
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [SwaggerOperation(Summary = "Swagger_MeterRead_DownExcel", Description = "Swagger_MeterRead_DownExcel_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> DownExcel([FromQuery] MeterReadParam input)
        {
            // 资产数据为空
            if (!input.PropertyList.Any())
            {
                return Ok(new ResponseBase<string>()
                {
                    Code = 50000,
                    Message = MessageContext.GetString("MeterRead_Err_AttributeByEmpty")
                });
            }

            var meterReadHelper = _provider.GetService<MeterReadServer>();

            // 获取资产设备信息
            var assetInfos = await meterReadHelper!.GetAssetInfoList(input);

            var areaInfo = await _db.Queryable<AssetInfo>()
                                 .FirstAsync(p => p.AssetLevel == AssetLevel.Area);

            var assetIds = new List<int>();

            assetInfos.Select(p => p.AssetIds).ForEach(item =>
            {
                assetIds.AddRange(item);
            });

            assetIds = assetIds.Distinct().ToList();

            // 获取属性的交集
            var propertyIntersects = UniversalDeviceInfo.Instance._dicProperty.Keys
                                              .ToList().Intersect(input.PropertyList).ToList();

            var meterReadByDtoList = new List<MeterReadByDto>();

            var count = 0;
            while (true)
            {
                var searchAssetIds = assetIds.Skip(count * 10).Take(10).ToList();
                if (searchAssetIds.Count == 0)
                {
                    break;
                }

                count++;
                // 获取influxDb中的数据
                var tempList = await meterReadHelper.GetMeterReadByDtoList(propertyIntersects, assetIds, assetInfos, input.Date ?? "");

                meterReadByDtoList.AddRange(tempList);
            }

            string fileName = $"{areaInfo.AssetName}低压系统远程抄表记录_{(!string.IsNullOrEmpty(input.Date) ?
                              $"{Convert.ToDateTime(input.Date):yyyyMMdd}" : $"{DateTime.Now:yyyyMMdd}")}.xlsx";

            var path = Path.Combine(Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploadfiles", "personal"), $"{fileName}");

            //生成excel
            meterReadHelper.ExportExcel(meterReadByDtoList, path, fileName);

            //获取二进制流
            var stream = FileHelper.FileToStream(path);

            return await Task.FromResult(new FileStreamResult(stream, "application/octet-stream")
            {
                FileDownloadName = fileName
            });
        }

        /// <summary>
        /// 资源释放
        /// </summary>
        public void Dispose()
        {
            if (_dbClient != null)
            {
                _dbClient.Close();
            }
        }
    }
}
