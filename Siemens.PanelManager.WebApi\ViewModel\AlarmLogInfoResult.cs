﻿using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.WebApi.StaticContent;
using System.Text;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class AlarmLogInfoResult
    {
        public AlarmLogInfoResult(AlarmLog log, AlarmRule? rule, List<AlarmLogChangeLog> changeLogs, MessageContext messageContext)
        {
            Id = log.Id;

            SubstationName = log.SubstationName ?? string.Empty;
            PanelName = log.PanelName ?? string.Empty;
            CircuitName = log.CircuitName ?? string.Empty;
            DeviceName = log.DeviceName ?? string.Empty;
            LogTime = log.CreatedTime;
            StringBuilder position = new StringBuilder();
            if(!string.IsNullOrWhiteSpace(log.SubstationName)) 
            {
                position.Append(log.SubstationName);
            }

            if (!string.IsNullOrWhiteSpace(log.PanelName))
            {
                if(position.Length > 0) 
                {
                    position.Append('-');
                }
                position.Append(log.PanelName);
            }

            if (!string.IsNullOrWhiteSpace(log.CircuitName))
            {
                if (position.Length > 0)
                {
                    position.Append('-');
                }
                position.Append(log.CircuitName);
            }
            Position = position.ToString();
            AlarmRuleId = log.RuleId;
            Message = log.Message;
            Name = rule?.Name ?? string.Empty;
            Rule = rule?.RuleInfo ?? string.Empty;

            var list = new List<AlarmAssetStatus>();
            var statusDic = log.AssetStatus;
            if (statusDic != null)
            {
                foreach (var item in statusDic)
                {
                    list.Add(new AlarmAssetStatus(item, messageContext));
                }
            }
            AssetStatus = list.ToArray();
            var histories = new List<AlarmLogHistory>();
            foreach (var item in changeLogs) 
            {
                histories.Add(new AlarmLogHistory(item, messageContext));
            }
            Histories = histories.ToArray();

            AlarmStatus = (int)log.Status;
            Severity = (int)log.Severity;
            EventType = (int)log.EventType;

            var settings = log.AssetSettings;
            if (settings != null) 
            {
                var assetSettings = new List<AlarmAssetStatus>();
                foreach (var item in settings) 
                {
                    assetSettings.Add(new AlarmAssetStatus(item.Key, item.Value));
                }
                AssetSettings = assetSettings.ToArray();
            }
        }
        public long Id { get; set; }
        public int? AlarmRuleId { get; set; }
        public string Name { get; set; }= string.Empty;
        public string Rule { get; set; }= string.Empty;
        public string Message { get; set; } = string.Empty;
        public string SubstationName { get; set; } = string.Empty;
        public string PanelName { get; set; } = string.Empty;
        public string CircuitName { get; set; } = string.Empty;
        public string DeviceName { get; set; } = string.Empty;
        public string Position { get; set; } = string.Empty;
        public int AlarmStatus { get; set; }
        public int EventType { get; set; }
        public int Severity { get; set; }
        public DateTime LogTime { get; set; }
        public AlarmAssetStatus[] AssetStatus { get; set; }
        public AlarmAssetStatus[]? AssetSettings { get; set; }

        public AlarmLogHistory[] Histories { get; set; }
    }

    public class AlarmAssetStatus
    {
        public AlarmAssetStatus(string name, string value)
        { 
            Name = name;
            Value = value;
        }
        public AlarmAssetStatus(KeyValuePair<string, string> item, MessageContext messageContext)
        {
            var match = Regex.Match(item.Key, "_([\\w]*)$");
            if (match.Success)
            {
                Code = match.Groups[1].Value;
            }
            else
            {
                Code = string.Empty;
            }

            if (item.Key.StartsWith("||"))
            {
                Name = item.Key.Substring(2);
            }
            else
            {
                Name = messageContext.GetDataPointName(item.Key);                
            }

            Value = item.Value;
        }
        public string? Code { get; set; }
        public string Name { get; set; }
        public string Value { get; set; }
    }

    public class AlarmLogHistory 
    {
        public AlarmLogHistory(AlarmLogChangeLog changeLog, MessageContext messageContext)
        {
            Remark = changeLog.Remark ?? string.Empty;
            ChangedBy = changeLog.ChangedBy ?? string.Empty;
            Time = changeLog.ChangeTime;
            switch (changeLog.Source)
            {
                case AlarmChangeSource.Person:
                    Source = messageContext.GetString("") ?? "Person";
                    break;
                case AlarmChangeSource.WorkOrder:
                    Source = messageContext.GetString("") ?? "WorkOrder";
                    break;
                default:
                    Source = string.Empty;
                    break;
            }
        }

        public string Remark { get; set; }
        public string ChangedBy { get; set; }
        public string Source { get; set; }
        public DateTime Time { get; set; }
    }
}
