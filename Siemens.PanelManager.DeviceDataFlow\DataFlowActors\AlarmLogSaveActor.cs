﻿using Akka.Actor;
using InfluxDB.Client.Api.Domain;
using log4net.Core;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.ExtendFunction;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.DeviceDataFlow.StaticData;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Interface.Extend;
using Siemens.PanelManager.Model.Alarm;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.DataFlow;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.Server.DataPoint;
using SqlSugar;
using System.Reactive;

namespace Siemens.PanelManager.DeviceDataFlow.DataFlowActors
{
    internal class AlarmLogSaveActor : ReceiveActor
    {
        private readonly ILogger<AlarmLogSaveActor> _logger;
        private readonly IServiceProvider _provider;
        private readonly SiemensCache _cache;
        private AlarmRuleServer _server;
        private IMessageContext _messageContext;

        public AlarmLogSaveActor(ILogger<AlarmLogSaveActor> logger, IServiceProvider provider, SiemensCache cache)
        {
            _logger = logger;
            _provider = provider;
            _cache = cache;
            _server = _provider.GetRequiredService<AlarmRuleServer>();
            var factory = _provider.GetRequiredService<IMessageContextFactory>();
            _messageContext = factory.GetMessageContext(factory.GetDefaultLanguage());

            ReceiveAsync<(AlarmRule[], AssetChangeData)>(p => SaveAlarmLogs(p.Item1, p.Item2));
            ReceiveAsync<AlarmLog>(SaveAlarmLog);
            ReceiveAsync<AlarmLog[]>(SaveAlarmLogs);
        }

        protected override bool AroundReceive(Receive receive, object message)
        {
            try
            {
                return base.AroundReceive(receive, message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AlarmLogSaveActor Failed");
                return true;
            }
        }

        private async Task SaveAlarmLogs(AlarmRule[] alarmRules, AssetChangeData assetChangeData)
        {
            var sqlClient = _provider.GetRequiredService<SqlSugarScope>();
            {
                var cacheKey = string.Format(Constant.AssetCurrentDataCacheKey, assetChangeData.AssetId);
                var currentStatus = _cache.GetHashAllData(cacheKey);
                var simgleInfo = _cache.Get<AssetSimpleInfo>(string.Format(Constant.AssetSimpleInfoCacheKey, assetChangeData.AssetId));
                if (simgleInfo == null) return;

                var dataPointServer = _provider.GetRequiredService<DataPointServer>();
                var dataPointList = await dataPointServer.GetDataPointInfos(simgleInfo.AssetLevel, simgleInfo.AssetType, simgleInfo.AssetModel, simgleInfo.AssetId);
                var logs = new List<AlarmLog>();
                string? deivceName = null;
                if (simgleInfo.AssetLevel == AssetLevel.Device)
                {
                    deivceName = simgleInfo.AssetName;
                }

                var isGeneralEquipment = false;

                if (simgleInfo.AssetLevel == AssetLevel.Device) 
                {
                    isGeneralEquipment = _server.IsGeneralEquipment(simgleInfo.AssetModel, simgleInfo.AssetType);
                }

                foreach (var rule in alarmRules)
                {
                    var exists = _cache.Get<bool>($"AlarmRoleCheck-{rule.Id}|{assetChangeData.AssetId}");

                    if (!exists)
                    {
                        _cache.Set($"AlarmRoleCheck-{rule.Id}|{assetChangeData.AssetId}", true);
                        var ruleInfo = _server.GetRuleInfo(rule, dataPointList, _messageContext);

                        var log = new AlarmLog()
                        {
                            RuleId = rule.Id,
                            Severity = rule.Severity,
                            AssetStatusStr = _server.GetAssetStatusStr(currentStatus, dataPointList, isGeneralEquipment),
                            AssetId = assetChangeData.AssetId,
                            CircuitName = simgleInfo.CircuitSimpleInfo?.AssetName,
                            PanelName = simgleInfo.PanelSimpleInfo?.AssetName,
                            SubstationName = simgleInfo.SubstationSimpleInfo?.AssetName,
                            Status = AlarmLogStatus.New,
                            DeviceName = deivceName,
                            Message = ruleInfo,
                            EventType = AlarmEventType.Alarm,
                            CreatedBy = "System",
                            CreatedTime = DateTime.Now,
                            UpdatedBy = "System",
                            UpdatedTime = DateTime.Now,
                        };
                        logs.Add(log);
                    }
                }

                await SaveAlarmLogs(sqlClient, logs.ToArray());
            }
        }

        private async Task SaveAlarmLog(AlarmLog alarmLog)
        {
            var sqlClient = _provider.GetRequiredService<SqlSugarScope>();
            await SaveAlarmLogs(sqlClient, new [] { alarmLog });
        }

        private async Task SaveAlarmLogs(ISqlSugarClient sqlClient, AlarmLog[] alarmLogs)
        {
            if (alarmLogs == null) return;

            var ids = await sqlClient.Insertable(alarmLogs).ExecuteReturnPkListAsync<long>();
            for (var i = 0; i < ids.Count; i++)
            {
                alarmLogs[i].Id = ids[i];
            }

            var alarmsDic = new Dictionary<AssetLevel, List<string>>();
            var needCalculateList = new List<AlarmLog>();
            var assetIds = alarmLogs.Select(a => a.AssetId).ToArray();
            foreach (var log in alarmLogs)
            {
                if ((log.EventType == AlarmEventType.Alarm
                    || log.EventType == AlarmEventType.UdcAlarm
                    || log.EventType == AlarmEventType.BreakerTrip)
                    && log.AssetId.HasValue
                    && (log.Status >= AlarmLogStatus.New
                    && log.Status < AlarmLogStatus.Finish))
                {
                    needCalculateList.Add(log);
                    if (!string.IsNullOrEmpty(log.CircuitName))
                    {
                        Append(alarmsDic, AssetLevel.Circuit, log.CircuitName);
                    }

                    if (!string.IsNullOrEmpty(log.PanelName))
                    {
                        Append(alarmsDic, AssetLevel.Panel, log.PanelName);
                    }

                    if (!string.IsNullOrEmpty(log.SubstationName))
                    {
                        Append(alarmsDic, AssetLevel.Substation, log.SubstationName);
                    }
                }
            }

            #region Asset Level 分开处理

            #region Circuit
            {
                if (alarmsDic.TryGetValue(AssetLevel.Circuit, out var alarmList) && alarmList != null)
                {
                    var assetNames = alarmList.Distinct().ToList();

                    var cacheDatas = _cache.GetHashData<AlarmCountModel>($"AlarmCount-{AssetLevel.Circuit}", assetNames.ToArray());

                    foreach (var assetName in assetNames)
                    {
                        AlarmCountModel? countModel;
                        if (!cacheDatas.TryGetValue(assetName, out countModel) || countModel == null)
                        {
                            countModel = new AlarmCountModel();
                        }

                        var tempList = needCalculateList.Where(a => a.CircuitName == assetName).ToList();
                        countModel.HighCount += tempList.Where(a => a.Severity == AlarmSeverity.High).Count();
                        countModel.MiddleCount += tempList.Where(a => a.Severity == AlarmSeverity.Middle).Count();
                        countModel.LowCount += tempList.Where(a => a.Severity == AlarmSeverity.Low).Count();
                        countModel.UDCAlarmCount += tempList.Where(a => a.EventType == AlarmEventType.UdcAlarm).Count();
                        countModel.AlarmCount += tempList.Where(a => a.EventType == AlarmEventType.Alarm).Count();
                        countModel.BreakerTripCount += tempList.Where(a => a.EventType == AlarmEventType.BreakerTrip).Count();

                        _cache.SetHashData($"AlarmCount-{AssetLevel.Circuit}", assetName, countModel);
                    }
                }
            }
            #endregion

            #region Panel
            {
                if (alarmsDic.TryGetValue(AssetLevel.Panel, out var alarmList) && alarmList != null)
                {
                    var assetNames = alarmList.Distinct().ToList();

                    var cacheDatas = _cache.GetHashData<AlarmCountModel>($"AlarmCount-{AssetLevel.Panel}", assetNames.ToArray());

                    foreach (var assetName in assetNames)
                    {
                        AlarmCountModel? countModel;
                        if (!cacheDatas.TryGetValue(assetName, out countModel) || countModel == null)
                        {
                            countModel = new AlarmCountModel();
                        }

                        var tempList = needCalculateList.Where(a => a.PanelName == assetName).ToList();
                        countModel.HighCount += tempList.Where(a => a.Severity == AlarmSeverity.High).Count();
                        countModel.MiddleCount += tempList.Where(a => a.Severity == AlarmSeverity.Middle).Count();
                        countModel.LowCount += tempList.Where(a => a.Severity == AlarmSeverity.Low).Count();
                        countModel.UDCAlarmCount += tempList.Where(a => a.EventType == AlarmEventType.UdcAlarm).Count();
                        countModel.AlarmCount += tempList.Where(a => a.EventType == AlarmEventType.Alarm).Count();
                        countModel.BreakerTripCount += tempList.Where(a => a.EventType == AlarmEventType.BreakerTrip).Count();

                        _cache.SetHashData($"AlarmCount-{AssetLevel.Panel}", assetName, countModel);
                    }
                }
            }
            #endregion

            #region Substation
            {
                if (alarmsDic.TryGetValue(AssetLevel.Substation, out var alarmList) && alarmList != null)
                {
                    var assetNames = alarmList.Distinct().ToList();

                    var cacheDatas = _cache.GetHashData<AlarmCountModel>($"AlarmCount-{AssetLevel.Substation}", assetNames.ToArray());

                    foreach (var assetName in assetNames)
                    {
                        AlarmCountModel? countModel;
                        if (!cacheDatas.TryGetValue(assetName, out countModel) || countModel == null)
                        {
                            countModel = new AlarmCountModel();
                        }

                        var tempList = needCalculateList.Where(a => a.SubstationName == assetName).ToList();
                        countModel.HighCount += tempList.Where(a => a.Severity == AlarmSeverity.High).Count();
                        countModel.MiddleCount += tempList.Where(a => a.Severity == AlarmSeverity.Middle).Count();
                        countModel.LowCount += tempList.Where(a => a.Severity == AlarmSeverity.Low).Count();
                        countModel.UDCAlarmCount += tempList.Where(a => a.EventType == AlarmEventType.UdcAlarm).Count();
                        countModel.AlarmCount += tempList.Where(a => a.EventType == AlarmEventType.Alarm).Count();
                        countModel.BreakerTripCount += tempList.Where(a => a.EventType == AlarmEventType.BreakerTrip).Count();
                        _cache.SetHashData($"AlarmCount-{AssetLevel.Substation}", assetName, countModel);
                    }
                }
            }
            #endregion

            #endregion

            #region
            var assetStatusRef = _provider.GetRequiredService<IAssetDataProxyRef>();

            foreach (var assetId in assetIds)
            {
                assetStatusRef.InputData(new AssetInputData
                {
                    AssetId = assetId,
                    Datas = new Dictionary<string, string>
                    {
                        ["HaveAlarm"] = "1"
                    },
                    InputTime = DateTime.Now,
                });
            }
            #endregion

            #region Send Extend Service
            {
                var action = _provider.GetRequiredService<IExtendAction>();
                foreach (var log in alarmLogs)
                {
                    await action.PublishAlarmAsync(log);
                }
            }
            #endregion
        }

        private async Task SaveAlarmLogs(AlarmLog[] logs)
        {
            var sqlClient = _provider.GetRequiredService<SqlSugarScope>();
            await SaveAlarmLogs(sqlClient, logs);
        }

        private void Append(Dictionary<AssetLevel, List<string>> dic, AssetLevel level, string assetName)
        {
            if (!dic.TryGetValue(level, out var list))
            {
                list = new List<string>();
                dic.Add(level, list);
            }

            list.Add(assetName);
        }
    }
}
