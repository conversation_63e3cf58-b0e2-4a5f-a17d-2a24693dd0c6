[{"Code": "HaveAlarm", "Name": "HaveAlarm", "GroupName": "Status", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "Alarm", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 1, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "IsConnected", "Name": "IsConnected", "GroupName": "Status", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "UDCConnect", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 2, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_1", "Name": "Sensor_1", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp1", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 3, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_2", "Name": "Sensor_2", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp2", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 4, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_3", "Name": "Sensor_3", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp3", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 5, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_4", "Name": "Sensor_4", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp4", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 6, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_5", "Name": "Sensor_5", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp5", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 7, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_6", "Name": "Sensor_6", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp6", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 8, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_7", "Name": "Sensor_7", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp7", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 9, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_8", "Name": "Sensor_8", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp8", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 10, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_9", "Name": "Sensor_9", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp9", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 11, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_10", "Name": "Sensor_10", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp10", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 12, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_11", "Name": "Sensor_11", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp11", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 13, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_12", "Name": "Sensor_12", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp12", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 14, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_13", "Name": "Sensor_13", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp13", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 15, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_14", "Name": "Sensor_14", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp14", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 16, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_15", "Name": "Sensor_15", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp15", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 17, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_16", "Name": "Sensor_16", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp16", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 18, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_17", "Name": "Sensor_17", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp17", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 19, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_18", "Name": "Sensor_18", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp18", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 20, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_19", "Name": "Sensor_19", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp19", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 21, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_20", "Name": "Sensor_20", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp20", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 22, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_21", "Name": "Sensor_21", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp21", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 23, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_22", "Name": "Sensor_22", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp22", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 24, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_23", "Name": "Sensor_23", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp23", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 25, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_24", "Name": "Sensor_24", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp24", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 26, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_25", "Name": "Sensor_25", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp25", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 27, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_26", "Name": "Sensor_26", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp26", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 28, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_27", "Name": "Sensor_27", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp27", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 29, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_28", "Name": "Sensor_28", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp28", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 30, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_29", "Name": "Sensor_29", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp29", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 31, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_30", "Name": "Sensor_30", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp30", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 32, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_31", "Name": "Sensor_31", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp31", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 33, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_32", "Name": "Sensor_32", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp32", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 34, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_33", "Name": "Sensor_33", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp33", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 35, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_34", "Name": "Sensor_34", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp34", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 36, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_35", "Name": "Sensor_35", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp35", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 37, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_36", "Name": "Sensor_36", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp36", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 38, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_37", "Name": "Sensor_37", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp37", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 39, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_38", "Name": "Sensor_38", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp38", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 40, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_39", "Name": "Sensor_39", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp39", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 41, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_40", "Name": "Sensor_40", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp40", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 42, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_41", "Name": "Sensor_41", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp41", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 43, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_42", "Name": "Sensor_42", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp42", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 44, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_43", "Name": "Sensor_43", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp43", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 45, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_44", "Name": "Sensor_44", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp44", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 46, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_45", "Name": "Sensor_45", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp45", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 47, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_46", "Name": "Sensor_46", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp46", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 48, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_47", "Name": "Sensor_47", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp47", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 49, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_48", "Name": "Sensor_48", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp48", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 50, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_49", "Name": "Sensor_49", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp49", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 51, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_50", "Name": "Sensor_50", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp50", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 52, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_51", "Name": "Sensor_51", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp51", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 53, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_52", "Name": "Sensor_52", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp52", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 54, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_53", "Name": "Sensor_53", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp53", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 55, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_54", "Name": "Sensor_54", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp54", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 56, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_55", "Name": "Sensor_55", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp55", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 57, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_56", "Name": "Sensor_56", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp56", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 58, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_57", "Name": "Sensor_57", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp57", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 59, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_58", "Name": "Sensor_58", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp58", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 60, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_59", "Name": "Sensor_59", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp59", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 61, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_60", "Name": "Sensor_60", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp60", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 62, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_61", "Name": "Sensor_61", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp61", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 63, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_62", "Name": "Sensor_62", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp62", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 64, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_63", "Name": "Sensor_63", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp63", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 65, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_64", "Name": "Sensor_64", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp64", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 66, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_65", "Name": "Sensor_65", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp65", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 67, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_66", "Name": "Sensor_66", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp66", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 68, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_67", "Name": "Sensor_67", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp67", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 69, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_68", "Name": "Sensor_68", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp68", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 70, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_69", "Name": "Sensor_69", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp69", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 71, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_70", "Name": "Sensor_70", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp70", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 72, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_71", "Name": "Sensor_71", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp71", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 73, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_72", "Name": "Sensor_72", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp72", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 74, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_73", "Name": "Sensor_73", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp73", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 75, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_74", "Name": "Sensor_74", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp74", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 76, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_75", "Name": "Sensor_75", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp75", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 77, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_76", "Name": "Sensor_76", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp76", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 78, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_77", "Name": "Sensor_77", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp77", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 79, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_78", "Name": "Sensor_78", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp78", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 80, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_79", "Name": "Sensor_79", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp79", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 81, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_80", "Name": "Sensor_80", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp80", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 82, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_81", "Name": "Sensor_81", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp81", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 83, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_82", "Name": "Sensor_82", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp82", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 84, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_83", "Name": "Sensor_83", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp83", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 85, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_84", "Name": "Sensor_84", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp84", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 86, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_85", "Name": "Sensor_85", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp85", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 87, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_86", "Name": "Sensor_86", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp86", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 88, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_87", "Name": "Sensor_87", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp87", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 89, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_88", "Name": "Sensor_88", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp88", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 90, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_89", "Name": "Sensor_89", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp89", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 91, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_90", "Name": "Sensor_90", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp90", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 92, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_91", "Name": "Sensor_91", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp91", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 93, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_92", "Name": "Sensor_92", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp92", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 94, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_93", "Name": "Sensor_93", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp93", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 95, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_94", "Name": "Sensor_94", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp94", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 96, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_95", "Name": "Sensor_95", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp95", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 97, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_96", "Name": "Sensor_96", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp96", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 98, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_97", "Name": "Sensor_97", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp97", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 99, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_98", "Name": "Sensor_98", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp98", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 100, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_99", "Name": "Sensor_99", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp99", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 101, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_100", "Name": "Sensor_100", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "dp100", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TempMotorLogic\"}", "Sort": 102, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_1_connect", "Name": "Sensor_1_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 103, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_2_connect", "Name": "Sensor_2_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 104, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_3_connect", "Name": "Sensor_3_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 105, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_4_connect", "Name": "Sensor_4_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 106, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_5_connect", "Name": "Sensor_5_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 107, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_6_connect", "Name": "Sensor_6_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 108, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_7_connect", "Name": "Sensor_7_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 109, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_8_connect", "Name": "Sensor_8_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 110, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_9_connect", "Name": "Sensor_9_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 111, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_10_connect", "Name": "Sensor_10_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 112, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_11_connect", "Name": "Sensor_11_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 113, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_12_connect", "Name": "Sensor_12_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 114, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_13_connect", "Name": "Sensor_13_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 115, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_14_connect", "Name": "Sensor_14_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 116, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_15_connect", "Name": "Sensor_15_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 117, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_16_connect", "Name": "Sensor_16_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 118, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_17_connect", "Name": "Sensor_17_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 119, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_18_connect", "Name": "Sensor_18_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 120, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_19_connect", "Name": "Sensor_19_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 121, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_20_connect", "Name": "Sensor_20_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 122, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_21_connect", "Name": "Sensor_21_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 123, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_22_connect", "Name": "Sensor_22_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 124, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_23_connect", "Name": "Sensor_23_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 125, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_24_connect", "Name": "Sensor_24_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 126, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_25_connect", "Name": "Sensor_25_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 127, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_26_connect", "Name": "Sensor_26_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 128, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_27_connect", "Name": "Sensor_27_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 129, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_28_connect", "Name": "Sensor_28_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 130, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_29_connect", "Name": "Sensor_29_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 131, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_30_connect", "Name": "Sensor_30_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 132, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_31_connect", "Name": "Sensor_31_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 133, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_32_connect", "Name": "Sensor_32_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 134, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_33_connect", "Name": "Sensor_33_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 135, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_34_connect", "Name": "Sensor_34_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 136, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_35_connect", "Name": "Sensor_35_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 137, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_36_connect", "Name": "Sensor_36_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 138, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_37_connect", "Name": "Sensor_37_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 139, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_38_connect", "Name": "Sensor_38_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 140, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_39_connect", "Name": "Sensor_39_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 141, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_40_connect", "Name": "Sensor_40_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 142, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_41_connect", "Name": "Sensor_41_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 143, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_42_connect", "Name": "Sensor_42_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 144, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_43_connect", "Name": "Sensor_43_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 145, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_44_connect", "Name": "Sensor_44_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 146, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_45_connect", "Name": "Sensor_45_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 147, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_46_connect", "Name": "Sensor_46_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 148, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_47_connect", "Name": "Sensor_47_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 149, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_48_connect", "Name": "Sensor_48_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 150, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_49_connect", "Name": "Sensor_49_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 151, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_50_connect", "Name": "Sensor_50_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 152, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_51_connect", "Name": "Sensor_51_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 153, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_52_connect", "Name": "Sensor_52_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 154, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_53_connect", "Name": "Sensor_53_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 155, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_54_connect", "Name": "Sensor_54_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 156, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_55_connect", "Name": "Sensor_55_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 157, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_56_connect", "Name": "Sensor_56_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 158, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_57_connect", "Name": "Sensor_57_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 159, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_58_connect", "Name": "Sensor_58_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 160, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_59_connect", "Name": "Sensor_59_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 161, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_60_connect", "Name": "Sensor_60_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 162, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_61_connect", "Name": "Sensor_61_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 163, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_62_connect", "Name": "Sensor_62_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 164, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_63_connect", "Name": "Sensor_63_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 165, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_64_connect", "Name": "Sensor_64_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 166, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_65_connect", "Name": "Sensor_65_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 167, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_66_connect", "Name": "Sensor_66_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 168, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_67_connect", "Name": "Sensor_67_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 169, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_68_connect", "Name": "Sensor_68_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 170, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_69_connect", "Name": "Sensor_69_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 171, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_70_connect", "Name": "Sensor_70_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 172, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_71_connect", "Name": "Sensor_71_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 173, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_72_connect", "Name": "Sensor_72_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 174, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_73_connect", "Name": "Sensor_73_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 175, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_74_connect", "Name": "Sensor_74_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 176, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_75_connect", "Name": "Sensor_75_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 177, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_76_connect", "Name": "Sensor_76_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 178, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_77_connect", "Name": "Sensor_77_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 179, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_78_connect", "Name": "Sensor_78_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 180, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_79_connect", "Name": "Sensor_79_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 181, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_80_connect", "Name": "Sensor_80_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 182, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_81_connect", "Name": "Sensor_81_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 183, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_82_connect", "Name": "Sensor_82_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 184, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_83_connect", "Name": "Sensor_83_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 185, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_84_connect", "Name": "Sensor_84_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 186, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_85_connect", "Name": "Sensor_85_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 187, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_86_connect", "Name": "Sensor_86_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 188, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_87_connect", "Name": "Sensor_87_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 189, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_88_connect", "Name": "Sensor_88_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 190, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_89_connect", "Name": "Sensor_89_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 191, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_90_connect", "Name": "Sensor_90_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 192, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_91_connect", "Name": "Sensor_91_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 193, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_92_connect", "Name": "Sensor_92_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 194, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_93_connect", "Name": "Sensor_93_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 195, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_94_connect", "Name": "Sensor_94_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 196, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_95_connect", "Name": "Sensor_95_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 197, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_96_connect", "Name": "Sensor_96_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 198, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_97_connect", "Name": "Sensor_97_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 199, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_98_connect", "Name": "Sensor_98_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 200, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_99_connect", "Name": "Sensor_99_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 201, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_100_connect", "Name": "Sensor_100_connect", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 202, "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_1_offline_time", "Name": "Sensor_1_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 203, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_2_offline_time", "Name": "Sensor_2_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 204, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_3_offline_time", "Name": "Sensor_3_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 205, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_4_offline_time", "Name": "Sensor_4_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 206, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_5_offline_time", "Name": "Sensor_5_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 207, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_6_offline_time", "Name": "Sensor_6_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 208, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_7_offline_time", "Name": "Sensor_7_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 209, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_8_offline_time", "Name": "Sensor_8_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 210, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_9_offline_time", "Name": "Sensor_9_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 211, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_10_offline_time", "Name": "Sensor_10_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 212, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_11_offline_time", "Name": "Sensor_11_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 213, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_12_offline_time", "Name": "Sensor_12_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 214, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_13_offline_time", "Name": "Sensor_13_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 215, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_14_offline_time", "Name": "Sensor_14_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 216, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_15_offline_time", "Name": "Sensor_15_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 217, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_16_offline_time", "Name": "Sensor_16_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 218, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_17_offline_time", "Name": "Sensor_17_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 219, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_18_offline_time", "Name": "Sensor_18_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 220, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_19_offline_time", "Name": "Sensor_19_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 221, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_20_offline_time", "Name": "Sensor_20_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 222, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_21_offline_time", "Name": "Sensor_21_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 223, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_22_offline_time", "Name": "Sensor_22_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 224, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_23_offline_time", "Name": "Sensor_23_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 225, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_24_offline_time", "Name": "Sensor_24_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 226, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_25_offline_time", "Name": "Sensor_25_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 227, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_26_offline_time", "Name": "Sensor_26_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 228, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_27_offline_time", "Name": "Sensor_27_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 229, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_28_offline_time", "Name": "Sensor_28_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 230, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_29_offline_time", "Name": "Sensor_29_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 231, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_30_offline_time", "Name": "Sensor_30_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 232, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_31_offline_time", "Name": "Sensor_31_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 233, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_32_offline_time", "Name": "Sensor_32_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 234, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_33_offline_time", "Name": "Sensor_33_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 235, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_34_offline_time", "Name": "Sensor_34_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 236, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_35_offline_time", "Name": "Sensor_35_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 237, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_36_offline_time", "Name": "Sensor_36_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 238, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_37_offline_time", "Name": "Sensor_37_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 239, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_38_offline_time", "Name": "Sensor_38_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 240, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_39_offline_time", "Name": "Sensor_39_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 241, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_40_offline_time", "Name": "Sensor_40_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 242, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_41_offline_time", "Name": "Sensor_41_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 243, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_42_offline_time", "Name": "Sensor_42_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 244, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_43_offline_time", "Name": "Sensor_43_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 245, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_44_offline_time", "Name": "Sensor_44_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 246, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_45_offline_time", "Name": "Sensor_45_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 247, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_46_offline_time", "Name": "Sensor_46_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 248, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_47_offline_time", "Name": "Sensor_47_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 249, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_48_offline_time", "Name": "Sensor_48_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 250, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_49_offline_time", "Name": "Sensor_49_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 251, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_50_offline_time", "Name": "Sensor_50_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 252, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_51_offline_time", "Name": "Sensor_51_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 253, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_52_offline_time", "Name": "Sensor_52_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 254, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_53_offline_time", "Name": "Sensor_53_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 255, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_54_offline_time", "Name": "Sensor_54_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 256, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_55_offline_time", "Name": "Sensor_55_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 257, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_56_offline_time", "Name": "Sensor_56_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 258, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_57_offline_time", "Name": "Sensor_57_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 259, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_58_offline_time", "Name": "Sensor_58_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 260, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_59_offline_time", "Name": "Sensor_59_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 261, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_60_offline_time", "Name": "Sensor_60_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 262, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_61_offline_time", "Name": "Sensor_61_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 263, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_62_offline_time", "Name": "Sensor_62_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 264, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_63_offline_time", "Name": "Sensor_63_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 265, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_64_offline_time", "Name": "Sensor_64_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 266, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_65_offline_time", "Name": "Sensor_65_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 267, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_66_offline_time", "Name": "Sensor_66_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 268, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_67_offline_time", "Name": "Sensor_67_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 269, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_68_offline_time", "Name": "Sensor_68_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 270, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_69_offline_time", "Name": "Sensor_69_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 271, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_70_offline_time", "Name": "Sensor_70_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 272, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_71_offline_time", "Name": "Sensor_71_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 273, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_72_offline_time", "Name": "Sensor_72_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 274, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_73_offline_time", "Name": "Sensor_73_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 275, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_74_offline_time", "Name": "Sensor_74_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 276, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_75_offline_time", "Name": "Sensor_75_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 277, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_76_offline_time", "Name": "Sensor_76_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 278, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_77_offline_time", "Name": "Sensor_77_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 279, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_78_offline_time", "Name": "Sensor_78_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 280, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_79_offline_time", "Name": "Sensor_79_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 281, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_80_offline_time", "Name": "Sensor_80_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 282, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_81_offline_time", "Name": "Sensor_81_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 283, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_82_offline_time", "Name": "Sensor_82_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 284, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_83_offline_time", "Name": "Sensor_83_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 285, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_84_offline_time", "Name": "Sensor_84_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 286, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_85_offline_time", "Name": "Sensor_85_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 287, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_86_offline_time", "Name": "Sensor_86_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 288, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_87_offline_time", "Name": "Sensor_87_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 289, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_88_offline_time", "Name": "Sensor_88_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 290, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_89_offline_time", "Name": "Sensor_89_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 291, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_90_offline_time", "Name": "Sensor_90_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 292, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_91_offline_time", "Name": "Sensor_91_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 293, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_92_offline_time", "Name": "Sensor_92_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 294, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_93_offline_time", "Name": "Sensor_93_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 295, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_94_offline_time", "Name": "Sensor_94_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 296, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_95_offline_time", "Name": "Sensor_95_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 297, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_96_offline_time", "Name": "Sensor_96_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 298, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_97_offline_time", "Name": "Sensor_97_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 299, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_98_offline_time", "Name": "Sensor_98_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 300, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_99_offline_time", "Name": "Sensor_99_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 301, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Sensor_100_offline_time", "Name": "Sensor_100_offline_time", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "TempMeasurement", "AssetModel": "SiemensTempMeasurement", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 302, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}]