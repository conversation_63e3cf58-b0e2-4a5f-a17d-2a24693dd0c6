﻿using Siemens.PanelManager.HubModel;
using System.Collections.Concurrent;

namespace Siemens.PanelManager.Monitor.StaticData
{
    public class IpTableManager
    {
        public IpTablesRuleInfo[] ConfigRules { get; private set; }
        public IpTableManager(IConfiguration configuration) 
        {
            var session = configuration.GetSection("ManagerPorts");
            if (session != null && session.Exists())
            {
                ConfigRules = session.Get<IpTablesRuleInfo[]>();
            }
            else
            {
                ConfigRules = new IpTablesRuleInfo[0];
            }
        }

        private ConcurrentDictionary<long, Queue<IpTablesRuleInfo>> CmdList = new ConcurrentDictionary<long, Queue<IpTablesRuleInfo>>();

        public void InputCmd(IpTablesRuleInfo info, DateTime expireTime)
        {
            var ts = expireTime.GetTimestampForMinutes();
            CmdList.AddOrUpdate(ts, (k) =>
            {
                var queue = new Queue<IpTablesRuleInfo>();
                queue.Enqueue(info);
                return queue;
            }, (k, q) =>
            {
                q.Enqueue(info);
                return q;
            });
        }

        public IpTablesRuleInfo[] GetExpireRules(DateTime time)
        {
            var ts = time.GetTimestampForMinutes();
            var result = new List<IpTablesRuleInfo>();
            var keys = CmdList.Keys.Where(k => k <= ts).ToArray();
            foreach (var key in keys)
            {
                if (CmdList.TryRemove(key, out Queue<IpTablesRuleInfo>? queue) && queue != null)
                {
                    while (queue.TryDequeue(out IpTablesRuleInfo? ruleInfo) && ruleInfo != null)
                    {
                        result.Add(ruleInfo);
                    }
                }
            }

            return result.ToArray();
        }
    }
}
