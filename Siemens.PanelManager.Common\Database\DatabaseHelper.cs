﻿using Microsoft.Extensions.DependencyInjection;
using Siemens.PanelManager.Common.Log;
using Siemens.PanelManager.Interface.ActorRefs;
using SqlSugar;

namespace Siemens.PanelManager.Common.Database
{
    static class DatabaseHelper
    {
        private static readonly ConfigureExternalServices _externalServices = new ConfigureExternalServices()
        {
            DataInfoCacheService = new DatabaseCacheService()
        };

        private static IAssetManagerRef? _refObj = null;

        private static readonly AopEvents _events = new AopEvents()
        {
            OnError = OnError,
            DataExecuting = DataExecuting
        };

        public static ConnectionConfig GetConnectionConfig(string connectStr)
        {
            return new ConnectionConfig()
            {
                ConnectionString = connectStr,
                DbType = DbType.PostgreSQL,
                IsAutoCloseConnection = true,
                ConfigureExternalServices = _externalServices,
                AopEvents = _events
            };
        }

        private static void OnError(SqlSugarException ex)
        {
            LogHelper.Error(ex);
        }

        private static void DataExecuting(object data, DataFilterModel dataFilter) 
        {
            if (_refObj == null) return;
            #region AssetInfo
            if (dataFilter.EntityName == "AssetInfo")
            {
                if (dataFilter.PropertyName == "Id" && dataFilter.OperationType == DataFilterType.UpdateByObject)
                {
                    _refObj.UpdatedAsset((int)data);
                }
                else if (dataFilter.OperationType == DataFilterType.InsertByObject && dataFilter.PropertyName == "AssetName")
                {
                    _refObj.AddAsset((string)data);
                }
            }
            else if (dataFilter.EntityName == "AssetRelation")
            {
                if (dataFilter.PropertyName == "ChildId" && (dataFilter.OperationType == DataFilterType.UpdateByObject 
                    || dataFilter.OperationType == DataFilterType.InsertByObject))
                {
                    _refObj.UpdatedAsset((int)data);
                }
            }
            #endregion
        }

        public static ISqlSugarClient GetSqlSugarClient(ConnectionConfig config, IServiceProvider provider)
        {
            if (_refObj == null)
            {
                _refObj = provider.GetService<IAssetManagerRef>();
            }

            var client = new SqlSugarClient(config);
            return client;
        }

        public static SqlSugarScope GetSqlSugarScope(ConnectionConfig config, IServiceProvider provider)
        {
            if (_refObj == null)
            {
                _refObj = provider.GetService<IAssetManagerRef>();
            }
            return new SqlSugarScope(config);
        }
    }
}
