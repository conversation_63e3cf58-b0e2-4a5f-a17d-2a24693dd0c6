﻿using Microsoft.AspNetCore.Mvc;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class SnapshotAlarmQueryParam
    {
        [FromQuery(Name = "reportId")]
        public string? ReportId { get; set; }

        [FromQuery(Name = "page")]
        public int Page { get; set; } = 1;

        [FromQuery(Name = "pageSize")]
        public int PageSize { get; set; } = 5;

        [FromQuery(Name = "alarmStatus")]
        public int? AlarmStatus { get; set; }

        [FromQuery(Name = "eventType")]
        public string? EventType { get; set; }
        [FromQuery(Name = "subId")]
        public int subId { get; set; } = 0;
    }
}
