﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.Model.UDC
{
    public class UdcAddDeviceResult
    {
        [JsonProperty(PropertyName = "id")]
        public string? Id { get; set; }
        [JsonProperty(PropertyName = "type_name")]
        public string? TypeName { get; set; }
        [JsonProperty(PropertyName = "name")]
        public string? Name { get; set; }
        [JsonProperty(PropertyName = "plant_id")]
        public string? PlantId { get; set; }
        [JsonProperty(PropertyName = "address")]
        public string? Address { get; set; }
        [JsonProperty(PropertyName = "mbgw_address")]
        public string? MbgwAddress { get; set; }
        [JsonProperty(PropertyName = "archiving")]
        public DeviceStatusInfo? Archiving { get; set; }
        [JsonProperty(PropertyName = "monitoring")]
        public DeviceStatusInfo? Monitoring { get; set; }
        [JsonProperty(PropertyName = "parent_id")]
        public string? ParentId { get; set; }
        [JsonProperty(PropertyName = "child_ids")]
        public string[] ChildIds { get; set; } = new string[0];
        [JsonProperty(PropertyName = "gw_child_ids")]
        public string[] GwChildIds { get; set; } = new string[0];
        [JsonProperty(PropertyName = "gw_type_names")]
        public string[] GwTypeNames { get; set; } = new string[0];
    }
}
