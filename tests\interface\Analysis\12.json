{"info": {"_postman_id": "149112eb-2f0c-4d5c-9a80-5cfbe0f649c9", "name": "12使用管理员账号进入panel manager智慧分析中的损耗智能诊断菜单，点击回路柱子跳转后查看测量数据和功率因数", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 12", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "获取单个资产的详情 Copy 11", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "let s1 = pm.response.json().items[0].id\r", "pm.environment.set(\"s1\", s1);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Asset/search?levels=Substation", "host": ["{{baseUrl}}"], "path": ["api", "v1", "<PERSON><PERSON>", "search"], "query": [{"key": "levels", "value": "Substation"}]}}, "response": []}, {"name": "获取损耗电量损耗分析图表 Copy 7", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"展示测量数据\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"x\");\r", "    pm.expect(pm.response.text()).to.include(\"y\");\r", "    pm.expect(pm.response.text()).to.include(\"Voltage\");\r", "    pm.expect(pm.response.text()).to.include(\"PhaseShiftActivePower\");\r", "    pm.expect(pm.response.text()).to.include(\"PhaseShiftReactivePower\");\r", "    pm.expect(pm.response.text()).to.include(\"ElectricCurrent\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/status/1/getchartdata?dateType=0&startDate=2023-04-20&endDate=2023-04-21&chartCodes=Voltage,PhaseShiftActivePower,PhaseShiftReactivePower,ElectricCurrent", "host": ["{{baseUrl}}"], "path": ["api", "v1", "status", "1", "getchartdata"], "query": [{"key": "dateType", "value": "0"}, {"key": "startDate", "value": "2023-04-20"}, {"key": "endDate", "value": "2023-04-21"}, {"key": "chartCodes", "value": "Voltage,PhaseShiftActivePower,PhaseShiftReactivePower,ElectricCurrent"}]}}, "response": []}, {"name": "获取损耗电量损耗分析图表 Copy 8", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"展示功率因数\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"device_names\");\r", "    pm.expect(pm.response.text()).to.include(\"value\");\r", "    pm.expect(pm.response.text()).to.include(\"mean\");\r", "    pm.expect(pm.response.text()).to.include(\"max\");\r", "    pm.expect(pm.response.text()).to.include(\"min\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/status/1/getPowerFactorData?startDate=2023-04-20&endDate=2023-04-21", "host": ["{{baseUrl}}"], "path": ["api", "v1", "status", "1", "getPowerFactorData"], "query": [{"key": "startDate", "value": "2023-04-20"}, {"key": "endDate", "value": "2023-04-21"}]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}]}