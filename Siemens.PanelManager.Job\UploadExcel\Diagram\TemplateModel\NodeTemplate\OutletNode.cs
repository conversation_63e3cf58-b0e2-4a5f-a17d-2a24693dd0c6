﻿using Newtonsoft.Json;
using Siemens.PanelManager.Model.Topology;

namespace Siemens.PanelManager.Job.UploadExcel.Diagram.TemplateModel.NodeTemplate
{
    /// <summary>
    /// 单线图中出线端元素
    /// </summary>
    internal class OutletNode : NodeData
    {
        public override NodeType NodeType => NodeType.Outlet;

        public OutletNode(string? busBarName) 
        {
            TypeCode = "L";
            Name = "Outlet";
            OpenStyle = "triangle";
            CloseStyle = "triangle";
            SizeHight = 30;
            SizeWidth = 30;
            SourceType = "transformer";
            Category = "lineAngle";
            BusBarId = busBarName;
        }

        [JsonProperty("busBarId", NullValueHandling = NullValueHandling.Ignore)]
        public string? BusBarId { get; set; }
    }
}
