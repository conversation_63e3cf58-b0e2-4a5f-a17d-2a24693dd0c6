﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Quartz;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.UDC;
using Siemens.PanelManager.Server.Asset;
using Siemens.PanelManager.Server.DataPoint;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.Mqtt
{
    [DisallowConcurrentExecution]
    public class ExternalMqttJob : JobBase
    {
        private readonly ILogger _logger;
        private readonly IServiceProvider _provider;
        private readonly SiemensCache _cache;
        private readonly ExternalMqttAction _action;
        private const string MqttConfigCycleKey = "MqttDataConfig-{0}";
        private const string AssetMqttConfigKey = "AssetMqttConfig-{0}";
        private const string AssetAllDataCacheKey = "AssetStatus:All-{0}";
        private const string AssetSimpleInfoCacheKey = "Asset:SimpleInfo-{0}";
        private const string MqttJobFlagKey = "Flag:ExternalMqttJob-{0}";
        private const int AutoStopCount = 5;
        private readonly AssetExtendServer _assetExtendServer;

        public override string Name => "ExternalMqttJob";

        public ExternalMqttJob(ILogger<ExternalMqttJob> logger, IServiceProvider provider, SiemensCache cache, ExternalMqttAction action)
        {
            _assetExtendServer = provider.GetRequiredService<AssetExtendServer>();
            _logger = logger;
            _provider = provider;
            _cache = cache;
            _action = action;
        }

        public override async Task Execute()
        {
            if (ContextData.TryGetValue("Cycle", out var cycleStr) && int.TryParse(cycleStr, out var cycle))
            {
                var hashDatas = _cache.GetHashAllData(string.Format(MqttConfigCycleKey, cycle));
                var assetIdList = hashDatas.Keys.ToList();

                var dataPointServer = _provider.GetRequiredService<DataPointServer>();

                int count = 0;
                foreach (var assetIdStr in assetIdList)
                {
                    int assetId;
                    if (!int.TryParse(assetIdStr, out assetId))
                    {
                        continue;
                    }

                    var assetSimpleInfo = _cache.Get<AssetSimpleInfo>(string.Format(AssetSimpleInfoCacheKey, assetId));
                    if (assetSimpleInfo == null) continue;

                    if (!await _assetExtendServer.GetAssetEnableStatus(assetId)) continue;

                    var dataPoints = await dataPointServer.GetDataPointInfos(assetSimpleInfo.AssetLevel, assetSimpleInfo.AssetType, assetSimpleInfo.AssetModel, assetId);

                    var configs = await GetMqttDataPointConfigs(assetId, cycle);
                    if (configs == null || configs.Count == 0) continue;
                    count++;

                    var keys = configs.Where(c => !string.IsNullOrEmpty(c.Code)).Select(c => c.Code ?? string.Empty).ToArray();
                    var currentDatas = _cache.GetHashData(string.Format(AssetAllDataCacheKey, assetId), keys);

                    var ts = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:sszzz");
                    var mqttData = new DeviceDataPointsResult()
                    {
                        ItemId = assetIdStr,
                        ItemName = assetSimpleInfo.AssetName,
                        TimeStamp = ts,
                        DataType = "MeasureData",
                        Count = currentDatas.Count,
                        Total = currentDatas.Count
                    };

                    foreach (var dataPoint in currentDatas)
                    {
                        var config = configs.FirstOrDefault(c => dataPoint.Key.Equals(c.Code));
                        if (config == null) continue;

                        var dataPointInfo = dataPoints.FirstOrDefault(d => d.Code == dataPoint.Key);

                        var dataPointItem = new DeivceDataPointItem()
                        {
                            Id = -1,
                            InternalName = dataPointInfo == null ? config.Code : dataPointInfo.UdcCode ?? config.Code,
                            DisplayName = config.Name,
                            DisplayValue = "inactive",
                            Value = dataPoint.Value,
                            Unit = dataPointInfo?.Unit ?? string.Empty,
                            Quality = "valid",
                            TimeStamp = ts
                        };

                        mqttData.Embedded.Items.Add(dataPointItem);
                    }

                    if (mqttData.Embedded.Items.Count > 0)
                    {
                        _action.SendDataAsync(string.Empty, JsonConvert.SerializeObject(mqttData));
                    }
                }

                if (JobKey != null)
                {
                    if (count > 0)
                    {
                        _cache.Clear(string.Format(MqttJobFlagKey, JobKey.Name));
                    }
                    else
                    {
                        var flag = _cache.Get<int>(string.Format(MqttJobFlagKey, JobKey.Name));
                        if (flag > AutoStopCount)
                        {
                            await _action.RemoveCycleJob(cycle);
                        }
                        else
                        {
                            flag++;
                            _cache.Set(string.Format(MqttJobFlagKey, JobKey.Name), flag);
                        }
                    }
                }
            }
        }

        private async Task<List<AssetMqttDataPointConfig>> GetMqttDataPointConfigs(int assetId, int cycle)
        {
            var result = await _cache.GetOrCreateAsync<List<AssetMqttDataPointConfig>>(string.Format(AssetMqttConfigKey, assetId), async () =>
            {
                using var sqlClient = _provider.GetRequiredService<ISqlSugarClient>();
                var mqttConfigs = await sqlClient.Queryable<AssetMqttDataPointConfig>()
                    .InnerJoin<AssetMqttDataPointConfig>((c1, c2) => c1.AssetId == c2.AssetId && c1.Name == c2.GroupName)
                    .InnerJoin<AssetInfo>((c1, c2, a) => c1.AssetId == a.Id)
                    .Where((c1, c2, a) => c1.ConfigType == GroupConfigType.Group
                        && c1.AssetId == assetId
                        && c2.SamplingPeriod == 0)
                    .Select((c1, c2, a) => new AssetMqttDataPointConfig()
                    {
                        AssetId = c2.AssetId,
                        Name = c2.Name,
                        Code = c2.Code,
                        ConfigType = c2.ConfigType,
                        CreatedBy = c2.CreatedBy,
                        CreatedTime = c2.CreatedTime,
                        AssetModel = c2.AssetModel,
                        GroupName = c2.GroupName,
                        SamplingPeriod = c1.SamplingPeriod,
                        Id = c2.Id,
                        UpdatedBy = c2.UpdatedBy,
                        UpdatedTime = c2.UpdatedTime,
                    })
                    .ToListAsync();

                return mqttConfigs;
            }, TimeSpan.FromHours(1));

            result = result.Where(c => c.SamplingPeriod == cycle).ToList();

            return result;
        }
    }
}
