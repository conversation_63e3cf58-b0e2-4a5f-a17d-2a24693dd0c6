﻿using System;
using System.Collections.Generic;

namespace Siemens.PanelManager.SyncService.UdcModels
{
    public partial class ArchiveDataFormatCustom
    {
        public string ObjectId { get; set; } = null!;
        public long Archive { get; set; }
        public long TimestampInS { get; set; }
        public string? Dp1 { get; set; }
        public string? Dp2 { get; set; }
        public string? Dp3 { get; set; }
        public string? Dp4 { get; set; }
        public string? Dp5 { get; set; }
        public string? Dp6 { get; set; }
        public string? Dp7 { get; set; }
        public string? Dp8 { get; set; }
        public string? Dp9 { get; set; }
        public string? Dp10 { get; set; }
        public string? Dp11 { get; set; }
        public string? Dp12 { get; set; }
        public string? Dp13 { get; set; }
        public string? Dp14 { get; set; }
        public string? Dp15 { get; set; }
        public string? Dp16 { get; set; }
        public string? Dp17 { get; set; }
        public string? Dp18 { get; set; }
        public string? Dp19 { get; set; }
        public string? Dp20 { get; set; }
        public string? Dp21 { get; set; }
        public string? Dp22 { get; set; }
        public string? Dp23 { get; set; }
        public string? Dp24 { get; set; }
        public string? Dp25 { get; set; }
        public string? Dp26 { get; set; }
        public string? Dp27 { get; set; }
        public string? Dp28 { get; set; }
        public string? Dp29 { get; set; }
        public string? Dp30 { get; set; }
        public string? Dp31 { get; set; }
        public string? Dp32 { get; set; }
        public string? Dp33 { get; set; }
        public string? Dp34 { get; set; }
        public string? Dp35 { get; set; }
        public string? Dp36 { get; set; }
        public string? Dp37 { get; set; }
        public string? Dp38 { get; set; }
        public string? Dp39 { get; set; }
        public string? Dp40 { get; set; }
        public string? Dp41 { get; set; }
        public string? Dp42 { get; set; }
        public string? Dp43 { get; set; }
        public string? Dp44 { get; set; }
        public string? Dp45 { get; set; }
        public string? Dp46 { get; set; }
        public string? Dp47 { get; set; }
        public string? Dp48 { get; set; }
        public string? Dp49 { get; set; }
        public string? Dp50 { get; set; }
        public string? Dp51 { get; set; }
        public string? Dp52 { get; set; }
        public string? Dp53 { get; set; }
        public string? Dp54 { get; set; }
        public string? Dp55 { get; set; }
        public string? Dp56 { get; set; }
        public string? Dp57 { get; set; }
        public string? Dp58 { get; set; }
        public string? Dp59 { get; set; }
        public string? Dp60 { get; set; }
        public string? Dp61 { get; set; }
        public string? Dp62 { get; set; }
        public string? Dp63 { get; set; }
        public string? Dp64 { get; set; }
        public string? Dp65 { get; set; }
        public string? Dp66 { get; set; }
        public string? Dp67 { get; set; }
        public string? Dp68 { get; set; }
        public string? Dp69 { get; set; }
        public string? Dp70 { get; set; }
        public string? Dp71 { get; set; }
        public string? Dp72 { get; set; }
        public string? Dp73 { get; set; }
        public string? Dp74 { get; set; }
        public string? Dp75 { get; set; }
        public string? Dp76 { get; set; }
        public string? Dp77 { get; set; }
        public string? Dp78 { get; set; }
        public string? Dp79 { get; set; }
        public string? Dp80 { get; set; }
        public string? Dp81 { get; set; }
        public string? Dp82 { get; set; }
        public string? Dp83 { get; set; }
        public string? Dp84 { get; set; }
        public string? Dp85 { get; set; }
        public string? Dp86 { get; set; }
        public string? Dp87 { get; set; }
        public string? Dp88 { get; set; }
        public string? Dp89 { get; set; }
        public string? Dp90 { get; set; }
        public string? Dp91 { get; set; }
        public string? Dp92 { get; set; }
        public string? Dp93 { get; set; }
        public string? Dp94 { get; set; }
        public string? Dp95 { get; set; }
        public string? Dp96 { get; set; }
        public string? Dp97 { get; set; }
        public string? Dp98 { get; set; }
        public string? Dp99 { get; set; }
        public string? Dp100 { get; set; }
    }
}
