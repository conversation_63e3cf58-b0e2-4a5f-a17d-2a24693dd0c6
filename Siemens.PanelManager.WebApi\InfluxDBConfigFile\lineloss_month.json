{"name": "lineloss_month", "status": "active", "flux": "import \"date\"\n\noption task = {\n    name: \"lineloss_month\",\n    every: 1mo,\n}\n\nfrom(bucket: \"panel\")\n    |> range(start: -1mo)\n    |> filter(fn: (r) => r[\"_measurement\"] == \"lineloss_day\")\n    |> filter(fn: (r) => r[\"_field\"] == \"mcurrent\")\n    |> mean()\n    |> map(fn: (r) => ({r with _measurement: \"lineloss_month\", _time: date.truncate(t: now(), unit: 1d)}))\n    |> to(bucket: \"panel\", tagColumns: [\"topology_id\", \"line_id\"])\n\nfrom(bucket: \"panel\")\n    |> range(start: -1mo)\n    |> filter(fn: (r) => r[\"_measurement\"] == \"lineloss_day\")\n    |> filter(fn: (r) => r[\"_field\"] == \"sump\")\n    |> sum()\n    |> map(fn: (r) => ({r with _measurement: \"lineloss_month\", _time: date.truncate(t: now(), unit: 1d)}))\n    |> to(bucket: \"panel\", tagColumns: [\"topology_id\", \"line_id\"])\n\nfrom(bucket: \"panel\")\n    |> range(start: -1mo)\n    |> filter(fn: (r) => r[\"_measurement\"] == \"lineloss_day\")\n    |> filter(fn: (r) => r[\"_field\"] == \"sumq\")\n    |> sum()\n    |> map(fn: (r) => ({r with _measurement: \"lineloss_month\", _time: date.truncate(t: now(), unit: 1d)}))\n    |> to(bucket: \"panel\", tagColumns: [\"topology_id\", \"line_id\"])", "every": "1mo"}