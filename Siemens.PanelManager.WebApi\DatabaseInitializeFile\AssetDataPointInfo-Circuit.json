[{"Code": "HaveAlarm", "Name": "HaveAlarm", "GroupName": "Status", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "Alarm", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 1, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Ua", "Name": "Ua", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "V", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 2, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ub", "Name": "Ub", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "V", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 3, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Uc", "Name": "Uc", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "V", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 4, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Uab", "Name": "Uab", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "V", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 5, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ubc", "Name": "Ubc", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "V", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 6, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Uca", "Name": "Uca", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "V", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 7, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ia", "Name": "Ia", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "A", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 8, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ib", "Name": "Ib", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "A", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ic", "Name": "Ic", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "A", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 10, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "I_Agv", "Name": "I_Agv", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "A", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 12, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "P", "Name": "P", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "W", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 13, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Pa", "Name": "Pa", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "W", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 14, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Pb", "Name": "Pb", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "W", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 15, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Pc", "Name": "Pc", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "W", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 16, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Q", "Name": "Q", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "var", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 17, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Qa", "Name": "Qa", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "var", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 18, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Qb", "Name": "Qb", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "var", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Qc", "Name": "Qc", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "var", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 20, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "S", "Name": "S", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "VA", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 21, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "F", "Name": "F", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "Hz", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 22, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor", "Name": "PowFactor", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 23, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ua", "Name": "THD_Ua", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "%", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 24, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ub", "Name": "THD_Ub", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "%", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 25, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Uc", "Name": "THD_Uc", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "%", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 26, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_U", "Name": "THD_U", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "%", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 28, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ia", "Name": "THD_Ia", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "%", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 29, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ib", "Name": "THD_Ib", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "%", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 30, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ic", "Name": "THD_Ic", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "%", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 31, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_I", "Name": "THD_I", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "%", "CollectMode": "CommonCircuit", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 33, "ParentName": "Measurement", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Switch", "Name": "Switch", "GroupName": "Status", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "BreakerOrExtend", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 42, "ParentName": "Status", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Alarm_Severity", "Name": "Alarm_Severity", "GroupName": "Warnings", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Alarm/Value/AlarmSeverity", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 43, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Status", "Name": "Alarm_Status", "GroupName": "Warnings", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Alarm/Value/AlarmStatus", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 44, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Path", "Name": "Alarm_Path", "GroupName": "Warnings", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Alarm/Value/AlarmPath", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 45, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Info", "Name": "Alarm_Info", "GroupName": "Warnings", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Alarm/Value/AlarmInfo", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 46, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Time", "Name": "Alarm_Time", "GroupName": "Warnings", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "Alarm/Value/AlarmTime", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 47, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "CircuitNextBreakerName", "Name": "CircuitNextBreakerName", "GroupName": "Others", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "CircuitNextBreakerName", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 48, "ParentName": "Others"}, {"Code": "CircuitNextBreakerStatus", "Name": "CircuitNextBreakerStatus", "GroupName": "Others", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "CircuitNextBreakerStatus", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 49, "ParentName": "Others"}, {"Code": "CircuitNextMeterName", "Name": "CircuitNextMeterName", "GroupName": "Others", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "CircuitNextMeterName", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 49, "ParentName": "Others"}, {"Code": "CircuitNextMeterStatus", "Name": "CircuitNextMeterStatus", "GroupName": "Others", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "CircuitNextMeterStatus", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 49, "ParentName": "Others"}, {"Code": "APhaseTemp1", "Name": "APhaseTemp1", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "Extend": "", "CanPrint": true, "Sort": 34, "ParentName": "Measurement"}, {"Code": "BPhaseTemp1", "Name": "BPhaseTemp1", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 35, "ParentName": "Measurement"}, {"Code": "CPhaseTemp1", "Name": "CPhaseTemp1", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 36, "ParentName": "Measurement"}, {"Code": "NPhaseTemp1", "Name": "NPhaseTemp1", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 37, "ParentName": "Measurement"}, {"Code": "APhaseTemp2", "Name": "APhaseTemp2", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 38, "ParentName": "Measurement"}, {"Code": "BPhaseTemp2", "Name": "BPhaseTemp2", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 39, "ParentName": "Measurement"}, {"Code": "CPhaseTemp2", "Name": "CPhaseTemp2", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 40, "ParentName": "Measurement"}, {"Code": "NPhaseTemp2", "Name": "NPhaseTemp2", "GroupName": "Measurement", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "archivedatarealtime", "SaveFunc": null, "CanPrint": true, "Extend": "", "Sort": 41, "ParentName": "Measurement"}, {"Code": "HasTriped", "Name": "HasTriped", "GroupName": "Status", "AssetLevel": 40, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "BreakerOrExtend", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "ParentName": "Status", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}]