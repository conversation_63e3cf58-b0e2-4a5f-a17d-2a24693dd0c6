﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class CurrentlyTemperatureInfo
    {
        public string Level { get; set; } = string.Empty;
        public string? Name { get; set; }
        [JsonIgnore]
        public string Code { get; set; } = string.Empty;
        public List<CurrentlyTemperatureInfo>? SubItems { get; set; }
        public List<CurrentlyTemperatureItem>? DataList { get; set; }
        public List<TemperatureDeviceStatus>? UnknownList { get; set; }
    }

    public class CurrentlyTemperatureItem
    {
        public int? Index { get; set; }
        public TemperatureDeviceStatus? APhase { get; set; }
        public TemperatureDeviceStatus? BPhase { get; set; }
        public TemperatureDeviceStatus? CPhase { get; set; }
        public TemperatureDeviceStatus? NPhase { get; set; }
        public int? Number { get; set; }
    }
}
