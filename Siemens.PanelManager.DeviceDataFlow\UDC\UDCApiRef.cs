﻿using Akka.Actor;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.UDC;

namespace Siemens.PanelManager.DeviceDataFlow.UDC
{
    public class UDCApiRef : IUDCApiRef
    {
        private IActorRef _udcApiRef;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="refObj"></param>
        public UDCApiRef(IActorRef refObj)
        {
            _udcApiRef = refObj;
        }

        public async Task<UdcMessageModelListResult?> GetBreakerTrips(string objectId, int count = 5, string? oid = null)
        {
            if (string.IsNullOrEmpty(objectId) || count <= 0)
                return null;

            var result = await _udcApiRef.Ask(new GetBreakerTripsParam
            {
                ObjectId = objectId,
                PageCount = count,
                Oid = oid
            });

            if (result is Exception ex)
            {
                return null;
            }
            if (result is UdcMessageModelListResult listResult)
            {
                return listResult;
            }
            return null;
        }

        public async Task<DeviceDataPointsResult?> GetDataPoints(string objectId, string[] internalNames)
        {
            if (string.IsNullOrEmpty(objectId) || internalNames.Length == 0)
                return null;

            var result = await _udcApiRef.Ask(new GetDataPointsParam
            {
                ObjectId = objectId,
                InternalNames = internalNames
            }, TimeSpan.FromSeconds(30));

            if (result is Exception ex)
            {
                return null;
            }
            if (result is DeviceDataPointsResult dataPointsResult)
            {
                return dataPointsResult;
            }
            return null;
        }

        public async Task<UdcMessageModelListResult?> GetDeviceMessages(string objectId, string? oid = null, int count = 100)
        {
            if (string.IsNullOrEmpty(objectId) || count <= 0)
                return null;

            var result = await _udcApiRef.Ask(new GetDeviceMessageParams
            {
                ObjectId = objectId,
                Oid = oid,
                Count = count
            }, TimeSpan.FromSeconds(30));
            if (result is Exception)
            {
                return null;
            }
            if (result is UdcMessageModelListResult listResult)
            {
                return listResult;
            }
            return null;
        }

        public async Task<DeviceItemsResult?> GetItems()
        {
            var result = await _udcApiRef.Ask(new GetItemsParam(), TimeSpan.FromSeconds(5));

            if (result is Exception ex)
            {
                return null;
            }
            if (result is DeviceItemsResult itemsResult)
            {
                return itemsResult;
            }
            return null;
        }

        public async Task<UdcMessageModel?> GetMessageByOid(string objectId, int oid)
        {
            if (string.IsNullOrEmpty(objectId) || oid <= 0)
                return null;

            var result = await _udcApiRef.Ask(new GetMessageParams
            {
                ObjectId = objectId,
                Oid = oid
            });

            if (result is Exception ex)
            {
                return null;
            }
            if (result is UdcMessageModel messageModel)
            {
                return messageModel;
            }
            return null;
        }

        public async Task<bool> ImportProject(Stream stream)
        {
            if (stream == null)
            {
                return false;
            }

            var result = await _udcApiRef.Ask(new ImportProjectParam
            {
                Stream = stream
            }, TimeSpan.FromMinutes(5));

            if (result is Exception)
            {
                //_logger.LogError("UDCApiRef_ImportProject异常信息:" + result);

                return false;
            }

            if (result is bool boolResult)
            {
                return boolResult;
            }

            return false;
        }

        public async Task<UdcLicenses?> GetUdcLicensesAsync()
        {
            var result = await _udcApiRef.Ask(new GetUdcLicensesParams());

            if (result is Exception ex)
            {
                return null;
            }
            if (result is UdcLicenses licensesModel)
            {
                return licensesModel;
            }
            return null;
        }
    }
}
