﻿using Siemens.PanelManager.Model.Topology3D;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.UploadExcel._3D.Model
{
    /// <summary>
    /// 柜子3D模型的简化模型
    /// </summary>
    internal class SingalCabinetNode : NodeBase3D
    {
        public override string NodeType => "panel";
        public SingalCabinetNode(string id, Position position, Size size) 
        {
            Type = NodeType;
            Id = id;
            Position.X = position.X;
            Position.Y = position.Y;
            Position.Z = position.Z;
            Size.Width = size.Width;
            Size.Height = size.Height;
            UserData = null;
        }

        public SingalCabinetNode(string id, string nodeType, Position position, Size size)
        {
            Type = nodeType;
            Id = id;
            Position.X = position.X;
            Position.Y = position.Y;
            Position.Z = position.Z;
            Size.Width = size.Width;
            Size.Height = size.Height;
            UserData = null;
        }
    }
}
