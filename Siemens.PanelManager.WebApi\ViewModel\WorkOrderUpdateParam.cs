﻿namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class WorkOrderUpdateParam
    {
        public int Id { get; set; }
        public string Device { get; set; } = string.Empty;
        public string WorkOrderType { get; set; } = string.Empty;
        public string AssetType { get; set; } = string.Empty;
        public DateTime ProcessingTime { get; set; }
        public DateTime ProcessingEndTime { get; set; }
        public List<WorkOrderContentModel> Contents { get; set; } = new List<WorkOrderContentModel>();
    }
}
