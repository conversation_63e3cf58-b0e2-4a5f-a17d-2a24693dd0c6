﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Siemens.PanelManager.Common;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.Topology;
using Siemens.PanelManager.Model.Topology3D;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.Server.Topology;
using Siemens.PanelManager.Server.Topoplogy;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;
using System.Text;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v2/topoplogy")]
    [ApiController]
    public class TopologyFor3DController : SiemensApiControllerBase
    {
        private const string ReceiptDataCacheKey = "ReceiptData:{0}";
        private ILogger<TopologyFor3DController> _log;
        private ISqlSugarClient _client;
        private IServiceProvider _provider;
        private SiemensCache _cache;
        private AlarmExtendServer _alarmExtendServer => _provider.GetRequiredService<AlarmExtendServer>();
        public TopologyFor3DController(
            IServiceProvider provider,
            SiemensCache cache,
            SqlSugarScope client,
            ILogger<TopologyFor3DController> log) : base(provider, cache)
        {
            _cache = cache;
            _log = log;
            _client = client;
            _provider = provider;
        }

        [HttpPut("3D")]
        [SwaggerOperation(Summary = "Swagger_Topology_Update", Description = "Swagger_Topology_Update_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<TopologySaveReceipt>> Update([FromQuery] string flag, [FromBody] JObject topoplogData)
        {
            if (topoplogData == null
                || !topoplogData.ContainsKey("id"))
            {
                return new ResponseBase<TopologySaveReceipt>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            string topologyType = "3D".ToUpper(); ;
            var idToken = topoplogData.GetValue("id");

            if (idToken == null)
            {
                return new ResponseBase<TopologySaveReceipt>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            int topologyId = 0;
            switch (idToken.Type)
            {
                case JTokenType.String:
                    if (!int.TryParse(idToken.Value<string>(), out topologyId))
                    {
                        return new ResponseBase<TopologySaveReceipt>()
                        {
                            Code = 40301,
                            Message = MessageContext.ErrorParam
                        };
                    }
                    break;
                case JTokenType.Bytes:
                case JTokenType.Integer:
                    topologyId = idToken.Value<int>();
                    break;
                default:
                    return new ResponseBase<TopologySaveReceipt>()
                    {
                        Code = 40301,
                        Message = MessageContext.ErrorParam
                    };
            }

            var message = new StringBuilder();
            var receipt = new TopologySaveReceipt()
            {
            };

            await UpdateTopologyInfo(flag, topoplogData, topologyType, topologyId, message, receipt);

            if (receipt.ResultCode == 99)
            {
                return new ResponseBase<TopologySaveReceipt>()
                {
                    Code = 40301,
                    Message = message.ToString()
                };
            }

            return new ResponseBase<TopologySaveReceipt>()
            {
                Code = 20000,
                Data = receipt
            };
        }

        /// <summary>
        /// 更新3D图方法
        /// 先验证 flag 
        /// </summary>
        /// <param name="flag">图纸的版本标识，[COVER]是强制更新</param>
        /// <param name="topoplogData">单线图数据</param>
        /// <param name="topologyType">图纸类型</param>
        /// <param name="topoplogId">图纸Id</param>
        /// <param name="message">错误信息</param>
        /// <param name="receipt">回执对象</param>
        /// <returns></returns>
        private async Task UpdateTopologyInfo(string flag, JObject topoplogData, string topologyType, int topoplogId, StringBuilder message, TopologySaveReceipt receipt)
        {
            var topologInfo = await _client.Queryable<TopologyInfo>().FirstAsync(t => t.Id == topoplogId && t.Type == topologyType);
            if (topologInfo == null)
            {
                receipt.ResultCode = 11;
                receipt.TopologyId = topoplogId;
                _cache.Set(string.Format(ReceiptDataCacheKey, receipt.ReceiptCode),
                    new TopologyReceiptData(topoplogData, flag, UserSession?.UserId ?? -1, topologyType, topoplogId, receipt.ResultCode, UserSession?.Id ?? string.Empty),
                    TimeSpan.FromMinutes(1));
            }
            else
            {
                if (!"[COVER]".Equals(flag) && !flag.Equals(topologInfo.TopologyFlag))
                {
                    receipt.ResultCode = 10;
                    receipt.TopologyId = topoplogId;
                    _cache.Set(string.Format(ReceiptDataCacheKey, receipt.ReceiptCode),
                        new TopologyReceiptData(topoplogData, flag, UserSession?.UserId ?? -1, topologyType, topoplogId, receipt.ResultCode, UserSession?.Id ?? string.Empty),
                        TimeSpan.FromMinutes(1));
                }
                else
                {
                    List<TopologyInfo> updateList = new List<TopologyInfo>();
                    var newFlag = Guid.NewGuid().ToString();
                    topologInfo.Name = topoplogData.Value<string>("name") ?? string.Empty;
                    topologInfo.Code = topoplogData.Value<string>("code") ?? string.Empty;
                    topologInfo.Description = topoplogData.Value<string>("description") ?? string.Empty;
                    topologInfo.Data = JsonConvert.SerializeObject(topoplogData.GetValue("nodes"), Formatting.None);
                    topologInfo.Maker = topoplogData.Value<string>("owner") ?? UserName;
                    topologInfo.TopologyFlag = newFlag;
                    topologInfo.UpdatedBy = UserName;
                    topologInfo.UpdatedTime = DateTime.Now;
                    updateList.Add(topologInfo);

                    if (topologInfo.CheckTopologyInfo(message, MessageContext))
                    {
                        receipt.ResultCode = 99;
                    }

                    if (receipt.ResultCode == 0)
                    {
                        // 拆分开关柜数据保存
                        if (topoplogData.TryGetValue("panelList", out JToken? cabinetListToken) && !string.IsNullOrEmpty(topologInfo.Extend))
                        {
                            var keyMappings = JsonConvert.DeserializeObject<Dictionary<string, int>>(topologInfo.Extend);
                            if (keyMappings != null)
                            {
                                var datas = cabinetListToken.ToObject<Dictionary<string, JArray>>();
                                if (datas != null)
                                {
                                    foreach (var item in keyMappings)
                                    {
                                        if (datas.TryGetValue(item.Key, out JArray? value) && value != null)
                                        {
                                            var subTopologyInfo = await _client.Queryable<TopologyInfo>().FirstAsync(t => t.Id == item.Value && t.Type == topologyType);
                                            if (subTopologyInfo != null)
                                            {
                                                subTopologyInfo.Data = JsonConvert.SerializeObject(value, Formatting.None);
                                                subTopologyInfo.TopologyFlag = Guid.NewGuid().ToString();
                                                subTopologyInfo.UpdatedBy = UserName;
                                                subTopologyInfo.UpdatedTime = DateTime.Now;
                                                updateList.Add(subTopologyInfo);
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        await _client.Updateable(updateList).ExecuteCommandAsync();
                        await _alarmExtendServer.InsertOperationLog(UserName, "UpdateTopology3D", Model.Database.Alarm.AlarmSeverity.Middle, _client, topologInfo.Name);
                        var svc = _provider.GetRequiredService<TopologyDraftService>();
                        await svc.DeleteDraftByUserId(UserSession?.UserId ?? 0, topologyType, _client);
                        receipt.ResultCode = 1;
                        receipt.Flag = newFlag;
                        receipt.TopologyId = topologInfo.Id;
                    }
                }
            }
        }

        [HttpDelete("3D/{id}")]
        [SwaggerOperation(Summary = "Swagger_Topology_Delete", Description = "Swagger_Topology_Delete_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> Delete(int id)
        {
            if (id <= 0)
            {
                return new ResponseBase<string>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            string topoplogType = "3D".ToUpper();
            var existItem = await _client.Queryable<TopologyInfo>().Select(t => new TopologyInfo()
            {
                Id = t.Id,
                Name = t.Name,
                Code = t.Code,
                Description = t.Description
            }).FirstAsync(t => t.Id == id && t.Type == topoplogType);
            if (existItem == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.GetErrorValue("Common_NotExists")
                };
            }

            var server = _provider.GetRequiredService<TopologyExtendFunc>();
            await server.DeleteTopologyByTopologyId(id, _client);
            await _alarmExtendServer.InsertOperationLog(UserName, "DeleteTopology3D", Model.Database.Alarm.AlarmSeverity.Middle, _client, existItem.Name);
            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = MessageContext.Success
            };
        }

        [HttpPost("3D")]
        [SwaggerOperation(Summary = "Swagger_Topology_Add", Description = "Swagger_Topology_Add_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<TopologySaveReceipt>> Add(JObject topoplogData)
        {
            string topoplogType = "3D".ToUpper();
            if (topoplogData == null)
            {
                return new ResponseBase<TopologySaveReceipt>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }

            var receipt = new TopologySaveReceipt()
            {
                ResultCode = 1
            };
            var message = new StringBuilder();
            await AddTopologyInfo(topoplogData, topoplogType, receipt, message);
            if (receipt.ResultCode == 99)
            {
                return new ResponseBase<TopologySaveReceipt>()
                {
                    Code = 40301,
                    Message = message.ToString()
                };
            }
            return new ResponseBase<TopologySaveReceipt>()
            {
                Code = 20000,
                Data = receipt,
            };
        }

        private async Task AddTopologyInfo(JObject topoplogData, string topoplogType, TopologySaveReceipt receipt, StringBuilder message)
        {
            var topoplogInfo = new TopologyInfo();
            JToken? token = null;
            if (topoplogData.TryGetValue("name", out token))
            {
                topoplogInfo.Name = token.ToString();
            }
            else
            {
                topoplogInfo.Name = string.Empty;
            }

            if (topoplogData.TryGetValue("code", out token))
            {
                topoplogInfo.Code = token.ToString();
            }
            else
            {
                topoplogInfo.Code = string.Empty;
            }

            if (topoplogData.TryGetValue("description", out token))
            {
                topoplogInfo.Description = token.ToString();
            }
            else
            {
                topoplogInfo.Description = string.Empty;
            }
            var flag = Guid.NewGuid().ToString();
            topoplogInfo.Data = JsonConvert.SerializeObject(topoplogData.GetValue("nodes"), Formatting.None);
            topoplogInfo.Type = topoplogType;
            topoplogInfo.TopologyFlag = flag;
            topoplogInfo.CreatedBy = UserName;
            topoplogInfo.CreatedTime = DateTime.Now;
            topoplogInfo.UpdatedBy = UserName;
            topoplogInfo.UpdatedTime = DateTime.Now;

            if (topoplogInfo.CheckTopologyInfo(message, MessageContext))
            {
                receipt.ResultCode = 99;
            }

            if (receipt.ResultCode != 99)
            {
                var newId = await _client.Insertable(topoplogInfo).ExecuteReturnIdentityAsync();
                receipt.TopologyId = newId;
                receipt.Flag = flag;
                await _alarmExtendServer.InsertOperationLog(UserName, "AddTopology3D", Model.Database.Alarm.AlarmSeverity.Middle, _client, topoplogInfo.Name);
                var svc = _provider.GetRequiredService<TopologyDraftService>();
                await svc.DeleteDraftByUserId(UserSession?.UserId ?? 0, topoplogType, _client);
            }
        }

        [HttpGet("3D")]
        [SwaggerOperation(Summary = "Swagger_Topology_GetAll", Description = "Swagger_Topology_GetAll_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<TopologySingleInfo[]>> GetAll([FromQuery] string? filter)
        {
            string topoplogType = "3D".ToUpper();


            List<int> substationIds = new List<int>();

            try
            {
                if (!string.IsNullOrWhiteSpace(filter) && filter.Contains("Substation", StringComparison.OrdinalIgnoreCase))
                {
                    var tempIds = await _client.Queryable<AssetInfo>()
                        .Where(a => a.AssetLevel == AssetLevel.Substation)
                        .Select(a => a.Topology3DId)
                        .ToListAsync();

                    tempIds?.ForEach(a =>
                    {
                        substationIds.Add(a != null && a.HasValue ? a.GetValueOrDefault() : 0);
                    });
                }
            }
            catch
            {

            }


            var topoplogyInfoes = await _client.Queryable<TopologyInfo,  TopologyIdManager>((t, idMapping) => new JoinQueryInfos(JoinType.Left, t.Id == idMapping.Key && idMapping.Type == "3D-Dashboard:Main"))
                .Where((t, idMapping) => t.Type == topoplogType)
                .WhereIF(substationIds != null && substationIds.Any(), (t, idMapping) => SqlFunc.ContainsArray(substationIds, t.Id))
                .Select((t, idMapping) => new TopologySingleInfo()
                {
                    Id = t.Id,
                    Name = t.Name,
                    Code = t.Code,
                    Time = t.CreatedTime,
                    Owner = string.IsNullOrEmpty(t.Maker) ? t.CreatedBy : t.Maker,
                    Description = t.Description,
                    DashboardFirst = idMapping.Id > 0
                })
                .OrderByDescending(t => t.Id)
                .ToArrayAsync();

            return new ResponseBase<TopologySingleInfo[]>()
            {
                Code = 20000,
                Data = topoplogyInfoes.ToArray()
            };
        }

        [HttpGet("3D/{id}")]
        [SwaggerOperation(Summary = "Swagger_Topology_Get", Description = "Swagger_Topology_Get_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<Topology3D>> Get(int id)
        {
            if (id <= 0)
            {
                return new ResponseBase<Topology3D>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            string topoplogType = "3D".ToUpper();
            JObject data = new JObject();
            var topoplogyInfo = await _client.Queryable<TopologyInfo>().FirstAsync(t => t.Id == id && t.Type == topoplogType);

            if (topoplogyInfo == null)
            {
                return new ResponseBase<Topology3D>()
                {
                    Code = 40400,
                    Message = MessageContext.GetErrorValue("Asset_Topology3D_NotExists")
                };
            }

            Topology3D model = new Topology3D();
            model.Id = topoplogyInfo.Id;
            model.NodeObject = JArray.Parse(topoplogyInfo.Data);
            model.Name = topoplogyInfo.Name;
            model.Code = topoplogyInfo.Code;
            model.Description = topoplogyInfo.Description;
            model.Time = topoplogyInfo.CreatedTime;
            model.Owner = topoplogyInfo.Maker;
            model.Flag = topoplogyInfo.TopologyFlag;
            if (!string.IsNullOrEmpty(topoplogyInfo.Extend))
            {
                try
                {
                    model.PanelList = JsonConvert.DeserializeObject<Dictionary<string, JToken>>(topoplogyInfo.Extend);
                }
                catch (Exception ex)
                {
                    _log.LogError(ex, $"拓扑数据解析有问题{topoplogyInfo.Id}");
                }
            }

            if (model.PanelList != null && model.PanelList.Keys.Count > 0)
            {
                foreach (var item in model.PanelList)
                {
                    if (item.Value.Type == JTokenType.Integer)
                    {
                        var subId = item.Value.Value<int>();
                        var subInfo = await _client.Queryable<TopologyInfo>().FirstAsync(t => t.Id == subId && t.Type == topoplogType);
                        if (subInfo != null)
                        {
                            var array = JArray.Parse(subInfo.Data);

                            if (array != null)
                            {
                                var newArray = new JArray();
                                foreach (var tokenItem in array)
                                {
                                    var nodeType = tokenItem.Value<string>("type");
                                    if ("panel".Equals(nodeType) || "shell".Equals(nodeType))
                                    {
                                        newArray.Add(tokenItem);
                                        break;
                                    }
                                }

                                model.PanelList[item.Key] = newArray;
                            }
                        }
                    }
                }
            }

            return new ResponseBase<Topology3D>()
            {
                Code = 20000,
                Data = model
            };
        }


        [HttpPost("3D/dashboard/{id}")]
        [SwaggerOperation(Summary = "Swagger_Topology_UpdateMainDashboard", Description = "Swagger_Topology_UpdateMainDashboard_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> UpdateMainDashboard(int id, UpdateMainDashboardParam param)
        {
            if (id <= 0 || param == null || !param.DashboardFirst.HasValue)
            {
                return new ResponseBase<string>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            string topoplogType = "3D";

            var existItem = await _client.Queryable<TopologyInfo>().Select(t => new TopologyInfo()
            {
                Id = t.Id,
                Name = t.Name,
                Code = t.Code,
                Description = t.Description
            }).FirstAsync(t => t.Id == id && t.Type == topoplogType);
            if (existItem != null)
            {
                var idManagers = await _client.Queryable<TopologyIdManager>()
                    .Where(t => t.Type == "3D-Dashboard:Main")
                    .ToListAsync();
                if (param.DashboardFirst.Value)
                {
                    if (idManagers.Count == 0)
                    {
                        await _client.Insertable(new TopologyIdManager()
                        {
                            Key = id,
                            Type = "3D-Dashboard:Main",
                            CreatedBy = UserName,
                            CreatedTime = DateTime.Now,
                            UpdatedBy = UserName,
                            UpdatedTime = DateTime.Now,
                        }).ExecuteCommandAsync();
                    }
                    else
                    {
                        try
                        {
                            _client.Ado.BeginTran();
                            idManagers[0].Key = id;
                            idManagers[0].UpdatedBy = UserName;
                            idManagers[0].UpdatedTime = DateTime.Now;
                            await _client.Updateable(idManagers[0]).ExecuteCommandAsync();
                            idManagers.RemoveAt(0);
                            if (idManagers.Count > 0)
                            {
                                await _client.Deleteable(idManagers).ExecuteCommandAsync();
                            }
                            _client.Ado.CommitTran();
                        }
                        catch (Exception ex)
                        {
                            _log.LogError(ex, "修改主页标识失败");
                            _client.Ado.RollbackTran();
                            return new ResponseBase<string>()
                            {
                                Code = 50000,
                                Message = MessageContext.ServerException,
                            };
                        }
                    }
                }
                else
                {
                    var idConfig = idManagers.FirstOrDefault(i => i.Key == id);
                    if (idConfig != null)
                    {
                        await _client.Deleteable(idConfig).ExecuteCommandAsync();
                    }
                }
                await _alarmExtendServer.InsertOperationLog(UserName, "UpdateTopology", Model.Database.Alarm.AlarmSeverity.Middle, _client, existItem.Name);
                return new ResponseBase<string>()
                {
                    Code = 20000,
                    Data = MessageContext.Success
                };
            }

            return new ResponseBase<string>()
            {
                Code = 40400,
                Message = MessageContext.GetErrorValue("Common_NotExists")
            };
        }


        [HttpGet("3D/dashboard")]
        [SwaggerOperation(Summary = "Swagger_Topology_GetMainDashboard", Description = "Swagger_Topology_GetMainDashboard_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<Topology3D>> GetMainDashboard()
        {
            string topoplogType = "3D";
            var idManagers = await _client.Queryable<TopologyIdManager>()
                .InnerJoin<TopologyInfo>((t, ti) => ti.Id == t.Key)
                .Where((t, ti) => t.Type == "3D-Dashboard:Main" || t.Type == "3D-Dashboard")
                .Select((t, ti) => t)
                .ToListAsync();

            var idConfig = idManagers.FirstOrDefault(t => t.Type == "3D-Dashboard:Main");
            if (idConfig == null)
            {
                idConfig = idManagers.OrderByDescending(o => o.Id).FirstOrDefault();
            }
            TopologyInfo? topoplogyInfo = null;
            if (idConfig != null)
            {
                topoplogyInfo = await _client.Queryable<TopologyInfo>().FirstAsync(t => t.Id == idConfig.Key && t.Type == topoplogType);
            }

            if (topoplogyInfo == null)
            {
                topoplogyInfo = await _client.Queryable<TopologyInfo>().OrderByDescending(t => t.Id).FirstAsync(t => t.Type == topoplogType);
            }

            if (topoplogyInfo != null)
            {
                Topology3D model = new Topology3D();
                model.Id = topoplogyInfo.Id;
                model.NodeObject = JArray.Parse(topoplogyInfo.Data);
                model.Name = topoplogyInfo.Name;
                model.Code = topoplogyInfo.Code;
                model.Description = topoplogyInfo.Description;
                model.Time = topoplogyInfo.CreatedTime;
                model.Owner = string.IsNullOrEmpty(topoplogyInfo.Maker) ? topoplogyInfo.CreatedBy : topoplogyInfo.Maker;
                if (!string.IsNullOrEmpty(topoplogyInfo.Extend))
                {
                    try
                    {
                        model.PanelList = JsonConvert.DeserializeObject<Dictionary<string, JToken>>(topoplogyInfo.Extend);
                    }
                    catch (Exception ex)
                    {
                        _log.LogError(ex, $"拓扑数据解析有问题{topoplogyInfo.Id}");
                    }
                }

                if (model.PanelList != null && model.PanelList.Keys.Count > 0)
                {
                    foreach (var item in model.PanelList)
                    {
                        if (item.Value.Type == JTokenType.Integer)
                        {
                            var subId = item.Value.Value<int>();
                            var subInfo = await _client.Queryable<TopologyInfo>().FirstAsync(t => t.Id == subId && t.Type == topoplogType);
                            if (subInfo != null)
                            {
                                model.PanelList[item.Key] = JToken.Parse(subInfo.Data);
                            }
                        }
                    }
                }

                return new ResponseBase<Topology3D>()
                {
                    Code = 20000,
                    Data = model
                };
            }
            return new ResponseBase<Topology3D>()
            {
                Code = 20000,
            };
        }

        [HttpGet("3D/{id}/currently")]
        [SwaggerOperation(Summary = "Swagger_Topology_CurrentlyData", Description = "Swagger_Topology_CurrentlyData_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<Dictionary<string, Dictionary<string, string>>>> Currently(int id, long ts)
        {
            string topoplogType = "3D";
            if (id <= 0)
            {
                return new ResponseBase<Dictionary<string, Dictionary<string, string>>>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }

            if (!await _client.Queryable<TopologyInfo>().Where(t => t.Id == id && t.Type == topoplogType).AnyAsync())
            {
                return new ResponseBase<Dictionary<string, Dictionary<string, string>>>()
                {
                    Code = 40400,
                    Message = MessageContext.GetErrorValue("Common_NotExists")
                };
            }

            var cache = _provider.GetRequiredService<SiemensCache>();
            var topologyCodeMapCacheKey = "TopologyCodeMap:{0}";
            var topologyCodeMap = cache.Get<Dictionary<string, List<string>>>(string.Format(topologyCodeMapCacheKey, id));

            var topologyCacheKey = "TopologyCurrently:{0}";
            var value = cache.GetHashAllData(string.Format(topologyCacheKey, id));

            var result = new Dictionary<string, Dictionary<string, string>>();
            if (topologyCodeMap != null)
            {
                foreach (var kv in topologyCodeMap)
                {
                    var codes = kv.Value;
                    var data = new Dictionary<string, string>();
                    result.Add(kv.Key, data);
                    foreach (var c in codes)
                    {
                        data.TryAdd(c, string.Empty);
                    }
                }

                var r = new Regex("^\\[(?<N>[\\w|\\-|_]*)\\]\\.\\[(?<K>[\\w|\\-|_]*)\\]$");
                foreach (var kv in value)
                {
                    var match = r.Match(kv.Key);
                    if (match.Success)
                    {
                        var nodeName = match.Groups["N"].Value;
                        var key = match.Groups["K"].Value;
                        if (result.TryGetValue(nodeName, out var data))
                        {
                            if (data.ContainsKey(key))
                            {
                                data[key] = kv.Value;
                            }
                            else
                            {
                                data.Add(key, kv.Value);
                            }
                        }
                        else
                        {
                            data = new Dictionary<string, string>
                            {
                                { key, kv.Value }
                            };
                            result.Add(nodeName, data);
                        }
                    }
                    else
                    {
                        _log.LogWarning($"Topology currently data key format error: {kv.Key}");
                    }
                }
            }

            return new ResponseBase<Dictionary<string, Dictionary<string, string>>>()
            {
                Code = 20000,
                Data = result
            };
        }

        [HttpGet("3D/{topoId}/CabinetAlarm")]
        [SwaggerOperation(Summary = "Swagger_Topology_CabinetAlarm", Description = "Swagger_Topology_CabinetAlarm")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<Dictionary<string, Dictionary<string, string>>>> CabinetAlarm(int topoId)
        {
            var result = new Dictionary<string, Dictionary<string, string>>();
            var asset = await _client.Queryable<AssetInfo>().Where(a => a.Topology3DId == topoId).FirstAsync();
            if (asset == null)
            {
                return new ResponseBase<Dictionary<string, Dictionary<string, string>>>
                {
                    Code = 20000,
                    Data = new Dictionary<string, Dictionary<string, string>>()
                };
            }
            var topoplogyInfo = await _client.Queryable<TopologyInfo>().Where(a => a.Id == topoId).FirstAsync();
            if (topoplogyInfo == null)
            {
                return new ResponseBase<Dictionary<string, Dictionary<string, string>>>
                {
                    Code = 20000,
                    Data = new Dictionary<string, Dictionary<string, string>>()
                };
            }
            List<string> assetIds = new List<string>();
            if (asset.AssetLevel == AssetLevel.Substation)
            {
                assetIds = await _client.Queryable<AssetRelation>().Where(a => a.ParentId == asset.Id).Select(a => a.ChildId.ToString()).ToListAsync();

            }
            else if (asset.AssetLevel >= AssetLevel.Panel|| asset.AssetLevel < AssetLevel.Circuit)
            {
                assetIds.Add(asset.Id.ToString());
            }


            var cache = _provider.GetRequiredService<SiemensCache>();
            var topologyCacheKey = "TopologyAlarmCurrently";
            var cacheTopo = cache.Get<Dictionary<string, Dictionary<string, string>>>(topologyCacheKey);
            if (cacheTopo != null) {
                foreach (var item in cacheTopo.Keys)
                {
                    foreach (var key in cacheTopo[item]) { 
                        if(key.Key== "assetId"&& assetIds.Contains(key.Value))
                        {
                            result.Add(item, cacheTopo[item]);
                        }
                    }
                }
            }
            else
            {
                return new ResponseBase<Dictionary<string, Dictionary<string, string>>>()
                {
                    Code = 20000,
                    Data = new Dictionary<string, Dictionary<string, string>>()
                };
            }
            

            return new ResponseBase<Dictionary<string, Dictionary<string, string>>>()
            {
                Code = 20000,
                Data = result ?? new Dictionary<string, Dictionary<string, string>>()
            };


        }

        [HttpGet("3D/{id}/GetSubstationId")]
        [SwaggerOperation(Summary = "Swagger_Topology_GetSubstationId", Description = "Swagger_Topology_GetSubstationId_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<int?>> GetSubstationId(int id)
        {
            if (id <= 0)
            {
                return new ResponseBase<int?>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }

            var assetInfo = await _client.Queryable<AssetInfo>()
                .FirstAsync(t => t.Topology3DId == id);
            if (assetInfo == null)
            {
                return new ResponseBase<int?>()
                {
                    Code = 20000,
                    Data = null
                };
            }

            if (assetInfo.AssetLevel == AssetLevel.Substation)
            {
                return new ResponseBase<int?>()
                {
                    Code = 20000,
                    Data = assetInfo.Id
                };
            }

            var assetRelation = await _client.Queryable<AssetRelation>().FirstAsync(ar => ar.ChildId == assetInfo.Id); ;
            if (assetRelation == null)
            {
                return new ResponseBase<int?>()
                {
                    Code = 20000,
                    Data = assetInfo.Id
                };
            }

            return new ResponseBase<int?>()
            {
                Code = 20000,
                Data = assetRelation.ParentId
            };
        }

        [HttpPut("3D/receipt/{code}")]
        [SwaggerOperation(Summary = "Swagger_Topology_Receipt", Description = "Swagger_Topology_Receipt_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<TopologySaveReceipt>> Receipt([FromQuery] TopologySaveReceiptParam param)
        {
            var topologyType = "3D".ToUpper();
            var data = _cache.Get<TopologyReceiptData>(string.Format(ReceiptDataCacheKey, param.ReceiptCode));
            if (data == null || !data.SessionId.Equals(UserSession?.Id))
            {
                return new ResponseBase<TopologySaveReceipt>()
                {
                    Code = 40400,
                    Message = MessageContext.GetString("Error_Topology_MissReceiptCode") ?? "MissReceiptCode"
                };
            }

            if (!data.TopologyType.Equals(topologyType))
            {
                return new ResponseBase<TopologySaveReceipt>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }

            if (param.Operation == 10)
            {
                var svc = _provider.GetRequiredService<TopologyDraftService>();
                await svc.DeleteDraftByUserId(UserSession?.UserId ?? 0, topologyType, _client);
                return new ResponseBase<TopologySaveReceipt>
                {
                    Code = 20000,
                    Data = new TopologySaveReceipt
                    {
                        ResultCode = 1,
                    }
                };
            }
            var receipt = new TopologySaveReceipt();
            var message = new StringBuilder();
            if (data.ResultCode == 10)
            {
                await UpdateTopologyInfo("[COVER]", data.Data, topologyType, data.TopologyId, message, receipt);
            }
            else if (data.ResultCode == 11)
            {
                var jObj = data.Data;
                var nodesToken = data.Data.GetValue("nodes");
                if (data.Data.TryGetValue("panelList", out JToken? cabinetListToken))
                {
                    var cabinetList = cabinetListToken.ToObject<Dictionary<string, JArray>>();
                    if (cabinetList != null)
                    {
                        if (nodesToken != null && nodesToken is JArray nodesArray)
                        {
                            var panelList = new List<JObject>();
                            GetPanelList(nodesArray, panelList);

                            foreach (var key in cabinetList.Keys)
                            {
                                var nodes = cabinetList[key];
                                foreach (var nodeToken in nodes)
                                {
                                    if (nodeToken is JObject nodeObj)
                                    {
                                        var nodeTypeToken = nodeObj.GetValue("type");
                                        var typeValue = nodeTypeToken?.ToObject<string>();
                                        if (!string.IsNullOrEmpty(typeValue) && ("panel".Equals(typeValue) || "shell".Equals(typeValue)))
                                        {
                                            var nodeItem = panelList.FirstOrDefault(n => key.Equals(n.GetValue("uuid")?.ToString()));
                                            if (nodeItem != null)
                                            {
                                                UpdateJObject(nodeItem, nodeObj);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                await AddTopologyInfo(jObj, topologyType, receipt, message);
            }

            receipt.ResultCode = 1;
            return new ResponseBase<TopologySaveReceipt>
            {
                Code = 20000,
                Data = receipt
            };
        }

        private void GetPanelList(JArray nodesArray, List<JObject> panelList)
        {
            foreach (var item in nodesArray)
            {
                if (item is JObject nodeObj)
                {
                    var nodeTypeToken = nodeObj.GetValue("type");
                    var typeValue = nodeTypeToken?.ToObject<string>();
                    if (!string.IsNullOrEmpty(typeValue) && ("panel".Equals(typeValue) || "shell".Equals(typeValue)))
                    {
                        panelList.Add(nodeObj);
                    }
                    else if (!string.IsNullOrEmpty(typeValue) && "group".Equals(typeValue))
                    {
                        var nodesToken = nodeObj.GetValue("nodes");
                        if (nodesToken != null && nodesToken is JArray subNodes)
                        {
                            GetPanelList(subNodes, panelList);
                        }
                    }
                }
            }
        }

        private void UpdateJObject(JObject data, JObject data2)
        {
            var p2 = data2.Properties().ToArray();

            foreach (var p in p2)
            {
                if (!data.TryGetValue(p.Name, out _))
                {
                    data.Add(p.Name, p.Value);
                }
            }
        }
    }
}
