﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace Siemens.PanelManager.Model.Database.Auth
{
    [SugarTable("auth_user")]
    public class User : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [UniquenessAttribute]
        [SugarColumn(ColumnName = "login_name", Length = 256, IsNullable = false)]
        public string LoginName { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "user_name", Length = 256, IsNullable = false)]
        public string UserName { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "password_hash", Length = 512, IsNullable = false)]
        public string PasswordHash { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "prefix_mobile_number", Length = 10, IsNullable = true)]
        public string? PrefixMobileNumber { get; set; }
        [SugarColumn(ColumnName = "mobile_number", Length = 50, IsNullable = true)]
        public string? MobileNumber { get; set; }
        [SugarColumn(ColumnName = "email_address", Length = 512, IsNullable = true)]
        public string? EmailAddress { get; set; }
        [SugarColumn(ColumnName = "language", Length = 10)]
        public string Language { get; set; } = "zh-cn";
        [SugarColumn(ColumnName = "last_login_time", IsNullable = true)]
        public DateTime? LastLoginTime { get; set; }

        [SugarColumn(ColumnName = "is_deleted", IsNullable = false)]
        public bool IsDeleted { get; set; } = false;
        [SugarColumn(ColumnName = "need_change_pw", IsNullable = true)]
        public bool NeedChangePassword { get; set; } = false;
        [SugarColumn(ColumnName = "read_license", IsNullable = true)]
        public int ReadLicense { get; set; } = 0;
    }
}
