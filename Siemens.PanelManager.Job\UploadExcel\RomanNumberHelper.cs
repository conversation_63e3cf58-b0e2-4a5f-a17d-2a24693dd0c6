﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.UploadExcel
{
    static class RomanNumberHelper
    {
        private static readonly Dictionary<string, int> RomanNumberToInt = new()
        {
            ["Ⅰ"] = 1,
            ["Ⅱ"] = 2,
            ["Ⅲ"] = 3,
            ["Ⅳ"] = 4,
            ["Ⅴ"] = 5,
            ["Ⅵ"] = 6,
        };

        private static readonly Dictionary<int, string> IntToRomanNumber = new Dictionary<int, string>()
        {
            [1] = "Ⅰ",
            [2] = "Ⅱ",
            [3] = "Ⅲ",
            [4] = "Ⅳ",
            [5] = "Ⅴ",
            [6] = "Ⅵ",
        };

        public static int GetNo(string key)
        {
            if(!RomanNumberToInt.ContainsKey(key)) return -1;
            return RomanNumberToInt[key];
        }

        public static string GetString(int no) 
        {
            if(!IntToRomanNumber.ContainsKey(no))return string.Empty;
            return IntToRomanNumber[no];
        }
    }
}
