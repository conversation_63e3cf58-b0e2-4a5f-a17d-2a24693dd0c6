﻿using Akka.Actor;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.Model.DataFlow;

namespace Siemens.PanelManager.DeviceDataFlow.ActorRefs
{
    public class AlarmRef: IAlarmRef
    {
        private IActorRef _alarmRef;
        private IActorRef _saveRef;
        public AlarmRef(IActorRef alarmRef, IActorRef saveRef)
        {
            _alarmRef = alarmRef;
            _saveRef = saveRef;
        }

        public void AssetValueChange(AssetChangeData changeData) 
        {
            _alarmRef.Tell(changeData);
        }

        public void AlarmLogAction(AlarmLogAction action)
        {
            _alarmRef.Tell(action);
        }

        public void AppendAlarmLog(params AlarmLog[] logs)
        {
            _saveRef.Tell(logs);
        }
    }
}
