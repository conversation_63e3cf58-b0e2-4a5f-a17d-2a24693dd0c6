﻿using SqlSugar;

namespace Siemens.PanelManager.Model.Database.Algorithm
{
    [SugarTable("health_suggestion")]
    public class HealthSuggestion : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [UniquenessAttribute]
        [SugarColumn(ColumnName = "indicatorName", IsNullable = false, Length = 256)]
        public string indicatorName { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "suggestion", IsNullable = false, Length = 256)]
        public string suggestion { get; set; } = string.Empty;
    }
}
