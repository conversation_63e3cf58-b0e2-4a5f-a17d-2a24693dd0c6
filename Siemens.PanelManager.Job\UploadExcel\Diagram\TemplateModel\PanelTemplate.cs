﻿using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Job.UploadExcel.Diagram.TemplateModel.NodeTemplate;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Job;
using Siemens.PanelManager.Model.Topology;
using System.Text;

namespace Siemens.PanelManager.Job.UploadExcel.Diagram.TemplateModel
{
    /// <summary>
    /// 柜子模型模板
    /// </summary>
    internal class PanelTemplate : LineTemplateModelBase
    {
        private IncomingCircuit? _incomingCircuit = null;
        private List<CircuitTemplateModelBase> _circuits = new List<CircuitTemplateModelBase>();
        private List<NodeData> _nodes = new List<NodeData>();
        private List<LineData> _lineDatas = new List<LineData>();
        private LineData? _connectors = null;
        private int _defaultBusBarId;
        private string? _busBarName;
        private int _height = 0;
        private int _width = 0;
        private Dictionary<string, GroupKeyMappingInfo> _idTable;
        public PanelTemplate(PanelModel panelModel, StringBuilder message, Dictionary<string, GroupKeyMappingInfo> idTable)
            : this(panelModel, 0, 0, message, idTable)
        {

        }

        public int[] BusBars { get; private set; }
        public int Height => _height;
        public int Width => _width;

        public PanelTemplate(PanelModel panelModel, int x, int y, StringBuilder message, Dictionary<string, GroupKeyMappingInfo> idTable) :
            base(1, x, y, message)
        {
            _idTable = idTable;
            PanelModel = panelModel;
            var busBarList = new List<int>();
            foreach (var b in panelModel.BusBars)
            {
                var busId = RomanNumberHelper.GetNo(b);
                if (busId <= 0) continue;
                if (_defaultBusBarId == 0)
                {
                    _defaultBusBarId = busId;
                    _busBarName = RomanNumberHelper.GetString(busId);
                }
                busBarList.Add(busId);
            }
            BusBars = busBarList.ToArray();
            InitCircuits(panelModel.SubCircuits.ToArray());
        }
        public PanelModel PanelModel { get; private set; }
        public override NodeData[] NodeDatas
        {
            get
            {
                var result = new List<NodeData>();
                result.AddRange(_nodes);
                foreach (var c in _circuits)
                {
                    result.AddRange(c.NodeDatas);
                }
                return result.ToArray();
            }
        }

        public override LineData[] LineDatas
        {
            get
            {
                var result = new List<LineData>();
                result.AddRange(_lineDatas);
                foreach (var c in _circuits)
                {
                    result.AddRange(c.LineDatas);
                }
                return result.ToArray();
            }
        }

        public bool HasLink { get; private set; }
        /// <summary>
        /// 将柜子连接到母线
        /// </summary>
        /// <param name="busBarId"></param>
        /// <param name="node"></param>
        public void LinkToBusBar(int busBarId, NodeData node)
        {
            if (busBarId <= 0) return;
            node.AssetId = PanelModel.AssetInfo.Id;
            if (_defaultBusBarId != busBarId)
            {
                var busBarCircuits = _circuits.OfType<BusBarCircuit>().ToArray();
                foreach (var c in busBarCircuits)
                {
                    var connectors = c.Connectors;
                    if (connectors.Length < 2) continue;
                    var line = connectors[1];
                    if (line.From <= 0)
                    {
                        line.From = node.Key ?? -1;
                    }
                    else if (line.To <= 0)
                    {
                        line.To = node.Key ?? -1;
                    }
                }
            }
            else
            {
                if (_connectors != null)
                {
                    if (_connectors.From <= 0)
                    {
                        _connectors.From = node.Key ?? -1;
                    }
                    else if (_connectors.To <= 0)
                    {
                        _connectors.To = node.Key ?? -1;
                    }
                }

                HasLink = true;
            }
        }

        /// <summary>
        /// 统一柜子的高度
        /// </summary>
        /// <param name="height"></param>
        public void SetTotalHeight(int height)
        {
            _height = height;
            SetCircuitHeight(height);
        }

        private void SetCircuitHeight(int height)
        {
            var count = _circuits.Count;
            if (count > 1)
            {
                var intervalHeight = height / (count - 1);
                var lastHeight = height - (intervalHeight * (count - 1));
                for (var i = 0; i < count; i++)
                {
                    var itemHeight = intervalHeight * i;
                    if (i == count - 1)
                    {
                        itemHeight = height;
                    }

                    var circuit = _circuits[i];
                    var node = _nodes[i];
                    node.LocationCurrentX = X;
                    node.LocationCurrentY = Y + itemHeight;
                    circuit.SetLoction(X + 150, Y + itemHeight);
                }
            }
            else if (count == 1)
            {
                var circuit = _circuits[0];
                circuit.SetLoction(X, Y + 100);
            }
        }

        /// <summary>
        /// 初始化回路对象
        /// </summary>
        /// <param name="circuits"></param>
        /// <exception cref="CreateTemplateException"></exception>
        public void InitCircuits(CircuitModel[] circuits)
        {
            if (circuits == null || circuits.Length <= 0)
            {
                Message.AppendLine("Topology_MissCircuit");

                //Message.AppendLine($"配电柜sheet页中第{PanelModel.Index + 2}行中的{PanelModel.AssetInfo.AssetName}开关柜没有回路");

                throw new CreateTemplateException($"{PanelModel.AssetInfo.AssetName}开关柜没有回路");
            }

            var circuitTemps = new List<CircuitTemplateModelBase>();
            for (var i = 0; i < circuits.Length; i++)
            {
                try
                {
                    var circuit = circuits[i];
                    var circuitTemp = CreateCircuitNode(circuit, 0);
                    if (circuitTemp.Connectors.Length <= 0)
                    {
                        Message.AppendLine("Topology_MissDevice");

                        //Message.AppendLine($"回路sheet页中第{PanelModel.Index + 2}行中的{PanelModel.AssetInfo.AssetName}回路没有对外连接线");

                        throw new CreateTemplateException($"{circuit.AssetInfo.AssetName}回路没有对外连接线");
                    }

                    circuitTemps.Add(circuitTemp);

                }
                catch
                {
                    Message.AppendLine("Topology_MissDevice");

                    //Message.AppendLine($"回路sheet页中第{PanelModel.Index + 2}行中的{PanelModel.AssetInfo.AssetName}回路没有对外连接线");
                }
            }

            if (circuitTemps.Count == 1)
            {
                var circuitModel = circuitTemps[0];
                circuitModel.Rotation(1);
                circuitModel.SetLoction(X, Y + 100);
                _circuits.Add(circuitModel);
                _connectors = circuitModel.Connectors[0];
                _height = circuitModel.Height;
                _width = circuitModel.Width;

                SetMaxId(circuitModel.SetKey(GetCurrentId()));
            }
            else
            {
                int lastPointId = -1;
                var hight = 220;
                for (var i = 0; i < circuitTemps.Count; i++)
                {
                    try
                    {
                        var circuitModel = circuitTemps[i];
                        hight += circuitModel.Height;
                        circuitModel.SetLoction(X + 100, Y + hight);
                        var point = new PointNode()
                        {
                            Key = GetNewId(),
                            LocationX = X + 100,
                            LocationY = hight,
                        };

                        var line = new LineData()
                        {
                            Key = GetNewId(),
                            From = lastPointId,
                            To = point.Key.Value,
                            IsConnector = lastPointId < 0
                        };

                        if (line.IsConnector)
                        {
                            _connectors = line;
                        }

                        SetMaxId(circuitModel.SetKey(GetCurrentId()));

                        lastPointId = point.Key.Value;

                        var linkLine = circuitModel.Connectors.First();

                        if (linkLine.From <= 0)
                        {
                            linkLine.From = point.Key.Value;
                        }
                        else
                        {
                            linkLine.To = point.Key.Value;
                        }

                        if (_width < circuitModel.Width)
                        {
                            _width = circuitModel.Width;
                        }

                        _lineDatas.Add(line);
                        _nodes.Add(point);
                        _circuits.Add(circuitModel);
                    }
                    catch
                    {
                        Message.AppendLine("Topology_MissDevice");
                    }
                }

                if (_circuits.Count == 0)
                {
                    Message.AppendLine("Topology_MissCircuit");

                    //Message.AppendLine($"配电柜sheet页中第{PanelModel.Index + 2}行中的{PanelModel.AssetInfo.AssetName}开关柜没有回路");

                    throw new CreateTemplateException($"{PanelModel.AssetInfo.AssetName}开关柜没有回路");
                }

                _height = hight;
            }
        }

        /// <summary>
        /// 构建完成
        /// </summary>
        public void FinishLoading()
        {
            foreach (var circuit in _circuits)
            {
                circuit.FinishLoading();
            }
            var id = PanelModel.AssetInfo.Id.ToString();
            var list = new List<int>();
            var group = new GroupKeyMappingInfo()
            {
                Level = AssetLevel.Panel.ToString(),
                List = list
            };

            foreach (var n in NodeDatas)
            {
                if (!n.Key.HasValue) continue;
                list.Add(n.Key.Value);
            }
            if (_idTable.TryAdd(id, group))
            {
                _idTable[id] = group;
            }
        }

        /// <summary>
        /// 设置路径
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        public void SetLocation(int x, int y)
        {
            X = x;
            Y = y;
            SetCircuitHeight(_height);
        }

        /// <summary>
        /// 创建回路节点
        /// </summary>
        /// <param name="model"></param>
        /// <param name="direction"></param>
        /// <returns></returns>
        /// <exception cref="CreateTemplateException"></exception>
        private CircuitTemplateModelBase CreateCircuitNode(CircuitModel model, int direction)
        {
            switch (model.AssetInfo.AssetType)
            {
                case "BusCoupler":
                    {
                        var circuit = new BusBarCircuit(_busBarName, model, direction, _idTable);
                        var connectorList = circuit.Connectors;
                        if (connectorList.Length != 2)
                        {
                            throw new CreateTemplateException($"{model.AssetInfo.AssetName}回路对外连接线错误");
                        }

                        return circuit;
                    }
                case "Incoming":
                    {
                        var circuit = new IncomingCircuit(_busBarName, model, direction, _idTable);
                        if(_incomingCircuit == null)
                        {
                            _incomingCircuit = circuit;
                        }

                        var connectorList = circuit.Connectors;
                        if (connectorList.Length != 1)
                        {
                            throw new CreateTemplateException($"{model.AssetInfo.AssetName}回路对外连接线错误");
                        }

                        return circuit;
                    }
                default:
                    {
                        var circuit = new OutletCircuit(_busBarName, model, direction, _idTable);
                        var connectorList = circuit.Connectors;
                        if (connectorList.Length != 1)
                        {
                            throw new CreateTemplateException($"{model.AssetInfo.AssetName}回路对外连接线错误");
                        }

                        return circuit;
                    }
            }
        }

        public int AddTransformer(TransformerModel model, string leftOrRight)
        {
            if(_incomingCircuit == null)
            {
                return 0;
            }

            return _incomingCircuit.AddTransformerNode(model, GetNewId, leftOrRight);
        }
    }
}
