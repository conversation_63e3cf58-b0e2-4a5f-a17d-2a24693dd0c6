﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.System
{
    [SugarTable("sys_job_running_log")]
    public class JobRunningLog: IPanelDataTable
    {
        [SugarColumn(ColumnName = "Id", IsPrimaryKey = true, IsIdentity = true)]
        public long Id { get; set; }
        [SugarColumn(ColumnName = "job_name", IsNullable = false, Length = 256)]
        public string JobName { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "job_code", IsNullable = false, Length = 50)]
        public string JobCode { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "job_status", IsNullable = false)]
        public int JobStatus { get; set; }
        [SugarColumn(ColumnName = "log_time", IsNullable = false)]
        public DateTime LogTime { get; set; }
    }
}
