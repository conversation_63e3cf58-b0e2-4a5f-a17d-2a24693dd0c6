﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.InfluxDB.Helper.FluxModel
{
    internal class Limit
    {
        public uint Count { get; set; } = 20;
        public uint Offset { get; set; }

        public void AppendFlux(StringBuilder flux)
        {
            flux.AppendLine($"|> limit(n:{Count}, offset: {Offset})");
        }
    }
}
