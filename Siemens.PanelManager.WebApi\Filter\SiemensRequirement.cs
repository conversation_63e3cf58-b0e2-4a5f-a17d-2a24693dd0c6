﻿using Microsoft.AspNetCore.Authorization;

namespace Siemens.PanelManager.WebApi.Filter
{
    public class SiemensRequirement: IAuthorizationRequirement
    {
        public string Name { get; private set; }
        public bool IsSyncDeviceInfo { get; private set; }
        public SiemensRequirement(string name) 
        {
            Name = name;
            IsSyncDeviceInfo= false;
        }
        public SiemensRequirement(string name, bool isSyncDeviceInfo)
        {
            Name = name;
            IsSyncDeviceInfo = isSyncDeviceInfo;
        }
    }
}
