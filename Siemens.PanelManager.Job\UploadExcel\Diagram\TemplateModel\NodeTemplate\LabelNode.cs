﻿using Newtonsoft.Json;
using Siemens.PanelManager.Model.Topology;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.UploadExcel.Diagram.TemplateModel.NodeTemplate
{
    /// <summary>
    /// 单线图中的标签元素
    /// 自动生成是用于 回路名
    /// </summary>
    internal class LabelNode : NodeData
    {
        public override NodeType NodeType => NodeType.Label;
        public LabelNode() 
        {
            Name = "标签注释";
            TypeCode = "Label Comment";
            CloseStyle = "comment";
            SizeHight = 40;
            SizeWidth = 40;
            Category = "labelComments";
        }
        [JsonProperty("text")]
        public string Text { get; set; } = string.Empty;
        [JsonProperty("isSelected")]
        public bool IsSelected { get; set; } = true;
    }
}
