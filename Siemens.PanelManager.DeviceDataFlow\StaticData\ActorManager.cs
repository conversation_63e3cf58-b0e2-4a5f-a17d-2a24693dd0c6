﻿using Akka.Actor;
using Akka.DependencyInjection;
using Akka.Routing;
using Microsoft.Extensions.DependencyInjection;
using Siemens.PanelManager.DeviceDataFlow.DataFlowActors;
using Siemens.PanelManager.DeviceDataFlow.MessageBus;
using Siemens.PanelManager.DeviceDataFlow.UDC;
using Siemens.PanelManager.Interface.ActorRefs;

namespace Siemens.PanelManager.DeviceDataFlow.StaticData
{
    public class ActorManager
    {
        private ActorManager(ActorSystem system, IActorRef assetProxy, IActorRef alarmRef, IActorRef callbackRef, IActorRef saveRef, IServiceProvider provider)
        {
            ActorSystem = system;
            AssetProxyRef = assetProxy;
            AlarmRef = alarmRef;
            CallbackRef = callbackRef;
            AssetStatusSaveRef = saveRef;
            var props = DependencyResolver.For(system).Props<TopologyActor>().WithRouter(new RoundRobinPool(3));
            TopologyRef = system.ActorOf(props);
            props = DependencyResolver.For(system).Props<MessageBusActor>();
            MessageBusRef = system.ActorOf(props);
            MessageReceiver = new MessageReceiver(MessageBusRef);
            props = DependencyResolver.For(system).Props<UDCApiActor>();
            UDCApiRef = system.ActorOf(props);
            props = DependencyResolver.For(system).Props<AlarmLogSaveActor>();
            AlarmSaveRef = system.ActorOf(props);

            props = DependencyResolver.For(system).Props<DataPointActor>();
            DataPointRef = system.ActorOf(props);

            props = DependencyResolver.For(system).Props<AssetAllDataActor>();
            AllDataRef = system.ActorOf(props);

            props = DependencyResolver.For(system).Props<TopologyActionActor>();
            TopologyActionRef = system.ActorOf(props);
            TopologyActionRef.Tell(string.Empty);

            Task.Factory.StartNew(async () =>
            {
                await Task.Delay(100);
                var register = provider.GetRequiredService<IMessageRegister>();
                register.InitMessageAction();
            });
        }
        public ActorSystem ActorSystem { get; private set; }
        public IActorRef AssetProxyRef { get; private set; }
        public IActorRef AlarmRef { get; private set; }
        public IActorRef AlarmSaveRef { get; private set; }
        public IActorRef CallbackRef { get; private set; }
        public IActorRef TopologyRef { get; private set; }

        public IActorRef TopologyActionRef { get; private set; }

        public IActorRef AssetStatusSaveRef { get; private set; }
        public IActorRef MessageBusRef { get; private set; }
        public IActorRef UDCApiRef { get; private set; }

        public IActorRef AllDataRef { get; private set; }

        public IActorRef DataPointRef { get; private set; }

        public MessageReceiver MessageReceiver { get; private set; }

        public static void Initialization(ActorSystem system, IActorRef assetProxy, IServiceProvider provider)
        {
            var props = DependencyResolver.For(system).Props<AlarmActor>();
            var alarmRef = system.ActorOf(props);
            props = DependencyResolver.For(system).Props<CallbackActor>();
            var callbackRef = system.ActorOf(props);
            props = DependencyResolver.For(system).Props<AssetStatusSaveActor>();
            var saveRef = system.ActorOf(props);
            
            _actorManager = new ActorManager(system, assetProxy, alarmRef, callbackRef, saveRef, provider);
        }
        private static ActorManager? _actorManager = null;
        public static ActorManager GetActorManager()
        {
            if (_actorManager == null)
            {
                throw new Exception("尚未初始化");
            }
            return _actorManager;
        }

        public static ActorManager? GetActorManagerNoException()
        {
            return _actorManager;
        }

        public static ActorManager GetActorManagerAlways()
        {
            while (true)
            {
                if(_actorManager != null)
                {
                    break;
                }
                Thread.Sleep(100);
            }

            return _actorManager;
        }
    }
}
