﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Asset
{
    [SugarTable("asset_third_model_info")]
    public class ThirdModelConfigInfo : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true, IsNullable = false)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "code", Length = 50, IsNullable = false)]
        public string ThirdPartCode { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "proerty_code", Length = 256, IsNullable = false)]
        public string PropertyCode { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "proerty_name", Length = 256, IsNullable = false)]
        public string PropertyName { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "data_type", Length = 50, IsNullable = false)]
        public string DataType { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "unit", Length = 10, IsNullable = false)]
        public string Unit { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "group_name", Length = 256, IsNullable = true)]
        public string? GroupName { get; set; }
        [SugarColumn(ColumnName = "register_address", IsNullable = false)]
        public uint RegisterAddress { get; set; }
        [SugarColumn(ColumnName = "need_listen", IsNullable = false)]
        public bool NeedListen { get; set; } = true;
        [SugarColumn(ColumnName = "display", IsNullable = true)]
        public bool Display { get; set; } = true;
        [SugarColumn(ColumnName = "default_mqtt", IsNullable = false)]
        public bool DefaultMqtt { get; set; } = false;
        [SugarColumn(ColumnName = "is_bit", IsNullable = false)]
        public bool IsBit { get; set; } = false;
        [SugarColumn(ColumnName = "func_code", Length = 50, IsNullable = false)]
        public string FuncCode { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "description_english", Length = 512, IsNullable = true)]
        public string? DescriptionInEnglish { get; set; }
        [SugarColumn(ColumnName = "description_german", Length = 512, IsNullable = true)]
        public string? DescriptionInGerman { get; set; }
        [SugarColumn(ColumnName = "factor", IsNullable = true)]
        public int? Factor { get; set; }
        [SugarColumn(ColumnName = "parse_mode", IsNullable = true, Length = 50)]
        public string? ParseMode { get; set; }
        [SugarColumn(ColumnName = "intercept", IsNullable = true)]
        public decimal? Intercept { get; set; }

        [SugarColumn(ColumnName = "logic_func", IsNullable = true, Length = 50)]
        public string? LogicFunc { get; set; }
    }

    [SugarTable("asset_third_model_Bit_item")]
    public class ThirdModelBitItem : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true, IsNullable = false)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "third_model_info_id", IsNullable = false)]
        public int ConfigInfoId { get; set; }
        [SugarColumn(ColumnName = "offset", IsNullable = false)]
        public uint Offset { get; set; }
        [SugarColumn(ColumnName = "bit_name", Length = 50, IsNullable = false)]
        public string BitName { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "bit_code", Length = 50, IsNullable = false)]
        public string BitCode { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "alarm_level", IsNullable = true)]
        public int? AlarmLevel { get; set; }
        [SugarColumn(ColumnName = "event_type", IsNullable = true)]
        public int? EventType { get; set; }
    }
}
