﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Topoplogy
{
    [SugarTable("topology_split_panel_info")]
    public class SplitPanelInfo : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        /// <summary>
        /// 生成时间戳
        /// </summary>
        [SugarColumn(ColumnName = "timestamp", IsNullable = false)]
        public long Timestamp { get; set; }
        [SugarColumn(ColumnName = "topology_id", IsNullable = false)]
        public int TopologyId { get; set; }
        [SugarColumn(ColumnName = "asset_id", IsNullable = false)]
        public int AssetId { get; set; }
        /// <summary>
        /// 长度为10M
        /// </summary>
        [SugarColumn(ColumnName = "data", ColumnDataType = "varchar(10485760)", IsNullable = false)]
        public string Data { get; set; } = string.Empty;
    }
}
