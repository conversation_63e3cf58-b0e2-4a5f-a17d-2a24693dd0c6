[{"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-N", "ParentName": "", "LanguageKey": "VoltageL-N", "Sort": 1}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-N_ActualInstantaneousMeasurementValues", "ParentName": "VoltageL-N", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 2}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-N_GreatestMeasuredValues", "ParentName": "VoltageL-N", "LanguageKey": "GreatestMeasuredValues", "Sort": 3}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-N_LowestMeasuredValues", "ParentName": "VoltageL-N", "LanguageKey": "LowestMeasuredValues", "Sort": 4}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-N_AverageValues1", "ParentName": "VoltageL-N", "LanguageKey": "AverageValues1", "Sort": 5}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-N_AverageValues1_ActualInstantaneousMeasurementValues", "ParentName": "VoltageL-N_AverageValues1", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 6}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-N_AverageValues1_GreatestMeasuredValues", "ParentName": "VoltageL-N_AverageValues1", "LanguageKey": "GreatestMeasuredValues", "Sort": 7}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-N_AverageValues1_LowestMeasuredValues", "ParentName": "VoltageL-N_AverageValues1", "LanguageKey": "LowestMeasuredValues", "Sort": 8}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-N_AverageValues2", "ParentName": "VoltageL-N", "LanguageKey": "AverageValues2", "Sort": 9}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-N_AverageValues2_ActualInstantaneousMeasurementValues", "ParentName": "VoltageL-N_AverageValues2", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 10}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-N_AverageValues2_GreatestMeasuredValues", "ParentName": "VoltageL-N_AverageValues2", "LanguageKey": "GreatestMeasuredValues", "Sort": 11}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-N_AverageValues2_LowestMeasuredValues", "ParentName": "VoltageL-N_AverageValues2", "LanguageKey": "LowestMeasuredValues", "Sort": 12}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-L", "ParentName": "", "LanguageKey": "VoltageL-L", "Sort": 13}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-L_ActualInstantaneousMeasurementValues", "ParentName": "VoltageL-L", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 14}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-L_GreatestMeasuredValues", "ParentName": "VoltageL-L", "LanguageKey": "GreatestMeasuredValues", "Sort": 15}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-L_LowestMeasuredValues", "ParentName": "VoltageL-L", "LanguageKey": "LowestMeasuredValues", "Sort": 16}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-L_AverageValues1", "ParentName": "VoltageL-L", "LanguageKey": "AverageValues1", "Sort": 17}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-L_AverageValues1_ActualInstantaneousMeasurementValues", "ParentName": "VoltageL-L_AverageValues1", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 18}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-L_AverageValues1_GreatestMeasuredValues", "ParentName": "VoltageL-L_AverageValues1", "LanguageKey": "GreatestMeasuredValues", "Sort": 19}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-L_AverageValues1_LowestMeasuredValues", "ParentName": "VoltageL-L_AverageValues1", "LanguageKey": "LowestMeasuredValues", "Sort": 20}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-L_AverageValues2", "ParentName": "VoltageL-L", "LanguageKey": "AverageValues2", "Sort": 21}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-L_AverageValues2_ActualInstantaneousMeasurementValues", "ParentName": "VoltageL-L_AverageValues2", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 22}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-L_AverageValues2_GreatestMeasuredValues", "ParentName": "VoltageL-L_AverageValues2", "LanguageKey": "GreatestMeasuredValues", "Sort": 23}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "VoltageL-L_AverageValues2_LowestMeasuredValues", "ParentName": "VoltageL-L_AverageValues2", "LanguageKey": "LowestMeasuredValues", "Sort": 24}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Current", "ParentName": "", "LanguageKey": "Current", "Sort": 25}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Current_ActualInstantaneousMeasurementValues", "ParentName": "Current", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 26}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Current_GreatestMeasuredValues", "ParentName": "Current", "LanguageKey": "GreatestMeasuredValues", "Sort": 27}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Current_LowestMeasuredValues", "ParentName": "Current", "LanguageKey": "LowestMeasuredValues", "Sort": 28}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Current_AverageValues1", "ParentName": "Current", "LanguageKey": "AverageValues1", "Sort": 29}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Current_AverageValues1_ActualInstantaneousMeasurementValues", "ParentName": "Current_AverageValues1", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 30}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Current_AverageValues1_GreatestMeasuredValues", "ParentName": "Current_AverageValues1", "LanguageKey": "GreatestMeasuredValues", "Sort": 31}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Current_AverageValues1_LowestMeasuredValues", "ParentName": "Current_AverageValues1", "LanguageKey": "LowestMeasuredValues", "Sort": 32}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Current_AverageValues2", "ParentName": "Current", "LanguageKey": "AverageValues2", "Sort": 33}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Current_AverageValues2_ActualInstantaneousMeasurementValues", "ParentName": "Current_AverageValues2", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 34}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Current_AverageValues2_GreatestMeasuredValues", "ParentName": "Current_AverageValues2", "LanguageKey": "GreatestMeasuredValues", "Sort": 35}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Current_AverageValues2_LowestMeasuredValues", "ParentName": "Current_AverageValues2", "LanguageKey": "LowestMeasuredValues", "Sort": 36}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power", "ParentName": "", "LanguageKey": "Power", "Sort": 37}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ActivePower", "ParentName": "Power", "LanguageKey": "ActivePower", "Sort": 38}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "InstantaneousValuesActivePower", "ParentName": "ActivePower", "LanguageKey": "InstantaneousValuesActivePower", "Sort": 39}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "InstantaneousValuesActivePower_ActualInstantaneousMeasurementValues", "ParentName": "InstantaneousValuesActivePower", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 40}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "InstantaneousValuesActivePower_GreatestMeasuredValues", "ParentName": "InstantaneousValuesActivePower", "LanguageKey": "GreatestMeasuredValues", "Sort": 41}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "InstantaneousValuesActivePower_LowestMeasuredValues", "ParentName": "InstantaneousValuesActivePower", "LanguageKey": "LowestMeasuredValues", "Sort": 42}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "MeanValuesActivePower", "ParentName": "ActivePower", "LanguageKey": "MeanValuesActivePower", "Sort": 43}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "MeanValuesActivePower_MaximumValuesInMeasuringPeriod", "ParentName": "MeanValuesActivePower", "LanguageKey": "MaximumValuesInMeasuringPeriod", "Sort": 44}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "MeanValuesActivePower_MinimumValuesInMeasuringPeriod", "ParentName": "MeanValuesActivePower", "LanguageKey": "MinimumValuesInMeasuringPeriod", "Sort": 45}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "CumulatedActivePower", "ParentName": "ActivePower", "LanguageKey": "CumulatedActivePower", "Sort": 46}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "CumulatedActivePowerImport", "ParentName": "CumulatedActivePower", "LanguageKey": "CumulatedActivePowerImport", "Sort": 47}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "CumulatedActivePowerExport", "ParentName": "CumulatedActivePower", "LanguageKey": "CumulatedActivePowerExport", "Sort": 48}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ReactivePower", "ParentName": "Power", "LanguageKey": "ReactivePower", "Sort": 49}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "MeasuringMethodQ1", "ParentName": "ReactivePower", "LanguageKey": "MeasuringMethodQ1", "Sort": 50}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "MeasuringMethodQ1_ActualInstantaneousMeasurementValues", "ParentName": "MeasuringMethodQ1", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 51}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "MeasuringMethodQ1_GreatestMeasuredValues", "ParentName": "MeasuringMethodQ1", "LanguageKey": "GreatestMeasuredValues", "Sort": 52}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "MeasuringMethodQ1_LowestMeasuredValues", "ParentName": "MeasuringMethodQ1", "LanguageKey": "LowestMeasuredValues", "Sort": 53}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "MeanValuesReactivePower", "ParentName": "ReactivePower", "LanguageKey": "MeanValuesReactivePower", "Sort": 54}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "MeanValuesReactivePower_MaximumValuesInMeasuringPeriod", "ParentName": "MeanValuesReactivePower", "LanguageKey": "MaximumValuesInMeasuringPeriod", "Sort": 55}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "MeanValuesReactivePower_MinimumValuesInMeasuringPeriod", "ParentName": "MeanValuesReactivePower", "LanguageKey": "MinimumValuesInMeasuringPeriod", "Sort": 56}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "CumulatedReactivePower", "ParentName": "ReactivePower", "LanguageKey": "CumulatedReactivePower", "Sort": 57}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "CumulatedReactivePowerImport", "ParentName": "CumulatedReactivePower", "LanguageKey": "CumulatedReactivePowerImport", "Sort": 58}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "CumulatedReactivePowerExport", "ParentName": "CumulatedReactivePower", "LanguageKey": "CumulatedReactivePowerExport", "Sort": 59}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ParentName": "Power", "LanguageKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Sort": 60}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ApparentPower_ActualInstantaneousMeasurementValues", "ParentName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 61}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ApparentPower_GreatestMeasuredValues", "ParentName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LanguageKey": "GreatestMeasuredValues", "Sort": 62}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ApparentPower_LowestMeasuredValues", "ParentName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LanguageKey": "LowestMeasuredValues", "Sort": 63}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "LoadManagement", "ParentName": "Power", "LanguageKey": "LoadManagement", "Sort": 64}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "CommonDataPowerMeasurementOfTheLastPeriod", "ParentName": "Power", "LanguageKey": "CommonDataPowerMeasurementOfTheLastPeriod", "Sort": 65}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "PowerFactor", "ParentName": "Power", "LanguageKey": "PowerFactor", "Sort": 66}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "PowerFactor_ActualInstantaneousMeasurementValues", "ParentName": "PowerFactor", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 67}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "PowerFactor_GreatestMeasuredValues", "ParentName": "PowerFactor", "LanguageKey": "GreatestMeasuredValues", "Sort": 68}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "PowerFactor_LowestMeasuredValues", "ParentName": "PowerFactor", "LanguageKey": "LowestMeasuredValues", "Sort": 69}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "DisplacementPowerFactor", "ParentName": "Power", "LanguageKey": "DisplacementPowerFactor", "Sort": 70}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "DisplacementPowerFactor_ActualInstantaneousMeasurementValues", "ParentName": "DisplacementPowerFactor", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 71}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "DisplacementPowerFactor_GreatestMeasuredValues", "ParentName": "DisplacementPowerFactor", "LanguageKey": "GreatestMeasuredValues", "Sort": 72}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "DisplacementPowerFactor_LowestMeasuredValues", "ParentName": "DisplacementPowerFactor", "LanguageKey": "LowestMeasuredValues", "Sort": 73}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues1", "ParentName": "Power", "LanguageKey": "AverageValues1", "Sort": 74}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues1_ActivePower", "ParentName": "AverageValues1", "LanguageKey": "ActivePower", "Sort": 75}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues1_ActivePower_InstantaneousValuesActivePower", "ParentName": "Power_AverageValues1_ActivePower", "LanguageKey": "InstantaneousValuesActivePower", "Sort": 76}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues1_ActivePower_MaximumValuesInMeasuringPeriod", "ParentName": "Power_AverageValues1_ActivePower", "LanguageKey": "MaximumValuesInMeasuringPeriod", "Sort": 77}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues1_ActivePower_MinimumValuesInMeasuringPeriod", "ParentName": "Power_AverageValues1_ActivePower", "LanguageKey": "MinimumValuesInMeasuringPeriod", "Sort": 78}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues1_ReactivePower", "ParentName": "AverageValues1", "LanguageKey": "ReactivePower", "Sort": 79}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues1_ReactivePower_ActualInstantaneousMeasurementValues", "ParentName": "Power_AverageValues1_ReactivePower", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 80}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues1_ReactivePower_GreatestMeasuredValues", "ParentName": "Power_AverageValues1_ReactivePower", "LanguageKey": "GreatestMeasuredValues", "Sort": 81}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues1_ReactivePower_LowestMeasuredValues", "ParentName": "Power_AverageValues1_ReactivePower", "LanguageKey": "LowestMeasuredValues", "Sort": 82}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues1_ApparentPower", "ParentName": "AverageValues1", "LanguageKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Sort": 83}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues1_ApparentPower_ActualInstantaneousMeasurementValues", "ParentName": "Power_AverageValues1_ApparentPower", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 84}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues1_ApparentPower_GreatestMeasuredValues", "ParentName": "Power_AverageValues1_ApparentPower", "LanguageKey": "GreatestMeasuredValues", "Sort": 85}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues1_ApparentPower_LowestMeasuredValues", "ParentName": "Power_AverageValues1_ApparentPower", "LanguageKey": "LowestMeasuredValues", "Sort": 86}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues1_PowerFactor", "ParentName": "AverageValues1", "LanguageKey": "PowerFactor", "Sort": 87}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues1_PowerFactor_ActualInstantaneousMeasurementValues", "ParentName": "Power_AverageValues1_PowerFactor", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 88}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues1_PowerFactor_GreatestMeasuredValues", "ParentName": "Power_AverageValues1_PowerFactor", "LanguageKey": "GreatestMeasuredValues", "Sort": 89}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues1_PowerFactor_LowestMeasuredValues", "ParentName": "Power_AverageValues1_PowerFactor", "LanguageKey": "LowestMeasuredValues", "Sort": 90}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues2", "ParentName": "Power", "LanguageKey": "AverageValues2", "Sort": 91}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues2_ActivePower", "ParentName": "AverageValues2", "LanguageKey": "ActivePower", "Sort": 92}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues2_ActivePower_InstantaneousValuesActivePower", "ParentName": "Power_AverageValues2_ActivePower", "LanguageKey": "InstantaneousValuesActivePower", "Sort": 93}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues2_ActivePower_MaximumValuesInMeasuringPeriod", "ParentName": "Power_AverageValues2_ActivePower", "LanguageKey": "MaximumValuesInMeasuringPeriod", "Sort": 94}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues2_ActivePower_MinimumValuesInMeasuringPeriod", "ParentName": "Power_AverageValues2_ActivePower", "LanguageKey": "MinimumValuesInMeasuringPeriod", "Sort": 95}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues2_ReactivePower", "ParentName": "AverageValues2", "LanguageKey": "ReactivePower", "Sort": 96}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues2_ReactivePower_ActualInstantaneousMeasurementValues", "ParentName": "Power_AverageValues2_ReactivePower", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 97}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues2_ReactivePower_GreatestMeasuredValues", "ParentName": "Power_AverageValues2_ReactivePower", "LanguageKey": "GreatestMeasuredValues", "Sort": 98}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues2_ReactivePower_LowestMeasuredValues", "ParentName": "Power_AverageValues2_ReactivePower", "LanguageKey": "LowestMeasuredValues", "Sort": 99}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues2_ApparentPower", "ParentName": "AverageValues2", "LanguageKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Sort": 100}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues2_ApparentPower_ActualInstantaneousMeasurementValues", "ParentName": "Power_AverageValues2_ApparentPower", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 101}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues2_ApparentPower_GreatestMeasuredValues", "ParentName": "Power_AverageValues2_ApparentPower", "LanguageKey": "GreatestMeasuredValues", "Sort": 102}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues2_ApparentPower_LowestMeasuredValues", "ParentName": "Power_AverageValues2_ApparentPower", "LanguageKey": "LowestMeasuredValues", "Sort": 103}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues2_PowerFactor", "ParentName": "AverageValues2", "LanguageKey": "PowerFactor", "Sort": 104}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues2_PowerFactor_ActualInstantaneousMeasurementValues", "ParentName": "Power_AverageValues2_PowerFactor", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 105}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues2_PowerFactor_GreatestMeasuredValues", "ParentName": "Power_AverageValues2_PowerFactor", "LanguageKey": "GreatestMeasuredValues", "Sort": 106}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Power_AverageValues2_PowerFactor_LowestMeasuredValues", "ParentName": "Power_AverageValues2_PowerFactor", "LanguageKey": "LowestMeasuredValues", "Sort": 107}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Energy", "ParentName": "", "LanguageKey": "Energy", "Sort": 108}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ActiveEnergy", "ParentName": "Energy", "LanguageKey": "ActiveEnergy", "Sort": 109}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ActiveEnergy_Import", "ParentName": "ActiveEnergy", "LanguageKey": "Import", "Sort": 110}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ActiveEnergyImportTariff1", "ParentName": "ActiveEnergy_Import", "LanguageKey": "ActiveEnergyImportTariff1", "Sort": 111}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ActiveEnergyImportTariff2", "ParentName": "ActiveEnergy_Import", "LanguageKey": "ActiveEnergyImportTariff2", "Sort": 112}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ActiveEnergy_Export", "ParentName": "ActiveEnergy", "LanguageKey": "Export", "Sort": 113}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ActiveEnergyExportTariff1", "ParentName": "ActiveEnergy_Export", "LanguageKey": "ActiveEnergyExportTariff1", "Sort": 114}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ActiveEnergyExportTariff2", "ParentName": "ActiveEnergy_Export", "LanguageKey": "ActiveEnergyExportTariff2", "Sort": 115}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ReactiveEnergy", "ParentName": "Energy", "LanguageKey": "ReactiveEnergy", "Sort": 116}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ReactiveEnergy_Import", "ParentName": "ReactiveEnergy", "LanguageKey": "Import", "Sort": 117}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "TotalReactiveEnergyImportTariff1", "ParentName": "ReactiveEnergy_Import", "LanguageKey": "TotalReactiveEnergyImportTariff1", "Sort": 118}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "TotalReactiveEnergyImportTariff2", "ParentName": "ReactiveEnergy_Import", "LanguageKey": "TotalReactiveEnergyImportTariff2", "Sort": 119}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ReactiveEnergy_Export", "ParentName": "ReactiveEnergy", "LanguageKey": "Export", "Sort": 120}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "TotalReactiveEnergyExportTariff1", "ParentName": "ReactiveEnergy_Export", "LanguageKey": "TotalReactiveEnergyExportTariff1", "Sort": 121}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "TotalReactiveEnergyExportTariff2", "ParentName": "ReactiveEnergy_Export", "LanguageKey": "TotalReactiveEnergyExportTariff2", "Sort": 122}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ApparentEnergy", "ParentName": "Energy", "LanguageKey": "ApparentEnergy", "Sort": 123}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ApparentEnergyTariff1", "ParentName": "ApparentEnergy", "LanguageKey": "ApparentEnergyTariff1", "Sort": 124}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ApparentEnergyTariff2", "ParentName": "ApparentEnergy", "LanguageKey": "ApparentEnergyTariff2", "Sort": 125}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Non-resettableActiveEnergyCounter", "ParentName": "Energy", "LanguageKey": "Non-resettableActiveEnergyCounter", "Sort": 126}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ThreePhaseSystem", "ParentName": "", "LanguageKey": "ThreePhaseSystem", "Sort": 127}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ThreePhaseSystem_AverageValues1", "ParentName": "ThreePhaseSystem", "LanguageKey": "AverageValues1", "Sort": 128}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ThreePhaseSystem_AverageValues1_ActualInstantaneousMeasurementValues", "ParentName": "ThreePhaseSystem_AverageValues1", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 129}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ThreePhaseSystem_AverageValues1_GreatestMeasuredValues", "ParentName": "ThreePhaseSystem_AverageValues1", "LanguageKey": "GreatestMeasuredValues", "Sort": 130}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ThreePhaseSystem_AverageValues1_LowestMeasuredValues", "ParentName": "ThreePhaseSystem_AverageValues1", "LanguageKey": "LowestMeasuredValues", "Sort": 131}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ThreePhaseSystem_AverageValues2", "ParentName": "ThreePhaseSystem", "LanguageKey": "AverageValues2", "Sort": 132}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ThreePhaseSystem_AverageValues2_ActualInstantaneousMeasurementValues", "ParentName": "ThreePhaseSystem_AverageValues2", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 133}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ThreePhaseSystem_AverageValues2_GreatestMeasuredValues", "ParentName": "ThreePhaseSystem_AverageValues2", "LanguageKey": "GreatestMeasuredValues", "Sort": 134}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "ThreePhaseSystem_AverageValues2_LowestMeasuredValues", "ParentName": "ThreePhaseSystem_AverageValues2", "LanguageKey": "LowestMeasuredValues", "Sort": 135}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "Counter", "ParentName": "", "LanguageKey": "Counter", "Sort": 136}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "CostManagement", "ParentName": "", "LanguageKey": "CostManagement", "Sort": 137}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "HarmonicDistortion", "ParentName": "", "LanguageKey": "HarmonicDistortion", "Sort": 138}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "THDCurrent", "ParentName": "HarmonicDistortion", "LanguageKey": "THDCurrent", "Sort": 139}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "THDCurrent_ActualInstantaneousMeasurementValues", "ParentName": "THDCurrent", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 140}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "THDCurrent_GreatestMeasuredValues", "ParentName": "THDCurrent", "LanguageKey": "GreatestMeasuredValues", "Sort": 141}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "THDVoltageL-N", "ParentName": "HarmonicDistortion", "LanguageKey": "THDVoltageL-N", "Sort": 142}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "THDVoltageL-N_ActualInstantaneousMeasurementValues", "ParentName": "THDVoltageL-N", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 143}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "THDVoltageL-N_GreatestMeasuredValues", "ParentName": "THDVoltageL-N", "LanguageKey": "GreatestMeasuredValues", "Sort": 144}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "THDVoltageL-L", "ParentName": "HarmonicDistortion", "LanguageKey": "THDVoltageL-L", "Sort": 145}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "THDVoltageL-L_ActualInstantaneousMeasurementValues", "ParentName": "THDVoltageL-L", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 146}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "THDVoltageL-L_GreatestMeasuredValues", "ParentName": "THDVoltageL-L", "LanguageKey": "GreatestMeasuredValues", "Sort": 147}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "HarmonicDistortion_AverageValues1", "ParentName": "HarmonicDistortion", "LanguageKey": "AverageValues1", "Sort": 148}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "HarmonicDistortion_AverageValues1_THDCurrent", "ParentName": "HarmonicDistortion_AverageValues1", "LanguageKey": "THDCurrent", "Sort": 149}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "HarmonicDistortion_AverageValues1_THDCurrent_ActualInstantaneousMeasurementValues", "ParentName": "HarmonicDistortion_AverageValues1_THDCurrent", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 150}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "HarmonicDistortion_AverageValues1_THDCurrent_GreatestMeasuredValues", "ParentName": "HarmonicDistortion_AverageValues1_THDCurrent", "LanguageKey": "GreatestMeasuredValues", "Sort": 151}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "HarmonicDistortion_AverageValues1_THDCurrent_LowestMeasuredValues", "ParentName": "HarmonicDistortion_AverageValues1_THDCurrent", "LanguageKey": "LowestMeasuredValues", "Sort": 152}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "HarmonicDistortion_AverageValues1_HDVoltageL-N", "ParentName": "HarmonicDistortion_AverageValues1", "LanguageKey": "HDVoltageL-N", "Sort": 153}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "HarmonicDistortion_AverageValues1_HDVoltageL-N_ActualInstantaneousMeasurementValues", "ParentName": "HarmonicDistortion_AverageValues1_HDVoltageL-N", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 154}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "HarmonicDistortion_AverageValues1_HDVoltageL-N_GreatestMeasuredValues", "ParentName": "HarmonicDistortion_AverageValues1_HDVoltageL-N", "LanguageKey": "GreatestMeasuredValues", "Sort": 155}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "HarmonicDistortion_AverageValues1_HDVoltageL-N_LowestMeasuredValues", "ParentName": "HarmonicDistortion_AverageValues1_HDVoltageL-N", "LanguageKey": "LowestMeasuredValues", "Sort": 156}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "HarmonicDistortion_AverageValues2", "ParentName": "HarmonicDistortion", "LanguageKey": "AverageValues2", "Sort": 157}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "HarmonicDistortion_AverageValues1_THDCurrent", "ParentName": "HarmonicDistortion_AverageValues2", "LanguageKey": "THDCurrent", "Sort": 158}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "HarmonicDistortion_AverageValues2_THDCurrent_ActualInstantaneousMeasurementValues", "ParentName": "HarmonicDistortion_AverageValues2_THDCurrent", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 159}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "HarmonicDistortion_AverageValues2_THDCurrent_GreatestMeasuredValues", "ParentName": "HarmonicDistortion_AverageValues2_THDCurrent", "LanguageKey": "GreatestMeasuredValues", "Sort": 160}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "HarmonicDistortion_AverageValues2_THDCurrent_LowestMeasuredValues", "ParentName": "HarmonicDistortion_AverageValues2_THDCurrent", "LanguageKey": "LowestMeasuredValues", "Sort": 161}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "HarmonicDistortion_AverageValues2_HDVoltageL-N", "ParentName": "HarmonicDistortion_AverageValues2", "LanguageKey": "HDVoltageL-N", "Sort": 162}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "HarmonicDistortion_AverageValues2_HDVoltageL-N_ActualInstantaneousMeasurementValues", "ParentName": "HarmonicDistortion_AverageValues2_HDVoltageL-N", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 163}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "HarmonicDistortion_AverageValues2_HDVoltageL-N_GreatestMeasuredValues", "ParentName": "HarmonicDistortion_AverageValues2_HDVoltageL-N", "LanguageKey": "GreatestMeasuredValues", "Sort": 164}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "HarmonicDistortion_AverageValues2_HDVoltageL-N_LowestMeasuredValues", "ParentName": "HarmonicDistortion_AverageValues2_HDVoltageL-N", "LanguageKey": "LowestMeasuredValues", "Sort": 165}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "FrequencyValues", "ParentName": "", "LanguageKey": "FrequencyValues", "Sort": 166}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "FrequencyValues_ActualInstantaneousMeasurementValues", "ParentName": "FrequencyValues", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 163}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "FrequencyValues_GreatestMeasuredValues", "ParentName": "FrequencyValues", "LanguageKey": "GreatestMeasuredValues", "Sort": 164}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "FrequencyValues_LowestMeasuredValues", "ParentName": "FrequencyValues", "LanguageKey": "LowestMeasuredValues", "Sort": 165}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "FrequencyValues_AverageValues1", "ParentName": "FrequencyValues", "LanguageKey": "AverageValues1", "Sort": 166}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "FrequencyValues_AverageValues1_ActualInstantaneousMeasurementValues", "ParentName": "FrequencyValues_AverageValues1", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 167}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "FrequencyValues_AverageValues1_THDCurrent_GreatestMeasuredValues", "ParentName": "FrequencyValues_AverageValues1", "LanguageKey": "GreatestMeasuredValues", "Sort": 168}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "FrequencyValues_AverageValues1_THDCurrent_LowestMeasuredValues", "ParentName": "FrequencyValues_AverageValues1", "LanguageKey": "LowestMeasuredValues", "Sort": 169}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "FrequencyValues_AverageValues2", "ParentName": "FrequencyValues", "LanguageKey": "AverageValues2", "Sort": 170}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "FrequencyValues_AverageValues2_ActualInstantaneousMeasurementValues", "ParentName": "FrequencyValues_AverageValues2", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 171}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "FrequencyValues_AverageValues2_THDCurrent_GreatestMeasuredValues", "ParentName": "FrequencyValues_AverageValues2", "LanguageKey": "GreatestMeasuredValues", "Sort": 172}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "FrequencyValues_AverageValues2_THDCurrent_LowestMeasuredValues", "ParentName": "FrequencyValues_AverageValues2", "LanguageKey": "LowestMeasuredValues", "Sort": 173}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "LocalDeviceDiagnostics", "ParentName": "", "LanguageKey": "LocalDeviceDiagnostics", "Sort": 174}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "GlobalDeviceDiagnostics", "ParentName": "", "LanguageKey": "GlobalDeviceDiagnostics", "Sort": 175}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "LocalDeviceStatus", "ParentName": "", "LanguageKey": "LocalDeviceStatus", "Sort": 174}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "GlobalDeviceStatus", "ParentName": "", "LanguageKey": "GlobalDeviceStatus", "Sort": 175}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "InputStatus", "ParentName": "", "LanguageKey": "InputStatus", "Sort": 176}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "InputStatus_Device", "ParentName": "InputStatus", "LanguageKey": "<PERSON><PERSON>", "Sort": 177}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "OutputStatus", "ParentName": "", "LanguageKey": "OutputStatus", "Sort": 178}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "OutputStatus_Device", "ParentName": "OutputStatus", "LanguageKey": "<PERSON><PERSON>", "Sort": 179}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "LimitMonitoring", "ParentName": "", "LanguageKey": "LimitMonitoring", "Sort": 180}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "DateTime", "ParentName": "", "LanguageKey": "DateTime", "Sort": 181}, {"AssetLevel": 50, "AssetModel": "PAC3220", "Name": "DeviceLED", "ParentName": "", "LanguageKey": "DeviceLED", "Sort": 182}]