﻿using Siemens.PanelManager.Model.Database.System;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.WebApi.StaticContent
{
    internal class FileManagerFunc
    {
        public static async Task<string> SaveFile(IFormFile file, string fileCode, string fileType, bool isSystemFile, ILogger logger)
        {
            try
            {
                var url = string.Empty;
                var uploadFilePath = string.Empty;
                var fileName = $"{fileCode}.{fileType}";
                var currentPath = Directory.GetCurrentDirectory();

                if (isSystemFile)
                {
                    var uploadFileDir = Path.Combine(currentPath, "wwwroot", "uploadfiles", "systemfile");
                    if (!Directory.Exists(uploadFileDir))
                    {
                        Directory.CreateDirectory(uploadFileDir);
                    }
                    uploadFilePath = Path.Combine(uploadFileDir, fileName);
                    url = $"/uploadfiles/systemfile/{fileName}";
                }
                else
                {
                    var uploadFileDir = Path.Combine(currentPath, "wwwroot", "uploadfiles", "personal");
                    if (!Directory.Exists(uploadFileDir))
                    {
                        Directory.CreateDirectory(uploadFileDir);
                    }
                    uploadFilePath = Path.Combine(uploadFileDir, fileName);
                    url = $"/uploadfiles/personal/{fileName}";
                }

                using (var fs = new FileStream(uploadFilePath, FileMode.OpenOrCreate))
                {
                    await file.CopyToAsync(fs);
                }
                return url;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "上传文件失败");
                return string.Empty;
            }
        }

        public static async Task<FileManager?> CreateFileManager(IFormFile f, string userName, ILogger logger)
        {
            var m = Regex.Match(f.FileName, "[\\\\]?([^\\\\]*)\\.([\\w]*)$");
            if (m.Success)
            {
                var fileType = m.Groups[2].Value;
                var fileName = m.Groups[1].Value;

                var fileManager = new FileManager()
                {
                    FileType = fileType,
                    Name = fileName,
                    CreatedBy = userName,
                    CreatedTime = DateTime.Now,
                    UpdatedBy = userName,
                    UpdatedTime = DateTime.Now,
                };
                var url = await FileManagerFunc.SaveFile(f, fileManager.Code, fileType, fileManager.IsSystemFile, logger);
                fileManager.Url = url;
                return fileManager;
            }

            return null;
        }

        public static bool RemoveFile(FileManager fileManager, ILogger logger)
        {
            if (fileManager.IsSystemFile)
            {
                return false;
            }

            var uploadFileDir = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploadfiles", "personal");

            var filePath = Path.Combine(uploadFileDir, $"{fileManager.Code}.{fileManager.FileType}");
            if (File.Exists(filePath))
            {
                try
                {
                    File.Delete(filePath);
                    return true;
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, $"删除{filePath}文件失败。");
                    return false;
                }
            }

            logger.LogInformation($"{filePath}文件不存在。");
            return true;
        }
    }
}
