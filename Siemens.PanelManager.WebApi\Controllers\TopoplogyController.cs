﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Siemens.PanelManager.Common;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.Topology;
using Siemens.PanelManager.Model.Topology;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.Server.Topology;
using Siemens.PanelManager.Server.Topoplogy;
using Siemens.PanelManager.WebApi.Models;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;
using System.Diagnostics.CodeAnalysis;
using System.Text;
using System.Text.RegularExpressions;
using Permission = Siemens.PanelManager.WebApi.StaticContent.Permission;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v1/sld")]
    [ApiController]
    public class TopologyController : SiemensApiControllerBase
    {
        private const string ReceiptDataCacheKey = "ReceiptData:{0}";
        private TopologyExtendFunc _func => _provider.GetRequiredService<TopologyExtendFunc>();
        private ILogger<TopologyController> _log;
        private IServiceProvider _provider;
        private ISqlSugarClient _client;
        private SiemensCache _cache;
        private const string AssetCurrentStatusCacheKey = "AssetStatus:Currently-{0}";

        private AlarmExtendServer _alarmExtendServer => _provider.GetRequiredService<AlarmExtendServer>();
        public TopologyController(SqlSugarScope client,
            SiemensCache cache,
            ILogger<TopologyController> log,
            IServiceProvider provider)
            : base(provider, cache)
        {
            _cache = cache;
            _provider = provider;
            _client = client;
            _log = log;
        }

        #region 原始 Topoplog
        [HttpPost("update")]
        [SwaggerOperation(Summary = "Swagger_Topology_Update", Description = "Swagger_Topology_Update_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<TopologySaveReceipt>> Update(JObject topoplogData, [FromQuery] string flag)
        {
            if (topoplogData == null
                || !topoplogData.ContainsKey("id")
                || string.IsNullOrEmpty(flag))
            {
                return new ResponseBase<TopologySaveReceipt>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }

            int topoplogId = 0;
            var idToken = topoplogData.GetValue("id");

            if (idToken == null)
            {
                return new ResponseBase<TopologySaveReceipt>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }

            switch (idToken.Type)
            {
                case JTokenType.String:
                    if (!int.TryParse(idToken.Value<string>(), out topoplogId))
                    {
                        return new ResponseBase<TopologySaveReceipt>()
                        {
                            Code = 40301,
                            Message = MessageContext.ErrorParam
                        };
                    }
                    break;
                case JTokenType.Bytes:
                case JTokenType.Integer:
                    topoplogId = idToken.Value<int>();
                    break;
                default:
                    return new ResponseBase<TopologySaveReceipt>()
                    {
                        Code = 40301,
                        Message = MessageContext.ErrorParam
                    };
            }

            var code = topoplogData.Value<string>("code") ?? string.Empty;
            if (string.IsNullOrEmpty(code))
            {
                return new ResponseBase<TopologySaveReceipt>()
                {
                    Code = 40301,
                    Message = MessageContext.GetErrorValue("Topology_MissingCode")
                };
            }

            string topologyType = "topology";
            var topoplogInfo = await _client.Queryable<TopologyInfo>().FirstAsync(t => t.Id == topoplogId && t.Type == topologyType);
            if (topoplogInfo == null)
            {
                var receipt = new TopologySaveReceipt
                {
                    ResultCode = 11,
                };
                _cache.Set(string.Format(ReceiptDataCacheKey, receipt.ReceiptCode),
                    new TopologyReceiptData(topoplogData, flag, UserSession?.UserId ?? -1, topologyType, topoplogId, receipt.ResultCode, UserSession?.Id ?? string.Empty),
                    TimeSpan.FromMinutes(1));

                return new ResponseBase<TopologySaveReceipt>
                {
                    Code = 20000,
                    Data = receipt
                };
            }

            // 比较 Flag
            // 不一致时将数据放入缓存，然后生成回执
            if (!flag.Equals(topoplogInfo.TopologyFlag))
            {
                var receipt = new TopologySaveReceipt
                {
                    ResultCode = 10,
                    LastUpdatedBy = topoplogInfo.UpdatedBy,
                    LastUpdatedDate = topoplogInfo.UpdatedTime
                };
                _cache.Set(string.Format(ReceiptDataCacheKey, receipt.ReceiptCode),
                    new TopologyReceiptData(topoplogData, flag, UserSession?.UserId ?? -1, topologyType, topoplogId, receipt.ResultCode, UserSession?.Id ?? string.Empty),
                    TimeSpan.FromMinutes(1));

                return new ResponseBase<TopologySaveReceipt>
                {
                    Code = 20000,
                    Data = receipt
                };
            }
            var message = new StringBuilder();
            var isError = await SaveTolopogyInfo(topoplogData, code, topoplogInfo, message);

            if (isError)
            {
                return new ResponseBase<TopologySaveReceipt>()
                {
                    Code = 40301,
                    Message = message.ToString()
                };
            }

            await SaveTopologyRules(topoplogData, topoplogId);

            return new ResponseBase<TopologySaveReceipt>()
            {
                Code = 20000,
                Data = new TopologySaveReceipt()
                {
                    ResultCode = 1,
                    TopologyId = topoplogId,
                    Flag = topoplogInfo.TopologyFlag
                }
            };
        }

        private async Task<bool> SaveTolopogyInfo(JObject topoplogData, string code, TopologyInfo topoplogInfo, StringBuilder message)
        {
            bool isError = false;

            string topologType = "topology";
            topoplogInfo.Name = topoplogData.Value<string>("name") ?? string.Empty;
            topoplogInfo.Description = topoplogData.Value<string>("discription") ?? string.Empty;

            bool needCheckCode = false;
            if (!code.Equals(topoplogInfo.Code))
            {
                topoplogInfo.Code = code;
                needCheckCode = true;
            }

            if (topoplogInfo.CheckTopologyInfo(message, MessageContext))
            {
                isError = true;
            }

            if (needCheckCode)
            {
                var checkResult = await _func.CheckUniqueCode(code, topologType, _client);
                if (!checkResult)
                {
                    isError = true;
                    message.Append(MessageContext.GetErrorValue("Topology_UniqueCode"));
                }
            }

            if (!isError)
            {
                topoplogInfo.Data = topoplogData.ToString(Newtonsoft.Json.Formatting.None);
                topoplogInfo.TopologyFlag = Guid.NewGuid().ToString();
                topoplogInfo.UpdatedBy = UserName;
                topoplogInfo.UpdatedTime = DateTime.Now;

                await _client.Updateable(topoplogInfo).ExecuteCommandAsync();
                await _alarmExtendServer.InsertOperationLog(UserName, "UpdateTopology", Model.Database.Alarm.AlarmSeverity.Middle, _client, topoplogInfo.Name);
                var svc = _provider.GetRequiredService<TopologyDraftService>();
                await svc.DeleteDraftByUserId(UserSession?.UserId ?? 0, topologType, _client, topoplogInfo.Id);
            }

            return isError;
        }

        [HttpDelete("{id}")]
        [SwaggerOperation(Summary = "Swagger_Topology_Delete", Description = "Swagger_Topology_Delete_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> Delete(int id)
        {
            if (id <= 0)
            {
                return new ResponseBase<string>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            string topoplogType = "topology";
            var existItem = await _client.Queryable<TopologyInfo>().Select(t => new TopologyInfo()
            {
                Id = t.Id,
                Name = t.Name,
                Code = t.Code,
                Description = t.Description
            }).FirstAsync(t => t.Id == id && t.Type == topoplogType);
            if (existItem == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.GetErrorValue("Common_NotExists")
                };
            }

            var server = _provider.GetRequiredService<TopologyExtendFunc>();
            await server.DeleteTopologyByTopologyId(id, _client);
            await _alarmExtendServer.InsertOperationLog(UserName, "UpdateTopology", Model.Database.Alarm.AlarmSeverity.Middle, _client, existItem.Name);
            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = MessageContext.Success
            };
        }

        [HttpPost()]
        [SwaggerOperation(Summary = "Swagger_Topology_Add", Description = "Swagger_Topology_Add_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<TopologySaveReceipt>> Add(JObject topoplogData)
        {
            string topoplogType = "topology";
            if (topoplogData == null)
            {
                return new ResponseBase<TopologySaveReceipt>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }

            var message = new StringBuilder();
            var receipt = await AddTopologyInfo(topoplogData, topoplogType, message);

            if (receipt.ResultCode == 99)
            {
                return new ResponseBase<TopologySaveReceipt>()
                {
                    Code = 40301,
                    Message = message.ToString()
                };
            }

            await SaveTopologyRules(topoplogData, receipt.TopologyId);

            return new ResponseBase<TopologySaveReceipt>()
            {
                Code = 20000,
                Data = receipt
            };
        }

        private async Task SaveTopologyRules(JObject topologyData, int topologyId)
        {
            var ruleToken = topologyData.SelectToken("topology.rule");
            if (ruleToken != null && ruleToken is JObject ruleObj)
            {
                var ruleDic = ruleObj.ToObject<Dictionary<string, TopologyRuleDetails>>();
                var existsRules = await _client.Queryable<TopologyRuleInfo>().Where(tr => tr.TopologyId == topologyId).ToListAsync();
                var keys = new List<string>();
                if (ruleDic != null)
                {
                    foreach (var rule in ruleDic)
                    {
                        if (rule.Value.TargeInfo == null) continue;
                        keys.Add(rule.Key);
                        var ruleInfo = existsRules.FirstOrDefault(r => r.RuleCode == rule.Key);
                        if (ruleInfo == null)
                        {
                            ruleInfo = new TopologyRuleInfo
                            {
                                RuleCode = rule.Key,
                                TopologyId = topologyId,
                                CreatedBy = UserName,
                                CreatedTime = DateTime.Now,
                                FormatFunction = string.Empty,
                            };
                            existsRules.Add(ruleInfo);
                        }

                        var dataPoint = rule.Value.DataPoint;
                        var formatFunction = string.Empty;

                        if ("swichStatus".Equals(rule.Value.TargeInfo.TargetProperty))
                        {
                            dataPoint = "Switch";
                            formatFunction = "{\"FunctionName\":\"ChangeSwitch\"}";
                        }
                        else if ("positionStatus".Equals(rule.Value.TargeInfo.TargetProperty))
                        {
                            dataPoint = "BreakerPosition";
                            formatFunction = "{\"FunctionName\":\"ChangeBreakerPosition\"}";
                        }

                        ruleInfo.AssetId = rule.Value.AssetId;
                        ruleInfo.DataPoint = dataPoint;
                        ruleInfo.TargetIdentify = rule.Value.TargeInfo.TargetIdentify;
                        ruleInfo.TargetProperty = rule.Value.TargeInfo.TargetProperty;
                        ruleInfo.FormatFunction = formatFunction;
                        ruleInfo.UpdatedBy = UserName;
                        ruleInfo.UpdatedTime = DateTime.Now;
                    }
                }

                var needRemoveRules = existsRules.Where(r => !keys.Contains(r.RuleCode)).ToList();
                needRemoveRules.ForEach(r => existsRules.Remove(r));

                await _client.Storageable(existsRules).ExecuteCommandAsync();
                if (needRemoveRules.Count > 0)
                {
                    var needDeleteIds = needRemoveRules.Select(r => r.Id).ToList();
                    await _client.Deleteable<TopologyRuleInfo>().Where(r => needDeleteIds.Contains(r.Id)).ExecuteCommandAsync();


                    var hashKeys = new List<string>();
                    foreach (var rr in needRemoveRules)
                    {
                        hashKeys.Add($"[{rr.TargetIdentify}].[{rr.TargetProperty}]");
                    }

                    if (hashKeys.Count > 0)
                    {
                        _cache.RemoveHashData($"TopologyCurrently:{topologyId}", hashKeys.ToArray());
                    }
                }

                var refObj = _provider.GetRequiredService<ITopologyDataChangeRef>();
                if (refObj != null)
                {
                    refObj.TopologyNeedReload(new int[] { topologyId });
                }
            }
        }

        private async Task<TopologySaveReceipt> AddTopologyInfo(JObject topoplogData, string topoplogType, StringBuilder message)
        {
            int newId = -1;
            var isError = false;
            var topoplogInfo = new TopologyInfo();
            JToken? token = null;

            topoplogData.TryGetValue("name", out token);
            if (token != null)
            {
                topoplogInfo.Name = token.ToString();
            }
            else
            {
                topoplogInfo.Name = string.Empty;
            }

            token = null;
            topoplogData.TryGetValue("code", out token);
            if (token != null)
            {
                topoplogInfo.Code = token.ToString();
            }
            else
            {
                isError = true;
                message.AppendLine(MessageContext.GetErrorValue("Topology_UniqueCode"));

            }

            token = null;
            topoplogData.TryGetValue("discription", out token);
            if (token != null)
            {
                topoplogInfo.Description = token.ToString();
            }
            else
            {
                topoplogInfo.Description = string.Empty;
            }


            if (topoplogInfo.CheckTopologyInfo(message, MessageContext))
            {
                isError = true;
            }

            var checkResult = await _func.CheckUniqueCode(topoplogInfo.Code, topoplogType, _client);
            if (!checkResult)
            {
                isError = true;
                message.AppendLine(MessageContext.GetErrorValue("Topology_UniqueCode"));
            }
            var flag = Guid.NewGuid().ToString();
            if (!isError)
            {
                topoplogInfo.Data = topoplogData.ToString(Formatting.None);
                topoplogInfo.Type = topoplogType;
                topoplogInfo.TopologyFlag = flag;
                topoplogInfo.CreatedBy = UserName;
                topoplogInfo.CreatedTime = DateTime.Now;
                topoplogInfo.UpdatedBy = UserName;
                topoplogInfo.UpdatedTime = DateTime.Now;

                newId = await _client.Insertable(topoplogInfo).ExecuteReturnIdentityAsync();
                await _alarmExtendServer.InsertOperationLog(UserName, "AddTopology", Model.Database.Alarm.AlarmSeverity.Middle, _client, topoplogInfo.Name);
                var svc = _provider.GetRequiredService<TopologyDraftService>();
                await svc.DeleteDraftByUserId(UserSession?.UserId ?? 0, topoplogType, _client);
            }

            return new TopologySaveReceipt
            {
                TopologyId = newId,
                ResultCode = newId > 0 ? 1 : 99,
                Flag = flag,
            };
        }

        [HttpGet()]
        [SwaggerOperation(Summary = "Swagger_Topology_GetAll", Description = "Swagger_Topology_GetAll_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<JObject[]>> GetAll([AllowNull] string? ids)
        {
            string topoplogType = "topology";
            var result = new List<JObject>();
            TopologyInfo[] topoplogyInfoes;
            var idManagers = await _client.Queryable<TopologyIdManager>().Where(t => t.Type == "Dashboard" || t.Type == "Dashboard:Main").ToArrayAsync();
            if (string.IsNullOrEmpty(ids))
            {
                topoplogyInfoes = await _client.Queryable<TopologyInfo>().Where(t => t.Type == topoplogType).OrderByDescending(t => t.CreatedTime).ToArrayAsync();
            }
            else
            {
                var idList = new List<int>();
                var idStrs = ids.Split(',');
                foreach (var idStr in idStrs)
                {
                    if (int.TryParse(idStr, out int id))
                    {
                        idList.Add(id);
                    }
                }
                topoplogyInfoes = await _client.Queryable<TopologyInfo>().Where(t => t.Type == topoplogType && idList.Contains(t.Id)).OrderByDescending(t => t.CreatedTime).ToArrayAsync();
            }

            foreach (var t in topoplogyInfoes)
            {
                try
                {
                    var data = JObject.Parse(t.Data);
                    data["id"] = t.Id;
                    data["dashboardVisible"] = idManagers.Any(i => i.Key == t.Id && i.Type == "Dashboard");
                    data["dashboardFirst"] = idManagers.Any(i => i.Key == t.Id && i.Type == "Dashboard:Main");
                    data["name"] = t.Name;
                    data["code"] = t.Code;
                    data["discription"] = t.Description;
                    result.Add(data);
                }
                catch (Exception ex)
                {
                    _log.LogError(ex, $"拓扑数据解析有问题{t.Id}");
                }
            }
            return new ResponseBase<JObject[]>()
            {
                Code = 20000,
                Data = result.ToArray()
            };
        }

        [HttpGet("{id}")]
        [SwaggerOperation(Summary = "Swagger_Topology_Get", Description = "Swagger_Topology_Get_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<JObject>> Get(int id)
        {
            if (id <= 0)
            {
                return new ResponseBase<JObject>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            string topoplogType = "topology";
            var idManagers = await _client.Queryable<TopologyIdManager>().Where(t => t.Key == id && (t.Type == "Dashboard" || t.Type == "Dashboard:Main")).ToArrayAsync();
            JObject data = new JObject();
            var svc = _provider.GetRequiredService<TopologyDraftService>();

            var details = await svc.GetTopologyInfo(id, UserSession?.UserId ?? -1, topoplogType, _client);

            if (details != null && details.Data != null)
            {
                if (details.From == Model.Topology.TopologyFrom.DB)
                {
                    data = details.Data;
                    data["id"] = details.TopologyId;
                    data["dashboardVisible"] = idManagers.Any(i => i.Type == "Dashboard");
                    data["dashboardFirst"] = idManagers.Any(i => i.Type == "Dashboard:Main");
                    data["name"] = details.TopologyName;
                    data["code"] = details.Code;
                    data["discription"] = details.Description;
                    data["flag"] = details.Flag;
                }
                else
                {
                    data = details.Data;
                    if (!string.IsNullOrEmpty(details.Flag))
                    {
                        data["flag"] = details.Flag;
                    }
                }
            }
            return new ResponseBase<JObject>()
            {
                Code = 20000,
                Data = data
            };
        }

        [HttpGet("arithmetic/{assetId}")]
        [SwaggerOperation(Summary = "Swagger_Topology_Get", Description = "Swagger_Topology_Get_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<JObject?> GetByArithmetic(int assetId)
        {
            if (assetId <= 0)
            {
                return null;
            }
            string topoplogType = "topology";
            var topologyInfo = await _client.Queryable<TopologyInfo>()
                .InnerJoin<AssetInfo>((t, a) => t.Id == a.TopologyId)
                .Where((t, a) => t.Type == topoplogType && a.Id == assetId)
                .Select((t, a) => t)
                .FirstAsync();

            if (topologyInfo == null)
            {
                return null;
            }
            JObject data = JObject.Parse(topologyInfo.Data);

            #region 添加算法需要的自动属性
            {
                var assetIds = data.SelectTokens("$..assetId").Select(t => t.Value<int>()).Distinct().ToArray();
                var nodes = data.SelectTokens("$..nodeDataArray").Select(t => t.Value<JArray>()).FirstOrDefault();
                var links = data.SelectTokens("$..linkDataArray").Select(t => t.Value<JArray>()).FirstOrDefault();
                if (nodes != null && links != null)
                {
                    var assetDetails = await _client.Queryable<DeviceDetails>().Where(d => assetIds.Contains(d.AssetId)).ToListAsync();

                    var replaceNodes = new Dictionary<int, JToken[]>();

                    var needReplace = new List<int>();

                    foreach (var id in assetIds)
                    {
                        var assetDetail = assetDetails.FirstOrDefault(a => a.AssetId == id);

                        var tokens = nodes.Where(n => n["assetId"] != null
                            && n["assetId"].Value<int>() == id
                            && n["type"] != null
                            && (n["type"].Value<string>() == "P"
                            || n["type"].Value<string>() == "B")).ToArray();

                        var extendData = JToken.FromObject(new TopologyExtendData()
                        {
                            RatedCurrent = assetDetail?.RatedCurrent,
                            RatedVoltage = assetDetail?.RatedVoltage,
                        });

                        foreach (var token in tokens)
                        {
                            if (token is JObject subObj)
                            {
                                if (token["key"] != null)
                                {
                                    var key = token["key"].Value<int>();
                                    if (key > 0)
                                    {
                                        var subLinks = links.Where(l => l["from"] != null && l["to"] != null && (l["from"].Value<int>() == key || l["to"].Value<int>() == key)).ToArray();
                                        replaceNodes.TryAdd(key, subLinks);

                                        if (token["type"] != null && token["type"].Value<string>() == "P" && assetDetail != null && assetDetail.MeterSite == MeterSite.BusbarSide)
                                        {
                                            needReplace.Add(key);
                                        }
                                    }
                                }

                                if (subObj["extendData"] == null)
                                {
                                    subObj.Add("extendData", extendData);
                                }
                            }
                        }
                    }

                    foreach(var key in needReplace)
                    {
                        var subLinks = replaceNodes[key];

                        var switchKey = 0;
                        var needChangeLinks = new List<JToken>();
                        foreach (var subLink in subLinks)
                        {
                            var toKey = subLink["to"].Value<int>();
                            var fromKey = subLink["from"].Value<int>();

                            bool isFrom = false;
                            switchKey = 0;
                            if (toKey == key)
                            {
                                switchKey = fromKey;
                                isFrom = false;
                            }
                            else
                            {
                                switchKey = toKey;
                                isFrom = true;
                            }

                            if (switchKey != 0 && replaceNodes.TryGetValue(switchKey, out var switchLinks))
                            {
                                needChangeLinks = subLinks.Where(s => s != subLink).ToList();
                                needChangeLinks.AddRange(switchLinks.Where(s => s["from"] != null && s["to"] != null
                                && !(s["from"].Value<int>() == key && s["to"].Value<int>() == switchKey)
                                && !(s["to"].Value<int>() == key && s["from"].Value<int>() == switchKey)).ToArray());
                                break;
                            }
                        }

                        foreach(var changeLink in needChangeLinks)
                        {
                            var toKey = changeLink["to"].Value<int>();
                            var fromKey = changeLink["from"].Value<int>();

                            if(toKey == key)
                            {
                                changeLink["to"] = switchKey;
                            }
                            else if(fromKey == key)
                            {
                                changeLink["from"] = switchKey;
                            }
                            else if (toKey == switchKey)
                            {
                                changeLink["from"] = key;
                            }
                            else if (fromKey == switchKey)
                            {
                                changeLink["from"] = key;
                            }
                        }
                    }
                }
            }
            #endregion

            return data;
        }
        #endregion

        #region dashboard
        [HttpPost("dashboard/{id}")]
        [SwaggerOperation(Summary = "Swagger_Topology_UpdateDashboard", Description = "Swagger_Topology_UpdateDashboard_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> UpdateDashboard(int id, UpdateDashboardParam param)
        {
            if (id <= 0 || param == null || !param.DashboardVisible.HasValue)
            {
                return new ResponseBase<string>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            string topoplogType = "topology";

            var existItem = await _client.Queryable<TopologyInfo>().Select(t => new TopologyInfo()
            {
                Id = t.Id,
                Name = t.Name,
                Code = t.Code,
                Description = t.Description
            }).FirstAsync(t => t.Id == id && t.Type == topoplogType);

            if (existItem != null)
            {
                if (param.DashboardVisible.Value)
                {
                    if (!await _client.Queryable<TopologyIdManager>().AnyAsync(t => t.Key == id && t.Type == "Dashboard"))
                    {
                        await _client.Insertable(new TopologyIdManager()
                        {
                            Key = id,
                            Type = "Dashboard",
                            CreatedBy = UserName,
                            CreatedTime = DateTime.Now,
                            UpdatedBy = UserName,
                            UpdatedTime = DateTime.Now,
                        }).ExecuteCommandAsync();
                    }
                }
                else
                {
                    await _client.Deleteable<TopologyIdManager>()
                    .Where(t => t.Key == id && t.Type == "Dashboard")
                    .ExecuteCommandAsync();
                }
                await _alarmExtendServer.InsertOperationLog(UserName, "UpdateTopology", Model.Database.Alarm.AlarmSeverity.Middle, _client, existItem.Name);
                return new ResponseBase<string>()
                {
                    Code = 20000,
                    Data = MessageContext.Success
                };
            }

            return new ResponseBase<string>()
            {
                Code = 40400,
                Message = MessageContext.GetErrorValue("Common_NotExists")
            };
        }

        [HttpPost("dashboardFirst/{id}")]
        [SwaggerOperation(Summary = "Swagger_Topology_UpdateMainDashboard", Description = "Swagger_Topology_UpdateMainDashboard_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> UpdateMainDashboard(int id, UpdateMainDashboardParam param)
        {
            if (id <= 0 || param == null || !param.DashboardFirst.HasValue)
            {
                return new ResponseBase<string>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            string topoplogType = "topology";

            var existItem = await _client.Queryable<TopologyInfo>().Select(t => new TopologyInfo()
            {
                Id = t.Id,
                Name = t.Name,
                Code = t.Code,
                Description = t.Description
            }).FirstAsync(t => t.Id == id && t.Type == topoplogType);
            if (existItem != null)
            {
                var idManagers = await _client.Queryable<TopologyIdManager>()
                    .Where(t => t.Type == "Dashboard:Main")
                    .ToListAsync();
                if (param.DashboardFirst.Value)
                {
                    if (idManagers.Count == 0)
                    {
                        await _client.Insertable(new TopologyIdManager()
                        {
                            Key = id,
                            Type = "Dashboard:Main",
                            CreatedBy = UserName,
                            CreatedTime = DateTime.Now,
                            UpdatedBy = UserName,
                            UpdatedTime = DateTime.Now,
                        }).ExecuteCommandAsync();
                    }
                    else
                    {
                        try
                        {
                            _client.Ado.BeginTran();
                            idManagers[0].Key = id;
                            idManagers[0].UpdatedBy = UserName;
                            idManagers[0].UpdatedTime = DateTime.Now;
                            await _client.Updateable(idManagers[0]).ExecuteCommandAsync();
                            idManagers.RemoveAt(0);
                            if (idManagers.Count > 0)
                            {
                                await _client.Deleteable(idManagers).ExecuteCommandAsync();
                            }
                            _client.Ado.CommitTran();
                        }
                        catch (Exception ex)
                        {
                            _log.LogError(ex, "修改主页标识失败");
                            _client.Ado.RollbackTran();
                            return new ResponseBase<string>()
                            {
                                Code = 50000,
                                Message = MessageContext.ServerException,
                            };
                        }
                    }
                }
                else
                {
                    var idConfig = idManagers.FirstOrDefault(i => i.Key == id);
                    if (idConfig != null)
                    {
                        await _client.Deleteable(idConfig).ExecuteCommandAsync();
                    }
                }
                await _alarmExtendServer.InsertOperationLog(UserName, "UpdateTopology", Model.Database.Alarm.AlarmSeverity.Middle, _client, existItem.Name);
                return new ResponseBase<string>()
                {
                    Code = 20000,
                    Data = MessageContext.Success
                };
            }

            return new ResponseBase<string>()
            {
                Code = 40400,
                Message = MessageContext.GetErrorValue("Common_NotExists")
            };
        }

        [HttpGet("dashboard")]
        [SwaggerOperation(Summary = "Swagger_Topology_GetMainDashboard", Description = "Swagger_Topology_GetMainDashboard_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<JObject>> GetMainDashboard()
        {
            string topoplogType = "topology";
            var idManagers = await _client.Queryable<TopologyIdManager>()
                .InnerJoin<TopologyInfo>((t, ti) => t.Key == ti.Id)
                .Where((t, ti) => t.Type == "Dashboard:Main" || t.Type == "Dashboard")
                .Select((t, ti) => t)
                .ToListAsync();

            var idConfig = idManagers.FirstOrDefault(t => t.Type == "Dashboard:Main");
            if (idConfig == null)
            {
                idConfig = idManagers.OrderByDescending(o => o.Id).FirstOrDefault();
            }
            TopologyInfo? topoplogyInfo = null;
            if (idConfig != null)
            {
                topoplogyInfo = await _client.Queryable<TopologyInfo>().FirstAsync(t => t.Id == idConfig.Key && t.Type == topoplogType);
            }

            if (topoplogyInfo == null)
            {
                topoplogyInfo = await _client.Queryable<TopologyInfo>().OrderByDescending(t => t.Id).FirstAsync(t => t.Type == topoplogType);
            }

            if (topoplogyInfo != null)
            {
                var data = JObject.Parse(topoplogyInfo.Data);
                data["id"] = topoplogyInfo.Id;
                data["name"] = topoplogyInfo.Name;
                data["code"] = topoplogyInfo.Code;
                data["discription"] = topoplogyInfo.Description;
                return new ResponseBase<JObject>()
                {
                    Code = 20000,
                    Data = data
                };
            }

            return new ResponseBase<JObject>()
            {
                Code = 20000,
                Data = JObject.Parse("{}"),
                Message = MessageContext.GetErrorValue("Common_NotExists")
            };
        }


        [HttpGet("dashboard/left")]
        [SwaggerOperation(Summary = "Swagger_Topology_GetDashboardList", Description = "Swagger_Topology_GetDashboardList_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<JObject[]>> GetDashboardList()
        {
            string topoplogType = "topology";

            var idManagers = await _client.Queryable<TopologyIdManager>()
                .Where(t => t.Type == "Dashboard:Main" || t.Type == "Dashboard")
                .ToListAsync();
            if (idManagers.Count == 0)
            {
                return new ResponseBase<JObject[]>()
                {
                    Code = 20000,
                    Data = new JObject[0]
                };
            }

            var main = idManagers.OrderByDescending(t => t.Id).FirstOrDefault(t => t.Type == "Dashboard:Main");
            var dashboadList = idManagers.Where(t => t.Type == "Dashboard" && t.Key != (main?.Key ?? 0)).ToList();
            if (dashboadList.Count == 0)
            {
                return new ResponseBase<JObject[]>()
                {
                    Code = 20000,
                    Data = new JObject[0]
                };
            }
            var ids = dashboadList.Select(d => d.Key).ToArray();
            var topoplogyInfoes = await _client.Queryable<TopologyInfo>()
               .Where(t => ids.Contains(t.Id) && t.Type == topoplogType)
               .ToArrayAsync();

            var dataList = new List<JObject>();
            foreach (var t in topoplogyInfoes)
            {
                var data = JObject.Parse(t.Data);
                data["id"] = t.Id;
                data["name"] = t.Name;
                data["code"] = t.Code;
                data["discription"] = t.Description;
                dataList.Add(data);
            }
            return new ResponseBase<JObject[]>()
            {
                Code = 20000,
                Data = dataList.ToArray()
            };
        }

        [HttpGet("working")]
        [SwaggerOperation(Summary = "Swagger_Topology_Working", Description = "Swagger_Topology_Working_Desc")]
        [Authorize(policy: Permission.Default)]
        public ResponseBase<string> Working()
        {
            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = MessageContext.Success
            };
        }

        [HttpGet("GetSingle")]
        [SwaggerOperation(Summary = "Swagger_Topology_GetAll", Description = "Swagger_Topology_GetAll_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<TopologySingleInfo[]>> GetSingleAll()
        {
            string topoplogType = "topology";
            var topoplogyInfoes = await _client.Queryable<TopologyInfo>()
                .Where(t => t.Type == topoplogType)
                .Select(t => new TopologySingleInfo()
                {
                    Id = t.Id,
                    Name = t.Name,
                    Code = t.Code,
                    Time = t.CreatedTime,
                    Owner = t.CreatedBy,
                    Description = t.Description
                })
                .ToArrayAsync();

            return new ResponseBase<TopologySingleInfo[]>()
            {
                Code = 20000,
                Data = topoplogyInfoes.ToArray()
            };
        }
        #endregion

        [HttpGet("{id}/currently")]
        [SwaggerOperation(Summary = "Swagger_Topology_CurrentlyData", Description = "Swagger_Topology_CurrentlyData_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<Dictionary<string, Dictionary<string,string>>>> Currently(int id, long ts)
        {
            string topoplogType = "topology";
            if (id <= 0)
            {
                return new ResponseBase<Dictionary<string, Dictionary<string, string>>>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }

            var exists = await _client.Queryable<TopologyInfo>().Where(t => t.Id == id && t.Type == topoplogType).AnyAsync();

            if (!exists)
            {
                return new ResponseBase<Dictionary<string, Dictionary<string, string>>>()
                {
                    Code = 40400,
                    Message = MessageContext.GetErrorValue("Common_NotExists")
                };
            }

            // await _semaphoreSlim.WaitAsync();

            var cache = _provider.GetRequiredService<SiemensCache>();
            var topologyCodeMapCacheKey = "TopologyCodeMap:{0}";
            var topologyCodeMap = cache.Get<Dictionary<string, List<string>>>(string.Format(topologyCodeMapCacheKey, id));

            var topologyCacheKey = "TopologyCurrently:{0}";
            var value = cache.GetHashAllData(string.Format(topologyCacheKey, id));

            var result = new Dictionary<string, Dictionary<string, string>>();
            if (topologyCodeMap != null)
            {
                foreach (var kv in topologyCodeMap)
                {
                    var codes = kv.Value;
                    var data = new Dictionary<string, string>();
                    result.Add(kv.Key, data);
                    foreach (var c in codes)
                    {
                        data.TryAdd(c, string.Empty);
                    }
                }

                var r = new Regex("^\\[(?<N>[\\w|\\-|_]*)\\]\\.\\[(?<K>[\\w|\\-|_]*)\\]$");
                foreach (var kv in value)
                {
                    var match = r.Match(kv.Key);
                    if (match.Success)
                    {
                        var nodeName = match.Groups["N"].Value;
                        var key = match.Groups["K"].Value;
                        if (result.TryGetValue(nodeName, out var data))
                        {
                            if (data.ContainsKey(key))
                            {
                                data[key] = kv.Value;
                            }
                            else
                            {
                                data.Add(key, kv.Value);
                            }
                        }
                        else
                        {
                            data = new Dictionary<string, string>
                            {
                                { key, kv.Value }
                            };
                            result.Add(nodeName, data);
                        }
                    }
                    else
                    {
                        _log.LogWarning($"Topology currently data key format error: {kv.Key}");
                    }
                }
            }

            return new ResponseBase<Dictionary<string, Dictionary<string, string>>>()
            {
                Code = 20000,
                Data = result
            };
        }

        [HttpPut("receipt/{code}")]
        [SwaggerOperation(Summary = "Swagger_Topology_Receipt", Description = "Swagger_Topology_Receipt_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<TopologySaveReceipt>> Receipt([FromQuery] TopologySaveReceiptParam param)
        {
            var topologyType = "topology";
            var data = _cache.Get<TopologyReceiptData>(string.Format(ReceiptDataCacheKey, param.ReceiptCode));

            if (data == null || !data.SessionId.Equals(UserSession?.Id))
            {
                return new ResponseBase<TopologySaveReceipt>()
                {
                    Code = 40400,
                    Message = MessageContext.GetString("Error_Topology_MissReceiptCode") ?? "MissReceiptCode"
                };
            }

            if (!data.TopologyType.Equals(topologyType))
            {
                return new ResponseBase<TopologySaveReceipt>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }

            if (param.Operation == 10)
            {
                var svc = _provider.GetRequiredService<TopologyDraftService>();
                await svc.DeleteDraftByUserId(UserSession?.UserId ?? 0, topologyType, _client);
                return new ResponseBase<TopologySaveReceipt>
                {
                    Code = 20000,
                    Data = new TopologySaveReceipt
                    {
                        ResultCode = 1,
                    }
                };
            }

            if (data.ResultCode == 10)
            {
                var topologyInfo = await _client.Queryable<TopologyInfo>().FirstAsync(t => t.Id == data.TopologyId);
                if (topologyInfo != null)
                {
                    var message = new StringBuilder();
                    var code = data.Data.Value<string>("code") ?? string.Empty;
                    if (string.IsNullOrEmpty(code))
                    {
                        return new ResponseBase<TopologySaveReceipt>()
                        {
                            Code = 40301,
                            Message = MessageContext.GetErrorValue("Topology_MissingCode")
                        };
                    }

                    var isError = await SaveTolopogyInfo(data.Data, code, topologyInfo, message);

                    if (isError)
                    {
                        return new ResponseBase<TopologySaveReceipt>
                        {
                            Code = 40301,
                            Message = message.ToString()
                        };
                    }
                    return new ResponseBase<TopologySaveReceipt>
                    {
                        Code = 20000,
                        Data = new TopologySaveReceipt
                        {
                            ResultCode = 1,
                            TopologyId = topologyInfo.Id,
                            Flag = topologyInfo.TopologyFlag,
                        }
                    };
                }
            }

            {
                var message = new StringBuilder();
                var receipt = await AddTopologyInfo(data.Data, data.TopologyType, message);
                if (receipt.ResultCode == 99)
                {
                    return new ResponseBase<TopologySaveReceipt>
                    {
                        Code = 40301,
                        Message = message.ToString()
                    };
                }
                receipt.ResultCode = 1;
                return new ResponseBase<TopologySaveReceipt>
                {
                    Code = 20000,
                    Data = receipt
                };
            }
        }
    }
}
