﻿using Akka.Actor;
using Akka.Util.Internal;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Siemens.PanelManager.Common;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.DeviceDataFlow.Model;
using Siemens.PanelManager.DeviceDataFlow.StaticData;
using Siemens.PanelManager.Model.Algorithm;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.Topology;
using Siemens.PanelManager.Model.DataFlow;
using Siemens.PanelManager.Model.Topology3D;
using SqlSugar;
using SqlSugar.Extensions;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.DeviceDataFlow.DataFlowActors
{
    internal class TopologyActor : ReceiveActor
    {
        private readonly SiemensCache _cache;
        private readonly ILogger<TopologyActor> _logger;
        private readonly IServiceProvider _provider;

        public TopologyActor(SiemensCache cache, ILogger<TopologyActor> logger, IServiceProvider provider)
        {
            _cache = cache;
            _logger = logger;
            _provider = provider;
            
            Receive<AssetChangeData>(AssetDataChange);
            Receive<(int, Dictionary<string, string>)>(p => AssetDataChange(p.Item1, p.Item2));
        }


        #region 对外输入处理
        private void AssetDataChange(AssetChangeData changeData)
        {
            AssetDataChange(changeData.AssetId, changeData.ChangeDatas);
        }

        private void AssetDataChange(int assetId, Dictionary<string, string> changeData)
        {
            var list = TopologyStaticData.Instance.Listeners.Where(t => t.RelatedAsset.ContainsKey(assetId)).ToList();
            foreach (var topology in list)
            {
                var ruleInfos = topology.RelatedAsset[assetId].ToArray();
                if (ruleInfos.Length > 0)
                {
                    var changeDataResult = GetChangeData(assetId, changeData, ruleInfos);

                    if (changeDataResult != null && changeDataResult.Count > 0)
                    {
                        var topologyCacheKey = "TopologyCurrently:{0}";

                        var cacheData = new Dictionary<string, string>();
                        foreach (var nodeData in changeDataResult)
                        {
                            foreach (var data in nodeData.Value)
                            {
                                cacheData.TryAdd($"[{nodeData.Key}].[{data.Key}]", data.Value);
                            }
                        }

                        _cache.SetHashData(string.Format(topologyCacheKey, topology.TopologyId), cacheData, 120);
                    }
                }
            }
        }

        #endregion

        #region 内部方法
        private Dictionary<string, Dictionary<string, string>> GetChangeData(int assetId, Dictionary<string, string> change, TopologyRuleInfo[] ruleInfos)
        {
            var newValue = new Dictionary<string, string>(change);
            var values = new Dictionary<string, Dictionary<string, string>>();
            var funcNameList = ruleInfos
                .Where(r => !string.IsNullOrEmpty(r.FormatFunction))
                .Select(r =>
                {
                    try
                    {
                        var data = JsonConvert.DeserializeObject<TopologyFormatFunction>(r.FormatFunction ?? string.Empty);
                        return data;
                    }
                    catch
                    {
                        return null;
                    }
                })
                .Where(f => f != null && !string.IsNullOrEmpty(f.FunctionName))
                .Select(f => f!.FunctionName)
                .Distinct()
                .ToArray();

            if (funcNameList.Length > 0)
            {
                foreach (var funcName in funcNameList)
                {
                    if (_func.TryGetValue(funcName, out var func))
                    {
                        func(newValue);
                    }
                }
            }

            foreach (var kv in newValue)
            {
                var ruleList = ruleInfos.Where(r => r.DataPoint == kv.Key).ToList();

                foreach (var r in ruleList)
                {
                    #region target identify
                    if (string.IsNullOrEmpty(r.TargetIdentify))
                    {
                        _logger.LogDebug($"Topology Id: {r.TopologyId}, Rule Id: {r.Id} 配置有问题");
                        continue;
                    }
                    Dictionary<string, string> valueDic;
                    if (values.TryGetValue(r.TargetIdentify, out var existDic))
                    {
                        valueDic = existDic;
                    }
                    else
                    {
                        valueDic = new Dictionary<string, string>();
                        values.Add(r.TargetIdentify, valueDic);
                    }
                    #endregion

                    var propName = kv.Key;
                    if (!string.IsNullOrEmpty(r.TargetProperty))
                    {
                        var match = Regex.Match(r.TargetProperty, "^\\$\\{([\\w]+)\\}\\.([\\w|\\d|_]+)$");
                        if (match.Success)
                        {
                            var type = match.Groups[1].Value;
                            propName = match.Groups[2].Value;

                            switch (type)
                            {
                                case "AssetId":
                                    var valueObj = new JObject
                                    {
                                        { propName, kv.Value },
                                        { "assetId", assetId }
                                    };
                                    valueDic.TryAdd("assetId", assetId.ToString());
                                    valueDic.TryAdd(propName, kv.Value);
                                    continue;
                                default: break;
                            }
                        }
                        else
                        {
                            valueDic.TryAdd(r.TargetProperty, kv.Value);
                            continue;
                        }
                    }

                    valueDic.TryAdd(kv.Key, kv.Value);
                }
            }

            return values;
        }


        #endregion

        #region 属性处理方法
        private static readonly Dictionary<string, Action<Dictionary<string, string>>> _func = new Dictionary<string, Action<Dictionary<string, string>>>()
        {
            ["ChangeSwitch"] = ChangeSwitch,
            ["ChangeBreakerPosition"] = ChangeBreakerPosition,
            ["ExceptByK"] = ExceptByK,
        };

        private static void ChangeSwitch(Dictionary<string, string> value)
        {
            if(value.TryGetValue("Switch", out var switchValue)) 
            {
                switch (switchValue) 
                {
                    case "1":
                        value.AddOrSet("Switch", "1");
                        break;
                    case "2":
                        value.AddOrSet("Switch", "2");
                        break;
                    case "0":
                    default:
                        value.AddOrSet("Switch", "0");
                        break;
                }

                if (value.TryGetValue("HasTriped", out var tripedValue) && "1".Equals(tripedValue))
                {
                    value.AddOrSet("Switch", "1");
                }
            }
        }

        private static void ChangeBreakerPosition(Dictionary<string, string> value)
        {
            if (value.TryGetValue("BreakerPosition", out var postion))
            {
                switch (postion)
                {
                    case "0":
                        value.AddOrSet("BreakerPosition", "0");
                        break;
                    case "1":
                        value.AddOrSet("BreakerPosition", "1");
                        break;
                    case "2":
                        value.AddOrSet("BreakerPosition", "3");
                        break;
                    default:
                        value.AddOrSet("BreakerPosition", "2");
                        break;
                }
            }
        }

        private static void ExceptByK(Dictionary<string, string> value)
        {
            if (value.TryGetValue("P", out var pStr) && decimal.TryParse(pStr, out decimal p))
            {
                value.AddOrSet("P", (Math.Round(p / 1000m, 2)).ToString());
            }

            if (value.TryGetValue("Q", out var qStr) && decimal.TryParse(qStr, out decimal q))
            {
                value.AddOrSet("Q", (Math.Round(q / 1000m, 2)).ToString());
            }
        }
        #endregion
    }
}
