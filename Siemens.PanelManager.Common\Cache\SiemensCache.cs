﻿using System.Collections.Concurrent;
using System.Diagnostics.CodeAnalysis;

namespace Siemens.PanelManager.Common.Cache
{
    public class SiemensCache
    {
        public T? Get<T>([DisallowNull] string key)
        {
            return CacheHelper.Get<T>(key);
        }

        public void Set<T>([Disal<PERSON>Null] string key, T value)
        {
            CacheHelper.Set<T>(key, value);
        }

        public void Set<T>([DisallowNull] string key, T value, TimeSpan timeSpan) 
        {
            CacheHelper.Set(key, value, timeSpan);
        }

        public void SetBySlip<T>([DisallowNull] string key, T data, TimeSpan timeSpan)
        {
            CacheHelper.SetBySlip(key, data, timeSpan);
        }

        public void Clear([DisallowNull] string key) 
        {
            CacheHelper.Remove(key);
        }

        public void Clear<T>([DisallowNull] string key, out T? data)
        {
            CacheHelper.Remove(key, out data);
        }

        public T GetOrCreate<T>([DisallowNull] string key, Func<T> createFunc, TimeSpan? timeSpan = null)
        {
            return CacheHelper.GetOrCreate(key, createFunc, false, timeSpan);
        }

        public Task<T> GetOrCreateAsync<T>([DisallowNull] string key, Func<Task<T>> createFunc, TimeSpan? timeSpan = null)
        {
            return CacheHelper.GetOrCreateAsync(key, createFunc, false, timeSpan);
        }

        public string[] SimpleKeys()
        {
            return CacheHelper.GetSimpleKeys();
        }

        public bool Contains(string key)
        {
            return CacheHelper.ContainsKey(key);
        }

        #region Hash Table
        public Dictionary<string, string> GetHashData(string key, string[] keys)
        {
            return CacheHelper.GetHashValues<string>(key, keys);
        }

        public Dictionary<string, T> GetHashData<T>(string key, params string[] keys)
        {
            if (key == null || key.Length == 0)
            {
                return new Dictionary<string, T>();
            }

            return CacheHelper.GetHashValues<T>(key, keys);
        }

        public Dictionary<string, string> GetHashAllData(string key)
        {
            return CacheHelper.GetHashAll<string>(key);
        }

        public void SetHashData(string key, Dictionary<string, string> keyValues, double times = 0)
        {
            CacheHelper.SetHashValues(key, keyValues, times);
        }

        public void SetHashData<T>(string key, string subKey, T data)
        {
            CacheHelper.SetHashValues<T>(key, new Dictionary<string, T>() { [subKey] = data });
        }

        public void SetHashData<T>(string key, Dictionary<string, T> hashDatas)
        {
            CacheHelper.SetHashValues<T>(key, hashDatas);
        }

        public void RemoveHashData(string key)
        {
            CacheHelper.RemoveHashAllData(key);
        }

        public void RemoveHashData(string key, string[] hashKeys)
        {
            foreach (var hashKey in hashKeys)
            {
                CacheHelper.RemoveHashData(key, hashKey);
            }
        }
        #endregion
    }
}
