﻿[
  {
    "Type": "MQTT",
    "Name": "AutoStart",
    "Value": "",
    "Sort": 1
  },
  {
    "Type": "MQTT",
    "Name": "Broker",
    "Value": "",
    "Sort": 2
  },
  {
    "Type": "MQTT",
    "Name": "Port",
    "Value": "",
    "Sort": 3
  },
  {
    "Type": "MQTT",
    "Name": "ClientId",
    "Value": "",
    "Sort": 4
  },
  {
    "Type": "MQTT",
    "Name": "UseTLS",
    "Value": "",
    "Sort": 5
  },
  {
    "Type": "MQTT",
    "Name": "TLSPemFile",
    "Value": "",
    "Sort": 6
  },
  {
    "Type": "MQTT",
    "Name": "AuthenticationType",
    "Value": "",
    "Sort": 7
  },
  {
    "Type": "MQTT",
    "Name": "UserName",
    "Value": "",
    "Sort": 8
  },
  {
    "Type": "MQTT",
    "Name": "Password",
    "Value": "",
    "Sort": 9
  },
  {
    "Type": "MQTT",
    "Name": "Topic",
    "Value": "",
    "Sort": 10
  },
  {
    "Type": "MQTT",
    "Name": "ClientPemFile",
    "Value": "",
    "Sort": 11
  },
  {
    "Type": "MQTT",
    "Name": "ClientPrivateFile",
    "Value": "",
    "Sort": 12
  }
]