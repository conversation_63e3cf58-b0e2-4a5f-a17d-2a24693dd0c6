﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.Model.UDC;
using Siemens.PanelManager.Server.DataPoint;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;
using System.Diagnostics.CodeAnalysis;
using System.Text.RegularExpressions;
using Permission = Siemens.PanelManager.WebApi.StaticContent.Permission;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class ThirdPartDeviceController : SiemensApiControllerBase
    {
        private ILogger _logger;
        private ISqlSugarClient _client;
        private IServiceProvider _provider;
        public ThirdPartDeviceController(IServiceProvider provider, SiemensCache cache, SqlSugarScope client, ILogger<ThirdPartDeviceController> logger)
            : base(provider, cache)
        {
            _provider = provider;
            _client = client;
            _logger = logger;
        }

        [HttpPost("UploadThirdPartJson")]
        [SwaggerOperation(Summary = "Swagger_ThirdPartDevice_UploadThirdPartJson", Description = "Swagger_ThirdPartDevice_UploadThirdPartJson_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> UploadThirdPartJson([FromQuery] string assetType, [FromQuery] string name, [FromQuery] string thirdDeviceName, [FromForm] IFormCollection form)
        {
            if (string.IsNullOrWhiteSpace(name) || string.IsNullOrWhiteSpace(assetType))
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }
            var exits = await _client.Queryable<ThirdModelConfig>().AnyAsync(t => t.Name.ToLower() == name.ToLower());
            if (exits)
            {
                return new ResponseBase<string>
                {
                    Code = 40300,
                    Message = MessageContext.ThirdModelTypeIsExists
                };
            }

            //都以第三方设备处理
            assetType = "GeneralDevice";

            var nameLower = name.ToLower();
            var modelConfig = await _client.Queryable<ThirdModelConfig>().Where(a => a.AssetType == assetType
            && a.NameLower == nameLower).FirstAsync();

            if (modelConfig == null)
            {
                modelConfig = new ThirdModelConfig()
                {
                    AssetType = assetType,
                    Name = name,
                    ThirdDeviceName = thirdDeviceName,
                    NameLower = nameLower,
                    Code = Guid.NewGuid().ToString(),
                    CreatedBy = UserName,
                    CreatedTime = DateTime.Now,
                };
            }
            else
            {
                modelConfig.ThirdDeviceName = thirdDeviceName;
            }

            var file = form.Files.FirstOrDefault(f => Regex.IsMatch(f.FileName, "\\.json$"));

            if (file == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            using (var sr = new StreamReader(file.OpenReadStream()))
            {
                var data = sr.ReadToEnd();

                try
                {
                    var template = JsonConvert.DeserializeObject<ThreePartModelTemplate>(data);

                    if (template == null || string.IsNullOrEmpty(template.Version)
                                         || template.Treeview == null
                                         || template.Treeview.Length == 0)
                    {
                        return new ResponseBase<string>()
                        {
                            Code = 40300,
                            Message = MessageContext.ErrorJsonParam
                        };
                    }

                    var systemConfig = await _client.Queryable<SystemConfig>().FirstAsync(c => c.Type == "SpecialConfig" && c.Name == "EnergyCoefficient");
                    if (systemConfig == null || !decimal.TryParse(systemConfig.Value, out var coefficientDecimal) || coefficientDecimal != 1M)
                    {
                        template.ChangeJson();
                    }

                    var service = _provider.GetRequiredService<DataPointServer>();
                    await service.SaveThirdDeviceConfigInfo(modelConfig.Code, template, _client, UserName);
                    modelConfig.JsonData = JsonConvert.SerializeObject(template);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "文件结构错误");
                    return new ResponseBase<string>()
                    {
                        Code = 40300,
                        Message = MessageContext.ErrorParam
                    };
                }
            }

            modelConfig.UpdatedBy = UserName;
            modelConfig.UpdatedTime = DateTime.Now;

            await _client.Storageable(modelConfig).ExecuteCommandAsync();

            await DelUniversalDevices(modelConfig.AssetType,modelConfig.NameLower);

            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = modelConfig.Code
            };
        }
       
        /// <summary>
        /// 上传json文件，之前历史遗留得数据
        /// </summary>
        /// <param name="assetType"></param>
        /// <param name="nameLower"></param>
        /// <returns></returns>
        private async Task DelUniversalDevices(string assetType, string nameLower)
        {
            try
            {
                //开启事务
                _client.Ado.BeginTran();

                var modelConfig = await _client.Queryable<ThirdModelConfig>()
                                             .Where(p => p.AssetType == assetType && p.NameLower == nameLower).FirstAsync();
                if (modelConfig == null)
                {
                    return;
                }

                var assetIds = await _client.Queryable<AssetInfo>().Where(p => p.ThirdPartCode == modelConfig.Code)
                                                                                          .Select(p => p.Id).ToListAsync();

                if (assetIds != null && assetIds.Any())
                {
                    var universalDeviceConfigs = await _client.Queryable<UniversalDeviceConfig>().Where(p => assetIds.Contains(p.AssetId)).ToListAsync();

                    var universalDeviceConfigIds = universalDeviceConfigs.Where(p => p.IsBit).Select(p => p.Id).Distinct().ToList();

                    await _client.Deleteable<UniversalDeviceConfig>(universalDeviceConfigs).ExecuteCommandAsync();

                    await _client.Deleteable<BitConfig>().Where(p => universalDeviceConfigIds.Contains(p.UniversalDeviceConfigId)).ExecuteCommandAsync();

                    //提交事务
                    _client.Ado.CommitTran();
                }
            }
            catch (Exception ex)
            {
                //回滚事务
                _client.Ado.RollbackTran();
            }
        }
        [HttpGet("DownloadJson")]
        [SwaggerOperation(Summary = "Swagger_ThirdPartDevice_DownloadJson", Description = "Swagger_ThirdPartDevice_DownloadJson_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<IActionResult> DownloadJson(string code)
        {
            var query = await _client.Queryable<ThirdModelConfig>().FirstAsync(a=>a.Code== code);
            if (query != null)
            {
                var jsonData = query.JsonData;
                // 将 JSON 字符串转换为字节数组
                byte[] jsonBytes = System.Text.Encoding.UTF8.GetBytes(jsonData ?? "");
                string fileName = query.Name;
                // 设置响应头，指定文件名和内容类型
                //Response.Headers.Append("Content-Disposition", $"attachment; filename={fileName}.json");
                //Response.ContentType = "application/json";

                // 返回文件作为响应
                return File(jsonBytes, "application/json", $"{fileName}.json");
            }
            else
            {
                // 返回一个空的 JSON 文件
                byte[] emptyJsonBytes = System.Text.Encoding.UTF8.GetBytes("{}");
                return File(emptyJsonBytes, "application/json", "empty.json");
            }
            
        }

        [HttpGet("GetThirdPartList")]
        [SwaggerOperation(Summary = "Swagger_ThirdPartDevice_GetThirdPartList", Description = "Swagger_ThirdPartDevice_GetThirdPartList_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<List<ThirdModelInfo>>> GetThirdPartList([AllowNull] string? assetType)
        {
            var query = _client.Queryable<ThirdModelConfig>();
            assetType = "GeneralDevice";
            if (!string.IsNullOrEmpty(assetType))
            {
                query = query.Where(a => a.AssetType == assetType);
            }
            var configs = await query.OrderByDescending(a => a.Id).Select(c => new ThirdModelInfo()
            {
                AssetType = c.AssetType,
                Name = c.Name,
                Code = c.Code,
                ThirdDeviceName = c.ThirdDeviceName,
            }).ToListAsync();

            return new ResponseBase<List<ThirdModelInfo>>()
            {
                Code = 20000,
                Data = configs
            };
        }

        [HttpDelete("{code}")]
        [SwaggerOperation(Summary = "Swagger_ThirdPartDevice_Delete", Description = "Swagger_ThirdPartDevice_Delete_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<bool>> DeleteThridModel(string code)
        {
            _client.Ado.BeginTran();
            int result = 0;
            try
            {

                var universalList = await _client.Queryable<UniversalDeviceConfig>().Where(a => a.ThirdPartCode == code).ToListAsync();
                foreach (var t in universalList)
                {
                    var bit = await _client.Deleteable<BitConfig>().Where(t => t.UniversalDeviceConfigId == t.Id).ExecuteCommandAsync();
                }
                var d = await _client.Deleteable<UniversalDeviceConfig>().Where(t => t.ThirdPartCode == code).ExecuteCommandAsync();
                result = await _client.Deleteable<ThirdModelConfig>().Where(t => t.Code == code).ExecuteCommandAsync();

                var service = _provider.GetRequiredService<DataPointServer>();
                await service.DeleteThirdDeviceConfigInfo(code, _client);

            }
            catch (Exception ex)
            {
                _client.Ado.RollbackTran();
            }
           
            _client.Ado.CommitTran();
            return new ResponseBase<bool>()
            {
                Code = 20000,
                Data = result > 0,
                Message = result > 0 ? MessageContext.Success : MessageContext.NotExists
            };
        }

        [HttpPut("UpdateThirdModel")]
        [SwaggerOperation(Summary = "Swagger_ThirdPartDevice_Update", Description = "Swagger_ThirdPartDevice_Update_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<bool>> UpdateThirdModel([FromQuery] string code, [FromQuery] string AssetType, [FromQuery] string ThirdDeviceName, [FromQuery] string Name , [FromForm] IFormCollection form)
        {
            if (string.IsNullOrEmpty(code))
            {
                return new ResponseBase<bool>
                {
                    Code = 40300,
                    Data = false,
                    Message = MessageContext.ErrorParam
                };
            }

            AssetType = "GeneralDevice";

            var config = await _client.Queryable<ThirdModelConfig>().FirstAsync(t => t.Code == code);
            if (config == null)
            {
                return new ResponseBase<bool>
                {
                    Code = 40300,
                    Data = false,
                    Message = MessageContext.NotExists
                };
            }
            //如果名称有改变，校验是否重复
            if (config.Name != Name) {
                var exits = await _client.Queryable<ThirdModelConfig>().AnyAsync(t =>   t.Name.ToLower() ==Name.ToLower() && t.Code != code);
                if (exits)
                {
                    return new ResponseBase<bool>
                    {
                        Code = 40300,
                        Data = false,
                        Message = MessageContext.ThirdModelTypeIsExists
                    };
                }
            }
            var file = form.Files.FirstOrDefault(f => Regex.IsMatch(f.FileName, "\\.json$"));
            
            if (file != null)
            {
                using (var sr = new StreamReader(file.OpenReadStream()))
                {
                    var data = sr.ReadToEnd();

                    try
                    {
                        var template = JsonConvert.DeserializeObject<ThreePartModelTemplate>(data);

                        if (template == null || string.IsNullOrEmpty(template.Version)
                                             || template.Treeview == null
                                             || template.Treeview.Length == 0)
                        {
                            return new ResponseBase<bool>()
                            {
                                Code = 40300,
                                Message = MessageContext.ErrorJsonParam
                            };
                        }

                        var systemConfig = await _client.Queryable<SystemConfig>().FirstAsync(c => c.Type == "SpecialConfig" && c.Name == "EnergyCoefficient");
                        if (systemConfig == null || !decimal.TryParse(systemConfig.Value, out var coefficientDecimal) || coefficientDecimal != 1M)
                        {
                            template.ChangeJson();
                        }
                        config.JsonData = JsonConvert.SerializeObject(template);

                        var service = _provider.GetRequiredService<DataPointServer>();
                        await service.SaveThirdDeviceConfigInfo(code, template, _client, UserName);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "文件结构错误");
                        return new ResponseBase<bool>()
                        {
                            Code = 40300,
                            Message = MessageContext.ErrorParam
                        };
                    }
                    
                }
            }
          
            config.UpdatedBy = UserName;
            config.UpdatedTime = DateTime.Now;
            config.AssetType = AssetType;
            config.Name = Name;
            config.ThirdDeviceName = ThirdDeviceName;
            await _client.Updateable(config).WhereColumns("code").ExecuteCommandAsync();

            var assets = await _client.Queryable<AssetInfo>().Where(a => a.ThirdPartCode == config.Code).ToListAsync();
            await UpdateUniversalConfigByAsset(code ?? string.Empty, assets);

            return new ResponseBase<bool>
            {
                Code = 20000,
                Data = true,
                Message = MessageContext.Success
            };
        }

        /// <summary>
        /// 获取第三方设备名称
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetThirdDeviceName")]
        [SwaggerOperation(Summary = "Swagger_Asset_GetThirdDeviceName", Description = "Swagger_Asset_GetThirdDeviceName_Desc")]
        public IActionResult GetThirdDeviceName()
        {
            ResponseBase<List<Dictionary<string,bool>>> result;

            try
            {
                var typeList = _client.Queryable<ThirdModelConfig>().Select(a => a.ThirdDeviceName).ToList();
                var dicData = new List<Dictionary<string,bool>>();

                for (int i = 1; i <= 16; i++)
                {
                    string typeStr = "type" + i;
                    Dictionary<string, bool> keyValues = new Dictionary<string, bool>();
                    if (typeList.Contains(typeStr))
                    {
                        keyValues.Add(typeStr, false);
                    }
                    else
                    {
                        keyValues.Add(typeStr, true);
                        
                    }
                    dicData.Add(keyValues);
                }

                result = new ResponseBase<List<Dictionary<string, bool>>>() { Code = 20000, Data = dicData };
            }
            catch (Exception ex)
            {
                result = new ResponseBase<List<Dictionary<string, bool>>>() { Code = 50000, Message = ex.Message };
            }

            return Ok(result);
        }
        private async Task UpdateUniversalConfigByAsset(string thirdPartCode, List<AssetInfo> assetInfos)
        {
            var assetIds = assetInfos.Select(a => a.Id).ToList();
            var allConfigs = await _client.Queryable<UniversalDeviceConfig>().Where(u => assetIds.Contains(u.AssetId)).ToListAsync();
            var thirdModelConfig = await _client.Queryable<ThirdModelConfig>().Where(t => t.Code == thirdPartCode).FirstAsync();
            if (thirdModelConfig == null)
            {
                return;
            }

            var jsonData = JsonConvert.DeserializeObject<ThreePartModelTemplate>(thirdModelConfig.JsonData!);

            if (jsonData == null || jsonData.Treeview == null)
            {
                return;
            }

            var propertiesGroup = new Dictionary<string, PropertyInfo[]>();
            foreach (var view in jsonData.Treeview)
            {
                var groupName = view.Name;
                if (view.SubGroups != null)
                {
                    foreach (var subGroup in view.SubGroups)
                    {
                        if (!string.IsNullOrEmpty(subGroup.Name))
                        {
                            groupName = subGroup.Name;
                        }

                        if (subGroup.Properties != null)
                        {
                            AddOrSet(propertiesGroup, subGroup.Name ?? Guid.NewGuid().ToString(), subGroup.Properties.ToArray());
                        }
                    }
                }

                if (view.Properties != null)
                {
                    AddOrSet(propertiesGroup, groupName ?? Guid.NewGuid().ToString(), view.Properties.ToArray());
                }
            }

            var insertList = new List<UniversalDeviceConfig>();
            var updateList = new List<UniversalDeviceConfig>();
            var deleteList = new List<UniversalDeviceConfig>();
            foreach (var asset in assetInfos)
            {
                var configs = allConfigs.Where(c => c.AssetId == asset.Id).ToList();
                foreach (var pg in propertiesGroup)
                {
                    foreach (var p in pg.Value)
                    {
                        var config = configs.FirstOrDefault(c => c.PropertyEnName == p.PropertyName);
                        if (config != null)
                        {
                            configs.Remove(config);
                            if (config.PropertyCnName != p.DescriptionInGerman
                                || config.GroupName != pg.Key
                                || config.Unit != p.Unit)
                            {
                                config.PropertyCnName = p.DescriptionInGerman;
                                config.GroupName = pg.Key;
                                config.Unit = p.Unit;
                                config.ThirdPartCode = thirdPartCode;
                                config.UpdatedBy = UserName;
                                config.UpdatedTime = DateTime.Now;
                                updateList.Add(config);
                            }
                        }
                        else
                        {
                            config = new UniversalDeviceConfig()
                            {
                                Id = 0,
                                PropertyEnName = p.PropertyName,
                                PropertyCnName = p.DescriptionInGerman,
                                AssetId = asset.Id,
                                IsBit = false,
                                Coefficient = "1",
                                GroupName = pg.Key,
                                Unit = p.Unit,
                                CreatedBy = UserName,
                                ThirdPartCode = thirdPartCode,
                                CreatedTime = DateTime.Now,
                                UpdatedBy = UserName,
                                UpdatedTime = DateTime.Now,
                            };

                            insertList.Add(config);
                        }
                    }
                }

                if (configs.Count > 0)
                {
                    deleteList.AddRange(configs);
                }
            }

            if (insertList.Count > 0)
            {
                await _client.Insertable(insertList).ExecuteCommandAsync();
            }

            if (updateList.Count > 0)
            {
                await _client.Updateable(updateList).ExecuteCommandAsync();
            }

            if (deleteList.Count > 0)
            {
                var bitConfigIds = deleteList.Where(c => c.IsBit).Select(c => c.Id).ToList();
                await _client.Deleteable<BitConfig>().Where(b => bitConfigIds.Contains(b.UniversalDeviceConfigId)).ExecuteCommandAsync();
                await _client.Deleteable(deleteList).ExecuteCommandAsync();
            }
        }

        private void AddOrSet(Dictionary<string, PropertyInfo[]> dic,
            string groupName,
            PropertyInfo[] properties)
        {
            if (dic.TryGetValue(groupName, out var propertyInfos))
            {
                var newList = new List<PropertyInfo>();
                newList.AddRange(propertyInfos);
                newList.AddRange(properties);
                dic[groupName] = newList.ToArray();
            }
            else
            {
                dic.Add(groupName, properties);
            }
        }
    }
}
