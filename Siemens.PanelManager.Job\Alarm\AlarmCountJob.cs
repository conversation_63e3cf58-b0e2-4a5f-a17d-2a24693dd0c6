﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Quartz;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.Alarm;
using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.DataFlow;
using Siemens.PanelManager.Server.Alarm;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.Alarm
{
    [DisallowConcurrentExecution]
    public class AlarmCountJob : JobBase
    {
        public override string Name => "AlarmCountJob";

        private readonly ILogger _logger;
        private readonly IServiceProvider _provider;

        public AlarmCountJob(ILogger<AlarmCountJob> logger, IServiceProvider provider)
        {
            _logger = logger;
            _provider = provider;
        }

        public override async Task Execute()
        {
            var cache = _provider.GetRequiredService<SiemensCache>();
            using (var client = _provider.GetRequiredService<ISqlSugarClient>())
            {
                var result = new Dictionary<AssetLevel, Dictionary<string, AlarmCountModel>>();
                int pageNum = 1;
                int pageSize = 1000;
                var assetIds = new List<int>();

                var alarmStartTime = DateTime.MinValue;
                var timeConfig = await client.Queryable<AlarmQueryConfig>().FirstAsync(c => c.ParamCode == "alarmQueryStartTime");
                if (timeConfig != null && !string.IsNullOrEmpty(timeConfig.QueryValue) && DateTime.TryParse(timeConfig.QueryValue, out var time))
                {
                    alarmStartTime = time;
                    var hasRun = cache.Get<bool>("AlarmQueryStartTimeHasRun");

                    if (!hasRun)
                    {
                        var server = _provider.GetRequiredService<AlarmExtendServer>();
                        await server.FinishAlarmByAlarmStartTime(time, client, "System");
                        cache.Set<bool>("AlarmQueryStartTimeHasRun", true);
                    }
                }

                while (true)
                {
                    var alarms = await client.Queryable<AlarmLog>()
                        .Where(a => (a.EventType == AlarmEventType.UdcAlarm || a.EventType == AlarmEventType.Alarm || a.EventType == AlarmEventType.BreakerTrip)
                        && a.Status >= AlarmLogStatus.New && a.Status < AlarmLogStatus.Finish
                        && a.AssetId.HasValue)
                        .WhereIF(alarmStartTime != DateTime.MinValue, a => a.CreatedTime >= alarmStartTime)
                        .OrderBy(a => a.Id)
                        .ToPageListAsync(pageNum, pageSize);
                    pageNum++;

                    if (alarms.Count > 0)
                    {
                        foreach (var alarm in alarms)
                        {
                            if (!string.IsNullOrEmpty(alarm.SubstationName))
                            {
                                var model = GetOrCreateAlarmCountModel(result, alarm.SubstationName, AssetLevel.Substation);
                                UpdateAlarmCountModel(model, alarm);
                            }

                            if (!string.IsNullOrEmpty(alarm.PanelName))
                            {
                                var model = GetOrCreateAlarmCountModel(result, alarm.PanelName, AssetLevel.Panel);
                                UpdateAlarmCountModel(model, alarm);
                            }

                            if (!string.IsNullOrEmpty(alarm.CircuitName))
                            {
                                var model = GetOrCreateAlarmCountModel(result, alarm.CircuitName, AssetLevel.Circuit);
                                UpdateAlarmCountModel(model, alarm);
                            }

                            if (alarm.AssetId.HasValue)
                            {
                                assetIds.Add(alarm.AssetId.Value);
                            }
                        }
                    }
                    else
                    {
                        break;
                    }
                }

                foreach (var kv in result)
                {
                    cache.SetHashData<AlarmCountModel>($"AlarmCount-{kv.Key}", kv.Value);
                }

                var refObj = _provider.GetRequiredService<IAssetDataProxyRef>();
                assetIds = assetIds.Distinct().ToList();
                foreach (var assetId in assetIds)
                {
                    refObj.InputData(new AssetInputData()
                    {
                        AssetId = assetId,
                        Datas = new Dictionary<string, string>()
                        {
                            ["HaveAlarm"] = "1"
                        }
                    });
                }

                var otherAssetIds = await client.Queryable<AssetInfo>()
                    .Where(a => a.AssetLevel != AssetLevel.Device && a.AssetLevel != AssetLevel.Area)
                    .Select(a => a.Id)
                    .ToListAsync();

                foreach (var assetId in otherAssetIds)
                {
                    refObj.InputData(new AssetInputData()
                    {
                        AssetId = assetId,
                        Datas = new Dictionary<string, string>()
                        {
                            ["HaveAlarm"] = "Check"
                        }
                    });
                }
            }
        }

        private AlarmCountModel GetOrCreateAlarmCountModel(Dictionary<AssetLevel, Dictionary<string, AlarmCountModel>> all, string assetName, AssetLevel level)
        {
            if (all.TryGetValue(level, out var dic) && dic != null)
            {
                if (dic.TryGetValue(assetName, out var model) && model != null)
                {
                    return model;
                }

                model = new AlarmCountModel();
                dic.Add(assetName, model);
                return model;
            }

            var newModel = new AlarmCountModel();
            dic = new Dictionary<string, AlarmCountModel>()
            {
                [assetName] = newModel,
            };
            all.Add(level, dic);
            return newModel;
        }

        private void UpdateAlarmCountModel(AlarmCountModel model, AlarmLog log)
        {
            switch (log.Severity)
            {
                case AlarmSeverity.High:
                    model.HighCount += 1;
                    break;
                case AlarmSeverity.Low:
                    model.LowCount += 1;
                    break;
                case AlarmSeverity.Middle:
                    model.MiddleCount += 1;
                    break;
                default: break; 
            }

            switch (log.EventType)
            {
                case AlarmEventType.Alarm:
                    model.AlarmCount += 1;
                    break;
                case AlarmEventType.UdcAlarm:
                    model.UDCAlarmCount += 1;
                    break;
                case AlarmEventType.BreakerTrip:
                    model.BreakerTripCount += 1;
                    break;
                default: break;
            }
        }
    }
}
