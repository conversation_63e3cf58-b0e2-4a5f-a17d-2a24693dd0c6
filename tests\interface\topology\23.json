{"info": {"_postman_id": "a562f13b-e7ef-4881-854f-379c07e027da", "name": "23导入资产为X配电房2配电柜X回路X断路器X配电表生成单线图", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 7", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "导入资产数据 Copy 8", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "let jobid = pm.response.json().data//获取Areaid\r", "pm.environment.set('jobid',jobid)//把id保存到全局变量中\r", "console.log(jobid)\r", "function sleep(numberMillis){\r", "    var now = new Date();\r", "    var exitTime = now.getTime() + numberMillis;\r", "    while (true){\r", "        now = new Date();\r", "        if (now.getTime() > exitTime)\r", "        return;\r", "    }\r", "};\r", "sleep(5000)"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "from", "type": "file", "src": "./tests/interface/topology/23.xlsx"}]}, "url": {"raw": "{{baseUrl}}/api/v1/Asset/uploadExcel", "host": ["{{baseUrl}}"], "path": ["api", "v1", "<PERSON><PERSON>", "uploadExcel"]}}, "response": []}, {"name": "根据jobid导入资产 Copy 8", "event": [{"listen": "test", "script": {"exec": ["function sleep(numberMillis){\r", "    var now = new Date();\r", "    var exitTime = now.getTime() + numberMillis;\r", "    while (true){\r", "        now = new Date();\r", "        if (now.getTime() > exitTime)\r", "        return;\r", "    }\r", "};pm.test(\"code码为2000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"单线图生成成功\", function () {\r", "    \r", "    pm.expect(pm.response.text()).to.include(\"单线图生成成功\");\r", "});"], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{baseUrl}}/api/v1/Asset/uploadExcelreult/{{jobid}}", "host": ["{{baseUrl}}"], "path": ["api", "v1", "<PERSON><PERSON>", "uploadExcelreult", "{{jobid}}"]}}, "response": []}, {"name": "查找所有拓扑图 Copy 6", "event": [{"listen": "test", "script": {"exec": ["console.log(pm.response.json().data[0].id)\r", "\r", "let tid = pm.response.json().data[0].id//获取单线图id\r", "pm.collectionVariables.set('tid',tid)//把id保存到全局变量中\r", "\r", "pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"生成对应的单线图\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"苏州西门子23\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/sld", "host": ["{{baseUrl}}"], "path": ["api", "v1", "sld"]}}, "response": []}, {"name": "导入资产数据 Copy 9", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "let jobid = pm.response.json().data//获取Areaid\r", "pm.environment.set('jobid',jobid)//把id保存到全局变量中\r", "console.log(jobid)\r", "function sleep(numberMillis){\r", "    var now = new Date();\r", "    var exitTime = now.getTime() + numberMillis;\r", "    while (true){\r", "        now = new Date();\r", "        if (now.getTime() > exitTime)\r", "        return;\r", "    }\r", "};\r", "sleep(5000)"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "from", "type": "file", "src": "./tests/interface/topology/23F.xlsx"}]}, "url": {"raw": "{{baseUrl}}/api/v1/Asset/uploadExcel", "host": ["{{baseUrl}}"], "path": ["api", "v1", "<PERSON><PERSON>", "uploadExcel"]}}, "response": []}, {"name": "根据jobid导入资产 Copy 9", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为2000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"单线图生成失败\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"单线图生成失败\");\r", "});"], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{baseUrl}}/api/v1/Asset/uploadExcelreult/{{jobid}}", "host": ["{{baseUrl}}"], "path": ["api", "v1", "<PERSON><PERSON>", "uploadExcelreult", "{{jobid}}"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 1000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});"]}}], "variable": [{"key": "tid", "value": ""}]}