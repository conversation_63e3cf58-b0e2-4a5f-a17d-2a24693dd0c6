﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Energy
{
    [SugarTable("energy_summary")]
    public class EnergySummary: LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [SugarColumn(ColumnName = "asset_id")]
        public int AssetId { get; set; }

        [SugarColumn(ColumnName = "consumption_date", IsNullable = false)]
        public DateTime ConsumptionDate { get; set; }

        [SugarColumn(ColumnName = "peak_electricity", IsNullable = false)]
        public decimal PeakElectricity { get; set; }

        [SugarColumn(ColumnName = "flat_electricity", IsNullable = false)]
        public decimal FlatElectricity { get; set; }

        [SugarColumn(ColumnName = "valley_electricity", IsNullable = false)]
        public decimal ValleyElectricity { get; set; }

        [SugarColumn(ColumnName = "peak_cost", IsNullable = false)]
        public decimal PeakCost { get; set; }

        [SugarColumn(ColumnName = "flat_cost", IsNullable = false)]
        public decimal FlatCost { get; set; }

        [SugarColumn(ColumnName = "valley_cost", IsNullable = false)]
        public decimal ValleyCost { get; set; }
        [SugarColumn(ColumnName = "high_peak_electricity", IsNullable = true)]
        public decimal HighPeakElectricity { get; set; }
        [SugarColumn(ColumnName = "high_peak_cost", IsNullable = true)]
        public decimal HighPeakCost { get; set; }
        [SugarColumn(ColumnName = "deep_valley_electricity", IsNullable = true)]
        public decimal DeepValleyElectricity { get; set; }
        [SugarColumn(ColumnName = "deep_valley_cost", IsNullable = true)]
        public decimal DeepValleyCost { get; set; }
    }
}
