﻿using Microsoft.AspNetCore.SignalR.Client;
using System;
using System.Threading.Tasks;

namespace Siemens.PanelManager.HubModel.Client
{
    public class AutoTestClient
    {
        private const string PUBLICKEY = "MIIBCgKCAQEApNvPKvV9xDkgBj2ol2qgoowtVHVplhMC40ML7xvj1Ok9a/AquhFXyPWbbsOSLNSX/BMIQM82OFQCHUM3A2e6Iscy1hZanrzToUveZpPxS5U07P9SOHRtPr5KKlXD7WvnOF9oa9D+os0ilY0BBFubFs82zThvruaxSFDPYnYh5vQtEkT6VMwfQ+WNYcNuRSjWk+6kVVJsR+zR5WJD+T2TaR76d6YhmKaEyG0pTde0+DRBSqO4Rg6ARWNskBEHuwlQkGhAZlRO0ThilJWVpppYRv4hyJhbn/RZWE7sIZIqere1LhZr/vVICSppNOv8bbJK5rXNtBnclQDZXcaWbPSHZQIDAQAB";
        private string _url;
        private IHubConnectionBuilder _builder;
        private HubConnection _connection;
        public AutoTestClient(string url)
        {
            _url = url;
            _builder = new HubConnectionBuilder()
                .WithUrl(url);
        }

        private Func<MonitorModel, Task> _monitorFunc = (m) => Task.CompletedTask;
        private Func<LogModel, Task> _logFunc = (m) => Task.CompletedTask;
        private Func<TestItemResult, Task> _testResultFunc = (r) => Task.CompletedTask;
        private Func<Exception, Task> _disconnectFunc = (ex) => Task.CompletedTask;

        public async Task<bool> Start(string userName, string password)
        {
            _connection = _builder.Build();
            _connection.Closed += Disconnect;
            _connection.On("MonitorInfo", _monitorFunc);
            _connection.On("LogInfo", _logFunc);
            _connection.On("AutoTestResult", _testResultFunc);
            await _connection.StartAsync();
            var auth = new AuthLoginModel(userName, password, PUBLICKEY);
            var result = await _connection.InvokeAsync<bool>("login", auth.EncryptCode, auth.RandomCode);
            if (!result)
            {
                _connection = null;
            }
            return result;
        }

        public bool IsConneted
        {
            get
            {
                return _connection?.State == HubConnectionState.Connected;
            }
        }

        private async Task Disconnect(Exception ex)
        {
            _connection = null;
            await _disconnectFunc(ex);
        }

        public async Task Stop()
        {
            if (_connection != null)
            {
                await _connection.StopAsync();
            }
        }

        public async Task StartAutoTest()
        {
            if (_connection != null)
            {
                await _connection.SendAsync("StartAutoTest");
            }
        }

        #region Client Reader

        public void OnMonitor(Func<MonitorModel, Task> func)
        {
            _monitorFunc += func;
        }

        public void OnLogInfo(Func<LogModel, Task> func)
        {
            _logFunc += func;
        }

        public void OnAutoTestResult(Func<TestItemResult, Task> func)
        {
            _testResultFunc += func;
        }



        public void OnDisconnect(Func<Exception, Task> func)
        {
            _disconnectFunc += func;
        }
        #endregion
    }
}
