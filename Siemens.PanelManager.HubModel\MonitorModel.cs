﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.HubModel
{
    public class MonitorModel
    {
        /// <summary>
        /// 系统 CPU
        /// </summary>
        public decimal CPU { get; set; }

        /// <summary>
        /// 系统内存
        /// 单位
        /// </summary>
        public string MemUnit { get; set; } = string.Empty;
        /// <summary>
        /// 系统内存
        /// 总量
        /// </summary>
        public decimal MemTotal { get; set; }
        /// <summary>
        /// 系统内存
        /// 已使用
        /// </summary>
        public decimal MemUsed { get; set; }
        /// <summary>
        /// 开机时间单位分钟
        /// </summary>
        public long RunTime { get; set; }
        /// <summary>
        /// 收集数据的系统时间
        /// </summary>
        public DateTime LogTime { get; set; }
        /// <summary>
        /// 镜像运行程序状态
        /// </summary>
        public DockerProcessStats[] DockerProcessStats { get; set; } = new DockerProcessStats[0];
    }
}
