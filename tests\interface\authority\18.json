{"info": {"_postman_id": "6ad5a9ea-b4e4-4e44-b366-f60126eb682f", "name": "使用超级管理员账号进入panel manager用户管理菜单，添加超级管理员账户", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "用户登录(用户登录)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = pm.response.json().data.token//获取token\r", "pm.collectionVariables.set('token',token)//把token保存到全局变量中\r", "console.log (token)\r", "\r", "pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/login", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "添加用户信息(循环)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "pm.test(\"生成随机密码\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"pwd\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"userName\": \"q1234565\",\r\n    \"personName\": \"h111\",\r\n    \"tel\": \"15751111111\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"roles\": [\r\n        {\r\n            \"id\": 1\r\n        }\r\n    ]\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/users", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "users"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}], "variable": [{"key": "usernameindex", "value": 213, "type": "string"}, {"key": "username", "value": "user-213", "type": "string"}, {"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1lIjoiaGtuNzc3IiwiaHR0cDovL3NjaGVtYXMueG1sc29hcC5vcmcvd3MvMjAwNS8wNS9pZGVudGl0eS9jbGFpbXMvbmFtZWlkZW50aWZpZXIiOiI4ZjVhZTA1Yy02MGM4LTQ3ZjEtODU4Ni1lMDBiMWU4ODk3NmEiLCJTeW5jRGV2aWNlIjoiW10iLCJuYmYiOjE2NzcxMzk5OTEsImV4cCI6MTY3NzEzOTk5MiwiaXNzIjoiU2llbWVuc0lzc3VlciIsImF1ZCI6IldlYkFwcEF1ZGllbmNlIn0.beLpaamUXpfspykblUSWV-bXQYpZHAM2t0AVraXMVCk", "type": "string"}, {"key": "userId", "value": 103, "type": "string"}, {"key": "user2Id", "value": 99, "type": "string"}, {"key": "user3Id", "value": 95, "type": "string"}, {"key": "roleId", "value": 1, "type": "string"}, {"key": "BASE_URL", "value": "http://localhost:5000"}]}