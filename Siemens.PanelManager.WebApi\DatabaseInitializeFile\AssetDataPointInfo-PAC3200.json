[{"Code": "IsConnected", "Name": "IsConnected", "GroupName": "Status", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "UDCConnect", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 1, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Ua", "Name": "Ua", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "V_LN/Inst/Value/L1N", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 2, "ParentName": "VoltageL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ub", "Name": "Ub", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "V_LN/Inst/Value/L2N", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 3, "ParentName": "VoltageL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Uc", "Name": "Uc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "V_LN/Inst/Value/L3N", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 4, "ParentName": "VoltageL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Uab", "Name": "Uab", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "V_LL/Inst/Value/L1L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 5, "ParentName": "VoltageL-L_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ubc", "Name": "Ubc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "V_LL/Inst/Value/L2L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 6, "ParentName": "VoltageL-L_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Uca", "Name": "Uca", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "V_LL/Inst/Value/L3L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 7, "ParentName": "VoltageL-L_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ia", "Name": "Ia", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "I/Inst/Value/L1", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 8, "ParentName": "Current_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ib", "Name": "Ib", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "I/Inst/Value/L2", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9, "ParentName": "Current_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ic", "Name": "Ic", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "I/Inst/Value/L3", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 10, "ParentName": "Current_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "I_Agv", "Name": "I_Agv", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "I/Inst/Value/AVG", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 12, "ParentName": "Current_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "P", "Name": "P", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Power/W/Inst/Value/Sum", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 13, "ParentName": "InstantaneousValuesActivePower_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Pa", "Name": "Pa", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Power/W/Inst/Value/L1", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 14, "ParentName": "InstantaneousValuesActivePower_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Pb", "Name": "Pb", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Power/W/Inst/Value/L2", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 15, "ParentName": "InstantaneousValuesActivePower_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Pc", "Name": "Pc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Power/W/Inst/Value/L3", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 16, "ParentName": "InstantaneousValuesActivePower_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Q", "Name": "Q", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Power/var/Qn/Inst/Value/Sum", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 17, "ParentName": "MeasuringMethodVARn_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Qa", "Name": "Qa", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Power/var/Qn/Inst/Value/L1", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 18, "ParentName": "MeasuringMethodVARn_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Qb", "Name": "Qb", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Power/var/Qn/Inst/Value/L2", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19, "ParentName": "MeasuringMethodVARn_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Qc", "Name": "Qc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Power/var/Qn/Inst/Value/L3", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 20, "ParentName": "MeasuringMethodVARn_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "S", "Name": "S", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Power/VA/Inst/Value/Sum", "Unit": "VA", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 21, "ParentName": "ApparentPower_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor", "Name": "PowFactor", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Power/Factor/Inst/Value/AVG", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 22, "ParentName": "PowerFactor_ActualInstantaneousMeasurementValues", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor_A", "Name": "PowFactor_A", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Power/Factor/Inst/Value/L1", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 23, "ParentName": "PowerFactor_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor_B", "Name": "PowFactor_B", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Power/Factor/Inst/Value/L2", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 24, "ParentName": "PowerFactor_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor_C", "Name": "PowFactor_C", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Power/Factor/Inst/Value/L3", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 25, "ParentName": "PowerFactor_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "F", "Name": "F", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Frequency/Inst/Value/Common", "Unit": "Hz", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 26, "ParentName": "GlobalValues_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ua", "Name": "THD_Ua", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "THD/V_LN/Inst/Value/L1N#", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 27, "ParentName": "THDVoltageL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ub", "Name": "THD_Ub", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "THD/V_LN/Inst/Value/L2N#", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 28, "ParentName": "THDVoltageL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Uc", "Name": "THD_Uc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "THD/V_LN/Inst/Value/L3N#", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 29, "ParentName": "THDVoltageL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ia", "Name": "THD_Ia", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "THD/I/Inst/Value/L1#", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 30, "ParentName": "THDCurrent_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ib", "Name": "THD_Ib", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "THD/I/Inst/Value/L2#", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 31, "ParentName": "THDCurrent_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ic", "Name": "THD_Ic", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "THD/I/Inst/Value/L3#", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 32, "ParentName": "THDCurrent_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ForwardActivePower", "Name": "ForwardActivePower", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "", "Unit": "Wh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TotalEnergy\"}", "Sort": 33, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ForwardReactivePower", "Name": "ForwardReactivePower", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "", "Unit": "varh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TotalEnergy\"}", "Sort": 34, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ReverseActivePower", "Name": "ReverseActivePower", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "", "Unit": "Wh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TotalEnergy\"}", "Sort": 35, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ReverseReactivePower", "Name": "ReverseReactivePower", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "", "Unit": "varh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"TotalEnergy\"}", "Sort": 36, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ForwardActivePower_Tariff1", "Name": "ForwardActivePower_Tariff1", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Energy/Wh/Import/OnPeak/Sum", "Unit": "Wh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 37, "ParentName": "ActiveEnergyImportTariff1", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ForwardReactivePower_Tariff1", "Name": "ForwardReactivePower_Tariff1", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Energy/varh/Import/OnPeak/Sum", "Unit": "varh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 38, "ParentName": "TotalReactiveEnergyImportTariff1", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ReverseActivePower_Tariff1", "Name": "ReverseActivePower_Tariff1", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Energy/Wh/Export/OnPeak/Sum", "Unit": "Wh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 39, "ParentName": "ActiveEnergyExportTariff1", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ReverseReactivePower_Tariff1", "Name": "ReverseReactivePower_Tariff1", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Energy/varh/Export/OnPeak/Sum", "Unit": "varh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 40, "ParentName": "TotalReactiveEnergyExportTariff1", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ForwardActivePower_Tariff2", "Name": "ForwardActivePower_Tariff2", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Energy/Wh/Import/OffPeak/Sum", "Unit": "Wh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 41, "ParentName": "ActiveEnergyImportTariff2", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ForwardReactivePower_Tariff2", "Name": "ForwardReactivePower_Tariff2", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Energy/varh/Import/OffPeak/Sum", "Unit": "varh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 42, "ParentName": "TotalReactiveEnergyImportTariff2", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ReverseActivePower_Tariff2", "Name": "ReverseActivePower_Tariff2", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Energy/Wh/Export/OffPeak/Sum", "Unit": "Wh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 43, "ParentName": "ActiveEnergyExportTariff2", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ReverseReactivePower_Tariff2", "Name": "ReverseReactivePower_Tariff2", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Energy/varh/Export/OffPeak/Sum", "Unit": "varh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 44, "ParentName": "TotalReactiveEnergyExportTariff2", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "DO_0.0", "Name": "DO_0.0", "GroupName": "DigitalInputOutput", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Output_Status/BinaryOutput0_step7", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 45, "ParentName": "OutputStatus", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "DI_0.0", "Name": "DI_0.0", "GroupName": "DigitalInputOutput", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Input_Status/BinaryInput0_step7", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 46, "ParentName": "InputStatus", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LimitMonitoring_0", "Name": "LimitMonitoring_0", "GroupName": "LimitMonitoring", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Limit_Monitoring/Limit_Monitoring_0_step7", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 47, "ParentName": "LimitMonitoring", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LimitMonitoring_1", "Name": "LimitMonitoring_1", "GroupName": "LimitMonitoring", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Limit_Monitoring/Limit_Monitoring_1_step7", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 48, "ParentName": "LimitMonitoring", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LimitMonitoring_2", "Name": "LimitMonitoring_2", "GroupName": "LimitMonitoring", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Limit_Monitoring/Limit_Monitoring_2_step7", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 49, "ParentName": "LimitMonitoring", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LimitMonitoring_3", "Name": "LimitMonitoring_3", "GroupName": "LimitMonitoring", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Limit_Monitoring/Limit_Monitoring_3_step7", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 50, "ParentName": "LimitMonitoring", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LimitMonitoring_4", "Name": "LimitMonitoring_4", "GroupName": "LimitMonitoring", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Limit_Monitoring/Limit_Monitoring_4_step7", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 51, "ParentName": "LimitMonitoring", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "LimitMonitoring_5", "Name": "LimitMonitoring_5", "GroupName": "LimitMonitoring", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Limit_Monitoring/Limit_Monitoring_5_step7", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 52, "ParentName": "LimitMonitoring", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "OperatingHours", "Name": "OperatingHours", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Counter/OperatingHours", "Unit": "h", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 53, "ParentName": "Counter", "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Actual<PERSON><PERSON><PERSON>", "Name": "Actual<PERSON><PERSON><PERSON>", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "CostManagement/actual tariff", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 54, "ParentName": "CostManagement", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Slot_1", "Name": "Slot_1", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Slot1_ModuleInfo_OrderNumber", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"SlotFunc\"}", "Sort": 55, "ParentName": "LocalDeviceStatus", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Slot_2", "Name": "Slot_2", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Slot2_ModuleInfo_OrderNumber", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "{\"FumcName\":\"SlotFunc\"}", "Sort": 56, "ParentName": "LocalDeviceStatus", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "MLFB", "Name": "MLFB", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "OrderNumber", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "ParentName": "Others", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "SerialNumber", "Name": "SerialNumber", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "SerialNumber", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "ParentName": "Others", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "UseVoltageTransformer", "Name": "UseVoltageTransformer", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "UseVoltageTransformer", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 59, "ParentName": "Others", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "PrimaryVoltage", "Name": "PrimaryVoltage", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "PrimaryVoltage", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 60, "ParentName": "Others", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "SecondaryVoltage", "Name": "SecondaryVoltage", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "SecondaryVoltage", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 61, "ParentName": "Others", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "PrimaryCurrent", "Name": "PrimaryCurrent", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "PrimaryCurrent", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 62, "ParentName": "Others", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "SecondaryCurrent", "Name": "SecondaryCurrent", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "SecondaryCurrent", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 63, "ParentName": "Others", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Alarm_Severity", "Name": "Alarm_Severity", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Alarm/Value/AlarmSeverity", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 64, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Status", "Name": "Alarm_Status", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Alarm/Value/AlarmStatus", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 65, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Path", "Name": "Alarm_Path", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Alarm/Value/AlarmPath", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 66, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Info", "Name": "Alarm_Info", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Alarm/Value/AlarmInfo", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 67, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Time", "Name": "Alarm_Time", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Alarm/Value/AlarmTime", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 68, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Msg_Severity", "Name": "Msg_Severity", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Alarm/Value/MsgSeverity", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 69, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Msg_Status", "Name": "Msg_Status", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Alarm/Value/MsgStatus", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 70, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Msg_Path", "Name": "Msg_Path", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Alarm/Value/MsgPath", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 71, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Msg_Info", "Name": "Msg_Info", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Alarm/Value/MsgInfo", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 72, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Msg_Time", "Name": "Msg_Time", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "PAC3200", "FilterIds": "", "UdcCode": "Alarm/Value/MsgTime", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 73, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}]