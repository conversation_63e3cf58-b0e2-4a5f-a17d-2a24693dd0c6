﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.ModbusDeviceWorker.DataParse
{
    public class StringRead : PanelModbusRead
    {
        public StringRead(bool isBigEndian, string? parseMode, float factor, float customFactor, float intercept)
            : base(isBigEndian, parseMode, factor, customFactor, intercept)
        {
        }

        public override string ReadData(ReadOnlySpan<byte> source)
        {
            throw new NotImplementedException();
        }
    }
}
