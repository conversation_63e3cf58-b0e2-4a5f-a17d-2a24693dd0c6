﻿using Newtonsoft.Json;
using Siemens.PanelManager.Model.Database.Topology;
using Siemens.PanelManager.Model.Topology;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.UploadExcel.Diagram.TemplateModel.NodeTemplate
{
    /// <summary>
    /// 单线图中的点数据点位元素
    /// </summary>
    internal class DataPointNode : NodeData
    {
        public override NodeType NodeType => NodeType.DataPoint;
        public DataPointNode()
        {
            Name = "Realtime Data Point";
            TypeCode = "Data Realtime Point";
            CloseStyle = "comment";
            Category = "dataRealtimePoint";
            SizeHight = 40;
            SizeWidth = 40;
        }

        private string _electricalType = string.Empty;
        /// <summary>
        /// 前端与数据的单位相关
        /// </summary>
        [JsonProperty("electricalType")]
        public string ElectricalType
        {
            get
            {
                return _electricalType;
            }
            set
            {
                switch (value)
                {
                    case "APhaseCurrent":
                    case "BPhaseCurrent":
                    case "CPhaseCurrent":
                        Unit = "A";
                        break;
                    case "Voltage":
                        Unit = "V";
                        break;
                    case "Frequency":
                        Unit = "Hz";
                        break;
                    case "reactivePower":
                        Unit = "var";
                        break;
                    case "Power":
                        Unit = "W";
                        break;
                    case "PowerFactor":
                        break;
                    default: break;
                }
            }
        }
        /// <summary>
        /// 前端与数据的单位相关
        /// </summary>
        [JsonProperty("electricalTypeName")]
        public string ElectricalName { get; set; } = string.Empty;
        [JsonProperty("value")]
        public string Value { get; set; } = "NaN";
        [JsonProperty("fontColor")]
        public string FontColor { get; set; } = string.Empty;
        [JsonProperty("isSelected")]
        public bool IsSelected { get; set; } = true;
        [JsonProperty("pointUnit")]
        public string? Unit { get; set; }
        [JsonIgnore]
        public int BindAssetId { get; set; }
        [JsonIgnore]
        public string BindPointName { get; set; }
        [JsonIgnore]
        public int Order { get; set; }

        public override void AddRules(List<TopologyRuleInfo> ruleInfos)
        {
            if (BindAssetId <= 0) return;
            if (string.IsNullOrEmpty(BindPointName)) return;
            if (!Key.HasValue) return;

            AssetId = BindAssetId;
            string func = string.Empty;

            switch (BindPointName)
            {
                case "P":
                case "Q":
                    func = JsonConvert.SerializeObject(new
                    {
                        FunctionName = "ExceptByK"
                    });
                    break;
                default:
                    break;
            }
            var ruleCode = Guid.NewGuid().ToString();
            ruleInfos.Add(new TopologyRuleInfo()
            {
                AssetId = BindAssetId,
                DataPoint = BindPointName,
                RuleCode = ruleCode,
                TargetIdentify = Key.Value.ToString(),
                TargetProperty = "value",
                FormatFunction = func,
            });

            RuleIdMap = new Dictionary<string, string>() { ["value"] = ruleCode };
        }
    }
}
