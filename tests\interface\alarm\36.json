{"info": {"_postman_id": "2f107d6b-fcd1-4735-bc90-983d48bf64f8", "name": "36使用管理员账号进入panel manager告警管理中的告警配置菜单，点击告警规则列表中的启用和禁用开关", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 14", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "获取全部告警规则 Copy 3", "event": [{"listen": "test", "script": {"exec": ["let ID1 = pm.response.json().items[0].id\r", "pm.environment.set(\"ID1\", ID1);\r", "console.log(ID1)"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/AlarmRule?page=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["api", "v1", "AlarmRule"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}]}}, "response": []}, {"name": "更改告警规则启用状态", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/AlarmRule/{{ID1}}/enable?isEnable=0", "host": ["{{baseUrl}}"], "path": ["api", "v1", "AlarmRule", "{{ID1}}", "enable"], "query": [{"key": "isEnable", "value": "0"}]}}, "response": []}, {"name": "获取单个告警规则 Copy 4", "event": [{"listen": "test", "script": {"exec": ["\r", "\r", "pm.test(\"告警规则为禁用状态\", function () {\r", "    var jsonData = pm.response.json().data;\r", "    pm.expect(jsonData[\"isEnable\"]).to.eql(false);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/AlarmRule/{{ID1}}", "host": ["{{baseUrl}}"], "path": ["api", "v1", "AlarmRule", "{{ID1}}"]}}, "response": []}, {"name": "更改告警规则启用状态 Copy", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/AlarmRule/{{ID1}}/enable?isEnable=1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "AlarmRule", "{{ID1}}", "enable"], "query": [{"key": "isEnable", "value": "1"}]}}, "response": []}, {"name": "获取单个告警规则 Copy 5", "event": [{"listen": "test", "script": {"exec": ["\r", "\r", "pm.test(\"告警规则为启用状态\", function () {\r", "    var jsonData = pm.response.json().data;\r", "    pm.expect(jsonData[\"isEnable\"]).to.eql(true);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/AlarmRule/{{ID1}}", "host": ["{{baseUrl}}"], "path": ["api", "v1", "AlarmRule", "{{ID1}}"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"code码为20000\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData[\"code\"]).to.eql(20000);", "});"]}}]}