﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.DataFlow;
using Siemens.PanelManager.Model.UDC;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TestController : ControllerBase
    {
        private const string AssetCurrentStatusCacheKey = "AssetStatus:Currently-{0}";
        public const string AssetSimpleInfoCacheKey = "Asset:SimpleInfo-{0}";
        private readonly IAssetDataProxyRef _proxyRef;
        private readonly SiemensCache _cache;
        private readonly IServiceProvider _provider;
        public TestController(IAssetDataProxyRef proxyRef, SiemensCache cache, IServiceProvider provider)
        {
            _proxyRef = proxyRef;
            _cache = cache;
            _provider = provider;
        }

        [HttpPost("InsetData")]
        public void InsetData(AssetInputData[] inputDatas)
        {
            //using (var client = _provider.GetRequiredService<ISqlSugarClient>())
            //{
            //    var assets = await client.Queryable<AssetInfo>().Select(a=>a.Id).FirstAsync();
            //}
            foreach (var data in inputDatas)
            {
                _proxyRef.InputData(data);
            }
        }

        [HttpPost("InsetUDCData")]
        public void InsetUDCData(DeviceDataPointsResult inputDatas)
        {
            //using (var client = _provider.GetRequiredService<ISqlSugarClient>())
            //{
            //    var assets = await client.Queryable<AssetInfo>().Select(a=>a.Id).FirstAsync();
            //}

            var refObj = _provider.GetRequiredService<IMessageReceiver>();
            refObj.InputMessage(inputDatas);
        }

        [HttpGet("GetCurrentlyStatus")]
        public Dictionary<string, string>? GetCurrentlyStatus(int assetId)
        {
            return _cache.Get<Dictionary<string, string>>(string.Format(AssetCurrentStatusCacheKey, assetId));
        }

        [HttpGet("GetSimpleInfo")]
        public string GetSimpleInfo(int assetId)
        {
            return JsonConvert.SerializeObject(_cache.Get<object>(string.Format(AssetSimpleInfoCacheKey, assetId)));
        }

        [HttpGet("gc")]
        public string TestGC()
        {
            GC.Collect();
            return "OK";
        }
    }
}
