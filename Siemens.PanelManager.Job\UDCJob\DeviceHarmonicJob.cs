﻿using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Microsoft.Extensions.Configuration;
using Quartz;
using Siemens.PanelManager.Common;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.Model.Config;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.Server.DataPoint;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using InfluxDB.Client;
using Org.BouncyCastle.Utilities.Collections;
using Siemens.PanelManager.Job.Energy;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.DataFlow;

namespace Siemens.PanelManager.Job.UDCJob
{
    [DisallowConcurrentExecution]
    public class DeviceHarmonicJob : JobBase
    {
        public override string Name => "DeviceHarmonicJob";
        private readonly ILogger<DeviceHarmonicJob> _logger;
        private readonly IServiceProvider _provider;
        private readonly IConfiguration _configuration;
        private InfluxDBConfig? InfluxdbConfig { get; set; }
        public DeviceHarmonicJob(ILogger<DeviceHarmonicJob> logger,
             IConfiguration configuration,
            IServiceProvider provider)
        {
            _logger = logger;
            _configuration = configuration;
            _provider = provider;
            InfluxdbConfig = _configuration.GetSection(InfluxDBConfig.Influxdb).Get<InfluxDBConfig>();

            if (InfluxdbConfig == null)
            {
                _logger.LogError("EnergySummarySyncJob init: Not found Influxdb config.");
            }
        }

        public override async Task Execute()
        {
            var refObj = _provider.GetRequiredService<IAssetDataProxyRef>();

            var time = DateTime.Now
                .AddMinutes(DateTime.Now.Minute % 5 - 5)
                .AddSeconds(-DateTime.Now.Second)
                .GetTimestampForSec();
            var timeNow = DateTime.Now
                .AddSeconds(-DateTime.Now.Second)
                .GetTimestampForSec();

            var server = _provider.GetRequiredService<DataPointServer>();

            using var sqlClient = _provider.GetRequiredService<ISqlSugarClient>();
            var typeCode = "DeviceModelForHarmonic".ToUpper();
            var staticConfigs = await sqlClient.Queryable<SystemStaticModel>()
                .Where(s => s.Type == typeCode)
                .ToArrayAsync();

            var codes = staticConfigs.Select(s => s.Code).ToArray();
            var assetInfoes = await sqlClient.Queryable<AssetInfo>()
                .Where(a => codes.Contains(a.AssetModel) && !string.IsNullOrEmpty(a.ObjectId))
                .ToArrayAsync();

            var histories = new List<DeviceHarmonicHistory>();
            var dashboardConfig = await sqlClient.Queryable<AssetDashboardConfig>().Where(d => d.ConfigName == "JOB_HARMONICS_VOLTAGE").FirstAsync();

            using var influxClient = new InfluxDBClient(InfluxdbConfig.Url, InfluxdbConfig.UserName, InfluxdbConfig.Password);
            var queryApi = influxClient.GetQueryApi();
            var fluxSql = dashboardConfig.Sql;
            fluxSql = fluxSql
                   .Replace("[[StartDate]]", time.ToString())
                   .Replace("[[EndDate]]", timeNow.ToString())
                   .Replace("[[DBName]]", InfluxdbConfig.Bucket)
                   .Replace("[[TableName]]", "archivedatarealtime");

            foreach (var asset in assetInfoes)
            {
                if (string.IsNullOrEmpty(asset.ObjectId)) continue;
                var dataPoints = await server.GetDataPointInfos(AssetLevel.Device, asset.AssetType ?? string.Empty, asset.AssetModel ?? string.Empty);
                var harmonicConfigs = dataPoints.Where(d => d.GroupName == "Harmonic").ToArray();
                var internalNames = harmonicConfigs.Select(s => s.UdcCode ?? string.Empty).ToArray();
                Dictionary<string, string> udcData = new Dictionary<string, string>();
                if ("Other".Equals(asset.AssetModel, StringComparison.OrdinalIgnoreCase) 
                    || (asset.AssetModel == "Modbus" && asset.AssetType == "Gateway"))
                {
                    continue;
                }
                else if (asset.AssetModel == "GeneralDevice")
                {
                    var curentFluxSql = fluxSql.Replace("[[AssetId]]", asset.Id.ToString());
                    var result = await queryApi.QueryAsync(curentFluxSql, InfluxdbConfig.OrgName ?? InfluxdbConfig.OrgId);
                    foreach (var table in result)
                    {
                        foreach (var item in table.Records)
                        {
                            var value = Convert.ToDecimal(item.GetValue());
                            var keycode = item.GetField();
                            udcData.Add(keycode, value.ToString());
                        }
                    }
                }
                else
                {
                    udcData = await server.GetDataByUDCApi(asset.ObjectId, internalNames);

                    #region 发送Asset Details
                    if (udcData.Count > 0)
                    {
                        var details = new Dictionary<string, string>();
                        foreach (var kv in udcData)
                        {
                            var config = harmonicConfigs.FirstOrDefault(c => c.UdcCode == kv.Key);
                            if (config != null)
                            {
                                details.TryAdd(config.Code, kv.Value);
                            }
                        }

                        refObj.AssetDetail(new AssetDetailsData
                        {
                            AssetId = asset.Id,
                            AssetLevel = asset.AssetLevel,
                            AssetModel = asset.AssetModel ?? string.Empty,
                            AssetType = asset.AssetType ?? string.Empty,
                            AssetName = asset.AssetName,
                            ChangeTime = DateTime.Now,
                            Details = details
                        });
                    }
                    #endregion
                }

                _logger.LogInformation(JsonConvert.SerializeObject(udcData));

                if (udcData != null && udcData.Keys.Count > 0)
                {
                    var data = new DeviceHarmonicHistory()
                    {
                        Ts = time,
                        AssetId = asset.Id,
                    };
                    var dataType = data.GetType();
                    decimal? baseValue = null;
                    foreach (var dataItem in udcData)
                    {
                        var dp = harmonicConfigs.FirstOrDefault(c => c.UdcCode == dataItem.Key);
                        if (dp == null) continue;
                        if (!decimal.TryParse(dataItem.Value, out var number)) continue;

                        SetDataByCode(data, dp.Code, number, dataType, ref baseValue);
                    }

                    histories.Add(data);
                }
            }

            if (histories.Count > 0)
            {
                await sqlClient.Insertable(histories).ExecuteCommandAsync();
            }
        }

        private void SetDataByCode(DeviceHarmonicHistory history,
            string code,
            decimal value,
            Type dataType,
            ref decimal? baseValue)
        {
            var m = Regex.Match(code, "^Harmonic_U([a|b|c]{0,1})_([\\d]{1,2})$");
            if (m.Success) 
            {
                if (!string.IsNullOrEmpty(m.Groups[1].Value))
                {
                    var fieldName = $"U{m.Groups[1].Value}{m.Groups[2].Value}";
                    var p = dataType.GetProperty(fieldName);
                    if (p != null) 
                    {
                        if ("1".Equals(m.Groups[2].Value))
                        {
                            baseValue = value;
                            p.SetValue(history, value, null);
                        }
                        else
                        {
                            if (baseValue != null)
                            {
                                p.SetValue(history, baseValue * (1m + value / 100m), null);
                            }
                        }
                    }
                }
            }
        }
    }
}
