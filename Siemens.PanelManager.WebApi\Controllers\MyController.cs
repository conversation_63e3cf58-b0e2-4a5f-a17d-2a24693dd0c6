﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Model.Database.Auth;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;
using System.Text;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class MyController : SiemensApiControllerBase
    {
        private ILogger<MyController> _log;
        private ISqlSugarClient _client;
        private IServiceProvider _provider;
        private AlarmExtendServer _alarmExtendServer => _provider.GetRequiredService<AlarmExtendServer>();
        public MyController(
            SqlSugarScope client,
            ILogger<MyController> log,
            SiemensCache cache,
            IServiceProvider provider)
            : base(provider, cache) 
        {
            _client = client;
            _log = log;
            _provider = provider;
        }

        /// <summary>
        /// 修改密码
        /// </summary>
        /// <remarks>用户登录接口</remarks>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPut("profile/updatePwd")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_My_UpdatePassword", Description = "Swagger_My_UpdatePassword_Desc")]
        public async Task<ResponseBase<string>> ChangePassword(ChangePasswordParam param)
        {
            if (param == null
                || string.IsNullOrEmpty(param.OldPassword)
                || string.IsNullOrEmpty(param.NewPassword))
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            if (!CheckPassword(param.NewPassword))
            {
                return new ResponseBase<string>()
                {
                    Code = 40301,
                    Message = MessageContext.GetErrorValue("My_InvaildPasswordFormat")
                };
            }

            if (string.Equals(param.OldPassword, param.NewPassword))
            {
                return new ResponseBase<string>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorMySamePassword
                };
            }

            var userSession = UserSession;
            if (userSession == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40100,
                    Message = MessageContext.TokenTimeout
                };
            }

            var user = await _client.Queryable<User>().Where(u => u.Id == userSession.UserId && !u.IsDeleted).FirstAsync();
            if (user == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.UserNotExists
                };
            }
            var oldCode = param.OldPassword.GetPasswordCode();
            if(user.PasswordHash != oldCode) 
            {
                return new ResponseBase<string>()
                {
                    Code = 40100,
                    Message = MessageContext.InvaildOldPassword
                };
            }
            var code = param.NewPassword.GetPasswordCode();
            user.PasswordHash = code;
            user.UpdatedBy = user.UserName;
            user.UpdatedTime= DateTime.Now;
            await _client.Updateable(user).ExecuteCommandAsync();
            await _alarmExtendServer.InsertOperationLog(UserName, "ChangeMyPassword", Model.Database.Alarm.AlarmSeverity.Low, _client);
            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = MessageContext.Success
            };
        }

        /// <summary>
        /// 修改个人信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpPut("profile/info")]
        [Authorize(policy: Permission.Default)]
        [SwaggerOperation(Summary = "Swagger_My_UpdateProfileInfo", Description = "Swagger_My_UpdateProfileInfo_Desc")]
        public async Task<ResponseBase<string>> UpdateMyInfo(UpateMyInfo param)
        {
            if (param == null
                || string.IsNullOrEmpty(param.PersonName))
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.InvaildPassword
                };
            }

            var userSession = UserSession;
            if (userSession == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40100,
                    Message = MessageContext.TokenTimeout
                };
            }

            var user = await _client.Queryable<User>().Where(u => u.Id == userSession.UserId && !u.IsDeleted).FirstAsync();
            await _alarmExtendServer.InsertOperationLog(UserName, "ChangeMyProfile", Model.Database.Alarm.AlarmSeverity.Low, _client);
            if (user == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.UserNotExists
                };
            }

            user.EmailAddress = param.Email;
            user.MobileNumber = param.Tel;
            user.UserName = param.PersonName;
            user.UpdatedBy = user.UserName;
            user.UpdatedTime = DateTime.Now;
            var message = new StringBuilder();
            if (!user.CheckUserInfo(message, this))
            {
                return new ResponseBase<string>()
                {
                    Code = 40301,
                    Message = message.ToString()
                };
            }

            await _client.Updateable(user).ExecuteCommandAsync();

            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = MessageContext.Success
            };
        }

        private bool CheckPassword(string? password)
        {
            if (string.IsNullOrEmpty(password)
                || password.Length <= 6
                || password.Length >= 20
                || !Regex.IsMatch(password, "[a-zA-Z]+")
                || !Regex.IsMatch(password, "[\\d]+")
                || !Regex.IsMatch(password, "[\\!|@|\\$|\\-|_|\\+|\\=|\\<|\\>|\\?|\\{|\\}|\\:|\\;|\\||\\&|\\#]+")
                || Regex.IsMatch(password, "[^(a-zA-Z|\\d|\\!|@|\\$|\\-|_|\\+|\\=|\\<|\\>|\\?|\\{|\\}|\\:|\\;|\\||\\&|\\#)]+")
                )
            {
                return false;
            }

            return true;
        }
    }
}
