﻿using Microsoft.AspNetCore.Mvc;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class AlarmQueryParam
    {
        [FromQuery(Name = "substationName")]
        public string[]? substationName { get; set; }
        [FromQuery(Name = "panelName")]
        public string[]? panelName { get; set; }
        [FromQuery(Name = "circuitName")]
        public string[]? circuitName { get; set; }
        [FromQuery(Name = "deviceName")]
        public string[]? deviceName { get; set; }
        [FromQuery(Name = "alarmRuleId")]
        public int[]? alarmRuleId { get; set; }
        [FromQuery(Name = "eventType")]
        public int[]? eventType { get; set; }
        [FromQuery(Name = "alarmLevel")]
        public int[]? alarmLevel { get; set; }
        [FromQuery(Name = "alarmStatus")]
        public int[]? alarmStatus { get; set; }
        [FromQuery(Name = "user")]
        public string? user { get; set; }
        [FromQuery(Name = "alarmStartTime")]
        public long? alarmStartTime { get; set; }
        [FromQuery(Name = "alarmEndTime")]
        public long? alarmEndTime { get; set; }
        [FromQuery(Name = "useFilter")]
        public bool useFilter { get; set; } = false;
        [FromQuery(Name = "page")]
        public int page { get; set; } = 1;
        [FromQuery(Name = "pageSize")]
        public int pageSize { get; set; } = 20;
    }
}
