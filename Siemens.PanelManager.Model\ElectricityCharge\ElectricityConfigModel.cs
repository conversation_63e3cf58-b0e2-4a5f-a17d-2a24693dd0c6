﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.ElectricityCharge
{
    public class ElectricityConfigModel
    {
        public int id { get; set; } = 0;
        public string rateName { get; set; }
        public int bsId { get; set; }
        public string rateDesc { get; set; }
        public decimal electrovalence { get; set; }
        public string electrovalenceType { get; set; }
        public List<timeRange> timeRange { get; set; } = new List<timeRange>();
        public string? seasonType { get; set; } = "";
        public List<seasonRange> seasonRange { get; set; } = new List<seasonRange>();
        public string? step { get; set; } = "";
        public stepTarif? stepTarif { get; set; } =new stepTarif{from="",to=""};
        public bool? edit { get; set; } = false;
    }
    public class timeRange
    {
        public string startTime { get; set; }
        public string endTime { get; set; }
    }
    public class seasonRange
    {
        public string startMonth { get; set; }
        public string endMonth { get; set; }
        public string? startDay { get; set; }
        public string? endDay { get; set; }
        public string? description { get; set; } = "";
    }
    public class stepTarif
    {
        public string from { get; set; }
        public string to { get; set; }
    }
}
