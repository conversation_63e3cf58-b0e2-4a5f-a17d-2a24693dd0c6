{"name": "panel-client", "version": "0.1.0", "private": true, "dependencies": {"@siemens/ix-icons": "^1.0.4", "@siemens/ix-react": "^1.4.2", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.4.0", "echarts": "^5.4.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router": "^6.10.0", "react-router-dom": "^6.10.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}