﻿using Newtonsoft.Json;
using Siemens.PanelManager.Model.Database.Auth;
using System.Diagnostics.CodeAnalysis;

namespace Siemens.PanelManager.WebApi.Models
{
    class UserSessionInfo
    {
        
        public UserSessionInfo([NotNull]User user, [NotNull] List<Role> roles, [NotNull] List<Page> pages) 
        {
            Id = Guid.NewGuid().ToString();
            UserId = user.Id;
            UserName = user.UserName ?? string.Empty;
            LoginName= user.LoginName ?? string.Empty;
            Language = user.Language;
            Roles = roles.Select(r => r.RoleName).ToArray();
            Pages = pages.Select(p=>p.PageCode).ToArray();
            SyncDeviceInfo = JsonConvert.SerializeObject(pages.Where(p => p.PageCode.Contains("sync")).ToArray().ToArray());
        }

        public UserSessionInfo(string id, int userId, string userName, string loginName, string language, string[] roles, string[] pages, string syncDeviceInfo)
        {
            Id = id;
            UserId = userId;
            UserName = userName;
            LoginName = loginName;
            Language = language;
            Roles = roles;
            Pages = pages;
            SyncDeviceInfo = syncDeviceInfo;
        }   

        public string Id { get; private set; }
        public int UserId { get; private set; }
        public string UserName { get; private set;} = string.Empty;
        public string LoginName { get; private set;} = string.Empty;
        public string Language { get; private set;}
        public string[] Roles { get; private set; }
        public string[] Pages { get; private set; }
        public string SyncDeviceInfo { get; private set; }
    }
}
