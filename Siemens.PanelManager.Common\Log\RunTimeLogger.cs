﻿using Dm;
using Newtonsoft.Json;
using Quartz.Util;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Numerics;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace Siemens.PanelManager.Common.Log
{
    public class RunTimeLogger : IDisposable
    {
        private ConcurrentDictionary<int, Stopwatch> _stopwatchList = new ConcurrentDictionary<int, Stopwatch>();
        private ConcurrentDictionary<int, bool> _isUsing = new ConcurrentDictionary<int, bool>();
        private ConcurrentDictionary<int, long> _currentTime = new ConcurrentDictionary<int, long>();

        private bool _hasInput = false;

        private int _fileWriterCount = 0;
        private StreamWriter? _streamWriter;
        private string _name;
        private int _flag = 1;
        private long _min1 = long.MaxValue;
        private long _max1 = 0;
        private long _sum1 = 0;
        private int _count1 = 0;
        // private ConcurrentQueue<string> _logInfo1 = new ConcurrentQueue<string>();
        private long _min2 = long.MaxValue;
        private long _max2 = 0;
        private long _sum2 = 0;
        private int _count2 = 0;
        // private ConcurrentQueue<string> _logInfo2 = new ConcurrentQueue<string>();
        public RunTimeLogger(string name)
        {
            _name = name;
            for (var i = 0; i < 5; i++)
            {
                _stopwatchList.TryAdd(i, new Stopwatch());
                _isUsing.TryAdd(i, false);
                _currentTime.TryAdd(i, 0L);
            }
            NewStreamWriter();
        }

        private void NewStreamWriter()
        {
            //if (_streamWriter != null)
            //{
            //    _streamWriter.Close();
            //}

            //var path = Path.Combine(Directory.GetCurrentDirectory(), "logs", $"{_name}-{DateTime.Now.ToString("MMdd-HHmmssf")}.log");
            //var fs = new FileStream(path, FileMode.OpenOrCreate);
            //_streamWriter = new StreamWriter(fs);
        }

        public RunTimeLogger(string name, int count)
        {
            _name = name;
            for (var i = 0; i < count; i++)
            {
                _stopwatchList.TryAdd(i, new Stopwatch());
                _isUsing.TryAdd(i, false);
            }
            var path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs", $"{name}-{DateTime.Now.ToString("MMdd-HHmmssf")}.log");
            var fs = new FileStream(path, FileMode.OpenOrCreate);
            _streamWriter = new StreamWriter(fs);
        }

        public void Dispose()
        {
            try
            {
                _streamWriter?.Close();
                _streamWriter?.Dispose();
            }
            catch { }
        }

        public int Start()
        {
            if (!_hasInput)
            {
                _hasInput = true;
                RunTimerLoggerService.Input(_name, this);
            }

            for (var i = 0; i < _isUsing.Count; i++)
            {
                if (!_isUsing[i])
                {
                    _isUsing[i] = true;
                    _currentTime[i] = 0L;
                    var sw = _stopwatchList[i];
                    sw.Restart();
                }
                return i;
            }
            return -1;
        }

        public void Mark(int key, string log)
        {
            long e = 0L;
            long c = 0L;
            if (_isUsing.ContainsKey(key))
            {
                _currentTime.TryGetValue(key, out c);
                _stopwatchList[key].Stop();
                e = _stopwatchList[key].ElapsedTicks;
                _isUsing[key] = false;
                _currentTime[key] = e;
            }
            else
            {
                return;
            }

            switch (_flag)
            {
                case 1:
                    {
                        // _logInfo1.Enqueue($"[{e - c}] {log}");
                        break;
                    }
                case 2:
                    {
                        // _logInfo2.Enqueue($"[{e - c}] {log}");
                        break;
                    }
                default:
                    break;
            }

            _stopwatchList[key].Start();
        }

        public void MarkByFilter(int key, string log, long timeFilter = 10000L)
        {
            long e = 0L;
            long c = 0L;
            if (_isUsing.ContainsKey(key))
            {
                _currentTime.TryGetValue(key, out c);
                _stopwatchList[key].Stop();
                e = _stopwatchList[key].ElapsedTicks;
                _isUsing[key] = false;
            }
            else
            {
                return;
            }

            if ((e - c) > timeFilter)
            {
                switch (_flag)
                {
                    case 1:
                        {
                            // _logInfo1.Enqueue($"[{e - c}] {log}");
                            break;
                        }
                    case 2:
                        {
                            // _logInfo2.Enqueue($"[{e - c}] {log}");
                            break;
                        }
                    default:
                        break;
                }
            }

            _stopwatchList[key].Start();
        }

        public void Stop(int key)
        {
            long e = 0L;
            if (_isUsing.ContainsKey(key))
            {
                _stopwatchList[key].Stop();
                e = _stopwatchList[key].ElapsedTicks;
                _isUsing[key] = false;
            }
            else
            {
                return;
            }

            switch (_flag)
            {
                case 1:
                    {
                        _count1++;
                        _sum1 += e;
                        if (_min1 > e)
                        {
                            _min1 = e;
                        }
                        if (_max1 < e)
                        {
                            _max1 = e;
                        }
                        break;
                    }
                case 2:
                    {
                        _count2++;
                        _sum2 += e;
                        if (_min2 > e)
                        {
                            _min2 = e;
                        }
                        if (_max2 < e)
                        {
                            _max2 = e;
                        }
                        break;
                    }
                default:
                    break;
            }
        }

        public void Stop(int key, string log)
        {
            long e = 0L;
            if (_isUsing.ContainsKey(key))
            {
                _stopwatchList[key].Stop();
                e = _stopwatchList[key].ElapsedTicks;
                _isUsing[key] = false;
            }
            else
            {
                return;
            }

            switch (_flag)
            {
                case 1:
                    {
                        _count1++;
                        _sum1 += e;
                        if (_min1 > e)
                        {
                            _min1 = e;
                        }
                        if (_max1 < e)
                        {
                            _max1 = e;
                        }
                        // _logInfo1.Enqueue($"[{e}] {log}");
                        break;
                    }
                case 2:
                    {
                        _count2++;
                        _sum2 += e;
                        if (_min2 > e)
                        {
                            _min2 = e;
                        }
                        if (_max2 < e)
                        {
                            _max2 = e;
                        }
                        // _logInfo2.Enqueue($"[{e}] {log}");
                        break;
                    }
                default:
                    break;
            }
        }

        public void StopByFilter(int key, object data, long timeFilter = 5000000)
        {
            long e = 0L;
            if (_isUsing.ContainsKey(key))
            {
                _stopwatchList[key].Stop();
                e = _stopwatchList[key].ElapsedTicks;
                _isUsing[key] = false;
            }
            else
            {
                return;
            }

            switch (_flag)
            {
                case 1:
                    {
                        _count1++;
                        _sum1 += e;
                        if (_min1 > e)
                        {
                            _min1 = e;
                        }
                        if (_max1 < e)
                        {
                            _max1 = e;
                        }
                        if (e > timeFilter)
                        {
                            // _logInfo1.Enqueue($"[{e}] {JsonConvert.SerializeObject(data)}");
                        }

                        break;
                    }
                case 2:
                    {
                        _count2++;
                        _sum2 += e;
                        if (_min2 > e)
                        {
                            _min2 = e;
                        }
                        if (_max2 < e)
                        {
                            _max2 = e;
                        }
                        if (e > timeFilter)
                        {
                            // _logInfo2.Enqueue($"[{e}] {JsonConvert.SerializeObject(data)}");
                        }

                        break;
                    }
                default:
                    break;
            }
        }

        public async Task WriteLogs()
        {
            switch (_flag)
            {
                case 1:
                    {
                        _flag = 2;
                        if (_count1 > 0)
                        {
                            await _streamWriter.WriteLineAsync($"========================={DateTime.Now.ToLongTimeString()}===============================");
                            await _streamWriter.WriteLineAsync($"min: {_min1}, max: {_max1}, agv: {_sum1 / _count1}, count: {_count1}");
                            _fileWriterCount++;
                            //while (_logInfo1.TryDequeue(out var log))
                            //{
                            //    await _streamWriter.WriteLineAsync(log);
                            //    _fileWriterCount++;
                            //}
                            await _streamWriter.FlushAsync();
                            _max1 = 0;
                            _min1 = long.MaxValue;
                            _sum1 = 0;
                            _count1 = 0;
                        }
                        break;
                    }
                case 2:
                    {
                        _flag = 1;
                        if (_count2 > 0)
                        {
                            await _streamWriter.WriteLineAsync($"========================={DateTime.Now.ToLongTimeString()}===============================");
                            await _streamWriter.WriteLineAsync($"min: {_min2}, max: {_max2}, agv: {_sum2 / _count2}, count: {_count2}");
                            _fileWriterCount++;
                            //while (_logInfo2.TryDequeue(out var log))
                            //{
                            //    await _streamWriter.WriteLineAsync(log);
                            //    _fileWriterCount++;
                            //}
                            await _streamWriter.FlushAsync();

                            _max2 = 0;
                            _min2 = long.MaxValue;
                            _sum2 = 0;
                            _count2 = 0;
                        }
                        break;
                    }
                default:
                    break;
            }

            if (_fileWriterCount > 500000)
            {
                _fileWriterCount = 0;
                NewStreamWriter();
            }
        }
    }

    static class RunTimerLoggerService
    {
        private static Timer? _timer;
        private static ConcurrentDictionary<string, RunTimeLogger> _exists = new ConcurrentDictionary<string, RunTimeLogger>();

        public static void Input(string name, RunTimeLogger logger)
        {
            if (_timer == null)
            {
                _timer = new Timer(Timer_Worker, null, 1000, 60 * 1000);
            }

            _exists.TryAdd(name, logger);
        }

        private static void Timer_Worker(object? state)
        {
            //var loggers = _exists.Values.ToList();
            //var tasks = new List<Task>();
            //foreach (var l in loggers)
            //{
            //    tasks.Add(l.WriteLogs());
            //}
            //Task.WaitAll(tasks.ToArray());
        }
    }
}
