﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Auth
{
    [SugarTable("auth_session")]
    public class SessionEntity
    {
        [SugarColumn(ColumnName = "code", IsPrimaryKey = true,Length = 50)]
        public string Code { get; set; } = string.Empty;
        public DateTime LastLoginTime { get; set; }

        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string LoginName { get; set; } = string.Empty;

        public string Language { get; set; } = string.Empty;
    }
}
