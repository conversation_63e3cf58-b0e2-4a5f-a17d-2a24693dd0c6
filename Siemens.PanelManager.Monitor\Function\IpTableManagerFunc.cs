﻿using Siemens.PanelManager.HubModel;
using Siemens.PanelManager.Monitor.Cmd;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.Monitor.Function
{
    static class IpTableManagerFunc
    {
        public static async Task<IpTablesRuleInfo[]> GetCurrentlyInputRules(ILogger logger)
        {
            var rules = new List<IpTablesRuleInfo>();
            var ruleStrList = await IpTablesCmd.GetInputFilter(logger);
            foreach (var ruleStr in ruleStrList)
            {
                var match = Regex.Match(ruleStr, "^((-(?<Cmd>A) (?<ChainName>[\\w]+))|(-(?<Cmd>P) (?<ChainName>[\\w]+) (?<Target>[\\w]+)))(([\\s]?-p (?<Protocol>[\\w]+))|([\\s]?-m (?<Match>[\\w]+))|([\\s]?--dport (?<Dport>[\\w]+))|([\\s]?--sport (?<Sport>[\\w]+))|([\\s]?-j (?<Target>[\\w]+)))*$");
                if (match.Success)
                {
                    var ruleInfo = new IpTablesRuleInfo();
                    var valueInfo = match.Groups.Values.FirstOrDefault(v => v.Name == "ChainName");
                    if (valueInfo != null && valueInfo.Value.Length > 0)
                    {
                        ruleInfo.ChainName = valueInfo.Value;
                    }
                    valueInfo = match.Groups.Values.FirstOrDefault(v => v.Name == "Cmd");
                    if (valueInfo != null && valueInfo.Value.Length > 0)
                    {
                        ruleInfo.Cmd = valueInfo.Value;
                    }
                    valueInfo = match.Groups.Values.FirstOrDefault(v => v.Name == "Target");
                    if (valueInfo != null && valueInfo.Value.Length > 0)
                    {
                        ruleInfo.Target = valueInfo.Value;
                    }
                    valueInfo = match.Groups.Values.FirstOrDefault(v => v.Name == "Protocol");
                    if (valueInfo != null && valueInfo.Value.Length > 0)
                    {
                        ruleInfo.Protocol = valueInfo.Value;
                    }
                    valueInfo = match.Groups.Values.FirstOrDefault(v => v.Name == "Match");
                    if (valueInfo != null && valueInfo.Value.Length > 0)
                    {
                        ruleInfo.Match = valueInfo.Value;
                    }
                    valueInfo = match.Groups.Values.FirstOrDefault(v => v.Name == "Dport");
                    if (valueInfo != null && valueInfo.Value.Length > 0)
                    {
                        ruleInfo.Dport = valueInfo.Value;
                    }
                    valueInfo = match.Groups.Values.FirstOrDefault(v => v.Name == "Sport");
                    if (valueInfo != null && valueInfo.Value.Length > 0)
                    {
                        ruleInfo.Sport = valueInfo.Value;
                    }
                    rules.Add(ruleInfo);
                }
            }
            return rules.ToArray();
        }

        public static async Task DeleteInputRule(ILogger logger, IpTablesRuleInfo ruleInfo)
        {
            string result = string.Empty;
            for (var i = 0; i < 5; i++)
            {
                result = await IpTablesCmd.DeleteRuleForInput(logger, ruleInfo);
                if(result.Length > 0) 
                {
                    return;
                }
            }
        }

        public static async Task AppendInputRule(ILogger logger, IpTablesRuleInfo ruleInfo)
        {
            if (ruleInfo == null ||
                (!string.IsNullOrEmpty(ruleInfo.ChainName) && !"INPUT".Equals(ruleInfo.ChainName)))
            {
                return;
            }

            await IpTablesCmd.AppendRuleIntoInput(logger, ruleInfo);
        }
    }
}
