﻿using Newtonsoft.Json;
using System.Text;

namespace Siemens.InfluxDB.Helper.FluxModel
{
    internal class Group
    {
        public Group(List<string> columns)
        {
            Columns = columns.ToArray();
        }
        public string[] Columns { get; set; }

        public void AppendFlux(StringBuilder flux)
        {
            flux.AppendLine($"|> group(columns: {JsonConvert.SerializeObject(Columns)})");
        }
    }
}
