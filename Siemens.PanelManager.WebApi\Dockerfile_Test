FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["Siemens.PanelManager.WebApi/Siemens.PanelManager.WebApi.csproj", "Siemens.PanelManager.WebApi/"]
RUN dotnet restore "./Siemens.PanelManager.WebApi/Siemens.PanelManager.WebApi.csproj"
COPY . .
WORKDIR "/src/Siemens.PanelManager.WebApi"
RUN dotnet build "./Siemens.PanelManager.WebApi.csproj" -c $BUILD_CONFIGURATION -o /app/build
RUN dotnet publish "./Siemens.PanelManager.WebApi.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM mcr.microsoft.com/dotnet/aspnet:8.0
LABEL maintainer="Panel Manager Team <<EMAIL>>"
WORKDIR /app
COPY --from=build /app/publish .
RUN echo `date +"%Y%m%d.%H%M"` >> /app/panel_build.txt
COPY ./config/dev/appsettings.json /app/appsettings.json
ENV TZ=Asia/Shanghai
RUN chmod 777 DatabaseInitializeFile
RUN chmod 777 Language
ENTRYPOINT ["dotnet", "Siemens.PanelManager.WebApi.dll","--urls","http://0.0.0.0:5000"]
