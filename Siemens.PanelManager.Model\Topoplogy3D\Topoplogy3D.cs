﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Siemens.PanelManager.Model.Topology3D
{
    public class Topology3D
    {
        [JsonProperty("id")]
        public int Id { get; set; }
        [JsonProperty("code")]
        public string? Code { get; set; }
        [JsonProperty("name")]
        public string? Name { get; set; }
        [JsonProperty("description")]
        public string? Description { get; set; }
        [JsonProperty("time")]
        public DateTime? Time { get; set; }
        [JsonProperty("owner")]
        public string? Owner { get; set; }
        [JsonProperty("nodes")]
        public JArray? NodeObject { get; set; }
        [JsonIgnore]
        public List<NodeBase3D>? Nodes { get; set; }
        [JsonProperty("panelList", NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, JToken>? PanelList { get; set; }
        [JsonProperty("flag", NullValueHandling = NullValueHandling.Ignore)]
        public string Flag { get; set; } = string.Empty;
    }
}