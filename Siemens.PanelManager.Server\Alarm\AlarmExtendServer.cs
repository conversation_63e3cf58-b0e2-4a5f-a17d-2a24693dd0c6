﻿using Akka.Actor;
using InfluxDB.Client.Api.Domain;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.Model.DataFlow;
using SqlSugar;
using System;

namespace Siemens.PanelManager.Server.Alarm
{
    public class AlarmExtendServer
    {
        private ILogger _logger;
        private IMessageContext _messageContext;
        private IServiceProvider _provider;
        public AlarmExtendServer(IMessageContextFactory factory, IConfiguration configuration, IServiceProvider provider, ILogger<AlarmExtendServer> logger) 
        {
            _logger = logger;
            _provider = provider;
            var section = configuration.GetSection("DefaultLanguage");
            var language = section.Value;
            if (!string.IsNullOrEmpty(language))
            {
                _messageContext = factory.GetMessageContext(language);
            }
            else
            {
                throw new AggregateException("缺失DefaultLanguage");
            }
        }

        public async Task InsertOperationLog(string user, string action, AlarmSeverity severity, ISqlSugarClient client, params string[]? paramList) 
        {
            string message;
            if (paramList != null && paramList.Length > 0)
            {
                message = string.Format($"{user}:{GetAction(action)}", paramList);
            }
            else
            {
                message = $"{user}:{GetAction(action)}";
            }

            await client.Insertable<AlarmLog>(new AlarmLog()
            {
                EventType = AlarmEventType.OperationLog,
                Severity = severity,
                Status = AlarmLogStatus.None,
                Message = message,
                CreatedBy = user,
                CreatedTime = DateTime.Now,
                UpdatedBy = user,
                UpdatedTime = DateTime.Now,
            }).ExecuteCommandAsync();

        }

        public async Task SignIn(string user, int userId, ISqlSugarClient client)
        {
            string message = $"{user}:{GetAction("SignIn")}";

            await client.Insertable<AlarmLog>(new AlarmLog()
            {
                EventType = AlarmEventType.OperationLog,
                Severity = AlarmSeverity.Middle,
                Status = AlarmLogStatus.None,
                AssetId = userId,
                Message = message,
                CreatedBy = user,
                CreatedTime = DateTime.Now,
                UpdatedBy = user,
                UpdatedTime = DateTime.Now,
            }).ExecuteCommandAsync();
        }

        private string GetAction(string action) 
        {
            return _messageContext.GetString($"OperationLogAction_{action}") ?? action;
        }

        public void FinishAlarmLog(long logId)
        {
            var refObj = _provider.GetRequiredService<IAlarmRef>();
            refObj.AlarmLogAction(new AlarmLogAction
            {
                AlarmLogId = logId,
                Action = "Finish"
            });
        }

        public void FinishAlarmLog(long[] logIds)
        {
            var refObj = _provider.GetRequiredService<IAlarmRef>();
            foreach (var logId in logIds)
            {
                refObj.AlarmLogAction(new AlarmLogAction
                {
                    AlarmLogId = logId,
                    Action = "Finish"
                });
            }
        }

        public async Task FinishAlarmByAlarmStartTime(DateTime startTime, ISqlSugarClient client, string user)
        {
            int page = 1;
            int count = 100;
            var refObj = _provider.GetRequiredService<IAlarmRef>();

            while (true)
            {
                var logHistories = new List<AlarmLogChangeLog>();

                var logs = await client.Queryable<AlarmLog>()
                    .Where(a => (a.Status == AlarmLogStatus.New || a.Status == AlarmLogStatus.InProcess) && a.CreatedTime < startTime)
                    .ToPageListAsync(page, count);

                foreach (var log in logs)
                {
                    log.UpdatedBy = user;
                    log.UpdatedTime = DateTime.Now;
                    log.Status = AlarmLogStatus.Finish;

                    logHistories.Add(new AlarmLogChangeLog
                    {
                        LogId = log.Id,
                        Remark = "The alarm processed by start time.",
                        Source = AlarmChangeSource.Person,
                        ChangedBy = user,
                        ChangeTime = DateTime.Now,
                    });
                }

                try
                {
                    await client.Ado.BeginTranAsync();
                    await client.Updateable(logs)
                        .UpdateColumns(c => new
                        {
                            c.UpdatedBy,
                            c.UpdatedTime,
                            c.Status,
                        })
                        .ExecuteCommandAsync();

                    await client.Insertable(logHistories)
                        .ExecuteCommandAsync();

                    await client.Ado.CommitTranAsync();
                }
                catch (Exception ex)
                {
                    await client.Ado.RollbackTranAsync();
                    _logger.LogError(ex, "Finish alarm log error");
                    break;
                }

                foreach (var log in logs)
                {
                    refObj.AlarmLogAction(new AlarmLogAction
                    {
                        AlarmLogId = log.Id,
                        Action = "Finish"
                    });
                }

                if (logs.Count < count)
                {
                    break;
                }
            }
        }
    }
}
