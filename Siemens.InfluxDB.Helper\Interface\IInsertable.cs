﻿using InfluxDB.Client.Api.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.InfluxDB.Helper.Interface
{
    public interface IInsertable<T> : IDisposable
        where T : IInfluxData
    {
        void Insert(T data, WritePrecision precision = WritePrecision.Ms);
        void Insert(List<T> data, WritePrecision precision = WritePrecision.Ms);
    }
}
