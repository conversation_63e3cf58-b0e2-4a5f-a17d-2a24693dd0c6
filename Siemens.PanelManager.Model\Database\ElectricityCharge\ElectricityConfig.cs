﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.ElectricityCharge
{
    [SugarTable("electricity_config")]
    public class ElectricityConfig : LogicDataBase
    {
       
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [UniquenessAttribute]
        [SugarColumn(ColumnName = "rate_name", IsNullable = false, Length = 256)]
        public string rateName { get; set; } = string.Empty;
        [UniquenessAttribute]
        [SugarColumn(ColumnName = "bsid", IsNullable = false)]
        public int bsid { get; set; }
        [SugarColumn(ColumnName = "rate_desc", IsNullable = false, Length = 256)]
        public string rateDesc { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "electrovalence", IsNullable = false)]
        public decimal electrovalence { get; set; }
        [SugarColumn(ColumnName = "electrovalence_type", IsNullable = false)]
        public string electrovalenceType { get; set; }
        [SugarColumn(ColumnName = "time_range", IsNullable = false, Length = 256)]
        public string timeRange { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "season_range", IsNullable = false, Length = 256)]
        public string seasonRange { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "season_type", IsNullable = false, Length = 256)]
        public string seasonType { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "step", IsNullable = false, Length = 256)]
        public string step { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "step_tarif", IsNullable = false, Length = 256)]
        public string stepTarif { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "effective_time_start", IsNullable = true)]
        public DateTime effectiveTimeStart { get; set; }
        [SugarColumn(ColumnName = "effective_time_end", IsNullable = true)]
        public DateTime effectiveTimeEnd { get; set; }
        [SugarColumn(ColumnName = "is_latest", IsNullable = true)]
        public bool isLatest { get; set; }
    }
    public enum ElectrovalenceType
    {
        receivePower,
        deliverPower
    }
}
