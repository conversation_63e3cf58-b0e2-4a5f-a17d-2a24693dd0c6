﻿using Newtonsoft.Json;
using SqlSugar;


namespace Siemens.PanelManager.Model.Database.Alarm
{
    [SugarTable("alarm_query_config")]
    public class AlarmQueryConfig : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsNullable = false, IsIdentity = true)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "query_type", IsNullable = false)]
        public AlarmQueryType QueryType { get; set; }
        [UniquenessAttribute]
        [SugarColumn(ColumnName = "param_code", IsNullable = true, Length = 128)]
        public string? ParamCode { get; set; }

        [SugarColumn(ColumnName = "query_value", IsNullable = true, ColumnDataType = "varchar(16384)")]
        public string? QueryValue { get; set; }
    }
    public enum AlarmQueryType : int
    {
        AlarmReminder = 0,
        PanelAlaram = 1,
        QueryParam = 2,
        QueryStartTime=3
    }
}
