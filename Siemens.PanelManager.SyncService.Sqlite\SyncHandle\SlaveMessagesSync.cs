﻿using InfluxDB.Client;
using InfluxDB.Client.Api.Domain;
using InfluxDB.Client.Writes;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.SyncService.UdcModels;
using SqlSugar;

namespace Siemens.PanelManager.SyncService.Sqlite
{
    public class SlaveMessagesSync : SyncEntity, ISyncMessageHandle
    {
        private readonly ILogger<SlaveMessagesSync> _logger;

        public SlaveMessagesSync(ILogger<SlaveMessagesSync> logger,
            IDbContextFactory<UdcContext> contextFactory,
            IConfiguration configuration,
            ISqlSugarClient client,
            IAssetDataProxyRef assetDataProxyRef) : base(contextFactory, configuration, client, assetDataProxyRef)
        {
            _logger = logger;

            ConfigName = nameof(SlaveMessages);
        }

        public async Task SyncWorkAsync()
        {
            try
            {
                using var udcContext = await _contextFactory.CreateDbContextAsync();
                 using var client = new InfluxDBClient(InfluxdbUrl, InfluxdbUserName, InfluxdbPassword);

                var influxdbPing = await client.PingAsync();
                if (!influxdbPing)
                {
                    _logger.LogError($"InfluxDB can't connect.");
                    return;
                }

                long lastTimestamp = GetLastTimestamp(0);
                if (lastTimestamp <= 0)
                {
                    lastTimestamp = udcContext.SlaveMessages.AsNoTracking()
                        .Select(a => a.Timestamp)
                        .DefaultIfEmpty()
                        .Min();
                }

                var data = await (from a in udcContext.SlaveMessages.AsNoTracking()
                                  join l in (
                                      from r in udcContext.SlaveMessages.AsNoTracking()
                                      where r.Timestamp >= lastTimestamp
                                      orderby r.Timestamp
                                      select new
                                      {
                                          r.Oid,
                                          r.DbSlaveObjectId,
                                          r.Timestamp,
                                      }).Take(_perQueryCount)
                                     on new { a.Oid, a.DbSlaveObjectId, a.Timestamp } equals new { l.Oid, l.DbSlaveObjectId, l.Timestamp }
                                  orderby a.Timestamp
                                  select a).ToListAsync();

                if (data.Any())
                {
                    lastTimestamp = data.LastOrDefault()?.Timestamp ?? 0;
                    SaveLastTimestamp(lastTimestamp);
                }

                using var writeApi = client.GetWriteApi();

                var typeSlaveMessages = typeof(SlaveMessages);
                var typeSlaveMessagesProperties = typeSlaveMessages.GetProperties();
                var entityType = udcContext.Model.FindEntityType(typeSlaveMessages);

                PointData point;
                foreach (var archiveData in data)
                {
                    point = PointData.Measurement(nameof(SlaveMessages).ToLower());
                    foreach (var item in typeSlaveMessagesProperties)
                    {
                        if (item.Name == nameof(SlaveMessages.DbSlaveObject))
                        {
                            continue;
                        }

                        var columnName = entityType.FindProperty(item.Name).GetColumnName();

                        if (item.Name == nameof(archiveData.Oid) || item.Name == nameof(archiveData.DbSlaveObjectId))
                        {
                            point = point.Tag(columnName.ToLower(), item.GetValue(archiveData)?.ToString());
                        }
                        else if (item.Name == nameof(archiveData.Timestamp))
                        {
                            point = point.Timestamp(Convert.ToInt64(item.GetValue(archiveData)), WritePrecision.S);
                        }
                        else
                        {
                            point = point.Field(columnName?.ToLower(), Convert.ChangeType(item.GetValue(archiveData), item.PropertyType));
                        }
                    }

                    writeApi.WritePoint(point, InfluxdbBucket, InfluxdbOrgName);
                }

                _logger.LogDebug($"{nameof(SlaveMessages)} Last Timestamp: {lastTimestamp}; Data Count: {data.Count}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.ToString());
                throw;
            }
        }
    }
}
