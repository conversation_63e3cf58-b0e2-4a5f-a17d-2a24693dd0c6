﻿using Siemens.InfluxDB.Helper.Enum;
using Siemens.InfluxDB.Helper.General;
using System.Linq.Expressions;

namespace Siemens.InfluxDB.Helper.ExpressionFunction
{
    internal class ExpreesionForSelect
    {
        public string[] AnalyseExpreesionForColumns(Expression expression)
        {
            return AnalyseForColumns(GetMemberInitExpression(expression));
        }

        public SelectAnalyseResult AnalyseExpreesionForSelect(Expression expression)
        {
            return AnalyseForSelect(GetMemberInitExpression(expression));
        }

        private MemberInitExpression GetMemberInitExpression(Expression expression)
        {
            switch (expression.NodeType)
            {
                case ExpressionType.Lambda:
                    var lambda = expression as LambdaExpression;
                    if (lambda != null)
                    {
                        return GetMemberInitExpression(lambda.Body);
                    }
                    break;
                case ExpressionType.MemberInit:
                    var member = expression as MemberInitExpression;
                    if (member != null)
                    {
                        return member;
                    }
                    break;
                default: break;
            }
            throw new InfluxDBHelperException("表达式错误，无法获取MemberInitExpression");
        }

        private string[] AnalyseForColumns(MemberInitExpression expression) 
        {
            var columns = new List<string>();
            foreach (var b in expression.Bindings)
            {
                if (b is MemberAssignment assignment
                    && assignment.Expression is MemberExpression member
                    && member.Expression?.NodeType == ExpressionType.Parameter)
                {
                    columns.Add(member.Member.Name);
                }
                else 
                {
                    throw new NotSupportedException("暂不支持此种方式的参数");
                }
            }
            return columns.ToArray();
        }

        private SelectAnalyseResult AnalyseForSelect(MemberInitExpression expression)
        {
            var funcList = new List<GroupFunctionEnum>();
            var columns = new List<SelectColumnInfo>();
            var supportFunType = typeof(InfluxDBGroupFun);
            foreach (var b in expression.Bindings)
            {
                var column = new SelectColumnInfo();
                if (b is MemberAssignment assignment)
                {
                    column.ColumnName = b.Member.Name;
                    switch (assignment.Expression.NodeType)
                    {
                        case ExpressionType.Call:
                            {
                                var call = assignment.Expression as MethodCallExpression;
                                if (call != null)
                                {
                                    var argument = call.Arguments.FirstOrDefault();
                                    if (argument == null)
                                    {
                                        NotSupport();
                                        continue;
                                    }
                                    if (call.Method.ReflectedType == supportFunType)
                                    {
                                        if (System.Enum.TryParse<GroupFunctionEnum>(call.Method.Name, out var funcType))
                                        {
                                            column.Function = funcType;
                                        }

                                        if (!funcList.Contains(column.Function))
                                        {
                                            funcList.Add(column.Function);
                                        }
                                    }
                                    else
                                    {
                                        NotSupport();
                                    }
                                }
                                else
                                {
                                    NotSupport();
                                }
                            }
                            break;
                        case ExpressionType.MemberAccess:
                            {
                                var memberAccess = assignment.Expression as MemberExpression;
                                if (memberAccess != null && memberAccess.Expression?.NodeType == ExpressionType.Parameter)
                                {
                                    column.ColumnName = memberAccess.Member.Name;
                                }
                                else
                                {
                                    NotSupport();
                                }
                            }
                            break;
                        default:
                            NotSupport();
                            break;
                    }
                    columns.Add(column);
                }
                else
                {
                    NotSupport();
                }
            }

            return new SelectAnalyseResult()
            {
                ColumnInfos = columns,
                GroupFunctions = funcList,
            };
        }

        private void NotSupport()
        {
            throw new NotSupportedException("Select使用的表达式不支持");
        }
    }
}
