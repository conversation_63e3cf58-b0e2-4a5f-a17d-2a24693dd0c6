﻿using Siemens.PanelManager.Model.Database.Asset;

namespace Siemens.PanelManager.Server.Asset
{
    public class BreakerHealthService
    {
        public void UpdateBreakerHealth(string assetModel, DeviceDetails details)
        {
            switch (assetModel)
            {
                case "3WA":
                    {
                        if (!details.ElectricalSwitchCycles.HasValue || !details.MechanicalSwitchCycles.HasValue)
                        {
                            details.ContactWearRate = null;
                            details.HealthLevel = null;
                            details.RemainingLife = null;
                            return;
                        }

                        var electricalSampleCycles = details.ElectricalSwitchSampleCycles ?? 1000;
                        var electricalCycles = details.ElectricalSwitchCycles ?? electricalSampleCycles;
                        var mechanicalSampleCycles = details.MechanicalSwitchSampleCycles ?? 1000;
                        var mechanicalCycles = details.MechanicalSwitchCycles ?? mechanicalSampleCycles;

                        var contactWearRate = 1m - ((1m - (decimal)electricalCycles / electricalSampleCycles) * (1m - (decimal)mechanicalCycles / mechanicalSampleCycles));
                        var healthSounce = (1m - contactWearRate) * 100m;
                        details.RemainingLife = healthSounce.ToString("#.##");
                        details.ContactWearRate = ((double)contactWearRate * 100d);
                        if (details.ContactWearRate > 100d)
                        {
                            details.ContactWearRate = 100d;
                        }
                        if (healthSounce > 50m)
                        {
                            details.HealthLevel = "normal";
                        }
                        else if (healthSounce <= 50m && healthSounce >= 20m)
                        {
                            details.HealthLevel = "attention";
                        }
                        else if (healthSounce < 20m && healthSounce > 0m)
                        {
                            details.HealthLevel = "maintain";
                        }
                        else
                        {
                            details.HealthLevel = "rushRepair";
                        }
                    }
                    break;
                case "3VA":
                    {
                        if (!string.IsNullOrEmpty(details.RemainingLife) && int.TryParse(details.RemainingLife, out int days))
                        {
                            if (days > (3 * 365))
                            {
                                details.HealthLevel = "normal";
                            }
                            else if (days > (2 * 365))
                            {
                                details.HealthLevel = "attention";
                            }
                            else if (days > (1 * 365))
                            {
                                details.HealthLevel = "maintain";
                            }
                            else
                            {
                                details.HealthLevel = "rushRepair";
                            }
                        }
                    }
                    break;
                case "3WL":
                    {
                        if (!details.ElectricalSwitchCycles.HasValue || !details.MechanicalSwitchCycles.HasValue)
                        {
                            details.ContactWearRate = null;
                            details.HealthLevel = null;
                            details.RemainingLife = null;
                            return;
                        }

                        var electricalSampleCycles = details.ElectricalSwitchSampleCycles ?? 1000;
                        var electricalCycles = details.ElectricalSwitchCycles ?? electricalSampleCycles;
                        var mechanicalSampleCycles = details.MechanicalSwitchSampleCycles ?? 1000;
                        var mechanicalCycles = details.MechanicalSwitchCycles ?? mechanicalSampleCycles;

                        var icw = (double)(details.Icw ?? 100m);
                        var i2tList = new double[] { (double)(details.SumI2t_A ?? 0m), (double)(details.SumI2t_B ?? 0m), (double)(details.SumI2t_C ?? 0m), (double)(details.SumI2t_N ?? 0m) };
                        var maxI2t = i2tList.Max();

                        var contactWearRate = 1d - (1d - (double)electricalCycles / electricalSampleCycles) * (1d - (double)mechanicalCycles / mechanicalSampleCycles) * (1d - maxI2t / (1.5d * Math.Pow((icw * 1000d), 2)));
                        var healthSounce = (1d - electricalCycles / electricalSampleCycles) * (1d - mechanicalCycles / mechanicalSampleCycles) * 100d;
                        details.RemainingLife = healthSounce.ToString("#.##");
                        details.ContactWearRate = contactWearRate * 100d;
                        if (details.ContactWearRate > 100d)
                        {
                            details.ContactWearRate = 100d;
                        }
                        if (healthSounce > 50d)
                        {
                            details.HealthLevel = "normal";
                        }
                        else if (healthSounce <= 50d && healthSounce >= 20d)
                        {
                            details.HealthLevel = "attention";
                        }
                        else if (healthSounce < 20d && healthSounce > 0d)
                        {
                            details.HealthLevel = "maintain";
                        }
                        else
                        {
                            details.HealthLevel = "rushRepair";
                        }
                    }
                    break;
                default: break;
            }
        }
    }
}
