﻿using Microsoft.AspNetCore.SignalR;
using System.Collections.Concurrent;

namespace Siemens.PanelManager.Monitor.StaticData
{
    static class ConnectManager
    {
        static ConnectManager()
        {
            LoopQueue<HubCallerContext> first = new LoopQueue<HubCallerContext>();
            LoopQueue<HubCallerContext> last = first;
            for (var i = 0; i < 3; i++)
            {
                var loopQueue = new LoopQueue<HubCallerContext>();
                last.Next = loopQueue;
                loopQueue.Last = last;
                last = loopQueue;
            }
            first.Last = last;
            last.Next = first;
            UnknowConnect = first;
        }

        public static LoopQueue<HubCallerContext> UnknowConnect { get; private set; }
        public static ConcurrentDictionary<string, HubCallerContext> ConnectedList { get; private set; } = new ConcurrentDictionary<string, HubCallerContext>();

        public static HubCallerContext[] GetCurrentlyToNext()
        {
            var loopQueue = UnknowConnect;
            if (loopQueue.Next != null)
            {
                UnknowConnect = loopQueue.Next;
            }
            if (loopQueue.Last != null)
            {
                var connects = new List<HubCallerContext>();
                while (loopQueue.Last.Queue.TryDequeue(out HubCallerContext? context))
                {
                    if (context != null)
                    {
                        connects.Add(context);
                    }
                }
                return connects.ToArray();
            }
            return new HubCallerContext[0];
        }
    }

    internal class LoopQueue<T>
    {
        public LoopQueue() 
        {
            Queue = new ConcurrentQueue<T>();
        }
        public ConcurrentQueue<T> Queue { get; private set; }
        public LoopQueue<T>? Last { get; set; }
        public LoopQueue<T>? Next { get; set; }
    }
}
