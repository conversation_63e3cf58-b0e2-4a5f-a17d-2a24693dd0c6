﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Model.Alarm;
using Siemens.PanelManager.Model.Common;
using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.Model.Database.Asset;
using SqlSugar;

namespace Siemens.PanelManager.Server.Alarm
{
    /// <summary>
    /// 添加第三方设备的日志服务
    /// </summary>
    public class AlarmLogServer
    {
        private readonly SqlSugarScope _db;

        private readonly ILogger _log;

        private readonly SiemensCache _cache;

        /// <summary>
        /// 构造函数初始化
        /// </summary>
        /// <param name="provider"></param>
        /// <param name="log"></param>
        /// <param name="cache"></param>
        public AlarmLogServer(IServiceProvider provider, ILogger<AlarmLogServer> log, SiemensCache cache)
        {
            _db = provider.GetService<SqlSugarScope>()!;
            _log = log;
            _cache = cache;
        }

        /// <summary>
        ///  资产id
        /// </summary>
        /// <param name="assetId"></param>
        /// <returns></returns>
        public async Task<List<BitPointDto>> GetBitConfigs(int? assetId)
        {
            var bitConfigs = new List<BitPointDto>();

            try
            {
                bitConfigs = await _db.Queryable<BitConfig>()
                    .LeftJoin<UniversalDeviceConfig>((t1, t2) => t1.UniversalDeviceConfigId == t2.Id)
                    .Where((t1, t2) => t2.AssetId == assetId && t1.EventType != -1)
                    .Select((t1, t2) => new BitPointDto
                    {
                        AssetId = t2.AssetId,
                        PropertyEnName = t2.PropertyEnName,
                        BitNumber = t1.BitNumber,
                        BitName = t1.BitName,
                        EventType = t1.EventType,
                        AlarmLevel = t1.AlarmLevel
                    })
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _log.LogError("AlarmLogServer_GetBitConfigs:" + ex.Message);
            }

            return bitConfigs;
        }

        /// <summary>
        /// 定时设置资产的二级制位信息缓存
        /// </summary>
        public void SetCacheBitConfigs()
        {
            try
            {
                var assetIds = _db.Queryable<UniversalDeviceConfig>().Distinct().Select(p => new { p.AssetId }).ToList();

                if (assetIds != null && assetIds.Any())
                {
                    foreach (var item in assetIds)
                    {
                        var bitConfigs = _db.Queryable<BitConfig>()
                        .LeftJoin<UniversalDeviceConfig>((t1, t2) => t1.UniversalDeviceConfigId == t2.Id)
                        .Where((t1, t2) => t2.AssetId == item.AssetId)
                        .Select((t1, t2) => new BitPointDto
                        {
                            BitCode = t1.BitCode,
                            AssetId = t2.AssetId,
                            PropertyEnName = t2.PropertyEnName,
                            BitNumber = t1.BitNumber,
                            BitName = t1.BitName,
                            EventType = t1.EventType,
                            AlarmLevel = t1.AlarmLevel

                        }).ToList();

                        _cache.Set($"AlarmLogServer_GetCacheBitConfigs_{item.AssetId}", bitConfigs);
                    }
                }
            }
            catch (Exception ex)
            {
                _log.LogError("AlarmLogServer_SetCacheBitConfigs:" + ex.Message);
            }
        }

        /// <summary>
        ///  资产id
        /// </summary>
        /// <param name="assetId"></param>
        /// <returns></returns>
        public List<BitPointDto> GetCacheBitConfigs(int? assetId)
        {
            var bitConfigs = new List<BitPointDto>();

            try
            {
                bitConfigs = _cache.Get<List<BitPointDto>>($"AlarmLogServer_GetCacheBitConfigs_{assetId}");
                if (bitConfigs == null)
                {
                    var r = new Random();
                    bitConfigs = _db.Queryable<BitConfig>()
                        .InnerJoin<UniversalDeviceConfig>((b,u)=>b.UniversalDeviceConfigId == u.Id)
                        .Where((b,u)=>u.AssetId == assetId)
                        .Select((b,u) => new BitPointDto
                        {
                            BitCode = b.BitCode,
                            AssetId = u.AssetId,
                            PropertyEnName = u.PropertyEnName,
                            BitNumber = b.BitNumber,
                            BitName = b.BitName,
                            EventType = b.EventType,
                            AlarmLevel = b.AlarmLevel
                        })
                        .ToList();

                    _cache.Set<List<BitPointDto>>($"AlarmLogServer_GetCacheBitConfigs_{assetId}", bitConfigs, TimeSpan.FromSeconds(r.Next(200,300)));
                }
            }
            catch (Exception ex)
            {
                _log.LogError("AlarmLogServer_GetCacheBitConfigs:" + ex.Message);
            }

            return bitConfigs;
        }

        /// <summary>
        /// 清空指定的缓存
        /// </summary>
        /// <param name=""></param>
        /// <returns></returns>
        public void ClearCache(List<int> assetIds)
        {
            if (assetIds != null && assetIds.Any())
            {
                foreach (var item in assetIds)
                {
                    _cache.Clear($"AlarmLogServer_GetCacheBitConfigs_{item}");
                }
            }
        }

        /// <summary>
        /// 根据资产名称获取告警消息
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public AlarmInfoDto GetAlarmInfo(AlarmInfoParam input)
        {
            AlarmInfoDto? alarmInfo = null;

            try
            {
                return _cache.GetOrCreate<AlarmInfoDto>($"AlarmLogServer_GetAlarmInfo_{input.AssetId}", () =>
                {

                    DateTime startTime = Convert.ToDateTime(DateTime.Now.ToString("yyyyy-MM-dd") + " 00:00:00");

                    var alarmLog = _db.Queryable<AlarmLog>().Where(p => (p.EventType == AlarmEventType.Alarm
                                                                    || p.EventType == AlarmEventType.UdcAlarm
                                                                    || p.EventType == AlarmEventType.BreakerTrip
                                                                    || p.EventType == AlarmEventType.DeviceLog)
                                                                    && (p.Status == AlarmLogStatus.New
                                                                    || p.Status == AlarmLogStatus.InProcess)
                                                                    && p.CreatedTime > startTime);

                    if (!string.IsNullOrWhiteSpace(input.SubStationName))
                    {
                        alarmLog = alarmLog.Where(p => p.SubstationName == input.SubStationName);
                    }

                    if (!string.IsNullOrWhiteSpace(input.PanelName))
                    {
                        alarmLog = alarmLog.Where(p => p.PanelName == input.PanelName);
                    }

                    if (!string.IsNullOrWhiteSpace(input.CircuitName))
                    {
                        alarmLog = alarmLog.Where(p => p.CircuitName == input.CircuitName);
                    }

                    if (!string.IsNullOrWhiteSpace(input.DeviceName))
                    {
                        alarmLog = alarmLog.Where(p => p.DeviceName == input.DeviceName);
                    }

                    var alarmByLog = alarmLog.OrderByDescending(p => p.CreatedTime).First();

                    if (alarmByLog != null)
                    {
                        alarmInfo = new AlarmInfoDto()
                        {
                            AlarmSeverity = (int)alarmByLog.Severity,
                            AlarmStatus = (int)alarmByLog.Status,
                            AlarmInfo = alarmByLog.Message,
                            AlarmPath = alarmByLog.SubstationName + "/" + alarmByLog.PanelName + "/" + alarmByLog.CircuitName + "/" + alarmByLog.DeviceName,
                            AlarmTime = alarmByLog.CreatedTime.ToString("yyyy-MM-dd HH:mm:ss")
                        };
                    }

                    return alarmInfo;

                }, TimeSpan.FromMinutes(GetRandom(1, 3)));
            }
            catch (Exception ex)
            {
                alarmInfo = null;
                _log.LogError("AlarmLogServer_GetAlarmInfo:" + ex.Message);
            }

            return alarmInfo;
        }

        /// <summary>
        /// 获取告警得数量(配电房，配电柜用)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public int GetAlarmNum(AlarmInfoParam input)
        {
            int num = 0;

            try
            {
                return _cache.GetOrCreate($"AlarmLogServer_GetAlarmNum_{input.AssetId}",
                    () =>
                    {
                        num = _db.Queryable<AlarmLog>()
                        .Where(p => (p.Status == AlarmLogStatus.New || p.Status == AlarmLogStatus.InProcess)
                        && !(p.EventType == AlarmEventType.Alarm || p.EventType == AlarmEventType.DeviceLog || p.EventType == AlarmEventType.UdcAlarm))
                        .WhereIF(!string.IsNullOrWhiteSpace(input.SubStationName), p => p.SubstationName == input.SubStationName)
                        .WhereIF(!string.IsNullOrWhiteSpace(input.PanelName), p => p.PanelName == input.PanelName)
                        .WhereIF(!string.IsNullOrWhiteSpace(input.CircuitName), p => p.CircuitName == input.CircuitName)
                        .WhereIF(!string.IsNullOrWhiteSpace(input.DeviceName), p => p.DeviceName == input.DeviceName)
                        .GroupBy(p => p.Severity)
                        .Count();

                        return num;

                    }, TimeSpan.FromMinutes(GetRandom(1,3)));
            }
            catch (Exception ex)
            {
                _log.LogError("AlarmLogServer_GetAlarmNum:" + ex.Message);
            }

            return num;
        }

        /// <summary>
        /// 获取随机值
        /// </summary>
        /// <returns></returns>
        private int GetRandom(int start, int end)
        {
            Random r = new Random();
            int number = r.Next(start, end);
            return number;
        }

        public AlarmCountModel? GetAlarmCount(AssetLevel level, string assetName)
        {
            var cacheData = _cache.GetHashData<AlarmCountModel>($"AlarmCount-{level}", new string[] { assetName });
            if (cacheData == null || cacheData.Count == 0 
                || !cacheData.TryGetValue(assetName, out var model) 
                || model == null)
            {
                return null;
            }

            return model;
        }

        public void SetAlarmCount(AssetLevel level, string assetName, AlarmCountModel model)
        {
            _cache.SetHashData($"AlarmCount-{level}", assetName, model);
        }
    }
}
