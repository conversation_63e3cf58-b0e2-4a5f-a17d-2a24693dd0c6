﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Siemens.PanelManager.Model.Database.Asset;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class EnergyQueryParamBase
    {
        [FromQuery(Name = "measurementType")]
        public string? MeasurementType { get; set; }

        [FromQuery(Name = "loop")]
        public string? Loop { get; set; }

        [FromQuery(Name = "device")]
        public string? Device { get; set; }

        [BindNever]
        public string MeasurementFinal
        {
            get
            {
                try
                {
                    if (string.IsNullOrWhiteSpace(MeasurementType) || MeasurementType.ToUpper() == "ALL")
                    {
                        return string.Empty;
                    }

                    if (int.TryParse(MeasurementType, out var tempValue))
                    {
                        return ((MeasurementType)tempValue).ToString();
                    }

                    return string.Empty;
                }
                catch
                {
                    return string.Empty;
                }
            }
        }

        [BindNever]
        public int CircuitId
        {
            get
            {
                try
                {
                    if (string.IsNullOrWhiteSpace(Loop) || Loop.ToUpper() == "ALL")
                    {
                        return 0;
                    }

                    if (int.TryParse(Loop, out var tempValue))
                    {
                        return tempValue;
                    }

                    return 0;
                }
                catch
                {
                    return 0;
                }
            }
        }

        [BindNever]
        public int AssetId
        {
            get
            {
                try
                {
                    if (string.IsNullOrWhiteSpace(Device) || Device.ToUpper() == "ALL")
                    {
                        return 0;
                    }

                    if (int.TryParse(Device, out var tempValue))
                    {
                        return tempValue;
                    }

                    return 0;
                }
                catch
                {
                    return 0;
                }
            }
        }
    }
}
