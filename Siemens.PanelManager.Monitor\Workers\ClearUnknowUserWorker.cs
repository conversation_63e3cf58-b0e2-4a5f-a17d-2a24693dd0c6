﻿using Microsoft.AspNetCore.SignalR;
using Siemens.PanelManager.Monitor.Hubs;
using Siemens.PanelManager.Monitor.StaticData;
using System;

namespace Siemens.PanelManager.Monitor.Workers
{
    public class ClearUnknowUserWorker : BackgroundService
    {
        private ILogger<ClearUnknowUserWorker> _logger;
        public ClearUnknowUserWorker(ILogger<ClearUnknowUserWorker> logger)
        {
            _logger = logger;
        }
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (true)
            {
                if (stoppingToken.IsCancellationRequested) return;
                await Task.Delay(10000);
                var connects = ConnectManager.GetCurrentlyToNext();
                foreach (var connect in connects)
                {
                    if (!ConnectManager.ConnectedList.ContainsKey(connect.ConnectionId))
                    {
                        connect.Abort();
                    }
                }
            }
        }
    }
}
