﻿using Siemens.PanelManager.Model.Database.Asset;
using SqlSugar;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.Server.DataPoint
{
    public class MLFBResolver
    {
        private readonly IServiceProvider _provider;
        public MLFBResolver(IServiceProvider provider)
        {
            _provider = provider;
        }

        public async Task UpdateDeviceInfoByMLFB(AssetInfo asset, DeviceDetails device, string mlfb, ISqlSugarClient client)
        {
            if (asset == null || asset.Id <= 0 || string.IsNullOrEmpty(mlfb) || device == null) return;
            var configs = await client.Queryable<MLFBResolveConfig>().Where(c=>c.DeviceType == asset.AssetType && c.DeviceModel == asset.AssetModel).ToArrayAsync();

            var keys = configs.Select(c=>c.GroupCode).ToArray();
            mlfb = mlfb.Replace("-", string.Empty);
            foreach(var key in keys) 
            {
                var list = configs.Where(c=>c.GroupCode == key).ToArray();
                foreach(var item in list) 
                {
                    if (Regex.IsMatch(mlfb, item.RuleString))
                    {
                        UpdateDeviceDetails(key, item.Value, device);
                    }
                }
            }
        }

        private void UpdateDeviceDetails(string key, string value, DeviceDetails device)
        {
            switch (key)
            {
                case "RatedCurrent":
                    {
                        if(decimal.TryParse(value, out var d)) 
                        {
                            device.RatedCurrent = d;
                        }
                        break;
                    }
                case "MechanicalSwitchSampleCycles":
                    {
                        if (int.TryParse(value, out var i))
                        {
                            device.MechanicalSwitchSampleCycles = i;
                        }
                        break;
                    }
                case "ElectricalSwitchSampleCycles":
                    {
                        if (int.TryParse(value, out var i))
                        {
                            device.ElectricalSwitchSampleCycles = i;
                        }
                        break;
                    }
                case "Icw":
                    {
                        if (decimal.TryParse(value, out var d))
                        {
                            device.Icw = d;
                        }
                        break;
                    }
                default:break;
            }
        }
    }
}
