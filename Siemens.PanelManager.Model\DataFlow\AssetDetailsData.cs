﻿using Siemens.PanelManager.Model.Database.Asset;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.DataFlow
{
    public class AssetDetailsData
    {
        public int AssetId { get; set; }
        public string AssetName { get; set; } = string.Empty;
        public string AssetType { get; set; } = string.Empty;
        public string AssetModel { get; set; } = string.Empty;
        public DateTime ChangeTime { get; set; } = DateTime.Now;
        public AssetLevel AssetLevel { get; set; }
        public Dictionary<string, string> Details { get; set; } = new Dictionary<string, string>();
    }
}
