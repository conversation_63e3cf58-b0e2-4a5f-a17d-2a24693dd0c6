﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Siemens.PanelManager.Model.Topology;

namespace Siemens.PanelManager.Job.UploadExcel.Diagram.TemplateModel.NodeTemplate
{
    /// <summary>
    /// 单线图中点元素
    /// </summary>
    internal class PointNode : NodeData
    {
        public override NodeType NodeType => NodeType.Point;
        public PointNode() 
        {
            TypeCode = "A";
            Name = "breaker";
            CloseStyle = "circle";
            SizeHight = 8;
            SizeWidth= 8;
            Category = "breakerTemplate";
        }

        [JsonProperty("index", NullValueHandling = NullValueHandling.Ignore)]
        public int? Index { get; set; }
        [JsonProperty("flag", NullValueHandling = NullValueHandling.Ignore)]
        public string? Flag { get; set; }
    }
}
