﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Common;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.Database.Topology;
using Siemens.PanelManager.Server.Topoplogy;
using SqlSugar;

namespace Siemens.PanelManager.Job.TopologyJob
{
    public class TopologyManangerJob : JobBase
    {
        public override string Name => "TopologyManangerJob";

        private IServiceProvider _provider;

        public TopologyManangerJob(IServiceProvider provider)
        {
            _provider = provider;
        }

        public override async Task Execute()
        {
            #region 获取更新内容
            var lastUpdatedTime = DateTime.Now.AddSeconds(-30);
            using (var client = _provider.GetRequiredService<ISqlSugarClient>())
            {
                var actor = _provider.GetRequiredService<ITopologyDataChangeRef>();
                var list = await client.Queryable<TopologyRuleInfo>()
                    .Where(r => r.UpdatedTime > lastUpdatedTime)
                    .Select(r => r.TopologyId)
                    .ToArrayAsync();

                if (list.Any())
                {
                    actor.TopologyNeedReload(list);
                }

                list = await client.Queryable<TopologyInfo>()
                    .Where(r => r.UpdatedTime > lastUpdatedTime && r.Type == "3D")
                    .Select(r => r.Id)
                    .ToArrayAsync();

                if (list.Any())
                {
                    actor.TopologyNeedReload(list);
                }

                #endregion

                #region 删除过期的更新日志
                var expiredTime = DateTime.Now.AddMinutes(-30).GetTimestampForMS();
                await client.Deleteable<TopologyDataChange>()
                    .Where(r => r.Timestamp <= expiredTime)
                    .ExecuteCommandAsync();
                #endregion

                #region 拆分单线图
                {
                    var service = _provider.GetRequiredService<TopoplogySplitService>();
                    await service.SplitPanelTopoplogy(client, lastUpdatedTime);
                }
                #endregion
            }
        }
    }
}
