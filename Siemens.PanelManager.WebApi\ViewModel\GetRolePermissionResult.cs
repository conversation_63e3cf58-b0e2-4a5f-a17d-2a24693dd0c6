﻿using Siemens.PanelManager.Model.Database.Auth;
using Siemens.PanelManager.WebApi.StaticContent;
using System.Runtime.CompilerServices;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class GetRolePermissionResult
    {
        public GetRolePermissionResult(Page[] pages, RolePageMapping[] rolePageMappings, MessageContext messageContext)
        {
            pages = pages.OrderBy(p => p.Id).OrderBy(p => p.OrderNo).ToArray();
            var i = 0;
            var level = 1;
            var children = new List<PermissionInfo>();
            while (i < pages.Length)
            {
                var newIndex = GetChildren(children, null, pages, rolePageMappings, level, i, messageContext);
                if (newIndex == i)
                {
                    break;
                }
                i = newIndex;
            }
            Children = children.ToArray();
        }

        public PermissionInfo[]? Children { get; set; }

        private int GetChildren(List<PermissionInfo> children, int[]? levelIds, Page[] pages, RolePageMapping[] rolePageMappings, int level, int index, MessageContext messageContext)
        {
            while (index < pages.Length)
            {
                var page = pages[index];
                if (page.IsSystemConfig)
                {
                    return index + 1;
                }
                if (levelIds != null)
                {
                    for (var levelIndex = 0; levelIndex < 2; levelIndex++)
                    {
                        if (levelIds[levelIndex] != 0 && levelIds[levelIndex] != page.LevelId[levelIndex])
                            return index;
                    }
                }
                bool isSelect = rolePageMappings.Any(rpm => rpm.PageId == page.Id);
                var permisssion = new PermissionInfo(page, isSelect, level, messageContext);
                children.Add(permisssion);
                if (page.LevelId[2] > 0)
                {
                    permisssion.Level = 4;
                    return index + 1;
                }

                var newChildren = new List<PermissionInfo>();
                var nextIndex = index + 1;
                while (nextIndex < pages.Length)
                {
                    var newIndex = GetChildren(newChildren, page.LevelId, pages, rolePageMappings, level + 1, nextIndex, messageContext);
                    if (newIndex == nextIndex)
                    {
                        nextIndex = newIndex;
                        break;
                    }
                    nextIndex = newIndex;
                }
                if (newChildren.Count > 0)
                {
                    permisssion.Children = newChildren.ToArray();
                }
                else
                {
                    permisssion.Level = 4;
                }
                return nextIndex;
            }
            return index;
        }
    }

    public class PermissionInfo
    {
        public PermissionInfo(Page page, bool isSelect, int level, MessageContext messageContext)
        {
            Id = page.Id;
            Label = messageContext.GetPageName(page.Id, page.PageName);
            Code = page.PageCode;
            Level = level;
            IsSelect = isSelect;
        }

        public int Id { get; set; }
        public string Code { get; set; }
        public string Label { get; set; }
        public bool IsSelect { get; set; }
        public int Level { get; set; }
        public PermissionInfo[]? Children { get; set; }
    }
}
