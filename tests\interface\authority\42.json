{"info": {"_postman_id": "190eb831-eaa2-4cbe-8db1-21fcda83d567", "name": "使用管理员账号进入panel manager用户管理菜单，批量删除用户", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "用户登录(超级管理员)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = pm.response.json().data.token//获取token\r", "pm.collectionVariables.set('token',token)//把token保存到全局变量中\r", "console.log (token)\r", "\r", "pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/login", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "获取用户列表(四位用户id)", "event": [{"listen": "test", "script": {"exec": ["let id = pm.response.json().data.items[0].id//获取id\r", "pm.collectionVariables.set('userId',id)//把id保存到全局变量中\r", "console.log (id)\r", "\r", "let id2 = pm.response.json().data.items[1].id//获取id\r", "pm.collectionVariables.set('user2Id',id2)//把id保存到全局变量中\r", "console.log (id2)\r", "\r", "let id3 = pm.response.json().data.items[2].id//获取id\r", "pm.collectionVariables.set('user3Id',id3)//把id保存到全局变量中\r", "console.log (id3)\r", "\r", "let id4 = pm.response.json().data.items[3].id//获取id\r", "pm.collectionVariables.set('user4Id',id4)//把id保存到全局变量中\r", "console.log (id4)\r", "\r", "pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"包含用户自己的信息\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"id\",\"userName\",\"personName\",\"tel\",\"email\",\"roles\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/api/v1/Users", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Users"]}}, "response": []}, {"name": "批量删除用户(成功)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"ids\": [\r\n    {{userId}},{{user2Id}},{{user3Id}},{{user4Id}}\r\n  ],\r\n  \"type\": \"string\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/Users/<USER>", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Users", "batch"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}], "variable": [{"key": "usernameindex", "value": 213, "type": "string"}, {"key": "username", "value": "user-213", "type": "string"}, {"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1lIjoiaGtuNzc3IiwiaHR0cDovL3NjaGVtYXMueG1sc29hcC5vcmcvd3MvMjAwNS8wNS9pZGVudGl0eS9jbGFpbXMvbmFtZWlkZW50aWZpZXIiOiI0OGYxNmVjMC03ZTY4LTRiNzItOWFiMi1mN2RmNjFkYTViODkiLCJTeW5jRGV2aWNlIjoiW10iLCJuYmYiOjE2NzcxNDEyODMsImV4cCI6MTY3NzE0MTI4NCwiaXNzIjoiU2llbWVuc0lzc3VlciIsImF1ZCI6IldlYkFwcEF1ZGllbmNlIn0.w_0H87s926JeZJmxCguk5QHiChVhFXhKwSvPrDtwFb8", "type": "string"}, {"key": "userId", "value": 114, "type": "string"}, {"key": "user2Id", "value": 113, "type": "string"}, {"key": "user3Id", "value": 112, "type": "string"}, {"key": "roleId", "value": 1, "type": "string"}, {"key": "user4Id", "value": 111, "type": "string"}, {"key": "BASE_URL", "value": "http://localhost:5000"}]}