{"info": {"_postman_id": "f2a9ce75-2c97-407e-bec4-78b71db2d7be", "name": "21使用管理员账号进入panel manager告警管理中的告警列表菜单，筛选事件类型是告警，点击详情查看基本信息，操作状态，快照信息，告警故障演变", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 8", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "获取事件类型为告警的告警列表 Copy 5", "event": [{"listen": "test", "script": {"exec": ["\r", "\r", "let id0 = pm.response.json().items[0].id\r", "pm.environment.set(\"id0\", id0);\r", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm?eventType=0&page=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm"], "query": [{"key": "eventType", "value": "0"}, {"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}]}}, "response": []}, {"name": "查看详情页基本信息、操作状态、 快照信息", "event": [{"listen": "test", "script": {"exec": ["\r", "pm.test(\"包含基本信息\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"alarmRuleId\");\r", "    pm.expect(pm.response.text()).to.include(\"name\");\r", "    pm.expect(pm.response.text()).to.include(\"rule\");\r", "    pm.expect(pm.response.text()).to.include(\"message\");\r", "    pm.expect(pm.response.text()).to.include(\"alarmStatus\");\r", "});\r", "pm.test(\"包含操作状态\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"severity\");\r", "    pm.expect(pm.response.text()).to.include(\"logTime\");\r", "    pm.expect(pm.response.text()).to.include(\"histories\");\r", "});\r", "pm.test(\"包含快照信息\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"assetStatus\");\r", "    pm.expect(pm.response.text()).to.include(\"name\");\r", "    pm.expect(pm.response.text()).to.include(\"value\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm/{{id0}}", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm", "{{id0}}"]}}, "response": []}, {"name": "查看详情页告警故障演变过程", "event": [{"listen": "test", "script": {"exec": ["\r", "pm.test(\"包含告警故障演变过程\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"data\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm/{{id0}}/retrospect?time=15", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm", "{{id0}}", "retrospect"], "query": [{"key": "time", "value": "15"}]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 1000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", "pm.test(\"code码为20000\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData[\"code\"]).to.eql(20000);", "});", ""]}}]}