﻿using Siemens.InfluxDB.Helper;
using Siemens.InfluxDB.Helper.Client;
using Siemens.InfluxDB.Helper.Interface;
using Siemens.PanelManager.Common.Log;
using Siemens.PanelManager.Model.Config;

namespace Siemens.PanelManager.Common.InfluxDB
{
    public static class InfluxDBClientFactory
    {
        private static InfluxDBConfig? _config;
        public static async Task LoadConfig(InfluxDBConfig config)
        {
            _config = config;
            try
            {
                var client = CreateClient();
                _config.BucketId = await client.GetBucketId();
                _config.OrgId = await client.GetOrgId();
            }
            catch (Exception ex)
            {
                LogHelper.Error(ex);
                _config = null;
            }
        }

        public static bool NeedConfig()
        {
            return _config == null;
        }

        public static InfluxDBConfig GetConfig()
        {
            if (_config == null) 
            {
                return new InfluxDBConfig();
            }
            return _config;
        }

        public static IInfluxDBClient CreateClient()
        {
            if (_config == null) 
            {
                LogHelper.Error("数据库缺少配置");
                throw new Exception("InfluxDB缺少数据库配置");
            }


            var dbConfig = new DBConfig
            {
                Url = _config.Url,
                Password = _config.Password,
                Bucket = _config.Bucket,
                OrgName = _config.OrgName,
                UserName = _config.UserName,
            };

            return new InfluxDBClient(dbConfig);
        }
    }
}

