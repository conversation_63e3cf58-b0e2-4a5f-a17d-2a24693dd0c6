﻿using Newtonsoft.Json;
using Siemens.PanelManager.Model.Database.Topology;
using Siemens.PanelManager.Model.Topology;

namespace Siemens.PanelManager.Job.UploadExcel.Diagram.TemplateModel.NodeTemplate
{
    /// <summary>
    /// 单线图中电源元素
    /// </summary>
    internal class SourceNode : NodeData
    {
        public override NodeType NodeType => NodeType.Source;
        public SourceNode(string? busBarName) 
        {
            TypeCode = "S";
            Name = "Source";
            OpenStyle = "statusLightA";
            CloseStyle = "statusLightA";
            SizeHight = 40;
            SizeWidth = 40;
            SourceType = "transformer";
            Category = "sourceNodeTemplate";
            BusBarId = busBarName;
        }

        [JsonProperty("busBarId", NullValueHandling = NullValueHandling.Ignore)]
        public string? BusBarId { get; set; }

        public override void AddRules(List<TopologyRuleInfo> ruleInfos)
        {
            if (!Key.HasValue) return;
            if (!AssetId.HasValue) return;
            // 当前数据点位不再此点位进行加载
        }
    }
}
