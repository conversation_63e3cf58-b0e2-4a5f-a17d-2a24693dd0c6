﻿using System;
using System.Collections.Generic;

namespace Siemens.PanelManager.SyncService.UdcModels
{
    public partial class ArchiveDataFormat6
    {
        public string ObjectId { get; set; } = null!;
        public long Archive { get; set; }
        public long TimestampInS { get; set; }
        public string? EnergyKwhImportSum { get; set; }
        public string? EnergyKwhImportTariff1Value { get; set; }
        public string? EnergyKwhImportTariff2Value { get; set; }
        public string? EnergyKwhExportSum { get; set; }
        public string? EnergyKwhExportTariff1Value { get; set; }
        public string? EnergyKwhExportTariff2Value { get; set; }
        public long EnergyKwhSum { get; set; }
        public string? EnergyKvarhImportSum { get; set; }
        public string? EnergyKvarhImportTariff1Value { get; set; }
        public string? EnergyKvarhImportTariff2Value { get; set; }
        public string? EnergyKvarhExportSum { get; set; }
        public string? EnergyKvarhExportTariff1Value { get; set; }
        public string? EnergyKvarhExportTariff2Value { get; set; }
        public long EnergyKvarhSum { get; set; }
    }
}
