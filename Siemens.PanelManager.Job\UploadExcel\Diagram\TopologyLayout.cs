﻿namespace Siemens.PanelManager.Job.UploadExcel.Diagram
{
    /// <summary>
    /// 单线图中 母线的排布
    /// 当大于 2小于 5
    /// 1 2
    /// 3 4
    /// 当大于5
    /// 1 2 3
    /// 4 5 6
    /// </summary>
    internal class TopologyLayout
    {
        private Dictionary<int, bool> _busBarLayout = new Dictionary<int, bool>();
        public TopologyLayout(int[] busBarList)     
        {
            var count = busBarList.Length;
            for (var i = 0; i < count; i++)
            {
                //if (i == 2 && count < 5)
                //{
                //    _busBarLayout.Add(busBarList[i], true);
                //}
                //else if (i == 3 && count >= 5 && count < 16)
                //{
                //    _busBarLayout.Add(busBarList[i], true);
                //}
                //else
                //{
                //    _busBarLayout.Add(busBarList[i], false);
                //}
                _busBarLayout.Add(busBarList[i], false);
            }
        }

        public bool NeedCreateNewLine(int busBar)
        {
            _busBarLayout.TryGetValue(busBar, out bool result);
            return result;
        }
    }
}
