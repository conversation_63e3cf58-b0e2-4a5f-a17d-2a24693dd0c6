﻿using Microsoft.Extensions.DependencyInjection;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Server.DataPoint;
using SqlSugar;

namespace Siemens.PanelManager.Server.Asset
{
    public class DeviceProtectionSettingService
    {
        private IServiceProvider _provider;
        public DeviceProtectionSettingService(IServiceProvider provider)
        {
            _provider = provider;
        }
        public async Task<bool> SyncDeviceProtectionSetting(int assetId, ISqlSugarClient? client = null, string updatedBy = "System")
        {
            if (assetId <= 0) return false;
            if (client == null)
            {
                client = _provider.GetRequiredService<SqlSugarScope>();
            }
            var assetInfo = await client.Queryable<AssetInfo>().FirstAsync(a => a.Id == assetId);
            if (assetInfo == null || string.IsNullOrEmpty(assetInfo.ObjectId)) return false;
            var dataPointService = _provider.GetRequiredService<DataPointServer>();
            var dataPoints = await dataPointService.GetDataPointInfos(assetInfo.AssetLevel, assetInfo.AssetType, assetInfo.AssetModel);
            var protectionSettings = dataPoints.Where(d => d.GroupName == "ProtectionSetting").ToArray();
            if (protectionSettings.Length <= 0) return false;
            var udcCodes = protectionSettings.Where(p => !string.IsNullOrEmpty(p.UdcCode)).Select(p => p.UdcCode ?? string.Empty).ToArray();
            if (udcCodes.Length <= 0) return false;
            var data = await dataPointService.GetDataByUDCApi(assetInfo.ObjectId, udcCodes);
            var panelData = new Dictionary<string, string>();
            foreach (var dataPoint in protectionSettings)
            {
                if (!string.IsNullOrEmpty(dataPoint.UdcCode)&& data.ContainsKey(dataPoint.UdcCode))
                {
                    if (panelData.ContainsKey(dataPoint.Code))
                    { 
                        panelData[dataPoint.Code] = data[dataPoint.UdcCode];
                    }
                    else if(!panelData.ContainsKey(dataPoint.Code))
                    {
                        panelData.Add(dataPoint.Code, data[dataPoint.UdcCode]);
                    }
                  
                }
            }
            await UpdateProtectionSettingToDB(assetId, client, updatedBy, panelData);
            return true;
        }

        public async Task UpdateProtectionSettingToDB(int assetId, ISqlSugarClient? client, string updatedBy, Dictionary<string, string> data)
        {
            if (assetId <= 0) return;
            if (client == null)
            {
                client = _provider.GetRequiredService<SqlSugarScope>();
            }

            var currentSetting = await client.Queryable<BreakerProtectionSetting>().FirstAsync(a => a.AssetId == assetId);
            if (currentSetting == null)
            {
                currentSetting = new BreakerProtectionSetting
                {
                    AssetId = assetId,
                    CreatedBy = updatedBy,
                    CreatedTime = DateTime.Now
                };
            }

            var func = _provider.GetRequiredService<ObjectReflectFunc>();
            func.UpdateObjByDic(currentSetting, data);

            currentSetting.UpdatedBy = updatedBy;
            currentSetting.UpdatedTime = DateTime.Now;

            await client.Storageable<BreakerProtectionSetting>(currentSetting).ExecuteCommandAsync();
        }
    }
}
