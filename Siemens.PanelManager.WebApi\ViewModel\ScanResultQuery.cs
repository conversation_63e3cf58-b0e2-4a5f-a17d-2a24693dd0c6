﻿using Microsoft.AspNetCore.Mvc;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class ScanResultQuery
    {
        [FromQuery(Name = "needPage")]
        public bool NeedPage { get; set; }

        [FromQuery(Name = "page")]
        public int Page { get; set; } = 1;

        [FromQuery(Name = "pageSize")]
        public int PageSize { get; set; } = 10;

        [FromQuery(Name = "name")]
        public string? Name { get; set; }

        [FromQuery(Name = "assetType")]
        public string? AssetType { get; set; }

        [FromQuery(Name = "assetId")]
        public string? AssetId { get; set; }

        [FromQuery(Name = "interfaceName")]
        public string? InterfaceName { get; set; }

        [FromQuery(Name = "importStatus")]
        public string? ImportStatus { get; set; }

        [FromQuery(Name = "ipAddress")]
        public string? IPAddress {  get; set; }
    }
}
