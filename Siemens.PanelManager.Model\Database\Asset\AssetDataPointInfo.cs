﻿using Newtonsoft.Json.Linq;
using SqlSugar;

namespace Siemens.PanelManager.Model.Database.Asset
{
    [SugarTable("asset_data_point_info")]
    public class AssetDataPointInfo : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [Uniqueness]
        [SugarColumn(ColumnName = "code", IsNullable = false, Length = 256)]
        public string Code { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "name", IsNullable = false, Length = 256)]
        public string Name { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "group_name", IsNullable = true, Length = 256)]
        public string GroupName { get; set; } = string.Empty;
        [Uniqueness]
        [SugarColumn(ColumnName = "asset_level", IsNullable = false)]
        public AssetLevel AssetLevel { get; set; }
        [Uniqueness]
        [SugarColumn(ColumnName = "asset_type", Length = 50, IsNullable = true)]
        public string? AssetType { get; set; }
        [Uniqueness]
        [SugarColumn(ColumnName = "asset_model", Length = 50, IsNullable = true)]
        public string? AssetModel { get; set; }
        [SugarColumn(ColumnName = "filter_Ids", Length = 256, IsNullable = true)]
        public string FilterIds { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "udc_code", IsNullable = true, Length = 256)]
        public string? UdcCode { get; set; }
        [SugarColumn(ColumnName = "unit", IsNullable = true, Length = 256)]
        public string Unit { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "collect_mode", IsNullable = true, Length = 256)]
        public string CollectMode { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "is_system_status", IsNullable = true)]
        public bool IsSystemStatus { get; set; } = false;
        [SugarColumn(ColumnName = "can_listen", IsNullable = true)]
        public bool CanListen { get; set; } = false;

        [SugarColumn(ColumnName = "save_to_table", IsNullable = true, Length = 50)]
        public string? SaveToTable { get; set; }
        [SugarColumn(ColumnName = "save_func", IsNullable = true, Length = 50)]
        public string? SaveFunc { get; set; }

        [SugarColumn(ColumnName = "extend", IsNullable = true, Length = 256)]
        public string? Extend { get; set; }
        [SugarColumn(ColumnName = "sort", IsNullable = true)]
        public int Sort { get; set; }

        [SugarColumn(ColumnName = "parent_name", IsNullable = true, Length = 256)]
        public string? ParentName { get; set; }

        [SugarColumn(ColumnName = "mqtt_group_name", IsNullable = true, Length = 256)]
        public string? MqttGroupName { get; set; }

        [SugarColumn(ColumnName = "mqtt_sampling_period", IsNullable = true)]
        public int? MqttSamplingPeriod { get; set; }

        [SugarColumn(ColumnName = "is_default_mqtt", IsNullable = true)]
        public short? IsDefaultMqtt { get; set; }

        /// <summary>
        /// 是否可以告警监听
        /// </summary>
        [SugarColumn(ColumnName = "can_alarm_listen", IsNullable = true)]
        public bool? CanAlarmListen { get; set; }

        /// <summary>
        /// 是否可以图表
        /// </summary>
        [SugarColumn(ColumnName = "can_chart", IsNullable = true)]
        public bool? CanChart { get; set; }

        /// <summary>
        /// 是否可以在可视化中添加实时值
        /// </summary>
        [SugarColumn(ColumnName = "can_print", IsNullable = true)]
        public bool? CanPrint { get; set; }

        /// <summary>
        /// 是否可以上报
        /// </summary>
        [SugarColumn(ColumnName = "can_reported_data", IsNullable = true)]
        public bool? CanReportedData { get; set; }

        [SugarColumn(IsIgnore = true)]
        public string? FuncName
        {
            get
            {
                if (!string.IsNullOrEmpty(Extend))
                {
                    var obj = JObject.Parse(Extend);
                    if (obj.ContainsKey("FumcName"))
                    {
                        return obj.GetValue("FumcName")?.Value<string>() ?? string.Empty;
                    }
                }
                return null;
            }
        }

        /// <summary>
        /// 是否二进制
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool IsBit { get; set; } = false;
    }
}
