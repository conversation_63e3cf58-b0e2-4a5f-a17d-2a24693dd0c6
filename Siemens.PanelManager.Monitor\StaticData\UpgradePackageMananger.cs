﻿using Microsoft.AspNetCore.SignalR;
using Newtonsoft.Json;
using System.Diagnostics.Eventing.Reader;
using System.IO;
using System.IO.Compression;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.Monitor.StaticData
{
    static class UpgradePackageMananger
    {
        public const string BasePath = "Base";
        public const string Part1Path = "Part1";
        public const string Part2Path = "Part2";
        public const string FolderName = "UpgradePackage";
        public const string TempFolderName = "Temp";
        public const string JsonFileName = "CurrentStatus.json";

        private static object _lock = new object();
        static UpgradePackageMananger()
        {
            var basePath = AppContext.BaseDirectory;
            var upgradeDirectoryPath = Path.Combine(basePath, FolderName);
            var files = Directory.GetFiles(upgradeDirectoryPath, "*.json");
            foreach ( var file in files) 
            {
                if (Regex.IsMatch(file, "CurrentStatus\\.json$"))
                {
                    _packageInfo = JsonConvert.DeserializeObject<UpgradePackageInfo>(File.ReadAllText(file));
                }
            }
        }

        private static UpgradePackageInfo? _packageInfo;
        public static string UpgradePath
        {
            get
            {
                return _packageInfo?.UpgradePath ?? BasePath;
            }
        }

        public static string CurrentlyYamlFile
        {
            get
            {
                if(_packageInfo != null)
                {
                    var basePath = AppContext.BaseDirectory;
                    var upgradeDirectoryPath = Path.Combine(basePath, FolderName, UpgradePath);
                    if (Directory.Exists(upgradeDirectoryPath))
                    {
                        var files = Directory.GetFiles(upgradeDirectoryPath);
                        var file = files.FirstOrDefault(f => Regex.IsMatch(f, "[\\w|\\W]+\\.(yml|yaml)$"));

                        return file ?? string.Empty;
                    }
                }
                return string.Empty;
            }
        }
        public static string CurrentlyTarFile
        {
            get
            {
                if (_packageInfo != null)
                {
                    var basePath = AppContext.BaseDirectory;
                    var upgradeDirectoryPath = Path.Combine(basePath, FolderName, UpgradePath);
                    if (Directory.Exists(upgradeDirectoryPath))
                    {
                        var files = Directory.GetFiles(upgradeDirectoryPath);
                        var file = files.FirstOrDefault(f => Regex.IsMatch(f, "[\\w|\\W]+\\.tar$"));

                        return file ?? string.Empty;
                    }
                }
                return string.Empty;
            }
        }

        public static string InitUpgradePath()
        {
            lock(_lock)
            {
                if (_packageInfo == null) 
                {
                    _packageInfo = new UpgradePackageInfo();
                }

                var upgrablePath = Part1Path;
                if (Part1Path.Equals(_packageInfo.UpgradePath))
                {
                    upgrablePath = Part2Path;
                }
                _packageInfo.UpgradePath = upgrablePath;
                _packageInfo.UpgradeTime = DateTime.Now;

                var basePath = AppContext.BaseDirectory;
                var upgradeDirectoryPath = Path.Combine(basePath, FolderName);
                var realPath = Path.Combine(upgradeDirectoryPath, upgrablePath);

                if (Directory.Exists(realPath)) 
                {
                    Directory.Delete(realPath, true);
                }
                Directory.CreateDirectory(realPath);

                var currentStatusJsonPath = Path.Combine(upgradeDirectoryPath, JsonFileName);
                var jsonFile = new FileInfo(currentStatusJsonPath);
                if (jsonFile.Exists)
                {
                    using (var fs = new FileStream(jsonFile.FullName, FileMode.Truncate))
                    {
                        fs.Write(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(_packageInfo)));
                    }
                }
                else
                {
                    using (var fs = jsonFile.Create())
                    {
                        fs.Write(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(_packageInfo)));
                    }
                }

                return realPath;
            }
        }
        public static DateTime UpgradeTime
        {
            get
            {
                return _packageInfo?.UpgradeTime ?? DateTime.MinValue;
            }
        }

        private class UpgradePackageInfo
        {
            public string? UpgradePath { get; set; }
            public DateTime? UpgradeTime { get; set; }
        }

        public static async Task<bool> CheckZip(string tempFile, string fileName, ILogger logger, List<FileInfo> fileInfos, Dictionary<string, string> paramDic)
        {
            var basePath = AppContext.BaseDirectory;
            var tempFolder = Path.Combine(basePath, UpgradePackageMananger.TempFolderName, fileName);
            if (!Directory.Exists(tempFolder))
            {
                Directory.CreateDirectory(tempFolder);
            }
            ZipFile.ExtractToDirectory(tempFile, tempFolder, true);

            #region 2 验证文件
            logger.LogInformation("check file [Step-2][Begin]");
            var zipFiles = Directory.GetFiles(tempFolder);
            var checkFlag = 0;
            FileInfo? tarFileInfo = null;
            FileInfo? md5File = null;
            foreach (var zipFile in zipFiles)
            {
                var fileInfo = new FileInfo(zipFile);
                if (fileInfo.Exists)
                {
                    fileInfos.Add(fileInfo);
                    var thisFileName = fileInfo.Name.ToLower();
                    if (Regex.IsMatch(thisFileName, "[\\w|\\W]+\\.tar$"))
                    {
                        checkFlag |= 1;
                        tarFileInfo = fileInfo;
                        paramDic.TryAdd("tarFileInfo", fileInfo.FullName);
                    }
                    else if (Regex.IsMatch(thisFileName, "[\\w|\\W]+\\.(yml|yaml)$"))
                    {
                        checkFlag |= 2;
                    }
                    else if (Regex.IsMatch(thisFileName, "[\\w|\\W]+\\.md5$"))
                    {
                        checkFlag |= 4;
                        md5File = fileInfo;
                    }
                    else if (Regex.IsMatch(thisFileName, "[\\w|\\W]+\\.txt$"))
                    {
                        checkFlag |= 8;
                    }
                    else if (Regex.IsMatch(thisFileName, "[\\w|\\W]+\\.json$"))
                    {
                        paramDic.TryAdd("dockerMappingFile", fileInfo.FullName);
                    }
                }
            }

            if (checkFlag < 15)
            {
                logger.LogInformation("File missing");
                logger.LogInformation("check file [Step-2][Fail]");
                return false;
            }

            if (tarFileInfo == null || md5File == null)
            {
                logger.LogInformation("Package invalid");
                logger.LogInformation("check file [Step-2][Fail]");
                return false;
            }
            using (var fs = tarFileInfo!.OpenRead())
            {
                var md5 = Convert.ToBase64String(await MD5.Create().ComputeHashAsync(fs));

                var md5Value = await File.ReadAllTextAsync(md5File!.FullName, Encoding.UTF8);
                if (!md5Value.Equals(md5))
                {
                    logger.LogInformation("md5 invalid");
                    logger.LogInformation("check file [Step-2][Fail]");
                    return false;
                }
            }
            logger.LogInformation("check file [Step-2][Finish]");
            #endregion
            return true;
        }
    }

    public class UpgradeWorkerStatus
    {
        private object _lock = new object();
        private string _tranId = string.Empty;
        public string JobId { get; private set; } = string.Empty;
        /// <summary>
        /// 0: 未开始
        /// 10: 等待上传
        /// 11: 上传成功
        /// 20: 开始处理
        /// </summary>
        public int Status { get; private set; }
        public AutoResetEvent AutoReset { get; private set; } = new AutoResetEvent(false);
        public string UploadFilePath { get; private set; } = string.Empty;
        public string FileName { get; private set; } = string.Empty;
        public Dictionary<string, string> Params { get; private set; } = new Dictionary<string, string>();
        public string StartUpgrade(IClientProxy proxy)
        {
            if (Status == 0)
            {
                lock (_lock)
                {
                    Status = 10;
                    _tranId = Guid.NewGuid().ToString();
                    JobId = Guid.NewGuid().ToString();
                    AutoReset = new AutoResetEvent(false);
                    Proxy = proxy;
                    return _tranId;
                }
            }
            return string.Empty;
        }

        public void UploadFile(string filePath, string fileName)
        {
            if (Status == 10)
            {
                lock (_lock)
                {
                    Status = 11;
                    UploadFilePath = filePath;
                    FileName = fileName;
                    AutoReset.Set();
                }
            }
        }

        public string StartUpgradeByUpload(Dictionary<string,string> paramDic)
        {
            if (Status == 0)
            {
                lock (_lock)
                {
                    Status = 10;
                    
                    _tranId = Guid.NewGuid().ToString();
                    JobId = Guid.NewGuid().ToString();
                    AutoReset = new AutoResetEvent(true);
                    Params.Clear();
                    foreach (var kv in paramDic)
                    {
                        Params.Add(kv.Key, kv.Value);
                    }
                    Proxy = null;
                    return _tranId;
                }
            }
            return string.Empty;
        }

        public IClientProxy? Proxy { get; private set; }

        public void BeginProcess()
        {
            if (Status == 11)
            {
                lock (_lock)
                {
                    Status = 20;
                }
            }
        }

        public void Finish()
        {
            lock (_lock)
            {
                Status = 0;
                Proxy = null;
            }
        }

        public bool Cancel(string tranId)
        {
            // 当已经开始无法终止
            if (Status != 0 && Status != 20 && _tranId.Equals(tranId))
            {
                lock (_lock)
                {
                    Status = 0;
                    UploadFilePath = string.Empty;
                    FileName = string.Empty;
                    Proxy = null;
                    AutoReset.Set();
                    return true;
                }
            }
            return false;
        }


    }
}
