﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components.Server.Circuits;
using Microsoft.AspNetCore.Mvc;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Model.Chart;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.Model.Database.Topology;
using Siemens.PanelManager.Server.Asset;
using Siemens.PanelManager.Server.Energy;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class AcbInUseController : SiemensApiControllerBase
    {
        private ILogger<AcbInUseController> _log;
        private ISqlSugarClient _client;
        private IServiceProvider _provider;
        private SiemensCache _cache;

        public AcbInUseController(IServiceProvider provider, SiemensCache cache, ILogger<AcbInUseController> log)
            : base(provider, cache)
        {
            _provider = provider;
            _client = provider.GetService<ISqlSugarClient>();
            _cache = cache;
            _log = log;
        }

        [HttpGet("{circuitId}")]
        //[SwaggerOperation(Summary = "Swagger_Asset_Select", Description = "Swagger_Asset_Select_Desc")]
        //[Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<AcbStatusOverview>> GetAcbStatus(int circuitId, [FromQuery] string queryDevice = "0")
        {
            // 回路的assetId
            if (circuitId <= 0)
            {
                return new ResponseBase<AcbStatusOverview>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var children = await _client.Queryable<AssetRelation>()
                .Where(a => a.ParentId == circuitId && a.AssetLevel == AssetLevel.Device)
                .Select(a => a.ChildId)
                .ToListAsync();

            var acbInfo = await _client.Queryable<AssetInfo>()
                .Where(a => SqlFunc.ContainsArray(children, a.Id) && (a.AssetType == "ACB" || a.AssetType == "MCB" || a.AssetType == "MCCB"))
                .FirstAsync();

            AcbStatusOverview overview = new();
            if (acbInfo == null)
            {
                overview.Overview = AcbUnitOverview.Excellent;

                return new ResponseBase<AcbStatusOverview>()
                {
                    Code = 20000,
                    Data = overview
                };
            }

            var acbInUseServer = _provider.GetRequiredService<AcbInUseServer>();
            var tempValue = await acbInUseServer.GetAcbInUseDataAsync(acbInfo.Id);


            if (tempValue == null)
            {
                overview.Overview = AcbUnitOverview.Excellent;

                return new ResponseBase<AcbStatusOverview>()
                {
                    Code = 20000,
                    Data = overview
                };
            }
            else
            {
                overview.ResistanceA = tempValue.ResistanceA;
                overview.ResistanceB = tempValue.ResistanceB;
                overview.ResistanceC = tempValue.ResistanceC;

                if (tempValue.ResistanceA == -40
                    && tempValue.ResistanceB == -40
                    && tempValue.ResistanceC == -40)
                {
                    overview.Overview = AcbUnitOverview.Excellent;
                }
                else if (tempValue.ResistanceA < 10000
                    && tempValue.ResistanceB < 10000
                    && tempValue.ResistanceC < 10000)
                {

                }
                else if (tempValue.ResistanceA < 100000
                    && tempValue.ResistanceB < 100000
                    && tempValue.ResistanceC < 100000)
                {
                    overview.Overview = AcbUnitOverview.Good;
                }
                else if (tempValue.ResistanceA < 1000000
                    || tempValue.ResistanceB < 100000
                    || tempValue.ResistanceC < 100000)
                {
                    overview.Overview = AcbUnitOverview.Medium;
                }
                else
                {
                    overview.Overview = AcbUnitOverview.Bad;
                }
            }

            if (queryDevice == "1")
            {
                var tempPacs = await _client.Queryable<AssetInfo>()
                    .Where(a => a.Id == tempValue.UpPacAssetId || a.Id == tempValue.DownPacAssetId)
                    .ToListAsync();

                if (tempPacs != null && tempPacs.Count != 0)
                {
                    AcbUnitDeviceInfo acbUnitDevice;

                    foreach (var item in tempPacs)
                    {
                        acbUnitDevice = new AcbUnitDeviceInfo();
                        if (item.Id == tempValue.UpPacAssetId)
                        {
                            acbUnitDevice.AcbUnitDeviceType = AcbUnitDeviceType.Up;
                        }
                        else
                        {
                            acbUnitDevice.AcbUnitDeviceType = AcbUnitDeviceType.Down;
                        }

                        acbUnitDevice.Id = item.Id;
                        acbUnitDevice.Name = item.AssetName;
                        acbUnitDevice.Model = item.AssetModel;
                        acbUnitDevice.Type = item.AssetType;
                        acbUnitDevice.Location = item.Location;

                        overview.Devices.Add(acbUnitDevice);
                    }
                }
            }

            return new ResponseBase<AcbStatusOverview>()
            {
                Code = 20000,
                Data = overview
            };
        }

        [HttpGet("Resistance")]
        public async Task<ResponseBase<AcbChartResult>> GetResistance(AcbResistanceQueryParam queryParam)
        {
            DateTime tempEndDate = DateTime.MinValue;
            if (queryParam == null
                || queryParam.CircuitId <= 0
                || string.IsNullOrWhiteSpace(queryParam.StartDate)
                || !DateTime.TryParse(queryParam.StartDate, out var tempStartDate)
                || tempStartDate > DateTime.Now
                || (queryParam.DateType == AcbChartDateType.Custom && !DateTime.TryParse(queryParam.EndDate, out tempEndDate)))
            {
                return new ResponseBase<AcbChartResult>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var children = await _client.Queryable<AssetRelation>()
                .Where(a => a.ParentId == queryParam.CircuitId && a.AssetLevel == AssetLevel.Device)
                .Select(a => a.ChildId)
                .ToListAsync();

            var acbInfo = await _client.Queryable<AssetInfo>()
                .Where(a => SqlFunc.ContainsArray(children, a.Id) && (a.AssetType == "ACB" || a.AssetType == "MCB" || a.AssetType == "MCCB"))
                .FirstAsync();

            if (acbInfo == null)
            {
                return new ResponseBase<AcbChartResult>()
                {
                    Code = 20000,
                    Message = MessageContext.ErrorParam
                };
            }

            var acbInUseServer = _provider.GetRequiredService<AcbInUseServer>();
            var lineChart = await acbInUseServer.GetAcbResistanceChart(acbInfo.Id, tempStartDate, tempEndDate, queryParam.DateType.GetHashCode(), queryParam.ChartType.GetHashCode());

            AcbChartResult acbChartResult = new();
            LineChartModel lineChartModel = lineChart as LineChartModel;

            acbChartResult.LineChartModel = lineChartModel;

            return new ResponseBase<AcbChartResult>()
            {
                Code = 20000,
                Data = acbChartResult
            };
        }
    }
}
