﻿namespace Siemens.InfluxDB.Helper.General
{
    public static class InfluxDBGroupFun
    {
        public static T Max<T>(T data) where T : struct
        {
            return default(T);
        }

        public static T Min<T>(T data) where T : struct
        {
            return default(T);
        }

        public static T Mean<T>(T data) where T : struct
        {
            return default(T);
        }

        public static T Count<T>(T data) where T : struct
        {
            return default(T);
        }

        public static T Sum<T>(T data) where T : struct
        {
            return default(T);
        }

        public static T First<T>(T data) where T : struct
        {
            return default(T);
        }

        public static T Last<T>(T data) where T : struct
        {
            return default(T);
        }

        public static T Median<T>(T data) where T : struct
        {
            return default(T);
        }


        public static T Max<T>(T? data) where T : struct
        {
            return default(T);
        }

        public static T Min<T>(T? data) where T : struct
        {
            return default(T);
        }

        public static T Mean<T>(T? data) where T : struct
        {
            return default(T);
        }

        public static T Count<T>(T? data) where T : struct
        {
            return default(T);
        }

        public static T Sum<T>(T? data) where T : struct
        {
            return default(T);
        }

        public static T First<T>(T? data) where T : struct
        {
            return default(T);
        }

        public static T Last<T>(T? data) where T : struct
        {
            return default(T);
        }

        public static T Median<T>(T? data) where T : struct
        {
            return default(T);
        }
    }
}
