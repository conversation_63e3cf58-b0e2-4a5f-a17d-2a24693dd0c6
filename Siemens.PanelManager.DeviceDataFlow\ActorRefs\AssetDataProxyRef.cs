﻿using Akka.Actor;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.DataFlow;

namespace Siemens.PanelManager.DeviceDataFlow.ActorRefs
{
    public class AssetDataProxyRef : IAssetDataProxyRef
    {
        private readonly IActorRef _ref;
        private readonly IActorRef _saveRef;
        internal AssetDataProxyRef(IActorRef actorRef, IActorRef saveRef)
        {
            _ref = actorRef;
            _saveRef = saveRef;
        }

        public void InputData(AssetInputData data)
        {
            _ref.Tell(data);
        }

        public void DataChanged(AssetChangeData data)
        {
            _ref.Tell(data);
        }

        public void AssetDetail(AssetDetailsData data)
        {
            _saveRef.Tell(data);
        }
    }
}
