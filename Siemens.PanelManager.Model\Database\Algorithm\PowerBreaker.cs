﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Algorithm
{
    [SugarTable("power_breaker")]
    public class PowerBreaker : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsNullable = false,IsIdentity =true)]
        public int Id { get; set; }
        [UniquenessAttribute]
        [SugarColumn(ColumnName = "model", IsNullable = false, Length = 256)]
        public string Model { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "power", IsNullable = false)]
        public decimal Power { get; set; }
        [SugarColumn(ColumnName = "rated_current", IsNullable = false)]
        public decimal RatedCurrent { get; set; }
    }
}
