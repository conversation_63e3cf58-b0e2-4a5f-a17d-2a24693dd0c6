﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Quartz;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.Model.DataFlow;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.Server.DataPoint;
using SqlSugar;
using System.Reflection.Metadata;
using System.Security.Cryptography;
using System.Text;
using TouchSocket.Core;
using static Akka.Actor.FSMBase;

namespace Siemens.PanelManager.Job.Alarm
{
    [DisallowConcurrentExecution]
    public class GetDeviceMessagesJob : JobBase
    {
        private const string UserName = "MessageJober";
        private const string AssetSimpleInfoCacheKey = "Asset:SimpleInfo-{0}";
        public override string Name => "GetDeviceMessagesJob";
        private readonly ILogger _logger;
        private readonly IServiceProvider _provider;

        public GetDeviceMessagesJob(ILogger<GetDeviceMessagesJob> logger,
            IServiceProvider provider)
        {
            _logger = logger;
            _provider = provider;
        }

        public override async Task Execute()
        {
            //var typeName = "DeviceModelForMessage".ToUpper();
            var refObj = _provider.GetRequiredService<IAlarmRef>();

            var typeNames = "DEVICEMODELFORMESSAGE,DEVICEMODEL".Split(',').ToList();

            var cache = _provider.GetRequiredService<SiemensCache>();
            using var client = _provider.GetRequiredService<ISqlSugarClient>();
            var configInfo = await client.Queryable<SystemStaticModel>()
                .Where(s => typeNames.Contains(s.Type))
                .ToArrayAsync();

            var deviceModels = configInfo.Select(c => c.Code).ToList();
            var assetInfos = await client.Queryable<AssetInfo>()
                .Where(a => a.AssetModel != null && deviceModels.Contains(a.AssetModel))
                .Where(a => a.AssetModel != "Other" && a.AssetType != "GeneralDevice")
                .Where(a => a.AssetModel != "Modbus")
                .ToArrayAsync();

            var assetMessageConfigs = await client.Queryable<AssetMessagesConfig>().ToArrayAsync();

            var needRemoveConfigs = assetMessageConfigs.Where(m => !assetInfos.Any(a => a.Id == m.AssetId) && m.AssetId > 0).ToList();
            if (needRemoveConfigs != null && needRemoveConfigs.Any())
            {
                try
                {
                    await client.Deleteable<AssetMessagesConfig>(needRemoveConfigs).ExecuteCommandAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "GetDeviceMessagesJob Delete AssetMessagesConfig 错误");
                }
            }

            var count = 100;
            var apiRef = _provider.GetRequiredService<IUDCApiRef>();
            foreach (var asset in assetInfos)
            {
                if ("Other".Equals(asset.AssetModel, StringComparison.OrdinalIgnoreCase)
                    || "GeneralDevice".Equals(asset.AssetType, StringComparison.OrdinalIgnoreCase)
                    || ("Modbus".Equals(asset.AssetModel, StringComparison.OrdinalIgnoreCase)
                    && "Gateway".Equals(asset.AssetType, StringComparison.OrdinalIgnoreCase)))
                {
                    continue;
                }

                try
                {
                    if ("3WL".Equals(asset.AssetModel))
                    {
                        await Save3WLUDCLog(refObj, cache, client, count, apiRef, asset);
                    }
                    else if ("3WA".Equals(asset.AssetModel))
                    {
                        await Save3WAUDCLog(refObj, cache, client, assetMessageConfigs, count, apiRef, asset);
                    }
                    else
                    {
                        await SaveUDCLog(refObj, cache, client, assetMessageConfigs, count, apiRef, asset);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "GetDeviceMessagesJob 错误");
                }
            }
        }

        private async Task Save3WAUDCLog(IAlarmRef refObj, SiemensCache cache, ISqlSugarClient client, AssetMessagesConfig[] assetMessagesConfigs, int count, IUDCApiRef apiRef, AssetInfo asset)
        {
            var messageConfig = assetMessagesConfigs.FirstOrDefault(m => m.AssetId == asset.Id);
            var newConfig = false;
            if (messageConfig == null)
            {
                messageConfig = new AssetMessagesConfig
                {
                    AssetId = asset.Id,
                    CreatedBy = UserName,
                    CreatedTime = DateTime.Now,
                    UpdatedBy = UserName,
                    UpdatedTime = DateTime.Now,
                };
                newConfig = true;
            }

            var cacheKey = string.Format("AssetStatus:Currently-{0}", asset.Id);
            var currentStatus = cache.GetHashAllData(cacheKey);
            var dataPointServer = _provider.GetRequiredService<DataPointServer>();
            var dataPointList = await dataPointServer.GetDataPointInfos(asset.AssetLevel, asset.AssetType, asset.AssetModel, asset.Id);

            var alarmServer = _provider.GetRequiredService<AlarmRuleServer>();
            var newStatusStr = alarmServer.GetAssetStatusStr(currentStatus, dataPointList, false);

            if (string.IsNullOrEmpty(asset.ObjectId)) return;
            var assetSimple = cache.Get<AssetSimpleInfo>(string.Format(AssetSimpleInfoCacheKey, asset.Id));
            if (assetSimple == null) return;

            var oid = messageConfig.Oid;

            var resultList = await apiRef.GetDeviceMessages(asset.ObjectId, oid, count);
            if (resultList == null || resultList.Count == 0) return;

            var alarmList = new List<AlarmLog>();
            foreach (var message in resultList.Embedded.Items)
            {
                if (message.Oid.Equals(messageConfig.Oid))
                {
                    continue;
                }

                var severity = GetAlarmSeverity(message.Severity ?? string.Empty);
                var status = severity == AlarmSeverity.High ? AlarmLogStatus.New : AlarmLogStatus.None;
                var eventType = severity == AlarmSeverity.High ? AlarmEventType.UdcAlarm : AlarmEventType.DeviceLog;

                var alarmLog = new AlarmLog
                {
                    SubstationName = assetSimple.SubstationSimpleInfo?.AssetName,
                    PanelName = assetSimple.PanelSimpleInfo?.AssetName,
                    CircuitName = assetSimple.CircuitSimpleInfo?.AssetName,
                    DeviceName = assetSimple.AssetName,
                    AssetId = assetSimple.AssetId,
                    CreatedBy = UserName,
                    CreatedTime = message.Time,
                    UpdatedBy = UserName,
                    UpdatedTime = DateTime.Now,
                    EventType = eventType,
                    Status = status,

                    Message = message.DisplayText,
                    Severity = severity,
                    AssetStatusStr = eventType == AlarmEventType.UdcAlarm ? newStatusStr : null,
                };
                alarmList.Add(alarmLog);
            }

            oid = resultList.Embedded.Items.OrderByDescending(i => i.Time).First().Oid;
            messageConfig.Oid = oid;

            var tripResults = await apiRef.GetBreakerTrips(asset.ObjectId, count, messageConfig.TripOid);

            if (tripResults != null && tripResults.Embedded.Items.Count > 0)
            {
                foreach (var trip in tripResults.Embedded.Items)
                {
                    if (trip.Oid.Equals(messageConfig.TripOid))
                    {
                        continue;
                    }
                    var severity = GetAlarmSeverity(trip.Severity ?? string.Empty);
                    var status = severity == AlarmSeverity.High ? AlarmLogStatus.New : AlarmLogStatus.None;
                    var eventType = AlarmEventType.BreakerTrip;
                    var settingsDic = new Dictionary<string, string>();
                    var statusDic = new Dictionary<string, string>();
                    var messageBuilder = new StringBuilder(trip.DisplayText);
                    var reasonCategory = trip.Details?.FirstOrDefault(d => "Reason".Equals(d.DisplayCategory) || "原因".Equals(d.DisplayCategory)); //原因
                    if (reasonCategory != null)
                    {
                        messageBuilder.AppendLine();
                        messageBuilder.AppendLine("Reason:");
                        foreach (var i in reasonCategory.Entries)
                        {
                            messageBuilder.Append(i.DisplayName);
                            messageBuilder.Append(':');
                            messageBuilder.AppendLine(i.DisplayValue);
                        }
                    }

                    var settingsCategory = trip.Details?.FirstOrDefault(d => "Settings".Equals(d.DisplayCategory) || "设置".Equals(d.DisplayCategory)); //设置
                    if (settingsCategory != null)
                    {
                        foreach (var i in settingsCategory.Entries)
                        {
                            settingsDic.TryAdd(i.DisplayName, i.DisplayValue);
                        }
                    }

                    var generalCategory = trip.Details?.FirstOrDefault(d => "General".Equals(d.DisplayCategory) || "常规".Equals(d.DisplayCategory)); //设置
                    if (generalCategory != null)
                    {
                        foreach (var i in generalCategory.Entries)
                        {
                            settingsDic.TryAdd(i.DisplayName, i.DisplayValue);
                        }
                    }

                    var parameterCategory = trip.Details?.FirstOrDefault(d => "Parameter".Equals(d.DisplayCategory) || "参数".Equals(d.DisplayCategory)); //设置
                    if (parameterCategory != null)
                    {
                        foreach (var i in parameterCategory.Entries)
                        {
                            settingsDic.TryAdd(i.DisplayName, i.DisplayValue);
                        }
                    }

                    var currentlyCategory = trip.Details?.FirstOrDefault(d => "Current".Equals(d.DisplayCategory) || "电流".Equals(d.DisplayCategory)); // 电流
                    if (currentlyCategory != null)
                    {
                        foreach (var i in currentlyCategory.Entries)
                        {
                            statusDic.TryAdd(i.DisplayName, i.DisplayValue);
                        }
                    }

                    var alarmLog = new AlarmLog
                    {
                        SubstationName = assetSimple.SubstationSimpleInfo?.AssetName,
                        PanelName = assetSimple.PanelSimpleInfo?.AssetName,
                        CircuitName = assetSimple.CircuitSimpleInfo?.AssetName,
                        DeviceName = assetSimple.AssetName,
                        AssetId = assetSimple.AssetId,
                        CreatedBy = UserName,
                        CreatedTime = trip.Time,
                        UpdatedBy = UserName,
                        UpdatedTime = DateTime.Now,
                        EventType = eventType,
                        Status = status,
                        Message = messageBuilder.ToString(),
                        Severity = severity,
                        AssetStatusStr = statusDic.Count > 0 ? JsonConvert.SerializeObject(statusDic) : newStatusStr,
                        AssetSettingsStr = JsonConvert.SerializeObject(settingsDic),
                    };
                    alarmList.Add(alarmLog);
                }
                messageConfig.TripOid = tripResults.Embedded.Items.OrderByDescending(i => i.Time).First().Oid;
            }

            try
            {
                await client.Ado.BeginTranAsync();
                if (newConfig)
                {
                    await client.Insertable(messageConfig).ExecuteCommandAsync();
                    newConfig = false;
                }
                else
                {
                    await client.Updateable(messageConfig).ExecuteCommandAsync();
                }

                await client.Ado.CommitTranAsync();
                refObj.AppendAlarmLog(alarmList.ToArray());
            }
            catch (Exception ex)
            {
                await client.Ado.RollbackTranAsync();
                _logger.LogError(ex, "GetDeviceMessagesJob 错误");
            }
        }

        private async Task SaveUDCLog(IAlarmRef refObj, SiemensCache cache, ISqlSugarClient client, AssetMessagesConfig[] assetMessageConfigs, int count, IUDCApiRef apiRef, AssetInfo asset)
        {
            var messgeConfig = assetMessageConfigs.FirstOrDefault(m => m.AssetId == asset.Id);
            var newConfig = false;
            if (messgeConfig == null)
            {
                messgeConfig = new AssetMessagesConfig
                {
                    AssetId = asset.Id,
                    CreatedBy = UserName,
                    CreatedTime = DateTime.Now,
                    UpdatedBy = UserName,
                    UpdatedTime = DateTime.Now,
                };
                newConfig = true;
            }

            var cacheKey = string.Format("AssetStatus:Currently-{0}", asset.Id);
            var currentStatus = cache.GetHashAllData(cacheKey);
            var dataPointServer = _provider.GetRequiredService<DataPointServer>();
            var dataPointList = await dataPointServer.GetDataPointInfos(asset.AssetLevel, asset.AssetType, asset.AssetModel, asset.Id);

            var alarmServer = _provider.GetRequiredService<AlarmRuleServer>();
            var newStatusStr = alarmServer.GetAssetStatusStr(currentStatus, dataPointList, false);

            if (string.IsNullOrEmpty(asset.ObjectId)) return;
            var assetSimple = cache.Get<AssetSimpleInfo>(string.Format(AssetSimpleInfoCacheKey, asset.Id));
            if (assetSimple == null) return;

            var oid = messgeConfig.Oid;

            var resultList = await apiRef.GetDeviceMessages(asset.ObjectId, oid, count);
            if (resultList == null || resultList.Count == 0) return;

            var alarmList = new List<AlarmLog>();
            foreach (var message in resultList.Embedded.Items)
            {
                if (message.Oid.Equals(messgeConfig.Oid))
                {
                    continue;
                }

                var severity = GetAlarmSeverity(message.Severity ?? string.Empty);
                var status = severity == AlarmSeverity.High ? AlarmLogStatus.New : AlarmLogStatus.None;
                var eventType = severity == AlarmSeverity.High ? AlarmEventType.UdcAlarm : AlarmEventType.DeviceLog;

                var settingsDic = new Dictionary<string, string>();
                var statusDic = new Dictionary<string, string>();

                var messageBuilder = new StringBuilder(message.DisplayText);
                var reasonCategory = message.Details?.FirstOrDefault(d => "Reason".Equals(d.DisplayCategory) || "原因".Equals(d.DisplayCategory)); //原因
                if (reasonCategory != null)
                {
                    messageBuilder.AppendLine();
                    messageBuilder.AppendLine("Reason:");
                    foreach (var i in reasonCategory.Entries)
                    {
                        messageBuilder.Append(i.DisplayName);
                        messageBuilder.Append(':');
                        messageBuilder.AppendLine(i.DisplayValue);
                    }
                }

                var settingsCategory = message.Details?.FirstOrDefault(d => "Settings".Equals(d.DisplayCategory) || "设置".Equals(d.DisplayCategory)); //设置
                if (settingsCategory != null)
                {
                    foreach (var i in settingsCategory.Entries)
                    {
                        settingsDic.TryAdd(i.DisplayName, i.DisplayValue);
                    }
                }

                var generalCategory = message.Details?.FirstOrDefault(d => "General".Equals(d.DisplayCategory) || "常规".Equals(d.DisplayCategory)); //设置
                if (generalCategory != null)
                {
                    foreach (var i in generalCategory.Entries)
                    {
                        settingsDic.TryAdd(i.DisplayName, i.DisplayValue);
                    }
                }

                var parameterCategory = message.Details?.FirstOrDefault(d => "Parameter".Equals(d.DisplayCategory) || "参数".Equals(d.DisplayCategory)); //设置
                if (parameterCategory != null)
                {
                    foreach (var i in parameterCategory.Entries)
                    {
                        settingsDic.TryAdd(i.DisplayName, i.DisplayValue);
                    }
                }

                var currentlyCategory = message.Details?.FirstOrDefault(d => "Current".Equals(d.DisplayCategory) || "电流".Equals(d.DisplayCategory)); // 电流
                if (currentlyCategory != null)
                {
                    foreach (var i in currentlyCategory.Entries)
                    {
                        statusDic.TryAdd(i.DisplayName, i.DisplayValue);
                    }
                }


                var alarmLog = new AlarmLog
                {
                    SubstationName = assetSimple.SubstationSimpleInfo?.AssetName,
                    PanelName = assetSimple.PanelSimpleInfo?.AssetName,
                    CircuitName = assetSimple.CircuitSimpleInfo?.AssetName,
                    DeviceName = assetSimple.AssetName,
                    AssetId = assetSimple.AssetId,
                    CreatedBy = UserName,
                    CreatedTime = message.Time,
                    UpdatedBy = UserName,
                    UpdatedTime = DateTime.Now,
                    EventType = eventType,
                    Status = status,

                    Message = messageBuilder.ToString(),
                    Severity = severity,
                    AssetStatusStr = eventType == AlarmEventType.UdcAlarm ? (statusDic.Count > 0 ? JsonConvert.SerializeObject(statusDic) : newStatusStr) : null,
                    AssetSettingsStr = settingsDic.Count > 0 ? JsonConvert.SerializeObject(settingsDic) : null,
                };
                alarmList.Add(alarmLog);
            }

            oid = resultList.Embedded.Items.OrderByDescending(i => i.Time).First().Oid;
            messgeConfig.Oid = oid;

            try
            {
                await client.Ado.BeginTranAsync();
                if (newConfig)
                {
                    await client.Insertable(messgeConfig).ExecuteCommandAsync();
                    newConfig = false;
                }
                else
                {
                    await client.Updateable(messgeConfig).ExecuteCommandAsync();
                }

                await client.Ado.CommitTranAsync();
                refObj.AppendAlarmLog(alarmList.ToArray());
            }
            catch (Exception ex)
            {
                await client.Ado.RollbackTranAsync();
                _logger.LogError(ex, "GetDeviceMessagesJob 错误");
            }
        }

        private async Task Save3WLUDCLog(IAlarmRef refObj, SiemensCache cache, ISqlSugarClient client, int count, IUDCApiRef apiRef, AssetInfo asset)
        {
            var assetSimple = cache.Get<AssetSimpleInfo>(string.Format(AssetSimpleInfoCacheKey, asset.Id));
            if (assetSimple == null) return;

            var cacheKey = string.Format("AssetStatus:Currently-{0}", asset.Id);
            var currentStatus = cache.GetHashAllData(cacheKey);
            var dataPointServer = _provider.GetRequiredService<DataPointServer>();
            var dataPointList = await dataPointServer.GetDataPointInfos(asset.AssetLevel, asset.AssetType, asset.AssetModel, asset.Id);
            var alarmServer = _provider.GetRequiredService<AlarmRuleServer>();
            var newStatusStr = alarmServer.GetAssetStatusStr(currentStatus, dataPointList, false);

            if(string.IsNullOrEmpty(asset.ObjectId)) return;
            var resultList = await apiRef.GetDeviceMessages(asset.ObjectId, null, count);
            if (resultList == null || resultList.Count == 0) return;

            var logs = await client.Queryable<AlarmLog>()
                .Where(l => l.AssetId == asset.Id && UserName.Equals(l.CreatedBy) && l.Extend != null)
                .OrderBy(l => l.CreatedTime, OrderByType.Desc)
                .Take(resultList.Embedded.Items.Count + 10)
                .ToListAsync();

            var alarmList = new List<AlarmLog>();
            foreach (var message in resultList.Embedded.Items)
            {
                if(logs.Any(l => l.Extend?.Equals(message.Oid) ?? false))
                {
                    continue;
                }

                var severity = GetAlarmSeverity(message.Severity ?? string.Empty);
                var status = severity == AlarmSeverity.High ? AlarmLogStatus.New : AlarmLogStatus.None;
                var eventType = severity == AlarmSeverity.High ? AlarmEventType.UdcAlarm : AlarmEventType.DeviceLog;

                var alarmLog = new AlarmLog
                {
                    SubstationName = assetSimple.SubstationSimpleInfo?.AssetName,
                    PanelName = assetSimple.PanelSimpleInfo?.AssetName,
                    CircuitName = assetSimple.CircuitSimpleInfo?.AssetName,
                    DeviceName = assetSimple.AssetName,
                    AssetId = assetSimple.AssetId,
                    CreatedBy = UserName,
                    CreatedTime = message.Time,
                    UpdatedBy = UserName,
                    UpdatedTime = DateTime.Now,
                    EventType = eventType,
                    Status = status,
                    Message = message.DisplayText,
                    Severity = severity,
                    AssetStatusStr = eventType == AlarmEventType.UdcAlarm ? newStatusStr : null,
                    Extend = message.Oid
                };
                alarmList.Add(alarmLog);
            }

            refObj.AppendAlarmLog(alarmList.ToArray());
        }

        private bool IsBreaker(AssetInfo asset)
        {
            var breakerTypes = new string[] { "ACB", "MCCB", "MCB" };
            return breakerTypes.Contains(asset.AssetType);
        }
        
        private AlarmSeverity GetAlarmSeverity(string severity)
        {
            switch (severity)
            {
                case "information":
                    return AlarmSeverity.Low;
                case "alarm":
                    return AlarmSeverity.High;
                case "warning":
                    return AlarmSeverity.Middle;
                default: return AlarmSeverity.Middle;
            }
        }
    }
}
