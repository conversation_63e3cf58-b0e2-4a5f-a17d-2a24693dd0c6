﻿using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Database.Asset;
using SqlSugar;

namespace Siemens.PanelManager.Common.ExtendFunction
{
    public static class AssetSimpleInfoExtend
    {

        public static void InitByDB(this AssetSimpleInfo simpleInfo, int assetId, ISqlSugarClient client)
        {
            simpleInfo.AssetId = assetId;
            var assetInfo = client.Queryable<AssetInfo>().Where(a => a.Id == assetId).First();
            if (assetInfo != null)
            {
                simpleInfo.AssetLevel = assetInfo.AssetLevel;
                simpleInfo.AssetName = assetInfo.AssetName;
                simpleInfo.AssetModel = assetInfo.AssetModel ?? string.Empty;
                simpleInfo.AssetType = assetInfo.AssetType ?? string.Empty;
                simpleInfo.ObjectId = assetInfo.ObjectId;

                var parents = client.Queryable<AssetRelation>().ToParentList(r => r.ParentId, assetId);
                int? substationId = parents.Where(ar => ar.AssetLevel == AssetLevel.Substation).Select(ar => ar.ChildId).FirstOrDefault();
                int? panelId = parents.Where(ar => ar.AssetLevel == AssetLevel.Panel).Select(ar => ar.ChildId).FirstOrDefault();
                int? circuitId = parents.Where(ar => ar.AssetLevel == AssetLevel.Circuit).Select(ar => ar.ChildId).FirstOrDefault();

                // TODO1
                //if (substationId.HasValue)
                //{
                //    simpleInfo.SubstationAssetId = substationId.Value;
                //}
                //if (panelId.HasValue)
                //{
                //    simpleInfo.PanelAssetId = panelId.Value;
                //}
                //if (circuitId.HasValue)
                //{
                //    simpleInfo.CircuitAssetId = circuitId.Value;
                //}
            }
        }
    }
}
