﻿namespace Siemens.PanelManager.Model.Emun
{
    public static class EmunExtend
    {
        #region Compare
        private const string EqualStr = "==";
        private const string NotEqualStr = "!=";
        private const string GreaterThanStr = ">";
        private const string GreaterThanOrEqualStr = ">=";
        private const string LessThanStr = "<";
        private const string LessThanOrEqualStr = "<=";

        public static string ToCompareString(this Compare compare)
        {
            switch (compare)
            {
                case Compare.Equal:
                    return EqualStr;
                case Compare.NotEqual:
                    return NotEqualStr;
                case Compare.GreaterThanOrEqual:
                    return GreaterThanOrEqualStr;
                case Compare.GreaterThan:
                    return GreaterThanStr;
                case Compare.LessThan:
                    return LessThanStr;
                case Compare.LessThanOrEqual:
                    return LessThanOrEqualStr;
                default:
                    break;
            }

            return string.Empty;
        }

        public static Compare ToCompare(this string str)
        {
            switch (str)
            {
                case EqualStr:
                    return Compare.Equal;
                case NotEqualStr:
                    return Compare.NotEqual;
                case GreaterThanStr:
                    return Compare.GreaterThan;
                case GreaterThanOrEqualStr:
                    return Compare.GreaterThanOrEqual;
                case LessThanStr:
                    return Compare.LessThan;
                case LessThanOrEqualStr:
                    return Compare.LessThanOrEqual;
                default:
                    break;
            }

            return Compare.None;
        }

        public static bool IsCompare(this string str)
        {
            switch (str)
            {
                case EqualStr:
                case NotEqualStr:
                case GreaterThanStr:
                case GreaterThanOrEqualStr:
                case LessThanStr:
                case LessThanOrEqualStr:
                    return true;
                default:
                    return false;
            }
        }
        #endregion

        #region LogicalOperator
        private const string AndStr = "and";
        private const string OrStr = "or";
        private const string NotStr = "!";

        public static string ToLogicalOperatorString(this LogicalOperator opt)
        {
            switch (opt)
            {
                case LogicalOperator.And:
                    return AndStr;
                case LogicalOperator.Or:
                    return OrStr;
                case LogicalOperator.Not:
                    return NotStr;
                default:
                    break;
            }

            return string.Empty;
        }

        public static LogicalOperator ToLogicalOperator(this string str)
        {
            str = str.ToLower();
            switch (str)
            {
                case AndStr:
                    return LogicalOperator.And;
                case OrStr:
                    return LogicalOperator.Or;
                case NotStr:
                    return LogicalOperator.Not;
                default:
                    break;
            }

            return LogicalOperator.None;
        }

        public static bool IsLogicalOperator(this string str)
        {
            str = str.ToLower();
            switch (str)
            {
                case AndStr:
                case OrStr:
                case NotStr:
                    return true;
                default: return false;
            }
        }
        #endregion
    }
}
