﻿using InfluxDB.Client;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Common;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Model.Chart;
using Siemens.PanelManager.Model.Config;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.ElectricityCharge;
using Siemens.PanelManager.Model.Database.Energy;
using Siemens.PanelManager.Server.ElectricityCharge;
using Siemens.PanelManager.Server.Energy;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Server.Asset
{
    public class AcbInUseServer
    {
        private readonly ILogger<AcbInUseServer> _logger;
        private readonly IServiceProvider _provider;
        private readonly SiemensCache _cache;
        private ISqlSugarClient _client;
        private readonly static string TABLE_NAME = "acb_in_use";

        public AcbInUseServer(ILogger<AcbInUseServer> logger, IServiceProvider provider, SqlSugarScope client, SiemensCache cache)
        {
            _logger = logger;
            _provider = provider;
            _client = client;
            _cache = cache;
        }

        /// <summary>
        /// 查询指定断路器的最新acb in use数据
        /// </summary>
        /// <param name="assetId">资产id</param>

        public async Task<AcbUnitModel?> GetAcbInUseDataAsync(int assetId)
        {

            AcbUnitModel? acbUnitModel = null;

            IConfiguration configuration = _provider.GetRequiredService<IConfiguration>();
            var session = configuration.GetSection(InfluxDBConfig.Influxdb);
            InfluxDBConfig influxDbConfig = null;
            if (session != null && session.Exists())
            {
                influxDbConfig = session.Get<InfluxDBConfig>();
            }

            if (influxDbConfig == null)
            {
                _logger.LogError($"{nameof(AcbInUseServer)} {nameof(GetAcbInUseDataAsync)} init: Not found Influxdb config.");
                return null;
            }

            try
            {
                var dashboardConfig = await _client.Queryable<AssetDashboardConfig>().Where(d => d.ConfigName == "ACB_IN_USE_OVERVIEW").FirstAsync();

                var sql = dashboardConfig.Sql;

                //#if DEBUG

                //                string sql0 = sql
                //                         .Replace("[[DBName]]", influxDbConfig.Bucket)
                //                         .Replace("[[TableName]]", TABLE_NAME)
                //                         .Replace("[[AssetId]]", 2370.ToString())
                //                         .Replace("[[StartDate]]", DateTime.Now.Date.AddDays(-30).GetTimestampForSec().ToString())
                //                         .Replace("[[EndDate]]", DateTime.Now.Date.AddDays(1).GetTimestampForSec().ToString());

                //                using var influxClient = new InfluxDBClient("http://122.51.17.74:8086", influxDbConfig.UserName, influxDbConfig.Password);
                //#else
                string sql0 = sql
                         .Replace("[[DBName]]", influxDbConfig.Bucket)
                         .Replace("[[TableName]]", TABLE_NAME)
                         .Replace("[[AssetId]]", assetId.ToString())
                         .Replace("[[StartDate]]", DateTime.Now.Date.AddDays(-30).GetTimestampForSec().ToString())
                         .Replace("[[EndDate]]", DateTime.Now.Date.AddDays(1).GetTimestampForSec().ToString());

                using var influxClient = new InfluxDBClient(influxDbConfig.Url, influxDbConfig.UserName, influxDbConfig.Password);

                //#endif



                var queryApi = influxClient.GetQueryApi();
                var result = await queryApi.QueryAsync(sql0, influxDbConfig.OrgId ?? influxDbConfig.OrgName);

                acbUnitModel = new AcbUnitModel();
                foreach (var table in result)
                {
                    for (var i = 0; i < table.Records.Count; i++)
                    {
                        switch (table.Records[i].GetField())
                        {
                            case "down_pac_asset_id":
                                if (int.TryParse(table.Records[i].GetValue().ToString(), out var tempDown))
                                {
                                    acbUnitModel.DownPacAssetId = tempDown;
                                }
                                break;
                            case "up_pac_asset_id":
                                if (int.TryParse(table.Records[i].GetValue().ToString(), out var tempUp))
                                {
                                    acbUnitModel.UpPacAssetId = tempUp;
                                }
                                break;
                            case "resistance_a":
                                if (double.TryParse(table.Records[i].GetValue().ToString(), out var tempA))
                                {
                                    acbUnitModel.ResistanceA = Math.Round(tempA, 0, MidpointRounding.AwayFromZero);
                                }
                                break;
                            case "resistance_b":
                                if (double.TryParse(table.Records[i].GetValue().ToString(), out var tempB))
                                {
                                    acbUnitModel.ResistanceB = Math.Round(tempB, 0, MidpointRounding.AwayFromZero);
                                }
                                break;
                            case "resistance_c":
                                if (double.TryParse(table.Records[i].GetValue().ToString(), out var tempC))
                                {
                                    acbUnitModel.ResistanceC = Math.Round(tempC, 0, MidpointRounding.AwayFromZero);
                                }
                                break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{nameof(AcbInUseServer)} {nameof(GetAcbInUseDataAsync)} Error.");
            }

            return acbUnitModel;
        }

        /// <summary>
        /// 根据图表类型查询算法算出的acb单元阻值
        /// </summary>
        /// <param name="assetId">资产id</param>
        /// <param name="startTime">需要查询的开始日期</param>
        /// <param name="endTime">需要查询的结束日期</param>
        /// <param name="dateType">
        /// <list type="bullet">
        ///     <item>0: 日</item>
        ///     <item>1: 月</item>
        ///     <item>2: 年</item>
        ///     <item>3: 自定义</item>
        /// </list>
        /// </param>
        /// <param name="chartType">
        /// <list type="bullet">
        ///     <item>0: 平均值</item>
        ///     <item>1: 实时值</item>
        /// </list>
        /// </param>
        /// <returns>IChart <para>see <see cref="IChart" /></para></returns>
        public async Task<IChart?> GetAcbResistanceChart(int assetId, DateTime startTime, DateTime endTime, int dateType, int chartType)
        {
            var lineChart = new LineChartModel();
            DateTime searchStartDate;
            DateTime searchEndDate;

            // 按当日统计数据
            if (dateType == 0)
            {
                searchStartDate = startTime.Date;
                searchEndDate = startTime.Date;
            }
            else if (dateType == 1)
            {
                var currentDate = DateTime.Now;
                searchStartDate = new DateTime(startTime.Year, startTime.Month, 1);

                if (currentDate.Year == startTime.Year && currentDate.Month == startTime.Month)
                {
                    searchEndDate = currentDate.Date;
                }
                else
                {
                    searchEndDate = new DateTime(startTime.Year, startTime.Month, DateTime.DaysInMonth(startTime.Year, startTime.Month));
                }
            }
            else if (dateType == 3)
            {
                searchStartDate = startTime.Date;
                searchEndDate = endTime.Date;
            }
            else
            {
                var currentDate = DateTime.Now;
                searchStartDate = new DateTime(startTime.Year, 1, 1);
                var totalMonthInYear = currentDate.Year == startTime.Year ? currentDate.Month : 12;
                searchEndDate = new DateTime(startTime.Year, totalMonthInYear, DateTime.DaysInMonth(startTime.Year, totalMonthInYear));
            }

            var acbData = await GetAcbResistanceDataAsync(assetId, searchStartDate, searchEndDate);

            if (acbData == null || acbData.Count == 0)
            {
                lineChart.X = [];
                lineChart.Y1 = [];
                return lineChart;
            }

            if (chartType == 0)
            {
                acbData = acbData.GroupBy(g => new DateTime(g.Time.Year, g.Time.Month, g.Time.Day, g.Time.Hour, 0, 0))
                    .Select(g => new AcbInUseChartModel
                    {
                        Time = g.Key,
                        ResistanceA = Math.Round(g.Average(x => x.ResistanceA), 0, MidpointRounding.AwayFromZero),
                        ResistanceB = Math.Round(g.Average(x => x.ResistanceB), 0, MidpointRounding.AwayFromZero),
                        ResistanceC = Math.Round(g.Average(x => x.ResistanceC), 0, MidpointRounding.AwayFromZero)
                    }).ToList();
            }

            var xData = new string[acbData.Count];
            var y1Data = new decimal[acbData.Count];
            var y2Data = new decimal[acbData.Count];
            var y3Data = new decimal[acbData.Count];

            for (int i = 0; i < acbData.Count; i++)
            {
                xData[i] = acbData[i].Time.ToString("yyyy-MM-dd HH:mm");
                y1Data[i] = acbData[i].ResistanceA;
                y2Data[i] = acbData[i].ResistanceB;
                y3Data[i] = acbData[i].ResistanceC;
            }

            lineChart.X = xData;
            lineChart.Y1 = y1Data;
            lineChart.Y2 = y2Data;
            lineChart.Y3 = y3Data;

            return lineChart;
        }

        private async Task<List<AcbInUseChartModel>> GetAcbResistanceDataAsync(int assetId, DateTime startDate, DateTime endDate)
        {
            List<AcbInUseChartModel> chartModels = new();

            IConfiguration configuration = _provider.GetRequiredService<IConfiguration>();
            var session = configuration.GetSection(InfluxDBConfig.Influxdb);
            InfluxDBConfig influxDbConfig = null;
            if (session != null && session.Exists())
            {
                influxDbConfig = session.Get<InfluxDBConfig>();
            }

            if (influxDbConfig == null)
            {
                _logger.LogError($"{nameof(AcbInUseServer)} {nameof(GetAcbResistanceDataAsync)} init: Not found Influxdb config.");
                return chartModels;
            }

            try
            {
                var dashboardConfig = await _client.Queryable<AssetDashboardConfig>().Where(d => d.ConfigName == "ACB_IN_USE_RESISTANCE").FirstAsync();

                var sql = dashboardConfig.Sql;

                //#if DEBUG

                //                string sql0 = sql
                //                         .Replace("[[DBName]]", influxDbConfig.Bucket)
                //                         .Replace("[[TableName]]", TABLE_NAME)
                //                         .Replace("[[AssetId]]", 2370.ToString())
                //                         .Replace("[[StartDate]]", startDate.GetTimestampForSec().ToString())
                //                         .Replace("[[EndDate]]", endDate.AddDays(1).GetTimestampForSec().ToString());

                //                using var influxClient = new InfluxDBClient("http://122.51.17.74:8086", influxDbConfig.UserName, influxDbConfig.Password);
                //#else
                string sql0 = sql
                         .Replace("[[DBName]]", influxDbConfig.Bucket)
                         .Replace("[[TableName]]", TABLE_NAME)
                         .Replace("[[AssetId]]", assetId.ToString())
                         .Replace("[[StartDate]]", startDate.GetTimestampForSec().ToString())
                         .Replace("[[EndDate]]", endDate.AddDays(1).GetTimestampForSec().ToString());

                using var influxClient = new InfluxDBClient(influxDbConfig.Url, influxDbConfig.UserName, influxDbConfig.Password);

                //#endif



                var queryApi = influxClient.GetQueryApi();
                var result = await queryApi.QueryAsync(sql0, influxDbConfig.OrgId ?? influxDbConfig.OrgName);
                var localTimeZone = TimeZoneInfo.Local;

                var tempIndex = 0;
                foreach (var table in result)
                {
                    for (var i = 0; i < table.Records.Count; i++)
                    {
                        AcbInUseChartModel acbInUseChartModel;
                        var item = table.Records[i];

                        if (tempIndex == 0)
                        {
                            acbInUseChartModel = new();
                            acbInUseChartModel.Time = TimeZoneInfo.ConvertTimeFromUtc(item.GetTimeInDateTime().Value, localTimeZone);

                            chartModels.Add(acbInUseChartModel);
                        }
                        else
                        {
                            acbInUseChartModel = chartModels[i];
                        }

                        switch (table.Records[i].GetField())
                        {
                            case "resistance_a":
                                if (decimal.TryParse(table.Records[i].GetValue().ToString(), out var tempA))
                                {
                                    acbInUseChartModel.ResistanceA = Math.Round(tempA, 0, MidpointRounding.AwayFromZero);
                                }
                                break;
                            case "resistance_b":
                                if (decimal.TryParse(table.Records[i].GetValue().ToString(), out var tempB))
                                {
                                    acbInUseChartModel.ResistanceB = Math.Round(tempB, 0, MidpointRounding.AwayFromZero);
                                }
                                break;
                            case "resistance_c":
                                if (decimal.TryParse(table.Records[i].GetValue().ToString(), out var tempC))
                                {
                                    acbInUseChartModel.ResistanceC = Math.Round(tempC, 0, MidpointRounding.AwayFromZero);
                                }
                                break;
                        }
                    }

                    tempIndex++;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{nameof(AcbInUseServer)} {nameof(GetAcbResistanceDataAsync)} Error.");
            }

            return chartModels;
        }
    }
}
