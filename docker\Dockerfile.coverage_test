FROM cr.siemens.com/si-lp-cea-rd-plm-project/edge/panel-manager/panel-manager-standard-product/server/panel-server/coverage_test_base:dev

ENV PATH $PATH:/root/.dotnet/tools

WORKDIR /src
COPY ["Siemens.PanelManager.WebApi/Siemens.PanelManager.WebApi.csproj", "Siemens.PanelManager.WebApi/"]
RUN dotnet restore "Siemens.PanelManager.WebApi/Siemens.PanelManager.WebApi.csproj"
COPY . .
WORKDIR "/src/Siemens.PanelManager.WebApi"
RUN dotnet build "Siemens.PanelManager.WebApi.csproj" -c Release -o /app/build
