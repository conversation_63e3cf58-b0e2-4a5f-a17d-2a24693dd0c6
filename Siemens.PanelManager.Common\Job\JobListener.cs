﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Quartz;
using Siemens.PanelManager.Model.Database.System;
using SqlSugar;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.Common.Job
{
    public class JobListener : IJobListener
    {
        private ILogger<JobListener> _logger;
        private readonly IServiceProvider _provider;
        //private ISqlSugarClient _client;
        public JobListener(ILogger<JobListener> logger, IServiceProvider provider) 
        {
            _logger = logger;
            _provider = provider;
            //_client = client;
        }
        public string Name => "Panel Manager Job Listener";

        public async Task JobExecutionVetoed(IJobExecutionContext context, CancellationToken cancellationToken = default)
        {
            using (var client = _provider.GetRequiredService<ISqlSugarClient>())
            {
                _logger.LogError($"Job: {context.JobDetail.Key} failed");

                await client.Insertable<JobRunningLog>(new JobRunningLog()
                {
                    JobCode = context.JobDetail.Key.Name,
                    JobName = context.JobDetail.Key.Group,
                    JobStatus = 99,
                    LogTime = DateTime.Now,
                }).ExecuteCommandAsync();
            }
        }

        public async Task JobToBeExecuted(IJobExecutionContext context, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug($"Job: {context.JobDetail.Key.Name} begin");
            if (context.JobDetail.Key.Group != null && !JobStaticManager.NoLogServer.Equals(context.JobDetail.Key.Group))
            {
                var match = Regex.Match(context.JobDetail.Key.Name, "^([\\w|-]+)-([\\w]+)$");
                if (match.Success)
                {
                    var code = match.Groups[1].Value;
                    var name = match.Groups[2].Value;
                    using (var client = _provider.GetRequiredService<ISqlSugarClient>())
                    {
                        await client.Insertable(new JobRunningLog()
                        {
                            JobCode = code,
                            JobName = name,
                            JobStatus = 0,
                            LogTime = DateTime.Now,
                        }).ExecuteCommandAsync();
                    }
                }
            }
        }

        public async Task JobWasExecuted(IJobExecutionContext context, JobExecutionException? jobException, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug($"Job: {context.JobDetail.Key.Name} finish");
            if (context.JobDetail.Key.Group != null && !JobStaticManager.NoLogServer.Equals(context.JobDetail.Key.Group))
            {
                var match = Regex.Match(context.JobDetail.Key.Name, "^([\\w|-]+)-([\\w]+)$");
                if (match.Success)
                {
                    var code = match.Groups[1].Value;
                    var name = match.Groups[2].Value;
                    using (var client = _provider.GetRequiredService<ISqlSugarClient>())
                    {
                        await client.Insertable(new JobRunningLog()
                        {
                            JobCode = code,
                            JobName = name,
                            JobStatus = 10,
                            LogTime = DateTime.Now,
                        }).ExecuteCommandAsync();
                    }
                }
            }
        }
    }
}
