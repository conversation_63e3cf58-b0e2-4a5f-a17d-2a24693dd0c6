﻿using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Siemens.PanelManager.Model.Database.ElectricityCharge;
using SqlSugar;
using Siemens.PanelManager.Model.ElectricityCharge;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TouchSocket.Sockets;

namespace Siemens.PanelManager.Server.ElectricityCharge
{
    public class ElectricityChargeServer
    {
        private IServiceProvider _provider;
        private ISqlSugarClient _client;
        public ElectricityChargeServer(SqlSugarScope client, IServiceProvider provider)
        {
            _client = client;
            _provider = provider;
        }
        public async Task<ElectricityConfig> GetElectricityChargeList(DateTime dt, ElectricityConfig[] electricitiesList, ElectricityConfigHistory[] configHistories)
        {
            int month = dt.Month;
            int day = dt.Day;
            int hour = dt.Hour;

            ElectricityConfig config = new ElectricityConfig();
            var electricityList = electricitiesList.Where(a => a.effectiveTimeStart <= dt && a.effectiveTimeEnd > dt).ToArray();

            if (electricityList == null || electricityList.Length == 0)
            {
                configHistories = configHistories.Where(a => a.effectiveTimeStart <= dt && a.effectiveTimeEnd > dt).ToArray();

                electricityList = configHistories.Select(a => new ElectricityConfig
                {
                    bsid = a.bsid,
                    rateDesc = a.rateDesc,
                    rateName = a.rateName,
                    electrovalence = a.electrovalence,
                    electrovalenceType = a.electrovalenceType,
                    timeRange = a.timeRange,
                    seasonRange = a.seasonRange,
                    seasonType = a.seasonType,
                    step = a.step,
                    stepTarif = a.stepTarif,
                    effectiveTimeEnd = a.effectiveTimeEnd,
                    effectiveTimeStart = a.effectiveTimeStart,
                }).ToArray();
            }
            //如果当前和历史都没有当前时效的数据，则用当前数据
            if(electricityList == null || electricityList.Length == 0)
            {
                electricityList = electricitiesList;
            }
           
            foreach (var electricity in electricityList)
            {
                var monthRange = JsonConvert.DeserializeObject<List<seasonRange>>(electricity.seasonRange);
                var timeRange = JsonConvert.DeserializeObject<List<timeRange>>(electricity.timeRange);

                foreach (seasonRange item in monthRange)
                {

                    if (month >= int.Parse(item.startMonth) && month <= int.Parse(item.endMonth))
                    {
                        foreach (timeRange t in timeRange)
                        {
                            int timeRangeHourEnd = 0;
                            if (t.endTime == "24:00")
                            {
                                timeRangeHourEnd = 24;
                            }
                            else
                            {
                                TimeSpan endTime = TimeSpan.Parse(t.endTime);
                                timeRangeHourEnd = endTime.Hours;
                            }
                            TimeSpan startTime = TimeSpan.Parse(t.startTime);
                            if (hour >= startTime.Hours && hour < timeRangeHourEnd)
                            {
                                if (electricity.seasonType == "Holidays" && day >= int.Parse(item.startDay) && day <= int.Parse(item.endDay))
                                {
                                    return electricity;

                                }
                                if (electricity.seasonType != "Holidays")
                                {
                                    config = electricity;
                                }

                            }
                        }

                    }

                }
            }
            return config;
        }
        public async Task<decimal> GetElectricityPrice(DateTime time, decimal totalElectricity, ElectricityConfig[] electricitisList, ElectricityConfigHistory[] configHistories)
        {
            configHistories = configHistories.Where(a => a.effectiveTimeStart <= time && a.effectiveTimeEnd > time).ToArray();

            var historyList = configHistories.Select(a => new ElectricityConfig
            {
                bsid = a.bsid,
                rateDesc = a.rateDesc,
                rateName = a.rateName,
                electrovalence = a.electrovalence,
                electrovalenceType = a.electrovalenceType,
                timeRange = a.timeRange,
                seasonRange = a.seasonRange,
                seasonType = a.seasonType,
                step = a.step,
                stepTarif = a.stepTarif,
                effectiveTimeEnd = a.effectiveTimeEnd,
                effectiveTimeStart = a.effectiveTimeStart,
            }).ToArray();
            var electricityList = electricitisList.Where(a => a.effectiveTimeStart <= time && a.effectiveTimeEnd > time).ToArray();
            if (electricityList == null || electricityList.Length == 0)
            {
                electricityList = historyList;
            }
            int month = time.Month;
            int day = time.Day;
            int hour = time.Hour;
            int billingId = 1;
            if (electricityList != null && electricityList.Length > 0)
            {
                billingId = electricityList.First().bsid;
            }

            switch (billingId)
            {
                case 3:
                    foreach (var electricity in electricityList)
                    {
                        var tarif = JsonConvert.DeserializeObject<stepTarif>(electricity.stepTarif);
                        decimal to = 0;
                        if (string.IsNullOrEmpty(tarif.to))
                        {
                            to = -1;
                        }
                        else
                        {
                            to = decimal.Parse(tarif.to);
                        }
                        decimal from = decimal.Parse(tarif.from);
                        // from or to's Unit is 10000KWH
                        decimal electricityConsumption = totalElectricity / 10000;
                        if ((from <= electricityConsumption && electricityConsumption < to) || (electricityConsumption >= from && to == -1))
                        {
                            return electricity.electrovalence;

                        }
                    }
                    break;
                case 1:
                    decimal price = 0;
                    foreach (var electricity in electricityList)
                    {
                        var monthRange = JsonConvert.DeserializeObject<List<seasonRange>>(electricity.seasonRange);
                        var timeRange = JsonConvert.DeserializeObject<List<timeRange>>(electricity.timeRange);

                        foreach (seasonRange item in monthRange)
                        {

                            if (month >= int.Parse(item.startMonth) && month <= int.Parse(item.endMonth))
                            {
                                foreach (timeRange t in timeRange)
                                {
                                    int timeRangeHourEnd = 0;
                                    if (t.endTime == "24:00")
                                    {
                                        timeRangeHourEnd = 24;
                                    }
                                    else
                                    {
                                        TimeSpan endTime = TimeSpan.Parse(t.endTime);
                                        timeRangeHourEnd = endTime.Hours;
                                    }
                                    TimeSpan startTime = TimeSpan.Parse(t.startTime);
                                    if (hour >= startTime.Hours && hour < timeRangeHourEnd)
                                    {
                                        if (electricity.seasonType == "Holidays" && day >= int.Parse(item.startDay) && day <= int.Parse(item.endDay))
                                        {
                                            return electricity.electrovalence;

                                        }
                                        if (electricity.seasonType != "Holidays")
                                        {
                                            price = electricity.electrovalence;
                                        }

                                    }
                                }

                            }
                        }
                    }
                    return price;
                case 2:
                    decimal price1 = 0;
                    foreach (var electricity in electricityList)
                    {
                        var tarif = JsonConvert.DeserializeObject<stepTarif>(electricity.stepTarif);
                        decimal to = 0;
                        if (string.IsNullOrEmpty(tarif.to))
                        {
                            to = -1;
                        }
                        else
                        {
                            to = decimal.Parse(tarif.to);
                        }
                        decimal from = decimal.Parse(tarif.from);
                        // from or to's Unit is 10000KWH
                        decimal electricityConsumption = totalElectricity / 10000;
                        if ((from <= electricityConsumption && electricityConsumption < to) || (electricityConsumption > from && to == -1))
                        {
                            //then use timerange to datemine which scheme to use 
                            var monthRange = JsonConvert.DeserializeObject<List<seasonRange>>(electricity.seasonRange);
                            var timeRange = JsonConvert.DeserializeObject<List<timeRange>>(electricity.timeRange);

                            foreach (seasonRange item in monthRange)
                            {

                                if (month >= int.Parse(item.startMonth) && month <= int.Parse(item.endMonth))
                                {
                                    foreach (timeRange t in timeRange)
                                    {
                                        int timeRangeHourEnd = 0;
                                        if (t.endTime == "24:00")
                                        {
                                            timeRangeHourEnd = 24;
                                        }
                                        else
                                        {
                                            TimeSpan endTime = TimeSpan.Parse(t.endTime);
                                            timeRangeHourEnd = endTime.Hours;
                                        }
                                        TimeSpan startTime = TimeSpan.Parse(t.startTime);
                                        if (hour > startTime.Hours && hour <= timeRangeHourEnd)
                                        {
                                            if (electricity.seasonType == "Holidays" && day >= int.Parse(item.startDay) && day <= int.Parse(item.endDay))
                                            {
                                                return electricity.electrovalence;
                                            }
                                            price1 = electricity.electrovalence;
                                        }
                                    }

                                }

                            }

                        }
                    }
                    return price1;
                default: return 0;
            }
            return 0;
        }
    }
}
