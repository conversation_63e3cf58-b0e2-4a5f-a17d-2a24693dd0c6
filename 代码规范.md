# C# 和数据库 编码规则

## C# 命名规则
1.	全局变量使用_开头 e.g. **_id**
2.	属性，方法，类使用大驼峰
3.	接口类必须“**I**”字母开头
4.	全局变量禁止对使用 **public**
5.	局部变量使用小驼峰

## 数据库命名规则

1.	全部使用小写字母
2.	单词与单词之间使用 ( **_** ) 分割
3.	数据表需要有 **created_by created_time updated_by updated_time**
4.	表的第一个单词用来分模块 e.g. **user_info, user_history**

## 布局规则

1.	花括号不能再同一行
2.	语句不能共享一行
3.	不允许有多行空白行紧挨着
4.	邻近的元素之间要有一个空白行

## 可维护规则

1.	必须定义访问修饰符
2.	变量必须定义为私有的
3.	一个CS文件里只定义一个类
4.	一个CS文件只包含一个命名空间

## 可读性规则

1.	linq语句要么同一行，要么关键字分布在多行，关键字对齐
2.	确保参数列表与开始括号同行，或者另起一行 参数整体要在同一行
3.	参数与参数之间不能隔空白行
4.	所有参数在同一行或者如下示例：

~~~ C#
    public string JoinName(
        string first, 
        string last)
~~~

5.	注释必须节点内部必须有文本
6.	使用内嵌的类型别名
7.	使用简写版的可空类型 如”**int?**”



## 拼写规则
1.	操作符与元素之间加空格
2.	逗号分号前不加空格
3.	操作必须要在一个空格之后
4.	同一行不允许出现多个空格
