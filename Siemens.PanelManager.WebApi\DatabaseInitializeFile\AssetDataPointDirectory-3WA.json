[{"AssetLevel": 50, "AssetModel": "3WA", "Name": "StatusValues", "ParentName": "", "LanguageKey": "StatusValues", "Sort": 1}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "Status", "ParentName": "StatusValues", "LanguageKey": "Status", "Sort": 2}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "Das_EnableFrom", "ParentName": "Status", "LanguageKey": "Das_EnableFrom", "Sort": 3}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ParameterSetBActivatedBy", "ParentName": "Status", "LanguageKey": "ParameterSetBActivatedBy", "Sort": 4}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ETUStatus", "ParentName": "Status", "LanguageKey": "ETUStatus", "Sort": 5}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "Warnings", "ParentName": "Status", "LanguageKey": "Warnings", "Sort": 6}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "Alarms", "ParentName": "Status", "LanguageKey": "Alarms", "Sort": 7}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ETUErrors", "ParentName": "Status", "LanguageKey": "ETUErrors", "Sort": 8}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "System", "ParentName": "StatusValues", "LanguageKey": "System", "Sort": 9}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "RotarySwitchPositions", "ParentName": "System", "LanguageKey": "RotarySwitchPositions", "Sort": 10}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "AvailableModules", "ParentName": "System", "LanguageKey": "AvailableModules", "Sort": 11}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "Maintenance", "ParentName": "StatusValues", "LanguageKey": "Maintenance", "Sort": 12}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "Statistics", "ParentName": "StatusValues", "LanguageKey": "Statistics", "Sort": 13}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "Limits", "ParentName": "StatusValues", "LanguageKey": "Limits", "Sort": 14}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "StateDigitalInputs_outputs", "ParentName": "StatusValues", "LanguageKey": "StateDigitalInputs_outputs", "Sort": 15}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "IOM230_1", "ParentName": "StateDigitalInputs_outputs", "LanguageKey": "IOM230_1", "Sort": 16}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "IOM230_2", "ParentName": "StateDigitalInputs_outputs", "LanguageKey": "IOM230_2", "Sort": 17}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "IOM230_3", "ParentName": "StateDigitalInputs_outputs", "LanguageKey": "IOM230_3", "Sort": 18}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "IOM230_4", "ParentName": "StateDigitalInputs_outputs", "LanguageKey": "IOM230_4", "Sort": 19}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "IOM230_5", "ParentName": "StateDigitalInputs_outputs", "LanguageKey": "IOM230_5", "Sort": 20}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "IOM350_1", "ParentName": "StateDigitalInputs_outputs", "LanguageKey": "IOM350_1", "Sort": 21}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "IOM350_2", "ParentName": "StateDigitalInputs_outputs", "LanguageKey": "IOM350_2", "Sort": 22}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "IOM350_3", "ParentName": "StateDigitalInputs_outputs", "LanguageKey": "IOM350_3", "Sort": 23}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "IOM350_4", "ParentName": "StateDigitalInputs_outputs", "LanguageKey": "IOM350_4", "Sort": 24}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "IOM350_5", "ParentName": "StateDigitalInputs_outputs", "LanguageKey": "IOM350_5", "Sort": 25}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "BSSModule", "ParentName": "StatusValues", "LanguageKey": "BSSModule", "Sort": 26}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ZSIModule", "ParentName": "StatusValues", "LanguageKey": "ZSIModule", "Sort": 27}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "CommunicationModules", "ParentName": "StatusValues", "LanguageKey": "CommunicationModules", "Sort": 28}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "Test", "ParentName": "StatusValues", "LanguageKey": "Test", "Sort": 29}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "VoltageL-N", "ParentName": "", "LanguageKey": "VoltageL-N", "Sort": 30}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "VoltageL-N_InstantaneousMeasuredValues", "ParentName": "VoltageL-N", "LanguageKey": "InstantaneousMeasuredValues", "Sort": 31}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "VoltageL-N_GreatestMeasuredValues", "ParentName": "VoltageL-N", "LanguageKey": "GreatestMeasuredValues", "Sort": 32}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "VoltageL-N_LowestMeasuredValues", "ParentName": "VoltageL-N", "LanguageKey": "LowestMeasuredValues", "Sort": 33}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "VoltageL-L", "ParentName": "", "LanguageKey": "VoltageL-L", "Sort": 34}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "VoltageL-L_InstantaneousMeasuredValues", "ParentName": "VoltageL-L", "LanguageKey": "InstantaneousMeasuredValues", "Sort": 35}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "VoltageL-L_GreatestMeasuredValues", "ParentName": "VoltageL-L", "LanguageKey": "GreatestMeasuredValues", "Sort": 36}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "VoltageL-L_LowestMeasuredValues", "ParentName": "VoltageL-L", "LanguageKey": "LowestMeasuredValues", "Sort": 37}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "Current", "ParentName": "", "LanguageKey": "Current", "Sort": 38}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "Current_InstantaneousMeasuredValues", "ParentName": "Current", "LanguageKey": "InstantaneousMeasuredValues", "Sort": 39}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "Current_GreatestMeasuredValues", "ParentName": "Current", "LanguageKey": "GreatestMeasuredValues", "Sort": 40}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "Current_LowestMeasuredValues", "ParentName": "Current", "LanguageKey": "LowestMeasuredValues", "Sort": 41}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "Power", "ParentName": "", "LanguageKey": "Power", "Sort": 42}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ActivePower", "ParentName": "Power", "LanguageKey": "ActivePower", "Sort": 43}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ActivePower_ActualInstantaneousMeasurementValues", "ParentName": "ActivePower", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 44}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ActivePower_GreatestMeasuredValues", "ParentName": "ActivePower", "LanguageKey": "GreatestMeasuredValues", "Sort": 45}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ActivePower_LowestMeasuredValues", "ParentName": "ActivePower", "LanguageKey": "LowestMeasuredValues", "Sort": 46}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ReactivePower", "ParentName": "Power", "LanguageKey": "ReactivePower", "Sort": 47}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "MeasuringMethodVAR1", "ParentName": "ReactivePower", "LanguageKey": "MeasuringMethodVAR1", "Sort": 48}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ReactivePower_ActualInstantaneousMeasurementValues", "ParentName": "MeasuringMethodVAR1", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 49}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ReactivePower_GreatestMeasuredValues", "ParentName": "MeasuringMethodVAR1", "LanguageKey": "GreatestMeasuredValues", "Sort": 50}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ReactivePower_LowestMeasuredValues", "ParentName": "MeasuringMethodVAR1", "LanguageKey": "LowestMeasuredValues", "Sort": 51}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "MeasuringMethodVARtot", "ParentName": "ReactivePower", "LanguageKey": "MeasuringMethodVARtot", "Sort": 52}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "MeasuringMethodVARtot_ActualInstantaneousMeasurementValues", "ParentName": "MeasuringMethodVARtot", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 53}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "MeasuringMethodVARtot_GreatestMeasuredValues", "ParentName": "MeasuringMethodVARtot", "LanguageKey": "GreatestMeasuredValues", "Sort": 54}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "MeasuringMethodVARtot_LowestMeasuredValues", "ParentName": "MeasuringMethodVARtot", "LanguageKey": "LowestMeasuredValues", "Sort": 55}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ParentName": "Power", "LanguageKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Sort": 56}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ApparentPower_ActualInstantaneousMeasurementValues", "ParentName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 57}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ApparentPower_GreatestMeasuredValues", "ParentName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LanguageKey": "GreatestMeasuredValues", "Sort": 58}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ApparentPower_LowestMeasuredValues", "ParentName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LanguageKey": "LowestMeasuredValues", "Sort": 59}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "PowerFactor", "ParentName": "Power", "LanguageKey": "PowerFactor", "Sort": 60}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "PowerFactor_ActualInstantaneousMeasurementValues", "ParentName": "PowerFactor", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 61}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "PowerFactor_GreatestMeasuredValues", "ParentName": "PowerFactor", "LanguageKey": "GreatestMeasuredValues", "Sort": 62}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "PowerFactor_LowestMeasuredValues", "ParentName": "PowerFactor", "LanguageKey": "LowestMeasuredValues", "Sort": 63}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "Energy", "ParentName": "", "LanguageKey": "Energy", "Sort": 64}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ActiveEnergy", "ParentName": "Energy", "LanguageKey": "ActiveEnergy", "Sort": 65}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ActiveEnergy_Import", "ParentName": "ActiveEnergy", "LanguageKey": "Import", "Sort": 65}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ActiveEnergy_Export", "ParentName": "ActiveEnergy", "LanguageKey": "Export", "Sort": 65}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ReactiveEnergy", "ParentName": "Energy", "LanguageKey": "ReactiveEnergy", "Sort": 66}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ReactiveEnergy_Import", "ParentName": "ReactiveEnergy", "LanguageKey": "Import", "Sort": 66}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ReactiveEnergy_Export", "ParentName": "ReactiveEnergy", "LanguageKey": "Export", "Sort": 66}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ApparentEnergy", "ParentName": "Energy", "LanguageKey": "ApparentEnergy", "Sort": 67}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "FrequencyValues", "ParentName": "", "LanguageKey": "FrequencyValues", "Sort": 68}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "Cos", "ParentName": "", "LanguageKey": "Cos", "Sort": 69}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "Cos_ActualInstantaneousMeasurementValues", "ParentName": "Cos", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 70}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "Cos_GreatestMeasuredValues", "ParentName": "Cos", "LanguageKey": "GreatestMeasuredValues", "Sort": 71}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "Cos_LowestMeasuredValues", "ParentName": "Cos", "LanguageKey": "LowestMeasuredValues", "Sort": 72}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ThreePhaseSystem", "ParentName": "", "LanguageKey": "ThreePhaseSystem", "Sort": 73}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ThreePhaseSystem_Unbalance", "ParentName": "ThreePhaseSystem", "LanguageKey": "Unbalance", "Sort": 74}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ThreePhaseSystem_FormFactor", "ParentName": "ThreePhaseSystem", "LanguageKey": "FormFactor", "Sort": 75}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ThreePhaseSystem_ActualInstantaneousMeasurementValues", "ParentName": "FormFactor", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 76}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ThreePhaseSystem_GreatestMeasuredValues", "ParentName": "FormFactor", "LanguageKey": "GreatestMeasuredValues", "Sort": 77}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "ThreePhaseSystem_LowestMeasuredValues", "ParentName": "FormFactor", "LanguageKey": "LowestMeasuredValues", "Sort": 78}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "CrestFactor", "ParentName": "ThreePhaseSystem", "LanguageKey": "CrestFactor", "Sort": 79}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "CrestFactor_ActualInstantaneousMeasurementValues", "ParentName": "CrestFactor", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 80}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "CrestFactor_GreatestMeasuredValues", "ParentName": "CrestFactor", "LanguageKey": "GreatestMeasuredValues", "Sort": 81}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "CrestFactor_LowestMeasuredValues", "ParentName": "CrestFactor", "LanguageKey": "LowestMeasuredValues", "Sort": 82}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicDistortion", "ParentName": "", "LanguageKey": "HarmonicDistortion", "Sort": 83}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "THDCurrent", "ParentName": "HarmonicDistortion", "LanguageKey": "THDCurrent", "Sort": 84}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "THDCurrent_ActualInstantaneousMeasurementValues", "ParentName": "THDCurrent", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 85}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "THDCurrent_GreatestMeasuredValues", "ParentName": "THDCurrent", "LanguageKey": "GreatestMeasuredValues", "Sort": 86}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "THDCurrent_LowestMeasuredValues", "ParentName": "THDCurrent", "LanguageKey": "LowestMeasuredValues", "Sort": 87}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "THDVoltage", "ParentName": "HarmonicDistortion", "LanguageKey": "THDVoltage", "Sort": 88}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "THDVoltage_ActualInstantaneousMeasurementValues", "ParentName": "THDVoltage", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 89}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "THDVoltage_GreatestMeasuredValues", "ParentName": "THDVoltage", "LanguageKey": "GreatestMeasuredValues", "Sort": 90}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "THDVoltage_LowestMeasuredValues", "ParentName": "THDVoltage", "LanguageKey": "LowestMeasuredValues", "Sort": 91}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "Harmonic", "ParentName": "", "LanguageKey": "Harmonic", "Sort": 92}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent", "ParentName": "Harmonic", "LanguageKey": "HarmonicCurrent", "Sort": 93}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "DirectHarmonicCurrent", "ParentName": "HarmonicCurrent", "LanguageKey": "DirectHarmonicCurrent", "Sort": 94}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent1", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent1", "Sort": 95}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent2", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent2", "Sort": 96}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent3", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent3", "Sort": 97}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent4", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent4", "Sort": 98}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent5", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent5", "Sort": 99}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent6", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent6", "Sort": 100}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent7", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent7", "Sort": 101}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent8", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent8", "Sort": 102}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent9", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent9", "Sort": 103}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent10", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent10", "Sort": 104}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent11", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent11", "Sort": 105}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent12", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent12", "Sort": 106}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent13", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent13", "Sort": 107}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent14", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent14", "Sort": 108}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent15", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent15", "Sort": 109}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent16", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent16", "Sort": 110}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent17", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent17", "Sort": 111}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent18", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent18", "Sort": 112}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent19", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent19", "Sort": 113}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent20", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent20", "Sort": 114}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent21", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent21", "Sort": 115}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent22", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent22", "Sort": 116}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent23", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent23", "Sort": 117}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent24", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent24", "Sort": 118}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent25", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent25", "Sort": 119}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent26", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent26", "Sort": 120}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent27", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent27", "Sort": 121}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent28", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent28", "Sort": 122}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent29", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent29", "Sort": 123}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent30", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent30", "Sort": 124}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicCurrent31", "ParentName": "HarmonicCurrent", "LanguageKey": "HarmonicCurrent31", "Sort": 125}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltageL-N", "ParentName": "Harmonic", "LanguageKey": "HarmonicVoltageL-N", "Sort": 126}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "DirectHarmonicVoltage", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "DirectHarmonicVoltage", "Sort": 127}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage1", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage1", "Sort": 128}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage2", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage2", "Sort": 129}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage3", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage3", "Sort": 130}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage4", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage4", "Sort": 131}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage5", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage5", "Sort": 132}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage6", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage6", "Sort": 133}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage7", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage7", "Sort": 134}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage8", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage8", "Sort": 135}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage9", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage9", "Sort": 136}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage10", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage10", "Sort": 137}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage11", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage11", "Sort": 138}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage12", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage12", "Sort": 139}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage13", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage13", "Sort": 140}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage14", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage14", "Sort": 141}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage15", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage15", "Sort": 142}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage16", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage16", "Sort": 143}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage17", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage17", "Sort": 144}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage18", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage18", "Sort": 145}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage19", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage19", "Sort": 146}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage20", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage20", "Sort": 147}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage21", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage21", "Sort": 148}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage22", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage22", "Sort": 149}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage23", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage23", "Sort": 150}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage24", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage24", "Sort": 151}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage25", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage25", "Sort": 152}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage26", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage26", "Sort": 153}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage27", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage27", "Sort": 154}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage28", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage28", "Sort": 155}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage29", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage29", "Sort": 156}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage30", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage30", "Sort": 157}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "HarmonicVoltage31", "ParentName": "HarmonicVoltageL-N", "LanguageKey": "HarmonicVoltage31", "Sort": 158}, {"AssetLevel": 50, "AssetModel": "3WA", "Name": "Temperatures", "ParentName": "", "LanguageKey": "Temperatures", "Sort": 159}]