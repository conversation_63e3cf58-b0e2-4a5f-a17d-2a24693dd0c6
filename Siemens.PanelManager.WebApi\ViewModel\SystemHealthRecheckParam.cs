﻿namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class SystemHealthRecheckParam
    {
        public string? SearchTime { get; set; }
        public SystemHealthCustomItem? FamilyDefects { get; set; }
        public SystemHealthCustomItem? Enviroment { get; set; }
        public SystemHealthCustomItem? Meter { get; set; }
        public SystemHealthCustomItem? Cabinet { get; set; }
        public SystemHealthCustomItem? Lamp { get; set; }
        public SystemHealthCustomItem? Isolated { get; set; }
        public SystemHealthCustomItem? Protection { get; set; }
    }

    public class SystemHealthCustomItem
    {
        public int Value { get; set; }
        public int Min { get; set; }
        public int Max { get; set; }
        public int Weight { get; set; }
    }
}
