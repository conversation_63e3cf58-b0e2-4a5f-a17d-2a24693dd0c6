﻿using System.Text.RegularExpressions;

namespace Siemens.PanelManager.Common.General
{
    public static class StringFunction
    {
        public static bool MustCharAndNotOnlyNumberOrSymbol(this string value, int max, int min = 0)
        {
            return string.IsNullOrEmpty(value)
                || !Regex.IsMatch(value, "[\\u4e00-\\u9fa5|a-zA-Z]+")
                || value.Length >= max
                || value.Length < min;
        }

        public static bool MustCharAndNotOnlySymbol(this string value, int max, int min = 0)
        {
            return string.IsNullOrEmpty(value)
                || !Regex.IsMatch(value, "[\\u4e00-\\u9fa5|\\w]+")
                || value.Length >= max
                || value.Length < min;
        }

        public static bool CanNullAndNotOnlyNumberOrSymbol(this string? value, int max, int min = 0)
        {
            return !string.IsNullOrEmpty(value)
                && (!Regex.IsMatch(value, "[\\u4e00-\\u9fa5|a-zA-Z]+")
                || value.Length >= max
                || value.Length < min);
        }

        public static bool CanNullAndNotOnlySymbol(this string? value, int max, int min = 0)
        {
            return !string.IsNullOrEmpty(value)
                && (!Regex.IsMatch(value, "[\\u4e00-\\u9fa5|\\w]+")
                || value.Length >= max
                || value.Length < min);
        }


        /// <summary>
        /// 判断是否二进制格式字符串
        /// </summary>
        /// <param name="str">字符串</param>
        /// <returns>true 是  false 不是</returns>
        public static bool IsBinary(string str)
        {
            if (str == "")
                return false;
            const string PATTERN = @"[0-1]+$";
            bool sign = false;
            for (int i = 0; i < str.Length; i++)
            {
                sign = Regex.IsMatch(str[i].ToString(), PATTERN);
                if (!sign)
                {
                    return sign;
                }
            }
            return sign;
        }

        /// <summary>
        /// 10进制转成16位的2进制
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static string TenToBit(int value)
        {
            string bitStr = Convert.ToString(value, 2);

            if (value > 65535)
            {
                int bitLength = bitStr.Length;

                bitStr = bitStr.Substring(bitLength - 16, 16);
            }
            else
            {
                bitStr = bitStr.PadLeft(16, '0');
            }

            return bitStr;
        }

        /// <summary>
        /// 字符串调整顺序
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static List<string?> BitToList(string? str)
        {
            var bitList = new List<string?>();

            if (!string.IsNullOrEmpty(str))
            {
                for (int i = str.Length - 1; i >= 0; i--)
                {
                    bitList.Add(str.Substring(i, 1));
                }
            }

            return bitList;
        }
    }
}
