﻿using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Model.Database.System;
using System.Resources;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.WebApi.StaticContent
{
    public class MessageContextFactory : IMessageContextFactory
    {
        public string DefaultLanguage { get; private set; } = "zh-cn";
        private readonly ResourceManager? _defaultResourceManager;
        private readonly Dictionary<string, ResourceManager> _messageList;
        private readonly ILogger<MessageContextFactory> _logger;
        public MessageContextFactory(
            ILogger<MessageContextFactory> logger,
            IConfiguration config)
        {
            var defaultLanguage = config.GetValue<string>("DefaultLanguage");
            if (!string.IsNullOrEmpty(defaultLanguage))
            {
                DefaultLanguage = defaultLanguage;
            }
            var messageList = new Dictionary<string, ResourceManager>();
            _logger = logger;
            var languageDirectory = Path.Combine(Directory.GetCurrentDirectory(), "Language");
            if (Directory.Exists(languageDirectory))
            {
                var files = Directory.GetFiles(languageDirectory, "*.resources");
                foreach (var file in files)
                {
                    var m = Regex.Match(file, "[\\\\|/]{1}(Message\\.([\\w|\\-|_]+))\\.resources$");
                    _logger.LogDebug($"资源文件 {file} format 结果 {m.Success}");
                    if (m.Success)
                    {
                        try
                        {
                            var rm = ResourceManager.CreateFileBasedResourceManager(m.Groups[1].Value, languageDirectory, null);
                            messageList.TryAdd(m.Groups[2].Value.ToLower(), rm);
                            if (DefaultLanguage.Equals(m.Groups[2].Value, StringComparison.OrdinalIgnoreCase))
                            {
                                _defaultResourceManager = rm;
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"{file} 语言包加载失败");
                        }
                    }
                }
            }
            _messageList = messageList;
        }

        public IMessageContext GetMessageContext(string language)
        {
            language = language.ToLower();
            if (_defaultResourceManager != null)
            {
                return new MessageContext(_messageList.GetValueOrDefault(language, _defaultResourceManager), _logger);
            }

            if (_messageList.TryGetValue(language, out ResourceManager? resourceManager) && resourceManager != null)
            {
                return new MessageContext(resourceManager, _logger);
            }

            return new MessageContext(_logger);
        }

        public string GetDefaultLanguage()
        {
            return DefaultLanguage;
        }
    }

    public class MessageContext: IMessageContext
    {
        private ILogger<MessageContextFactory> _logger;
        private ResourceManager? _resourceManager;
        public MessageContext(ILogger<MessageContextFactory> logger)
        {
            _logger = logger;
            _resourceManager = null;
        }

        public MessageContext(ResourceManager resourceManager, ILogger<MessageContextFactory> logger)
        {
            _logger = logger;
            _resourceManager = resourceManager;
        }

        public string UserNameIsExists
        {
            get
            {
                return _resourceManager?.GetString("Error_User_UserNameIsExists") ?? string.Empty;
            }
        }

        public string ServerException
        {
            get
            {
                return _resourceManager?.GetString("Error_Common_ServerException") ?? "Error_Common_ServerException";
            }
        }

        public string RoleCountNotEnough
        {
            get
            {
                return _resourceManager?.GetString("Error_Role_CountNotEnough") ?? "Error_Role_CountNotEnough";
            }
        }

        public string RoleNotExists
        {
            get
            {
                return _resourceManager?.GetString("Error_Role_NotExists") ?? "Error_Role_NotExists";
            }
        }

        public string InvaildEmailFormat
        {
            get
            {
                return _resourceManager?.GetString("Error_User_InvaildEmailFormat") ?? "Error_User_InvaildEmailFormat";
            }
        }

        public string InvaildMobileFormat
        {
            get
            {
                return _resourceManager?.GetString("Error_User_InvaildMobileFormat") ?? "Error_User_InvaildMobileFormat";
            }
        }

        public string InvaildNameFormat
        {
            get
            {
                return _resourceManager?.GetString("Error_User_InvaildNameFormat") ?? "Error_User_InvaildNameFormat";
            }
        }

        public string InvaildUserNameFormat
        {
            get
            {
                return _resourceManager?.GetString("Error_User_InvaildUserNameFormat") ?? "Error_User_InvaildUserNameFormat";
            }
        }

        public string InvaildPassword
        {
            get
            {
                return _resourceManager?.GetString("Error_My_InvaildPassword") ?? "Error_My_InvaildPassword";
            }
        }

        public string InvaildOldPassword
        {
            get
            {
                return _resourceManager?.GetString("Error_My_InvaildOldPassword") ?? "Error_My_InvaildOldPassword";
            }
        }

        public string UserNotExists
        {
            get
            {
                return _resourceManager?.GetString("Error_User_NotExists") ?? "Error_User_NotExists";
            }
        }

        public string TokenTimeout
        {
            get
            {
                return _resourceManager?.GetString("Error_Common_TokenTimeout") ?? "Error_Common_TokenTimeout";
            }
        }

        public string ErrorParam
        {
            get
            {
                return _resourceManager?.GetString("Error_Common_Param") ?? "Error_Common_Param";
            }
        }

        public string ErrorJsonParam
        {
            get
            {
                return _resourceManager?.GetString("Error_Json_Param") ?? "Error_Json_Param";
            }
        }

        public string NotExists
        {
            get
            {
                return _resourceManager?.GetString("Error_Common_NotExists") ?? "Error_Common_NotExists";
            }
        }

        public string AssetNotExists
        {
            get
            {
                return _resourceManager?.GetString("Error_Asset_NotExists") ?? "Error_Asset_NotExists";
            }
        }
        public string PanelHealthCodeIsExists
        {
            get
            {
                return _resourceManager?.GetString("Error_PanelHealth_PanelHealthCodeIsExists") ?? "Error_PanelHealth_PanelHealthCodeIsExists";
            }
        }
        public string ThirdModelTypeIsExists
        {
            get
            {
                return _resourceManager?.GetString("Error_ThirdModel_TypeIsExists") ?? "Error_ThirdModel_TypeIsExists";
            }
        }
        public string TempMonitor_Verify_DataPoint
        {
            get
            {
                return _resourceManager?.GetString("Error_TempMonitor_Verify_DataPoint") ?? "Error_TempMonitor_Verify_DataPoint";
            }
        }
        public string ErrorAuthUserPassword
        {
            get
            {
                return _resourceManager?.GetString("Error_Auth_UserPassword") ?? "Error_Auth_UserPassword";
            }
        }

        public string ErrorMySamePassword
        {
            get
            {
                return _resourceManager?.GetString("Error_My_SamePassword") ?? "Error_My_SamePassword";
            }
        }
        public string ErrorAssetNotInJson
        {
            get
            {
                return _resourceManager?.GetString("Error_Asset_Not_InJson") ?? "ErrorAssetNotInJson";
            }
        }

        public string ErrorInvaildAssetLevel
        {
            get
            {
                return _resourceManager?.GetString("Error_Asset_InvaildLevel") ?? "Error_Asset_InvaildLevel";
            }
        }

        public string Success
        {
            get
            {
                return _resourceManager?.GetString("Success") ?? "Success";
            }
        }

        public string AssetFileName
        {
            get
            {
                return _resourceManager?.GetString("Download_Asset_FileName") ?? "Download_Asset_FileName";
            }
        }

        public string PowerConsumptionFileName
        {
            get
            {
                return _resourceManager?.GetString("Power_Consumption_File_Name") ?? "PowerConsumptionFileName";
            }
        }

        public string PowerFileName
        {
            get
            {
                return _resourceManager?.GetString("Power_File_Name") ?? "PowerFileName";
            }
        }

        public string PeakFlatValleyFileName
        {
            get
            {
                return _resourceManager?.GetString("Peak_Flat_Valley_File_Name") ?? "PeakFlatValleyFileName";
            }
        }

        public string StructureFileName
        {
            get
            {
                return _resourceManager?.GetString("Structure_File_Name") ?? "StructureFileName";
            }
        }

        public string TopTenFileName
        {
            get
            {
                return _resourceManager?.GetString("Top_Ten_File_Name") ?? "TopTenFileName";
            }
        }
        public string TemperatureAlarm
        {
            get
            {
                return _resourceManager?.GetString("Alarm_Temperature") ?? "Alarm_Temperature";
            }
        }
        public string ShowNameError
        {
            get
            {
                return _resourceManager?.GetString("Error_DataPoint_ShowName") ?? "Error_DataPoint_ShowName";
            }
        }

        public string? GetString(string key)
        {
            var value = _resourceManager?.GetString(key);
            if (_resourceManager != null && string.IsNullOrEmpty(value))
            {
                _logger.LogDebug($"{_resourceManager.BaseName}缺少{key}");
            }

            return value;
        }
    }

    static class MessageContextExtend
    {
        public static string GetDataPointName(this MessageContext messageContext, string dataPoint)
        {
            var code = $"DataPoint_{dataPoint}";
            return messageContext.GetString(code) ?? code;
        }
        public static string GetStaticModelName(this MessageContext messageContext, SystemStaticModel model)
        {
            return messageContext.GetString($"StaticModel_{model.Type}_{model.Code}") ?? model.Name;
        }
        public static string GetStaticModelName(this MessageContext messageContext, string key)
        {
            return messageContext.GetString($"StaticModel_{key}") ?? key;
        }

        public static string GetErrorValue(this MessageContext messageContext, string key) 
        {
            return messageContext.GetString($"Error_{key}") ?? key;
        }

        public static string GetPageName(this MessageContext messageContext, int pageId, string pageName)
        {
            return messageContext.GetString($"Page_{pageId}") ?? pageName;
        }

        public static string GetRoleName(this MessageContext messageContext, string roleCode, string name)
        {
            return messageContext.GetString($"Role_{roleCode}") ?? name;
        }

        public static string GetOverviewValue(this MessageContext messageContext, string key)
        {
            return messageContext.GetString($"Overview_{key}") ?? key;
        }
        
        public static string GetPanelHealthValue(this MessageContext messageContext, string key)
        {
            return messageContext.GetString($"PanelHealth_{key}") ?? key;
        }
    }
}
