{"info": {"_postman_id": "89d7fd1a-00e0-4797-b59c-28dccf680a40", "name": "24使用管理员账号进入panel manager智慧分析中的健康管理菜单，在基本状态下添加临时自定义指标", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 18", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "获取单个资产的详情 Copy 16", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "let s1 = pm.response.json().items[0].id\r", "pm.environment.set(\"s1\", s1);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Asset/search?levels=Substation", "host": ["{{baseUrl}}"], "path": ["api", "v1", "<PERSON><PERSON>", "search"], "query": [{"key": "levels", "value": "Substation"}]}}, "response": []}, {"name": "获取损耗电量损耗分析图表 Copy 14", "event": [{"listen": "test", "script": {"exec": ["let C1 = pm.response.json().data\r", "pm.environment.set(\"C1\", C1);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"searchTime\": \"string\",\r\n  \"familyDefects\": {\r\n    \"value\": 0,\r\n    \"min\": 0,\r\n    \"max\": 0,\r\n    \"weight\": 0\r\n  },\r\n  \"enviroment\": {\r\n    \"value\": 0,\r\n    \"min\": 0,\r\n    \"max\": 0,\r\n    \"weight\": 0\r\n  },\r\n  \"meter\": {\r\n    \"value\": 0,\r\n    \"min\": 0,\r\n    \"max\": 0,\r\n    \"weight\": 0\r\n  },\r\n  \"cabinet\": {\r\n    \"value\": 0,\r\n    \"min\": 0,\r\n    \"max\": 0,\r\n    \"weight\": 0\r\n  },\r\n  \"lamp\": {\r\n    \"value\": 0,\r\n    \"min\": 0,\r\n    \"max\": 0,\r\n    \"weight\": 0\r\n  },\r\n  \"isolated\": {\r\n    \"value\": 0,\r\n    \"min\": 0,\r\n    \"max\": 0,\r\n    \"weight\": 0\r\n  },\r\n  \"protection\": {\r\n    \"value\": 0,\r\n    \"min\": 0,\r\n    \"max\": 0,\r\n    \"weight\": 0\r\n  }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/SystemHealth/{{s1}}/recheck", "host": ["{{baseUrl}}"], "path": ["api", "v1", "SystemHealth", "{{s1}}", "recheck"]}}, "response": []}, {"name": "获取损耗电量损耗分析图表 Copy 15", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json().data;\r", "    pm.expect(jsonData[\"status\"]).to.eql(20);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/SystemHealth/6825/RecheckResult/{{C1}}", "host": ["{{baseUrl}}"], "path": ["api", "v1", "SystemHealth", "6825", "RecheckResult", "{{C1}}"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}]}