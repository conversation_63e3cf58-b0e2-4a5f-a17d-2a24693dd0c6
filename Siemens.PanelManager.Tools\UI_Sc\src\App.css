.app-container{
  height: 100%;
  width: 100%;

  display:grid;
  grid-template-columns: 10px auto 10px;
  grid-template-rows: 64px auto;
  grid-template-areas:
  "header header header "
  ". body ."
}

.app-header{
  width: 100%;
  grid-area:header;
  background-color: #69696326;
  color: #000028;
  display: table;
  padding-left: 10px;
}

.app-header-span{
  display: table-cell;
  vertical-align: middle;
}

.app-body{
  grid-area:body;
}
