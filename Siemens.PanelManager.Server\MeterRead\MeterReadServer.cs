﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.XSSF.UserModel;
using Siemens.PanelManager.Common.IO;
using Siemens.PanelManager.Common.Model;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.Model.MeterRead;
using Siemens.PanelManager.Server.Asset;
using SqlSugar;
using System.Data;
using System.Text;

namespace Siemens.PanelManager.Server.MeterRead
{
    public class MeterReadServer
    {
        private readonly SqlSugarScope _db;

        private readonly IServiceProvider _provider;

        private readonly ILogger _log;

        public MeterReadServer(IServiceProvider provider, ILogger<MeterReadServer> log)
        {
            _db = provider.GetService<SqlSugarScope>()!;
            _provider = provider;
            _log = log;
        }

        /// <summary>
        /// 获取资产信息集合
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<MeterReadDto>> GetAssetInfoList(MeterReadParam input)
        {
            var assetInfoList = new List<MeterReadDto>();

            //获取所有的资产信息
            var allAssetinfos = await _db.Queryable<AssetInfo, AssetRelation>((t1, t2) =>
                              new JoinQueryInfos(
                                  JoinType.Inner, t1.Id == t2.ChildId
                              ))
                             .OrderBy((t1, t2) => t1.AssetLevel)
                             .Select((t1, t2) => new AssetByInfoDto
                             {
                                 Id = t1.Id,
                                 ParentId = t2.ParentId,
                                 AssetName = t1.AssetName,
                                 AssetLevel = t1.AssetLevel
                             }).ToListAsync();


            var assetInfos = new List<AssetByInfoDto>();

            //回路(实时值)
            if (input.CircuitIdList != null && input.CircuitIdList.Any())
            {
                var assetByCircuitNameList = allAssetinfos.Where(p => p.AssetLevel == AssetLevel.Circuit && input.CircuitIdList.Contains(p.Id!))
                                             .WhereIF(!string.IsNullOrWhiteSpace(input.FilterCircuitName), p => p.AssetName!.Contains(input.FilterCircuitName!))
                                             .ToList();

                assetInfos.AddRange(assetByCircuitNameList);
            }
            // 配电房 
            else if (!string.IsNullOrWhiteSpace(input.Substation))
            {

                var assetBySubstations = allAssetinfos.Where(p => p.AssetLevel == AssetLevel.Substation && input.Substation.Contains(p.AssetName!)).ToList();

                var allAssetByChirds = new List<AssetByInfoDto>();

                var assetIds = assetBySubstations.Select(o => o.Id).ToList();

                var assetByChirds = allAssetinfos.Where(p => assetIds.Contains(p.ParentId)).ToList();

                while (assetByChirds.Count != 0)
                {
                    allAssetByChirds.AddRange(assetByChirds);

                    assetIds = assetByChirds.Select(p => p.Id).Distinct().ToList();

                    assetByChirds = allAssetinfos.Where(p => assetIds.Contains(p.ParentId)).ToList();
                }

                allAssetByChirds = allAssetByChirds.Where(p => p.AssetLevel == AssetLevel.Circuit)
                                 .WhereIF(!string.IsNullOrWhiteSpace(input.FilterCircuitName), p => p.AssetName!.Contains(input.FilterCircuitName!))
                                 .ToList();

                assetInfos.AddRange(allAssetByChirds);
            }
            else
            {
                assetInfos = allAssetinfos.Where(p => p.AssetLevel == AssetLevel.Substation || p.AssetLevel == AssetLevel.Circuit)
                            .WhereIF(!string.IsNullOrWhiteSpace(input.FilterCircuitName), p => p.AssetName!.Contains(input.FilterCircuitName!))
                            .ToList();
            }

            if (assetInfos != null && assetInfos.Any())
            {
                //有几种资产类型
                int num = assetInfos.Select(p => p.AssetLevel).Distinct().Count();

                AssetLevel assetLevel = AssetLevel.Circuit;

                if (num == 2)
                {
                    assetLevel = AssetLevel.Circuit;
                }

                if (num == 1)
                {
                    assetLevel = assetInfos.FirstOrDefault()!.AssetLevel;
                }

                // 获取所有的子集数据
                var allAssetByChirds = new List<AssetByInfoDto>();

                var assetIds = assetInfos.Where(p => p.AssetLevel == assetLevel).Select(p => p.Id).Distinct().ToList();

                var assetByChirds = allAssetinfos.Where(p => assetIds.Contains(p.ParentId)).ToList();

                while (assetByChirds.Count != 0)
                {
                    allAssetByChirds.AddRange(assetByChirds);

                    assetIds = assetByChirds.Select(p => p.Id).Distinct().ToList();

                    assetByChirds = allAssetinfos.Where(p => assetIds.Contains(p.ParentId)).ToList();
                }

                switch (assetLevel)
                {
                    // 配电房
                    case AssetLevel.Substation:

                        // 获取回路的数据
                        var assetByCircuits = allAssetByChirds.Where(p => p.AssetLevel == AssetLevel.Circuit).ToList();

                        if (assetByCircuits.Any())
                        {
                            foreach (var item in assetByCircuits)
                            {
                                //资产
                                var entity = allAssetinfos.FirstOrDefault(p => p.Id == item.Id);

                                // 设备集合
                                var deviceIds = allAssetByChirds.Where(p => p.AssetLevel == AssetLevel.Device
                                                   && p.ParentId == item.Id).Select(p => p.Id).Distinct().ToList();

                                if (entity != null && deviceIds.Any())
                                {
                                    assetInfoList.Add(new MeterReadDto
                                    {
                                        CircuitId = item.Id,
                                        CircuitName = entity!.AssetName,
                                        AssetIds = deviceIds
                                    });
                                }
                            }
                        }

                        break;

                    //回路
                    case AssetLevel.Circuit:

                        assetInfos = assetInfos.Where(p => p.AssetLevel == AssetLevel.Circuit).ToList();

                        if (assetInfos.Any())
                        {
                            foreach (var item in assetInfos)
                            {
                                // 设备集合
                                var deviceIds = allAssetByChirds.Where(p => p.AssetLevel == AssetLevel.Device
                                                   && p.ParentId == item.Id).Select(p => p.Id).Distinct().ToList();

                                if (deviceIds.Any())
                                {
                                    assetInfoList.Add(new MeterReadDto
                                    {
                                        CircuitId = item.Id,
                                        CircuitName = item.AssetName,
                                        AssetIds = deviceIds
                                    });
                                }
                            }
                        }

                        break;
                }
            }

            if (assetInfoList != null && assetInfoList.Any())
            {
                assetInfoList = assetInfoList.OrderBy(p => p.CircuitName).ToList();
            }

            return assetInfoList!;
        }

        /// <summary>
        /// 获取从influxDb获取的临时数据集合
        /// </summary>
        /// <param name="propertyIntersects"></param>
        /// <param name="assetIds"></param>
        /// <param name="assetInfos"></param>
        /// <param name="date"></param>
        /// <returns></returns>
        public async Task<List<MeterReadByDto>> GetMeterReadByDtoList(List<string> propertyIntersects,
             List<int> assetIds,
             List<MeterReadDto> assetInfos,
             string date)
        {
            var influxHelper = _provider.GetRequiredService<AssetInfluxHelper>();

            var meterReadByDtoList = new List<MeterReadByDto>();

            var tabChartDtoList = new List<TabChartDto>();

            var realTabChartDtoList = new List<TabChartDto>();

            if (influxHelper != null)
            {
                List<Task> tasks = new List<Task>();

                tasks.Add(Task.Run(async () =>
                {
                    tabChartDtoList = await influxHelper.GetTabChartDataAsync(propertyIntersects, assetIds, date);
                }));

                tasks.Add(Task.Run(async () =>
                {
                    realTabChartDtoList = await influxHelper.GetRealTabChartDataAsync(propertyIntersects, assetIds);
                }));

                await Task.WhenAll(tasks);


                var coefficient = 1000.0M;

                using var sqlClient = _provider.GetRequiredService<ISqlSugarClient>();

                var systemConfig = await sqlClient.Queryable<SystemConfig>().FirstAsync(c => c.Type == "SpecialConfig" && c.Name == "EnergyCoefficient");
                if (systemConfig != null && decimal.TryParse(systemConfig.Value, out var coefficientDecimal))
                {
                    coefficient = coefficientDecimal;
                }

                foreach (var item in assetInfos)
                {
                    if (item.AssetIds == null || !item.AssetIds.Any(a => assetIds.Contains(a)))
                    {
                        continue;
                    }

                    int sortNum = 0;

                    var tabChartChirdList = tabChartDtoList.Where(p => item.AssetIds.Contains(p.AssetId) && p.Val > 0).ToList();

                    var realTimeChirdList = realTabChartDtoList.Where(p => item.AssetIds.Contains(p.AssetId) && p.Val > 0).ToList();

                    foreach (var itemProPerty in propertyIntersects)
                    {
                        sortNum++;

                        var realTimeVal = realTimeChirdList.Where(p => p.Field == itemProPerty.ToLower()).FirstOrDefault()?.Val ?? 0;

                        if (itemProPerty == "active_energy_import" || itemProPerty == "active_power")
                        {
                            realTimeVal = realTimeVal / coefficient;
                        }

                        realTimeVal = Convert.ToDecimal(realTimeVal.ToString("0.00"));

                        UniversalDeviceInfo.Instance._dicProperty.TryGetValue(itemProPerty, out string? propertyName);

                        var entity = new MeterReadByDto()
                        {
                            CircuitId = item.CircuitId,
                            CircuitName = item.CircuitName,
                            PropertyName = propertyName,
                            RealTimeValue = realTimeVal,
                            SortNum = sortNum
                        };

                        var tabChartChirds = tabChartChirdList.Where(p => p.Field == itemProPerty.ToLower()).ToList();

                        // 时间数据添加
                        for (int i = 0; i <= 23; i++)
                        {
                            string fileName = $"Val{i}";

                            var dateTime = i >= 10 ? $"{i}:00" : $"0{i}:00";

                            var modelVal = tabChartChirds.Where(p => !string.IsNullOrEmpty(p.DateTime) && p.DateTime.StartsWith(dateTime)).FirstOrDefault()?.Val ?? 0;

                            if (itemProPerty.ToLower() == "active_energy_import" || itemProPerty.ToLower() == "active_power")
                            {
                                modelVal = modelVal / coefficient;
                            }

                            #region 时刻赋值
                            switch (fileName)
                            {
                                case "Val0":
                                    entity.Val0 = modelVal ;
                                    break;
                                case "Val1":
                                    entity.Val1 = modelVal;
                                    break;
                                case "Val2":
                                    entity.Val2 = modelVal;
                                    break;
                                case "Val3":
                                    entity.Val3 = modelVal;
                                    break;
                                case "Val4":
                                    entity.Val4 = modelVal;
                                    break;
                                case "Val5":
                                    entity.Val5 = modelVal;
                                    break;
                                case "Val6":
                                    entity.Val6 = modelVal;
                                    break;
                                case "Val7":
                                    entity.Val7 = modelVal;
                                    break;
                                case "Val8":
                                    entity.Val8 = modelVal;
                                    break;
                                case "Val9":
                                    entity.Val9 = modelVal;
                                    break;
                                case "Val10":
                                    entity.Val10 = modelVal;
                                    break;
                                case "Val11":
                                    entity.Val11 = modelVal;
                                    break;
                                case "Val12":
                                    entity.Val12 = modelVal;
                                    break;
                                case "Val13":
                                    entity.Val13 = modelVal;
                                    break;
                                case "Val14":
                                    entity.Val14 = modelVal;
                                    break;
                                case "Val15":
                                    entity.Val15 = modelVal;
                                    break;
                                case "Val16":
                                    entity.Val16 = modelVal;
                                    break;
                                case "Val17":
                                    entity.Val17 = modelVal;
                                    break;
                                case "Val18":
                                    entity.Val18 = modelVal;
                                    break;
                                case "Val19":
                                    entity.Val19 = modelVal;
                                    break;
                                case "Val20":
                                    entity.Val20 = modelVal;
                                    break;
                                case "Val21":
                                    entity.Val21 = modelVal;
                                    break;
                                case "Val22":
                                    entity.Val22 = modelVal;
                                    break;
                                case "Val23":
                                    entity.Val23 = modelVal;
                                    break;
                            }
                            #endregion
                        }

                        meterReadByDtoList.Add(entity);
                    }
                }
            }

            return meterReadByDtoList.OrderBy(p => p.CircuitId).ThenBy(p => p.SortNum).ToList();
        }

        /// <summary>
        /// 导出excel(方式1)
        /// </summary>
        /// <param name="meterReadByDtoList"></param>
        /// <returns></returns>
        public Stream GetTabHtml(List<MeterReadByDto> meterReadByDtoList)
        {

            StringBuilder sbHtml = new StringBuilder();

            sbHtml.Append("<table border='1' cellspacing='0' cellpadding='0'>");
            sbHtml.Append("<tr>");
            sbHtml.Append("<td style='font-size: 14px;text-align:center;background-color: #DCE0E2; font-weight:bold;height:25px;'>回路名称</td>");
            sbHtml.Append("<td style='font-size: 14px;text-align:center;background-color: #DCE0E2; font-weight:bold;height:25px;'>属性</td>");
            sbHtml.Append("<td style='font-size: 14px;text-align:center;background-color: #DCE0E2; font-weight:bold;height:25px;'>实时值</td>");

            for (int i = 0; i <= 23; i++)
            {
                var dateTime = i >= 10 ? $"{i}:00" : $"0{i}:00";
                sbHtml.Append($"<td style='font-size: 14px;text-align:center;background-color: #DCE0E2; font-weight:bold;height:25px;with:100px;'>{dateTime}</td>");
            }

            sbHtml.Append("</tr>");

            if (meterReadByDtoList != null && meterReadByDtoList.Any())
            {
                var groupMeterReadDtoList = meterReadByDtoList.GroupBy(p => p.CircuitId)
                                           .Select(p => new
                                           {
                                               p.FirstOrDefault()?.CircuitId,
                                               Num = p.Count()
                                           })
                                           .OrderBy(p => p.CircuitId).ToList();

                foreach (var groupItem in groupMeterReadDtoList)
                {
                    var meterReadDetails = meterReadByDtoList
                                        .Where(p => p.CircuitId == groupItem.CircuitId)
                                        .ToList();

                    int sortNum = 0;

                    foreach (var item in meterReadDetails)
                    {
                        sortNum++;

                        sbHtml.Append("<tr>");
                        if (sortNum == 1)
                        {
                            sbHtml.Append($"<td rowspan='{groupItem.Num}' style='font-size: 12px;height:20px; text-align: center;vertical-align: middle; " +
                                           $"vnd.ms-excel.number format:@'>{item.CircuitName}</td>");
                        }

                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.PropertyName}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.RealTimeValue}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val0}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val1}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val2}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val3}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val4}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val5}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val6}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val7}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val8}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val9}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val10}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val11}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val12}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val13}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val14}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val15}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val16}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val17}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val18}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val19}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val20}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val21}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val22}</td>");
                        sbHtml.Append($"<td style='font-size:12px;height:20px;'>{item.Val23}</td>");
                        sbHtml.Append("</tr>");
                    }
                }
            }

            sbHtml.Append("</table>");

            return FileHelper.GetStrByStream(sbHtml.ToString());
        }

        /// <summary>
        /// 导出excel(方式2)
        /// </summary>
        /// <param name="meterReadByDtoList"></param>
        /// <param name="path"></param>
        /// <param name="fileName"></param>
        public void ExportExcel(List<MeterReadByDto> meterReadByDtoList, string path, string fileName)
        {
            IWorkbook book = new XSSFWorkbook();
            ISheet sheet = book.CreateSheet("Sheet1");

            sheet.PrintSetup.PaperSize = (short)PaperSize.A4;
            sheet.DefaultRowHeight = 23 * 20;
            sheet.SetColumnWidth(0, 50 * 256);
            sheet.SetColumnWidth(1, 30 * 256);
            sheet.SetColumnWidth(2, 10 * 256);
            sheet.SetColumnWidth(3, 10 * 256);
            sheet.SetColumnWidth(4, 10 * 256);
            sheet.SetColumnWidth(5, 10 * 256);
            sheet.SetColumnWidth(6, 10 * 256);
            sheet.SetColumnWidth(7, 10 * 256);
            sheet.SetColumnWidth(8, 10 * 256);
            sheet.SetColumnWidth(9, 10 * 256);
            sheet.SetColumnWidth(10, 10 * 256);
            sheet.SetColumnWidth(11, 10 * 256);
            sheet.SetColumnWidth(12, 10 * 256);
            sheet.SetColumnWidth(13, 10 * 256);
            sheet.SetColumnWidth(14, 10 * 256);
            sheet.SetColumnWidth(15, 10 * 256);
            sheet.SetColumnWidth(16, 10 * 256);
            sheet.SetColumnWidth(17, 10 * 256);
            sheet.SetColumnWidth(18, 10 * 256);
            sheet.SetColumnWidth(19, 10 * 256);
            sheet.SetColumnWidth(20, 10 * 256);
            sheet.SetColumnWidth(21, 10 * 256);
            sheet.SetColumnWidth(22, 10 * 256);
            sheet.SetColumnWidth(23, 10 * 256);
            sheet.SetColumnWidth(24, 10 * 256);
            sheet.SetColumnWidth(25, 10 * 256);
            sheet.SetColumnWidth(26, 10 * 256);

            ICellStyle style = book.CreateCellStyle();
            style.Alignment = HorizontalAlignment.Left;
            style.VerticalAlignment = VerticalAlignment.Center;

            //克隆样式
            ICellStyle newCellStyle = book.CreateCellStyle();
            newCellStyle.CloneStyleFrom(style);
            newCellStyle.Alignment = HorizontalAlignment.Center;
            newCellStyle.VerticalAlignment = VerticalAlignment.Center;

            #region 创建大标题头
            var headRow = sheet.CreateRow(0);

            for (int i = 0; i <= 26; i++)
            {
                if (i == 0)
                {
                    headRow.CreateCell(i).SetCellValue(fileName.Replace(".xlsx", ""));
                }
                else if (i == 24)
                {
                    headRow.CreateCell(i).SetCellValue("导出时间");
                }
                else if (i == 25)
                {
                    headRow.CreateCell(i).SetCellValue(DateTime.Now.ToString("yyyyMMdd HH:mm:ss"));
                }
                else
                {
                    headRow.CreateCell(i);
                }
            }

            #endregion

            //创建表头行
            #region 创建行标题头
            var headerRow = sheet.CreateRow(1);
            headerRow.CreateCell(0).SetCellValue("回路名称");
            headerRow.CreateCell(1).SetCellValue("属性");
            headerRow.CreateCell(2).SetCellValue("实时值");

            for (int i = 0; i <= 23; i++)
            {
                var dateTime = i >= 10 ? $"{i}:00" : $"0{i}:00";
                headerRow.CreateCell(i + 3).SetCellValue(dateTime);
            }
            #endregion

            #region 创建body
            if (meterReadByDtoList != null && meterReadByDtoList.Any())
            {
                int num = 1;
                foreach (var item in meterReadByDtoList)
                {
                    num++;
                    var dataRow = sheet.CreateRow(num);
                    dataRow.CreateCell(0).SetCellValue(item.CircuitName);
                    dataRow.CreateCell(1).SetCellValue(item.PropertyName);
                    dataRow.CreateCell(2).SetCellValue((double)item.RealTimeValue);
                    dataRow.CreateCell(3).SetCellValue((double)item.Val0);
                    dataRow.CreateCell(4).SetCellValue((double)item.Val1);
                    dataRow.CreateCell(5).SetCellValue((double)item.Val2);
                    dataRow.CreateCell(6).SetCellValue((double)item.Val3);
                    dataRow.CreateCell(7).SetCellValue((double)item.Val4);
                    dataRow.CreateCell(8).SetCellValue((double)item.Val5);
                    dataRow.CreateCell(9).SetCellValue((double)item.Val6);
                    dataRow.CreateCell(10).SetCellValue((double)item.Val7);
                    dataRow.CreateCell(11).SetCellValue((double)item.Val8);
                    dataRow.CreateCell(12).SetCellValue((double)item.Val9);
                    dataRow.CreateCell(13).SetCellValue((double)item.Val10);
                    dataRow.CreateCell(14).SetCellValue((double)item.Val11);
                    dataRow.CreateCell(15).SetCellValue((double)item.Val12);
                    dataRow.CreateCell(16).SetCellValue((double)item.Val13);
                    dataRow.CreateCell(17).SetCellValue((double)item.Val14);
                    dataRow.CreateCell(18).SetCellValue((double)item.Val15);
                    dataRow.CreateCell(19).SetCellValue((double)item.Val16);
                    dataRow.CreateCell(20).SetCellValue((double)item.Val17);
                    dataRow.CreateCell(21).SetCellValue((double)item.Val18);
                    dataRow.CreateCell(22).SetCellValue((double)item.Val19);
                    dataRow.CreateCell(23).SetCellValue((double)item.Val20);
                    dataRow.CreateCell(24).SetCellValue((double)item.Val21);
                    dataRow.CreateCell(25).SetCellValue((double)item.Val22);
                    dataRow.CreateCell(26).SetCellValue((double)item.Val23);
                }

                #region 处理第一行的单元格合并和样式
                sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 23));
                var cell1 = sheet.GetRow(0).GetCell(0);
                cell1.CellStyle = newCellStyle;

                sheet.AddMergedRegion(new CellRangeAddress(0, 0, 25, 26));

                #endregion

                var groupMeterReadDtoList = meterReadByDtoList.GroupBy(p => p.CircuitId)
                                           .Select(p => new
                                           {
                                               p.FirstOrDefault()?.CircuitId,
                                               Num = p.Count()
                                           })
                                           .OrderBy(p => p.CircuitId).ToList();

                int sortNum = 1;
                foreach (var groupItem in groupMeterReadDtoList)
                {
                    int startNum = sortNum + 1;
                    int endNum = sortNum + groupItem.Num;

                    if (groupItem.Num > 1)
                    {
                        sheet.AddMergedRegion(new CellRangeAddress(startNum, endNum, 0, 0));
                    }

                    //设置合并后style
                    var cell = sheet.GetRow(startNum).GetCell(0);
                    cell.CellStyle = style;
                    sortNum += groupItem.Num;
                }
            }
            #endregion

            if (File.Exists(path))
            {
                //删除文件
                File.Delete(path);
            }

            //将工作簿写入文件
            using (FileStream fs = new FileStream(path, FileMode.Create, FileAccess.Write))
            {
                book.Write(fs);
            }
        }

    }
}
