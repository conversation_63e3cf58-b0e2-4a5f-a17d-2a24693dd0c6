{"info": {"_postman_id": "6be77309-908d-448a-bfc2-63631e77ffc6", "name": "09使用超级管理员账号进入panel manager组态编辑中的图形编辑页面，点击创建新单线图拖动横线或竖线添加节点", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "添加拓扑图 Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "        {\r\n            \"code\": \"母线01\",\r\n            \"name\": \"母线01\",\r\n            \"time\": \"2023-03-14 15:01:18\",\r\n            \"owner\": \"\",\r\n            \"topology\": {\r\n                \"class\": \"GraphLinksModel\",\r\n                \"copiesKey\": false,\r\n                \"linkKeyProperty\": \"key\",\r\n                \"nodeDataArray\": [\r\n                    {\r\n                        \"type\": \"L1\",\r\n                        \"name\": \"Busbar\",\r\n                        \"displayName\": \"横平母线\",\r\n                        \"geo\": \"busBar\",\r\n                        \"size\": \"70 70\",\r\n                        \"category\": \"busLineNodeTemplate\",\r\n                        \"key\": -1,\r\n                        \"location\": \"0 0\",\r\n                        \"isSelected\": false\r\n                    },\r\n                    {\r\n                        \"key\": 0,\r\n                        \"index\": 0,\r\n                        \"type\": \"A\",\r\n                        \"name\": \"breaker\",\r\n                        \"geo\": \"circle\",\r\n                        \"size\": \"8 8\",\r\n                        \"loc\": \"-1150 -150\",\r\n                        \"flag\": \"busPoint\",\r\n                        \"figure\": \"Ellipse\",\r\n                        \"fill\": \"black\",\r\n                        \"isSelected\": false,\r\n                        \"category\": \"breakerTemplate\"\r\n                    },\r\n                    {\r\n                        \"key\": 1,\r\n                        \"index\": 0,\r\n                        \"type\": \"A\",\r\n                        \"name\": \"breaker\",\r\n                        \"geo\": \"circle\",\r\n                        \"size\": \"8 8\",\r\n                        \"loc\": \"450 -150\",\r\n                        \"flag\": \"busPoint\",\r\n                        \"figure\": \"Ellipse\",\r\n                        \"fill\": \"black\",\r\n                        \"isSelected\": false,\r\n                        \"category\": \"breakerTemplate\"\r\n                    },\r\n                    {\r\n                        \"type\": \"L1\",\r\n                        \"name\": \"Busbar2\",\r\n                        \"displayName\": \"竖直母线\",\r\n                        \"geo\": \"busBar4\",\r\n                        \"size\": \"70 70\",\r\n                        \"category\": \"busLineNodeTemplate\",\r\n                        \"key\": -2,\r\n                        \"location\": \"0 66.5\",\r\n                        \"isSelected\": false\r\n                    },\r\n                    {\r\n                        \"key\": 2,\r\n                        \"index\": 0,\r\n                        \"type\": \"A\",\r\n                        \"name\": \"breaker\",\r\n                        \"geo\": \"circle\",\r\n                        \"size\": \"8 8\",\r\n                        \"loc\": \"-1150 -150\",\r\n                        \"flag\": \"bus2Point\",\r\n                        \"figure\": \"Ellipse\",\r\n                        \"fill\": \"black\",\r\n                        \"isSelected\": false,\r\n                        \"selectable\": false,\r\n                        \"category\": \"breakerTemplate\"\r\n                    },\r\n                    {\r\n                        \"key\": 3,\r\n                        \"index\": 0,\r\n                        \"type\": \"A\",\r\n                        \"name\": \"breaker\",\r\n                        \"geo\": \"circle\",\r\n                        \"size\": \"8 8\",\r\n                        \"loc\": \"-1150 450\",\r\n                        \"flag\": \"bus2Point\",\r\n                        \"figure\": \"Ellipse\",\r\n                        \"fill\": \"black\",\r\n                        \"isSelected\": false,\r\n                        \"selectable\": false,\r\n                        \"category\": \"breakerTemplate\"\r\n                    }\r\n                ],\r\n                \"linkDataArray\": [\r\n                    {\r\n                        \"from\": 0,\r\n                        \"to\": 1,\r\n                        \"key\": 0,\r\n                        \"line_type_id\": 1,\r\n                        \"length\": 0,\r\n                        \"selectable\": false,\r\n                        \"standard\": \"125mm*8mm的铜排\",\r\n                        \"resistance_per_meter\": \"0.0000116660\",\r\n                        \"reactance_per_meter\": \"0.0001575\",\r\n                        \"color\": \"#000\",\r\n                        \"category\": \"busBarlink\",\r\n                        \"points\": [\r\n                            -1146,\r\n                            -149.9999971354169,\r\n                            446,\r\n                            -149.9999971354169\r\n                        ]\r\n                    },\r\n                    {\r\n                        \"from\": 2,\r\n                        \"to\": 3,\r\n                        \"key\": 0,\r\n                        \"line_type_id\": 1,\r\n                        \"length\": 0,\r\n                        \"selectable\": false,\r\n                        \"standard\": \"125mm*8mm的铜排\",\r\n                        \"resistance_per_meter\": \"0.0000116660\",\r\n                        \"reactance_per_meter\": \"0.0001575\",\r\n                        \"color\": \"#000\",\r\n                        \"category\": \"busBarlink2\",\r\n                        \"points\": [\r\n                            -1150,\r\n                            -146,\r\n                            -1150,\r\n                            446\r\n                        ]\r\n                    }\r\n                ]\r\n            },\r\n            \"discription\": \"\",\r\n            \"dashboardVisible\": false,\r\n            \"dashboardFirst\": false\r\n        }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/sld", "host": ["{{baseUrl}}"], "path": ["api", "v1", "sld"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 1000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});"]}}]}