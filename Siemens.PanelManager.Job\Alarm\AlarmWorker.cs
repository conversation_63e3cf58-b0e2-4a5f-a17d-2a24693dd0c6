﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.Model.DataFlow;
using Siemens.PanelManager.Server.DataPoint;
using SqlSugar;

namespace Siemens.PanelManager.Job.Alarm
{
    internal class AlarmWorker : BackgroundService
    {
        private readonly ILogger _logger;
        private readonly IServiceProvider _provider;
        public AlarmWorker(ILogger<AlarmWorker> logger, IServiceProvider provider)
        {
            _logger = logger;
            _provider = provider;
        }
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            var dataPointSvc = _provider.GetRequiredService<DataPointServer>();
            await dataPointSvc.InitDataPoint();
            await Task.Delay(1 * 60 * 1000);
            _logger.LogInformation("告警状态初始化服务启动");
            var sqlClient = _provider.GetRequiredService<SqlSugarScope>();
            int pageNum = 1;
            int pageSize = 1000;
            var refObj = _provider.GetRequiredService<IAssetDataProxyRef>();
            var cache = _provider.GetRequiredService<SiemensCache>();
            cache.Set("Status:AlarmWorker", 0);
            while (true)
            {
                var alarmLogs = await sqlClient.Queryable<AlarmLog>()
                    .Where(al => al.Status >= AlarmLogStatus.New
                        && (al.EventType == AlarmEventType.UdcAlarm || al.EventType == AlarmEventType.Alarm || al.EventType == AlarmEventType.BreakerTrip)
                        && al.Status < AlarmLogStatus.Finish
                        && al.AssetId.HasValue)
                    .OrderBy(al => al.Id)
                    .ToPageListAsync(pageNum, pageSize);
                pageNum++;
                if (alarmLogs.Count > 0)
                {
                    foreach (var al in alarmLogs)
                    {
                        if (al.RuleId.HasValue)
                        {
                            cache.Set($"AlarmRoleCheck-{al.RuleId}|{al.AssetId}", true);
                        }
                    }
                }

                if (alarmLogs.Count < pageSize)
                {
                    break;
                }
            }

            cache.Set("Status:AlarmWorker", 10);

            var jobManager = _provider.GetRequiredService<JobManager>();
            await jobManager.TriggerJob("AlarmCountJob");
        }
    }
}
