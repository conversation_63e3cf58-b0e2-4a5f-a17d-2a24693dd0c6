﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Akka.DependencyInjection" Version="1.5.24" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Siemens.PanelManager.Common\Siemens.PanelManager.Common.csproj" />
    <ProjectReference Include="..\Siemens.PanelManager.Interface\Siemens.PanelManager.Interface.csproj" />
    <ProjectReference Include="..\Siemens.PanelManager.Model\Siemens.PanelManager.Model.csproj" />
    <ProjectReference Include="..\Siemens.PanelManager.Server\Siemens.PanelManager.Server.csproj" />
  </ItemGroup>

</Project>
