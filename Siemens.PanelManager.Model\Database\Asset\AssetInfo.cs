﻿using Newtonsoft.Json;
using SqlSugar;

namespace Siemens.PanelManager.Model.Database.Asset
{
    [SugarTable("asset_info")]
    public class AssetInfo : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "asset_name", IsNullable = false, Length = 256)]
        public string AssetName { get; set; } = string.Empty;
        [UniquenessAttribute]
        [SugarColumn(ColumnName = "asset_number", IsNullable = false, Length = 256)]
        public string AssetNumber { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "asset_level", IsNullable = false)]
        public AssetLevel AssetLevel { get; set; }
        [SugarColumn(ColumnName = "asset_model", Length = 50, IsNullable = true)]
        public string? AssetModel { get; set; }
        [SugarColumn(ColumnName = "mlfb", Length = 50, IsNullable = true)]
        public string? MLFB { get; set; }
        [SugarColumn(ColumnName = "asset_type", Length = 50, IsNullable = true)]
        public string? AssetType { get; set; }
        [SugarColumn(ColumnName = "asset_description", Length = 1024, IsNullable = true)]
        public string? Description { get; set; }
        [SugarColumn(ColumnName = "telephone", Length = 512, IsNullable = true)]
        public string? Telephone { get; set; }
        [SugarColumn(ColumnName = "prefix_telephone", Length = 10, IsNullable = true)]
        public string? PrefixTelephone { get; set; }
        [SugarColumn(ColumnName = "principal", Length = 512, IsNullable = true)]
        public string? Principal { get; set; }
        [SugarColumn(ColumnName = "asset_maker", Length = 256, IsNullable = true)]
        public string? AssetMaker { get; set; }
        [SugarColumn(ColumnName = "asset_location", Length = 512, IsNullable = true)]
        public string? Location { get; set; }
        [SugarColumn(ColumnName = "point_data", Length = 512, IsNullable = true)]
        public string? PointData { get; set; }
        [SugarColumn(ColumnName = "use_scene", Length = 50, IsNullable = true)]
        public string? UseScene { get; set; }
        [SugarColumn(ColumnName = "meter_type", Length = 50, IsNullable = true)]
        public string? MeterType { get; set; }
        [SugarColumn(ColumnName = "install_date", IsNullable = true)]
        public DateTime? InstallDate { get; set; }
        [SugarColumn(ColumnName = "drawing_file_ids", IsNullable = true, Length = 256)]
        public string? DrawingFileIdsStr { get; set; }
        [SugarColumn(ColumnName = "topoplogy_id", IsNullable = true)]
        public int? TopologyId { get; set; }
        [SugarColumn(ColumnName = "topoplogy_3d_id", IsNullable = true)]
        public int? Topology3DId { get; set; }
        [SugarColumn(ColumnName = "image_ids", IsNullable = true, Length = 256)]
        public string? ImageIdsStr { get;set; }
        [SugarColumn(ColumnName = "circuit_name", IsNullable = true, Length = 256)]
        public string? CircuitName { get; set; }
        [SugarColumn(ColumnName = "ip_address", IsNullable = true, Length = 50)]
        public string? IPAddress { get; set; }
        [SugarColumn(ColumnName = "port", IsNullable = true, Length = 50)]
        public string? Port { get; set; }
        [SugarColumn(ColumnName = "object_id", IsNullable = true, Length = 50)]
        public string? ObjectId { get; set; }

        [SugarColumn(ColumnName = "bus_bar_id", IsNullable = true, Length = 50)]
        public string? BusBarId { get; set; }
        [SugarColumn(ColumnName = "row_no", IsNullable = true, Length = 50)]
        public string? RowNo { get; set; }
        [SugarColumn(ColumnName = "width", IsNullable = true, Length = 50)]
        public string? Width { get; set; }
        [SugarColumn(ColumnName = "height", IsNullable = true, Length = 50)]
        public string? Height { get; set; }
        [SugarColumn(ColumnName = "relation_panel_id", IsNullable = true)]
        public int? RelationPanelId { get; set; }
        [SugarColumn(ColumnName = "third_part_device_code", IsNullable = true, Length = 50)]
        public string? ThirdPartCode { get; set; }

        [SugarColumn(ColumnName = "enable_mqtt", IsNullable = true)]
        public bool? EnableMqtt { get; set; }
        [SugarColumn(ColumnName = "sort_no", IsNullable = true)]
        public int? SortNo { get; set; }

        [SugarColumn(IsIgnore = true)]
        public int SortNoNotNull
        {
            get
            {
                return SortNo ?? Id;
            }
        }

        [SugarColumn(IsIgnore = true)]
        public int[]? ImageIds
        {
            get
            {
                if (string.IsNullOrEmpty(ImageIdsStr)) return null;
                return JsonConvert.DeserializeObject<int[]>(ImageIdsStr);
            }
            set
            {
                if (value == null)
                {
                    ImageIdsStr = null;
                    return;
                }
                ImageIdsStr = JsonConvert.SerializeObject(value);
            }
        }

        [SugarColumn(IsIgnore = true)]
        public int[]? DrawingFileIds
        {
            get 
            {
                if (string.IsNullOrEmpty(DrawingFileIdsStr)) return null;
                return JsonConvert.DeserializeObject<int[]>(DrawingFileIdsStr);
            }
            set
            {
                if (value == null)
                {
                    DrawingFileIdsStr = null;
                    return;
                }
                DrawingFileIdsStr = JsonConvert.SerializeObject(value);
            }
        }

        [SugarColumn(IsIgnore = true)]
        public string? DrawingCode { get; set; }

        [SugarColumn(IsIgnore = true)]
        public string? IpAddressStr
        {
            get
            {
                if (string.IsNullOrEmpty(IPAddress))
                {
                    return IPAddress;
                }

                var ips = IPAddress.Split('/');
                if (ips.Length > 0)
                {
                    return ips[0];
                }
                return null;
            }
        }

        [SugarColumn(IsIgnore = true)]
        public int? SlaveId
        {
            get
            {
                if (string.IsNullOrEmpty(IPAddress))
                {
                    return null;
                }

                var ips = IPAddress.Split('/');
                if (ips.Length == 1)
                {
                    return 1;
                }
                else if (ips.Length > 1 && int.TryParse(ips[1], out int slaveId))
                {
                    return slaveId;
                }

                return null;
            }
        }
    }

    public enum AssetLevel : int
    {
        Area = 10,
        Substation = 20,
        Panel = 30,
        Transformer =31,
        Circuit = 40,
        Device = 50
    }

    public enum MeasurementType
    {
        Gateway,
        Normal,
        Terminal
    }
}
