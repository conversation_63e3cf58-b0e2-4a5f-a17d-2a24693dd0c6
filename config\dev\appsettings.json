{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "DefaultLanguage": "zh-cn", "AllowedHosts": "*", "SecretKey": "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDJKIhG77In5y4h\r\n1GnsH500sqD/W+S2g+gePFSeqvLmnqs+8Qiha/WKY/KtwAB4bgs0Ds7b2Vt8n1lX\r\nCng4waZ3rwQTlK5BsX4ZhMWuIWQYj2MIOq0mYGS0/blUzNrdEI61x2ga9HYVsWCT\r\ngws8XwKBtuZT0rtn5ycPpKLhrpP3jzhAeqXJr2neLFrVYWszlqNfwfBMmT7ISWnB\r\nBly1uLP4RThJsphKJecbqvv/HNH/f9yzye8MpOz3DxEsLRx5eXA5GXEF8EbdlGyv\r\nS4jLoH0PrHP9W7b+PuHZLC6YJpetJJ3Y/U5bG5t0gSvq6dI47sODa6I833MB6fLM\r\nn3lo2OCzAgMBAAECggEAEo+B24G8X4AuOoLd7yKPn9AnmMhC1zBel4ObjzRYyzyy\r\nslENKc/LTSShvLRtlq8Yq4n+PjzU+y+8z5Iwu5Pqpzpn1uKuHXOiHfqpPsLn+Hgq\r\nfBTl6xeT0ztiAVgRjbo8YLtweKQ7zQ6byaCaxcxCb/OPVwanshObAA+B0+8Gyh2s\r\nA15v97RrLu7biz/wmuIzxv4dbQqE4gSbJMPtdHRfZcuIZ7KetDi3mkljN6PMqyP7\r\nsxzi8cE7G4EZH7toq2IA0cWV4pK+4z3nx/n+Mz/WPlguIgr+4n+DAu2kpwA9TtDq\r\nc2eEx8qIBQ20YbqAXn2cM+DaVWzxHP6ztK8cEsy3qQKBgQDj316e+8TJMn3yQeJJ\r\nUX/jh6HuGlkLr48eHEB8B1Yc0tsh2ubV1lEai9xd2dSs3pOjdDUOkMlo0ZSFi0rl\r\nfc6Nsl8oxyGgsC1onXm7nnNy3+6osVJSVqCS1Jdp1hfZx+RnIuUfeqRA7xAvyfjm\r\ntLgWYRrYRfAuk6XexJSmkfbQVwKBgQDh/QKnVTP05wiFBVCnla6pcNUD977/C0ls\r\nZS7GSk/4IO4JmHPa4vFh9kIg9bJO6nPJhanfZfZ9TKFBYUYbxT0PKC0m1hYBqfX6\r\n+Pp6W96y9/fbi1J3bjzU8oTIoCbcE/qwMwbGjNdgopRbyZvdYHvSiZxZFmuVH74E\r\nDgTzBJJJBQKBgGUoEpQXBgue64QwtSiBEnnKCxts/NyQKcNfHU0x4ItZG0gTKegB\r\nhO146vGKsruu7vFfs/HsWky6xOjnpe7sg1Ypuc6bX5U6wF2xkom0VGHJgOPIG9hL\r\ndmrYDwM/tETMVNtoBTiNM/9TYOcmDOU1kob0wnsByRaUGs0ijS7Imz1vAoGAG31P\r\naVNv8oZ3/tZarUgD6xCSmNz2GsggslLMkcpQV/qJehlVXnbkrenquV+pxPe4BfeA\r\nZnBcv3km1HEkuhQQDVvxwhugqasnBNRsg53RRSvstoQIEAcU10J47H6uu26iu/Q5\r\nCsXvHQ4pxdCMaS1nYoDix+N2SrmFv7CE4ZrYwJECgYBNkX/eRZhXBYPrkuCEjPb7\r\njlxUhJuZ7wQuyFjRndyYtEPa5U68s/gWC03GJ3zsqFHtTasTRMHflipTkHyvsUYq\r\ntFBg9fdmNAAFfnfTjZ09WulvczFWUAwFQGHlJv0XNd3Lxn/yIAmJtlOVUL7lsNGS\r\nEyMc6Ybg+VMTE/vTG46TJg==", "LocalPrivateKey": "MIIEowIBAAKCAQEA7pXtNU0JFFeEGFJRpkyibsWruI+J1BTuOETfHIuKu38Ka18T8jjIrZZByko3NFDonHPdZHTY2HLpFzY3a2gfAmMvhD4HoMt49bNGtVmZIYoJLCvZ8PzqypVSH2M1B9PYH3m3v7qX/4ZgEgPHm+dM6J0k8xGRgZaqA4kObwmWywAFWWKzCylyZN6aVgy52R784pgdbfb8GGV/x1Ct1YY1QfLICp8HHNUE40s4MRoHY5zL/tr1mHx+drDiooFVPjQaKofG8qC6sOTw0nWBP300xseaju9B74uMlLR8xgblRJRPjnqljt6JJlTvxKm05uZORE1ZUZOAr559Jxg8L+/IzwIDAQABAoIBAH94nRcYRJyYsZvpB3TJhxOWC0x9bE9iq3Bm46m1qHbKxn9deiO1UQrlt9rj0iDihZVplkTg5SaoJn9fWTbKl57H7ZBDMAIqFEN5+HJWH6m6vXHbMANArhV0uD7K23+GIPeYKaOHh0RfwB5/5HsYlo/FvgMcRNiu7cEbzhlnTyi/nuhBASQ37PBcOWj7WmP6gWMzWxRZHXAk0T4IN8o+mBzaimkkdU1+TOkDihjZ8O3/GR8Dz+tgkU1yK46CbaLxWcYksGd7T30jAzrMLGUZwN0n9i3ZUh8MUgukUNgRCyt87vH+Lc6dZ5bdrIFsJz2eVwbib7Sl5If6wU/HxQ8Ip3ECgYEA/o/wDKVz0IINPowMX8VvyI3PBvp4q76yPVyGaGq6uqZBovT3Y1Por8MVYUrPQxxN/qD3rPdGH0OvxHgKI6ESKplsUmJq5JuNAioy4WQF9sZq18kLl2FQWU3KOSdMaUW218JepLIHf3JWAbtsIdaEVh1n48GS0fRrDKrXCRmQ51kCgYEA7+7jj39E0BVFWCdnMBRHANGnkGOCgxShQa4I5Hmn1Z8WPdXM2az7nCEisrL/Kedt3c8HRQngEshw3Ik33u/DZ5kDuCV4NQXReF080WcorUNgrVpLdKA2pQ+NapBRqaKcu1pW8oUJ78h48WQmXw+6TibBB99nm44WysvOhnrV1GcCgYArbgkpTyz0HMv/qMsPp9KLZ84zRZZCztT/7QItXFAhOe4mzB5MvusK2YCTSb0UmQahc3rt8n3vJZAeN9CbBYbg5634XDwaZoNnJCXsGDGASZe+2tj0abDYm5HtsEu0BiXMp6eX95uZ3brYrs4i88M+IeQwkjznPwjob1kif08eeQKBgB/gaiVPcFIA8XSTz5bIquJF+HAreFR38jCqsY3BR/YOq8LNW+jznX4RvO1m8JWdpLAaaEnY1QdJ0AzMwjqBOkHSR3c4qy7INhs6r/DWNsZGNFrzqyC0hG4Lkes9f0v9SbtudizEVhygUsknWB27FjJ53Z1xJe+5wfdxel/NhAkzAoGBAKK8kdjMxq1XvnpCiCAfa0s6uW4BIisYyT1j8F3+KUSgYTy9DIRkJaJwG9yGwb+vwRrN7cs7AnpiAdQo5z+7rQaRCQlg7HeI/iwvw2RYWNaCBTn+cJd2wb5Al7MVKwbRiuUDpcK+3dGGbbW16B9MDwhVLqeC5OR9yfr/yi6i5SdZ", "ConnectionStrings": {"default": "Host=pgsql-dev;port=5432;Database=PanelManager;Username=********;password=********;", "Sqlite": "Data Source=/data/data.db;Mode=ReadOnly;Cache=Private;"}, "UdcApiPath": "http://*************:5002/", "UdcDataPath": "/data", "AlgorithmLossApiPath": "http://**************:5088", "AlgorithmEvaluationApiPath": "http://**************:5078", "AlgorithmLoss2ApiPath": "http://**************:5068", "InfluxDb": {"Url": "http://**************:8086", "UserName": "panel", "Password": "myPassWord", "Bucket": "panel", "OrgName": "panel"}, "MonitorApi": "http://**********:51030/", "MqttClient": {"Server": "**********", "Port": 1883, "ClientId": "PanelManagerServer", "UserName": "admin", "Password": "admin", "Topic": "topic_1"}, "PerQueryCount": 5000}