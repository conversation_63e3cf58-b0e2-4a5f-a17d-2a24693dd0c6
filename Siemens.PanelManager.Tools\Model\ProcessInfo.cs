﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Tools.Model
{
    internal class ProcessInfo
    {
        [JsonProperty(PropertyName = "pid")]
        public string PId { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "id")]
        public string ImageId { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "name")]
        public string Name { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "CPU")]
        public string CPU { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "mem")]
        public string Memory { get; set; } = string.Empty;
    }
}
