﻿using Microsoft.AspNetCore.SignalR;
using Siemens.PanelManager.Monitor.StaticData;
using Siemens.PanelManager.Monitor.Workers;
using System.Security.Cryptography;
using System.Text;

namespace Siemens.PanelManager.Monitor.Hubs
{
    public class AutoTestHub : Hub
    {
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        private const string PUBLICKEY = "-----BEGIN RSA PUBLIC KEY-----\r\nMIIBCgKCAQEApNvPKvV9xDkgBj2ol2qgoowtVHVplhMC40ML7xvj1Ok9a/AquhFX\r\nyPWbbsOSLNSX/BMIQM82OFQCHUM3A2e6Iscy1hZanrzToUveZpPxS5U07P9SOHRt\r\nPr5KKlXD7WvnOF9oa9D+os0ilY0BBFubFs82zThvruaxSFDPYnYh5vQtEkT6VMwf\r\nQ+WNYcNuRSjWk+6kVVJsR+zR5WJD+T2TaR76d6YhmKaEyG0pTde0+DRBSqO4Rg6A\r\nRWNskBEHuwlQkGhAZlRO0ThilJWVpppYRv4hyJhbn/RZWE7sIZIqere1LhZr/vVI\r\nCSppNOv8bbJK5rXNtBnclQDZXcaWbPSHZQIDAQAB\r\n-----END RSA PUBLIC KEY-----";

        private ILogger<AutoTestHub> _logger;
        private IServiceProvider _provider;

        public AutoTestHub(ILogger<AutoTestHub> logger, IServiceProvider provider)
        {
            _logger = logger;
            _provider = provider;
        }
        public override Task OnConnectedAsync()
        {
            ConnectManager.UnknowConnect.Queue.Enqueue(Context);
            return base.OnConnectedAsync();
        }

        public override Task OnDisconnectedAsync(Exception? exception)
        {
            ConnectManager.ConnectedList.TryRemove(Context.ConnectionId, out _);
            _logger.LogDebug($"Disconnect {Context.ConnectionId}");
            return base.OnDisconnectedAsync(exception);
        }
        public bool Login(string code, string randronCode)
        {
            try
            {
                RSACryptoServiceProvider p = new RSACryptoServiceProvider();
                p.ImportFromPem(new ReadOnlySpan<char>(PRIVATEKEY.ToArray()));

                var decryptData = Encoding.UTF8.GetString(p.Decrypt(Convert.FromBase64String(code), false));
                var result = false;
                if (!string.IsNullOrEmpty(decryptData))
                {
                    var codes = decryptData.Split('|');

                    if (codes.Length > 3)
                    {
                        result = true;
                        if (!randronCode.Equals(codes[3]))
                        {
                            result = false;
                        }

                        if (!DateTime.TryParseExact(codes[2], "yyyyMMddHHmmss", null, System.Globalization.DateTimeStyles.None, out DateTime tokenTime)
                            || Math.Abs((int)(DateTime.Now - tokenTime).TotalMinutes) > 15m)
                        {
                            result = false;
                        }

                        if (result)
                        {
                            Context.Items.TryAdd("UseName", codes[0]);
                            Context.Items.TryAdd("AutoTestHasLogin", true);
                            ConnectManager.ConnectedList.TryAdd(Context.ConnectionId, Context);
                        }
                    }
                }
                return result;
            }
            catch
            {
                return false;
            }
        }

        #region AutoTest
        public async Task StartAutoTest()
        {
            var worker = _provider.GetService<AutoTestWorker>();
            if (worker != null) 
            {
                await Groups.AddToGroupAsync(Context.ConnectionId, Constants.Group_AutoTestClient);
                var source = new CancellationTokenSource();
                await worker.StartAsync(source.Token);
            }
        }
        #endregion 
    }
}
