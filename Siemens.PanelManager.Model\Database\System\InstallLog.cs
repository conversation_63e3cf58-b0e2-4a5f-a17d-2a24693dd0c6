﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.System
{
    [SugarTable("sys_install_log")]
    public class InstallLog
    {
        [SugarColumn(ColumnName = "Id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "system_version", IsNullable = false, Length = 256)]
        public string Version { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "file_hash", IsNullable = true, ColumnDataType = "varchar(10485760)")]
        public string? FileHash { get; set; }
        [SugarColumn(ColumnName = "log_time", IsNullable = false)]
        public DateTime LogTime { get; set; }
    }
}
