﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Alarm
{
    public class AlarmCountModel
    {
        public AlarmCountModel() 
        {
            
        }

        public AlarmCountModel(AlarmCountModel model)
        {
            HighCount = model.HighCount;
            MiddleCount = model.MiddleCount;
            LowCount = model.LowCount;
            BreakerTripCount = model.BreakerTripCount;
            AlarmCount = model.AlarmCount;
            UDCAlarmCount = model.UDCAlarmCount;
        }

        /// <summary>
        /// 高 的数量
        /// </summary>
        public int HighCount { get; set; }

        /// <summary>
        /// 中 的数量
        /// </summary>
        public int MiddleCount { get; set; }

        /// <summary>
        /// 低 的数量
        /// </summary>
        public int LowCount { get; set; }

        /// <summary>
        /// 脱扣事件数量
        /// </summary>
        public int BreakerTripCount { get; set; }

        /// <summary>
        /// 可编程告警数量
        /// </summary>
        public int AlarmCount { get; set; }

        /// <summary>
        /// Udc 告警数量
        /// </summary>
        public int UDCAlarmCount { get; set; }
    }
}
