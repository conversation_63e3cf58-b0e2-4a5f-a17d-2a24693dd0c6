﻿using Siemens.PanelManager.Monitor.Cmd;
using Siemens.PanelManager.Monitor.StaticData;

namespace Siemens.PanelManager.Monitor.Function
{
    static class DockerFunc
    {
        public static async Task RemoveImages(ILogger logger)
        {
            var images = await DockerCmd.Images(logger);

            var imageIds = images.Select(i => i.ID).ToArray();
            if (imageIds.Any())
            {
                await DockerCmd.ImageRM(logger, imageIds);
            }
        }

        public static async Task Upgrade(string tarFilePath, Dictionary<string, string>? dockerMapping, string newYamlFilePath, string oldYamlFilePath, ILogger logger)
        {
            await DockerCmd.Load(tarFilePath, logger);
            if (dockerMapping != null)
            {
                foreach (var mapping in dockerMapping)
                {
                    await DockerCmd.Tag(logger, mapping.Key, mapping.Value);
                }
            }
            await DockerComposeCmd.Down(oldYamlFilePath, logger);
            
            await Task.Delay(1000); // 等待3s
            await DockerComposeCmd.Up(newYamlFilePath, logger);
            await Task.Delay(3000); // 等待3s
            await DockerComposeCmd.Logs(newYamlFilePath, logger);
        }

        public static async Task RestartService(string tarFilePath, string newYamlFilePath, ILogger logger)
        {
            await DockerComposeCmd.Down(newYamlFilePath, logger);
            await Task.Delay(1000);
            await RemoveImages(logger);
            await Task.Delay(1000);
            await DockerCmd.Load(tarFilePath, logger);
            await DockerComposeCmd.Up(newYamlFilePath, logger);
            await Task.Delay(3000); // 等待3s
            await DockerComposeCmd.Logs(newYamlFilePath, logger);
        }

        public static async Task<DockerProcessStatus[]> GetAllProcessStatus(ILogger logger)
        {
            return await DockerCmd.PS(logger, true);
        }

    }
}
