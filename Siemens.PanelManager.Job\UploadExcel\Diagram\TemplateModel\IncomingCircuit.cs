﻿using Siemens.PanelManager.Job.UploadExcel.Diagram.TemplateModel.NodeTemplate;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Topology;

namespace Siemens.PanelManager.Job.UploadExcel.Diagram.TemplateModel
{
    /// <summary>
    /// 单线图中进线回路
    /// </summary>
    internal class IncomingCircuit: CircuitTemplateModelBase
    {
        private readonly int Vertical_Source_RelativeHigh = 320;

        private SourceNode _source;

        private List<NodeData> _nodes;
        private List<LineData> _lineDatas;
        private int _height = 0;
        private int _width = 30;

        private TransformerNode? _transformerNode;

        public IncomingCircuit(string? busBarName, CircuitModel model, Dictionary<string, GroupKeyMappingInfo> idTable)
            : this(busBarName, model, 1, idTable)
        {
            
        }
        public IncomingCircuit(string? busBarName, CircuitModel model, int direction, Dictionary<string, GroupKeyMappingInfo> idTable)
            : base(busBarName, model, idTable)
        {
            Direction = direction;
            if (model == null)
            {
                throw new CreateTemplateException("回路缺失信息");
            }
            if (model.AssetInfo == null)
            {
                throw new CreateTemplateException("回路缺失资产信息");
            }

            if (model.SubDevices == null || model.SubDevices.Count == 0)
            {
                throw new CreateTemplateException($"回路{model.AssetInfo.AssetNumber}-{model.AssetInfo.AssetName}缺失子设备");
            }

            if (model.SubDevices == null || !model.SubDevices.Any(d => d.AssetType == "MCCB" || d.AssetType == "ACB" || d.AssetType == "MCB"))
            {
                throw new CreateTemplateException($"回路{model.AssetInfo.AssetNumber}-{model.AssetInfo.AssetName}缺失断路器");
            }

            _nodes = new List<NodeData>();
            _lineDatas = new List<LineData>();
            _source = new SourceNode(busBarName)
            {
                Key = GetNewId(),
                AssetId = model.AssetInfo.Id
            };
            // 添加电源
            _nodes.Add(_source);
            var meterId = GetNewId();
            var breakerId = GetNewId();
            var labelId = GetNewId();

            // 断路器
            var breakerAsset = model.SubDevices.FirstOrDefault(d => d.AssetType == "MCCB" || d.AssetType == "ACB" || d.AssetType == "MCB");
            if (breakerAsset != null)
            {
                var breaker = new SwitchNode(busBarName)
                {
                    Model = breakerAsset.AssetModel ?? string.Empty,
                    AssetId = breakerAsset.Id,
                    AssetName = breakerAsset.AssetName ?? string.Empty,
                    DeviceType = breakerAsset.AssetType,
                    Angle = SwitchAngle,
                    CircuitId = model.AssetInfo.Id,
                    CircuitName = model.AssetInfo.CircuitName ?? string.Empty,
                    CircuitModel = model.AssetInfo.AssetType ?? string.Empty,
                    Key = breakerId,
                };
                _nodes.Add(breaker);

                if ("3WA".Equals(breakerAsset.AssetModel) || "3WL".Equals(breakerAsset.AssetModel))
                {
                    breaker.BreakerPosition = "2";
                }
            }

            //var breakerAssets = model.SubDevices.Where(d => d.AssetType == "MCCB" || d.AssetType == "ACB" || d.AssetType == "MCB" || d.AssetType == "GeneralDevice").ToList();
            //if (breakerAssets != null && breakerAssets.Any())
            //{
            //    foreach (var item in breakerAssets)
            //    {
            //        var breakerId = GetNewId();

            //        var breaker = new SwitchNode(busBarName)
            //        {
            //            Model = item.AssetModel ?? string.Empty,
            //            AssetId = item.Id,
            //            AssetName = item.AssetName ?? string.Empty,
            //            Angle = SwitchAngle,
            //            CircuitId = model.AssetInfo.Id,
            //            CircuitName = model.AssetInfo.CircuitName ?? string.Empty,
            //            CircuitModel = model.AssetInfo.AssetType ?? string.Empty,
            //            Key = breakerId,
            //        };

            //        if ("3WA".Equals(item.AssetModel) || "3WL".Equals(item.AssetModel) || "GeneralDevice".Equals(item.AssetModel))
            //        {
            //            breaker.BreakerPosition = "0";
            //        }

            //        _nodes.Add(breaker);
            //    }
            //}


            var meter = model.SubDevices.FirstOrDefault(d => d.AssetType == "Meter");
            if (meter == null)
            {
                meter = model.SubDevices.FirstOrDefault(d => d.AssetType == "GeneralDevice" && d.AssetModel == "GeneralDevice");
            }

            _nodes.Add(new MeterNode(busBarName)
            {
                Model = meter?.AssetModel ?? string.Empty,
                AssetId = meter?.Id ?? 0,
                AssetName = meter?.AssetName ?? string.Empty,
                Key = meterId
            });

            _nodes.Add(new LabelNode()
            {
                Text = model.AssetInfo.CircuitName ?? string.Empty,
                Key = labelId
            });

            #region 数据点位
            var dataPointId = GetNewId();
            _nodes.Add(new DataPointNode()
            {
                Name = model.AssetInfo.AssetName ?? string.Empty,
                ElectricalType = "Voltage",
                ElectricalName = "A 相电压",
                Key = dataPointId,
                BindAssetId = model.AssetInfo.Id,
                BindPointName = "Ua",
                Order = 1,
            });
            dataPointId = GetNewId();
            _nodes.Add(new DataPointNode()
            {
                Name = model.AssetInfo.AssetName ?? string.Empty,
                ElectricalType = "APhaseCurrent",
                ElectricalName = "A 相电流",
                Key = dataPointId,
                BindAssetId = model.AssetInfo.Id,
                BindPointName = "Ia",
                Order = 4,
            });

            dataPointId = GetNewId();
            _nodes.Add(new DataPointNode()
            {
                Name = model.AssetInfo.AssetName ?? string.Empty,
                ElectricalType = "Voltage",
                ElectricalName = "B 相电压",
                Key = dataPointId,
                BindAssetId = model.AssetInfo.Id,
                BindPointName = "Ub",
                Order = 2,
            });
            dataPointId = GetNewId();
            _nodes.Add(new DataPointNode()
            {
                Name = model.AssetInfo.AssetName ?? string.Empty,
                ElectricalType = "BPhaseCurrent",
                ElectricalName = "B 相电流",
                Key = dataPointId,
                BindAssetId = model.AssetInfo.Id,
                BindPointName = "Ib",
                Order = 5,
            });

            dataPointId = GetNewId();
            _nodes.Add(new DataPointNode()
            {
                Name = model.AssetInfo.AssetName ?? string.Empty,
                ElectricalType = "Voltage",
                ElectricalName = "C 相电压",
                Key = dataPointId,
                BindAssetId = model.AssetInfo.Id,
                BindPointName = "Uc",
                Order = 3,
            });
            dataPointId = GetNewId();
            _nodes.Add(new DataPointNode()
            {
                Name = model.AssetInfo.AssetName ?? string.Empty,
                ElectricalType = "CPhaseCurrent",
                ElectricalName = "C 相电流",
                Key = dataPointId,
                BindAssetId = model.AssetInfo.Id,
                BindPointName = "Ic",
                Order = 6,
            });

            dataPointId = GetNewId();
            _nodes.Add(new DataPointNode()
            {
                Name = model.AssetInfo.AssetName ?? string.Empty,
                ElectricalType = "Power",
                ElectricalName = "有功功率",
                Key = dataPointId,
                BindAssetId = model.AssetInfo.Id,
                BindPointName = "P",
                Order = 7,
            });
            dataPointId = GetNewId();
            _nodes.Add(new DataPointNode()
            {
                Name = model.AssetInfo.AssetName ?? string.Empty,
                ElectricalType = "reactivePower",
                ElectricalName = "无功功率",
                Key = dataPointId,
                BindAssetId = model.AssetInfo.Id,
                BindPointName = "Q",
                Order = 8,
            });
            dataPointId = GetNewId();
            _nodes.Add(new DataPointNode()
            {
                Name = model.AssetInfo.AssetName ?? string.Empty,
                ElectricalType = "Frequency",
                ElectricalName = "频率",
                Key = dataPointId,
                BindAssetId = model.AssetInfo.Id,
                BindPointName = "F",
                Order = 9,
            });
            dataPointId = GetNewId();
            _nodes.Add(new DataPointNode()
            {
                Name = model.AssetInfo.AssetName ?? string.Empty,
                ElectricalType = "PowerFactor",
                ElectricalName = "功率因数",
                Key = dataPointId,
                BindAssetId = model.AssetInfo.Id,
                BindPointName = "PowFactor",
                Order = 10,
            });
            #endregion

            #region 计算坐标
            CalculateLocation();
            #endregion

            #region 添加线
            LinkLine(_nodes, _lineDatas);
            #endregion
        }

        /// <summary>
        /// 计算回路中设备对应的相对位置
        /// </summary>
        private void CalculateLocation()
        {
            switch (Direction)
            {
                case 1:
                    {
                        var switchModel = _nodes.First(d => d.NodeType == NodeType.Switch);
                        switchModel.LocationX = 0;
                        switchModel.LocationY = 20;

                        var label = _nodes.First(d => d.NodeType == NodeType.Label);
                        label.LocationX = 160;
                        label.LocationY = -170;

                        #region DataPoint
                        var dataPoints = _nodes.Where(d => d.NodeType == NodeType.DataPoint)
                            .OfType<DataPointNode>()
                            .OrderBy(d => d.Order).ToArray();

                        for (int i = 0; i < dataPoints.Length; i++)
                        {
                            var dataPoint = dataPoints[i];
                            SetDataPointsVertical(i, dataPoint, 160, -110);
                        }
                        #endregion

                        var meterModel = _nodes.First(d => d.NodeType == NodeType.Meter);
                        meterModel.LocationX = 0;
                        meterModel.LocationY = 150;
                        var source = _nodes.First(d => d.NodeType == NodeType.Source);
                        source.LocationX = 0;
                        source.LocationY = Vertical_Source_RelativeHigh;

                        _width = 250;
                        _height = Vertical_Source_RelativeHigh;
                    }
                    break;
                default:
                    {
                        var switchModel = _nodes.First(d => d.NodeType == NodeType.Switch);
                        switchModel.LocationX = 20;
                        switchModel.LocationY = 0;
                        var label = _nodes.First(d => d.NodeType == NodeType.Label);
                        label.LocationX = 30;
                        label.LocationY = -50;

                        #region DataPoint
                        var dataPoints = _nodes.Where(d => d.NodeType == NodeType.DataPoint)
                            .OrderBy(d => d.Key).ToArray();

                        for (int i = 0; i < dataPoints.Length; i++)
                        {
                            var dataPoint = dataPoints[i];
                            SetDataPointsCrosswise(i, dataPoint, 30, 100);
                        }
                        #endregion

                        var meterModel = _nodes.First(d => d.NodeType == NodeType.Meter);
                        meterModel.LocationX = 120;
                        meterModel.LocationY = 0;
                        var source = _nodes.First(d => d.NodeType == NodeType.Source);
                        source.LocationX = 220;
                        source.LocationY = 0;

                        _height = 180;
                        _width = 500;
                    }
                    break;
            }
        }

        public override void Rotation(int action = -1)
        {
            bool needCalculate = false;
            switch (action)
            {
                // 90度翻转
                case -1:
                    {
                        if (Direction == 0)
                        {
                            Direction = 1;
                            needCalculate = true;
                        }
                        else if (Direction == 1)
                        {
                            Direction = 0;
                            needCalculate = true;
                        }
                        break;
                    }
                // 强制横排
                case 0:
                    {
                        if (Direction != 0)
                        {
                            Direction = 0;
                            needCalculate = true;
                        }
                        break;
                    }
                // 强制纵排
                case 1:
                    {
                        if (Direction != 1)
                        {
                            Direction = 1;
                            needCalculate = true;
                        }
                        break;
                    }
                default: break;
            }

            if (needCalculate)
            {
                RotationNodes(_nodes);
                CalculateLocation();
                foreach(var node in _nodes) 
                {
                    SetNodeLocation(node);
                }
            }
        }

        /// <summary>
        /// 横排时数据点位的排布
        /// </summary>
        /// <param name="index"></param>
        /// <param name="node"></param>
        /// <param name="baseX"></param>
        /// <param name="baseY"></param>
        private void SetDataPointsCrosswise(int index, NodeData node, int baseX, int baseY)
        {
            var row = index / 2;
            var col = index % 2;
            node.LocationX = col * 150 + baseX;
            node.LocationY =  baseY + row * 40;
        }

        /// <summary>
        /// 竖排时数据点位的排布
        /// </summary>
        /// <param name="index"></param>
        /// <param name="node"></param>
        /// <param name="baseX"></param>
        /// <param name="baseY"></param>
        private void SetDataPointsVertical(int index, NodeData node, int baseX, int baseY)
        {
            node.LocationX = baseX;
            node.LocationY = baseY + index * 40;
        }

        /// <summary>
        /// 添加变压器节点
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public int AddTransformerNode(TransformerModel model, Func<int> createIdFunc, string leftOrRight)
        {
            var width = 0;
            if(_source == null || !_source.Key.HasValue)
            {
                return width;
            }

            switch (Direction)
            {
                case 1:
                    {
                        int x = 0;
                        int dataPointX = 0;
                        int labelX = 0;
                        width = 350;
                        if ("Right".Equals(leftOrRight))
                        {
                            x = -310;
                            dataPointX = -150;
                            labelX = -150;
                        }
                        else
                        {
                            x = 310;
                            dataPointX = 450;
                            labelX = 450;
                        }

                        var labelNode = new LabelNode()
                        {
                            Text = model.AssetInfo.AssetName,
                            Key = createIdFunc(),
                            LocationX = labelX,
                            LocationY = -170,
                            IsTransformer = true
                        };
                        _nodes.Add(labelNode);

                        var dataPoints = new List<DataPointNode>()
                        {
                            new DataPointNode
                            {
                                AssetName = model.AssetInfo.AssetName ?? string.Empty,
                                ElectricalName = "A相电压",
                                ElectricalType = "Voltage",
                                BindAssetId = model.AssetInfo.Id,
                                BindPointName = "TR_Low_Ua",
                                Key = createIdFunc(),
                                Unit = "V",
                                IsTransformer = true,
                                Order = 1
                            },
                            new DataPointNode
                            {
                                AssetName = model.AssetInfo.AssetName ?? string.Empty,
                                ElectricalName = "B相电压",
                                ElectricalType = "Voltage",
                                BindAssetId = model.AssetInfo.Id,
                                BindPointName = "TR_Low_Ub",
                                Key = createIdFunc(),
                                Unit = "V",
                                IsTransformer = true,
                                Order = 2
                            },
                            new DataPointNode
                            {
                                AssetName = model.AssetInfo.AssetName ?? string.Empty,
                                ElectricalName = "C相电压",
                                ElectricalType = "Voltage",
                                BindAssetId = model.AssetInfo.Id,
                                BindPointName = "TR_Low_Uc",
                                Key = createIdFunc(),
                                Unit = "V",
                                IsTransformer = true,
                                Order = 3
                            },
                            new DataPointNode
                            {
                                AssetName = model.AssetInfo.AssetName ?? string.Empty,
                                ElectricalName = "A相电流",
                                ElectricalType = "Current",
                                BindAssetId = model.AssetInfo.Id,
                                BindPointName = "TR_Low_Ia",
                                Key = createIdFunc(),
                                Unit = "A",
                                IsTransformer = true,
                                Order = 4
                            },
                            new DataPointNode
                            {
                                AssetName = model.AssetInfo.AssetName ?? string.Empty,
                                ElectricalName = "B相电流",
                                ElectricalType = "Current",
                                BindAssetId = model.AssetInfo.Id,
                                BindPointName = "TR_Low_Ib",
                                Key = createIdFunc(),
                                Unit = "A",
                                IsTransformer = true,
                                Order = 5
                            },
                            new DataPointNode
                            {
                                AssetName = model.AssetInfo.AssetName ?? string.Empty,
                                ElectricalName = "C相电流",
                                ElectricalType = "Current",
                                BindAssetId = model.AssetInfo.Id,
                                BindPointName = "TR_Low_Ic",
                                Key = createIdFunc(),
                                Unit = "A",
                                IsTransformer = true,
                                Order = 6
                            },
                            new DataPointNode
                            {
                                AssetName = model.AssetInfo.AssetName ?? string.Empty,
                                ElectricalName = "A相温度",
                                ElectricalType = "Temperature",
                                BindAssetId = model.AssetInfo.Id,
                                BindPointName = "temperature_tr_phaseA",
                                Key = createIdFunc(),
                                Unit = "℃",
                                IsTransformer = true,
                                Order = 7
                            },
                            new DataPointNode
                            {
                                AssetName = model.AssetInfo.AssetName ?? string.Empty,
                                ElectricalName = "B相温度",
                                ElectricalType = "Temperature",
                                BindAssetId = model.AssetInfo.Id,
                                BindPointName = "temperature_tr_phaseB",
                                Key = createIdFunc(),
                                Unit = "℃",
                                IsTransformer = true,
                                Order = 8
                            },
                            new DataPointNode
                            {
                                AssetName = model.AssetInfo.AssetName ?? string.Empty,
                                ElectricalName = "C相温度",
                                ElectricalType = "Temperature",
                                BindAssetId = model.AssetInfo.Id,
                                BindPointName = "temperature_tr_phaseC",
                                Key = createIdFunc(),
                                Unit = "℃",
                                IsTransformer = true,
                                Order = 9
                            }
                        };

                        var pointNode = new PointNode();
                        pointNode.LocationX = x;
                        pointNode.LocationY = Vertical_Source_RelativeHigh;
                        pointNode.Key = createIdFunc();
                        pointNode.IsTransformer = true;

                        _nodes.Add(pointNode);

                        var node = new TransformerNode();
                        node.AssetId = model.AssetInfo.Id;
                        node.Name = model.AssetInfo.AssetName ?? string.Empty;
                        node.LocationX = x;
                        node.LocationY = Vertical_Source_RelativeHigh - 200;
                        node.Angle = 90;
                        node.Key = createIdFunc();
                        _transformerNode = node;
                        _nodes.Add(node);

                        for (var i = 0; i < dataPoints.Count; i++)
                        {
                            var point = dataPoints[i];
                            SetDataPointsVertical(i, point, dataPointX, -110);
                            _nodes.Add(point);
                        }

                        _lineDatas.Add(new LineData()
                        {
                            From = _source.Key ?? 0,
                            To = pointNode.Key ?? 0,
                        });

                        _lineDatas.Add(new LineData()
                        {
                            From = pointNode.Key ?? 0,
                            To = node.Key ?? 0,
                        });
                        break;
                    }
                case 0:
                    {
                        // 横向无法添加变压器

                        //var node = new TransformerNode();
                        //node.AssetId = model.AssetInfo.Id;
                        //node.Name = model.AssetInfo.AssetName ?? string.Empty;
                        //node.LocationX = 350;
                        //node.LocationY = 0;
                        //node.Key = createIdFunc();
                        //node.Angle = 180;

                        //_nodes.Add(node);

                        //width = 120;

                        //_lineDatas.Add(new LineData()
                        //{
                        //    From = _source.Key ?? 0,
                        //    To = node.Key ?? 0
                        //});
                        break;
                    }
                default: break;
            }
            return width;
        }

        public override void FinishLoading()
        {
            var id = Model.AssetInfo.Id.ToString();
            var list = new List<int>();
            var mappingInfo = new GroupKeyMappingInfo()
            {
                Level = AssetLevel.Circuit.ToString(),
                List = list
            };

            var transformerKeys = new List<int>();
            foreach (var n in NodeDatas)
            {
                if (!n.Key.HasValue) continue;
                if (n.IsTransformer)
                {
                    transformerKeys.Add(n.Key.Value);
                }
                else
                {
                    list.Add(n.Key.Value);
                }
            }

            if (!IdTable.TryAdd(id, mappingInfo))
            {
                IdTable[id] = mappingInfo;
            }

            if (_transformerNode != null && _transformerNode.AssetId.HasValue && _transformerNode.Key.HasValue)
            {
                IdTable.TryAdd(_transformerNode.AssetId.Value.ToString(), new GroupKeyMappingInfo()
                {
                    Level = AssetLevel.Transformer.ToString(),
                    List = transformerKeys
                });
            }
        }

        public override int Height => _height;
        public override int Width => _width;
        public override NodeData[] NodeDatas => _nodes.ToArray();
        public override LineData[] LineDatas => _lineDatas.ToArray();
        public override LineData[] Connectors => _lineDatas.Where(l=>l.IsConnector).ToArray();
    }
}
