{"info": {"_postman_id": "ea4fff6f-96bf-4886-b209-35fc681d39b9", "name": "22使用管理员账号进入panel manager资产管理中的资产列表菜单，进入回路详情页点击编辑按钮修改信息", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 8", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "获取所有资产详情 Copy 15", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "let Areaid = pm.response.json().data[0].id//获取Areaid\r", "pm.environment.set('Areaid',Areaid)//把id保存到全局变量中\r", "\r", "let Substationid = pm.response.json().data[0].children[2].id//获取Substationid\r", "pm.environment.set('Substationid',Substationid)//把id保存到全局变量中\r", "\r", "let Panelid = pm.response.json().data[0].children[2].children[2].id//获取Panelid\r", "pm.environment.set('Panelid',Panelid)//把id保存到全局变量中\r", "\r", "let Circuitid = pm.response.json().data[0].children[2].children[2].children[2].id//获取Circuitid\r", "pm.environment.set('Circuitid',Circuitid)//把id保存到全局变量中\r", "\r", "let Deviceid = pm.response.json().data[0].children[2].children[2].children[2].children[2].id//获取Deviceid\r", "pm.environment.set('<PERSON>ce<PERSON>',<PERSON>ceid)//把id保存到全局变量中\r", "\r", "pm.test(\"获取所有资产详情\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"id\",\"assetName\",\"assetNumber\",\"level\",\"location\",\"children\",\"type\",\"model\");\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Asset", "host": ["{{baseUrl}}"], "path": ["api", "v1", "<PERSON><PERSON>"]}}, "response": []}, {"name": "更新资产 Copy 4", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"type\": \"update\",\r\n  \"currentId\": {{Circuitid}},\r\n  \"assetData\": {\r\n    \"id\": {{Circuitid}},\r\n    \"assetName\": \"回路01\",\r\n    \"person\": \"string\",\r\n    \"tel\": \"123123123\",\r\n    \"description\": \"string\",\r\n    \"assetNumber\": \"\",\r\n    \"level\": \"Circuit\",\r\n    \"type\": \"string\",\r\n    \"model\": \"string\",\r\n    \"mlfb\": \"string\",\r\n    \"factory\": \"string\",\r\n    \"location\": \"string\",\r\n    \"pointData\": \"string\",\r\n    \"installDate\": \"2023-01-02T12:26:11.769Z\",\r\n    \"useScene\": \"string\",\r\n    \"meterType\": \"string\",\r\n    \"circuitName\": \"string\",\r\n    \"drawing\": [\r\n      {\r\n        \"id\": 0,\r\n        \"name\": \"string\",\r\n        \"url\": \"string\"\r\n      }\r\n    ],\r\n    \"img\": [\r\n      {\r\n        \"id\": 0,\r\n        \"name\": \"string\",\r\n        \"url\": \"string\"\r\n      }\r\n    ]\r\n  }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/Asset", "host": ["{{baseUrl}}"], "path": ["api", "v1", "<PERSON><PERSON>"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}]}