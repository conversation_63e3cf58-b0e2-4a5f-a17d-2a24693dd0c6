﻿using InfluxDB.Client.Writes;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.DeviceDataFlow.StaticData
{
    internal class InfluxDBQueue
    {
        private ConcurrentQueue<PointData> _queue = new ConcurrentQueue<PointData>();

        public void Append(PointData point)
        {
            _queue.Enqueue(point);
        }

        public PointData[] Read()
        {
            var points = new List<PointData>();
            while (_queue.TryDequeue(out var point))
            {
                points.Add(point);
            }
            return points.ToArray();
        }
    }
}
