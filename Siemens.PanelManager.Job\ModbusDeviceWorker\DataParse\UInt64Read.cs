﻿using System;
using System.Buffers.Binary;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.ModbusDeviceWorker.DataParse
{
    public class UInt64Read : PanelModbusRead
    {
        public UInt64Read(bool isBigEndian, string? parseMode, float factor, float customFactor, float intercept)
            : base(isBigEndian, parseMode, factor, customFactor, intercept)
        {
        }

        public override string ReadData(ReadOnlySpan<byte> source)
        {
            string readValue = string.Empty;
            if (!string.IsNullOrEmpty(ParseMode))
            {
                if (ParseMode == "ABCD")
                {
                    readValue = Calculate(BinaryPrimitives.ReadUInt64BigEndian(source));
                }
                else if (ParseMode == "DCBA")
                {
                    readValue = Calculate(BinaryPrimitives.ReadUInt64LittleEndian(source));
                }
                else if (ParseMode == "CDAB")
                {
                    // 4个寄存器的方式待实现
                    readValue = Calculate(BinaryPrimitives.ReadUInt64BigEndian(source));
                    ////CDAB,2,3,0,1 => ABCD
                    //readValue = (BinaryPrimitives.ReadUInt64BigEndian([source[2], source[3], source[0], source[1]]) * Factor).ToString();
                }
                else
                {
                    // 4个寄存器的方式待实现
                    readValue = Calculate(BinaryPrimitives.ReadUInt64BigEndian(source));
                    ////BADC,1,0,3,2 =>ABCD
                    //readValue = (BinaryPrimitives.ReadUInt64BigEndian([source[1], source[0], source[3], source[2]]) * Factor).ToString();
                }
            }
            else
            {
                if (IsBigEndian)
                {
                    readValue = Calculate(BinaryPrimitives.ReadUInt64BigEndian(source));
                }
                else
                {
                    readValue = Calculate(BinaryPrimitives.ReadUInt64LittleEndian(source));
                }
            }

            return readValue;
        }
    }
}
