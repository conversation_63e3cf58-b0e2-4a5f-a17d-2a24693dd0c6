﻿using InfluxDB.Client.Api.Domain;
using InfluxDB.Client;
using Siemens.InfluxDB.Helper.Database;
using Siemens.InfluxDB.Helper.Interface;
using System.Text;
using Siemens.InfluxDB.Helper.Enum;
using Siemens.InfluxDB.Helper.FluxModel;

namespace Siemens.InfluxDB.Helper.Client
{
    public class SelectableClient<T> : ISelectable<T>
        where T : IInfluxData
    {
        private IQueryApi _queryApi;
        private Bucket _bucket;
        private Organization _organization;
        private IClientBase _clientBase;
        private SearchResultHelper<T> _helper;
        private GroupFunctionEnum[] _groupFunctionEnum;
        private DataHelperBase _dataHelper;
        internal SelectableClient(IClientBase clientBase,
            DataHelperBase dataHelper,
            SearchResultHelper<T> resultHelper,
            IQueryApi queryApi, 
            Organization organization, 
            Bucket bucket,
            List<GroupFunctionEnum> groups)
        {
            _dataHelper = dataHelper;
            _queryApi = queryApi;
            _bucket = bucket;
            _organization = organization;
            _clientBase = clientBase;
            _helper = resultHelper;
            _groupFunctionEnum = groups.ToArray();
        }
        public void Dispose()
        {
            _clientBase.Dispose();
        }

        public string ToFlux()
        {
            if (_dataHelper.AggregateWindow != null)
            {
                var query = new StringBuilder(100);
                var baseQuery = _clientBase.ToFluxNotIncludeGroup();
                foreach (var function in _groupFunctionEnum)
                {
                    var tempQuery = new StringBuilder(100);
                    tempQuery.AppendLine(baseQuery);
                    var group = new AggregateWindow()
                    {
                        FunctionName = function.GetGroupFunctionName(),
                        IntervalLevel = _dataHelper.AggregateWindow.IntervalLevel,
                        IntervalNum = _dataHelper.AggregateWindow.IntervalNum,
                    };

                    group.AppendFlux(tempQuery, _dataHelper.Limit, _dataHelper.Sort, _dataHelper.First, _dataHelper.Last);
                    query.AppendLine(tempQuery.ToString());
                    query.AppendLine();
                }

                return query.ToString();
            }

            if (_groupFunctionEnum.Length > 1 || (_groupFunctionEnum.Length == 1
                && _groupFunctionEnum[0] != GroupFunctionEnum.Default))
            {
                throw new InfluxDBHelperException("聚合函数必须设置GroupByTime");
            }
            return _clientBase.ToFlux();
        }

        public string ToFluxNotIncludeGroup()
        {
            return _clientBase.ToFluxNotIncludeGroup();
        }

        public async Task<IList<T>> ToListAsync()
        {
            var query = ToFlux();
            var data = await _queryApi.QueryAsync(query, _organization.Id);
            return _helper.GetValues(data);
        }
    }
}
