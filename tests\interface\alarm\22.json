{"info": {"_postman_id": "2fd5dbd9-3ebd-4c76-bb54-0e08a50d5683", "name": "22使用管理员账号进入panel manager告警管理中的告警列表菜单，筛选事件类型是告警，点击详情中基本信息的确认警告按钮", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 9", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "获取事件类型为告警的告警列表 Copy 6", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "\r", "let id0 = pm.response.json().items[0].id\r", "pm.environment.set(\"id0\", id0);\r", "console.log(id0)"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm?eventType=0&page=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm"], "query": [{"key": "eventType", "value": "0"}, {"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}]}}, "response": []}, {"name": "查看详情页基本信息、操作状态、 快照信息 Copy 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"点击告警按钮成功\", function () {\r", "    var jsonData = pm.response.json()\r", "    pm.expect(jsonData[\"data\"]).to.eql(true);\r", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm/{{id0}}/ChangeLogStatus?action=finish", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm", "{{id0}}", "ChangeLogStatus"], "query": [{"key": "action", "value": "finish"}]}}, "response": []}, {"name": "添加新的告警", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm?message=test&ruleId=0&eventType=0", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm"], "query": [{"key": "message", "value": "test"}, {"key": "ruleId", "value": "0"}, {"key": "eventType", "value": "0"}]}}, "response": []}, {"name": "获取事件类型为告警的告警列表 Copy 7", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "\r", "let id0 = pm.response.json().items[0].id\r", "pm.environment.set(\"id0\", id0);\r", "console.log(id0)"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm?eventType=0&page=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm"], "query": [{"key": "eventType", "value": "0"}, {"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}]}}, "response": []}, {"name": "查看详情页基本信息、操作状态、 快照信息 Copy 3", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"点击告警按钮成功\", function () {\r", "    var jsonData = pm.response.json()\r", "    pm.expect(jsonData[\"data\"]).to.eql(true);\r", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm/{{id0}}/ChangeLogStatus?action=finish", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm", "{{id0}}", "ChangeLogStatus"], "query": [{"key": "action", "value": "finish"}]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 1000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", ""]}}]}