<log4net>
	<appender name="RollingLogFileAppender" type="log4net.Appender.RollingFileAppender">
		<!--日志路径-->
		<param name= "File" value= "logs/"/>
		<!--多线程时采用最小锁定-->
		<lockingModel type="log4net.Appender.FileAppender+MinimalLock"/>
		<!--是否是向文件中追加日志-->
		<param name= "AppendToFile" value= "true"/>
		<!--log保留天数-->
		<param name= "MaxSizeRollBackups" value= "10"/>
		<!--日志文件名是否是固定不变的-->
		<param name= "StaticLogFileName" value= "false"/>
		<!--日志文件名格式为:2022-05-22.log-->
		<param name= "DatePattern" value= "yyyy-MM-dd'.log'"/>
		<!--日志根据日期滚动-->
		<param name= "RollingStyle" value= "Date"/>
		<layout type="log4net.Layout.PatternLayout">
			<param name="ConversionPattern" value="%n%d [%t] %-5p %c [%L] - %m %n" />
		</layout>
	</appender>
	<appender name="apiLogAppender" type="log4net.Appender.RollingFileAppender">
		<!--日志路径-->
		<param name= "File" value= "logs/"/>
		<!--多线程时采用最小锁定-->
		<lockingModel type="log4net.Appender.FileAppender+MinimalLock"/>
		<!--是否是向文件中追加日志-->
		<param name= "AppendToFile" value= "true"/>
		<!--log保留天数-->
		<param name= "MaximumFileSize" value= "10MB"/>
		<!--日志文件名是否是固定不变的-->
		<param name= "StaticLogFileName" value= "false"/>
		<!--日志文件名格式为:2022-05-22.log-->
		<param name= "DatePattern" value= "yyyy-MM-dd'-api.log'"/>
		<!--日志根据日期滚动-->
		<param name= "RollingStyle" value= "Date"/>
		<layout type="log4net.Layout.PatternLayout">
			<param name="ConversionPattern" value="%n%d [%t] %-5p %c [%L] - %m %n" />
		</layout>
	</appender>

	<logger name="ApiLogger">
		<level value="OFF" />
		<appender-ref ref="apiLogAppender" />
	</logger>
	<root>
		<!--(高) OFF > FATAL > ERROR > WARN > INFO > DEBUG > ALL (低) -->
		<level value="All" />
		<!--<appender-ref ref="ColoredConsoleAppender"/>-->
		<appender-ref ref="RollingLogFileAppender"/>
	</root>
</log4net>