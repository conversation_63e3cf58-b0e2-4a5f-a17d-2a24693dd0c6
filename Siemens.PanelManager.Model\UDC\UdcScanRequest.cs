﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.Model.UDC
{
    public class UdcScanRequest
    {
        [JsonProperty(PropertyName = "type")]
        public string? Type { get; set; }

        [JsonProperty(PropertyName = "options")]
        public UdcScanRequestOptions? Options { get; set; }
    }

    public class UdcScanRequestOptions
    {
        [JsonProperty(PropertyName = "interface")]
        public string? Interface { get; set; }

        [JsonProperty(PropertyName = "type_name")]
        public string? TypeName { get; set; }

        [JsonProperty(PropertyName = "address")]
        public string? Address { get; set; }

        [JsonProperty(PropertyName = "result")]
        public UdcScanRequestResult? Result { get; set; }
    }

    public class UdcScanRequestResult
    {
        [JsonProperty(PropertyName = "format")]
        public string? Format { get; set; }
    }

}
