﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Siemens.PanelManager.Job.UploadExcel._3D.Model;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.Topology;
using Siemens.PanelManager.Model.Topology3D;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Diagnostics.Tracing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.UploadExcel._3D
{
    /// <summary>
    /// 3D 图的主入口
    /// </summary>
    internal class Topology3DModelBuilder
    {
        public async Task<bool> CreateTopology(List<PanelModel> panelList, ISqlSugarClient client, string createdBy, List<string> errors, List<TransformerModel> models)
        {
            if (panelList == null || panelList.Count <= 0) return false;
            ChangePanelHeightAndWidth(panelList);
            var substations = panelList.Select(p => p.ParentAsset).Distinct().ToList();
            bool hasCreate = false;
            foreach (var substation in substations)
            {
                if (substation == null) continue;
                #region 计算图形
                var main = new Topology3D();
                main.Nodes = new List<NodeBase3D>();
                main.PanelList = new Dictionary<string, JToken>();
                var cabinetList = new List<ICabinetNode>();
                // 获取最大的行号
                var lineNumber = panelList.Max(p => p.LineNo);
                var maxTransLineNo = 0;
                if (models != null && models.Count > 0)
                {
                    maxTransLineNo = models.Max(p => p.LineNo);
                }

                if (maxTransLineNo > lineNumber)
                {
                    lineNumber = maxTransLineNo;
                }
                decimal z = 0m;
                decimal maxX = 0m;

                // 遍历行号 逐行构建3D图
                for (var i = lineNumber; i > 0; i--)
                {
                    decimal totalNodeWidth = 0m;
                    var panelModels = panelList.Where(p => p.LineNo == i && p.ParentAsset== substation).ToArray();
                    TransformerModel[] transformers;
                    if (models != null && models.Count > 0)
                    {
                        transformers = models.Where(p => p.LineNo == i && p.ParentAsset == substation).ToArray();
                    }
                    else
                    {
                        transformers = new TransformerModel[0];
                    }
                    if (panelModels.Length <= 0) continue;
                    decimal x = 0m;
                    int rowNumber = 0;
                    if(panelModels.Length > 0)
                    {
                        rowNumber = panelModels.Max(p => p.RowNo);
                    }
                    var transformerRowNo = 0;
                    if (transformers.Length > 0)
                    {
                        transformerRowNo = transformers.Max(p => p.RowNo);
                    }

                    if (transformerRowNo > rowNumber)
                    {
                        rowNumber = transformerRowNo;
                    }
                    var groupNode = new GroupNode();
                    main.Nodes.Add(groupNode);
                    IPanelModel? lastModel = null;
                    for (var row = 1; row <= rowNumber; row++)
                    {
                        var panels = panelModels.Where(p => p.RowNo == row).ToArray();
                        var transformer = transformers.Where(p => p.RowNo == row).FirstOrDefault();
                        //if (panels.Length > 1 && !errors.Contains("Topology_ReuseRowNo"))
                        //{
                        //    errors.Add("Topology_ReuseRowNo");
                        //}

                        var model = panels.FirstOrDefault();
                        decimal partition = 0m;
                        NodeBase3D node;
                        if (model != null)
                        {
                            var cabinet = new CabinetNode(model);
                            cabinetList.Add(cabinet);
                            node = cabinet;
                            cabinet.GroupNode = groupNode;
                            if (lastModel != null && (!model.BusBarStr.Contains(lastModel.BusBarStr) && !lastModel.BusBarStr.Contains(model.BusBarStr)))
                            {
                                partition = node.Size.Width * 2m;
                            }
                            lastModel = model;
                        }
                        else if (transformer != null)
                        {
                            var transformerNode = new TransformerNode(transformer);
                            cabinetList.Add(transformerNode);
                            node = transformerNode;
                            transformerNode.GroupNode = groupNode;
                            if (lastModel != null && (!transformer.BusBarStr.Contains(lastModel.BusBarStr) && !lastModel.BusBarStr.Contains(transformer.BusBarStr)))
                            {
                                partition = node.Size.Width * 2m;
                            }
                            lastModel = transformer;
                        }
                        else
                        {
                            // 添加地板元素
                            var floor = new FloorNode();
                            floor.SetWidth(1000);
                            node = floor;
                            groupNode.Nodes.Add(floor);
                        }

                        node.Position.X = x + node.Size.Width / 2m + 4m + partition;
                        node.Position.Z = z;
                        x = node.Position.X + node.Size.Width / 2m;
                        totalNodeWidth += node.Size.Width + partition;
                    }
                    if (totalNodeWidth > 0)
                    {
                        var floorNode = new FloorNode();
                        floorNode.SetWidth(totalNodeWidth);
                        floorNode.Position.X = totalNodeWidth / 2;
                        floorNode.Position.Z = z + 1100;
                        groupNode.Nodes.Add(floorNode);
                    }

                    if (maxX < x)
                    {
                        maxX = x;
                    }

                    z += 6000;
                }

                //Todo 添加门

                var moveX = -maxX / 2;
                var moveZ = -z / 2;

                // 开关柜抽离，用singe 模型替代原模型
                foreach (var cabinet in cabinetList)
                {
                    var single = cabinet.GetSingalNode();
                    if (cabinet.GroupNode == null)
                    {
                        main.Nodes.Add(single);
                    }
                    else
                    {
                        cabinet.GroupNode.Nodes.Add(single);
                    }
                }

                main.Nodes.ForEach(n => n.Move(moveX, moveZ));

                maxX += 3000;
                z += 3000;
                var groundNode = new GroundNode();
                groundNode.Size.Width = maxX;
                groundNode.Size.Depth = z;
                main.Nodes.Add(groundNode);
                #endregion
                #region 保存数据
                try
                {
                    client.Ado.BeginTran();
                    foreach (var cabinet in cabinetList)
                    {
                        var newCabinet = cabinet.GetNew();
                        var list = new List<ICabinetNode>
                        {
                            newCabinet
                        };
                        var topoplogyId = await client.Insertable<TopologyInfo>(new TopologyInfo()
                        {
                            Name = $"{cabinet.Name}-3D",
                            Code = string.Format(cabinet.AssetInfo.DrawingCode ?? "{0}-000", "3D"),
                            Type = "3D",
                            Data = JsonConvert.SerializeObject(list),
                            TopologyFlag = Guid.NewGuid().ToString(),
                            CreatedBy = createdBy,
                            CreatedTime = DateTime.Now,
                            UpdatedBy = createdBy,
                            UpdatedTime = DateTime.Now,
                        }).ExecuteReturnIdentityAsync();

                        cabinet.AssetInfo.Topology3DId = topoplogyId;
                        cabinet.AssetInfo.UpdatedBy = createdBy;
                        cabinet.AssetInfo.UpdatedTime = DateTime.Now;
                        await client.Updateable(cabinet.AssetInfo).ExecuteCommandAsync();

                        main.PanelList.Add(cabinet.Id, JToken.FromObject(topoplogyId));
                    }

                    var mainId = await client.Insertable(new TopologyInfo()
                    {
                        Name = $"{substation.AssetName}-3D",
                        Code = string.Format(substation.DrawingCode ?? "{0}-000", "3D"),
                        Type = "3D",
                        Data = JsonConvert.SerializeObject(main.Nodes),
                        TopologyFlag = Guid.NewGuid().ToString(),
                        CreatedBy = createdBy,
                        CreatedTime = DateTime.Now,
                        UpdatedBy = createdBy,
                        UpdatedTime = DateTime.Now,
                        Extend = JsonConvert.SerializeObject(main.PanelList),
                    }).ExecuteReturnIdentityAsync();
                    substation.Topology3DId = mainId;
                    substation.UpdatedBy = createdBy;
                    substation.UpdatedTime = DateTime.Now;
                    await client.Updateable(substation).ExecuteCommandAsync();
                    await client.Insertable(new TopologyIdManager()
                    {
                        CreatedBy = createdBy,
                        CreatedTime = DateTime.Now,
                        UpdatedBy = createdBy,
                        UpdatedTime = DateTime.Now,
                        Key = mainId,
                        Type = "3D-Dashboard"
                    }).ExecuteReturnIdentityAsync();
                    client.Ado.CommitTran();
                    hasCreate = true;
                }
                catch
                {
                    client.Ado.RollbackTran();
                    throw;
                }
                #endregion
            }

            return hasCreate;
        }

        /// <summary>
        /// 修改配电柜高度和宽度
        /// 高度
        /// 1000~3000
        /// 宽度
        /// 500~1500
        /// </summary>
        /// <param name="panelList"></param>
        private void ChangePanelHeightAndWidth(List<PanelModel> panelList)
        {
            foreach (var panel in panelList)
            {
                if (panel.Height > 3000)
                {
                    panel.Height = 3000;
                }
                else if (panel.Height < 1000)
                {
                    panel.Height = 1000;
                }

                if (panel.Width < 500)
                {
                    panel.Width = 500;
                }
                else if (panel.Width > 1500)
                {
                    panel.Width = 1500;
                }

            }
        }
    }
}
