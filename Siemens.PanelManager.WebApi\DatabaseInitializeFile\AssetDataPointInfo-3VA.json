[{"Code": "HaveAlarm", "Name": "HaveAlarm", "GroupName": "Status", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "Alarm", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 1, "ParentName": "Status", "MqttGroupName": "Alarm", "MqttSamplingPeriod": 1, "IsDefaultMqtt": 1, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "IsConnected", "Name": "IsConnected", "GroupName": "Status", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "UDCConnect", "IsSystemStatus": true, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 2, "ParentName": "Status", "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Switch", "Name": "Switch", "GroupName": "Status", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "BREAKER_PROFILE_STATUS/BREAKER_PROFILE_STATUS_M_STATE_OF_BREAKER", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": true, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 3, "ParentName": "3VAState", "MqttGroupName": "Status", "MqttSamplingPeriod": 1, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ua", "Name": "Ua", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "V_LN/Inst/Value/L1N", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 4, "ParentName": "VoltageL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ub", "Name": "Ub", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "V_LN/Inst/Value/L2N", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 5, "ParentName": "VoltageL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Uc", "Name": "Uc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "V_LN/Inst/Value/L3N", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 6, "ParentName": "VoltageL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Uab", "Name": "Uab", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "V_LL/Inst/Value/L1L2", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 7, "ParentName": "VoltageL-L_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ubc", "Name": "Ubc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "V_LL/Inst/Value/L2L3", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 8, "ParentName": "VoltageL-L_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Uca", "Name": "Uca", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "V_LL/Inst/Value/L3L1", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9, "ParentName": "VoltageL-L_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ia", "Name": "Ia", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "I/Inst/Value/L1", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 10, "ParentName": "Current_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ib", "Name": "Ib", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "I/Inst/Value/L2", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 11, "ParentName": "Current_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Ic", "Name": "Ic", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "I/Inst/Value/L3", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 12, "ParentName": "Current_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "I_Agv", "Name": "I_Agv", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "I/Inst/Value/AVG", "Unit": "A", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 13, "ParentName": "Current_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "P", "Name": "P", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Power/W/Inst/Value/Sum", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 14, "ParentName": "ActivePower_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Pa", "Name": "Pa", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Power/W/Inst/Value/L1", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 15, "ParentName": "ActivePower_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Pb", "Name": "Pb", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Power/W/Inst/Value/L2", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 16, "ParentName": "ActivePower_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Pc", "Name": "Pc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Power/W/Inst/Value/L3", "Unit": "W", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 17, "ParentName": "ActivePower_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Q", "Name": "Q", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Power/var/Qtot/Inst/Value/Sum", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 18, "ParentName": "MeasuringMethodVARtot_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Qa", "Name": "Qa", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Power/var/Qtot/Inst/Value/L1", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19, "ParentName": "MeasuringMethodVARtot_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Qb", "Name": "Qb", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Power/var/Qtot/Inst/Value/L2", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 20, "ParentName": "MeasuringMethodVARtot_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Qc", "Name": "Qc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Power/var/Qtot/Inst/Value/L3", "Unit": "var", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 21, "ParentName": "MeasuringMethodVARtot_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "S", "Name": "S", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Power/VA/Inst/Value/Sum", "Unit": "VA", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 22, "ParentName": "ApparentPower_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor", "Name": "PowFactor", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Power/Factor/Inst/Value/Sum", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 23, "ParentName": "PowerFactor_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor_A", "Name": "PowFactor_A", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Power/Factor/Inst/Value/L1", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 24, "ParentName": "PowerFactor_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor_B", "Name": "PowFactor_B", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Power/Factor/Inst/Value/L2", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 25, "ParentName": "PowerFactor_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "PowFactor_C", "Name": "PowFactor_C", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Power/Factor/Inst/Value/L3", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 26, "ParentName": "PowerFactor_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "F", "Name": "F", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Frequency/Inst/Value/Common", "Unit": "Hz", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 27, "ParentName": "Frequency_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ua", "Name": "THD_Ua", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "THD/V_LN/Inst/Value/L1N#", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 28, "ParentName": "THDCurrent_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ub", "Name": "THD_Ub", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "THD/V_LN/Inst/Value/L2N#", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 29, "ParentName": "THDCurrent_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Uc", "Name": "THD_Uc", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "THD/V_LN/Inst/Value/L3N#", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 30, "ParentName": "THDCurrent_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ia", "Name": "THD_Ia", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "THD/I/Inst/Value/L1#", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 31, "ParentName": "THDVoltageL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ib", "Name": "THD_Ib", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "THD/I/Inst/Value/L2#", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 32, "ParentName": "THDVoltageL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "THD_Ic", "Name": "THD_Ic", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "THD/I/Inst/Value/L3#", "Unit": "%", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 33, "ParentName": "THDVoltageL-N_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ForwardActivePower", "Name": "ForwardActivePower", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "EA_IMPORT", "Unit": "Wh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 34, "ParentName": "ActiveEnergy_Import", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ForwardReactivePower", "Name": "ForwardReactivePower", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "ER_IMPORT", "Unit": "varh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 35, "ParentName": "ReactiveEnergy_Import", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ReverseActivePower", "Name": "ReverseActivePower", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "EA_EXPORT", "Unit": "Wh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 36, "ParentName": "ActiveEnergy_Export", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "ReverseReactivePower", "Name": "ReverseReactivePower", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "ER_EXPORT", "Unit": "varh", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 37, "ParentName": "ReactiveEnergy_Export", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "BreakerTemp", "Name": "BreakerTemp", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "[T]", "UdcCode": "Temperatures/Inst/Value/ETU", "Unit": "℃", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 39, "ParentName": "Temperatures_ActualInstantaneousMeasurementValues", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "OperatingHours", "Name": "OperatingHours", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "StatusValues/Maintenance/DIAG_OPERATING_HOURS", "Unit": "h", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 56, "ParentName": "MaintenanceInformation", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LTTrips", "Name": "LTTrips", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "StatusValues/TripCounter/DIAG_CNT_TRIP_LONG_TIME", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 57, "ParentName": "TripCounters", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "STTrips", "Name": "STTrips", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "StatusValues/TripCounter/DIAG_CNT_TRIP_SHORT_TIME", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 58, "ParentName": "TripCounters", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "INSTTrips", "Name": "INSTTrips", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "StatusValues/TripCounter/DIAG_CNT_TRIP_INST", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 59, "ParentName": "TripCounters", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "GFTrips", "Name": "GFTrips", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "StatusValues/TripCounter/DIAG_CNT_TRIP_GROUND_FAULT", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 60, "ParentName": "TripCounters", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "NTrips", "Name": "NTrips", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "StatusValues/TripCounter/DIAG_CNT_TRIP_NEUTRAL_CONDUCTOR", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 61, "ParentName": "TripCounters", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "AllTrips", "Name": "AllTrips", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "StatusValues/TripCounter/DIAG_CNT_TRIP_ALL", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 62, "ParentName": "TripCounters", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "HealthScore", "Name": "HealthScore", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "BREAKER_HEALTH_INDICATOR", "Unit": "%", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 63, "ParentName": "3VAState", "MqttGroupName": "Status", "MqttSamplingPeriod": 1, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "RemainingLife", "Name": "RemainingLife", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "BREAKER_REMAINING_LIFETIME/REMAINING_TIME_Value", "Unit": "day", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 64, "ParentName": "3VAState", "MqttGroupName": "Status", "MqttSamplingPeriod": 1, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "HasTriped", "Name": "HasTriped", "GroupName": "Status", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "RCD_STATUS/RCD_STATUS_M_TRIP_IDN", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "ParentName": "3VAState", "MqttGroupName": "Status", "MqttSamplingPeriod": 1, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "MLFB", "Name": "MLFB", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "IDENT_BREAKER_IM0_DATA_ORIG_OrderNumber", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 66, "ParentName": "Others", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "SerialNumber", "Name": "SerialNumber", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "IDENT_BREAKER_IM0_DATA_ORIG_SerialNumber", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 67, "ParentName": "Others", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "FirmwareRevision_1", "Name": "FirmwareRevision_1", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "IDENT_BREAKER_IM0_DATA_ORIG_FirmwareRevision", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 68, "ParentName": "Others", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "IdentNumber", "Name": "IdentNumber", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "IDENT_BREAKER_IM1_DATA_IM1_DATA_TAG_FUNKTION", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 69, "ParentName": "Others", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "MechanicalSwitchCycles", "Name": "MechanicalSwitchCycles", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "StatusValues/Maintenance/DIAG_OPERATING_COUNT", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 70, "ParentName": "Others", "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "RatedVoltage", "Name": "RatedVoltage", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Etu_CONF_VT_PRIMARY_RATED_VOLTAGE_LN", "Unit": "V", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 71, "ParentName": "Others", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LT_IR", "Name": "LT_IR", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "LT_IR_A", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 72, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LT_TR", "Name": "LT_TR", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "LT_TR_A", "Unit": "s", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 73, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LT_THERMAL_MEM_ONOFF", "Name": "LT_THERMAL_MEM_ONOFF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "LT_THERMAL_MEM_ONOFF_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 74, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LTN_ONOFF", "Name": "LTN_ONOFF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "LTN_ONOFF_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 75, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LTN_IN", "Name": "LTN_IN", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "LTN_IN_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 76, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "MP_TcTp_SELECTOR", "Name": "MP_TcTp_SELECTOR", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "MP_TcTp_SELECTOR", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 77, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "MP_Tc", "Name": "MP_Tc", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "MP_Tc", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 78, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "MP_Tp", "Name": "MP_Tp", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "MP_Tp", "Unit": "s", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 79, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "ST_ISD", "Name": "ST_ISD", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "ST_ISD_A", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 80, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "ST_TSD", "Name": "ST_TSD", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "ST_TSD_A", "Unit": "s", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 81, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "ST_I2t_ON_OFF", "Name": "ST_I2t_ON_OFF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "ST_I2t_ON_OFF_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 82, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "INST_II", "Name": "INST_II", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "INST_II_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 83, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "INST_II_ARC", "Name": "INST_II_ARC", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "INST_II_ARC_A", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 84, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LTN_ONOFF", "Name": "LTN_ONOFF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "LTN_ONOFF_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 85, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "LTN_IN", "Name": "LTN_IN", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "LTN_IN_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 86, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_ONOFF", "Name": "GF_ONOFF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "GF_ONOFF_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 87, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_IG", "Name": "GF_IG", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "GF_IG_A", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 88, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_TG", "Name": "GF_TG", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "GF_TG_A", "Unit": "s", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 89, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_PARA_CURVE", "Name": "GF_PARA_CURVE", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "GF_I2t_ON_OFF_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 90, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_TYPE", "Name": "GF_TYPE", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "GF_TYPE_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 91, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_IG_ARC", "Name": "GF_IG_ARC", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "GF_IG_ARC", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 92, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_ALARM_ONOFF", "Name": "GF_ALARM_ONOFF", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "GF_ALARM_ONOFF_A", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 93, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "GF_IG_DIRECT_ALARM", "Name": "GF_IG_DIRECT_ALARM", "GroupName": "ProtectionSetting", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "GF_ALARM_IG_A", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 94, "ParentName": "Status", "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "Trip_Severity", "Name": "Trip_Severity", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Alarm/Value/TripSeverity", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 95, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Trip_Status", "Name": "Trip_Status", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Alarm/Value/TripStatus", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 96, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Trip_Path", "Name": "Trip_Path", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Alarm/Value/TripPath", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 97, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Trip_Info", "Name": "Trip_Info", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Alarm/Value/TripInfo", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 98, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Trip_Time", "Name": "Trip_Time", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Alarm/Value/TripTime", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 99, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Severity", "Name": "Alarm_Severity", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Alarm/Value/AlarmSeverity", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 100, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Status", "Name": "Alarm_Status", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Alarm/Value/AlarmStatus", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 101, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Path", "Name": "Alarm_Path", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Alarm/Value/AlarmPath", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 102, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Info", "Name": "Alarm_Info", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Alarm/Value/AlarmInfo", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 103, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Alarm_Time", "Name": "Alarm_Time", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Alarm/Value/AlarmTime", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 104, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Msg_Severity", "Name": "Msg_Severity", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Alarm/Value/MsgSeverity", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 105, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Msg_Status", "Name": "Msg_Status", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Alarm/Value/MsgStatus", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 106, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Msg_Path", "Name": "Msg_Path", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Alarm/Value/MsgPath", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 107, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Msg_Info", "Name": "Msg_Info", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Alarm/Value/MsgInfo", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 108, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "Msg_Time", "Name": "Msg_Time", "GroupName": "Warnings", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "Alarm/Value/MsgTime", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 109, "ParentName": "Warnings", "MqttGroupName": "Warnings", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1}, {"Code": "INST_II_ARC", "Name": "INST_II_ARC", "GroupName": "Others", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "INST_II_ARC", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 110, "ParentName": "Others"}, {"Code": "GF_I2t_ON_OFF", "Name": "GF_I2t_ON_OFF", "GroupName": "Others", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "GF_I2t_ON_OFF", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 111, "ParentName": "Others"}, {"Code": "GF_ALARM_IG", "Name": "GF_ALARM_IG", "GroupName": "Others", "AssetLevel": 50, "AssetType": "<PERSON>er", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "GF_ALARM_IG", "Unit": "", "CollectMode": "", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 112, "ParentName": "Others"}, {"Code": "Health_Status", "Name": "Health_Status", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "BREAKER_HEALTH_INDICATOR", "Unit": "%", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 113, "ParentName": "3VAState", "MqttGroupName": "Status", "MqttSamplingPeriod": 1, "IsDefaultMqtt": 1}, {"Code": "HealthLevel", "Name": "HealthLevel", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "MCCB", "AssetModel": "3VA", "FilterIds": "", "UdcCode": "BREAKER_HEALTH_INDICATOR", "Unit": "%", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 114, "ParentName": "3VAState", "MqttGroupName": "Status", "MqttSamplingPeriod": 1, "IsDefaultMqtt": 1}]