﻿using Microsoft.AspNetCore.Mvc;
using System.Xml.Linq;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class EnergyQualityQueryParam
    {
        [FromQuery(Name = "device")]
        public string? Device { get; set; }

        [FromQuery(Name = "thdType")]
        public List<THDType>? THDType { get; set; }
    }

    public enum THDType
    {
        Ua,
        Ub,
        Uc,
        Ia,
        Ib,
        Ic
    }
}
