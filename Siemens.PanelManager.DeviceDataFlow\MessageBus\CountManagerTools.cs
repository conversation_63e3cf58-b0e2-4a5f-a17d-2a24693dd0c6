﻿using System.Collections.Concurrent;
using System.Text;
using Microsoft.Extensions.Logging;

namespace Siemens.PanelManager.DeviceDataFlow.MessageBus
{
    static class CountManagerTools
    {
        private static ConcurrentDictionary<string, CountObj> _count = new ConcurrentDictionary<string, CountObj>();
        private static ILogger? _logger;

        static CountManagerTools()
        {
            Task.Factory.StartNew(async () =>
            {
                while (true)
                {
                    var sb = new StringBuilder();
                    var keys = _count.Keys;
                    foreach (var k in keys)
                    {
                        var countObj = _count[k];
                        _count[k] = _count[k].Next;
                        sb.Append("Key:[");
                        sb.Append(k);
                        sb.Append("] Count:[");
                        sb.Append(countObj.Count);
                        sb.AppendLine("]");
                        countObj.Count = 0;
                    }
                    if (_logger != null)
                    {
                        _logger.LogInformation(sb.ToString());
                    }
                    await Task.Delay(5 * 60 * 1000);
                }
            });
        }

        public static void InitLogger(ILogger logger) 
        {
            _logger = logger;
        }

        public static CountObj GetCount(string name)
        {
            var count = _count.GetOrAdd(name, (k) =>
            {
                var last = new CountObj();
                var first = last;
                for (var i = 0; i < 3; i++)
                {
                    var c = new CountObj();
                    last.Next = c;
                    last = c;
                }
                last.Next = first;
                return first;
            });

            return count;
        }
    }

    public class CountObj
    {
        public int Count { get; set; }
        public CountObj Next { get; set; }
    }
}
