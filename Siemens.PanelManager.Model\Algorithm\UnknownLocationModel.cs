﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Algorithm
{
    
    public class UnknownLocationModel
    {
        [JsonProperty(PropertyName = "value")]
        public ntlResultModel[] value { get; set; }

    }
    public class ntlResultModel
    {
        [JsonProperty(PropertyName = "node_id")]
        public int node_id { get; set; }
        [JsonProperty(PropertyName = "level")]
        public string level { get; set; }
    }
}
