﻿using log4net.Core;
using Microsoft.Extensions.Logging;
using Quartz;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Common.Job
{
    public class JobManager
    {
        public async Task<bool> TriggerJob(string jobName)
        {
            return await JobStaticManager.TriggerJob(new RunOnceJobInfo(jobName));
        }

        public async Task<bool> TriggerJob(string jobName, IReadOnlyDictionary<string, string> parameters)
        {
            return await JobStaticManager.TriggerJob(new RunOnceJobInfo(jobName, parameters));
        }

        public async Task<string> TriggerCronJob<T>(string jobName, string cron)
            where T : JobBase
        {
            var jobInfo = new RunCronJobInfo(jobName, cron, typeof(T));
            var result = await JobStaticManager.TriggerCronJob(jobInfo);
            if (!string.IsNullOrEmpty(jobInfo.JobKey))
            {
                return jobInfo.JobKey;
            }

            return string.Empty;
        }

        public async Task<string> TriggerCronJob<T>(string jobName, string cron, Dictionary<string, string> parameters)
            where T : JobBase
        {
            var jobInfo = new RunCronJobInfo(jobName, cron, typeof(T))
            {
                Parameters = parameters
            };
            var result = await JobStaticManager.TriggerCronJob(jobInfo);
            if (result)
            {
                return jobInfo.JobKey ?? string.Empty;
            }

            return string.Empty;
        }

        public async Task<bool> DeleteCronJob(string jobName)
        {
            return await JobStaticManager.DeleteCronJob(jobName);
        }
    }
}
