﻿namespace Siemens.PanelManager.DeviceDataFlow.Model
{
    internal class AssetSaveParam
    {
        public int AssetId { get; set; }
        public string ObjectId { get; set; } = string.Empty;
        public DateTime Time { get; set; }
        public string TableName { get; set; } = string.Empty;
        public Dictionary<string, string> Datas { get; set;} = new Dictionary<string, string>();
    }
}
