﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<ProductName>Siemens.UDC</ProductName>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<NoWarn>$(NoWarn);1591</NoWarn>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="Controllers\MyTestController.cs" />
	  <Compile Remove="Controllers\TestController.cs" />
	</ItemGroup>

	<ItemGroup>
		
		<Content Remove="DatabaseInitializeFile\HealthSuggestion.json" />
		<Content Remove="DatabaseInitializeFile\Page.json" />
		<Content Remove="DatabaseInitializeFile\Role.json" />
		<Content Remove="DatabaseInitializeFile\RolePage.json" />
		<Content Remove="DatabaseInitializeFile\SystemStaticModel.json" />
		<Content Remove="DatabaseInitializeFile\TestData\User.json" />
		<Content Remove="DatabaseInitializeFile\TestData\UserRoleMapping.json" />
		<Content Remove="DatabaseInitializeFile\User.json" />
		<Content Remove="DatabaseInitializeFile\UserRole.json" />
		<Content Remove="log4net.config" />
		<Content Remove="wwwroot\uploadfiles\systemfile\zh-cn\Asset-Template - 副本.xlsx" />
		<Content Remove="wwwroot\uploadfiles\systemfile\zh-cn\~%24Asset-Template.xlsx" />
	</ItemGroup>

	<ItemGroup>
		
		<None Include="DatabaseInitializeFile\HealthSuggestion.json" />
		<None Include="DatabaseInitializeFile\SystemStaticModel.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Include="DatabaseInitializeFile\TestData\User.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Include="DatabaseInitializeFile\TestData\UserRoleMapping.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ItemGroup>
		<None Include="DatabaseInitializeFile\UserRole.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ItemGroup>
		<None Include="DatabaseInitializeFile\User.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ItemGroup>
		<None Include="DatabaseInitializeFile\RolePage.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ItemGroup>
		<None Include="DatabaseInitializeFile\Role.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ItemGroup>
		<None Include="DatabaseInitializeFile\Page.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ItemGroup>
		<None Include="log4net.config">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Mapster" Version="7.4.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.6" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.6" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.20.1" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
		<PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.6.2" />
		<PackageReference Include="System.IO.Packaging" Version="8.0.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Siemens.PanelManager.Common\Siemens.PanelManager.Common.csproj" />
		<ProjectReference Include="..\Siemens.PanelManager.DeviceDataFlow\Siemens.PanelManager.DeviceDataFlow.csproj" />
		<ProjectReference Include="..\Siemens.PanelManager.Job\Siemens.PanelManager.Job.csproj" />
		<ProjectReference Include="..\Siemens.PanelManager.Server\Siemens.PanelManager.Server.csproj" />
		<ProjectReference Include="..\Siemens.PanelManager.SyncService.Sqlite\Siemens.PanelManager.SyncService.Sqlite.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Language\" />
		<Folder Include="InfluxDBConfigFile\" />
	</ItemGroup>

	<ItemGroup>
		<Content Update="DatabaseInitializeFile\AssetDataPointInfo-Panel - 复制.json">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		  <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
		  <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Update="DatabaseInitializeFile\AssetKnowledge-3VA.json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="DatabaseInitializeFile\AssetKnowledge-3WA .json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="DatabaseInitializeFile\AssetKnowledge-3WL.json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="DatabaseInitializeFile\AssetKnowledge-3WT.json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="DatabaseInitializeFile\JobSchedule-1.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="wwwroot\uploadfiles\systemfile\Asset-Template.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="wwwroot\uploadfiles\systemfile\en\Asset-Template.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="wwwroot\uploadfiles\systemfile\zh-cn\Asset-Template.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
		<None Update="DatabaseInitializeFile\ChangeDeviceDetails.sql">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Dockerfile">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExcelTemplate\Asset-Template.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExcelTemplate\en\Alarm&amp;Log-Template.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExcelTemplate\en\Asset-Template.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExcelTemplate\en\PeakFlatValleyTemplate.xlsx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExcelTemplate\en\PowerConsumptionTemplate.xlsx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExcelTemplate\en\PowerTemplate.xlsx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExcelTemplate\en\StructureTemplate.xlsx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExcelTemplate\en\TopTenTemplate.xlsx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExcelTemplate\en\WorkOrderTemplate.xlsx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExcelTemplate\zh-cn\Alarm&amp;Log-Template.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExcelTemplate\zh-cn\Asset-Template.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExcelTemplate\zh-cn\PeakFlatValleyTemplate.xlsx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExcelTemplate\zh-cn\PowerConsumptionTemplate.xlsx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExcelTemplate\zh-cn\PowerTemplate.xlsx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExcelTemplate\zh-cn\StructureTemplate.xlsx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExcelTemplate\zh-cn\ThirdTemperatureDeviceExport.xlsx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExcelTemplate\zh-cn\ThirdTemperatureDeviceTemplate.xlsx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExcelTemplate\zh-cn\TopTenTemplate.xlsx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ExcelTemplate\zh-cn\WorkOrderTemplate.xlsx">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Language\Message.en.resources">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Language\Message.zh-cn.resources">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="panel_version.txt">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ProjectExtensions><VisualStudio><UserProperties databaseinitializefile_4assetdatapointdirectory-pac3120_1json__JsonSchema="https://cdn.jsdelivr.net/gh/roadrunner-server/roadrunner@latest/schemas/config/3.0.schema.json" /></VisualStudio></ProjectExtensions>

</Project>
