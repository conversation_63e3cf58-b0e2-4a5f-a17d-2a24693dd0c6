﻿using NodaTime;
using Siemens.PanelManager.Model.Database.Asset;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class TemperatureMonitorDetails
    {
        public int OnlineCount { get; set; }
        public int OfflineCount { get; set; }
        public int AlarmCount { get; set; }
        public int AlarmDeviceCount { get; set; }
        public TemperatureDeviceStatus? MaxTemperatureDevice { get; set; }
        public List<TemperatureDeviceStatus> OfflineDevices { get; set; } = new List<TemperatureDeviceStatus>();
        public List<TemperatureDeviceStatus> AlarmDevices { get; set; } = new List<TemperatureDeviceStatus>();
    }
}
