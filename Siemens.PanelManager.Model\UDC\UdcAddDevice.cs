﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.Model.UDC
{
    public class UdcAddDevice
    {
        [JsonProperty(PropertyName = "type_name")]
        public string? TypeName { get; set; }

        [JsonProperty(PropertyName = "name")]
        public string? Name { get; set; }

        [JsonProperty(PropertyName = "address")]
        public string? Address { get; set; }

        [JsonProperty(PropertyName = "parent_id")]
        public string? ParentId { get; set; }

        [JsonProperty(PropertyName = "gw_parent_id")]
        public string? GwParentId { get; set; }
    }
}
