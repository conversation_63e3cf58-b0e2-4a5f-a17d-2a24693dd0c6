﻿namespace Siemens.InfluxDB.Helper.Enum
{
    internal enum GroupFunctionEnum
    {
        <PERSON>,
        Min,
        Mean,
        Count,
        Sum,
        First,
        Last,
        Median,
        Default
    }
    internal static class EnumExtend
    {
        public const string DefaultResultCode = "_result";
        public static string GetGroupFunctionName(this GroupFunctionEnum groupFunction)
        {
            switch (groupFunction)
            {
                case GroupFunctionEnum.Mean:
                    return "mean";
                case GroupFunctionEnum.Max:
                    return "max";
                case GroupFunctionEnum.Median:
                    return "median";
                case GroupFunctionEnum.Count:
                    return "count";
                case GroupFunctionEnum.Sum:
                    return "sum";
                case GroupFunctionEnum.First:
                    return "first";
                case GroupFunctionEnum.Last:
                    return "last";
                case GroupFunctionEnum.Min:
                    return "min";
                default: return DefaultResultCode;
            }
        }
    }
}
