﻿using Newtonsoft.Json;
using Quartz;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.Common.Job
{
    public abstract class JobBase : IJob
    {
        public abstract string Name { get; }
        public string Code { get; private set; } = string.Empty;

        protected Dictionary<string, string> ContextData { get; set; } = new Dictionary<string, string>();

        protected JobKey? JobKey { get; private set; }

        public async Task Execute(IJobExecutionContext context)
        {
            JobKey = context.JobDetail.Key;

            var isMatch = Regex.Match(context.JobDetail.Key.Name, $"^([\\w|-]+)-{Name}$");
            if (isMatch.Success)
            {
                Code = isMatch.Groups[1].Value;

                var dataMap = context.MergedJobDataMap;
                foreach (var key in dataMap.Keys)
                {
                    var data = dataMap[key];
                    if (data == null) continue;
                    if (data is object && data is not string)
                    {
                        ContextData.Add(key, JsonConvert.SerializeObject(data));
                    }
                    else
                    {
                        ContextData.Add(key, data?.ToString() ?? string.Empty);
                    }
                }

                await Execute();

                var nextJobs = JobStaticManager.GetNext(Code);
                foreach (var job in nextJobs)
                {
                    if (job.Key == null) continue;
                    if (job.Parameters != null)
                    {
                        var parameters = new Dictionary<string, string>();
                        foreach (var key in job.Parameters.Keys)
                        {

                            var value = job.Parameters[key];
                            if (string.IsNullOrEmpty(value)) continue;
                            var match = Regex.Match(value, "^{{([\\w]+)}}$");
                            if (match.Success)
                            {
                                var keyName = match.Groups[1].Value;
                                if (ContextData.ContainsKey(keyName))
                                {
                                    parameters.Add(key, ContextData[keyName]);
                                }
                            }
                        }

                        await JobStaticManager.TriggerJob(new RunOnceJobInfo(job.Key, parameters));
                    }
                    else
                    {
                        await JobStaticManager.TriggerJob(job);
                    }
                }
            }
            else
            {
                throw new IllegalJobException($"Job Name: {context.JobDetail.Key.Name}, Job Group: {context.JobDetail.Key.Group}");
            }
        }

        public abstract Task Execute();
    }
}
