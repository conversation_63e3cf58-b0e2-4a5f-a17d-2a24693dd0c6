{"info": {"_postman_id": "c403c854-f03c-4f6e-9e3d-81e841ed7b0a", "name": "05使用管理员账号进入panel manager告警管理中的告警列表菜单，查看默认过滤条件和列表", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 3", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "获取告警当日信息总览 Copy 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"显示告警列表\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"substationName\");\r", "    pm.expect(pm.response.text()).to.include(\"panelName\");\r", "    pm.expect(pm.response.text()).to.include(\"circuitName\");\r", "    pm.expect(pm.response.text()).to.include(\"deviceName\");\r", "    pm.expect(pm.response.text()).to.include(\"eventType\");\r", "    pm.expect(pm.response.text()).to.include(\"severity\");\r", "    pm.expect(pm.response.text()).to.include(\"alarmInfo\");\r", "    pm.expect(pm.response.text()).to.include(\"alarmStatus\");\r", "    pm.expect(pm.response.text()).to.include(\"logTime\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 1000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", "pm.test(\"code码为20000\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData[\"code\"]).to.eql(20000);", "});", ""]}}]}