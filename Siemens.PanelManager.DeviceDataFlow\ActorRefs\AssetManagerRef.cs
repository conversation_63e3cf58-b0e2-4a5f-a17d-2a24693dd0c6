﻿using Akka.Actor;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.DataFlow;

namespace Siemens.PanelManager.DeviceDataFlow.ActorRefs
{
    public class AssetManagerRef: IAssetManagerRef
    {
        private readonly IActorRef _ref;
        private readonly SiemensCache _cache;
        private readonly ILogger _logger;
        internal AssetManagerRef(IActorRef actorRef, SiemensCache cache, ILogger<AssetManagerRef> logger)
        {
            _ref = actorRef;
            _cache = cache;
            _logger = logger;
        }

        public void UpdatedAsset(int[] assetIds)
        {
            foreach (var assetId in assetIds)
            {
                _cache.Set<DateTime>($"AssetChangeFlag-{assetId}", DateTime.Now);
                _ref.Tell(new AssetOptionParam(AssetOpt.Update, assetId));
            }
        }

        public void UpdatedAsset(int assetId)
        {
            // _logger.LogInformation($"更新资产{assetId}");

            _cache.Set<DateTime>($"AssetChangeFlag-{assetId}", DateTime.Now);
            _ref.Tell(new AssetOptionParam(AssetOpt.Update, assetId));
        }

        public void RemoveAsset(int[] assetIds)
        {
            foreach (var assetId in assetIds)
            {
                _cache.Set<DateTime>($"AssetChangeFlag-{assetId}", DateTime.Now);
                _ref.Tell(new AssetOptionParam(AssetOpt.Remove, assetId));
            }
        }

        public void RemoveAsset(int assetId) 
        {
            _cache.Set<DateTime>($"AssetChangeFlag-{assetId}", DateTime.Now);
            _ref.Tell(new AssetOptionParam(AssetOpt.Remove, assetId));
        }

        public void AddAsset(string[] assetNames)
        {
            // _logger.LogInformation($"添加资产集");
            foreach (var assetName in assetNames)
            {
                _cache.Set<DateTime>($"AssetAddFlag-{assetName}", DateTime.Now);
                _ref.Tell(new AssetOptionParam(AssetOpt.Add, assetName));
            }
        }

        public void AddAsset(string assetName)
        {
            // _logger.LogInformation($"添加资产");
            _cache.Set<DateTime>($"AssetAddFlag-{assetName}", DateTime.Now);
            _ref.Tell(new AssetOptionParam(AssetOpt.Add, assetName));
        }

        public void UpdateCircuitSource()
        {
            _ref.Tell(AssetOpt.Reload);
        }
    }
}
