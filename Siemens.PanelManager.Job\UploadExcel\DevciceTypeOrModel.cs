﻿using Newtonsoft.Json.Linq;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Model.Database.System;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.UploadExcel
{
    internal class DevciceTypeOrModel
    {
        public DevciceTypeOrModel(SystemStaticModel model) 
        {
            Code = model.Code;
            Name= model.Name;
            ModelByType = string.Empty;
            if (!string.IsNullOrEmpty(model.Extend))
            {
                var obj = JObject.Parse(model.Extend);
                if(obj.TryGetValue("NoSupport", out var token)) 
                {
                    NoSupport = token.Value<bool>();
                }
                else 
                {
                    NoSupport = false;
                }

                if (obj.TryGetValue("ModelByType", out token))
                {
                    ModelByType = token.Value<string>() ?? string.Empty;
                }
            }
        }

        public string Code { get; private set; }
        public string Name { get; private set; }
        public bool NoSupport { get; private set; }
        public string ModelByType { get; private set; }
    }
}
