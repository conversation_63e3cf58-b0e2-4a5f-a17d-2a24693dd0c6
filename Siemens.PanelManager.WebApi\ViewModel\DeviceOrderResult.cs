﻿using Siemens.PanelManager.Model.Database.Asset;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class DeviceOrderResult
    {
        public DeviceOrderResult()
        {
        }
        public DeviceOrderResult(AssetInfo assetInfo)
        {
            AssetId = assetInfo.Id;
            Name = assetInfo.AssetName;
            BuyNo = assetInfo.MLFB;
            Address = assetInfo.Location;
            AssetType = assetInfo.AssetType;
            AssetModel = assetInfo.AssetModel;
        }
        public int AssetId { get; set; }
        public string? Name { get; set; }
        public string? BuyNo { get; set; }
        public string? Address { get; set; }
        public string? AssetType { get; set; }
        public string? AssetModel { get; set; }
    }
}
