[{"Name": "待处理", "Code": "Pending", "Type": "WORK_ORDER_STATUS", "Sort": 0}, {"Name": "处理中", "Code": "Processing", "Type": "WORK_ORDER_STATUS", "Sort": 1}, {"Name": "已完成", "Code": "Completed", "Type": "WORK_ORDER_STATUS", "Sort": 2}, {"Name": "逾期未处理", "Code": "Overdue", "Type": "WORK_ORDER_STATUS", "Sort": 3}, {"Name": "检查", "Code": "Check", "Type": "WORK_ORDER_MEASURE", "Sort": 0}, {"Name": "维护", "Code": "Maintain", "Type": "WORK_ORDER_MEASURE", "Sort": 1}, {"Name": "维修", "Code": "Repair", "Type": "WORK_ORDER_MEASURE", "Sort": 2}, {"Name": "普通工单", "Code": "General", "Type": "WORK_ORDER_TYPE", "Sort": 0}, {"Name": "告警工单", "Code": "Alarm", "Type": "WORK_ORDER_TYPE", "Sort": 1}, {"Name": "送电", "Code": "SendingEnd", "Type": "ELECTRICITY_TYPE", "Sort": 1}, {"Name": "受电", "Code": "ReceivingEnd", "Type": "ELECTRICITY_TYPE", "Sort": 0}, {"Name": "阶梯1", "Code": "Step1", "Type": "STEP_TYPE", "Sort": 0}, {"Name": "阶梯2", "Code": "Step2", "Type": "STEP_TYPE", "Sort": 1}, {"Name": "阶梯3", "Code": "Step3", "Type": "STEP_TYPE", "Sort": 2}, {"Name": "阶梯4", "Code": "Step4", "Type": "STEP_TYPE", "Sort": 3}, {"Name": "月份", "Code": "Month", "Type": "SEASON_TYPE", "Sort": 0}, {"Name": "节假日", "Code": "Holidays", "Type": "SEASON_TYPE", "Sort": 1}]