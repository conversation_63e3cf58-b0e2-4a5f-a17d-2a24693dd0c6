import { IxB<PERSON>on, Modal } from "@siemens/ix-react";
import { useRef } from "react";

export default function ConfirmModal(prop) {
  const modalRef = useRef();

  const cancelFunc = () => {
    modalRef.current?.close(false);
  };

  const okFunc = () => {
    modalRef.current?.close(true);
  };
  return (
    <Modal ref={modalRef}>
      <div className="modal-header">{prop.title}</div>
      <div className="modal-body">{prop.message}</div>
      <div className="modal-footer">
        <IxButton outline onClick={() => cancelFunc()}>
          {" "}
          Cancel{" "}
        </IxButton>
        <IxButton onClick={() => okFunc()}>OK</IxButton>
      </div>
    </Modal>
  );
}
