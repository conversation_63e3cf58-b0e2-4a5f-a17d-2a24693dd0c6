﻿namespace Siemens.PanelManager.Job.ModbusDeviceWorker
{
    public class PanelModbusClientConfig
    {
        public string IPAddress { get; set; } = string.Empty;

        public int Port { get; set; }

        public int AssetId { get; set; }

        public string? AssetName { get; set; }

        public string ObjectId { get; set; } = string.Empty;

        public int SlaveId { get; set; }

        public string? ThirdPartCode { get; set; }

        public List<PanelModbusSlave>? PanelModbusSlaves { get; set; }
    }
}
