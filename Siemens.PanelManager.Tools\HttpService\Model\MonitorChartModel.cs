﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace Siemens.PanelManager.Tools.HttpService.Model
{
    internal class MonitorChartModel
    {
        [JsonProperty(PropertyName = "tooltip")]
        public dynamic Tooltip = new
        {
            trigger= "axis"
        };
        [JsonProperty(PropertyName = "legend", NullValueHandling = NullValueHandling.Ignore)]
        public ChartItem<string>? Legend { get; set; }
        [JsonProperty(PropertyName = "xAxis", NullValueHandling = NullValueHandling.Ignore)]
        public List<ChartItem<string>> XAxis { get; set; } = new List<ChartItem<string>>();
        [JsonProperty(PropertyName = "yAxis", NullValueHandling = NullValueHandling.Ignore)]
        public ChartItem<string> YAxis { get; set; } = new ChartItem<string>() { Type = "value" };
        [JsonProperty(PropertyName = "series", NullValueHandling = NullValueHandling.Ignore)]
        public List<ChartItem<decimal>> Series { get; set; } = new List<ChartItem<decimal>>();
    }

    internal class ChartItem<T>
    {
        [JsonProperty(PropertyName = "name", NullValueHandling = NullValueHandling.Ignore)]
        public string? Name { get; set; }
        [JsonProperty(PropertyName = "data", NullValueHandling = NullValueHandling.Ignore)]
        public T[]? Data { get; set; }
        [JsonProperty(PropertyName = "type", NullValueHandling = NullValueHandling.Ignore)]
        public string Type { get; set; } = string.Empty;
    }
}
