﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="AssetStatusLogSaveJob\**" />
    <EmbeddedResource Remove="AssetStatusLogSaveJob\**" />
    <None Remove="AssetStatusLogSaveJob\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="GetDeviceStatusByUDCApi\GetDeviceStatusByUdcApiJob.cs" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Siemens.PanelManager.Common\Siemens.PanelManager.Common.csproj" />
    <ProjectReference Include="..\Siemens.PanelManager.Server\Siemens.PanelManager.Server.csproj" />
    <ProjectReference Include="..\Siemens.PanelManager.SyncService.Sqlite\Siemens.PanelManager.SyncService.Sqlite.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FluentModbus" Version="5.3.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
    <PackageReference Include="Quartz.AspNetCore" Version="3.9.0" />
    <PackageReference Include="System.IO.Packaging" Version="8.0.0" />
  </ItemGroup>

</Project>
