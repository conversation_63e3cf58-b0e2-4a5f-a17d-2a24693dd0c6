﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using SqlSugar;
using System.Security.Principal;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class WorkOrderContentModel
    {
        public int Id { get; set; }

        public int? WorkOrderId { get; set; }

        public int? ReserveId { get; set; }

        public string? Tag { get; set; }

        public string? Content { get; set; }

        public string? Measure { get; set; }

        public FileInfoModel[]? Attachment { get; set; }

        public DateTime AddTime { get; set; }
    }
}
