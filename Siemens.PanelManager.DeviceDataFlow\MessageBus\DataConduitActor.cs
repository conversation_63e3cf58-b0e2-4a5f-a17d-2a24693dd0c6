﻿using Akka.Actor;
using Siemens.PanelManager.Model.DataFlow;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.DeviceDataFlow.MessageBus
{
    internal class DataConduitActor : ReceiveActor
    {
        private string? _name;
        private Action<AssetInputData> _action = (p) => { };

        public DataConduitActor()
        {
            Receive<AssetInputData>((p) =>
            {
                try
                {
                    var count = CountManagerTools.GetCount(_name ?? "unknow");
                    count.Count++;
                    _action(p);
                }
                catch
                {
                }
            });

            Receive<(string, Action<AssetInputData>)>((p) => 
            {
                _name = p.Item1;
                _action = p.Item2; 
            });
        }
    }
}
