﻿using log4net;
using Siemens.PanelManager.HubModel;
using Siemens.PanelManager.Tools.Model;
using Siemens.PanelManager.Tools.Worker;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Tools
{
    static class StaticInfo
    {
        private static Timer _timer = new Timer(CheckConnectStatus);
        public static Action ConnectStatusChange = () => { };
        public static Action SetUpgradeSteps = () => { };
        public static ConnectStatus? ConnectStatus { get; private set; }
        public static MonitorCurrentStatus? CurrentStatus { get; private set; }
        public static ProcessInfo[]? ProcessInfos { get; private set; }
        public static Settings Settings { get; private set; }
        public static UpgradeStatus? UpgradeStatus { get; private set; }
        public static UpgrageLogHelper? UpgrageLog { get; private set; }
        static StaticInfo() 
        {
            Settings = Settings.ReadByFile();
        }

        private static void CheckConnectStatus(object? state) 
        {
            if (ConnectStatus != null && !ConnectStatus.Client.IsConneted)
            {
                try
                {
                    if (!string.IsNullOrEmpty(Settings.Ip))
                    {
                        CreateConnect(Settings.Ip);
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Error(ex);
                }
            }
        }
        public static async void CreateConnect(string ip, int port = 51030)
        {
            var connectStatus = new ConnectStatus(ip, port);
            ConnectStatus = connectStatus;
            connectStatus.Client.OnMonitor(OnMonitor);
            connectStatus.Client.OnLogInfo(OnLogInfo);
            connectStatus.Client.OnDisconnect(OnDisconnect);
            connectStatus.Client.OnUpgradeFinish(OnUpgradeFinish);
            await connectStatus.Start();
            ConnectStatusChange();
            _timer.Change(10000, 10000);
            try
            {
                var steps = await connectStatus.Client.GetUpgradeStep();
                UpgradeStatus = new UpgradeStatus()
                {
                    Steps = steps,
                };

                UpgradeStatus.InitStepStatus();
                SetUpgradeSteps();

                await ConnectStatus.Client.BeginMonitor();
                

            }
            catch (Exception ex)
            {
                LogHelper.Error(ex);
            }

        }

        private static Task OnUpgradeFinish(bool arg)
        {
            if (UpgradeStatus != null)
            {
                UpgradeStatus.Status = arg ? 2 : 99;
            }
            if (UpgrageLog != null)
            {
                UpgrageLog.Dispose();
            }
            return Task.CompletedTask;
        }

        private static Task OnDisconnect(Exception? arg)
        {
            ConnectStatusChange();
            return Task.CompletedTask;
        }

        private static Task OnMonitor(MonitorModel monitorModel)
        {
            if (CurrentStatus == null)
            {
                CurrentStatus = new MonitorCurrentStatus();
            }
            var newProcessList = new List<ProcessInfo>();

            WorkerServer.ResolveMontorModel(monitorModel, newProcessList, CurrentStatus);

            ProcessInfos = newProcessList.ToArray();
            return Task.CompletedTask;
        }

        private static Task OnLogInfo(LogModel logModel) 
        {
            try
            {
                UpgrageLog?.WriteLog(logModel.Message);
                var match = Regex.Match(logModel.Message, "\\[Step-([\\d]{1,2})\\]\\[(Begin|Finish|Fail)\\]");
                if (match.Success)
                {
                    int stepId = int.Parse(match.Groups[1].Value);
                    string status = match.Groups[2].Value;

                    if (UpgradeStatus != null)
                    {
                        UpgradeStatus.SetStepStatus(stepId, status);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error(ex);
            }
            return Task.CompletedTask;
        }

        public static void StartUpgrade()
        {
            if (UpgradeStatus == null) return;
            UpgradeStatus.Status = 1;
            if (UpgrageLog != null) 
            {
                UpgrageLog.Dispose();
            }
            UpgrageLog = new UpgrageLogHelper();
        }
    }
}
