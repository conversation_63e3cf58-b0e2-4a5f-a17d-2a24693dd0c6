﻿using Akka.Actor;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.DeviceDataFlow.StaticData;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.DataFlow;
using System.Diagnostics;

namespace Siemens.PanelManager.DeviceDataFlow.DataFlowActors
{
    internal abstract class AssetActorBase : ReceiveActor
    {
        private int _assetId;
        protected AssetActorBase(AssetSimpleInfo simpleInfo, IServiceProvider provider, ILogger logger, SiemensCache cache)
        {
            _assetId = simpleInfo.AssetId;
            Provider = provider;
            Logger = logger;
            Cache = cache;

            ReceiveAsync<AssetInputData>(InputDataFunc);
            ReceiveAsync<AssetChangeData>(ChangeDataFunc);
        }

        protected string LoggerName { get; set; } = string.Empty;
        protected bool NeedLog { get; set; } = false;

        protected SiemensCache Cache { get; private set; }
        protected IServiceProvider Provider { get; set; }

        private AssetSimpleInfo? _simpleInfo = null;
        protected AssetSimpleInfo AssetSimpleInfo
        {
            get
            {
                if (_simpleInfo == null)
                {
                    _simpleInfo = Cache.Get<AssetSimpleInfo>(string.Format(Constant.AssetSimpleInfoCacheKey, _assetId));
                    if (_simpleInfo == null)
                    {
                        throw new Exception("Actor对应资产不存在");
                    }
                }
                return _simpleInfo;
            }
        }
        protected ILogger Logger { get; set; }

        protected override bool AroundReceive(Receive receive, object message)
        {
            var result = true;

            if (!string.IsNullOrEmpty(LoggerName) && NeedLog)
            {
                var logger = AkkaRuntimeLoggerManager.GetLogger(LoggerName);
                if (logger != null)
                {
                    var key = logger.Start();
                    try
                    {
                        result = base.AroundReceive(receive, message);

                    }
                    catch (Exception ex)
                    {
                        Logger.LogError(ex, $"AroundReceive Failed");
                    }
                    logger.Stop(key);
                    return result;
                }
            }

            try
            {
                result = base.AroundReceive(receive, message);
                
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"AroundReceive Failed");
            }
            // 每个消息内simpleInfo 不刷新
            // 新消息重新获取simpleInfo
            _simpleInfo = null;
            return result;
        }

        protected abstract Task InputDataFunc(AssetInputData inputData);
        protected abstract Task ChangeDataFunc(AssetChangeData changeData);
    }
}
