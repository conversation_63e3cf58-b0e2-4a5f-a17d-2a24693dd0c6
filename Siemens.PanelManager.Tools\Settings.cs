﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Tools
{
    internal class Settings
    {
        [JsonProperty(PropertyName = "ip")]
        public string Ip { get; set; } = string.Empty;
        [JsonProperty(PropertyName = "port")]
        public int Port { get; set; } = 51030;

        public async Task SaveToFile()
        {
            if (File.Exists("settings.json"))
            {
                File.Delete("settings.json");
            }
            using (var sw = new StreamWriter("settings.json"))
            {
                await sw.WriteAsync(JsonConvert.SerializeObject(this));
            }
        }

        public static Settings ReadByFile()
        {
            Settings? settings = null;
            if (File.Exists("settings.json")) 
            {
                using (var sr = new StreamReader("settings.json"))
                {
                    try
                    {
                        settings = JsonConvert.DeserializeObject<Settings>(sr.ReadToEnd());
                    }
                    catch
                    {
                        settings = null;
                    }
                }
            }

            if(settings == null)
            {
                settings = new Settings();
            }

            return settings;
        }
    }
}
