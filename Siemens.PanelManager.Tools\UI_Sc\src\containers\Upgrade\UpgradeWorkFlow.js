import { IxWorkflowStep, IxWorkflowSteps } from "@siemens/ix-react";
import "./upgradeWorkFlow.css";
import { useState } from "react";

export default function UpgradeWorkFlow(props) {
  const steps = JSON.parse(sessionStorage.getItem("upgradeSteps"));
  const [textData, setTextData] = useState({ time: 0, message: "" });
  if (props.logText !== "" && textData.time != props.time) {
    setTextData({
      time: props.time,
      message: textData.message + props.logText,
    });
  }
  let stepItems = [];
  let currentStatusObj = {};
  if (!!props.currentStatus) {
    currentStatusObj = JSON.parse(props.currentStatus);
  }
  steps?.forEach((element, n) => {
    let stepStatus = currentStatusObj[n + 1 + ""];
    if (stepStatus === "Begin") {
      stepItems.push(<IxWorkflowStep status="open">{element}</IxWorkflowStep>);
    } else if (stepStatus === "Finish") {
      stepItems.push(<IxWorkflowStep status="done">{element}</IxWorkflowStep>);
    } else if (stepStatus === "Fail") {
      stepItems.push(<IxWorkflowStep status="error">{element}</IxWorkflowStep>);
    } else {
      stepItems.push(<IxWorkflowStep disabled>{element}</IxWorkflowStep>);
    }
  });
  return (
    <div className="upgradeWorkFlow-body">
      <div className="upgradeWorkFlow-workflow">
        <IxWorkflowSteps>{stepItems}</IxWorkflowSteps>
      </div>
      <textarea
        className="upgradeWorkFlow-text form-control"
        wrap="hard"
        value={textData.message}
      ></textarea>
    </div>
  );
}
