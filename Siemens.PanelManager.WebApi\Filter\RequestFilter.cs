﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Siemens.PanelManager.Common.Log;
using Siemens.PanelManager.WebApi.ViewModel;

namespace Siemens.PanelManager.WebApi.Filter
{
    public class RequestFilter : IException<PERSON>ilter, I<PERSON><PERSON><PERSON>ilter, IResultFilter
    {
        private ILogger<RequestFilter> _logger;
        private ApiLogger _apiLogger;

        public RequestFilter(ILogger<RequestFilter> logger, ApiLogger apiLogger) 
        {
            _logger = logger;
            _apiLogger = apiLogger;
        }

        #region IExceptionFilter
        public void OnActionExecuted(ActionExecutedContext context)
        {
            _apiLogger.LogInfo(context.HttpContext.Request.Path, "end", context.HttpContext.Request.Method);
        }
        #endregion

        public void OnActionExecuting(ActionExecutingContext context)
        {
            _apiLogger.LogInfo(context.HttpContext.Request.Path, "start", context.HttpContext.Request.Method);
        }

        public void OnException(ExceptionContext context)
        {
            if (context.Exception != null)
            {
                _logger.LogError(context.Exception, $"{context.HttpContext.Request.Path} 请求失败");
            }
            context.Result = new JsonResult(new ResponseBase<string>()
            {
                Code = 50000,
                Data = null,
                Message = "Server exception"
            });
        }

        public void OnResultExecuted(ResultExecutedContext context)
        {
            _logger.LogDebug($"{context.HttpContext.Request.Path}--{context.HttpContext.Request.Method}--finish");
        }

        public void OnResultExecuting(ResultExecutingContext context)
        {

        }
    }
}
