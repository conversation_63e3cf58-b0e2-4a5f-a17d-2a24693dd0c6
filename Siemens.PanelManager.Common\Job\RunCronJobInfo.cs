﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Common.Job
{
    internal class RunCronJobInfo
    {
        public RunCronJobInfo(string jobName,
            string cron, Type jobType)
        {
            JobName = jobName;
            Cron = cron;
            JobType = jobType;
        }

        public Type JobType { get; set; }
        public string JobName { get; set; }
        public string Cron { get; set; }
        public Dictionary<string, string>? Parameters { get; set; }
        public string? JobKey { get; set; }

    }
}
