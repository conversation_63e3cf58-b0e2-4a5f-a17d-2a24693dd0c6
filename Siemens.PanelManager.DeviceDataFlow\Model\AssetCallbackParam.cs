﻿using Siemens.PanelManager.Model.DataFlow;

namespace Siemens.PanelManager.DeviceDataFlow.Model
{
    internal class AssetCallbackParam
    {
        public AssetCallbackParam(AssetChangeData data, Action<AssetChangeData> action) 
        {
            ChangeData = data;
            Action = action;
        }
        public AssetChangeData ChangeData { get; private set; }
        public Action<AssetChangeData> Action { get; private set; }
    }
}
