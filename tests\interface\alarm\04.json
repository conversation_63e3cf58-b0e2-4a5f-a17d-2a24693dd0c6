{"info": {"_postman_id": "05da0f28-dd7b-4005-ad98-f4ea86cedc3f", "name": "04使用管理员账号进入panel manager告警管理中的告警列表菜单，查看全部，最近一周，最近一个月，最近三个月，选择时间段告警统计", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 4", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "获取告警全部信息总览 Copy 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"显示全部未确认告警，已确认告警，告警总数，高级告警，中级告警，低级告警\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"highCount\");\r", "    pm.expect(pm.response.text()).to.include(\"lowCount\");\r", "    pm.expect(pm.response.text()).to.include(\"middleCount\");\r", "    pm.expect(pm.response.text()).to.include(\"totalCount\");\r", "    pm.expect(pm.response.text()).to.include(\"finishCount\");\r", "    pm.expect(pm.response.text()).to.include(\"ignoreCount\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm/GetCurrentInfo?startTime=&endTime=", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm", "GetCurrentInfo"], "query": [{"key": "startTime", "value": ""}, {"key": "endTime", "value": ""}]}}, "response": []}, {"name": "获取告警一周信息总览 Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"显示一周未确认告警，已确认告警，告警总数，高级告警，中级告警，低级告警\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"highCount\");\r", "    pm.expect(pm.response.text()).to.include(\"lowCount\");\r", "    pm.expect(pm.response.text()).to.include(\"middleCount\");\r", "    pm.expect(pm.response.text()).to.include(\"totalCount\");\r", "    pm.expect(pm.response.text()).to.include(\"finishCount\");\r", "    pm.expect(pm.response.text()).to.include(\"ignoreCount\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm/GetCurrentInfo?startTime=1680578082&endTime=1681182882", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm", "GetCurrentInfo"], "query": [{"key": "startTime", "value": "1680578082"}, {"key": "endTime", "value": "1681182882"}]}}, "response": []}, {"name": "获取告警一个月信息总览 Copy 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"显示一个月未确认告警，已确认告警，告警总数，高级告警，中级告警，低级告警\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"highCount\");\r", "    pm.expect(pm.response.text()).to.include(\"lowCount\");\r", "    pm.expect(pm.response.text()).to.include(\"middleCount\");\r", "    pm.expect(pm.response.text()).to.include(\"totalCount\");\r", "    pm.expect(pm.response.text()).to.include(\"finishCount\");\r", "    pm.expect(pm.response.text()).to.include(\"ignoreCount\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm/GetCurrentInfo?startTime=1678590927&endTime=1681182927", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm", "GetCurrentInfo"], "query": [{"key": "startTime", "value": "1678590927"}, {"key": "endTime", "value": "1681182927"}]}}, "response": []}, {"name": "获取告警三个月信息总览 Copy 3", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"显示三个月未确认告警，已确认告警，告警总数，高级告警，中级告警，低级告警\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"highCount\");\r", "    pm.expect(pm.response.text()).to.include(\"lowCount\");\r", "    pm.expect(pm.response.text()).to.include(\"middleCount\");\r", "    pm.expect(pm.response.text()).to.include(\"totalCount\");\r", "    pm.expect(pm.response.text()).to.include(\"finishCount\");\r", "    pm.expect(pm.response.text()).to.include(\"ignoreCount\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm/GetCurrentInfo?startTime=1673407755&endTime=1681183755", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm", "GetCurrentInfo"], "query": [{"key": "startTime", "value": "1673407755"}, {"key": "endTime", "value": "1681183755"}]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 1000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", "pm.test(\"code码为20000\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData[\"code\"]).to.eql(20000);", "});", ""]}}]}