﻿using Akka.Event;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Quartz;
using Siemens.PanelManager.Model.Database.System;
using SqlSugar;
using System.Collections.Concurrent;
using System.Text;

namespace Siemens.PanelManager.Common.Job
{
    internal static class JobStaticManager
    {
        public const string NoLogServer = "NoLogServer";
        private static JobSchedule[] _schedules = new JobSchedule[0];
        private static string[] _existsJobs = new string[0];
        private static ConcurrentDictionary<string, JobKey> _cronJobs = new ConcurrentDictionary<string, JobKey>();
        private static IScheduler? _scheduler = null;
        private static ConcurrentQueue<RunCronJobInfo> _cronJobQueue = new ConcurrentQueue<RunCronJobInfo>();

        private static Dictionary<string, IJobDetail> _singleJobDetails = new Dictionary<string, IJobDetail>();
        private static Dictionary<string, IJobDetail> _serviceJobDetails = new Dictionary<string, IJobDetail>();
        public static RunOnceJobInfo[] GetNext(string code)
        {
            var nextJobs = _schedules.Where(s=>s.ParentJobCode == code && !s.IsMain).ToArray();

            var jobs = new List<RunOnceJobInfo>();
            foreach (var j in nextJobs)
            {
                if (!_existsJobs.Contains(j.JobName))
                {
                    var jobKey = new JobKey($"{j.JobCode}-{j.JobName}");
                    if (string.IsNullOrEmpty(j.Parameters)) 
                    {
                        jobs.Add(new RunOnceJobInfo(jobKey));
                    }
                    else 
                    {
                        Dictionary<string, string> parameters = new Dictionary<string, string>();
                        try 
                        {
                            parameters = JsonConvert.DeserializeObject<Dictionary<string, string>>(j.Parameters);
                        }
                        catch (Exception ex) 
                        {
                            Log.LogHelper.Error($"计划{j.Id}参数解析失败", ex);
                        }

                        jobs.Add(new RunOnceJobInfo(jobKey, parameters));
                    }
                    
                }
            }

            return jobs.ToArray();
        }

        public static async Task StartJobServer(IScheduler scheduler, 
            IServiceProvider serviceProvider, 
            ILogger<JobHostedService> logger)
        {
            _scheduler = scheduler;
            var client = serviceProvider.GetService<SqlSugarScope>();
            _schedules = await client.Queryable<JobSchedule>().ToArrayAsync();
            var jobs = serviceProvider.GetServices<JobBase>();
            var jobNames = new List<string>();
            foreach (var job in jobs) 
            {
                var jobType = job.GetType();
                if (jobNames.Contains(job.Name))
                {
                    var message = new StringBuilder();
                    message.AppendLine($"{job.Name} 已经存在");
                    message.AppendLine($"命名空间: {jobType.Namespace}");
                    message.AppendLine($"类名: {jobType.FullName}");

                    logger.LogError(message.ToString());
                    continue;
                }
                jobNames.Add(job.Name);
                var singleJobKey = new JobKey($"Single-{job.Name}", "Single");
                var singleJob = JobBuilder.Create(jobType)
                        .WithIdentity(singleJobKey)
                        .Build();

                _singleJobDetails.Add(singleJobKey.Name, singleJob);
                var schedules = _schedules.Where(s=>s.JobName == job.Name).ToArray();
                foreach (var schedule in schedules)
                {
                    var serverName = NoLogServer;
                    if (schedule.NeedRecordRuntime)
                    {
                        serverName = "Server";
                    }

                    var jobKey = new JobKey($"{schedule.JobCode}-{schedule.JobName}", serverName);
                    var jobDataMap = new JobDataMap();
                    
                    if (!string.IsNullOrEmpty(schedule.Parameters))
                    {
                        try
                        {
                            var param = JsonConvert.DeserializeObject<Dictionary<string, string>>(schedule.Parameters);

                            foreach(var key in param.Keys) 
                            {
                                var value = param[key];
                                if (string.IsNullOrEmpty(value)) continue;
                                if (value.IndexOf("{{") == 0) continue;

                                jobDataMap.Add(key, value);
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.LogError($"计划{schedule.Id}参数解析失败", ex);
                        }
                    }

                    var jobDetail = JobBuilder.Create(jobType)
                        .UsingJobData(jobDataMap)
                        .WithIdentity(jobKey)
                        .Build();
                    
                    _serviceJobDetails.Add(jobKey.Name, jobDetail);


                    List<ITrigger> triggers = new List<ITrigger>();
                    if (schedule.IsMain && !string.IsNullOrEmpty(schedule.Cron))
                    {
                        var trigger = TriggerBuilder.Create()
                            .WithCronSchedule(schedule.Cron)
                            .StartAt(DateTime.Now.AddSeconds(5))
                            .Build();

                        triggers.Add(trigger);
                    }

                    if (schedule.NeedRun ?? false)
                    {
                        var runOnceTrigger = TriggerBuilder.Create()
                            .WithSimpleSchedule()
                            .UsingJobData(jobDataMap)
                            .StartNow()
                            .Build();
                        triggers.Add(runOnceTrigger);
                    }

                    if(triggers.Count > 0)
                    {
                        await scheduler.ScheduleJob(jobDetail, triggers, true);
                    }
                }
            }

            _existsJobs = jobNames.ToArray();

            while (_cronJobQueue.TryDequeue(out var runCronJobInfo))
            {
                await TriggerCronJob(runCronJobInfo);
            }
        }

        public static async Task<bool> TriggerJob(RunOnceJobInfo info)
        {
            if (_scheduler == null)
            {
                Log.LogHelper.Error("任务功能没有初始化");
                return false;
            }

            if (!_existsJobs.Any(n => n == info.Name))
            {
                return false;
            }

            var jobData = new JobDataMap();

            if(info.Parameters != null) 
            {
                foreach (var key in info.Parameters.Keys)
                {
                    jobData.Add(key, info.Parameters[key]);
                }
            }

            try
            {
                var runOnceTrigger = TriggerBuilder.Create().WithSimpleSchedule().UsingJobData(jobData).StartNow().Build();

                if (info.Key != null)
                {
                    if (_serviceJobDetails.TryGetValue(info.Key.Name, out IJobDetail? jobDetail) && jobDetail != null)
                    {
                        await _scheduler.ScheduleJob(jobDetail, runOnceTrigger);
                    }
                }
                else
                {
                    if (_singleJobDetails.TryGetValue($"Single-{info.Name}", out IJobDetail? jobDetail) && jobDetail != null)
                    {
                        await _scheduler.ScheduleJob(jobDetail, runOnceTrigger);
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                Log.LogHelper.Error($"{info.Name} 启动失败", ex);
                return false;
            }
        }

        public static async Task<bool> TriggerCronJob(RunCronJobInfo info)
        {
            if (_scheduler == null)
            {
                Log.LogHelper.Error("任务功能没有初始化");
                _cronJobQueue.Enqueue(info);
                if (string.IsNullOrEmpty(info.JobKey))
                {
                    info.JobKey = $"{Guid.NewGuid().ToString()}-{info.JobType.Name}";
                }
                return true;
            }

            if (string.IsNullOrEmpty(info.JobName))
            {
                Log.LogHelper.Info("TriggerCronJob参数错误");
                return false;
            }

            bool needCheckExists = true;
            if (string.IsNullOrEmpty(info.JobKey))
            {
                needCheckExists = false;
                info.JobKey = $"{Guid.NewGuid().ToString()}-{info.JobType.Name}";
            }

            var newJobDetails = JobBuilder.Create(info.JobType)
                .WithIdentity(info.JobKey, "CustomCronJob")
                .Build();

            if (needCheckExists)
            {
                var d = await _scheduler.GetJobDetail(newJobDetails.Key);

                if (d != null)
                {
                    return false;
                }
            }

            if (info.Parameters != null && info.Parameters.Count > 0)
            {
                foreach (var kv in info.Parameters)
                {
                    if (string.IsNullOrEmpty(kv.Value)) continue;
                    newJobDetails.JobDataMap.Add(kv.Key, kv.Value);
                }
            }

            var trigger = TriggerBuilder.Create()
                .WithCronSchedule(info.Cron)
                .Build();

            await _scheduler.ScheduleJob(newJobDetails, trigger);
            return true;
        }

        public static async Task<bool> DeleteCronJob(string key)
        {
            if (_scheduler == null)
            {
                Log.LogHelper.Error("任务功能没有初始化");
                return false;
            }
            var jobKey = new JobKey(key, "CustomCronJob");
            var result = await _scheduler.DeleteJob(jobKey);
            return result;
        }
    }
}

