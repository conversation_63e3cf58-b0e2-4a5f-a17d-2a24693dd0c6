﻿using Microsoft.Extensions.DependencyInjection;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.Database.Topology;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Server.Topology
{
    public class TopologyExtendFunc
    {
        private readonly IServiceProvider _provider;
        public TopologyExtendFunc(IServiceProvider provider)
        {
            _provider = provider;
        }

        public async Task<bool> CheckUniqueCode(string code, string topoplogyType, ISqlSugarClient client)
        {
            if (string.IsNullOrEmpty(code)) return false;
            return !await client.Queryable<TopologyInfo>().AnyAsync(t => t.Code == code && t.Type == topoplogyType);
        }

        public async Task DeleteTopologyByTopologyId(int topologyId, ISqlSugarClient client)
        {
            await client.Deleteable<TopologyIdManager>().Where(t=>t.Key == topologyId).ExecuteCommandAsync();
            await client.Deleteable<TopologyRuleInfo>().Where(t => t.TopologyId == topologyId).ExecuteCommandAsync();
            await client.Deleteable<TopologyInfo>().Where(t => t.Id == topologyId).ExecuteCommandAsync();

            var refObj = _provider.GetRequiredService<ITopologyDataChangeRef>();
            if (refObj != null)
            {
                refObj.RemoveTopologys(new int[] { topologyId });
            }
        }

        public async Task DeleteTopologyByTopologyIds(int[] topologyIds, ISqlSugarClient client)
        {
            var r = await client.Deleteable<TopologyIdManager>().Where(t => topologyIds.Contains(t.Key)).ExecuteCommandAsync();
            r = await client.Deleteable<TopologyRuleInfo>().Where(t => topologyIds.Contains(t.TopologyId)).ExecuteCommandAsync();
            r = await client.Deleteable<TopologyInfo>().Where(t => topologyIds.Contains(t.Id)).ExecuteCommandAsync();

            var refObj = _provider.GetRequiredService<ITopologyDataChangeRef>();
            if (refObj != null)
            {
                refObj.RemoveTopologys(topologyIds);
            }
        }
    }
}
