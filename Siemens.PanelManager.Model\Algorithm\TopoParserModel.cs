﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Algorithm
{
    public class TopoParserModel
    {
        public int Key { get; set; }
        public string Type { get;set; }
        public string Name { get; set; }
        public List<int> ChildNode { get; set; }

        public TopoParserModel(int key,string type,string name) {
            Key = key;
            Type = type;
            Name = name;
            ChildNode = new List<int>();
        }
    }
}
