﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class SystemDictController : SiemensApiControllerBase
    {
        private ILogger<SystemDictController> _log;
        private ISqlSugarClient _client;

        public SystemDictController(SiemensCache cache, SqlSugarScope client, IServiceProvider provider, ILogger<SystemDictController> logger)
            : base(provider, cache)
        {
            _log = logger;
            _client = client;
        }

        [HttpGet("{dictionaryType}")]
        [SwaggerOperation(Summary = "Swagger_SystemDict_GetSystemDict", Description = "Swagger_SystemDict_GetSystemDict_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<List<AssetStaticModelResult>>> GetModel(string dictionaryType, bool appendAll = false)
        {
            dictionaryType = dictionaryType.ToUpper();

            var models = await _client.Queryable<SystemStaticModel>()
                .Where(a => a.Type == dictionaryType)
                .WithCache($"SystemStaticModel:{dictionaryType}", (int)TimeSpan.FromHours(1).TotalSeconds)
                .ToArrayAsync();

            List<AssetStaticModelResult> result = new List<AssetStaticModelResult>();
            if (models != null)
            {
                if (appendAll)
                {
                    result.Add(new AssetStaticModelResult { Name = UserLanguage?.ToLower() == "zh-cn" ? "全部" : "ALL", Code = "ALL" });
                }
                result.AddRange(models.Select(a => new AssetStaticModelResult(a, MessageContext)).ToList());
            }

            return new ResponseBase<List<AssetStaticModelResult>>()
            {
                Code = 20000,
                Data = result
            };
        }
    }
}
