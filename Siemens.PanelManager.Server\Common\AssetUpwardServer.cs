﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.Enum;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Model.Alarm;
using Siemens.PanelManager.Model.Algorithm;
using Siemens.PanelManager.Model.Chart;
using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Server.Algorithm;
using Siemens.PanelManager.Server.Asset;
using SqlSugar;
using System.ComponentModel;

namespace Siemens.PanelManager.Server.Common
{
    /// <summary>
    /// 资产上送逻辑
    /// </summary>
    public class AssetUpwardServer
    {
        private readonly SqlSugarScope _db;

        private readonly ILogger _log;

        private readonly SiemensCache _cache;

        private readonly IServiceProvider _provider;

        /// <summary>
        /// 构造函数初始化
        /// </summary>
        /// <param name="provider"></param>
        /// <param name="log"></param>
        /// <param name="cache"></param>
        public AssetUpwardServer(IServiceProvider provider, ILogger<AssetUpwardServer> log, SiemensCache cache)
        {
            _db = provider.GetService<SqlSugarScope>()!;
            _provider = provider;
            _log = log;
            _cache = cache;
        }

        /// <summary>
        /// 获取配电房的属性数据
        /// </summary>
        /// <returns></returns>
        public async Task<SubStationUpwardDto> GetSubStationInfo(int assetId)
        {
            SubStationUpwardDto? entity = null;
            var _lossDiagnose = _provider.GetRequiredService<LossDiagnose>();

            try
            {
                entity = _cache.Get<SubStationUpwardDto>($"AssetUpwardServer_GetSubStationInfo_{assetId}");

                if (entity != null)
                {
                    return entity;
                }
                else
                {
                    int topoId = 0;
                    var assetInfo = await _db.Queryable<AssetInfo>().FirstAsync(a => a.Id == assetId && a.AssetLevel == AssetLevel.Substation);

                    if (assetInfo != null && assetInfo.TopologyId.HasValue)
                    {
                        entity = new SubStationUpwardDto() { AssetName = assetInfo.AssetName };

                        topoId = (int)assetInfo.TopologyId;

                        List<Task> tasks = new List<Task>();

                        tasks.Add(Task.Factory.StartNew(async () =>
                        {
                            Dictionary<string, object> requestBody = new Dictionary<string, object>();
                            requestBody.Add("searchTime", GetDateTime());
                            requestBody.Add("topo", topoId);
                            var response = await _lossDiagnose.GetSysLossdataApi(requestBody, "AlgorithmEvaluationApiPath", "searchCabinet");
                            if (response != null && !response.Contains("<!doctype html>"))
                            {
                               var dataAPI = JsonConvert.DeserializeObject<CabinetHealthStatistics>(response);

                                //健康状态
                                entity.HealthStatus = dataAPI?.excellent > 0 ? "优" : dataAPI?.good > 0 ? "良"
                                                                                 : dataAPI?.medium > 0 ? "中" : "差";
                            }
                        }));

                        tasks.Add(Task.Factory.StartNew(async () =>
                        {
                            Dictionary<string, object> requestBody = new Dictionary<string, object>();
                            requestBody.Add("topology_id", topoId.ToString());
                            requestBody.Add("searchTimeType", "日");
                            requestBody.Add("searchType", "base");
                            requestBody.Add("searchTime", DateTime.Now.ToString("yyyy-MM-dd"));
                            var response = await _lossDiagnose.GetSysLossdataApi(requestBody, "AlgorithmEvaluationApiPath", "/loss/loss_rmb");

                            if (response != null && !response.Contains("<!doctype html>"))
                            {
                                var result = JsonConvert.DeserializeObject<lossForCNYModel>(response);

                                if (result != null)
                                {
                                    entity.SubStationLoss = result.lossP.Any(p => p > 0) ? result.lossP.Last(p => p > 0).ToString() : "0";
                                    entity.SubStationPercentage = result.line.Any(p => p > 0) ? result.line.Last(p => p > 0).ToString() : "0";
                                    entity.SubStationSafetyScope = result.referLossP.Any(p => p > 0) ? result.referLossP.Last(p => p > 0).ToString() : "0";
                                }
                            }

                        }));

                        tasks.Add(Task.Factory.StartNew(async () =>
                        {
                            Dictionary<string, object> requestBody = new Dictionary<string, object>();
                            requestBody.Add("currentTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                            requestBody.Add("topology_id", topoId.ToString());
                            var response = await _lossDiagnose.GetSysLossKpiApi(requestBody);
                            if (response != null && !response.Contains("<!doctype html>"))
                            {
                                var result = JsonConvert.DeserializeObject<LossDiagnosisByKpiDto>(response);
                                if (result != null)
                                {
                                    entity.SubStationDayEfficiency = result.Current.SystemEfficiency;
                                }
                            }
                        }));

                        await Task.WhenAll(tasks);

                        _log.LogInformation("AssetUpwardServer_GetSubStationInfo的完成详细信息:" + JsonConvert.SerializeObject(entity));

                        _cache.Set($"AssetUpwardServer_GetSubStationInfo_{assetId}", entity, TimeSpan.FromMinutes(GetRandom(10,15)));
                    }
                }
            }
            catch (Exception ex)
            {
                entity = null;
                _log.LogError("AssetUpwardServer_GetSubStationInfo:" + ex.Message);
            }

            return entity;
        }

        /// <summary>
        /// 获取配电柜信息
        /// </summary>
        /// <param name="assetId"></param>
        public async Task<PanelUpwardDto> GetCabinetHealthInfo(int assetId)
        {
            PanelUpwardDto? entity = null;
            var _lossDiagnose = _provider.GetRequiredService<LossDiagnose>();

            try
            {
                entity = _cache.Get<PanelUpwardDto>($"AssetUpwardServer_GetCabinetHealthInfo_{assetId}");

                if (entity != null)
                {
                    return entity;
                }
                else
                {
                    int topoId = 0;
                    var assetRelation = await _db.Queryable<AssetRelation>().Where(p => p.ChildId == assetId).FirstAsync();
                    var assetInfo = _db.Queryable<AssetInfo>().First(a => a.Id == assetRelation.ParentId && a.AssetLevel == AssetLevel.Substation);
                    if (assetInfo != null && assetInfo.TopologyId.HasValue)
                    {
                        topoId = (int)assetInfo.TopologyId;
                        entity = new PanelUpwardDto();
                        var panelHealthModels = new List<PanelHealthModel>();
                        var panelHealths = _db.Queryable<PanelHealth>().Where(p => p.AssetId == assetId).OrderBy(p => p.Id).ToArray();
                        Dictionary<string, object> customizeParams = new Dictionary<string, object>();
                        Dictionary<string, object> requestBody = new Dictionary<string, object>();
                        requestBody.Add("searchTime", GetDateTime());
                        requestBody.Add("topo", topoId);
                        requestBody.Add("topoLevel", AssetLevel.Panel);
                        requestBody.Add("assetId", assetId);

                        foreach (var panelHealth in panelHealths.Where(i => i.IsSystem == false))
                        {
                            var param = new Dictionary<string, decimal>();
                            var limit = JsonConvert.DeserializeObject<int[]>(panelHealth.Limit);
                            if (limit != null && limit.Length > 1)
                            {
                                param.Add("max", limit[1]);
                                param.Add("min", limit[0]);
                            }
                            else
                            {
                                param.Add("max", 10);
                                param.Add("min", 0);
                            }

                            param.Add("value", panelHealth.Value);
                            param.Add("weight", panelHealth.Weight);
                            customizeParams.Add(panelHealth.Code, param);
                        }

                        if (customizeParams != null)
                        {
                            requestBody.Add("param", customizeParams);
                        }

                        string response = "";

                        if (customizeParams != null && customizeParams.Count > 0)
                        {
                            response = await _lossDiagnose.GetSysLossdataApi(requestBody, "AlgorithmEvaluationApiPath", "customize");
                        }
                        else
                        {
                            response = await _lossDiagnose.GetSysLossdataApi(requestBody, "AlgorithmEvaluationApiPath", "search");
                        }

                        if (response != null && !response.Contains("<!doctype html>"))
                        {
                            var dataAPI = JsonConvert.DeserializeObject<HealthEvaluationModel>(response);
                            int reValue = Convert.ToInt32(dataAPI?.evaluationResult?.result ?? 0);
                            string grade = EnumUtil.Instance.GetDepict<EvaluationType>(reValue) ?? "";
                            if (dataAPI != null && !string.IsNullOrWhiteSpace(grade))
                            {
                                entity.HealthScore = Math.Round(dataAPI.score, 2, MidpointRounding.AwayFromZero);
                                entity.HealthGrade = grade;

                                if (panelHealths != null && panelHealths.Any())
                                {
                                    int abnormalCount = 0;

                                    foreach (var panelHealth in panelHealths)
                                    {
                                        var model = new PanelHealthModel()
                                        {
                                            Id = panelHealth.Id,
                                            Name = panelHealth.Name ?? "",
                                            Code = panelHealth.Code ?? "",
                                            Value = panelHealth.Value,
                                            Weight = panelHealth.Weight,
                                            Message = "",
                                            Status = "正常"
                                        };

                                        switch (panelHealth.Code)
                                        {
                                            case "MaxElectricity":
                                                model.Value = Math.Round(dataAPI!.branchCurrent, 2, MidpointRounding.AwayFromZero);
                                                model.Message = dataAPI.branchCurrentMsg;
                                                model.Name = "本柜最高电流测点(A)";
                                                entity.MaxElectricity = model.Value ?? 0;
                                                break;

                                            case "RemainingLife":
                                                model.Value = dataAPI!.CBs;
                                                model.Message = dataAPI.CBsMsg;
                                                model.Name = "本柜断路器剩余寿命(%)";
                                                entity.RemainingLifePercentage = model.Value ?? 0;
                                                break;

                                            case "MaxTemperature":
                                                model.Value = Math.Round(Convert.ToDecimal(dataAPI!.temperature), 2, MidpointRounding.AwayFromZero);
                                                model.Message = dataAPI.temperatureMsg;
                                                model.Name = "本柜最高温度测点(℃)";
                                                entity.MaxTemperature = model.Value ?? 0;
                                                break;
                                        }

                                        if (dataAPI?.IndicatorResult != null && dataAPI.IndicatorResult.ContainsKey(panelHealth.Code ?? ""))
                                        {
                                            int indicatorResult = Convert.ToInt32(dataAPI.IndicatorResult[panelHealth.Code!].result);
                                            switch (indicatorResult)
                                            {
                                                case 1:
                                                    model.Status = "异常";
                                                    model.Message = "存在风险";
                                                    abnormalCount++;
                                                    break;
                                                default:
                                                    model.Status = "正常";
                                                    break;
                                            }
                                        }

                                        // 这几个从panelHealthModels中排除
                                        if (panelHealth.Code == "MaxElectricity"
                                            || panelHealth.Code == "RemainingLife"
                                            || panelHealth.Code == "MaxTemperature")
                                        {
                                            switch (panelHealth.Code)
                                            {
                                                case "MaxElectricity":
                                                    entity.MaxElectricityStatus = model.Status;
                                                    break;
                                                case "RemainingLife":
                                                    entity.RemainingLifeStatus = model.Status;
                                                    break;
                                                case "MaxTemperature":
                                                    entity.MaxTemperatureStatus = model.Status;
                                                    break;
                                            }

                                            continue;
                                        }

                                        if (panelHealth.IsSystem)
                                        {
                                            if (!string.IsNullOrEmpty(panelHealth.ParentCode))
                                            {
                                                var panertPanelHealth = panelHealths.FirstOrDefault(p => p.Code == panelHealth.ParentCode);
                                                if (panertPanelHealth != null)
                                                {
                                                    var panelHealthModel = panelHealthModels.FirstOrDefault(pm => pm.Id == panertPanelHealth.Id);
                                                    if (panelHealthModel != null)
                                                    {
                                                        if (panelHealthModel.Children == null)
                                                        {
                                                            panelHealthModel.Children = new List<PanelHealthModel>();
                                                        }
                                                        panelHealthModel.Children.Add(model);

                                                        continue;
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                var children = panelHealths.Where(p => p.ParentCode == panelHealth.Code).ToArray();
                                                if (children != null && children.Length > 0)
                                                {
                                                    model.Children = new List<PanelHealthModel>();
                                                    var models = panelHealthModels.Where(pm => children.Any(c => c.Id == pm.Id)).ToArray();
                                                    if (models != null && models.Length > 0)
                                                    {
                                                        foreach (var m in models)
                                                        {
                                                            if (m == null) continue;
                                                            model.Children.Add(m);
                                                        }
                                                    }
                                                }
                                            }
                                        }

                                        var alarmList = new List<string>();
                                        alarmList.Add("LowLevelAlarmCount");
                                        alarmList.Add("MiddleLevelAlarmCount");
                                        alarmList.Add("HighLevelAlarmCount");
                                        alarmList.Add("AlarmCount");

                                        if (!alarmList.Contains(model.Code))
                                        {
                                            panelHealthModels.Add(model);
                                        }
                                    }

                                    entity.AbnormalCount = abnormalCount;
                                    entity.PanelCustomIndicators = JsonConvert.SerializeObject(panelHealthModels);

                                    var assetByInfo = await _db.Queryable<AssetInfo>().FirstAsync(p => p.Id == assetId);

                                    if (assetByInfo != null)
                                    {
                                        var childsCircuit = await _db.Queryable<AssetRelation>().Where(a => a.ParentId == assetInfo.Id)
                                           .Select(a => a.ChildId).ToListAsync();
                                        var childsDevice = await _db.Queryable<AssetRelation>().Where(a => childsCircuit.Contains(a.ParentId))
                                        .Select(a => a.ChildId).ToListAsync();

                                        entity.AlarmStatusCount = await _db.Queryable<AlarmLog>().Where(p => p.PanelName == assetByInfo.AssetName
                                                                                                      && childsDevice.Contains(p.AssetId ?? 0)).CountAsync();
                                    }
                                }
                            }

                            _cache.Set($"AssetUpwardServer_GetCabinetHealthInfo_{assetId}", entity, TimeSpan.FromMinutes(GetRandom(10,15)));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                entity = null;
                _log.LogError("AssetUpwardServer_GetCabinetHealthInfo:" + ex.Message);
            }

            return entity;
        }

        /// <summary>
        /// 获取3wL容量分析和断路器负载率
        /// </summary>
        /// <param name="assetId"></param>
        public async Task<DeviceBy3WLDto> GetChartDataBy3WL(int assetId)
        {
            DeviceBy3WLDto? entity = null;

            var assetDashboardServer = _provider.GetRequiredService<AssetDashboardServer>();

            try
            {
                entity = _cache.Get<DeviceBy3WLDto>($"AssetUpwardServer_GetChartDataBy3WL_{assetId}");

                if (entity != null)
                {
                    return entity;
                }
                else
                {
                    var messageContext = _cache.Get<IMessageContext>("MessageByContext");

                    if (messageContext != null)
                    {
                        entity = new DeviceBy3WLDto();

                        var dicCapacityAnalysis = new Dictionary<string, string>()
                        {
                            {"ChartDateType","0"},
                            {"AssetId",assetId.ToString()},
                            {"StartDate",DateTime.Now.ToString("yyyy-MM-dd")},
                            {"EndDate",DateTime.Now.ToString("yyyy-MM-dd")}
                        };

                        //获取设备的详细信息
                        var details = await _db.Queryable<DeviceDetails>().FirstAsync(d => d.AssetId == assetId);

                        // 容量分析
                        var capacityAnalysis = await assetDashboardServer.GetDashboard("CapacityAnalysis", messageContext!, dicCapacityAnalysis, _db) as LineChartModel;

                        if (capacityAnalysis != null && capacityAnalysis.Y1 != null && capacityAnalysis.Y1.Any())
                        {
                            entity.CapacityAnalysis = capacityAnalysis.Y1.Any(p => p > 0) ? capacityAnalysis.Y1.Last(p => p > 0).ToString() : "0";
                        }

                        // 断路器负载率
                        if (details != null && details.RatedCurrent.HasValue)
                        {
                            var dicLoadRate = new Dictionary<string, string>()
                            {
                                {"ChartDateType","0"},
                                {"AssetId",assetId.ToString()},
                                {"StartDate",DateTime.Now.ToString("yyyy-MM-dd")},
                                {"EndDate",DateTime.Now.ToString("yyyy-MM-dd")},
                                {"RatedCurrent",details.RatedCurrent.Value.ToString()}
                            };

                            var loadRate = await assetDashboardServer.GetDashboard("LoadRate", messageContext!, dicLoadRate, _db) as LineChartModel;

                            if (loadRate != null && loadRate.Y1 != null && loadRate.Y1.Any())
                            {
                                entity.LoadRate = loadRate.Y1.Any(p => p > 0) ? loadRate.Y1.Last(p => p > 0).ToString() : "0";
                            }
                        }

                        _cache.Set($"AssetUpwardServer_GetChartDataBy3WL_{assetId}", entity, TimeSpan.FromMinutes(GetRandom(1,5)));

                    }
                }
            }
            catch (Exception ex)
            {
                entity = null;
                _log.LogError("AssetUpwardServer_GetChartDataBy3WL:" + ex.Message);
            }

            return entity;
        }

        /// <summary>
        /// 获取当前的入参时间
        /// </summary>
        /// <returns></returns>
        private string GetDateTime()
        {
            string dateTime = "";

            DateTime nowTime = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            string hour = DateTime.Now.Hour.ToString();
            DateTime dataTime1 = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd") + $" {hour}:00:00");
            DateTime dataTime2 = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd") + $" {hour}:15:00");
            DateTime dataTime3 = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd") + $" {hour}:30:00");
            DateTime dataTime4 = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd") + $" {hour}:45:00");

            #region 计算dateTime的值
            if (nowTime >= dataTime1 && nowTime < dataTime2)
            {
                dateTime = dataTime1.ToString("yyyy-MM-dd HH:mm:ss");
            }

            if (nowTime >= dataTime2 && nowTime < dataTime3)
            {
                dateTime = dataTime2.ToString("yyyy-MM-dd HH:mm:ss");
            }

            if (nowTime >= dataTime3 && nowTime < dataTime4)
            {
                dateTime = dataTime3.ToString("yyyy-MM-dd HH:mm:ss");
            }

            if (nowTime >= dataTime4)
            {
                dateTime = dataTime4.ToString("yyyy-MM-dd HH:mm:ss");
            }
            #endregion

            return dateTime;
        }

        /// <summary>
        /// 获取随机值
        /// </summary>
        /// <returns></returns>
        private int GetRandom(int start,int end)
        {
            Random r = new Random();
            int number = r.Next(start, end);
            return number;
        }
    }

    /// <summary>
    /// 枚举
    /// </summary>
    public enum EvaluationType
    {
        [Description("优")]
        excellent = 4,

        [Description("良")]
        good = 3,

        [Description("中")]
        medium = 2,

        [Description("差")]
        bad = 1
    }
}
