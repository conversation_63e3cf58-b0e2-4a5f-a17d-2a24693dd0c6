﻿using Newtonsoft.Json;
using Siemens.PanelManager.Model.Database.Asset;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reactive;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Server.Alarm
{
    internal class AlarmInternalFunc
    {
        public static string GetAssetStatusStr(Dictionary<string, string> values, List<AssetDataPointInfo> pointInfos, bool isGeneralEquipment)
        {
            var result = new Dictionary<string, string>();
            foreach (var item in values)
            {
                var dataPoint = pointInfos.FirstOrDefault(d => d.Code == item.Key);
                if (dataPoint != null)
                {
                    if (isGeneralEquipment)
                    {
                        result.TryAdd($"{dataPoint.Name}", ConventValue(item.Value, (dataPoint.Unit ?? string.Empty)));
                    }
                    else
                    {
                        result.TryAdd(item.Key, ConventValue(item.Value, (dataPoint.Unit ?? string.Empty)));
                    }
                }
            }

            return JsonConvert.SerializeObject(result);
        }

        private static string ConventValue(string value, string unit)
        {
            var result = string.Empty;
            if ("NaN".Equals(value))
            {
                return value;
            }

            if (string.IsNullOrEmpty(value) || string.IsNullOrEmpty(unit))
            {
                return value;
            }

            switch (unit)
            {
                case "var":
                case "varh":
                case "W":
                case "Wh":
                case "VA":
                case "VAh":
                    {
                        if (decimal.TryParse(value, out var valueDecimal))
                        {
                            if (valueDecimal % (1000 * 1000) > 0)
                            {
                                result = Math.Round(valueDecimal / (1000m * 1000m), 2).ToString() + "M" + unit;
                            }
                            else if (valueDecimal % 1000 > 0)
                            {
                                result = Math.Round(valueDecimal / 1000m, 2).ToString() + "k" + unit;
                            }
                            else
                            {
                                result = value + unit;
                            }
                        }
                    }
                    break;
                default:
                    {
                        result = value + unit;
                    }
                    break;
            }
            return result;
        }
    }
}
