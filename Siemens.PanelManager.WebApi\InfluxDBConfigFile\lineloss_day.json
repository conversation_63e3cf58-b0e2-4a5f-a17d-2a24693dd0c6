{"name": "lineloss_day", "status": "active", "flux": "import \"date\"\n\noption task = {\n    name: \"lineloss_day\",\n    every: 1d,\n}\n\nfrom(bucket: \"panel\")\n    |> range(start: -1d)\n    |> filter(fn: (r) => r[\"_measurement\"] == \"lineloss_hour\")\n    |> filter(fn: (r) => r[\"_field\"] == \"mcurrent\")\n    |> mean()\n    |> map(fn: (r) => ({r with _measurement: \"lineloss_day\", _time: date.truncate(t: now(), unit: 1d)}))\n    |> to(bucket: \"panel\", tagColumns: [\"topology_id\", \"line_id\"])\n\nfrom(bucket: \"panel\")\n    |> range(start: -1d)\n    |> filter(fn: (r) => r[\"_measurement\"] == \"lineloss_hour\")\n    |> filter(fn: (r) => r[\"_field\"] == \"sump\")\n    |> sum()\n    |> map(fn: (r) => ({r with _measurement: \"lineloss_day\", _time: date.truncate(t: now(), unit: 1d)}))\n    |> to(bucket: \"panel\", tagColumns: [\"topology_id\", \"line_id\"])\n\nfrom(bucket: \"panel\")\n    |> range(start: -1d)\n    |> filter(fn: (r) => r[\"_measurement\"] == \"lineloss_hour\")\n    |> filter(fn: (r) => r[\"_field\"] == \"sumq\")\n    |> sum()\n    |> map(fn: (r) => ({r with _measurement: \"lineloss_day\", _time: date.truncate(t: now(), unit: 1d)}))\n    |> to(bucket: \"panel\", tagColumns: [\"topology_id\", \"line_id\"])", "every": "1d"}