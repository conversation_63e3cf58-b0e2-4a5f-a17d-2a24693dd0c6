﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Algorithm
{
    [SugarTable("line_impedance")]
    public class LineImpedance : LogicDataBase
    {
        [SugarColumn(ColumnName = "LineTypeId", IsPrimaryKey = true, IsIdentity = true)]
        public int LineTypeId { get; set; }
        [UniquenessAttribute]
        [SugarColumn(ColumnName = "standard", IsNullable = false, Length = 256)]
        public string Standard { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "resistance_per_meter", IsNullable = false)]
        public decimal ResistancePerMeter { get; set; }
        [SugarColumn(ColumnName = "reactance_per_meter", IsNullable = false)]
        public decimal ReactancePerMeter { get; set; }
        [SugarColumn(ColumnName = "ampacity", IsNullable = true)]
        public decimal Ampacity { get; set; }
    }
}
