﻿using SqlSugar;

namespace Siemens.PanelManager.Model.Database.Asset
{
    [SugarTable("asset_relation")]
    public class AssetRelation : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "parent_id", IsNullable = false)]
        public int ParentId { get; set; }
        [SugarColumn(ColumnName = "child_id", IsNullable = false, IsTreeKey = true)]
        public int ChildId { get; set; }
        [SugarColumn(ColumnName = "asset_level", IsNullable = false)]
        public AssetLevel AssetLevel { get; set; }
        #region Tree
        [SugarColumn(IsIgnore = true)]
        public List<AssetRelation>? Children { get; set; }
        #endregion
    }
}
