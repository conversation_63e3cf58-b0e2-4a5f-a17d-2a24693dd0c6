{"info": {"_postman_id": "15adaa62-5bef-4045-8c6a-08e97b7e1232", "name": "10使用管理员账号进入panel manager告警管理中的告警列表菜单，点击导出按钮选择全部导出or页面过滤条件导出", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 6", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "告警列表全部导出", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "let all1 = pm.response.json().data\r", "pm.environment.set('all1',all1)\r", "console.log(all1)\r", "\r", "function sleep(numberMillis){\r", "    var now = new Date();\r", "    var exitTime = now.getTime() + numberMillis;\r", "    while (true){\r", "        now = new Date();\r", "        if (now.getTime() > exitTime)\r", "        return;\r", "    }\r", "};\r", "sleep(10000)"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm/ExportAlarm", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm", "ExportAlarm"]}}, "response": []}, {"name": "告警列表全部导出 Copy", "event": [{"listen": "test", "script": {"exec": ["console.log(pm.response.json())\r", "\r", "pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "let all2 = pm.response.json().data.result\r", "pm.environment.set('all2',all2)\r", "console.log(all2)\r", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm/ExportAlarmResult/{{all1}}", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm", "ExportAlarmResult", "{{all1}}"]}}, "response": []}, {"name": "告警列表全部导出 Copy 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"文件以xlsx格式输出\", function () {\r", "    var test = postman.getResponseHeader(\"Content-Disposition\");\r", "    pm.expect(test).to.include(\"filename=____.xlsx\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{all2}}", "host": ["{{baseUrl}}{{all2}}"]}}, "response": []}, {"name": "告警列表过滤条件导出Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "let all1 = pm.response.json().data\r", "pm.environment.set('all1',all1)\r", "console.log(all1)\r", "\r", "function sleep(numberMillis){\r", "    var now = new Date();\r", "    var exitTime = now.getTime() + numberMillis;\r", "    while (true){\r", "        now = new Date();\r", "        if (now.getTime() > exitTime)\r", "        return;\r", "    }\r", "};\r", "sleep(10000)"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm/ExportAlarm?eventType=0", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm", "ExportAlarm"], "query": [{"key": "eventType", "value": "0"}]}}, "response": []}, {"name": "告警列表过滤条件导出Copy1", "event": [{"listen": "test", "script": {"exec": ["console.log(pm.response.json())\r", "\r", "pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "let all2 = pm.response.json().data.result\r", "pm.environment.set('all2',all2)\r", "console.log(all2)\r", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Alarm/ExportAlarmResult/{{all1}}", "host": ["{{baseUrl}}"], "path": ["api", "v1", "Alarm", "ExportAlarmResult", "{{all1}}"]}}, "response": []}, {"name": "告警列表过滤条件导出Copy2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"文件以xlsx格式输出\", function () {\r", "    var test = postman.getResponseHeader(\"Content-Disposition\");\r", "    pm.expect(test).to.include(\"filename=____.xlsx\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}{{all2}}", "host": ["{{baseUrl}}{{all2}}"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 1000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", ""]}}]}