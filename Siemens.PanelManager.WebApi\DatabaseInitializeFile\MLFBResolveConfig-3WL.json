[{"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 2, "Begin": 6, "KeyRule": "06", "Value": "630"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 2, "Begin": 6, "KeyRule": "08", "Value": "800"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 2, "Begin": 6, "KeyRule": "10", "Value": "1000"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 2, "Begin": 6, "KeyRule": "12", "Value": "1250"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 2, "Begin": 6, "KeyRule": "16", "Value": "1600"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 2, "Begin": 6, "KeyRule": "20", "Value": "2000"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 2, "Begin": 6, "KeyRule": "25", "Value": "2500"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 2, "Begin": 6, "KeyRule": "32", "Value": "3200"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 2, "Begin": 6, "KeyRule": "40", "Value": "4000"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 2, "Begin": 6, "KeyRule": "50", "Value": "5000"}, {"Id": 0, "GroupCode": "RatedCurrent", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 2, "Begin": 6, "KeyRule": "63", "Value": "6300"}, {"Id": 0, "GroupCode": "MechanicalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 4, "Begin": 5, "KeyRule": "1..(2|3)", "Value": "15000"}, {"Id": 0, "GroupCode": "MechanicalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 4, "Begin": 5, "KeyRule": "1..4", "Value": "10000"}, {"Id": 0, "GroupCode": "MechanicalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 4, "Begin": 5, "KeyRule": "2..(2|3|4)", "Value": "10000"}, {"Id": 0, "GroupCode": "MechanicalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 4, "Begin": 5, "KeyRule": "2..5", "Value": "5000"}, {"Id": 0, "GroupCode": "MechanicalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 4, "Begin": 5, "KeyRule": "3..(4|5)", "Value": "5000"}, {"Id": 0, "GroupCode": "ElectricalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 4, "Begin": 5, "KeyRule": "1(10|12|16)(2|3)", "Value": "10000"}, {"Id": 0, "GroupCode": "ElectricalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 4, "Begin": 5, "KeyRule": "120(2|3)", "Value": "7500"}, {"Id": 0, "GroupCode": "ElectricalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 4, "Begin": 5, "KeyRule": "1..4", "Value": "7500"}, {"Id": 0, "GroupCode": "ElectricalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 4, "Begin": 5, "KeyRule": "2(08|10|12|16|20|25)(2|3|4)", "Value": "7500"}, {"Id": 0, "GroupCode": "ElectricalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 4, "Begin": 5, "KeyRule": "232(2|3|4)", "Value": "4000"}, {"Id": 0, "GroupCode": "ElectricalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 4, "Begin": 5, "KeyRule": "240(2|3|4)", "Value": "2000"}, {"Id": 0, "GroupCode": "ElectricalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 4, "Begin": 5, "KeyRule": "2(08|10|12|16|20|25)5", "Value": "5000"}, {"Id": 0, "GroupCode": "ElectricalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 4, "Begin": 5, "KeyRule": "2325", "Value": "4000"}, {"Id": 0, "GroupCode": "ElectricalSwitchSampleCycles", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 4, "Begin": 5, "KeyRule": "3..(4|5)", "Value": "1000"}, {"Id": 0, "GroupCode": "Icw", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 4, "Begin": 5, "KeyRule": "1..2", "Value": "55"}, {"Id": 0, "GroupCode": "Icw", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 4, "Begin": 5, "KeyRule": "1..3", "Value": "66"}, {"Id": 0, "GroupCode": "Icw", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 4, "Begin": 5, "KeyRule": "1..4", "Value": "85"}, {"Id": 0, "GroupCode": "Icw", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 4, "Begin": 5, "KeyRule": "2..2", "Value": "66"}, {"Id": 0, "GroupCode": "Icw", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 4, "Begin": 5, "KeyRule": "2..3", "Value": "85"}, {"Id": 0, "GroupCode": "Icw", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 4, "Begin": 5, "KeyRule": "2..(4|5)", "Value": "100"}, {"Id": 0, "GroupCode": "Icw", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 4, "Begin": 5, "KeyRule": "3..4", "Value": "100"}, {"Id": 0, "GroupCode": "Icw", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 7, "Begin": 5, "KeyRule": "3..5..(3|6)", "Value": "130"}, {"Id": 0, "GroupCode": "Icw", "DeviceType": "ACB", "DeviceModel": "3WL", "Length": 7, "Begin": 5, "KeyRule": "3..5..(4|7)", "Value": "120"}]