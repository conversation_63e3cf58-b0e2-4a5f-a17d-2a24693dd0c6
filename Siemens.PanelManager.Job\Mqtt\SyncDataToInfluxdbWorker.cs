﻿using InfluxDB.Client;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Common.Model;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.Config;
using Siemens.PanelManager.Server.Asset;

namespace Siemens.PanelManager.Job.Mqtt
{
    public class SyncDataToInfluxdbWorker : BackgroundService
    {
        private readonly ILogger<SyncDataToInfluxdbWorker> _logger;
        private readonly IConfiguration _configuration;
        private readonly IServiceProvider _provider;

        private InfluxDBConfig? InfluxdbConfig { get; set; }

        public SyncDataToInfluxdbWorker(ILogger<SyncDataToInfluxdbWorker> logger,
            IConfiguration configuration,
            IServiceProvider provider)
        {
            _logger = logger;
            _configuration = configuration;
            _provider = provider;

            InitInfluxConfig();
        }

        private InfluxDBConfig? InitInfluxConfig()
        {
            if (InfluxdbConfig == null)
            {
                var config = _configuration.GetSection(InfluxDBConfig.Influxdb).Get<InfluxDBConfig>();

                if (config == null)
                {
                    _logger.LogError("SyncDataToInfluxdbWorker init: Not found Influxdb config.");
                }
                else
                {
                    InfluxdbConfig = config;
                }
            }
            return InfluxdbConfig;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            IMessageRegister? messageRegister = null;
            var errorCount = 0;
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    messageRegister = _provider.GetRequiredService<IMessageRegister>();
                    break;
                }
                catch (Exception ex)
                {
                    if (errorCount > 3)
                    {
                        _logger.LogError(ex, "SyncDataToInfluxdbWorker RegisterMessageAction error.");
                        break;
                    }
                    errorCount++;
                    await Task.Delay(1000);
                }
            }

            if (messageRegister == null) return;

            var config = InitInfluxConfig();
            if (config == null) return;

            var helper = _provider.GetRequiredService<AssetInfluxHelper>();

            messageRegister.RegisterMessageAction("SyncToInfluxdb", (inputData) =>
            {
                try
                {
                    // 15分钟数据measurement名称：archivedataquarter
                    if (inputData.AssetId.HasValue)
                    {
                        helper.WriterData(inputData.AssetId.Value,
                            inputData.ObjectId ?? string.Empty,
                            inputData.InputTime,
                            inputData.Datas);

                        //定义一个全局变量
                        if (!string.IsNullOrWhiteSpace(inputData.AssetId.ToString())
                            && !UniversalDeviceInfo.Instance._dicData.ContainsKey(inputData.AssetId.ToString() ?? "0"))
                        {
                            UniversalDeviceInfo.Instance._dicData.Add(inputData.AssetId.ToString() ?? "0", inputData.AssetName ?? "");
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "SyncDataToInfluxdbWorker RegisterMessageAction error.");
                }
            });
        }
    }
}
