﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.WebApi.Models
{
    public class TopologyExtendData
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, PropertyName = "ratedVoltage")]
        public decimal? RatedVoltage { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore, PropertyName = "ratedCurrent")]
        public decimal? RatedCurrent { get; set; }
    }
}
