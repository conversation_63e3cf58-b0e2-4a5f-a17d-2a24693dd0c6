﻿using Siemens.PanelManager.HubModel;
using Siemens.PanelManager.HubModel.Client;
using Siemens.PanelManager.Tools.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Tools.Worker
{
    static class WorkerServer
    {
        public static async Task<UpgradeStatus> GetStepInfo(MontorClient client)
        {
            var steps = await client.GetUpgradeStep();
            return new UpgradeStatus()
            {
                Steps= steps,
            };
        }

        public static void ResolveMontorModel(MonitorModel model, List<ProcessInfo> processInfos, MonitorCurrentStatus currentStatus)
        {
            currentStatus.CPU =$"{model.CPU}%" ;
            currentStatus.Memory = $"{Math.Round(model.MemUsed / model.MemTotal, 2)} {model.MemUnit}";

            var runTime = new StringBuilder();
            var time = model.RunTime;
            if (time > 24 * 60)
            {
                var days = time / (24 * 60);

                runTime.Append($"{days}天 ");
            }

            if (time > 60)
            {
                var hours = time % (24 * 60) / 60;
                runTime.Append($"{hours}小时 ");
            }
            var mins = time % (24 * 60) % 60;
            runTime.Append($"{mins}分钟 ");
            currentStatus.RunTime = runTime.ToString();

            foreach(var processInfo in model.DockerProcessStats ) 
            {
                processInfos.Add(new ProcessInfo()
                {
                    CPU = processInfo.CPUPerc,
                    Memory = processInfo.MemPerc,
                    Name= processInfo.Name,
                    ImageId = processInfo.ID,
                    PId = processInfo.PIDs
                });
            }
        }
    }
}
