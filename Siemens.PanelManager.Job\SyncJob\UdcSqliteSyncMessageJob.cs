﻿using Microsoft.Extensions.Logging;
using Quartz;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.SyncService.Sqlite;

namespace Siemens.PanelManager.Job.SyncJob
{
    [DisallowConcurrentExecution]
    public class UdcSqliteSyncMessageJob : JobBase
    {
        public override string Name => "UdcSqliteSyncMessageJob";

        private ILogger<UdcSqliteSyncMessageJob> _logger;
        private readonly IEnumerable<ISyncMessageHandle> _syncHandles;

        public UdcSqliteSyncMessageJob(ILogger<UdcSqliteSyncMessageJob> logger, IEnumerable<ISyncMessageHandle> syncHandles)
        {
            _logger = logger;
            _syncHandles = syncHandles;
        }

        public override async Task Execute()
        {
            foreach (var syncType in _syncHandles)
            {
                try
                {
                    await syncType.SyncWorkAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError($"UdcSqliteSyncMessageJob SyncWorkAsync error", ex);
                }
            }
        }
    }
}
