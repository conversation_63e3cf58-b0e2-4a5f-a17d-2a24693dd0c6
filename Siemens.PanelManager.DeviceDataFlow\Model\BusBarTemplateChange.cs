﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.DeviceDataFlow.Model
{
    public class BusBarTemperatureChange
    {
        /// <summary>
        /// 水平母排Id 
        /// </summary>
        public int? BusBarId { get; set; }
        /// <summary>
        /// 水平母排名
        /// </summary>
        public string? BusBarName { get; set; }
        /// <summary>
        /// 垂直母排对应的回路
        /// </summary>
        public int? CircuitId { get; set; }
        /// <summary>
        /// 垂直母排对应的柜子
        /// </summary>
        public int? PanelId { get; set; }
        /// <summary>
        /// 垂直母排对应的配电房
        /// </summary>
        public int? SubstationId { get; set; }
        /// <summary>
        /// 相位 A | B | C | N
        /// </summary>
        public string? PhasePosition { get; set; }
        /// <summary>
        /// 当垂直母线时
        /// 1 为进线 2 为出线
        /// </summary>
        public int? Index { get; set; }
    }
}
