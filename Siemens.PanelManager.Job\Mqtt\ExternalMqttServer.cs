﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MQTTnet;
using MQTTnet.Client;
using MQTTnet.Formatter;
using Siemens.PanelManager.Common.Http;
using Siemens.PanelManager.Common.mqtt;
using Siemens.PanelManager.Model.Database.System;
using SqlSugar;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using TouchSocket.Sockets;

namespace Siemens.PanelManager.Job.Mqtt
{
    public class ExternalMqttServer : BackgroundService
    {
        private ILogger _logger;
        private IServiceProvider _provider;
        private ManualResetEvent _resetEvent = new ManualResetEvent(false);
        private MqttFactory? _mqttFactory;
        private bool _firstLoad = true;
        private IMqttClient? _mqttClient;
        private MqttClientOptions? _opt = null;
        private CancellationToken? _token = null;
        private Timer? _reconnectTimer;
        private ExternalMqttAction _action;
        private string _serverCertHash = string.Empty;


        private ConcurrentQueue<(string Topic, string Data)> DataQueue = new ConcurrentQueue<(string, string)>();

        private MqttFactory MqttFactory
        {
            get
            {
                if (_mqttFactory == null)
                {
                    _mqttFactory = new MqttFactory();
                }

                return _mqttFactory;
            }
        }

        private MqttConfig? _mqttConfig = null;

        public ExternalMqttServer(ILogger<ExternalMqttServer> logger, IServiceProvider provider, ExternalMqttAction action)
        {
            _logger = logger;
            _provider = provider;
            action.Init(this);
            _action = action;
        }

        public bool IsRunning()
        {
            return _resetEvent.WaitOne(0);
        }

        /// <summary>
        /// 获取Mqtt服务状态
        /// </summary>
        /// <returns>1: 运行中, 2: 连接中, 0: 未启动</returns>
        public int GetMqttServerStatus()
        {
            if (_resetEvent.WaitOne(0))
            {
                if (_mqttClient == null || !_mqttClient.IsConnected)
                {
                    return 2;
                }
                return 1;
            }
            return 0;
        }

        public override async Task StartAsync(CancellationToken cancellationToken)
        {
            if (_resetEvent.WaitOne(0))
            {
                return;
            }
            using (var client = _provider.GetRequiredService<ISqlSugarClient>())
            {
                var config = new MqttConfig();
                await config.InitBySql(client);

                if (_firstLoad)
                {
                    _firstLoad = false;

                    if (!"1".Equals(config.AutoStart))
                    {
                        return;
                    }
                    await _action.InitMqttConfigs();
                }

                _mqttConfig = config;
                await base.StartAsync(cancellationToken);
            }
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _resetEvent.Set();
            if (_mqttConfig == null)
            {
                using (var client = _provider.GetRequiredService<ISqlSugarClient>())
                {
                    var config = new MqttConfig();
                    await config.InitBySql(client);
                    _mqttConfig = config;
                }
            }

            if (_token == null || _token.Value.IsCancellationRequested)
            {
                _token = _action.GetCancellationToken();
            }

            try
            {
                _opt = GetMqttClientOptions(_mqttConfig);

                var result = await ConnectClient();
                if (result != MqttClientConnectResultCode.Success)
                {
                    _logger.LogInformation($"External Mqtt Client启动失败，结果：{result}");
                    BeginTryReconnet();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "External Mqtt Client启动失败");
                BeginTryReconnet();
            }
        }

        /// <summary>
        /// 断开回调
        /// </summary>
        /// <param name="arg"></param>
        /// <returns></returns>
        private async Task Client_DisconnectedAsync(MqttClientDisconnectedEventArgs arg)
        {
            if (_mqttConfig == null
                || _token == null
                || _token.Value.IsCancellationRequested
                || "1".Equals(_mqttConfig.AutoStart))
            {
                await MqttClientClose();
                return;
            }

            BeginTryReconnet();
            return;
        }

        /// <summary>
        /// 启动重连
        /// </summary>
        private void BeginTryReconnet()
        {
            if (_reconnectTimer == null)
            {
                _reconnectTimer = new Timer(ReconnectClient, null, 10000, 10000);
            }
        }

        /// <summary>
        /// Client 重连方法
        /// </summary>
        /// <param name="state"></param>
        private async void ReconnectClient(object? state)
        {
            try
            {
                if (_mqttClient != null && _token != null)
                {
                    await _mqttClient.ReconnectAsync(_token.Value);
                }
            }
            catch
            {

            }
        }

        private void StopTimer()
        {
            try
            {
                _reconnectTimer?.Change(0, Timeout.Infinite);
                _reconnectTimer?.Dispose();
            }
            catch
            {

            }
            _reconnectTimer = null;
        }

        /// <summary>
        /// Client 连接
        /// </summary>
        /// <returns></returns>
        private async Task<MqttClientConnectResultCode> ConnectClient()
        {
            var client = MqttFactory.CreateMqttClient();

            client.ConnectedAsync += (arg) =>
            {
                _mqttClient = client;
                StopTimer();
                PublishData();
                return Task.CompletedTask;
            };

            client.DisconnectedAsync += Client_DisconnectedAsync;

            using var timeoutToken = new CancellationTokenSource(TimeSpan.FromSeconds(10));
            var result = await client.ConnectAsync(_opt, timeoutToken.Token);

            return result.ResultCode;
        }

        private void PublishData()
        {
            Task.Factory.StartNew(async () =>
            {
                while (true)
                {
                    if (_token == null || _token.Value.IsCancellationRequested)
                    {
                        return;
                    }

                    if (DataQueue.TryDequeue(out var data))
                    {
                        if (_mqttClient != null && _mqttClient.IsConnected)
                        {
                            try
                            {
                                using var timeoutToken = new CancellationTokenSource(TimeSpan.FromSeconds(3));
                                var result = await _mqttClient.PublishStringAsync(data.Topic, data.Data, cancellationToken: timeoutToken.Token);
                                if (!result.IsSuccess)
                                {
                                    _logger.LogError($"Publish Failed\r\nTopic: {data.Topic}, Data: {data.Data}");
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, $"Publish Failed\r\nTopic: {data.Topic}, Data: {data.Data}");
                            }
                        }
                    }
                    else
                    {
                        await Task.Delay(200);
                    }
                }
            });
        }

        /// <summary>
        /// Client 关闭
        /// </summary>
        private async Task MqttClientClose()
        {
            _mqttConfig = null;
            _resetEvent.Reset();
            DataQueue.Clear();
            if (_reconnectTimer != null)
            {
                _reconnectTimer.Change(0, Timeout.Infinite);
                _reconnectTimer.Dispose();
                _reconnectTimer = null;
            }

            if (_mqttClient != null)
            {
                await _mqttClient.DisconnectAsync();
                _mqttClient = null;
            }
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            await MqttClientClose();
        }

        public async Task<ResultDto> CheckMqttClient(MqttConfig mqttConfig)
        {
            ResultDto result;

            var opt = GetMqttClientOptions(mqttConfig);
            if (opt != null)
            {
                try
                {
                    var client = MqttFactory.CreateMqttClient();
                    using var timeoutToken = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                    await client.ConnectAsync(opt, timeoutToken.Token);
                    await client.DisconnectAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Mqtt_Err_Ftosmqttwemsg");
                    result = new ResultDto()
                    {
                        Code = WhetherToManuallyStop.Instance.AbnormalCode = 50000,
                        IsSuccess = false,
                        Msg = $"Mqtt_Err_Ftosmqttwemsg:{ex.Message}"
                    };
                    return result;
                }

                result = new ResultDto() { Code = 20000, IsSuccess = true, Msg = $"Mqtt_Info_Successstartmqtt" };
                return result;
            }

            result = new ResultDto() { Code = 50000, IsSuccess = false };
            return result;
        }

        /// <summary>
        /// 获取MqttClientOptions对象
        /// </summary>
        /// <param name="mqttConfig"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        private MqttClientOptions GetMqttClientOptions(MqttConfig? mqttConfig)
        {
            if (mqttConfig == null || string.IsNullOrEmpty(mqttConfig.Broker) || !int.TryParse(mqttConfig.Port, out var port)) throw new ArgumentNullException($"Mqtt_Err_Tconfcanbempty");

            var optionsBuilder = new MqttClientOptionsBuilder()
                .WithTcpServer(mqttConfig.Broker, port)
                .WithClientId($"{mqttConfig.ClientId ?? "panelmanager"}-{Guid.NewGuid()}")
                .WithKeepAlivePeriod(TimeSpan.FromMinutes(1))
                .WithProtocolVersion(MQTTnet.Formatter.MqttProtocolVersion.V311)
                .WithWillTopic(mqttConfig.Topic ?? string.Empty);


            _logger.LogInformation($"ExtendMqtt useTls:{mqttConfig.UseTLS}");
            //是否开启ssl/tls认证
            switch (mqttConfig.UseTLS)
            {
                case "0":
                    if (!string.IsNullOrWhiteSpace(mqttConfig.UserName))
                    {
                        optionsBuilder = optionsBuilder.WithCredentials(mqttConfig.UserName, mqttConfig.Password);
                    }
                    else
                    {
                        optionsBuilder = optionsBuilder.WithCredentials(string.Empty, string.Empty);
                    }
                    break;

                case "1":
                case "3":
                    {
                        if (!string.IsNullOrWhiteSpace(mqttConfig.UserName))
                        {
                            optionsBuilder = optionsBuilder.WithCredentials(mqttConfig.UserName, mqttConfig.Password);
                        }

                        var tlsOptBuilder = new MqttClientTlsOptionsBuilder()
                            .WithCertificateValidationHandler(arg =>
                            {
                                if (string.IsNullOrEmpty(_serverCertHash))
                                {
                                    return true;
                                }

                                return arg.Certificate != null && _serverCertHash == arg.Certificate.GetCertHashString();
                            })
                            .WithSslProtocols(System.Security.Authentication.SslProtocols.Tls12 | System.Security.Authentication.SslProtocols.Tls13)
                            .WithAllowUntrustedCertificates(true);

                        var fileIds = new List<string>()
                        {
                            mqttConfig.TLSPemFile ?? string.Empty,
                            mqttConfig.ClientPemFile ?? string.Empty,
                            mqttConfig.ClientPrivateFile ?? string.Empty
                        };

                        if (fileIds.Count > 0)
                        {
                            var uploadFileDir = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot");

                            List<FileManager> fileManagers;
                            using (var client = _provider.GetRequiredService<ISqlSugarClient>())
                            {

                                fileManagers = client.Queryable<FileManager>()
                                    .Where(f => fileIds.Contains(f.Code) || fileIds.Contains(f.Url))
                                    .ToList();
                            }
                            if (!string.IsNullOrEmpty(mqttConfig.TLSPemFile))
                            {
                                var file = fileManagers.FirstOrDefault(f => f.Code == mqttConfig.TLSPemFile || f.Url == mqttConfig.TLSPemFile);
                                if (file != null)
                                {
                                    try
                                    {
                                        var filePath = FilePath(uploadFileDir, file.Url);
                                        _logger.LogInformation($"Mqtt ServerCert p1:{filePath}");
                                        var cert = new X509Certificate2(filePath);
                                        _serverCertHash = cert.GetCertHashString();
                                        _logger.LogInformation($"Mqtt ServerCert Hash:{_serverCertHash}");
                                    }
                                    catch (Exception ex)
                                    {
                                        _serverCertHash = Guid.NewGuid().ToString();
                                        _logger.LogError(ex, $"Mqtt_Err_ServerCert:{file.Url}");
                                    }
                                }
                                else
                                {
                                    _serverCertHash = string.Empty;
                                }
                            }

                            if (!string.IsNullOrEmpty(mqttConfig.ClientPemFile) && !string.IsNullOrEmpty(mqttConfig.ClientPrivateFile))
                            {
                                var certFile = fileManagers.FirstOrDefault(f => f.Code == mqttConfig.ClientPemFile || f.Url == mqttConfig.ClientPemFile);
                                var keyFile = fileManagers.FirstOrDefault(f => f.Code == mqttConfig.ClientPrivateFile || f.Url == mqttConfig.ClientPrivateFile);
                                if (certFile != null && keyFile != null)
                                {
                                    try
                                    {
                                        var cert1Path = FilePath(uploadFileDir, certFile.Url);
                                        var cert2Path = FilePath(uploadFileDir, keyFile.Url);
                                        _logger.LogInformation($"Mqtt ClientCert p1:{cert1Path} p2:{cert2Path}");
                                        var cert1 = X509Certificate2.CreateFromPemFile(cert1Path, cert2Path);
                                        var cert2 = new X509Certificate2(cert1.Export(X509ContentType.Pfx),
                                            (string?)null,                            // 无密码
                                            X509KeyStorageFlags.MachineKeySet |      // 存储选项
                                            X509KeyStorageFlags.PersistKeySet |
                                            X509KeyStorageFlags.Exportable);

                                        tlsOptBuilder = tlsOptBuilder.WithClientCertificates(new[] { cert2 });
                                        
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger.LogError(ex, $"Mqtt_Err_ClientCert");
                                    }
                                }
                            }
                        }

                        optionsBuilder = optionsBuilder.WithTlsOptions(tlsOptBuilder.Build());
                    }
                    break;
                case "2":
                default:
                    break;
            }

            var connectOptions = optionsBuilder.Build();

            return connectOptions;
        }

        private string FilePath(string baseUrl, string subUrl)
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                subUrl = subUrl.Replace("/", "\\");
                if (subUrl.StartsWith("\\"))
                {
                    subUrl = subUrl.Substring(1);
                }
            }
            else
            {
                if (subUrl.StartsWith("/"))
                {
                    subUrl = subUrl.Substring(1);
                }
            }

            var filePath = Path.Combine(baseUrl, subUrl);
            return filePath;
        }

        /// <summary>
        /// 发送数据
        /// </summary>
        /// <param name="topic"></param>
        /// <param name="data"></param>
        public void SendData(string topic, string data)
        {
            if (_mqttConfig == null) return;

            if (string.IsNullOrEmpty(topic))
            {
                topic = _mqttConfig.Topic ?? string.Empty;
            }
            else
            {
                topic = $"{_mqttConfig.Topic}/{topic}";
            }

            DataQueue.Enqueue((topic, data));
        }
    }
}
