﻿using Siemens.PanelManager.Model.Database.Asset;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.CacheModel
{
    public class AssetSimpleInfo
    {
        public AssetSimpleInfo(AssetInfo assetInfo)
        {
            AssetId = assetInfo.Id;
            AssetNumber = assetInfo.AssetNumber;
            AssetName = assetInfo.AssetName;
            AssetLevel = assetInfo.AssetLevel;
            ObjectId = assetInfo.ObjectId;
            AssetType = assetInfo.AssetType ?? string.Empty;
            AssetModel = assetInfo.AssetModel ?? string.Empty;
            EnableMqtt = assetInfo.EnableMqtt ?? false;
        }

        public int AssetId { get; set; }
        public string? AssetNumber { get; set; }
        public string AssetName { get; set; } = string.Empty;
        public string? ObjectId { get; set; }
        public string AssetType { get; set; } = string.Empty;
        public string AssetModel { get; set; } = string.Empty;
        public bool EnableMqtt { get; set; } = false;
        public AssetLevel AssetLevel { get; set; }
        public int? SourceAssetId { get; set; }
        public AssetSimpleInfo? AreaSimpleInfo { get; set; }
        public AssetSimpleInfo? PanelSimpleInfo { get; set; }
        public AssetSimpleInfo? SubstationSimpleInfo { get; set; }
        public AssetSimpleInfo? CircuitSimpleInfo { get; set; }

    }
}
