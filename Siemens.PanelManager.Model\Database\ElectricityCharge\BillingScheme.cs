﻿using SqlSugar;


namespace Siemens.PanelManager.Model.Database.ElectricityCharge
{
    [SugarTable("billing_scheme")]
    public class BillingScheme : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsNullable = false, IsIdentity = true)]
        public int Id { get; set; }
        [UniquenessAttribute]
        [SugarColumn(ColumnName = "billingName", IsNullable = false, Length = 256)]
        public string? billingName { get; set; }
        [SugarColumn(ColumnName = "billingCode", IsNullable = true, Length = 256)]
        public string? billingCode { get; set; }

        [SugarColumn(ColumnName = "isEnable", IsNullable = false)]
        public bool isEnable { get; set; }
    }
}
