﻿using System;
using System.Collections.Generic;

namespace Siemens.PanelManager.SyncService.UdcModels
{
    public partial class ArchiveDataFormat7
    {
        public string ObjectId { get; set; } = null!;
        public long Archive { get; set; }
        public long TimestampInS { get; set; }
        public string? EtuEaSumTotal { get; set; }
        public string? EtuEaL1Total { get; set; }
        public string? EtuEaL2Total { get; set; }
        public string? EtuEaL3Total { get; set; }
        public string? EtuEaSumImport { get; set; }
        public string? EtuEaL1Import { get; set; }
        public string? EtuEaL2Import { get; set; }
        public string? EtuEaL3Import { get; set; }
        public string? EtuEaSumExport { get; set; }
        public string? EtuEaL1Export { get; set; }
        public string? EtuEaL2Export { get; set; }
        public string? EtuEaL3Export { get; set; }
        public string? EtuErSumTotal { get; set; }
        public string? EtuErL1Total { get; set; }
        public string? EtuErL2Total { get; set; }
        public string? EtuErL3Total { get; set; }
        public string? EtuErSumImport { get; set; }
        public string? EtuErL1Import { get; set; }
        public string? EtuErL2Import { get; set; }
        public string? EtuErL3Import { get; set; }
        public string? EtuErSumExport { get; set; }
        public string? EtuErL1Export { get; set; }
        public string? EtuErL2Export { get; set; }
        public string? EtuErL3Export { get; set; }
        public string? EtuEapSum { get; set; }
        public string? EtuEapL1 { get; set; }
        public string? EtuEapL2 { get; set; }
        public string? EtuEapL3 { get; set; }
    }
}
