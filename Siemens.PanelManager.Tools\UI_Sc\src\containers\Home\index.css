.home-container{
    height: 100%;
    width: 100%;

    display: grid;
    grid-template-columns: 10px auto 10px;
    grid-template-rows: 10px 100px 10px 200px 10px 200px auto;
    grid-template-areas:
    ". . ."
    ". currentStatus ."
    ". . ."
    ". processes ."
    ". . ."
    ". echart ."
    ". echart .";
}
.home-processes{
    grid-area: processes;
    width: 100%;
    display: flex;
    justify-content: center;
    font-size: calc(10px + 2vmin);
}

.home-echart{
    grid-area: echart;
    width: 100%;
    display: flex;
    justify-content: center;
}
