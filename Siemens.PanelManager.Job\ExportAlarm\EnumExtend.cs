﻿using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Model.Database.Alarm;

namespace Siemens.PanelManager.Job.ExportAlarm
{
    static class EnumExtend
    {
        public static string ToExportString(this AlarmEventType eventType, IMessageContext context)
        {
            switch (eventType)
            {
                case AlarmEventType.Alarm:
                    return context.GetString("Alarm_EventType_Alarm") ?? "Alarm";
                case AlarmEventType.BreakerTrip:
                    return context.GetString("Alarm_EventType_BreakerTrip") ?? "BreakerTrip";
                case AlarmEventType.UdcAlarm:
                    return context.GetString("Alarm_EventType_UdcAlarm") ?? "UdcAlarm";
                case AlarmEventType.DeviceLog:
                    return context.GetString("Alarm_EventType_DeviceLog") ?? "DeviceLog";
                default: break;
            }
            return string.Empty;
        }

        public static string ToExportString(this AlarmRuleOpt opt, IMessageContext context)
        {
            switch (opt)
            {
                case AlarmRuleOpt.Add:
                    return context.GetString("Alarm_RuleOpt_Add") ?? "Add";
                case AlarmRuleOpt.Update:
                    return context.GetString("Alarm_RuleOpt_Update") ?? "Update";
                case AlarmRuleOpt.Disable:
                    return context.GetString("Alarm_RuleOpt_Disable") ?? "Update";
                case AlarmRuleOpt.Enable:
                    return context.GetString("Alarm_RuleOpt_Enable") ?? "Update";
                case AlarmRuleOpt.Delete:
                    return context.GetString("Alarm_RuleOpt_Delete") ?? "Delete";
                case AlarmRuleOpt.Current:
                    return context.GetString("Alarm_RuleOpt_Current") ?? "Current";
                default: break;
            }
            return string.Empty;
        }

        public static string ToExportString(this AlarmSeverity severity, IMessageContext context)
        {
            switch (severity)
            {
                case AlarmSeverity.High:
                    return context.GetString("Alarm_Severity_High") ?? "High";
                case AlarmSeverity.Middle:
                    return context.GetString("Alarm_Severity_Middle") ?? "Middle";
                case AlarmSeverity.Low:
                    return context.GetString("Alarm_Severity_Low") ?? "Low";
                default: break;
            }
            return string.Empty;
        }

        public static string ToExportString(this AlarmLogStatus status, IMessageContext context)
        {
            switch (status)
            {
                case AlarmLogStatus.New:
                    return context.GetString("Alarm_Status_New") ?? "New";
                case AlarmLogStatus.InProcess:
                    return context.GetString("Alarm_Status_InProcess") ?? "InProcess";
                case AlarmLogStatus.Finish:
                    return context.GetString("Alarm_Status_Finish") ?? "Finish";
                case AlarmLogStatus.Error:
                    return context.GetString("Alarm_Status_Error") ?? "Error";
                case AlarmLogStatus.Ignore:
                    return context.GetString("Alarm_Status_Ignore") ?? "Ignore";
                default: break;
            }
            return string.Empty;
        }
    }
}
