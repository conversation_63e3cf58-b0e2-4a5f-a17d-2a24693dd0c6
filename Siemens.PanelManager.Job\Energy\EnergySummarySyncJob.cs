﻿using InfluxDB.Client;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NPOI.SS.Formula.Functions;
using Siemens.PanelManager.Common;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.Model.Config;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.ElectricityCharge;
using Siemens.PanelManager.Model.Database.Energy;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.Server.ElectricityCharge;
using SqlSugar;
using System.Text;
using TouchSocket.Sockets;
using static Akka.Actor.FSMBase;

namespace Siemens.PanelManager.Job.Energy
{
    public class EnergySummarySyncJob : JobBase
    {
        public override string Name { get; } = "EnergySummarySyncJob";

        private readonly ILogger<EnergySummarySyncJob> _logger;
        private readonly IConfiguration _configuration;
        private readonly IServiceProvider _provider;
        private ElectricityChargeServer _electricityCharge => _provider.GetRequiredService<ElectricityChargeServer>();

        private InfluxDBConfig? InfluxdbConfig { get; set; }

        public EnergySummarySyncJob(ILogger<EnergySummarySyncJob> logger,
            IConfiguration configuration,
            IServiceProvider provider)
        {
            _logger = logger;
            _configuration = configuration;
            _provider = provider;

            InfluxdbConfig = _configuration.GetSection(InfluxDBConfig.Influxdb).Get<InfluxDBConfig>();

            if (InfluxdbConfig == null)
            {
                _logger.LogError("EnergySummarySyncJob init: Not found Influxdb config.");
            }
        }

        public override async Task Execute()
        {
            var sqlClient = _provider.GetRequiredService<SqlSugarScope>();
            var assetIds = await sqlClient.Queryable<AssetInfo>().Where(a => a.AssetLevel == AssetLevel.Device&&a.MeterType== MeasurementType.Gateway.ToString()).Select(a => a.Id).ToListAsync();
            var dashboardConfig = await sqlClient.Queryable<AssetDashboardConfig>().Where(d => d.ConfigName == "JOB_ENERGY_SUMMARY").FirstAsync();
            //var dashboardConfig_Energy_Sum = await sqlClient.Queryable<AssetDashboardConfig>().Where(d => d.ConfigName == "JOB_ENERGY_SUM").FirstAsync();
            if (dashboardConfig != null)
            {
                var loaclTimeZone = TimeZoneInfo.Local;

                using var influxClient = new InfluxDBClient(InfluxdbConfig.Url, InfluxdbConfig.UserName, InfluxdbConfig.Password);
                var queryApi = influxClient.GetQueryApi();

                var fluxSql = dashboardConfig.Sql;
                fluxSql = fluxSql
                    .Replace("[[DBName]]", InfluxdbConfig.Bucket)
                    .Replace("[[TableName]]", "archivedatarealtime");
                //var sumSql = dashboardConfig_Energy_Sum.Sql;
                //sumSql = sumSql
                //   .Replace("[[DBName]]", InfluxdbConfig.Bucket)
                //   .Replace("[[TableName]]", "archivedataquarter");
                List<EnergySummaryModel> energyDatas = new List<EnergySummaryModel>();
                List<EnergySummary> energySummaries = new List<EnergySummary>();
                EnergySummaryModel energySummaryModel;
                EnergySummary energySummary;
                decimal yearSumValue=0;
                var electricityList = await sqlClient.Queryable<ElectricityConfig>()
                      .Where(a => a.electrovalenceType == "受电" || a.electrovalenceType == ElectrovalenceType.receivePower.ToString()).ToArrayAsync();
                var history = await sqlClient.Queryable<ElectricityConfigHistory>()
                    .Where(a => a.electrovalenceType == "受电" || a.electrovalenceType == ElectrovalenceType.receivePower.ToString()).ToArrayAsync();
                long currentYearStart =  new DateTime(DateTime.Now.Year, 1, 1).GetTimestampForSec();
                #region 从influxdb获取当前累计用电量，用于电费计算,太慢了，换个方式取
                //var assetIdQueryAll = new StringBuilder();
                //foreach (var id in assetIds)
                //{
                //    if (assetIdQueryAll.Length > 0)
                //    {
                //        assetIdQueryAll.Append(" or ");
                //    }
                //    assetIdQueryAll.Append("r[\"assetid\"] == \"");
                //    assetIdQueryAll.Append(id);
                //    assetIdQueryAll.Append('\"');
                //}


                //var curentYearFluxSql = sumSql
                //      .Replace("[[AssetIdList]]", assetIdQueryAll.ToString())
                //      .Replace("[[StartDate]]", currentYearStart.ToString())
                //      .Replace("[[EndDate]]", DateTime.Now.AddSeconds(1).GetTimestampForSec().ToString());
                //var resultYearSum = await queryApi.QueryAsync(curentYearFluxSql, InfluxdbConfig.OrgName ?? InfluxdbConfig.OrgId);
                //foreach (var table in resultYearSum)
                //{
                //    foreach (var item in table.Records)
                //    {
                //        yearSumValue += Convert.ToDecimal(item.GetValue());
                //    }
                //}
                #endregion
                #region 从pg的表里获取总用电数据
                var yearSummaryList = await sqlClient.Queryable<EnergySummary>()
                .Where(a => SqlFunc.ContainsArray(assetIds, a.AssetId))
                .Where(a => a.ConsumptionDate >= new DateTime(DateTime.Now.Year, 1, 1) && a.ConsumptionDate <= DateTime.Now)
                .ToListAsync();
                var yearSummary = yearSummaryList.Select(a => new
                {
                    Electricity = (a.PeakElectricity + a.FlatElectricity + a.ValleyElectricity),
                    Cost = a.PeakCost + a.FlatCost + a.ValleyCost,
                });

                var coefficient = 1000.0M;

                var systemConfig = await sqlClient.Queryable<SystemConfig>().FirstAsync(c => c.Type == "SpecialConfig" && c.Name == "EnergyCoefficient");
                if (systemConfig != null && decimal.TryParse(systemConfig.Value, out var coefficientDecimal))
                {
                    coefficient = coefficientDecimal;
                }

                yearSumValue = yearSummary.Sum(a => a.Electricity) / coefficient;
                long endDate = DateTime.Now.GetTimestampForSec();
                #endregion
                foreach (var itemId in assetIds)
                {
                    energyDatas.Clear();
                    energySummaries.Clear();

                    var lastAssetSummary = await sqlClient.Queryable<EnergySummary>()
                        .Where(a => a.AssetId == itemId)
                        .OrderByDescending(a => a.ConsumptionDate).FirstAsync();

                    long startDate = lastAssetSummary?.ConsumptionDate.AddMinutes(-15).GetTimestampForSec() ?? currentYearStart;
                    var curentFluxSql = fluxSql
                        .Replace("[[AssetId]]", itemId.ToString())
                        .Replace("[[StartDate]]", startDate.ToString())
                        .Replace("[[EndDate]]", endDate.ToString());

                    var result = await queryApi.QueryAsync(curentFluxSql, InfluxdbConfig.OrgName ?? InfluxdbConfig.OrgId);

                    foreach (var table in result)
                    {
                        foreach (var item in table.Records)
                        {
                            var time = TimeZoneInfo.ConvertTimeFromUtc(item.GetTimeInDateTime().Value, loaclTimeZone);
                            var price = await _electricityCharge.GetElectricityPrice(time, yearSumValue, electricityList, history);
                            
                            var electricity = await _electricityCharge.GetElectricityChargeList(time, electricityList.Where(a=>a.bsid==1).ToArray(), history.Where(a => a.bsid == 1).ToArray());
                            energySummaryModel = new EnergySummaryModel
                            {
                                AssetId = Convert.ToInt32(item.GetValueByKey("assetid")?.ToString()),
                                Value = Convert.ToDecimal(item.GetValue()),
                                Time = time,
                                Price = price,
                                Desc = electricity.rateDesc
                            };

                            energyDatas.Add(energySummaryModel);
                        }
                    }

                    // 计算电表的峰平谷电量以及电费

                    // 苏州工业用电电价及峰平谷时间段：峰：8:00-11:00，17:00-22:00，电价：1.0347
                    // 平：11:00-17:00，22:00-24:00，电价：0.6068
                    // 谷：0:00-8:00，电价：0.2589
                    if (energyDatas.Any())
                    {
                        for (int i = 0; i < energyDatas.Count - 1; i++)
                        {
                            energyDatas[i].Value = energyDatas[i + 1].Value - energyDatas[i].Value;
                            energyDatas[i].Price = energyDatas[i].Value * energyDatas[i].Price;
                        }


                        energyDatas.RemoveAt(energyDatas.Count - 1);

                        var peakEnergyData = energyDatas
                            .Where(a => a.Desc == "峰")
                            .GroupBy(a => a.Time.Date)
                            .Select(a => new { Time = a.Key, Value = a.Sum(it => it.Value), Cost = a.Sum(it => it.Price) / coefficient })
                            .ToList();

                        var flatEnergyData = energyDatas
                            .Where(a => a.Desc == "平")
                            .GroupBy(a => a.Time.Date)
                            .Select(a => new { Time = a.Key, Value = a.Sum(it => it.Value), Cost = a.Sum(it => it.Price) / coefficient })
                            .ToList();

                        var valleyEnergyData = energyDatas
                            .Where(a => a.Desc == "谷")
                            .GroupBy(a => a.Time.Date)
                            .Select(a => new { Time = a.Key, Value = a.Sum(it => it.Value), Cost = a.Sum(it => it.Price) / coefficient })
                            .ToList();

                        var deepValleyData = energyDatas
                            .Where(a => a.Desc == "深谷")
                            .GroupBy(a => a.Time.Date)
                            .Select(a => new { Time = a.Key, Value = a.Sum(it => it.Value), Cost = a.Sum(it => it.Price)  / coefficient })
                            .ToList();

                        var highPeakData = energyDatas
                            .Where(a => a.Desc == "尖峰")
                            .GroupBy(a => a.Time.Date)
                            .Select(a => new { Time = a.Key, Value = a.Sum(it => it.Value), Cost = a.Sum(it => it.Price)  / coefficient })
                            .ToList();

                        var diffDateTimes = energyDatas.GroupBy(a => a.Time.Date).Select(a => a.Key).ToList();
                        var energySummaryDatas = await sqlClient.Queryable<EnergySummary>()
                            .Where(a => SqlFunc.ContainsArray(diffDateTimes, a.ConsumptionDate) && a.AssetId == itemId)
                            .ToListAsync();

                        diffDateTimes?.ForEach(diff =>
                        {
                            var peak = peakEnergyData.FirstOrDefault(a => a.Time == diff);
                            var flat = flatEnergyData.FirstOrDefault(a => a.Time == diff);
                            var valley = valleyEnergyData.FirstOrDefault(a => a.Time == diff);
                            var deepValley = deepValleyData.FirstOrDefault(a => a.Time == diff);
                            var highPeak = highPeakData.FirstOrDefault(a => a.Time == diff);
                            energySummary = new EnergySummary
                            {
                                Id = energySummaryDatas.FirstOrDefault(a => a.ConsumptionDate == diff)?.Id ?? 0,
                                AssetId = itemId,
                                ConsumptionDate = diff,
                                PeakElectricity = (peak?.Value ?? 0.0M) >= 0.0M ? (peak?.Value ?? 0.0M) : 0.0M,
                                PeakCost = (peak?.Cost ?? 0.0M) >= 0.0M ? (peak?.Cost ?? 0.0M) : 0.0M,
                                FlatElectricity = (flat?.Value ?? 0.0M) >= 0.0M ? (flat?.Value ?? 0.0M) : 0.0M,
                                FlatCost = (flat?.Cost ?? 0.0M) >= 0.0M ? (flat?.Cost ?? 0.0M) : 0.0M,
                                ValleyElectricity = (valley?.Value ?? 0.0M) >= 0.0M ? (valley?.Value ?? 0.0M) : 0.0M,
                                ValleyCost = (valley?.Cost ?? 0.0M) >= 0.0M ? (valley?.Cost ?? 0.0M) : 0.0M,
                                HighPeakElectricity = (highPeak?.Value ?? 0.0M) >= 0.0M ? (highPeak?.Value ?? 0.0M) : 0.0M,
                                HighPeakCost = (highPeak?.Cost ?? 0.0M) >= 0.0M ? (highPeak?.Cost ?? 0.0M) : 0.0M,
                                DeepValleyElectricity = (deepValley?.Value ?? 0.0M) >= 0.0M ? (deepValley?.Value ?? 0.0M) : 0.0M,
                                DeepValleyCost = (deepValley?.Cost ?? 0.0M) >= 0.0M ? (deepValley?.Cost ?? 0.0M) : 0.0M,
                                CreatedBy = "EnergySummarySyncJob",
                                CreatedTime = DateTime.Now,
                                UpdatedBy = "EnergySummarySyncJob",
                                UpdatedTime = DateTime.Now,
                            };

                            energySummaries.Add(energySummary);
                        });

                        var x = sqlClient.Storageable(energySummaries).Saveable().ToStorage();
                        x.AsInsertable.ExecuteCommand();
                        x.AsUpdateable.ExecuteCommand();
                    }
                }
            }
        }
    }
}
