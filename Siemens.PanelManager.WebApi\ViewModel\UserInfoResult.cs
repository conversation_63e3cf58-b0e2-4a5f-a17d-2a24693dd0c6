﻿using Microsoft.AspNetCore.Authorization.Infrastructure;
using Siemens.PanelManager.Model.Database.Auth;
using Siemens.PanelManager.WebApi.StaticContent;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class UserInfoResult
    {
        public UserInfoResult(User user, Role[] roles, Page[] pages, MessageContext messageContext)
        {
            UserName = user.LoginName;
            PersonName = user.UserName;
            PrefixTel = user.PrefixMobileNumber ?? string.Empty;
            Tel = user.MobileNumber ?? string.Empty;
            Email = user.EmailAddress ?? string.Empty;
            Language = user.Language ?? string.Empty;
            NeedChangePassword = user.NeedChangePassword;
            ReadLicense = user.ReadLicense;
            LastLoginTime = user.LastLoginTime;
            Roles = roles.Select(r => messageContext.GetRoleName(r.RoleCode, r.RoleName)).Distinct().ToArray();
            Buttons = pages.Where(p => p.PageLevel == PageLevel.ButtonKey && !p.IsSystemConfig).OrderBy(p => p.OrderNo).Select(p => p.PageCode).Distinct().ToArray();
            Routes = pages.Where(p => p.PageLevel != PageLevel.ButtonKey).OrderBy(p => p.OrderNo).Select(p => p.PageCode).Distinct().ToArray();
        }
        public string UserName { get; set; }
        public string PersonName { get; set; }
        public string PrefixTel { get; set; } = string.Empty;
        public string Tel { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string[] Buttons { get; set; }
        public string[] Roles { get; set; }
        public string[] Routes { get; set; }
        public string Language { get; set; }
        public bool NeedChangePassword { get; set; }
        public int ReadLicense { get; set; }
        public DateTime? LastLoginTime { get; set; }
    }
}
