﻿using InfluxDB.Client;
using InfluxDB.Client.Api.Domain;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using Siemens.PanelManager.Common;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.Chart;
using Siemens.PanelManager.Model.Config;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.Model.DataPoint;
using Siemens.PanelManager.Model.UDC;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.Server.Asset;
using SqlSugar;
using System.Collections.Generic;
using System.Text;

namespace Siemens.PanelManager.Server.DataPoint
{
    public class DataPointServer
    {
        private IServiceProvider _provider;
        private ILogger _logger;

        public DataPointServer(IServiceProvider provider, ILogger<DataPointServer> logger)
        {
            _logger = logger;
            _provider = provider;
        }

        public async Task InitDataPoint()
        {
            var client = _provider.GetRequiredService<SqlSugarScope>();
            var cache = _provider.GetRequiredService<SiemensCache>();

            var staticDatas = await client.Queryable<AssetDataPointInfo>()
                    .Where(a => a.AssetLevel == AssetLevel.Device)
                    .ToListAsync();

            #region Device
            var typeModelList = staticDatas
                .Select(a => $"{a.AssetType}|{a.AssetModel}")
                .Distinct()
                .ToList();

            foreach (var typeModel in typeModelList)
            {
                var splitData = typeModel.Split('|');

                var type = splitData[0];
                var model = splitData[1];

                var dataPoints = staticDatas
                    .Where(a => a.AssetType == type && a.AssetModel == model)
                    .ToList();

                cache.Set($"DataPoint_Device_{type.ToUpper()}_{model.ToUpper()}", dataPoints); ;
            }
            #endregion
        }

        public async Task<List<AssetDataPointInfo>> GetDataPointInfos(AssetLevel level, string? type = null, string? model = null, int? assetId = null)
        {
            var client = _provider.GetRequiredService<SqlSugarScope>();
            var cache = _provider.GetRequiredService<SiemensCache>();
            List<AssetDataPointInfo> dataPointInfos = await GetAssetDataPointInfosAsync(level, type, model, assetId, client, cache);

            return dataPointInfos;
        }

        private async Task<List<AssetDataPointInfo>> GetAssetDataPointInfosAsync(AssetLevel level, string? type, string? model, int? assetId, ISqlSugarClient client, SiemensCache cache)
        {
            List<AssetDataPointInfo> dataPointInfos;
            switch (level)
            {
                case AssetLevel.Device:
                    {
                        // AssetSimpleInfo.AssetModel ==  || AssetSimpleInfo.AssetType == "GeneralDevice" || (AssetSimpleInfo.AssetModel == "Modbus" && AssetSimpleInfo.AssetType == "Gateway")
                        if (("Other".Equals(model, StringComparison.OrdinalIgnoreCase)
                                || "GeneralDevice".Equals(type, StringComparison.OrdinalIgnoreCase)
                                || ("Modbus".Equals(model, StringComparison.OrdinalIgnoreCase) && "Gateway".Equals(type, StringComparison.OrdinalIgnoreCase))))
                        {
                            if (!assetId.HasValue)
                            {
                                return new List<AssetDataPointInfo>();
                            }

                            var r = Random.Shared;

                            var assetDataPointsStr = await cache.GetOrCreateAsync<string>($"DataPoint_Device_{assetId}", async () =>
                            {
                                var deviceConfigs = await client.Queryable<UniversalDeviceConfig>()
                                     .Where(d => d.AssetId == assetId).OrderBy(d=>d.Id).ToListAsync();
                                //如果是第三方测温设备，过滤掉未配置的点位
                                if ("TempMeasurement".Equals(type, StringComparison.OrdinalIgnoreCase) && "Other".Equals(model, StringComparison.OrdinalIgnoreCase))
                                {
                                    var tempMonitorInfos = await client.Queryable<TemperatureMonitorInfo>().Where(a => a.AssetId == assetId).ToListAsync();
                                    deviceConfigs = deviceConfigs.Where(a => tempMonitorInfos.Any(b => b.DataPointName == a.PropertyEnName)).ToList();
                                }

                                var bitConfigIds = deviceConfigs.Where(d => d.IsBit == true).Select(d => d.Id).ToArray();

                                var bitConfigs = await client.Queryable<BitConfig>()
                                    .Where(b => bitConfigIds.Contains(b.UniversalDeviceConfigId))
                                    .ToListAsync();

                                var dp = new List<AssetDataPointInfo>();

                                foreach (var config in deviceConfigs)
                                {
                                    if (config.IsBit) continue;
                                    dp.Add(GetDataPointInfoByDeviceConfig(config, model ?? string.Empty, type ?? string.Empty));
                                }

                                foreach (var config in bitConfigs)
                                {
                                    dp.Add(GetDataPointInfoByBitConfig(config, model ?? string.Empty, type ?? string.Empty));
                                }

                                return JsonConvert.SerializeObject(dp);
                            }, TimeSpan.FromMinutes(r.Next(50, 70)));

                            if (!string.IsNullOrEmpty(assetDataPointsStr))
                            {
                                dataPointInfos = JsonConvert.DeserializeObject<List<AssetDataPointInfo>>(assetDataPointsStr) ?? new List<AssetDataPointInfo>();
                            }
                            else
                            {
                                return new List<AssetDataPointInfo>();
                            }

                            break;
                        }

                        type = (type ?? string.Empty).ToUpper();
                        model = (model ?? string.Empty).ToUpper();

                        dataPointInfos = cache.GetOrCreate<List<AssetDataPointInfo>>($"DataPoint_Device_{type}_{model}", () =>
                        {
                            return client.Queryable<AssetDataPointInfo>().Where(a => a.AssetLevel == AssetLevel.Device && SqlFunc.ToUpper(a.AssetType) == type && SqlFunc.ToUpper(a.AssetModel) == model).OrderBy(d => d.Sort).WithCache($"DataPoint_Device_{type}_{model}").ToList();
                        });
                        break;
                    }
                case AssetLevel.Circuit:
                    {
                        dataPointInfos = cache.GetOrCreate<List<AssetDataPointInfo>>($"DataPoint_Circuit", () =>
                        {
                            return client.Queryable<AssetDataPointInfo>().Where(a => a.AssetLevel == AssetLevel.Circuit).OrderBy(d => d.Sort).WithCache($"DataPoint_Circuit").ToList();
                        });
                        break;
                    }
                case AssetLevel.Panel:
                    {
                        dataPointInfos = cache.GetOrCreate<List<AssetDataPointInfo>>($"DataPoint_Panel", () =>
                        {
                            return client.Queryable<AssetDataPointInfo>().Where(a => a.AssetLevel == AssetLevel.Panel).OrderBy(d => d.Sort).WithCache($"DataPoint_Panel").ToList();
                        });
                        break;
                    }
                case AssetLevel.Transformer:
                    {
                        dataPointInfos = cache.GetOrCreate<List<AssetDataPointInfo>>($"DataPoint_Transformer", () =>
                        {
                            return client.Queryable<AssetDataPointInfo>().Where(a => a.AssetLevel == AssetLevel.Transformer).OrderBy(d => d.Sort).WithCache($"DataPoint_Transformer").ToList();
                        });
                        break;
                    }
                case AssetLevel.Substation:
                    {
                        dataPointInfos = cache.GetOrCreate<List<AssetDataPointInfo>>($"DataPoint_Substation", () =>
                        {
                            return client.Queryable<AssetDataPointInfo>().Where(a => a.AssetLevel == AssetLevel.Substation).OrderBy(d => d.Sort).WithCache($"DataPoint_Substation").ToList();
                        });
                        break;
                    }
                default:
                    {
                        dataPointInfos = new List<AssetDataPointInfo>();
                        break;
                    }
            }
            return dataPointInfos;
        }

        public async Task<List<AssetDataPointInfo>> GetOtherDeviceDataPoints(int assetId)
        {
            var sqlClient = _provider.GetRequiredService<SqlSugarScope>();
            var generalDataPoints = await sqlClient.Queryable<AssetGeneralDataPoints>()
                .Where(a => a.AssetId == assetId)
                .ToArrayAsync();

            var result = new List<AssetDataPointInfo>();
            foreach (var general in generalDataPoints)
            {
                result.Add(new AssetDataPointInfo
                {
                    AssetLevel = AssetLevel.Device,
                    GroupName = "Measurement",
                    Code = general.Code,
                    UdcCode = general.UdcCode,
                });
            }

            return result;
        }

        public async Task<List<SystemStaticModel>> GetDataPointToStaticModel(AssetLevel level, string? type = null, string? model = null)
        {
            var dataPoint = await GetDataPointInfos(level, type, model);
            dataPoint = dataPoint.Where(d => d.CanListen).ToList();
            var resultList = new List<SystemStaticModel>();

            var codeType = string.Empty;
            switch (level)
            {
                case AssetLevel.Device:
                    codeType = $"DataPoint_Device_{type ?? string.Empty}_{model ?? string.Empty}";
                    break;
                case AssetLevel.Circuit:
                    codeType = $"DataPoint_Circuit_{type ?? string.Empty}";
                    break;
                case AssetLevel.Panel:
                    codeType = $"DataPoint_Panel_{type ?? string.Empty}";
                    break;
                case AssetLevel.Substation:
                    codeType = $"DataPoint_Substation";
                    break;
                default: return resultList;
            }

            foreach (var d in dataPoint)
            {
                if (!string.IsNullOrEmpty(d.FilterIds) && d.FilterIds.Contains("[S]")) continue;

                resultList.Add(new SystemStaticModel()
                {
                    Type = codeType.ToUpper(),
                    Code = d.Code,
                    Name = d.Name,
                    Sort = d.Id,
                });
            }

            return resultList;
        }

        public string GetDataPointName(string code, IMessageContext messageContext)
        {
            return messageContext.GetString($"DataPoint_{code}") ?? code;
        }

        public async Task<Dictionary<string, string>> GetDataByUDCApi(string objectId, string[] internalNames)
        {
            var refObj = _provider.GetService<IUDCApiRef>();

            var result = new Dictionary<string, string>();
            if (refObj != null)
            {
                var responseObj = await refObj.GetDataPoints(objectId, internalNames);
                if (responseObj != null)
                {
                    foreach (var item in responseObj.Embedded.Items)
                    {
                        result.Add(item.InternalName, item.Value);
                    }
                }
            }
            return result;
        }

        public async Task<DeviceItemInfo[]> GetDeviceItems()
        {
            var refObj = _provider.GetService<IUDCApiRef>();

            var result = new List<DeviceItemInfo>();
            if (refObj != null)
            {
                var responseObj = await refObj.GetItems();
                if (responseObj != null)
                {
                    result = responseObj.Embedded.Items;
                }
            }
            return result.ToArray();
        }

        public async Task SaveThirdDeviceConfigInfo(string code,
            ThreePartModelTemplate template,
            ISqlSugarClient sqlClient,
            string userName)
        {
            var existsConfigs = await sqlClient.Queryable<ThirdModelConfigInfo>()
                .Where(c => c.ThirdPartCode == code)
                .ToListAsync();

            var updateConfigs = new List<ThirdModelConfigInfo>();
            var insertConfigs = new List<ThirdModelConfigInfo>();

            if (template.Treeview != null)
            {
                GetDataProperties(code, template.Treeview, userName, existsConfigs, updateConfigs, insertConfigs);

                #region SaveData
                if (existsConfigs.Count > 0)
                {
                    await sqlClient.Deleteable(existsConfigs).ExecuteCommandAsync();
                }

                if (updateConfigs.Count > 0)
                {
                    await sqlClient.Updateable(updateConfigs).ExecuteCommandAsync();
                }

                if (insertConfigs.Count > 0)
                {
                    await sqlClient.Insertable(insertConfigs).ExecuteCommandAsync();
                }
                #endregion
            }
        }

        public async Task DeleteThirdDeviceConfigInfo(string code, ISqlSugarClient sqlClient)
        {
            var configIds = await sqlClient.Queryable<ThirdModelConfigInfo>().Where(c => c.ThirdPartCode == code).Select(c => c.Id).ToListAsync();
            await sqlClient.Deleteable<ThirdModelBitItem>().Where(b => configIds.Contains(b.ConfigInfoId)).ExecuteCommandAsync();
            await sqlClient.Deleteable<ThirdModelConfigInfo>().Where(c => c.ThirdPartCode == code).ExecuteCommandAsync();
        }

        private static void GetDataProperties(string code, Treeview[] treeviews, string userName, List<ThirdModelConfigInfo> existsConfigs, List<ThirdModelConfigInfo> updateConfigs, List<ThirdModelConfigInfo> insertConfigs)
        {
            foreach (var group in treeviews)
            {
                if (group.Properties != null)
                {
                    var needListen = true;
                    var display = true;
                    if ("Current Harmonics".Equals(group.Name)
                        || "Voltage Harmonics".Equals(group.Name))
                    {
                        needListen = false;
                        display = false;
                    }

                    foreach (var item in group.Properties)
                    {
                        if (string.IsNullOrEmpty(item.PropertyName)) continue;

                        var funcCode = item.SelectedFunctionCode?.Functioncode?.Substring(0, 3) ?? string.Empty;
                        var propertyName = item.PropertyName;
                        var dataType = item.SelectedTransformationType?.TransformationDataType ?? string.Empty;
                        int? factor = null;
                        if (!string.IsNullOrEmpty(item.Factor) && int.TryParse(item.Factor, out var f))
                        {
                            factor = f;
                        }
                        var descInEN = item.DescriptionInEnglish;
                        var descInGE = item.DescriptionInGerman;
                        var unit = item.Unit;

                        uint registerAddress = 0;
                        if (!string.IsNullOrEmpty(item.Register))
                        {
                            uint.TryParse(item.Register, out registerAddress);
                        }

                        var existsItem = existsConfigs.FirstOrDefault(i => i.PropertyCode == item.PropertyName);
                        if (existsItem != null)
                        {
                            existsConfigs.Remove(existsItem);
                            updateConfigs.Add(existsItem);
                            existsItem.PropertyCode = propertyName;
                            existsItem.PropertyName = propertyName;
                            existsItem.DescriptionInGerman = descInGE;
                            existsItem.DescriptionInEnglish = descInEN;
                            existsItem.Unit = unit ?? string.Empty;
                            existsItem.DataType = dataType ?? string.Empty;
                            existsItem.FuncCode = funcCode;
                            existsItem.Factor = factor;
                            existsItem.RegisterAddress = registerAddress;
                            existsItem.GroupName = group.Name ?? string.Empty;
                            existsItem.UpdatedTime = DateTime.Now;
                            existsItem.UpdatedBy = userName;
                        }
                        else
                        {
                            existsItem = new ThirdModelConfigInfo()
                            {
                                ThirdPartCode = code,
                                Factor = factor,
                                DescriptionInGerman = descInGE,
                                DescriptionInEnglish = descInEN,
                                DataType = dataType,
                                DefaultMqtt = false,
                                FuncCode = funcCode,
                                GroupName = group.Name ?? string.Empty,
                                NeedListen = needListen,
                                Display = display,
                                PropertyCode = propertyName,
                                PropertyName = propertyName,
                                RegisterAddress = registerAddress,
                                IsBit = false,
                                Unit = unit ?? string.Empty,
                                CreatedBy = userName,
                                UpdatedBy = userName,
                                CreatedTime = DateTime.Now,
                                UpdatedTime = DateTime.Now,
                            };
                            insertConfigs.Add(existsItem);
                        }
                    }
                }

                if (group.SubGroups != null)
                {
                    GetDataProperties(code, group.SubGroups.ToArray(), userName, existsConfigs, updateConfigs, insertConfigs);
                }
            }
        }

        private AssetDataPointInfo GetDataPointInfoByBitConfig(BitConfig bitConfig, string model, string type)
        {
            return new AssetDataPointInfo()
            {
                AssetLevel = AssetLevel.Device,
                AssetType = type,
                AssetModel = model,
                Code = bitConfig.BitCode ?? bitConfig.BitName ?? string.Empty,
                Name = $"||{bitConfig.BitName ?? string.Empty}",
                GroupName = "Status",
                IsBit = true,
                CanAlarmListen = true,
                CanChart = true,
                CanPrint = true,
                CanReportedData = true,
            };
        }

        private AssetDataPointInfo GetDataPointInfoByDeviceConfig(UniversalDeviceConfig config, string model, string type)
        {
            return new AssetDataPointInfo()
            {
                AssetLevel = AssetLevel.Device,
                AssetType = type,
                AssetModel = model,
                Code = config.PropertyEnName ?? string.Empty,
                Name = $"||{config.PropertyCnName}",
                GroupName = config.GroupName ?? string.Empty,
                Unit = config.Unit ?? string.Empty,
                IsBit = false,
                CanAlarmListen = true,
                CanChart = true,
                CanPrint = true,
                CanReportedData = true,
            };
        }

        /// <summary>
        /// 获取配电房特殊点位
        /// </summary>
        /// <param name="assetId"></param>
        /// <param name="cache"></param>
        /// <returns></returns>
        public async Task<List<SubstationDataPointConfigDetails>> GetSubstationDataPointConfigDetailsNoCache(int assetId, SiemensCache? cache = null)
        {
            if (cache == null)
            {
                cache = _provider.GetRequiredService<SiemensCache>();
            }
            using (var client = _provider.GetRequiredService<ISqlSugarClient>())
            {
                var output = await client.Queryable<CustomerDataPointConfig>()
                    .InnerJoin<CustomDataPoint>((s, c) => s.CustomDataPointId == c.Id)
                    .InnerJoin<AssetInfo>((s, c, a) => c.RealAssetId == a.Id)
                    .Where((s, c, a) => s.AssetId == assetId)
                    .Select((s, c, a) => new SubstationDataPointConfigDetails
                    {
                        BindAssetId = c.RealAssetId,
                        BindAssetName = a.AssetName,
                        BindAssetModel = a.AssetModel,
                        BindAssetType = a.AssetType,
                        BindAssetLevel = a.AssetLevel,
                        BindDataPoint = c.RealDataPointName,
                        DataPointCode = c.TargetDataPointName,
                        Id = s.Id,
                        ShowName = s.LayoutName,
                        ShowType = s.LayoutFormat,
                    }).OrderByDescending(s=>s.Id)
                    .ToListAsync();

                var dataPointsDic = new Dictionary<int, List<AssetDataPointInfo>>();
                foreach (var item in output)
                {
                    if (!dataPointsDic.TryGetValue(item.BindAssetId, out var points))
                    {
                        var dataPoints = await GetAssetDataPointInfosAsync(item.BindAssetLevel, item.BindAssetType, item.BindAssetModel, item.BindAssetId, client, cache);
                        dataPointsDic.TryAdd(item.BindAssetId, dataPoints);
                        points = dataPoints;
                    }

                    var pointInfo = points.FirstOrDefault(p => p.Code == item.BindDataPoint);
                    if (pointInfo != null)
                    {
                        item.BindDataPointName = pointInfo.Name;
                        item.BindDataPointUnit = pointInfo.Unit;
                    }
                }

                return output;
            }
        }

        /// <summary>
        /// 获取配电房特殊点位 通过缓存
        /// </summary>
        /// <param name="assetId"></param>
        /// <returns></returns>
        public async Task<List<SubstationDataPointConfigDetails>> GetSubstationDataPointConfigDetailsByCache(int assetId)
        {
            var cache = _provider.GetRequiredService<SiemensCache>();

            var result = await cache.GetOrCreateAsync($"SubstationDataPointConfig-{assetId}", () => GetSubstationDataPointConfigDetailsNoCache(assetId, cache));
            return result;
        }

        /// <summary>
        /// 保存配电房特殊点位信息
        /// </summary>
        /// <param name="substationId"></param>
        /// <param name="details"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public async Task<int> SaveSubstationDataPointConfig(int substationId, SubstationDataPointConfigDetails details, string user)
        {
            var cache = _provider.GetRequiredService<SiemensCache>();
            var alarmExtendServer = _provider.GetRequiredService<AlarmExtendServer>();

            if (details == null) return 0;
            if (details.Id <= 0)
            {
                #region Add
                using (var client = _provider.GetRequiredService<ISqlSugarClient>())
                {
                    var assetInfo = await client.Queryable<AssetInfo>()
                        .Where(a => a.Id == substationId)
                        .FirstAsync();

                    var substationName = assetInfo?.AssetName;

                    if (assetInfo == null || string.IsNullOrEmpty(substationName))
                    {
                        return 0;
                    }
                    if (string.IsNullOrEmpty(details.DataPointCode))
                    {
                        details.DataPointCode = $"DP_{Guid.NewGuid().ToString()}";
                    }
                    #region Tran
                    var customDataPoint = new CustomDataPoint()
                    {
                        TargetAssetId = substationId,
                        TargetDataPointName = details.DataPointCode,
                        RealAssetId = details.BindAssetId,
                        RealDataPointName = details.BindDataPoint,
                        CreatedBy = user,
                        CreatedTime = DateTime.Now,
                        UpdatedBy = user,
                        UpdatedTime = DateTime.Now,
                    };

                    details.DataPointCode = customDataPoint.TargetDataPointName;

                    var substationConfig = new CustomerDataPointConfig()
                    {
                        AssetId = substationId,
                        CreatedBy = user,
                        CreatedTime = DateTime.Now,
                        UpdatedBy = user,
                        UpdatedTime = DateTime.Now,
                        LayoutFormat = details.ShowType,
                        LayoutName = details.ShowName,
                    };

                    try
                    {
                        await client.Ado.BeginTranAsync();
                        var customDataPointId = await client.Insertable(customDataPoint).ExecuteReturnIdentityAsync();
                        substationConfig.CustomDataPointId = customDataPointId;
                        var newId = await client.Insertable(substationConfig).ExecuteReturnIdentityAsync();
                        cache.Clear($"CustomDataPoint-{details.BindAssetId}");
                        cache.Clear($"SubstationDataPointConfig-{substationId}");
                        await client.Ado.CommitTranAsync();

                        UpdateCurrentlyData(substationId, details, cache, assetInfo);
                        await alarmExtendServer.InsertOperationLog(user, "AddSubstationDataPointConfig", Model.Database.Alarm.AlarmSeverity.Middle, client, substationName);
                        return newId;
                    }
                    catch (Exception ex)
                    {
                        await client.Ado.RollbackTranAsync();
                        _logger.LogError(ex, "插入失败");
                    }
                    #endregion
                }
                #endregion
            }
            else
            {
                #region Update
                using (var client = _provider.GetRequiredService<ISqlSugarClient>())
                {
                    var assetInfo = await client.Queryable<AssetInfo>()
                        .Where(a => a.Id == substationId)
                        .FirstAsync();

                    var substationName = assetInfo?.AssetName;

                    if (assetInfo == null || string.IsNullOrEmpty(substationName))
                    {
                        return 0;
                    }

                    var substationConfig = await client.Queryable<CustomerDataPointConfig>()
                        .Where(s => s.AssetId == substationId && s.Id == details.Id)
                        .FirstAsync();

                    if (substationConfig == null)
                    {
                        return 0;
                    }

                    #region Tran
                    substationConfig.LayoutFormat = details.ShowType;
                    substationConfig.LayoutName = details.ShowName;
                    
                    var createCustomDataPoint = false;

                    var oldDataPointCode = string.Empty;
                    var oldBindAssetId = 0;
                    var customDataPoint = await client.Queryable<CustomDataPoint>().FirstAsync(c => c.Id == substationConfig.CustomDataPointId);
                    if (customDataPoint == null)
                    {
                        if (string.IsNullOrEmpty(details.DataPointCode))
                        {
                            details.DataPointCode = $"DP_{Guid.NewGuid().ToString()}";
                        }
                        createCustomDataPoint = true;
                        customDataPoint = new CustomDataPoint()
                        {
                            TargetAssetId = substationId,
                            TargetDataPointName = $"DP_{Guid.NewGuid().ToString()}",
                            RealAssetId = details.BindAssetId,
                            RealDataPointName = details.BindDataPoint,
                            CreatedBy = user,
                            CreatedTime = DateTime.Now,
                            UpdatedBy = user,
                            UpdatedTime = DateTime.Now,
                        };
                    }
                    else
                    {
                        oldDataPointCode = customDataPoint.TargetDataPointName;
                        oldBindAssetId = customDataPoint.RealAssetId;

                        if (string.IsNullOrEmpty(details.DataPointCode))
                        {
                            customDataPoint.TargetDataPointName = $"DP_{Guid.NewGuid().ToString()}";
                        }
                        if (!string.IsNullOrEmpty(details.DataPointCode) && customDataPoint.TargetDataPointName != details.DataPointCode)
                        {
                            customDataPoint.TargetDataPointName = details.DataPointCode;
                        }
                        
                        customDataPoint.RealAssetId = details.BindAssetId;
                        customDataPoint.RealDataPointName = details.BindDataPoint;
                    }

                    details.DataPointCode = customDataPoint.TargetDataPointName;

                    try
                    {
                        await client.Ado.BeginTranAsync();
                        bool needSendEmpty = false;
                        if (createCustomDataPoint)
                        {
                            var customDataPointId = await client.Insertable(customDataPoint).ExecuteReturnIdentityAsync();
                            substationConfig.CustomDataPointId = customDataPointId;
                            await client.Updateable(substationConfig).ExecuteCommandAsync();
                        }
                        else
                        {
                            await client.Updateable(customDataPoint).ExecuteCommandAsync();
                            await client.Updateable(substationConfig).ExecuteCommandAsync();

                            if (oldBindAssetId != customDataPoint.RealAssetId)
                            {
                                cache.Clear($"CustomDataPoint-{oldBindAssetId}");
                            }

                            if (!oldDataPointCode.Equals(customDataPoint.TargetDataPointName))
                            {
                                needSendEmpty = true;
                                cache.RemoveHashData($"AssetStatus:Currently-{substationId}", new string[] { oldDataPointCode });
                            }
                        }

                        cache.Clear($"CustomDataPoint-{details.BindAssetId}");
                        cache.Clear($"SubstationDataPointConfig-{substationId}");
                        await client.Ado.CommitTranAsync();

                        UpdateCurrentlyData(substationId, details, cache, assetInfo);

                        if (needSendEmpty)
                        {
                            var actorRef = _provider.GetRequiredService<IAssetDataProxyRef>();
                            var datas = new Dictionary<string, string>();
                            datas.Add(oldDataPointCode, string.Empty);
                            actorRef.DataChanged(new Model.DataFlow.AssetChangeData
                            {
                                AssetId = substationId,
                                AssetLevel = assetInfo.AssetLevel,
                                AssetModel = assetInfo.AssetModel ?? string.Empty,
                                AssetType = assetInfo.AssetType ?? string.Empty,
                                AssetName = substationName,
                                ChangeDatas = datas,
                                ChangeTime = DateTime.Now,
                            });
                        }

                        await alarmExtendServer.InsertOperationLog(user, "UpdateSubstationDataPointConfig", Model.Database.Alarm.AlarmSeverity.Middle, client, substationName);
                        return substationConfig.Id;
                    }
                    catch (Exception ex)
                    {
                        await client.Ado.RollbackTranAsync();
                        _logger.LogError(ex, "修改失败");
                    }
                    #endregion
                }
                #endregion
            }

            return 0;
        }

        public void UpdateCurrentlyData(int substationId, SubstationDataPointConfigDetails details, SiemensCache cache, AssetInfo assetInfo)
        {
            var valueDic = cache.GetHashData($"AssetStatus:Currently-{details.BindAssetId}", new string[] { details.BindDataPoint });

            if (valueDic != null && valueDic.TryGetValue(details.BindDataPoint, out var value))
            {
                var actorRef = _provider.GetRequiredService<IAssetDataProxyRef>();
                var datas = new Dictionary<string, string>();
                datas.Add(details.DataPointCode, value);
                actorRef.DataChanged(new Model.DataFlow.AssetChangeData
                {
                    AssetId = substationId,
                    AssetLevel = assetInfo.AssetLevel,
                    AssetModel = assetInfo.AssetModel ?? string.Empty,
                    AssetType = assetInfo.AssetType ?? string.Empty,
                    AssetName = assetInfo.AssetName ?? string.Empty,
                    ChangeDatas = datas,
                    ChangeTime = DateTime.Now,
                });
            }
        }

        /// <summary>
        /// 删除配电房特殊点位信息
        /// </summary>
        /// <param name="substationId"></param>
        /// <param name="configIds"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public async Task<bool> DeleteSubstationDataPointConfig(int substationId, int[] configIds, string user)
        {
            var cache = _provider.GetRequiredService<SiemensCache>();
            var alarmExtendServer = _provider.GetRequiredService<AlarmExtendServer>();

            using (var client = _provider.GetRequiredService<ISqlSugarClient>())
            {
                var assetInfo = await client.Queryable<AssetInfo>()
                    .Where(a => a.Id == substationId)
                    .FirstAsync();

                var substationName = assetInfo?.AssetName;

                if (assetInfo == null || string.IsNullOrEmpty(substationName))
                {
                    return false;
                }

                var configs = await client.Queryable<CustomerDataPointConfig>()
                    .Where(c => c.AssetId == substationId && configIds.Contains(c.Id))
                    .ToListAsync();

                configIds = configs
                    .Select(c => c.Id)
                    .ToArray();

                var customDpIds = configs
                    .Select(c => c.CustomDataPointId)
                    .ToList();

                var customAssets = await client.Queryable<CustomDataPoint>()
                    .Where(c => customDpIds.Contains(c.Id))
                    .ToListAsync();

                var configDps = customAssets.Select(c => c.TargetDataPointName).ToArray();

                var customAssetIds = customAssets.Select(c => c.RealAssetId).Distinct().ToList();

                #region Tran
                try
                {
                    await client.Ado.BeginTranAsync();
                    await client.Deleteable<CustomDataPoint>().Where(c => customDpIds.Contains(c.Id)).ExecuteCommandAsync();
                    await client.Deleteable<CustomerDataPointConfig>().Where(s => configIds.Contains(s.Id)).ExecuteCommandAsync();


                    var actorRef = _provider.GetRequiredService<IAssetDataProxyRef>();
                    var datas = new Dictionary<string, string>();
                    foreach (var item in configDps)
                    {
                        datas.Add(item, string.Empty);
                    }
                    actorRef.DataChanged(new Model.DataFlow.AssetChangeData
                    {
                        AssetId = substationId,
                        AssetLevel = assetInfo.AssetLevel,
                        AssetModel = assetInfo.AssetModel ?? string.Empty,
                        AssetType = assetInfo.AssetType ?? string.Empty,
                        AssetName = substationName,
                        ChangeDatas = datas,
                        ChangeTime = DateTime.Now,
                    });

                    cache.RemoveHashData($"AssetStatus:Currently-{substationId}", configDps);
                    cache.Clear($"SubstationDataPointConfig-{substationId}");
                    await client.Ado.CommitTranAsync();

                    foreach (var cAssetId in customAssetIds)
                    {
                        cache.Clear($"CustomDataPoint-{cAssetId}");
                    }

                    await alarmExtendServer.InsertOperationLog(user, "DeleteSubstationDataPointConfig", Model.Database.Alarm.AlarmSeverity.Middle, client, substationName);
                    return true;
                }
                catch (Exception ex)
                {
                    await client.Ado.RollbackTranAsync();
                    _logger.LogError(ex, "删除失败");
                }
                #endregion
            }
            return false;
        }

        /// <summary>
        /// 配电房环境数据图表
        /// </summary>
        /// <param name="assetId"></param>
        /// <param name="detailsList"></param>
        /// <param name="start"></param>
        /// <param name="end"></param>
        /// <returns></returns>
        public async Task<Dictionary<SubstationDataPointConfigDetails, LineChartModel>> GetAssetChart(int assetId, List<SubstationDataPointConfigDetails> detailsList, long start, long end)
        {
            var result = new Dictionary<SubstationDataPointConfigDetails, LineChartModel>();

            var dashboardServer = _provider.GetRequiredService<AssetDashboardServer>();
            var queryStr = "from(bucket: \"panel\")\r\n" +
                "  |> range(start: {0}, stop: {1})\r\n" +
                "  |> filter(fn: (r) => r[\"_measurement\"] == \"archivedatarealtime\")\r\n" +
                "  |> filter(fn: (r) => {3})\r\n" +
                "  |> filter(fn: (r) => r[\"assetid\"] == \"{2}\")\r\n" +
                "  |> drop(columns: [\"objectid\"])\r\n" +
                "  |> aggregateWindow(every: 10s, fn: mean, createEmpty: false)\r\n" +
                "  |> yield(name: \"mean\")";

            var fieldQuerySb = new StringBuilder();
            foreach (var item in detailsList)
            {
                if (fieldQuerySb.Length > 0)
                {
                    fieldQuerySb.Append(" or ");
                }
                fieldQuerySb.Append($"r[\"_field\"] == \"{item.BindDataPoint.ToLower()}\"");
            }

            if (fieldQuerySb.Length <= 0)
            {
                return result;
            }

            var configuration = _provider.GetRequiredService<IConfiguration>();
            var influxDbConfig = configuration.GetSection(InfluxDBConfig.Influxdb).Get<InfluxDBConfig>();

            if (influxDbConfig == null)
            {
                return result;
            }

            var startTime = start;
            var endTime = end;

            var query = string.Format(queryStr, startTime, endTime, assetId, fieldQuerySb.ToString());

            using (var influxDbClient = new InfluxDBClient(new InfluxDBClientOptions(influxDbConfig.Url)
            {
                Username = influxDbConfig.UserName,
                Password = influxDbConfig.Password,
                Bucket = influxDbConfig.Bucket,
                Org = influxDbConfig.OrgName,
            }))
            {
                var queryApi = influxDbClient.GetQueryApi();
                var fluxDatas = await queryApi.QueryAsync(query);
                var loaclTimeZone = TimeZoneInfo.Local;

                foreach (var table in fluxDatas)
                {
                    var dataPointName = string.Empty;
                    var lineMode = new LineChartModel();
                    List<string> x = new List<string>();
                    List<decimal> y = new List<decimal>();

                    foreach (var r in table.Records)
                    {
                        if (string.IsNullOrEmpty(dataPointName))
                        {
                            dataPointName = r.GetField();
                        }

                        var time = r.GetTimeInDateTime();
                        if (time.HasValue)
                        {
                            x.Add(TimeZoneInfo.ConvertTimeFromUtc(time.Value, loaclTimeZone).ToString("HH:mm:ss"));
                            var val = r.GetValueByKey("_value");
                            try
                            {
                                y.Add(Convert.ToDecimal(val));
                            }
                            catch
                            {
                                y.Add(0m);
                            }
                        }
                    }

                    var details = detailsList.Where(d => d.BindDataPoint.Equals(dataPointName, StringComparison.OrdinalIgnoreCase)).ToList();

                    if (details != null && details.Count > 0)
                    {
                        foreach (var d in details)
                        {
                            var lineModel = new LineChartModel
                            {
                                X = x.ToArray(),
                                Y1 = y.ToArray(),
                            };
                            if (!result.ContainsKey(d))
                            {
                                result.Add(d, lineModel);
                            }
                            else
                            {
                                result[d]= lineModel;
                            }
                           
                        }
                    }
                }
            }

            return result;
        }
    }
}
