﻿using SqlSugar;

namespace Siemens.PanelManager.Model.Database.Alarm
{
    [SugarTable("alarm_rule_change_history")]
    public class AlarmRuleChangeHistory: IPanelDataTable
    {
        [SugarColumn(ColumnName = "id", IsNullable = false,IsPrimaryKey = true, IsIdentity = true)]
        public long Id { get; set; }
        [SugarColumn(ColumnName = "timestamp",IsNullable = false)]
        public long Timestamp { get; set; }

        [SugarColumn(ColumnName = "rule_id", IsNullable = false)]
        public int RuleId { get; set; }
        [SugarColumn(ColumnName = "name", IsNullable = false, Length = 512)]
        public string RuleName { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "rule_info", IsNullable = true, Length = 512)]
        public string RuleInfo { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "severity", IsNullable = false)]
        public AlarmSeverity Severity { get; set; }

        [SugarColumn(ColumnName = "operation", IsNullable = false)]
        public AlarmRuleOpt Operation { get; set; }

        [SugarColumn(ColumnName = "updated_by", IsNullable = false)]
        public string UpdatedBy { get; set; } = string.Empty;
    }

    public enum AlarmRuleOpt : int
    {
        Add = 0,
        Update = 10,
        Enable = 11,
        Disable = 12,
        Delete = 20,
        Current = -1
    }
}
