﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.Excel;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Job;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class PanelHealthController : SiemensApiControllerBase
    {
        private SiemensExcelHelper _excelHelper => _provider.GetRequiredService<SiemensExcelHelper>();
        private ILogger<PanelHealthController> _log;
        private ISqlSugarClient _client;
        private IServiceProvider _provider;
        private SiemensCache _cache;
        private AlarmExtendServer _alarmExtendServer => _provider.GetRequiredService<AlarmExtendServer>();

        public PanelHealthController(SqlSugarScope client,
           SiemensCache cache,
           ILogger<PanelHealthController> log,
           IServiceProvider provider)
           : base(provider, cache)
        {
            _client = client;
            _log = log;
            _provider = provider;
            _cache = cache;
        }

        [HttpGet("current/{assetId}")]
        [SwaggerOperation(Summary = "Swagger_PanelHealth_GetPanelHealth", Description = "Swagger_PanelHealth_GetPanelHealth_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<CurrentPannelHealthResult>> GetPanelHealth(int assetId)
        {
            if (assetId <= 0)
            {
                return new ResponseBase<CurrentPannelHealthResult>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            if (await _client.Queryable<AssetInfo>().AnyAsync(a => a.Id == assetId && a.AssetLevel == AssetLevel.Panel))
            {
                var panelHealths = await _client.Queryable<PanelHealth>().Where(p=>p.AssetId == assetId).OrderBy(p=>p.Id).ToArrayAsync();
                var panelHealthModels = new List<PanelHealthModel>();
                if (panelHealths != null)
                {
                    foreach (var panelHealth in panelHealths)
                    {
                        if (panelHealth == null) continue;
                        int? limitOpt = null;
                        if (panelHealth.Limit != null)
                        {
                            switch (panelHealth.Limit.Replace(" ", ""))
                            {
                                case "[1,10]":
                                    limitOpt = 0;
                                    break;
                                case "[1,100]":
                                    limitOpt = 1;
                                    break;
                                default: break;
                            }
                        }
                        var name = MessageContext.GetOverviewValue(panelHealth.Code ?? string.Empty);
                        if (string.IsNullOrEmpty(name))
                        {
                            name = panelHealth.Name;
                        }
                        var model = new PanelHealthModel()
                        {
                            Id = panelHealth.Id,
                            IsSystem = panelHealth.IsSystem,
                            Code = panelHealth.Code ?? string.Empty,
                            LimitOpt = limitOpt,
                            Message = string.Empty,
                            Name = name,
                            Score = string.Empty,
                            Value = panelHealth.Value,
                            Weight = panelHealth.Weight,
                        };
                        switch (panelHealth.Code)
                        {
                            case "MaxElectricity":
                                model.Name = "本柜最高电流测点(A)";
                                break;
                            case "RemainingLife":
                                model.Name = "本柜断路器剩余寿命(%)";
                                break;
                            case "MaxTemperature":
                                model.Name = "本柜最高温度测点(℃)";
                                break;
                        }
                        if (panelHealth.IsSystem) 
                        {
                            model.Value = null;
                            if (!string.IsNullOrEmpty(panelHealth.ParentCode))
                            {
                                var panertPanelHealth = panelHealths.FirstOrDefault(p => p.Code == panelHealth.ParentCode);
                                if (panertPanelHealth != null)
                                {
                                    var panelHealthModel = panelHealthModels.FirstOrDefault(pm => pm.Id == panertPanelHealth.Id);
                                    if (panelHealthModel != null)
                                    {
                                        if (panelHealthModel.Children == null)
                                        {
                                            panelHealthModel.Children = new List<PanelHealthModel>();
                                        }

                                        panelHealthModel.Children.Add(model);
                                        continue;
                                    }
                                }
                            }
                            else
                            {
                                var children = panelHealths.Where(p => p.ParentCode == panelHealth.Code).ToArray();
                                if (children != null && children.Length > 0)
                                {
                                    model.Children = new List<PanelHealthModel>();
                                    var models = panelHealthModels.Where(pm => children.Any(c => c.Id == pm.Id)).ToArray();
                                    if (models != null && models.Length > 0)
                                    {
                                        foreach (var m in models)
                                        {
                                            if (m == null) continue;
                                            model.Children.Add(m);
                                        }
                                    }
                                }
                            }
                        }
                        var alarmList = new List<string>();
                        alarmList.Add("LowLevelAlarmCount");
                        alarmList.Add("MiddleLevelAlarmCount");
                        alarmList.Add("HighLevelAlarmCount");
                        alarmList.Add("AlarmCount");
                        if (!alarmList.Contains(model.Code))
                        {
                            panelHealthModels.Add(model);
                        }
                    }
                }
                return new ResponseBase<CurrentPannelHealthResult>()
                {
                    Code= 20000,
                    Data = new CurrentPannelHealthResult()
                    {
                        Score = 90,
                        Grade =MessageContext.GetOverviewValue("Excellent"),
                        Count = 1,
                        AbnormalIndicators = new AbnormalIndicatorResult[0],
                        IntercatorData = panelHealthModels.ToArray()
                    }
                };
            }

            return new ResponseBase<CurrentPannelHealthResult>()
            {
                Code = 40400,
                Message = MessageContext.AssetNotExists
            };
        }

        [HttpGet("recheckresult/{assetId}/{jobId}")]
        [SwaggerOperation(Summary = "Swagger_PanelHealth_RecheckResult", Description = "Swagger_PanelHealth_RecheckResult_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<JobResult>> RecheckResult(int assetId,string jobId)
        {
            if (assetId <= 0 || string.IsNullOrEmpty(jobId))
            {
                return new ResponseBase<JobResult>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            if (await _client.Queryable<AssetInfo>().AnyAsync(a => a.Id == assetId && a.AssetLevel == AssetLevel.Panel))
            {
                var jobBase = _cache.Get<JobInfo>($"PanelHealthRecheck:{assetId.ToString()}");
                int status = 0;
                if (jobBase != null)
                {
                    if (jobId == jobBase.JobId)
                    {
                        status = jobBase.JobStatus;
                    }
                    else
                    {
                        status = 99;
                    }
                }
                else
                {
                    status = 99;
                }
                return new ResponseBase<JobResult>()
                {
                    Code = 20000,
                    Data = new JobResult()
                    {
                        Status = status,
                        Result = jobBase?.Result,
                        ResultMessage = string.Empty
                    }
                };
            }

            return new ResponseBase<JobResult>()
            {
                Code = 40400,
                Message = MessageContext.AssetNotExists
            };
        }

        [HttpGet("recheck/{assetId}")]
        [SwaggerOperation(Summary = "Swagger_PanelHealth_Recheck", Description = "Swagger_PanelHealth_Recheck_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> Recheck(int assetId)
        {
            if (assetId <= 0)
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }
            var panel = await _client.Queryable<AssetInfo>().FirstAsync(a => a.Id == assetId && a.AssetLevel == AssetLevel.Panel);

            if (panel != null)
            {
                var jobInfo = _cache.GetOrCreate($"PanelHealthRecheck:{assetId}", () => new JobInfo(), TimeSpan.FromSeconds(20));
                await _alarmExtendServer.InsertOperationLog(UserName, "RecheckPanelHealth", Model.Database.Alarm.AlarmSeverity.Middle, _client, panel.AssetName);
                return new ResponseBase<string>()
                {
                    Code = 20000,
                    Data = jobInfo.JobId
                };
            }

            return new ResponseBase<string>()
            {
                Code = 40400,
                Message = MessageContext.AssetNotExists
            };
        }


        [HttpGet("list/{assetId}")]
        [SwaggerOperation(Summary = "Swagger_PanelHealth_GetPanelHealthList", Description = "Swagger_PanelHealth_GetPanelHealthList_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<PanelHealthModel[]>> GetPanelHealthList(int assetId)
        {
            if (assetId <= 0)
            {
                return new ResponseBase<PanelHealthModel[]>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            if (await _client.Queryable<AssetInfo>().AnyAsync(a => a.Id == assetId && (a.AssetLevel == AssetLevel.Panel|| a.AssetLevel == AssetLevel.Substation)))
            {
                var pannelHealthList = new List<PanelHealthModel>();

                var dbList = await _client.Queryable<PanelHealth>()
                    .Where(p => p.AssetId == assetId)
                    .OrderBy(p => p.Id)
                    .ToArrayAsync();

                foreach (var db in dbList)
                {
                    var model = new PanelHealthModel();
                    model.Id = db.Id;
                    model.Code = db.Code ?? string.Empty;
                    model.Name = db.Name;
                    model.Value = null;
                    model.IsSystem=db.IsSystem;
                    if (!db.IsSystem)
                    {
                        model.Value = db.Value;
                    }
                    switch (model.Code)
                    {
                        case "MaxElectricity":
                            model.Name = "本柜最高电流测点(A)";
                            break;
                        case "RemainingLife":
                            model.Name = "本柜断路器剩余寿命(%)";
                            break;
                        case "MaxTemperature":
                            model.Name = "本柜最高温度测点(℃)";
                            break;
                    }

                    if (db.Weight >= 0)
                    {
                        model.Weight = db.Weight;
                    }
                    if (!string.IsNullOrEmpty(db.Limit))
                    {
                        var limitList = JsonConvert.DeserializeObject<int[]>(db.Limit);
                        if (limitList != null && limitList.Length == 2)
                        {
                            if (limitList[0] == 0 && limitList[1] == 10) 
                            {
                                model.LimitOpt = 0;
                            }
                            else 
                            {
                                model.LimitOpt = 1;
                            }
                            model.Limit = limitList;
                        }
                    }
                    var alarmList = new List<string>();
                    alarmList.Add("LowLevelAlarmCount");
                    alarmList.Add("MiddleLevelAlarmCount");
                    alarmList.Add("HighLevelAlarmCount");
                    alarmList.Add("AlarmCount");
                    if (!alarmList.Contains(model.Code))
                    {
                        pannelHealthList.Add(model);
                    }
                }

                return new ResponseBase<PanelHealthModel[]>()
                {
                    Code = 20000,
                    Data = pannelHealthList.ToArray()
                };
            }

            return new ResponseBase<PanelHealthModel[]>()
            {
                Code = 40400,
                Message = MessageContext.AssetNotExists
            };
        }

        [HttpPost("{assetId}")]
        [SwaggerOperation(Summary = "Swagger_PanelHealth_Add", Description = "Swagger_PanelHealth_Add_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<int>> AddPanelHealth(int assetId, List<PanelHealthModel> panelHealths)
        {
            if (assetId <= 0 || panelHealths == null || panelHealths.Count==0)
            {
                return new ResponseBase<int>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }
            AssetInfo panel = null;
            foreach (var panelHealth in panelHealths)
            {
                if (string.IsNullOrEmpty(panelHealth.Name) || string.IsNullOrEmpty(panelHealth.Code))
                {
                    return new ResponseBase<int>()
                    {
                        Code = 40300,
                        Message = MessageContext.ErrorParam
                    };
                }
                panel = await _client.Queryable<AssetInfo>().FirstAsync(a => a.Id == assetId && a.AssetLevel == AssetLevel.Panel);
                if(panel == null)
                {
                    return new ResponseBase<int>()
                    {
                        Code = 40400,
                        Message = MessageContext.AssetNotExists
                    };
                }
            }
            _client.Ado.BeginTran();
            //delete items before add 
            var dbList = await _client.Queryable<PanelHealth>()
                    .Where(p => p.AssetId == assetId&&!p.IsSystem)
                    .ToArrayAsync();
            foreach (var db in dbList)
            {
                await _client.Deleteable<PanelHealth>(db).ExecuteCommandAsync();
            }
           
            foreach (var panelHealth in panelHealths)
            {
              
                var dbItem = await _client.Queryable<PanelHealth>()
                    .Where(p => p.AssetId == assetId && p.Code == panelHealth.Code && p.Name == panelHealth.Name)
                    .FirstAsync();
                if (dbItem != null)
                {
                    continue;
                }

                //if (panelHealth.IsEnable)
                //{

                //}
                //var limitStr = string.Empty;
                //if (panelHealth.LimitOpt.HasValue)
                //{
                //    switch (panelHealth.LimitOpt.Value)
                //    {
                //        case 0:
                //            limitStr = JsonConvert.SerializeObject(new int[] { 0, 10 });
                //            break;
                //        case 1:
                //            limitStr = JsonConvert.SerializeObject(new int[] { 0, 100 });
                //            break;
                //        default: break;
                //    }
                //}

                var id = await _client.Insertable<PanelHealth>(new PanelHealth()
                {
                    AssetId = assetId,
                    Code = panelHealth.Code,
                    Name = panelHealth.Name ?? string.Empty,
                    Limit = JsonConvert.SerializeObject(panelHealth.Limit),
                    Weight = panelHealth.Weight ?? -1m,
                    Value = panelHealth.Value ?? 0m,
                    CreatedBy = UserName,
                    CreatedTime = DateTime.Now,
                    UpdatedBy = UserName,
                    UpdatedTime = DateTime.Now,
                }).ExecuteReturnIdentityAsync();
                await _alarmExtendServer.InsertOperationLog(UserName, "AddPanelHealth", Model.Database.Alarm.AlarmSeverity.Middle, _client, panel.AssetName, panelHealth.Name ?? string.Empty);
                 
            }
            _client.Ado.CommitTran();
            return new ResponseBase<int>()
            {
                Code = 20000,
                Data = 1
            };
        }

        [HttpPut("{assetId}")]
        [SwaggerOperation(Summary = "Swagger_PanelHealth_Update", Description = "Swagger_PanelHealth_Update_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> UpdatePanelHealth(int assetId, PanelHealthModel panelHealth)
        {
            if (assetId <= 0 || panelHealth == null || panelHealth.Id == null || panelHealth.Id <= 0 || string.IsNullOrEmpty(panelHealth.Name))
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var panel = await _client.Queryable<AssetInfo>().FirstAsync(a => a.Id == assetId && a.AssetLevel == AssetLevel.Panel);

            if (panel !=null)
            {
                var dbData = await _client.Queryable<PanelHealth>().FirstAsync(p=>p.Id == panelHealth.Id);
                if (dbData != null) 
                {
                    dbData.Name = panelHealth.Name;
                    if (panelHealth.LimitOpt.HasValue)
                    {
                        switch (panelHealth.LimitOpt)
                        {
                            case 0:
                                dbData.Limit = JsonConvert.SerializeObject(new int[] { 0, 10 });
                                break;
                            case 1:
                                dbData.Limit = JsonConvert.SerializeObject(new int[] { 0, 100 });
                                break;
                            default: break;
                        }
                    }
                    else
                    {
                        dbData.Limit = string.Empty;
                    }
                    dbData.Weight = panelHealth.Weight ?? -1m;
                    dbData.Value = panelHealth.Value ?? 0m;
                    dbData.UpdatedBy = UserName;
                    dbData.UpdatedTime = DateTime.Now;
                    await _client.Updateable(dbData).ExecuteCommandAsync();
                    await _alarmExtendServer.InsertOperationLog(UserName, "UpdatePanelHealth", Model.Database.Alarm.AlarmSeverity.Middle, _client, panel.AssetName, panelHealth.Name ?? string.Empty);
                }

                return new ResponseBase<string>()
                {
                    Code = 20000,
                    Data = MessageContext.Success
                };
            }

            return new ResponseBase<string>()
            {
                Code = 40400,
                Message = MessageContext.AssetNotExists
            };
        }

        [HttpDelete("{assetId}")]
        [SwaggerOperation(Summary = "Swagger_PanelHealth_Delete", Description = "Swagger_PanelHealth_Delete_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> DeletePanelHealth(int assetId, PanelHealthModel panelHealth)
        {
            if (assetId <= 0 || panelHealth == null || panelHealth.Id == null || panelHealth.Id <= 0)
            {
                return new ResponseBase<string>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var panel = await _client.Queryable<AssetInfo>().FirstAsync(a => a.Id == assetId && a.AssetLevel == AssetLevel.Panel);
            if (panel !=null)
            {
                var existItem = await _client.Queryable<PanelHealth>().FirstAsync(p => p.Id == panelHealth.Id && !p.IsSystem);
                
                if (existItem != null)
                {
                    await _client.Deleteable(existItem).ExecuteCommandAsync();
                    await _alarmExtendServer.InsertOperationLog(UserName, "DeletePanelHealth", Model.Database.Alarm.AlarmSeverity.Middle, _client, panel.AssetName, existItem.Name);
                    return new ResponseBase<string>()
                    {
                        Code = 20000,
                        Data = MessageContext.Success
                    };
                }
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.GetErrorValue("Common_NotExists")
                };
            }

            return new ResponseBase<string>()
            {
                Code = 40400,
                Message = MessageContext.AssetNotExists
            };
        }
    }
}
