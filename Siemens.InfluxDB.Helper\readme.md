# InfluxDB.Helper

## Basic information
The current library is extended to allow you to use Object-Relational Mapping to group time.
- Only single table is supported

## How to use
- Configure the influx db information
    1. set config into appsetting.json
    ~~~JSON
        "InfluxDb": {
            "Url": "http://127.0.0.1:8086",
            "UserName": "test",
            "Password": "testPassword",
            "Bucket": "test",
            "OrgName": "test"
        }
    ~~~
    2. use code get the configuration
    ~~~C#
        var config = _configuration.GetSection("InfluxDb").Get<InfluxDBConfig>();
    ~~~

- Create client
    1. write client
    ~~~C#
        var client = new InfluxDBClient(config);
        var writeClient = await client.GetInsertClient<DemoData>();
    ~~~
    2. query client
    ~~~C#
        var client = new InfluxDBClient(config);
        var queryClient = await client.GetQueryClient<DemoData>();
    ~~~

    3. demo data
    ~~~C#
        [Measurement("demo_data")]
        class DemoData : IInfluxData
        {
            [Column("ip", IsTag = true)] public string IP { get; set; } = string.Empty;
            [Column("port", IsTag = true)] public string Port { get; set; } = string.Empty;
            [Column("a_temperature")] public double? TempA { get; set; }
            [Column("b_temperature")] public double? TempB { get; set; }
            [Column("c_temperature")] public double? TempC { get; set; }
            [Column("n_temperature")] public double? TempN { get; set; }
            [Column(IsTimestamp = true)] public DateTime Time { get; set; }
        }
    ~~~

- Query data

    1. single query
    ~~~C#
        var data = await queryClient.WhereAnd(d => d.IP == "************" && d.Time > startTIme && d.Time < endTime).ToListAsync();
    ~~~

    2. query by group time
    ~~~C#
        var client = new InfluxDBClient(config);
        var queryClient = await client.GetQueryClient<DemoData>();

        var startTIme = DateTime.Now.AddMinutes(-10);
        var endTime = DateTime.Now;

        queryClient.WhereAnd(d => d.IP == "************" && d.Time > startTIme && d.Time < endTime);
        queryClient.GroupByTime(10, Siemens.InfluxDB.Helper.Enum.TimeInterval.Second);
        var select = queryClient.Select(d => new DemoData()
        {
            IP = d.IP,
            Port = d.Port,
            TempA = InfluxDBGroupFun.Max(d.TempA),
            TempB = InfluxDBGroupFun.Last(d.TempB)
        });

        var data = await select.ToListAsync();
    ~~~

## Client description
### Client details
|Client name       |Function name          |Desc                                                             |
|------------------|-----------------------|-----------------------------------------------------------------|
|InfluxDBClient    |GetBucket              |N/A                                                              |
|                  |GetOrganizationName    |N/A                                                              |
|                  |GetUserName            |N/A                                                              |
|                  |GetInsertClient        |Generic objects must inherit IInfluxData                         |
|                  |GetQueryClient         |Generic objects must inherit IInfluxData                         |
|                  |Dispose                |N/A                                                              |
|InsertableClient  |Insert                 |Insert one record                                                |
|                  |Insert                 |Insert records                                                   |
|                  |Dispose                |N/A                                                              |
|QueryableClient   |GroupByTime            |Set the AggregateWindow, default fn is mean                      |
|                  |Limit                  |Set the Limit                                                    |
|                  |WhereAnd               |Adding a new condition is and related to the previous condition  |
|                  |WhereOr                |Adding a new condition is or related to the previous condition   |
|                  |First                  |Set the First                                                    |
|                  |Last                   |Set the Last                                                     |
|                  |Select                 |Get the SelectableClient                                         |
|                  |ToFlux                 |Get the flux query string                                        |
|                  |ToFluxNotIncludeGroup  |Get the flux query string not include AggregateWindow info       |
|                  |ToListAsync            |Get the data                                                     |
|                  |Dispose                |N/A                                                              |
|SelectableClient  |ToFlux                 |Get the flux query string                                        |
|                  |ToFluxNotIncludeGroup  |Get the flux query string not include AggregateWindow info       |
|                  |ToListAsync            |Get the data                                                     |
|                  |Dispose                |N/A                                                              |

### Influx AggregateWindow function support
|Function name  |Desc                     |
|---------------|-------------------------|
|Max            |N/A                      |
|Min            |N/A                      |
|Mean           |N/A                      |
|Sum            |N/A                      |
|First          |N/A                      |
|Count          |N/A                      |
|Median         |N/A                      |

### influx flux support
|Function name    |Desc                                                                              |
|-----------------|----------------------------------------------------------------------------------|
|aggregateWindow  |Multiple methods produce multiple queries                                         |
|filter           |Filtration condition                                                              |
|first            |N/A                                                                               |
|from             |N/A                                                                               |
|last             |N/A                                                                               |
|limit            |The default is 20 pages per page                                                  |
|sort             |Use the reverse order of time, the current program does not support modification  |
|range            |N/A                                                                               |
|yield            |N/A                                                                               |

## Dll depend

- InfluxDB.Client 4.11.0

## Environment depend

- netcore 6.0
- influx db 2.x

## Todo List

### influx flux todo list
- group
- sort

### linq todo list
- function call
~~~C#
    // The wrong way
    queryClient.WhereAnd(d => d.IP == "************" && d.Time > DateTime.Now.AddMinutes(-10) && d.Time < DateTime.Now.AddSeconds(-5));

    // The right way
    var startTIme = DateTime.Now.AddMinutes(-10);
    var endTime = DateTime.Now;
    queryClient.WhereAnd(d => d.IP == "************" && d.Time > startTIme && d.Time < endTime);
~~~
- Coalesce
~~~C#
    var select = queryClient.Select(d => new DemoData()
    {
        IP = d.IP,
        Port = d.Port,
        TempA = tempA ?? 0d,
    });
~~~