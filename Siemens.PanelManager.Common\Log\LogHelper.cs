﻿using log4net;
using log4net.Config;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Common.Log
{
    static class LogHelper
    {
        private static ILog _log;
        public const string LogFileName = "log4net.config";
        static LogHelper()
        {
            if (File.Exists(LogFileName))
            {
                XmlConfigurator.Configure(new FileInfo(LogFileName));
            }

            _log = LogManager.GetLogger("System");
        }

        public static ILog GetLog(string name)
        {
            return LogManager.GetLogger(name);
        }

        public static void Info(string message) 
        {
            _log.Info(message);
        }

        public static void Error(string message) 
        {
            _log.Error(message);
        }

        public static void Error(Exception exception)
        {
            _log.Error(exception);
        }

        public static void Error(string message, Exception exception)
        {
            _log.Error(message, exception);
        }

        public static void Debug(string message)
        {
            _log.Debug(message);
        }
    }
}
