﻿using Siemens.PanelManager.Model.Database.Alarm;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Asset
{
    [SugarTable("asset_temperature_monitor_info")]
    public class TemperatureMonitorInfo : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "asset_id", IsNullable = false)]
        public int AssetId { get; set; }
        [SugarColumn(ColumnName = "device_name", IsNullable = true, Length = 256)]
        public string? DeviceName { get; set; }
        [SugarColumn(ColumnName = "location", IsNullable = true, Length = 512)]
        public string? Location { get; set; }
        [SugarColumn(ColumnName = "device_id", IsNullable = true, Length = 256)]
        public string? DeviceId { get; set; }
        [SugarColumn(ColumnName = "data_point_name", IsNullable = true, Length = 256)]
        public string? DataPointName { get; set; }
        [SugarColumn(ColumnName = "alarm_temperature", IsNullable = true)]
        public decimal? AlarmTemperature { get; set; }
        [SugarColumn(ColumnName = "modbus_point", IsNullable = true)]
        public int? ModbusPoint { get; set; }
        [SugarColumn(ColumnName = "bind_asset_id", IsNullable = true)]
        public int? BindAssetId { get; set; }
        [SugarColumn(ColumnName = "data_point", IsNullable = true)]
        public string? DataPoint { get; set; }
        [SugarColumn(ColumnName = "alarm_severity", IsNullable = true)]
        public AlarmSeverity? AlarmSeverity { get; set; }
    }
}
