﻿using Newtonsoft.Json;
using Siemens.PanelManager.Model.Database.Topology;

namespace Siemens.PanelManager.Model.Topology
{
    public class NodeData
    {
        [JsonProperty("type")]
        public string TypeCode { get; set; } = string.Empty;
        [JsonProperty("name", NullValueHandling = NullValueHandling.Ignore)]
        public string? Name { get; set; }
        [JsonProperty("geo2", NullValueHandling = NullValueHandling.Ignore)]
        public string? OpenStyle { get; set; }
        [JsonProperty("geo", NullValueHandling = NullValueHandling.Ignore)]
        public string? CloseStyle { get; set; }
        [JsonProperty("model", NullValueHandling = NullValueHandling.Ignore)]
        public string? Model { get; set; }
        [JsonProperty("assetId", NullValueHandling = NullValueHandling.Ignore)]
        public int? AssetId { get; set; }
        [JsonProperty("assetName", NullValueHandling = NullValueHandling.Ignore)]
        public string? AssetName { get; set; }
        [JsonProperty("size")]
        public string Size
        {
            get
            {
                return $"{SizeWidth} {SizeHight}";
            }
        }
        [JsonProperty("alarm", NullValueHandling = NullValueHandling.Ignore)]
        public bool? Alarm { get; set; }
        [JsonProperty("alarmStatus", NullValueHandling = NullValueHandling.Ignore)]
        public string? AlarmStatus { get; set; }
        [JsonProperty("swichStatus", NullValueHandling = NullValueHandling.Ignore)]
        public string? SwichStatus { get; set; }
        [JsonProperty("colorStatus", NullValueHandling = NullValueHandling.Ignore)]
        public bool? ColorStatus { get; set; }
        [JsonProperty("angle", NullValueHandling = NullValueHandling.Ignore)]
        public int? Angle { get; set; }
        [JsonProperty("category")]
        public string Category { get; set; } = string.Empty;
        [JsonProperty("key")]
        public int? Key { get; set; }
        [JsonProperty("loc")]
        public string Loctaion
        {
            get
            {
                if (!LocationCurrentX.HasValue || !LocationCurrentY.HasValue)
                {
                    return $"{LocationX} {LocationY}";
                }

                return $"{LocationCurrentX} {LocationCurrentY}";
            }
        }
        [JsonProperty("sourceType", NullValueHandling = NullValueHandling.Ignore)]
        public string? SourceType { get; set; }

        [JsonProperty("ruleIdMap", NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, string>? RuleIdMap { get; set; }

        /// <summary>
        /// 相对的 X
        /// </summary>
        [JsonIgnore]
        public int LocationX { get; set; }
        /// <summary>
        /// 相对的 Y
        /// </summary>
        [JsonIgnore]
        public int LocationY { get; set; }
        /// <summary>
        /// 图纸中的 X
        /// </summary>
        [JsonIgnore]
        public int? LocationCurrentX { get; set; }
        /// <summary>
        /// 图纸中的 Y
        /// </summary>
        [JsonIgnore]
        public int? LocationCurrentY { get; set; }
        [JsonIgnore]
        public int SizeHight { get; set; }
        [JsonIgnore]
        public int SizeWidth { get; set; }

        [JsonIgnore]
        public bool IsTransformer { get; set; } = false;

        [JsonIgnore]
        public virtual NodeType NodeType => NodeType.AnyThings;

        public virtual void AddRules(List<TopologyRuleInfo> ruleInfos)
        {

        }
    }

    public enum NodeType
    {
        Switch,
        Source,
        Meter,
        Point,
        Outlet,
        AnyThings,
        Label,
        DataPoint,
        Title,
        Transformer
    }
}
