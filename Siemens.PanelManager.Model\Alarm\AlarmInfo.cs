﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.Model.Alarm
{
    public class AlarmInfoDto
    {
        /// <summary>
        /// 脱扣级别
        /// </summary>
        public int TripSeverity { get; set; }

        /// <summary>
        /// 脱扣状态
        /// </summary>
        public int TripStatus { get; set; }

        /// <summary>
        /// 脱扣路径
        /// </summary>
        public string? TripPath { get; set; }

        /// <summary>
        /// 脱扣信息
        /// </summary>
        public string? TripInfo { get; set; }

        /// <summary>
        /// 脱扣时间
        /// </summary>
        public string? TripTime { get; set; }

        /// <summary>
        /// 告警级别
        /// </summary>
        public int AlarmSeverity { get; set; }

        /// <summary>
        /// 告警状态
        /// </summary>
        public int AlarmStatus { get; set; }

        /// <summary>
        /// 告警路径
        /// </summary>
        public string? AlarmPath { get; set; }

        /// <summary>
        /// 告警内容
        /// </summary>
        public string? AlarmInfo { get; set; }

        /// <summary>
        /// 告警时间
        /// </summary>
        public string? AlarmTime { get; set; }

        /// <summary>
        /// 消息级别
        /// </summary>
        public int MsgSeverity { get; set; }

        /// <summary>
        /// 消息状态
        /// </summary>
        public int MsgStatus { get; set; }

        /// <summary>
        /// 消息路径
        /// </summary>
        public string? MsgPath { get; set; }

        /// <summary>
        /// 消息内容
        /// </summary>
        public string? MsgInfo { get; set; }

        /// <summary>
        /// 消息时间
        /// </summary>
        public string? MsgTime { get; set; }
    }

    /// <summary>
    /// 告警日志入参
    /// </summary>
    public class AlarmInfoParam
    {  
        /// <summary>
        /// 资产id
        /// </summary>
        public int AssetId { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string? DeviceName { get; set; }

        /// <summary>
        /// 回路名称
        /// </summary>
        public string? CircuitName { get; set; }

        /// <summary>
        /// 配电柜名称
        /// </summary>
        public string? PanelName { get; set; }

        /// <summary>
        /// 配电房名称
        /// </summary>
        public string? SubStationName { get; set; }
    }

    /// <summary>
    /// 配电柜上送属性
    /// </summary>
    public class PanelUpwardDto
    {
        /// <summary>
        /// 健康评分
        /// </summary>
        public decimal HealthScore { get; set; } = 0;

        /// <summary>
        /// 健康等级
        /// </summary>
        public string? HealthGrade { get; set; }

        /// <summary>
        /// 异常指标数量
        /// </summary>
        public int AbnormalCount { get; set; }

        /// <summary>
        /// 告警信息数目统计
        /// </summary>
        public int AlarmStatusCount { get; set; }

        /// <summary>
        /// 最高温度测定指标值（℃）
        /// </summary>
        public decimal MaxTemperature { get; set; }

        /// <summary>
        /// 最高温度状态
        /// </summary>
        public string? MaxTemperatureStatus { get; set; }

        /// <summary>
        /// 最高温度状态
        /// </summary>
        public decimal MaxElectricity { get; set; }

        /// <summary>
        /// 最高电流状态
        /// </summary>
        public string? MaxElectricityStatus { get; set; }

        /// <summary>
        /// 断路器剩余寿命指标值
        /// </summary>
        public decimal RemainingLifePercentage { get; set; }

        /// <summary>
        /// 短路器剩余寿命状态
        /// </summary>
        public string? RemainingLifeStatus { get; set; }

        /// <summary>
        /// 配电柜自定义指标值
        /// </summary>
        public string? PanelCustomIndicators { get; set; }
    }

    /// <summary>
    /// 配电房上送属性
    /// </summary>
    public class SubStationUpwardDto
    {
        /// <summary>
        /// 当日配送电能 (缓存中已存在)
        /// </summary>
        //public double TotalActiveEnergy { get; set; }

        public string? AssetName { get; set; }

        /// <summary>
        /// 健康状态
        /// </summary>
        public string? HealthStatus { get; set; }

        /// <summary>
        /// 当日配送电能
        /// </summary>
        public string? SubStationLoss { get; set; }

        /// <summary>
        /// 配电房损耗时序数据
        /// </summary>
        public string? SubStationPercentage { get; set; }

        /// <summary>
        /// 配电房损耗时序数据
        /// </summary>
        public string? SubStationSafetyScope { get; set; }

        /// <summary>
        /// 当日系统平均效率
        /// </summary>
        public decimal? SubStationDayEfficiency { get; set; }

        /// <summary>
        /// 当月系统平均效率
        /// </summary>
        public decimal? SubStationMonthEfficiency { get; set; }

        /// <summary>
        /// 当年系统平均效率
        /// </summary>
        public decimal? SubStationYearEfficiency { get; set; }

        /// <summary>
        /// 配电房当日累计电能损耗
        /// </summary>
        public double SubStationDayEnergyloss { get; set; }

        /// <summary>
        /// 配电房当月累计电能损耗
        /// </summary>
        public double SubStationMonthEnergyloss { get; set; }

        /// <summary>
        /// 配电房当年累计电能损耗
        /// </summary>
        public double SubStationYearEnergyloss { get; set; }

        /// <summary>
        /// 配电房当日累计费用损耗
        /// </summary>
        public double SubStationDayCostloss { get; set; }

        /// <summary>
        /// 配电房当月累计费用损耗
        /// </summary>
        public double SubStationMonthCostloss { get; set; }

        /// <summary>
        /// 配电房当年累计费用损耗
        /// </summary>
        public double SubStationYearCostloss { get; set; }
    }

    /// <summary>
    /// 3WL的设备信息
    /// </summary>
    public class DeviceBy3WLDto
    {
        /// <summary>
        /// 容量分析
        /// </summary>
        public string? CapacityAnalysis { get; set; }

        /// <summary>
        /// 断路器负载率
        /// </summary>
        public string? LoadRate { get; set; }

    }


    /// <summary>
    /// panel健康实体
    /// </summary>
    public class PanelHealthModel
    {
        public int? Id { get; set; }

        public string? Name { get; set; }

        public decimal? Value { get; set; }

        public decimal? Weight { get; set; }

        public string? Code { get; set; }
      
        public string? Message { get; set; }

        public string? Status { get; set; }
       
        public List<PanelHealthModel>? Children { get; set; }
    }

  
}



