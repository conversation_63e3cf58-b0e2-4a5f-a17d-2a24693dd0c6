{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "TestEnv": true, "DefaultLanguage": "zh-cn", "AllowedHosts": "*", "SecretKey": "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDJKIhG77In5y4h\r\n1GnsH500sqD/W+S2g+gePFSeqvLmnqs+8Qiha/WKY/KtwAB4bgs0Ds7b2Vt8n1lX\r\nCng4waZ3rwQTlK5BsX4ZhMWuIWQYj2MIOq0mYGS0/blUzNrdEI61x2ga9HYVsWCT\r\ngws8XwKBtuZT0rtn5ycPpKLhrpP3jzhAeqXJr2neLFrVYWszlqNfwfBMmT7ISWnB\r\nBly1uLP4RThJsphKJecbqvv/HNH/f9yzye8MpOz3DxEsLRx5eXA5GXEF8EbdlGyv\r\nS4jLoH0PrHP9W7b+PuHZLC6YJpetJJ3Y/U5bG5t0gSvq6dI47sODa6I833MB6fLM\r\nn3lo2OCzAgMBAAECggEAEo+B24G8X4AuOoLd7yKPn9AnmMhC1zBel4ObjzRYyzyy\r\nslENKc/LTSShvLRtlq8Yq4n+PjzU+y+8z5Iwu5Pqpzpn1uKuHXOiHfqpPsLn+Hgq\r\nfBTl6xeT0ztiAVgRjbo8YLtweKQ7zQ6byaCaxcxCb/OPVwanshObAA+B0+8Gyh2s\r\nA15v97RrLu7biz/wmuIzxv4dbQqE4gSbJMPtdHRfZcuIZ7KetDi3mkljN6PMqyP7\r\nsxzi8cE7G4EZH7toq2IA0cWV4pK+4z3nx/n+Mz/WPlguIgr+4n+DAu2kpwA9TtDq\r\nc2eEx8qIBQ20YbqAXn2cM+DaVWzxHP6ztK8cEsy3qQKBgQDj316e+8TJMn3yQeJJ\r\nUX/jh6HuGlkLr48eHEB8B1Yc0tsh2ubV1lEai9xd2dSs3pOjdDUOkMlo0ZSFi0rl\r\nfc6Nsl8oxyGgsC1onXm7nnNy3+6osVJSVqCS1Jdp1hfZx+RnIuUfeqRA7xAvyfjm\r\ntLgWYRrYRfAuk6XexJSmkfbQVwKBgQDh/QKnVTP05wiFBVCnla6pcNUD977/C0ls\r\nZS7GSk/4IO4JmHPa4vFh9kIg9bJO6nPJhanfZfZ9TKFBYUYbxT0PKC0m1hYBqfX6\r\n+Pp6W96y9/fbi1J3bjzU8oTIoCbcE/qwMwbGjNdgopRbyZvdYHvSiZxZFmuVH74E\r\nDgTzBJJJBQKBgGUoEpQXBgue64QwtSiBEnnKCxts/NyQKcNfHU0x4ItZG0gTKegB\r\nhO146vGKsruu7vFfs/HsWky6xOjnpe7sg1Ypuc6bX5U6wF2xkom0VGHJgOPIG9hL\r\ndmrYDwM/tETMVNtoBTiNM/9TYOcmDOU1kob0wnsByRaUGs0ijS7Imz1vAoGAG31P\r\naVNv8oZ3/tZarUgD6xCSmNz2GsggslLMkcpQV/qJehlVXnbkrenquV+pxPe4BfeA\r\nZnBcv3km1HEkuhQQDVvxwhugqasnBNRsg53RRSvstoQIEAcU10J47H6uu26iu/Q5\r\nCsXvHQ4pxdCMaS1nYoDix+N2SrmFv7CE4ZrYwJECgYBNkX/eRZhXBYPrkuCEjPb7\r\njlxUhJuZ7wQuyFjRndyYtEPa5U68s/gWC03GJ3zsqFHtTasTRMHflipTkHyvsUYq\r\ntFBg9fdmNAAFfnfTjZ09WulvczFWUAwFQGHlJv0XNd3Lxn/yIAmJtlOVUL7lsNGS\r\nEyMc6Ybg+VMTE/vTG46TJg==", "ConnectionStrings": {"default": "Host=pgsql-test;port=5432;Database=PanelManager;Username=********;password=********;", "Sqlite": "Data Source=/data/data.db;Mode=ReadOnly;Cache=Private;"}, "UdcApiPath": "http://*************:5002/", "UdcDataPath": "/data", "InfluxDb": {"Url": "http://**************:8086", "UserName": "udc", "Password": "myPassWord", "Bucket": "panel_test", "OrgName": "udc"}, "MonitorApi": "http://**********:51030/", "MqttClient": {"Server": "127.0.0.1", "Port": 1883, "ClientId": "PanelManagerServer", "UserName": "admin", "Password": "admin", "Topic": "topic_1"}, "PerQueryCount": 5000}