[{"Code": "HaveAlarm", "Name": "HaveAlarm", "GroupName": "Status", "AssetLevel": 50, "AssetType": "Gateway", "AssetModel": "COM800", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "Alarm", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 99, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "MLFB", "Name": "MLFB", "GroupName": "Maintain", "AssetLevel": 50, "AssetType": "Gateway", "AssetModel": "COM800", "FilterIds": "", "UdcCode": "BDS_ID_IM0_DATA_OrderNumber", "Unit": "", "CollectMode": "", "IsSystemStatus": false, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9999, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "IsConnected", "Name": "IsConnected", "GroupName": "Status", "AssetLevel": 50, "AssetType": "Gateway", "AssetModel": "COM800", "FilterIds": "", "UdcCode": "", "Unit": "", "CollectMode": "UDCConnect", "IsSystemStatus": true, "CanListen": false, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 1, "CanAlarmListen": false, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "T", "Name": "T", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "Gateway", "AssetModel": "COM800", "FilterIds": "", "UdcCode": "BDS_TIMESTAMP", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 2, "ParentName": "Status", "MqttGroupName": "Status", "MqttSamplingPeriod": 1, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Op", "Name": "Op", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "Gateway", "AssetModel": "COM800", "FilterIds": "", "UdcCode": "BDS_OPERATING_HOUR", "Unit": "h", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 3, "ParentName": "Status", "MqttGroupName": "Status", "MqttSamplingPeriod": 1, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "WriteP", "Name": "WriteP", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "Gateway", "AssetModel": "COM800", "FilterIds": "", "UdcCode": "BDS_WRITE_PROTECT_STATE", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 4, "ParentName": "Status", "MqttGroupName": "Status", "MqttSamplingPeriod": 1, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "StateSet", "Name": "StateSet", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "Gateway", "AssetModel": "COM800", "FilterIds": "", "UdcCode": "BDS_CONFIGURATION_STATE/BDS_CONFIGURATION_STATE_M_CONFIGURATION_IS_SET_OR_NOT", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 5, "ParentName": "State3VA-line", "MqttGroupName": "Status", "MqttSamplingPeriod": 1, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "StateOk", "Name": "StateOk", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "Gateway", "AssetModel": "COM800", "FilterIds": "", "UdcCode": "BDS_CONFIGURATION_STATE/BDS_CONFIGURATION_STATE_M_CONFIGURATION_IS_OKAY", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 6, "ParentName": "State3VA-line", "MqttGroupName": "", "MqttSamplingPeriod": "", "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "<PERSON>er", "Name": "<PERSON>er", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "Gateway", "AssetModel": "COM800", "FilterIds": "", "UdcCode": "BBDS_CONFIGURATION_STATE/BDS_CONFIGURATION_STATE_M_MINIMUM_ONE_DESERTER", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 7, "ParentName": "State3VA-line", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Stowaway", "Name": "Stowaway", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "Gateway", "AssetModel": "COM800", "FilterIds": "", "UdcCode": "BDS_CONFIGURATION_STATE/BDS_CONFIGURATION_STATE_M_MINIMUM_ONE_STOWAWAY", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 8, "ParentName": "State3VA-line", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "TooManyB", "Name": "TooManyB", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "Gateway", "AssetModel": "COM800", "FilterIds": "", "UdcCode": "BDS_CONFIGURATION_STATE/BDS_CONFIGURATION_STATE_M_TOO_MANY_BREAKERS", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9, "ParentName": "State3VA-line", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Slot1", "Name": "Slot1", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "Gateway", "AssetModel": "COM800", "FilterIds": "", "UdcCode": "BDS_GLOBAL_STATE/BDS_GLOBAL_DEVICE_STATE_LOCAL_STATE_MODULE_SLOT1_PRESENT", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 10, "ParentName": "LocalDeviceStatus", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Boot", "Name": "Boot", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "Gateway", "AssetModel": "COM800", "FilterIds": "", "UdcCode": "BDS_GLOBAL_STATE/BDS_GLOBAL_DEVICE_STATE_BOOTLOADER_ACTIVE", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 11, "ParentName": "LocalDeviceStatus", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "SNTPSync", "Name": "SNTPSync", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "Gateway", "AssetModel": "COM800", "FilterIds": "", "UdcCode": "BDS_GLOBAL_STATE/BDS_GLOBAL_DEVICE_STATE_GLOBAL_STATE_NO_SYCHRONISATION_TELEGRAM", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 12, "ParentName": "GlobalDeviceState", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "DateInfo", "Name": "DateInfo", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "Gateway", "AssetModel": "COM800", "FilterIds": "", "UdcCode": "BDS_GLOBAL_STATE/BDS_GLOBAL_DEVICE_STATE_GLOBAL_STATE_DEVICE_DATE_TIME_UNCERTAIN", "Unit": "", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 13, "ParentName": "GlobalDeviceState", "MqttGroupName": "Measurement", "MqttSamplingPeriod": 5, "IsDefaultMqtt": 1, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Cv", "Name": "Cv", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "Gateway", "AssetModel": "COM800", "FilterIds": "", "UdcCode": "SupplyVoltage/Inst/Value/COM_ACC_SUPPLY_VOLTAGE", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 14, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "Sv", "Name": "Sv", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "Gateway", "AssetModel": "COM800", "FilterIds": "", "UdcCode": "SupplyVoltage/Inst/Value/BDS_SUPPLY_VOLTAGE", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 15, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "MaxSv", "Name": "MaxSv", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "Gateway", "AssetModel": "COM800", "FilterIds": "", "UdcCode": "SupplyVoltage/Inst/Greatest/BDS_SUPPLY_VOLTAGE_MAXTS", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 16, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "MaxCv", "Name": "MaxCv", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "Gateway", "AssetModel": "COM800", "FilterIds": "", "UdcCode": "SupplyVoltage/Inst/Greatest/COM_ACC_SUPPLY_VOLTAGE_MAXTS", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 17, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "MinCv", "Name": "MinCv", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "Gateway", "AssetModel": "COM800", "FilterIds": "", "UdcCode": "SupplyVoltage/Inst/Lowest/COM_ACC_SUPPLY_VOLTAGE_MINTS", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 18, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}, {"Code": "MinSv", "Name": "MinSv", "GroupName": "Measurement", "AssetLevel": 50, "AssetType": "Gateway", "AssetModel": "COM800", "FilterIds": "", "UdcCode": "SupplyVoltage/Inst/Lowest/BDS_SUPPLY_VOLTAGE_MINTS", "Unit": "V", "CollectMode": "udc-api", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 19, "CanAlarmListen": true, "CanChart": true, "CanPrint": true, "CanReportedData": true}]