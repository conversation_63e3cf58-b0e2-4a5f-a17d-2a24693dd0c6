﻿using Newtonsoft.Json;
using Siemens.PanelManager.Job.UploadExcel.Diagram;
using Siemens.PanelManager.Model.Topology3D;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.UploadExcel._3D.Model
{
    /// <summary>
    /// 分组的3D模型
    /// </summary>
    internal class GroupNode : NodeBase3D
    {
        public GroupNode() 
        {
            Name = null;
            UserData= null;
            Size = new Size()
            {
                Width = 0,
                Height = 0,
                Depth = 0,
            };
        }

        public override void Move(decimal x, decimal z)
        {
            Nodes.ForEach(n=>n.Move(x, z));
        }
        public override string NodeType => "group";
        [JsonProperty(propertyName: "nodes", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public new List<NodeBase3D> Nodes { get; set; } = new List<NodeBase3D>();
    }
}
