﻿using System.Text.RegularExpressions;

namespace Siemens.PanelManager.Monitor.Workers
{
    public class ClearLogFileWorker : BackgroundService
    {
        private ILogger<ClearLogFileWorker> _logger;
        public ClearLogFileWorker(ILogger<ClearLogFileWorker> logger)
        {
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            var runTime = DateTime.MinValue;
            while (true)
            {
                if (stoppingToken.IsCancellationRequested) return;
                if (runTime < DateTime.Today)
                {
                    runTime = DateTime.Today;
                    var days = 7;
                    var basePath = AppContext.BaseDirectory;
                    var logPath = Path.Combine(basePath, "logs");
                    var files = Directory.GetFiles(logPath, "*.log");
                    foreach (var file in files)
                    {
                        var fileInfo = new FileInfo(file);
                        var match = Regex.Match(fileInfo.Name, "^([\\d]{4})-([\\d]{1,2})-([\\d]{1,2})");
                        if (match.Success)
                        {
                            var year = int.Parse(match.Groups[1].Value);
                            var month = int.Parse(match.Groups[2].Value);
                            var day = int.Parse(match.Groups[3].Value);

                            try
                            {
                                var dateTime = new DateTime(year, month, day);
                                if (dateTime <= DateTime.Today.AddDays(0 - days))
                                {
                                    try
                                    {
                                        File.Delete(file);
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger.LogError($"文件({fileInfo.Name})删除失败", ex);
                                    }
                                }
                            }
                            catch
                            {
                            }
                        }
                    }
                }
                await Task.Delay(1000 * 10);
            }
        }
    }
}
