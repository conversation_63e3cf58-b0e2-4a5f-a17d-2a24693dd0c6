﻿﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Org.BouncyCastle.Crypto;
using Quartz.Impl.AdoJobStore.Common;
using Siemens.PanelManager.Common;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Model.Chart;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.DataPoint;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.Server.AssetDataPoint;
using Siemens.PanelManager.Server.DataPoint;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;
using System.Diagnostics.CodeAnalysis;
using TouchSocket.Sockets;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class DataPointsController : SiemensApiControllerBase
    {
        private ILogger<DataPointsController> _log;
        private DataPointServer _server;
        private readonly ISqlSugarClient _db;

        private readonly AssetDataPointInfoServer _assetDataPointInfoServer;

        public DataPointsController(DataPointServer server,
            SiemensCache cache,
            ILogger<DataPointsController> log,
            IServiceProvider provider,
            AssetDataPointInfoServer assetDataPointInfoServer)
            : base(provider, cache)
        {
            _log = log;
            _server = server;
            _assetDataPointInfoServer = assetDataPointInfoServer;
            _db = provider.GetService<SqlSugarScope>()!;
        }

        /// <summary>
        /// 获取点位信息
        /// </summary>
        /// <param name="assetLevel"></param>
        /// <param name="assetType"></param>
        /// <param name="assetModel"></param>
        /// <param name="assetId"></param>
        /// <param name="code">点位模糊查询</param>
        /// <returns></returns>
        [HttpGet("GetAllDataPoints")]
        [SwaggerOperation(Summary = "Swagger_DataPoints_Get", Description = "Swagger_DataPoints_Get_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<AssetDataPointInfo[]>> GetAllDataPoints(int assetLevel,
            [AllowNull] string? assetType,
            [AllowNull] string? assetModel,
            [AllowNull] int? assetId,
            [AllowNull] string? code)
        {
            if (assetLevel < 0)
            {
                return new ResponseBase<AssetDataPointInfo[]>
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var dataPoints = await _server.GetDataPointInfos((AssetLevel)assetLevel, assetType, assetModel, assetId);
            foreach (var dataPoint in dataPoints)
            {
                if (dataPoint.Name.StartsWith("||"))
                {
                    dataPoint.Name = dataPoint.Name.Substring(2);
                }
                else
                {
                    dataPoint.Name = MessageContext.GetDataPointName(dataPoint.Code);
                }
            }

            if (!string.IsNullOrEmpty(code))
            {
                dataPoints = dataPoints.Where(d => d.Name.Contains(code)).ToList();
            }

            return new ResponseBase<AssetDataPointInfo[]>
            {
                Code = 20000,
                Data = dataPoints.ToArray(),
            };
        }

        [HttpGet("GetDataPoints")]
        [SwaggerOperation(Summary = "Swagger_DataPoints_Get", Description = "Swagger_DataPoints_Get_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<AssetDataPointInfo[]>> GetDataPoints(int assetId, int assetLevel,
            [AllowNull] string? assetType,
            [AllowNull] string? assetModel)
        {
            if (assetLevel < 0)
            {
                return new ResponseBase<AssetDataPointInfo[]>
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var dataPoints = new List<AssetDataPointInfo>();

            // 添加第三方设备
            if (assetType == "GeneralDevice" && assetModel == "GeneralDevice")
            {
                dataPoints = await _assetDataPointInfoServer.GetDataPoints(assetId);
            }
            else
            {
                dataPoints = await _server.GetDataPointInfos((AssetLevel)assetLevel, assetType, assetModel);

                dataPoints = dataPoints.Where(d => d.CanListen).ToList();

                foreach (var dataPoint in dataPoints)
                {
                    dataPoint.Name = MessageContext.GetDataPointName(dataPoint.Code);
                }
            }

            return new ResponseBase<AssetDataPointInfo[]>
            {
                Code = 20000,
                Data = dataPoints.ToArray(),
            };
        }

        [HttpGet("{assetId}/GetSubstationDataPointConfigs")]
        [SwaggerOperation(Summary = "Swagger_GetSubstationDataPointConfigs", Description = "Swagger_GetSubstationDataPointConfigs_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<List<Output_SubstationDataPointConfig>>> GetSubstationDataPointConfigs(int assetId)
        {
            if (assetId <= 0)
            {
                return new ResponseBase<List<Output_SubstationDataPointConfig>>
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam,
                };
            }

            var result = new List<Output_SubstationDataPointConfig>();
            var dataPointServer = Provider.GetRequiredService<DataPointServer>();
            var detailsList = await dataPointServer.GetSubstationDataPointConfigDetailsNoCache(assetId);

            foreach (var d in detailsList)
            {
                result.Add(new Output_SubstationDataPointConfig(d, MessageContext));
            }

            return new ResponseBase<List<Output_SubstationDataPointConfig>>
            {
                Code = 20000,
                Data = result
            };
        }

        [HttpPut("{assetId}/AddSubstationDataPointConfig")]
        [SwaggerOperation(Summary = "Swagger_AddSubstationDataPointConfig", Description = "Swagger_AddSubstationDataPointConfig_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<int>> AddSubstationDataPointConfig([FromRoute] int assetId, [FromBody] Add_SubstationDataPointConfig param)
        {
            if (assetId <= 0
                || param == null
                || string.IsNullOrEmpty(param.BindDataPoint)
                || param.BindAssetId <= 0
                || string.IsNullOrEmpty(param.ShowName)
                || !Enum.TryParse<LayoutFormat>(param.ShowType, out _))
            {
                return new ResponseBase<int>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }
            var configs = await _db.Queryable<CustomerDataPointConfig>().Where(a=>a.LayoutName==param.ShowName&&a.AssetId== assetId).FirstAsync();
            if (configs != null)
            {
                return new ResponseBase<int>()
                {
                    Code = 40300,
                    Message = MessageContext.ShowNameError
                };
            }

            var details = param.GetDetails();
            var dataPointServer = Provider.GetRequiredService<DataPointServer>();
            var result = await dataPointServer.SaveSubstationDataPointConfig(assetId, details, UserName);

            return new ResponseBase<int>()
            {
                Code = result > 0 ? 20000 : 50000,
                Data = result
            };
        }

        [HttpPost("{assetId}/UpdateSubstationDataPointConfig")]
        [SwaggerOperation(Summary = "Swagger_UpdateSubstationDataPointConfig", Description = "Swagger_UpdateSubstationDataPointConfig_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<bool>> UpdateSubstationDataPointConfig([FromRoute] int assetId, [FromBody] Update_SubstationDataPointConfig param)
        {
            if (assetId <= 0
                || param == null
                || param.Id <= 0
                || string.IsNullOrEmpty(param.BindDataPoint)
                || param.BindAssetId <= 0
                || string.IsNullOrEmpty(param.ShowName)
                || !Enum.TryParse<LayoutFormat>(param.ShowType, out _))
            {
                return new ResponseBase<bool>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }
            var configs = await _db.Queryable<CustomerDataPointConfig>().Where(a =>a.Id!=param.Id && a.LayoutName == param.ShowName&&a.AssetId== assetId).FirstAsync();
            if (configs != null)
            {
                return new ResponseBase<bool>()
                {
                    Code = 40300,
                    Message = MessageContext.ShowNameError
                };
            }
            var details = param.GetDetails();
            var dataPointServer = Provider.GetRequiredService<DataPointServer>();
            var result = await dataPointServer.SaveSubstationDataPointConfig(assetId, details, UserName);

            return new ResponseBase<bool>()
            {
                Code = result > 0 ? 20000 : 50000,
                Data = result > 0
            };
        }

        [HttpDelete("{assetId}/DeleteSubstationDataPointConfig")]
        [SwaggerOperation(Summary = "Swagger_DeleteSubstationDataPointConfig", Description = "Swagger_DeleteSubstationDataPointConfig_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<bool>> DeleteSubstationDataPointConfig([FromRoute] int assetId, [FromQuery] int[] ids)
        {
            if (assetId <= 0
                || ids == null
                || ids.Length <= 0)
            {
                return new ResponseBase<bool>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var dataPointServer = Provider.GetRequiredService<DataPointServer>();
            var result = await dataPointServer.DeleteSubstationDataPointConfig(assetId, ids, UserName);

            return new ResponseBase<bool>()
            {
                Code = 20000,
                Data = result
            };
        }

        [HttpGet("{assetId}/GetSubstationHistoryValues")]
        [SwaggerOperation(Summary = "Swagger_GetSubstationHistoryValues", Description = "Swagger_GetSubstationHistoryValues_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task< ResponseBase<Dictionary<string, Chart_SubstationDataPoint>>> GetSubstationDataPointsChart([FromRoute] int assetId, 
            [FromQuery]long? ts = null)
        {
            if (assetId <= 0)
            {
                return new ResponseBase<Dictionary<string, Chart_SubstationDataPoint>>()
                {
                    Code = 40300,
                    Message = MessageContext.ErrorParam
                };
            }

            var dataPointServer = Provider.GetRequiredService<DataPointServer>();
            var configs = await dataPointServer.GetSubstationDataPointConfigDetailsByCache(assetId);
            configs = configs.Where(c => c.ShowType == LayoutFormat.History).ToList();
            var dic = configs.GroupBy(c => c.BindAssetId);
            var taskList = new List<Task<Dictionary<SubstationDataPointConfigDetails, LineChartModel>>>();

            var endTime = DateTime.Now.GetTimestampForSec();
            var startTime = DateTime.Now.AddDays(-1).AddSeconds(1).GetTimestampForSec();

            if (ts.HasValue && ts.Value < endTime && startTime < ts.Value)
            {
                startTime = ts.Value + 1L;
            }
            
            foreach (var kvp in dic)
            {
                var list = kvp.ToList();
                var task = dataPointServer.GetAssetChart(kvp.Key, list, startTime, endTime);
                taskList.Add(task);
            }

            var result = new Dictionary<string, Chart_SubstationDataPoint>();
            var dataList = await Task.WhenAll(taskList.ToArray());

            if (dataList != null && dataList.Length > 0)
            {
                foreach (var data in dataList)
                {
                    var keys = data.Keys;
                    foreach (var key in keys)
                    {
                        if (!result.ContainsKey(key.DataPointCode))
                        {
                            var value = data[key];
                            result.TryAdd(key.DataPointCode, new Chart_SubstationDataPoint
                            {
                                Data = value,
                                DataPointUnit = key.BindDataPointUnit,
                                ShowName = key.ShowName,
                                EndTime = endTime
                            });
                        }
                    }
                }
            }

            return new ResponseBase<Dictionary<string, Chart_SubstationDataPoint>>
            {
                Code = 20000,
                Data = result,
            };
        }
    }
}
