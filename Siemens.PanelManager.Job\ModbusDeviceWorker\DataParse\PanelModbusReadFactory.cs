﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.ModbusDeviceWorker.DataParse
{
    public class PanelModbusReadFactory
    {
        public static PanelModbusRead? Create(string transformationType, bool isBigEndian, string? parseMode, float factor, float customFactor, float intercept)
        {
            PanelModbusRead? panelModbusRead;

            // 此处可以使用反射的方式动态创建，减少代码量，设计之初，先用此种方式实现，方便修改
            switch (transformationType)
            {
                case "uint16":
                    panelModbusRead = new UInt16Read(isBigEndian, parseMode, factor, customFactor, intercept);
                    break;
                case "uint32":
                    panelModbusRead = new UInt32Read(isBigEndian, parseMode, factor, customFactor, intercept);
                    break;
                case "uint64":
                    panelModbusRead = new UInt64Read(isBigEndian, parseMode, factor, customFactor, intercept);
                    break;
                case "int16":
                    panelModbusRead = new Int16Read(isBigEndian, parseMode, factor, customFactor, intercept);
                    break;
                case "int32":
                    panelModbusRead = new Int32Read(isBigEndian, parseMode, factor, customFactor, intercept);
                    break;
                case "int64":
                    panelModbusRead = new Int64Read(isBigEndian, parseMode, factor, customFactor, intercept);
                    break;
                case "float":
                    panelModbusRead = new FloatRead(isBigEndian, parseMode, factor, customFactor, intercept);
                    break;
                default:
                    panelModbusRead = null;
                    break;
            }

            return panelModbusRead;
        }
    }
}