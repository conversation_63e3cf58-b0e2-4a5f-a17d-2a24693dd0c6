﻿using Newtonsoft.Json;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    /// <summary>
    /// 同步资产返回前端结构体
    /// </summary>
    public class SynAssetinfoResult
    {
        /// <summary>
        /// 资产类型
        /// </summary>
        public string? AssetType { get; set; }

        /// <summary>
        /// 资产名称
        /// </summary>
        public string? AssetName { get; set; }

        /// <summary>
        ///  父级id
        /// </summary>
        public long ParentId { get; set; }

        /// <summary>
        ///  子id
        /// </summary>
        public long ChildId { get; set; }


    }

    /// <summary>
    /// 导出mqtt的json配置
    /// </summary>
    public class MqttConfigResult
    {
        /// <summary>
        /// 文件信息
        /// </summary>
        [JsonProperty("fileInfo")]
        public FileInfoDto? FileInfo { get; set; }

        /// <summary>
        /// 资产信息
        /// </summary>
        [JsonProperty("assetInfo")]
        public AssetInfoDto? AssetInfo { get; set; }

        /// <summary>
        /// 子集资产信息
        /// </summary>
        [JsonProperty("configurationInfo")]
        public List<ConfigurationInfoDto> ConfigurationInfo { get; set; } = new List<ConfigurationInfoDto>();
    }

    /// <summary>
    /// 文件信息
    /// </summary>
    public class FileInfoDto
    {
        /// <summary>
        /// 格式
        /// </summary>
        [JsonProperty("format")]
        public string? Format { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        [JsonProperty("version")]
        public string? Version { get; set; }
    }

    /// <summary>
    /// 资产信息
    /// </summary>
    public class AssetInfoDto
    {
        /// <summary>
        /// 资产信息id
        /// </summary>
        [JsonProperty("assetId")]
        public string? AssetId { get; set; }

        /// <summary>
        /// 主键id
        /// </summary>
        [JsonProperty("id")]
        public string? Id { get; set; }

        /// <summary>
        /// 资产名称
        /// </summary>
        [JsonProperty("name")]
        public string? Name { get; set; }

        /// <summary>
        /// 资产版本号
        /// </summary>
        [JsonProperty("version")]
        public string? Version { get; set; }

        /// <summary>
        /// 资产描述
        /// </summary>
        [JsonProperty("description")]
        public string? Description { get; set; }

        /// <summary>
        /// 时间区域
        /// </summary>
        [JsonProperty("timeZone")]
        public string? TimeZone { get; set; }

        /// <summary>
        /// 本地信息
        /// </summary>
        [JsonProperty("location")]
        public LocationDto? Location { get; set; }
    }

    /// <summary>
    /// 本地信息
    /// </summary>
    public class LocationDto
    {
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty("country")]
        public string? Country { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty("region")]
        public string? Region { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty("locality")]
        public string? Locality { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty("streetAddress")]
        public string? StreetAddress { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty("postalCode")]
        public string? PostalCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty("longitude")]
        public string? Longitude { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty("latitude")]
        public string? Latitude { get; set; }
    }

    /// <summary>
    /// 子集资产信息
    /// </summary>
    public class ConfigurationInfoDto
    {
        /// <summary>
        /// 资产名称
        /// </summary>
        [JsonProperty("typeId")]
        public string? TypeId { get; set; }

        /// <summary>
        /// 资产级别
        /// </summary>
        [JsonProperty("assetType")]
        public string? AssetType { get; set; }

        /// <summary>
        /// 设备类型
        /// </summary>
        [JsonProperty("deviceType")]
        public string? DeviceType { get; set; }

        /// <summary>
        /// 第三方设备id
        /// </summary>
        [JsonProperty("deviceId")]
        public string? DeviceId { get; set; }

        /// <summary>
        /// 主键id
        /// </summary>
        [JsonProperty("id")]
        public string? Id { get; set; }

        /// <summary>
        /// 资产名称
        /// </summary>
        [JsonProperty("name")]
        public string? Name { get; set; }

        /// <summary>
        /// 资产描述
        /// </summary>
        [JsonProperty("description")]
        public string? Description { get; set; }

        /// <summary>
        /// 父级id
        /// </summary>
        [JsonProperty("parentId")]
        public string? ParentId { get; set; }

        /// <summary>
        /// 子集id集合
        /// </summary>
        [JsonProperty("childIds")]
        public List<string> ChildIds = new List<string>();
    }

}
