﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using Siemens.PanelManager.Common;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Model.Database.Topology;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.Server.Topology;
using Siemens.PanelManager.WebApi.StaticContent;
using Siemens.PanelManager.WebApi.ViewModel;
using SqlSugar;
using Swashbuckle.AspNetCore.Annotations;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.WebApi.Controllers
{
    [Route("api/v2/topoplogy")]
    [ApiController]
    public class TopologyForAssetController : SiemensApiControllerBase
    {
        private ILogger<TopologyForAssetController> _log;
        private ISqlSugarClient _client;
        private IServiceProvider _provider;
        private AlarmExtendServer _alarmExtendServer => _provider.GetRequiredService<AlarmExtendServer>();
        public TopologyForAssetController(SqlSugarScope client,
            SiemensCache cache,
            ILogger<TopologyForAssetController> log,
            IServiceProvider provider)
            : base(provider, cache)
        {
            _client = client;
            _log = log;
            _provider = provider;
        }

        [HttpPut("asset")]
        [SwaggerOperation(Summary = "Swagger_Topology_Update", Description = "Swagger_Topology_Update_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> Update(JObject topoplogData)
        {
            if (topoplogData == null
                || !topoplogData.ContainsKey("id"))
            {
                return new ResponseBase<string>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            string topoplogType = "Asset".ToUpper();
            int topoplogId = 0;
            var idToken = topoplogData.GetValue("id");

            if (idToken == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }

            switch (idToken.Type)
            {
                case JTokenType.String:
                    if (!int.TryParse(idToken.Value<string>(), out topoplogId))
                    {
                        return new ResponseBase<string>()
                        {
                            Code = 40301,
                            Message = MessageContext.ErrorParam
                        };
                    }
                    break;
                case JTokenType.Bytes:
                case JTokenType.Integer:
                    topoplogId = idToken.Value<int>();
                    break;
                default:
                    return new ResponseBase<string>()
                    {
                        Code = 40301,
                        Message = MessageContext.ErrorParam
                    };
            }
            var topoplogInfo = await _client.Queryable<TopologyInfo>().FirstAsync(t => t.Id == topoplogId && t.Type == topoplogType);
            if (topoplogInfo == null)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.GetErrorValue("Common_NotExists")
                };
            }

            topoplogInfo.Name = topoplogData.Value<string>("name") ?? string.Empty;
            topoplogInfo.Description = topoplogData.Value<string>("discription") ?? string.Empty;
            topoplogInfo.Data = topoplogData.ToString(Newtonsoft.Json.Formatting.None);
            topoplogInfo.UpdatedBy = UserName;
            topoplogInfo.UpdatedTime = DateTime.Now;

            await _client.Updateable(topoplogInfo).ExecuteCommandAsync();
            await _alarmExtendServer.InsertOperationLog(UserName, "UpdateTopologyAsset", Model.Database.Alarm.AlarmSeverity.Low, _client);
            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = MessageContext.Success
            };
        }

        [HttpDelete("Asset/{id}")]
        [SwaggerOperation(Summary = "Swagger_Topology_Delete", Description = "Swagger_Topology_Delete_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<string>> Delete(int id)
        {
            if (id <= 0)
            {
                return new ResponseBase<string>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            string topoplogType = "Asset".ToUpper();
            var exists = await _client.Queryable<TopologyInfo>().AnyAsync(t => t.Id == id && t.Type == topoplogType);
            if (!exists)
            {
                return new ResponseBase<string>()
                {
                    Code = 40400,
                    Message = MessageContext.GetErrorValue("Common_NotExists")
                };
            }

            var server = _provider.GetRequiredService<TopologyExtendFunc>();
            await server.DeleteTopologyByTopologyId(id, _client);
            await _alarmExtendServer.InsertOperationLog(UserName, "DeleteTopologyAsset", Model.Database.Alarm.AlarmSeverity.Low, _client);
            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = MessageContext.Success
            };
        }

        [HttpPost("Asset")]
        [SwaggerOperation(Summary = "Swagger_Topology_Add", Description = "Swagger_Topology_Add_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<int>> Add(JObject topoplogData)
        {
            string topoplogType = "Asset".ToUpper();
            if (topoplogData == null)
            {
                return new ResponseBase<int>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }

            var topoplogInfo = new TopologyInfo();
            JToken? token = null;
            topoplogData.TryGetValue("name", out token);
            if (token != null)
            {
                topoplogInfo.Name = token.ToString();
            }
            else
            {
                topoplogInfo.Name = string.Empty;
            }

            token = null;
            topoplogData.TryGetValue("code", out token);
            if (token != null)
            {
                topoplogInfo.Code = token.ToString();
            }
            else
            {
                topoplogInfo.Code = string.Empty;
            }

            token = null;
            topoplogData.TryGetValue("discription", out token);
            if (token != null)
            {
                topoplogInfo.Description = token.ToString();
            }
            else
            {
                topoplogInfo.Description = string.Empty;
            }

            topoplogInfo.Data = topoplogData.ToString(Newtonsoft.Json.Formatting.None);
            topoplogInfo.Type = topoplogType;
            topoplogInfo.CreatedBy = UserName;
            topoplogInfo.CreatedTime = DateTime.Now;
            topoplogInfo.UpdatedBy = UserName;
            topoplogInfo.UpdatedTime = DateTime.Now;

            var newId = await _client.Insertable(topoplogInfo).ExecuteReturnIdentityAsync();
            await _alarmExtendServer.InsertOperationLog(UserName, "AddTopologyAsset", Model.Database.Alarm.AlarmSeverity.Low, _client);
            return new ResponseBase<int>()
            {
                Code = 20000,
                Data = newId
            };
        }

        [HttpGet("Asset")]
        [SwaggerOperation(Summary = "Swagger_Topology_GetAll", Description = "Swagger_Topology_GetAll_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<TopologySingleInfo[]>> GetAll()
        {
            string topoplogType = "Asset".ToUpper();
            var topoplogyInfoes = await _client.Queryable<TopologyInfo>()
                .Where(t => t.Type == topoplogType)
                .Select(t => new TopologySingleInfo()
                {
                    Id = t.Id,
                    Name = t.Name,
                    Code = t.Code,
                    Time = t.CreatedTime,
                    Owner = t.CreatedBy,
                    Description = t.Description
                })
                .ToArrayAsync();

            return new ResponseBase<TopologySingleInfo[]>()
            {
                Code = 20000,
                Data = topoplogyInfoes
            };
        }

        [HttpGet("Asset/{id}")]
        [SwaggerOperation(Summary = "Swagger_Topology_Get", Description = "Swagger_Topology_Get_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<JObject>> Get(int id)
        {
            if (id <= 0)
            {
                return new ResponseBase<JObject>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }
            string topoplogType = "Asset".ToUpper();
            JObject data = new JObject();
            var topoplogyInfo = await _client.Queryable<TopologyInfo>().FirstAsync(t => t.Id == id && t.Type == topoplogType);
            if (topoplogyInfo == null)
            {
                return new ResponseBase<JObject>()
                {
                    Code = 40400,
                    Message = MessageContext.GetErrorValue("Common_NotExists")
                };
            }

            data = JObject.Parse(topoplogyInfo.Data);
            data["id"] = topoplogyInfo.Id;
            data["name"] = topoplogyInfo.Name;
            data["code"] = topoplogyInfo.Code;
            data["time"] = topoplogyInfo.CreatedTime;
            data["owner"] = topoplogyInfo.CreatedBy;
            data["discription"] = topoplogyInfo.Description;

            return new ResponseBase<JObject>()
            {
                Code = 20000,
                Data = data
            };
        }


        [HttpGet("working")]
        [SwaggerOperation(Summary = "Swagger_Topology_Working", Description = "Swagger_Topology_Working_Desc")]
        [Authorize(policy: Permission.Default)]
        public ResponseBase<string> Working()
        {
            return new ResponseBase<string>()
            {
                Code = 20000,
                Data = MessageContext.Success
            };
        }

        [HttpGet("Asset/{id}/currently")]
        [SwaggerOperation(Summary = "Swagger_Topology_CurrentlyData", Description = "Swagger_Topology_CurrentlyData_Desc")]
        [Authorize(policy: Permission.Default)]
        public async Task<ResponseBase<Dictionary<string, Dictionary<string, string>>>> Currently(int id, long ts)
        {
            string topoplogType = "Asset".ToUpper();
            if (id <= 0)
            {
                return new ResponseBase<Dictionary<string, Dictionary<string, string>>>()
                {
                    Code = 40301,
                    Message = MessageContext.ErrorParam
                };
            }

            if (!await _client.Queryable<TopologyInfo>().Where(t => t.Id == id && t.Type == topoplogType).AnyAsync())
            {
                return new ResponseBase<Dictionary<string, Dictionary<string, string>>>()
                {
                    Code = 40400,
                    Message = MessageContext.GetErrorValue("Common_NotExists")
                };
            }

            var cache = _provider.GetRequiredService<SiemensCache>();
            var topologyCodeMapCacheKey = "TopologyCodeMap:{0}";
            var topologyCodeMap = cache.Get<Dictionary<string, List<string>>>(string.Format(topologyCodeMapCacheKey, id));

            var topologyCacheKey = "TopologyCurrently:{0}";
            var value = cache.GetHashAllData(string.Format(topologyCacheKey, id));

            var result = new Dictionary<string, Dictionary<string, string>>();
            if (topologyCodeMap != null)
            {
                foreach (var kv in topologyCodeMap)
                {
                    var codes = kv.Value;
                    var data = new Dictionary<string, string>();
                    result.Add(kv.Key, data);
                    foreach (var c in codes)
                    {
                        data.TryAdd(c, string.Empty);
                    }
                }

                var r = new Regex("^\\[(?<N>[\\w|\\-|_]*)\\]\\.\\[(?<K>[\\w|\\-|_]*)\\]$");
                foreach (var kv in value)
                {
                    var match = r.Match(kv.Key);
                    if (match.Success)
                    {
                        var nodeName = match.Groups["N"].Value;
                        var key = match.Groups["K"].Value;
                        if (result.TryGetValue(nodeName, out var data))
                        {
                            if (data.ContainsKey(key))
                            {
                                data[key] = kv.Value;
                            }
                            else
                            {
                                data.Add(key, kv.Value);
                            }
                        }
                        else
                        {
                            data = new Dictionary<string, string>
                            {
                                { key, kv.Value }
                            };
                            result.Add(nodeName, data);
                        }
                    }
                    else
                    {
                        _log.LogWarning($"Topology currently data key format error: {kv.Key}");
                    }
                }
            }

            return new ResponseBase<Dictionary<string, Dictionary<string, string>>>()
            {
                Code = 20000,
                Data = result
            };
        }
    }
}
