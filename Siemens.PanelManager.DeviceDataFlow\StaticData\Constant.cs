﻿namespace Siemens.PanelManager.DeviceDataFlow.StaticData
{
    static class Constant
    {
        public const string LastUpdatedTimeCacheKey = "Alarm:LastUpdatedTime";
        public const string AssetCurrentDataCacheKey = "AssetStatus:Currently-{0}";
        public const string AssetCurrentStatusCacheKey = "AssetStatus:Currently-{0}";
        public const string AssetSimpleInfoCacheKey = "Asset:SimpleInfo-{0}";

        public const string SystemName = "PanelManager-DeviceDataFlow";
        public const string ObjectIdCacheKey = "AssetObjectId:{0}";

        /// <summary>
        /// 实时数据Influxdb measurement名称
        /// </summary>
        public const string RealTimeMeasurementName = "archivedatarealtime";

        /// <summary>
        /// 15分钟数据Influxdb measurement名称
        /// </summary>
        public const string QuarterMeasurementName = "archivedataquarter";

        public const string AssetAllDataCacheKey = "AssetStatus:All-{0}";
    }
}
