﻿namespace Siemens.PanelManager.Model.Job
{
    public class JobInfo
    {
        public string JobId { get; set; } = Guid.NewGuid().ToString();
        /// <summary>
        /// 0:尚未开始
        /// 10:进行中
        /// 20:成功
        /// 99:失败
        /// </summary>
        public int JobStatus { get; set; } = 0;

        public JobResultModel? Result { get; set; }
    }

    public class JobResultModel
    {
        public List<string> ErrorInfo { get; set; } = new List<string>();

        public object? Data { get; set; }

    }
}
