﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Mqtt\**" />
    <EmbeddedResource Remove="Mqtt\**" />
    <None Remove="Mqtt\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="InfluxDB.Client" Version="4.15.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="8.0.6" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.1" />
    <PackageReference Include="NPOI" Version="2.7.1" />
    <PackageReference Include="System.IO.Packaging" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Siemens.PanelManager.Common\Siemens.PanelManager.Common.csproj" />
    <ProjectReference Include="..\Siemens.PanelManager.Interface\Siemens.PanelManager.Interface.csproj" />
  </ItemGroup>

</Project>
