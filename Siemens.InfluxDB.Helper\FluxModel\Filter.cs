﻿using System.Text;

namespace Siemens.InfluxDB.Helper.FluxModel
{
    internal class Filter
    {
        public Filter(string measurmentFilter) 
        {
            MeasurmentFilter = measurmentFilter;
        }
        public string MeasurmentFilter { get; private set; }
        private string? FilterFlux { get; set; }
        private string? Key { get; set; }

        public void SetFilterFlux(string filterFlux, string key)
        {
            FilterFlux = filterFlux;
            Key = key;
        }

        public bool NeedRecalculateFilter(string key)
        {
            return key != Key;
        }

        public void AppendFlux(StringBuilder flux)
        {
            flux.Append("|> filter(fn: (r)=> ");
            flux.Append(MeasurmentFilter);
            if (!string.IsNullOrEmpty(FilterFlux))
            {
                flux.Append(" and ");
                flux.Append(FilterFlux);
            }
            flux.AppendLine(")");
        }
    }
}
