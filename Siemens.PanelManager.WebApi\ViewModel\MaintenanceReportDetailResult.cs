﻿using SqlSugar;
using Siemens.PanelManager.Model.WorkOrder;
using Siemens.PanelManager.Model.Chart;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class MaintenanceReportDetailResult
    {
        public int Id { get; set; }
        public string? GeneratedTime { get; set; }
        public string ReportName { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string SubstationName { get; set; } = string.Empty;
        public List<IndicatorType> IndicatorOverview { get; set; } = new List<IndicatorType>();
        public List<AssetAbnormalMessage> StationExceptionList { get; set; } = new List<AssetAbnormalMessage>();
        public List<AssetAbnormal> CabinetExceptionList { get; set; } = new List<AssetAbnormal>();
        public List<AssetAbnormal> CircuitExceptionList { get; set; } = new List<AssetAbnormal>();
        public List<AssetAbnormal> DeviceExceptionList { get; set; } = new List<AssetAbnormal>();
        public AlarmCurrentInfoResult? AlarmOverview { get; set; } = new AlarmCurrentInfoResult();
        public List<PieInfo> ReplacementPartChart { get; set; } = new List<PieInfo>();
        public List<PieInfo> PanelHealthChart { get; set; } = new List<PieInfo>();
        public List<PieInfo> BreakerHealthChart { get; set; } = new List<PieInfo>();
    }
}