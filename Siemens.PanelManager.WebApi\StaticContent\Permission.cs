﻿namespace Siemens.PanelManager.WebApi.StaticContent
{
    static class Permission
    {
        public const string Default = "Default";

        #region User
        public const string UserRead = "User.Read";
        public const string UserCreate = "User.Create";
        public const string UserUpdate = "User.Update";
        public const string UserDelete = "User.Delete";
        #endregion

        #region Role
        public const string RoleRead = "Role.Read";
        public const string RoleCreate = "Role.Create";
        public const string RoleUpdate = "Role.Update";
        public const string RoleDelete = "Role.Delete";
        #endregion

        #region Page
        public const string PageRead = "Page.Read";
        public const string PageCreate = "Page.Create";
        public const string PageUpdate = "Page.Update";
        public const string PageDelete = "Page.Delete";
        #endregion
    }
}
