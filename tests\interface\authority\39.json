{"info": {"_postman_id": "87d362ec-8325-404d-98bb-134d4c9c7730", "name": "使用管理员进行权限操作", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "用户登录(超级管理员)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = pm.response.json().data.token//获取token\r", "pm.collectionVariables.set('token',token)//把token保存到全局变量中\r", "console.log (token)\r", "\r", "pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/login", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "修改个人信息口(成功)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n \"personName\": \"hkn777\",\r\n  \"tel\": \"15751111112\",\r\n  \"email\": \"<EMAIL>\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/My/profile/info", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "My", "profile", "info"]}}, "response": []}, {"name": "更新角色名和描述(成功)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"roleId\": 1,\r\n  \"name\": \"超级管理员\",\r\n  \"desc\": \"\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/Roles", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Roles"]}}, "response": []}, {"name": "获取角色信息(成功)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"包含角色id和名称\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"id\",\"roleName\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/api/v1/Roles", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Roles"]}}, "response": []}, {"name": "获取角色权限(成功)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"包含角色id和权限\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"id\",\"label\",\"isSelect\",\"level\",\"children\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/api/v1/Roles/1/permission", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Roles", "1", "permission"]}}, "response": []}, {"name": "更改角色权限(成功)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"roleId\": 3,\r\n  \"permissionId\": [\r\n  1000000\r\n  ]\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/Roles/3/permission", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Roles", "3", "permission"]}}, "response": []}, {"name": "获取用户自己的信息(成功)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"包含用户自己的信息\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"userName\",\"personName\",\"tel\",\"email\",\"roles\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/api/v1/Users/<USER>", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Users", "info"]}}, "response": []}, {"name": "获取用户列表(最新的用户id)", "event": [{"listen": "test", "script": {"exec": ["let id = pm.response.json().data.items[0].id//获取id\r", "pm.collectionVariables.set('userId',id)//把id保存到全局变量中\r", "console.log (id)\r", "\r", "pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"包含用户自己的信息\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"id\",\"userName\",\"personName\",\"tel\",\"email\",\"roles\");\r", "});\r", "\r", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/api/v1/Users", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Users"]}}, "response": []}, {"name": "添加用户信息(循环)", "event": [{"listen": "test", "script": {"exec": ["var i = pm.collectionVariables.get(\"usernameindex\");\r", "\r", "i = Number(i) + 1;\r", "\r", "pm.collectionVariables.set(\"usernameindex\", i);\r", "\r", "pm.collectionVariables.set(\"username\",\"user-\"+i);\r", "\r", "\r", "\r", "pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"生成随机密码\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"pwd\");\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["console.log(1);\r", "var i = pm.collectionVariables.get(\"usernameindex\");\r", "console.log(2);\r", "if(!!!i){\r", "pm.collectionVariables.set(\"usernameindex\", \"1\");\r", "i = 1;\r", "}\r", "\r", "\r", "pm.collectionVariables.set(\"username\",\"user-\"+i);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n    \"userName\": \"{{username}}\",\r\n    \"personName\": \"h111\",\r\n    \"tel\": \"15751111111\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"roles\": [\r\n        {\r\n            \"id\": 5\r\n        }\r\n    ]\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/users", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "users"]}}, "response": []}, {"name": "获取用户角色(成功)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"包含角色id和名称\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"id\",\"roleName\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/api/v1/Users/<USER>/role", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Users", "{{userId}}", "role"]}}, "response": []}, {"name": "重置密码(成功)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "pm.test(\"生成随机密码\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"pwd\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/api/v1/Users/<USER>/resetpwd", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Users", "{{userId}}", "resetpwd"]}}, "response": []}, {"name": "删除用户(成功)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "text/plain"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{BASE_URL}}/api/v1/Users/<USER>", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Users", "{{userId}}"]}}, "response": []}, {"name": "获取用户列表(四位用户id)", "event": [{"listen": "test", "script": {"exec": ["let id = pm.response.json().data.items[0].id//获取id\r", "pm.collectionVariables.set('userId',id)//把id保存到全局变量中\r", "console.log (id)\r", "\r", "let id2 = pm.response.json().data.items[1].id//获取id\r", "pm.collectionVariables.set('user2Id',id2)//把id保存到全局变量中\r", "console.log (id2)\r", "\r", "let id3 = pm.response.json().data.items[2].id//获取id\r", "pm.collectionVariables.set('user3Id',id3)//把id保存到全局变量中\r", "console.log (id3)\r", "\r", "let id4 = pm.response.json().data.items[3].id//获取id\r", "pm.collectionVariables.set('user4Id',id4)//把id保存到全局变量中\r", "console.log (id4)\r", "\r", "pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"包含用户自己的信息\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"id\",\"userName\",\"personName\",\"tel\",\"email\",\"roles\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/api/v1/Users", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Users"]}}, "response": []}, {"name": "批量删除用户(成功)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"ids\": [\r\n    {{userId}},{{user2Id}},{{user3Id}},{{user4Id}}\r\n  ],\r\n  \"type\": \"string\"\r\n}"}, "url": {"raw": "{{BASE_URL}}/api/v1/Users/<USER>", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "Users", "batch"]}}, "response": []}, {"name": "用户登出(成功)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/api/v1/logout", "host": ["{{BASE_URL}}"], "path": ["api", "v1", "logout"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}], "variable": [{"key": "usernameindex", "value": 214, "type": "string"}, {"key": "username", "value": "user-214", "type": "string"}, {"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1lIjoiaGtuNzc3IiwiaHR0cDovL3NjaGVtYXMueG1sc29hcC5vcmcvd3MvMjAwNS8wNS9pZGVudGl0eS9jbGFpbXMvbmFtZWlkZW50aWZpZXIiOiJjYjNmNzA3Ni0wOGVkLTQwMzAtYTU4ZS1kOWI3MTg3MzFiYmEiLCJTeW5jRGV2aWNlIjoiW10iLCJuYmYiOjE2NzcxNDE0MjQsImV4cCI6MTY3NzE0MTQyNSwiaXNzIjoiU2llbWVuc0lzc3VlciIsImF1ZCI6IldlYkFwcEF1ZGllbmNlIn0.8yMnfBmb4d2kB9egvBtSWbntXZ2nPArNxqXTBCjNiD0", "type": "string"}, {"key": "userId", "value": 110, "type": "string"}, {"key": "user2Id", "value": 109, "type": "string"}, {"key": "user3Id", "value": 108, "type": "string"}, {"key": "roleId", "value": 1, "type": "string"}, {"key": "user4Id", "value": 107, "type": "string"}, {"key": "BASE_URL", "value": "http://localhost:5000"}]}