﻿namespace Siemens.PanelManager.Common.Tree
{
    /// <summary>
    /// 树形结构帮助类
    /// </summary>
    public static class TreeHelper
    {
        /// <summary>
        ///  构建树形结构(通用版本)
        /// </summary>
        /// <param name="treeNodes"></param>
        /// <param name="parentId"></param>
        /// <returns></returns>
        public static List<T> BuildTree<T>(this List<T> treeNodes, string? parentId) where T : ITreeNode<T>
        {
            var lookup = treeNodes.ToLookup(x => x.Pid);

            List<T> Build(string? pid)
            {
                return lookup[pid].Where(o => !o.HasMark).Select(o =>
                {
                    o.HasMark = true;
                    var children = Build(o.Rid);

                    o.Children = children;

                    return o;

                }).ToList();
            }

            var result = Build(parentId);

            return result;
        }

        /// <summary>
        /// 判断当前节点是否有子节点
        /// </summary>
        /// <typeparam name="T">树模型</typeparam>
        /// <param name="nodes">所有节点</param>
        /// <param name="nodeId">当前节点Id</param>
        /// <returns></returns>
        public static bool HaveChildren<T>(List<T> nodes, string? nodeId) where T : ITreeNode<T>
        {
            return nodes.Exists(x => x.Pid == nodeId);
        }
    }

    /// <summary>
    /// 树节点
    /// </summary>
    public interface ITreeNode<T>
    {
        /// <summary>
        /// 递归过程中防止无限循环
        /// </summary>
        public bool HasMark { get; set; }

        /// <summary>
        /// 主键Id
        /// </summary>
        public string? Rid { get; set; }

        /// <summary>
        /// 父Id
        /// </summary>
        public string? Pid { get; set; }

        /// <summary>
        /// 子节点
        /// </summary>
        public List<T>? Children { get; set; }

    }
}
