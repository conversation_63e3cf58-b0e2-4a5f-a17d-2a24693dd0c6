﻿using InfluxDB.Client;
using InfluxDB.Client.Api.Domain;
using InfluxDB.Client.Writes;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.DataFlow;
using Siemens.PanelManager.SyncService.UdcModels;
using SqlSugar;

namespace Siemens.PanelManager.SyncService.Sqlite
{
    public class ArchiveDataFormatCustomSync : SyncEntity, ISyncArchiveDataHandle
    {
        private readonly ILogger<ArchiveDataFormatCustomSync> _logger;

        public ArchiveDataFormatCustomSync(ILogger<ArchiveDataFormatCustomSync> logger,
            IDbContextFactory<UdcContext> contextFactory,
            IConfiguration configuration,
            ISqlSugarClient client,
            IAssetDataProxyRef assetDataProxyRef) : base(contextFactory, configuration, client, assetDataProxyRef)
        {
            _logger = logger;
            ConfigName = nameof(ArchiveDataFormatCustom);
        }

        public async Task SyncWorkAsync()
        {
            try
            {
                using var udcContext = await _contextFactory.CreateDbContextAsync();
                 using var client = new InfluxDBClient(InfluxdbUrl, InfluxdbUserName, InfluxdbPassword);

                var influxdbPing = await client.PingAsync();
                if (!influxdbPing)
                {
                    _logger.LogError($"InfluxDB can't connect.");
                    return;
                }

                long lastTimestamp = GetLastTimestamp();
                if (lastTimestamp <= 0)
                {
                    lastTimestamp = udcContext.ArchiveDataFormatCustoms.AsNoTracking()
                        .Select(a => a.TimestampInS)
                        .DefaultIfEmpty()
                        .Min();
                }

                var data = await (from a in udcContext.ArchiveDataFormatCustoms.AsNoTracking()
                                  join l in (
                                      from r in udcContext.ArchiveDataFormatCustoms.AsNoTracking()
                                      where r.TimestampInS >= lastTimestamp
                                      orderby r.TimestampInS
                                      select new
                                      {
                                          r.ObjectId,
                                          r.Archive,
                                          r.TimestampInS,
                                      }).Take(_perQueryCount)
                                     on new { a.ObjectId, a.Archive, a.TimestampInS } equals new { l.ObjectId, l.Archive, l.TimestampInS }
                                  orderby a.TimestampInS
                                  select a).ToListAsync();

                if (data.Any())
                {
                    lastTimestamp = data.LastOrDefault()?.TimestampInS ?? 0;
                    SaveLastTimestamp(lastTimestamp);
                }

                using var writeApi = client.GetWriteApi();

                var typeArchiveCustom = typeof(ArchiveDataFormatCustom);
                var typeArchiveProperties = typeArchiveCustom.GetProperties();
                var entityType = udcContext.Model.FindEntityType(typeArchiveCustom);

                PointData point;
                foreach (var archiveData in data)
                {
                    point = PointData.Measurement(nameof(ArchiveDataFormatCustom).ToLower());
                    Dictionary<string, string> inputData = new Dictionary<string, string>();

                    foreach (var item in typeArchiveProperties)
                    {
                        var columnName = entityType.FindProperty(item.Name).GetColumnName();

                        if (item.Name == nameof(archiveData.ObjectId) || item.Name == nameof(archiveData.Archive))
                        {
                            point = point.Tag(columnName.ToLower(), item.GetValue(archiveData)?.ToString());
                        }
                        else if (item.Name == nameof(archiveData.TimestampInS))
                        {
                            point = point.Timestamp(Convert.ToInt64(item.GetValue(archiveData)), WritePrecision.S);
                        }
                        else
                        {
                            if (item.PropertyType == typeof(string))
                            {
                                var itemValue = item.GetValue(archiveData)?.ToString() ?? string.Empty;
                                if (!string.IsNullOrWhiteSpace(itemValue) && itemValue != "NaN")
                                {
                                    point = point.Field(columnName?.ToLower(), Convert.ToDouble(itemValue));
                                }
                                else
                                {
                                    point = point.Field(columnName?.ToLower(), double.NaN);
                                }
                            }
                            else
                            {
                                point = point.Field(columnName?.ToLower(), Convert.ChangeType(item.GetValue(archiveData), item.PropertyType));
                            }
                        }

                        if (!string.IsNullOrWhiteSpace(columnName))
                        {
                            inputData.TryAdd(columnName.ToLower(), item.GetValue(archiveData)?.ToString() ?? string.Empty);
                        }
                    }

                    writeApi.WritePoint(point, InfluxdbBucket, InfluxdbOrgName);
                    _assetDataProxyRef.InputData(new AssetInputData { ObjectId = archiveData.ObjectId, Datas = inputData });
                }

                _logger.LogDebug($"{nameof(ArchiveDataFormatCustom)} Last Timestamp: {lastTimestamp}; Data Count: {data.Count}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.ToString());
                throw;
            }
        }
    }
}
