﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Siemens.PanelManager.HubModel.Client;
using TouchSocket.Sockets;

namespace Siemens.PanelManager.Tools.Model
{
    internal class ConnectStatus
    {
        public MontorClient Client { get; private set; }
        public string IP { get; private set; }
        public int Port { get; private set; }
        public ConnectStatus(string ip, int port)
        {
            IP = ip;
            Port = port;
            Client = new MontorClient(ip, port);
        }

        public async Task<bool> Start()
        {
            try
            {
                var userName = "ToolsUser";
                var passWord = "Siemens";
                var result = await Client.Start(userName, passWord);
                return result;
            }
            catch
            {
                return false;
            }
        }
    }
}
