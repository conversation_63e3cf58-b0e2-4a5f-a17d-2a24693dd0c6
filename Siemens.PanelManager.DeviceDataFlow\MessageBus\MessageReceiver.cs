﻿using Akka.Actor;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.UDC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.DeviceDataFlow.MessageBus
{
    public class MessageReceiver: IMessageReceiver
    {

        private IActorRef _ref;
        public MessageReceiver(IActorRef actorRef)
        {
            _ref = actorRef;
        }

        public void InputMessage(IUdcData data)
        {
            if (data == null) return;
            var count = CountManagerTools.GetCount("MessageReceiver");
            count.Count++;
            _ref.Tell(data);
        }

        public void InputMessage(IUdcData[] datas)
        {
            if (datas == null || datas.Length <= 0) return;
            foreach (var data in datas)
            {
                _ref.Tell(data);
            }
        }
    }
}
