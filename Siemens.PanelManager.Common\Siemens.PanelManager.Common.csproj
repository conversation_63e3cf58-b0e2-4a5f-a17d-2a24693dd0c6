﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<ProductName>Siemens.UDC.Common</ProductName>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="MesssageQueue\**" />
	  <EmbeddedResource Remove="MesssageQueue\**" />
	  <None Remove="MesssageQueue\**" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Akka" Version="1.5.24" />
		<PackageReference Include="InfluxDB.Client" Version="4.15.0" />
		<PackageReference Include="log4net" Version="2.0.17" />
		<PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.1" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.1" />
		<PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
		<PackageReference Include="MiniExcel" Version="1.32.1" />
		<PackageReference Include="Quartz.AspNetCore" Version="3.9.0" />
		<PackageReference Include="SqlSugarCore" Version="5.1.4.158" />
		<PackageReference Include="MQTTnet" Version="4.3.7.1207" />
		<PackageReference Include="TouchSocket" Version="1.3.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Siemens.InfluxDB.Helper\Siemens.InfluxDB.Helper.csproj" />
    <ProjectReference Include="..\Siemens.PanelManager.Interface\Siemens.PanelManager.Interface.csproj" />
		<ProjectReference Include="..\Siemens.PanelManager.Model\Siemens.PanelManager.Model.csproj" />
	</ItemGroup>

</Project>
