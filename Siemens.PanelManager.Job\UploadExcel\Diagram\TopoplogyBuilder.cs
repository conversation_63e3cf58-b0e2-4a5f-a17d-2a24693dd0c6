﻿using Newtonsoft.Json;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Job.UploadExcel.Diagram.TemplateModel;
using Siemens.PanelManager.Job.UploadExcel.Diagram.TemplateModel.NodeTemplate;
using Siemens.PanelManager.Model.Database.Topology;
using Siemens.PanelManager.Model.Topology;
using SqlSugar;
using System.Text;

namespace Siemens.PanelManager.Job.UploadExcel.Diagram
{
    /// <summary>
    /// 单线图主入口
    /// </summary>
    internal class TopologyBuilder
    {
        public async Task<bool> CreateTopology(List<PanelModel> panelList, ISqlSugarClient client, string createdBy, StringBuilder messageBuilder, List<TransformerModel> transformerModels)
        {
            if (panelList != null && panelList.Count > 0)
            {
                bool hasCreated = false;
                var substations = panelList.Where(p => p.ParentAsset != null).Select(p => p.ParentAsset).Distinct().ToArray();
                foreach (var substation in substations)
                {
                    if (substation == null) continue;
                    var panelDataList = panelList.Where(p => p.ParentAsset == substation).ToArray();
                    var mainDigram = new MainDigram();
                    var busBarList = new List<int>();
                    var panelNodes = new List<PanelTemplate>();
                    var idTable = new Dictionary<string, GroupKeyMappingInfo>();
                    mainDigram.GroupKeyMappings = idTable;
                    foreach (var panelModel in panelDataList)
                    {
                        if (panelModel == null) continue;
                        var panelBusBarList = panelModel.BusBars;
                        foreach (var b in panelBusBarList)
                        {
                            var barInt = RomanNumberHelper.GetNo(b);
                            if (busBarList.Contains(barInt)) continue;
                            busBarList.Add(barInt);
                        }
                        try
                        {
                            panelNodes.Add(new PanelTemplate(panelModel, messageBuilder, idTable));
                        }
                        catch
                        {

                        }
                    }

                    if (busBarList.Count > 0)
                    {
                        var maxBar = busBarList.Max();
                        for (var i = 0; i <= maxBar; i++)
                        {
                            mainDigram.SourceKeyList.Add(new List<int>());
                        }
                        var busBarArray = busBarList.OrderBy(b => b).ToArray();
                        int key = 3000;
                        int busBarKey = 0;
                        int busBarX = 0;
                        int busBarY = 0;
                        var maxHeight = 200;
                        var layout = new TopologyLayout(busBarArray);
                        var lineX = 0;
                        foreach (var busBar in busBarArray)
                        {
                            var linePanels = panelNodes.Where(n => n.BusBars.Contains(busBar)).OrderBy(n => n.PanelModel.BusBarStr).ThenBy(n => n.PanelModel.Index).ToArray();
                            if (linePanels.Length <= 0) continue;

                            if (layout.NeedCreateNewLine(busBar))
                            {
                                mainDigram.Y += maxHeight + 350;
                                busBarY = mainDigram.Y;
                                busBarX = 0;
                                lineX = 0;
                                if (linePanels.Any(n => !n.HasLink))
                                {
                                    maxHeight = linePanels.Where(n => !n.HasLink).Max(n => n.Height);
                                }
                            }
                            else
                            {
                                busBarX = lineX + 300;
                                if (linePanels.Any(n => !n.HasLink))
                                {
                                    var max = linePanels.Where(n => !n.HasLink).Max(n => n.Height);
                                    if (maxHeight < max) maxHeight = max;
                                }
                            }

                            var firstPanel = linePanels.FirstOrDefault();
                            TransformerModel? transformer = null;
                            if (firstPanel != null)
                            {
                                transformer = transformerModels.FirstOrDefault(t => t.AssetInfo.RelationPanelId.HasValue
                                        && t.AssetInfo.RelationPanelId == firstPanel.PanelModel.AssetInfo.Id
                                        && t.BusBars.Contains(RomanNumberHelper.GetString(busBar)));

                                if (transformer != null)
                                {
                                    busBarX += 350;
                                }
                            }

                            var busBarModel = new BusBarTemplate(busBar, busBarX, busBarY, messageBuilder);
                            busBarModel.SetKey(busBarKey);
                            int lastX = busBarX;

                            int assetId = 0;
                            
                            for (var i = 0; i< linePanels.Length;i++)
                            {
                                var linePanel = linePanels[i];
                                var offset = 0;

                                var width = 100;

                                transformer = transformerModels.FirstOrDefault(t => t.AssetInfo.RelationPanelId.HasValue 
                                    && t.AssetInfo.RelationPanelId == linePanel.PanelModel.AssetInfo.Id 
                                    && t.BusBars.Contains(busBarModel.BusBarName));

                                var needAddOffset = false;

                                if (transformer != null)
                                {
                                    if (i == 0)
                                    {
                                        offset += linePanel.AddTransformer(transformer, "Right");
                                        lastX += offset;
                                    }
                                    else if (i == (linePanels.Length - 1))
                                    {
                                        offset += linePanel.AddTransformer(transformer, "Left");
                                        needAddOffset = true;
                                    }
                                    else
                                    {
                                        offset += linePanel.AddTransformer(transformer, "Right");
                                        lastX += offset;
                                    }
                                }

                                if (!linePanel.HasLink)
                                {
                                    width = linePanel.Width + 120;
                                    if (needAddOffset)
                                    {
                                        width += offset;
                                    }

                                    key = linePanel.SetKey(key);
                                    mainDigram.Nodes.AddRange(linePanel.NodeDatas);
                                    mainDigram.Lines.AddRange(linePanel.LineDatas);
                                    linePanel.SetLocation(lastX, busBarModel.Y + 130);
                                    linePanel.SetTotalHeight(maxHeight);
                                    linePanel.FinishLoading();
                                }

                                if (assetId <= 0)
                                {
                                    assetId = linePanel.PanelModel.AssetInfo.Id;
                                }
                                var node = busBarModel.CreateNode(width, offset: needAddOffset ? 0 : offset);
                                if (node == null) continue;
                                node.AssetId = assetId;
                                linePanel.LinkToBusBar(busBar, node);
                                lastX += width;

                                var keyIds = linePanel.NodeDatas.Where(n => n.NodeType == NodeType.Source).Select(n => n.Key ?? 0).ToList();
                                mainDigram.SourceKeyList[busBar].AddRange(keyIds);
                            }
                            if (mainDigram.X < lastX)
                            {
                                mainDigram.X = lastX;
                            }

                            if (lineX < lastX)
                            {
                                lineX = lastX;
                            }

                            mainDigram.Nodes.AddRange(busBarModel.NodeDatas);
                            mainDigram.Lines.AddRange(busBarModel.LineDatas);
                            busBarKey = busBarModel.GetCurrentId();

                        }

                        #region 添加图纸名
                        {
                            var maxId = mainDigram.Nodes.Select(n => n.Key).Max();
                            //var topologyNameX = mainDigram.X / 2;
                            var topologyNameX = 500;
                            var topologyNameY = -150;

                            var topologyNameLable = new TitleNode()
                            {
                                Text = string.Format(substation.DrawingCode ?? "{0}-000", "SLD")
                            };
                            topologyNameLable.Key = ++maxId;
                            topologyNameLable.LocationX = topologyNameX;
                            topologyNameLable.LocationY = topologyNameY;
                            mainDigram.Nodes.Add(topologyNameLable);
                        }
                        #endregion
                    }

                    if (mainDigram.Nodes == null || mainDigram.Nodes.Count == 0) continue;

                    UpdateLocation(mainDigram);

                    var rules = new List<TopologyRuleInfo>();

                    mainDigram.Nodes.ForEach(n => n.AddRules(rules));

                    if (rules.Count > 0)
                    {
                        var topologyRuleDetals = new Dictionary<string, TopologyRuleDetails>();
                        foreach (var rule in rules)
                        {
                            topologyRuleDetals.Add(rule.RuleCode, new TopologyRuleDetails
                            {
                                AssetId = rule.AssetId ?? 0,
                                DataPoint = rule.DataPoint,
                                TargeInfo = new TopologyTargeInfo
                                {
                                    TargetIdentify = rule.TargetIdentify,
                                    TargetProperty = rule.TargetProperty,
                                }
                            });
                        }

                        mainDigram.BindRule = topologyRuleDetals;
                    }

                    #region 更新数据
                    var model = new TopologyModel()
                    {
                        Topology = mainDigram,
                        CreatedBy = createdBy,
                    };

                    var topoplopyId = await client.Insertable(new TopologyInfo()
                    {
                        Data = JsonConvert.SerializeObject(model),
                        Name = substation.AssetName,
                        Code = string.Format(substation.DrawingCode ?? "{0}-000", "SLD"),
                        Description = string.Empty,
                        Type = "topology",
                        TopologyFlag = Guid.NewGuid().ToString(),
                        CreatedBy = createdBy,
                        CreatedTime = DateTime.Now,
                        UpdatedBy = createdBy,
                        UpdatedTime = DateTime.Now,
                    }).ExecuteReturnIdentityAsync();

                    await client.Insertable(new TopologyIdManager()
                    {
                        Key = topoplopyId,
                        Type = "Dashboard",
                        CreatedBy = createdBy,
                        CreatedTime = DateTime.Now,
                        UpdatedBy = createdBy,
                        UpdatedTime = DateTime.Now,
                    }).ExecuteCommandAsync();

                    if (rules.Count > 0)
                    {
                        rules.ForEach(rule =>
                        {
                            rule.TopologyId = topoplopyId;
                            rule.CreatedBy = createdBy;
                            rule.CreatedTime = DateTime.Now;
                            rule.UpdatedBy = createdBy;
                            rule.UpdatedTime = DateTime.Now;
                        });

                        await client.Insertable(rules).ExecuteCommandAsync();
                    }
                    substation.TopologyId = topoplopyId;
                    substation.UpdatedBy = createdBy;
                    substation.UpdatedTime = DateTime.Now;
                    await client.Updateable(substation).ExecuteCommandAsync();
                    hasCreated = true;
                    #endregion
                }

                if (!hasCreated && messageBuilder.Length == 0)
                {
                    messageBuilder.AppendLine("Topology_MissInfo");
                }
                return hasCreated;
            }

            return false;
        }

        private void UpdateLocation(MainDigram main)
        {
            var newX = main.X / 2;
            var newY = main.Y / 2;

            main.Nodes.ForEach(n =>
            {
                if (n.LocationCurrentX.HasValue)
                {
                    n.LocationCurrentX -= newX;
                }
                else
                {
                    n.LocationCurrentX = n.LocationX - newX;
                }
                if (n.LocationCurrentY.HasValue)
                {
                    n.LocationCurrentY -= newY;
                }
                else
                {
                    n.LocationCurrentY = n.LocationY - newY;
                }
            });
        }
    }
}
