﻿using Siemens.PanelManager.Model.Database.Auth;
using Siemens.PanelManager.WebApi.StaticContent;
using System.Text;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class AllRoleResult
    {
        public AllRoleResult(Role[] roles, List<Page> pages, List<RolePageMapping> rolePages, MessageContext messageContext)
        {
            #region Order by
            roles = roles.OrderBy(r => r.Id).ToArray();
            #endregion

            var rolesList = new List<GetRoleInfo>();
            foreach (var role in roles)
            {
                var rolePageMappings = rolePages.Where(r => r.RoleId == role.Id).ToArray();
                var rolePageList = pages.Where(p => rolePageMappings.Any(r => r.PageId == p.Id && p.PageLevel == PageLevel.EnumKey)).ToArray();
                rolesList.Add(new GetRoleInfo(role, rolePageList, messageContext));
            }
            Items = rolesList.ToArray();
            Total = Items.Length;
        }
        public GetRoleInfo[] Items { get; set; }
        public int Total { get; set; }
    }

    public class GetRoleInfo
    {
        public GetRoleInfo(Role role, MessageContext messageContext)
        {
            Id = role.Id;
            RoleName = messageContext.GetRoleName(role.RoleCode, role.RoleName);
            Desc = role.RoleDescription;
        }

        public GetRoleInfo(Role role, Page[] pages, MessageContext messageContext)
        {
            Id = role.Id;
            RoleName = messageContext.GetRoleName(role.RoleCode, role.RoleName);
            var desc = new StringBuilder();
            foreach (var p in pages)
            {
                if (desc.Length > 0)
                {
                    desc.Append(',');
                }
                desc.Append(messageContext.GetPageName(p.Id, p.PageName));
            }

            Desc = desc.ToString();
        }

        public int Id { get; set; }
        public string RoleName { get; set; }
        public string? Desc { get; set; }
    }
}
