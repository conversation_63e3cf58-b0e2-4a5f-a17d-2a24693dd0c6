﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Algorithm
{
    
    public class LossPredictModel
    {
        [JsonProperty(PropertyName = "times")]
        public string[] times { get; set; }

        [JsonProperty(PropertyName = "values")]
        public LossPredictModelVlaues values { get; set; }

    }
    public class LossPredictModelVlaues
    {
        [JsonProperty(PropertyName = "max")]
        public decimal[] max { get; set; }
        [JsonProperty(PropertyName = "min")]
        public decimal[] min { get; set; }
        [JsonProperty(PropertyName = "lossW")]
        public decimal[] lossW { get; set; }
        public string abnormal { get; set; }
    }
}
