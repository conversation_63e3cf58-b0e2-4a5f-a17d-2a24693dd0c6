{"info": {"_postman_id": "bf0b47fd-5f08-4537-b58f-9c35f134eaf4", "name": "01使用管理员账号进入panel manager智慧分析中的损耗分析菜单，切换当日or当月or当年or总计查看损耗电量", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "获取单个资产的详情 Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "let s1 = pm.response.json().items[0].id\r", "pm.environment.set(\"s1\", s1);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Asset/search?levels=Substation", "host": ["{{baseUrl}}"], "path": ["api", "v1", "<PERSON><PERSON>", "search"], "query": [{"key": "levels", "value": "Substation"}]}}, "response": []}, {"name": "获取累计指标", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"成功获取当日/当月/ 当年/总计损耗电量\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"currentDate\");\r", "    pm.expect(pm.response.text()).to.include(\"yearDate\");\r", "    pm.expect(pm.response.text()).to.include(\"monthDate\");\r", "    pm.expect(pm.response.text()).to.include(\"sum\");\r", "    pm.expect(pm.response.text()).to.include(\"loss_equivalent_electricity\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/LossDiagnosis/{{s1}}/getKpi", "host": ["{{baseUrl}}"], "path": ["api", "v1", "LossDiagnosis", "{{s1}}", "getKpi"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}], "variable": [{"key": "s1", "value": ""}]}