﻿using Newtonsoft.Json;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.System;
using SqlSugar;

namespace Siemens.PanelManager.Common.mqtt
{
    /// <summary>
    /// MqttConfig配置
    /// </summary>
    public class MqttConfig
    {
        /// <summary>
        /// 是否自动启用(0:不启用 1:启用)
        /// </summary>
        public string? AutoStart { get; set; } = "0";

        /// <summary>
        /// ip地址
        /// </summary>
        public string? Broker { get; set; }

        /// <summary>
        /// 端口号
        /// </summary>
        public string? Port { get; set; }

        /// <summary>
        /// ClientId
        /// </summary>
        public string? ClientId { get; set; }

        /// <summary>
        /// 是否使用证书(0:不使用，1：使用Tls, 2: 无验证, 3: 双向验证)
        /// </summary>
        public string? UseTLS { get; set; } = "0";

        /// <summary>
        /// Tls文件路径
        /// </summary>
        public string? TLSPemFile { get; set; }

        /// <summary>
        /// Tls文件真实路径
        /// </summary>
        public string? TLSPemFilePath { get; private set; }

        /// <summary>
        /// 验证类型(0:基本身份认证，1:TLs客户端证书认证，2：无，3：双向验证)
        /// </summary>
        public string? AuthenticationType { get; set; } = "0";

        /// <summary>
        /// 用户名
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string? Password { get; set; }

        /// <summary>
        /// mqtt主题
        /// </summary>
        public string? Topic { get; set; }

        /// <summary>
        /// ClientPemFile证书路径
        /// </summary>
        public string? ClientPemFile { get; set; }

        /// <summary>
        /// ClientPemFile证书真实路径
        /// </summary>
        public string? ClientPemFilePath { get; private set; }

        /// <summary>
        /// ClientPrivateFile证书路径
        /// </summary>
        public string? ClientPrivateFile { get; set; }

        /// <summary>
        /// ClientPrivateFile证书真实路径
        /// </summary>
        public string? ClientPrivateFilePath { get; private set; }

        /// <summary>
        /// mqtt Qos配置(0,1,2)
        /// </summary>
        public int Qos { get; set; } = 0;

        public virtual async Task InitBySql(ISqlSugarClient sqlSugarClient)
        {
            var data = await sqlSugarClient.Queryable<SystemConfig>().Where(c => c.Type == "MQTT").ToDictionaryAsync(c => c.Name, c => c.Value);

            try
            {
                var tempCconfig = JsonConvert.DeserializeObject<MqttConfig>(JsonConvert.SerializeObject(data));
                if (tempCconfig != null)
                {
                    this.AutoStart = tempCconfig.AutoStart;
                    this.AuthenticationType = tempCconfig.AuthenticationType;

                    this.Port = tempCconfig.Port;
                    this.Password = tempCconfig.Password;

                    this.Topic = tempCconfig.Topic;
                    this.TLSPemFile = tempCconfig.TLSPemFile;

                    this.UserName = tempCconfig.UserName;
                    this.UseTLS = tempCconfig.UseTLS;

                    this.Broker = tempCconfig.Broker;
                    
                    this.Qos = tempCconfig.Qos;

                    this.ClientId = tempCconfig.ClientId;
                    this.ClientPrivateFile = tempCconfig.ClientPrivateFile;
                    this.ClientPemFile = tempCconfig.ClientPemFile;

                    await UpdateFilePath(sqlSugarClient);
                }
            }
            catch
            {
            }
        }

        protected virtual async Task UpdateFilePath(ISqlSugarClient sqlSugarClient)
        {
            var fileIds = new string[] { TLSPemFile ?? string.Empty, ClientPemFile ?? string.Empty, ClientPrivateFile ?? string.Empty };
            var fileInfoes = await sqlSugarClient.Queryable<FileManager>().Where(f => (fileIds.Contains(f.Code) || fileIds.Contains(f.Url)) && !f.IsSystemFile).ToArrayAsync();
            var uploadFileDir = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploadfiles", "personal");

            foreach (FileManager file in fileInfoes)
            {
                if (string.IsNullOrWhiteSpace(file.Code) || string.IsNullOrWhiteSpace(file.Url)) continue;

                if (file.Url == TLSPemFile || file.Code == TLSPemFile)
                {
                    TLSPemFilePath = Path.Combine(uploadFileDir, file.Url);
                }

                if (file.Url == ClientPemFile || file.Code == ClientPemFile)
                {
                    ClientPemFilePath = Path.Combine(uploadFileDir, file.Url);
                }

                if (file.Url == ClientPrivateFile || file.Code == ClientPrivateFile)
                {
                    ClientPrivateFilePath = Path.Combine(uploadFileDir, file.Url);
                }
            }
        }
    }

    /// <summary>
    /// 是否手动停止
    /// </summary>
    public class WhetherToManuallyStop
    {
        /// <summary>
        /// 私有构造函数
        /// </summary>
        private WhetherToManuallyStop() { }

        /// <summary>
        /// 实例化
        /// </summary>
        private static readonly WhetherToManuallyStop SIntance = new WhetherToManuallyStop();

        /// <summary>
        /// 外部访问器
        /// </summary>
        public static WhetherToManuallyStop Instance
        {
            get { return SIntance; }
        }

        /// <summary>
        /// (异常编码:50000，正常编码:20000)
        /// </summary>
        public int AbnormalCode { get; set; } = 20000;
    }

    /// <summary>
    /// 推送点位信息临时对象
    /// </summary>

    public class AssetMqttDataPointConfigDto
    {
        /// <summary>
        /// 资产id
        /// </summary>
        public long AssetId { get; set; }

        /// <summary>
        /// 文件夹层级
        /// </summary>
        public GroupConfigType ConfigType { get; set; }

        /// <summary>
        /// 文件夹名称
        /// </summary>
        public string? GroupName { get; set; }

        /// <summary>
        /// 点位Code
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 点位名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 资产类型
        /// </summary>
        public string? AssetModel { get; set; }

        /// <summary>
        /// UdcCode
        /// </summary>
        public string? UdcCode { get; set; }

        /// <summary>
        /// 周期
        /// </summary>
        public int SamplingPeriod { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? Unit { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? ObjectId { get; set; }
    }


    public class AssetInfoEnity
    {
        /// <summary>
        /// 资产id
        /// </summary>
        public long AssetId { get; set; }

        /// <summary>
        /// 资产名称
        /// </summary>
        public string? AssetName { get; set; }

        /// <summary>
        /// 父级id
        /// </summary>
        public long ParentId { get; set; }
    }
}
