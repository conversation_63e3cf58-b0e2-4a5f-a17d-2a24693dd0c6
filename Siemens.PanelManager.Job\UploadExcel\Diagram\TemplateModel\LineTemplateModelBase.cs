﻿using Siemens.PanelManager.Model.Topology;
using System.Text;

namespace Siemens.PanelManager.Job.UploadExcel.Diagram.TemplateModel
{
    /// <summary>
    /// 线的模板基类
    /// </summary>
    internal abstract class LineTemplateModelBase
    {
        protected StringBuilder Message { get; private set; }
        public LineTemplateModelBase(int direction, int x, int y, StringBuilder message)
            : this(x, y, message)
        {
            Direction = direction;

        }

        public LineTemplateModelBase(int x, int y, StringBuilder message)
        {
            X = x;
            Y = y;
            Message = message;
        }

        /// <summary>
        /// 图形方向 
        /// 0 = 横向排布 (默认)
        /// 1 = 纵向排布
        /// </summary>
        protected virtual int Direction { get; set; } = 0;
        public virtual int Length { get; protected set; }
        public virtual int X { get; protected set; }
        public virtual int Y { get; protected set; }
        /// <summary>
        /// 设置通过初始 Key 更新当前点和线对应的 Key值
        /// </summary>
        /// <param name="beforeKey">初始Key</param>
        /// <returns>最大的Key值</returns>
        public virtual int SetKey(int beforeKey)
        {
            _maxId += beforeKey;
            int maxKey = beforeKey;
            foreach (var node in NodeDatas)
            {
                if (!node.Key.HasValue) continue;
                node.Key += beforeKey;
                if (node.Key > maxKey)
                {
                    maxKey = node.Key.Value;
                }
            }

            foreach (var line in LineDatas)
            {
                if (line.To > 0)
                {
                    line.To += beforeKey;
                }

                if (line.From > 0)
                {
                    line.From += beforeKey;
                }

                if (line.Key.HasValue)
                {
                    line.Key += beforeKey;
                    if (line.Key > maxKey)
                    {
                        maxKey = line.Key.Value;
                    }
                }
            }
            return maxKey;
        }

        private int _maxId = 0;
        public virtual int GetNewId()
        {
            return ++_maxId;
        }
        public virtual int GetCurrentId()
        {
            return _maxId;
        }
        public virtual void SetMaxId(int key)
        {
            _maxId = key;
        }
        public abstract NodeData[] NodeDatas { get; }
        public abstract LineData[] LineDatas { get; }
    }
}
