﻿using Akka.Routing;
using NPOI.OpenXml4Net.OPC.Internal;
using Siemens.PanelManager.DeviceDataFlow.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.DeviceDataFlow.StaticData
{
    class TopologyStaticData
    {
        private const int _timeOut = 1000;
        private static TopologyStaticData? _instance;

        public static TopologyStaticData Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new TopologyStaticData();
                }
                return _instance;
            }
        }

        private IReadOnlyList<TopologyListener> _listeners = new List<TopologyListener>();

        public IReadOnlyList<TopologyListener> Listeners
        {
            get
            {
                return _listeners;
            }
        }

        private ManualResetEvent _event = new ManualResetEvent(false);

        private List<TopologyListener> _listenerList = new List<TopologyListener>();

        #region Update
        public bool StartUpdateNoWait()
        {
            if(!_event.WaitOne(0))
            {
                _event.Set();
                return true;
            }
            return false;
        }

        public async Task StartUpdateByWait()
        {
            var i = 0;
            while (true)
            {
                if (!_event.WaitOne(10))
                {
                    _event.Set();
                    return;
                }
                await Task.Delay(100);
                i++;

                if (i >= _timeOut)
                {
                    throw new TimeoutException("topology change timeout, please check the topology data source.");
                }
            }
        }

        public void EndUpdate()
        {
            if (_event.WaitOne(0))
            {
                _listeners = GetReadOnlyListeners();
                _event.Reset();
            }
        }

        public TopologyListener? AddListener(TopologyListener listener)
        {
            if (_event.WaitOne(0))
            {
                var existListener = _listenerList.FirstOrDefault(l => l.TopologyId == listener.TopologyId);

                if(existListener == null)
                {
                    _listenerList.Add(listener);
                }
                else
                {
                    existListener.Copy(listener);
                }

                return existListener ?? listener;
            }

            return null;
        }

        public void RemoveListener(int[] topologyIds)
        {
            if (_event.WaitOne(0))
            {
                var existsTopologys = _listenerList.Where(l => topologyIds.Contains(l.TopologyId)).ToList();
                if (existsTopologys.Any())
                {
                    foreach (var t in existsTopologys)
                    {
                        _listenerList.Remove(t);
                    }
                }
            }
        }

        public List<TopologyListener>? GetListeners(Func<TopologyListener, bool> predicate)
        {
            if (_event.WaitOne(0))
            {
                return _listenerList.Where(predicate).ToList();
            }
            return null;
        }
        #endregion

        private IReadOnlyList<TopologyListener> GetReadOnlyListeners()
        {
            var newListeners = new List<TopologyListener>(_listenerList.Count);
            foreach (var listener in _listenerList)
            {
                newListeners.Add(listener.Clone());
            }
            return newListeners.AsReadOnly();
        }
    }
}
