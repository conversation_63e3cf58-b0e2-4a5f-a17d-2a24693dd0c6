﻿using Siemens.PanelManager.Model.Database.Topology;

namespace Siemens.PanelManager.DeviceDataFlow.Model
{
    internal class TopologyListener
    {
        private TopologyListener(int topologyId, 
            string topologyType, 
            Dictionary<string, List<string>> codeMap,
            Dictionary<int, List<TopologyRuleInfo>> relatedAsset,
            Dictionary<string, List<TopologyRuleInfo>> relatedObject, 
            int? parentTopologyId)
        {
            TopologyId = topologyId;
            RelatedAsset = relatedAsset;
            RelatedObject = relatedObject;
            TopologyType = topologyType;
            CodeMap = codeMap;
            ParentTopologyId = parentTopologyId;
        }

        public TopologyListener(int topologyId, string topologyType, Dictionary<string, List<string>> codeMap)
        {
            TopologyId = topologyId;
            RelatedAsset = new Dictionary<int, List<TopologyRuleInfo>>();
            RelatedObject = new Dictionary<string, List<TopologyRuleInfo>>();
            TopologyType = topologyType;
            CodeMap = codeMap;
        }
        public TopologyListener(int topologyId, string topologyType,  List<TopologyRuleInfo> ruleInfos, Dictionary<string, List<string>> codeMap) 
        {
            TopologyId = topologyId;
            RelatedAsset = new Dictionary<int, List<TopologyRuleInfo>>();
            RelatedObject = new Dictionary<string, List<TopologyRuleInfo>>();
            TopologyType = topologyType;

            foreach(var ruleInfo in ruleInfos) 
            {
                if(ruleInfo.AssetId.HasValue) 
                {
                    RelatedAsset.TryAdd(ruleInfo.AssetId.Value, new List<TopologyRuleInfo>());
                    RelatedAsset[ruleInfo.AssetId.Value].Add(ruleInfo);
                }
                if (!string.IsNullOrEmpty(ruleInfo.ObjectName))
                {
                    RelatedObject.TryAdd(ruleInfo.ObjectName, new List<TopologyRuleInfo>());
                    RelatedObject[ruleInfo.ObjectName].Add(ruleInfo);
                }
            }

            CodeMap = codeMap;
        }
        public int TopologyId { get; private set; }
        public string TopologyType { get; private set; }
        public Dictionary<int, List<TopologyRuleInfo>> RelatedAsset { get; private set; }
        public Dictionary<string, List<TopologyRuleInfo>> RelatedObject { get; private set; }

        public Dictionary<string, List<string>> CodeMap { get; private set; }

        public int? ParentTopologyId { get; set; }

        public void Copy(TopologyListener parentListener)
        {
            ParentTopologyId = parentListener.ParentTopologyId;
            TopologyId = parentListener.TopologyId;
            TopologyType = parentListener.TopologyType;
            RelatedAsset = parentListener.RelatedAsset;
            RelatedObject = parentListener.RelatedObject;
            CodeMap = parentListener.CodeMap;
        }

        public TopologyListener Clone()
        {
            var newCodeMap = new Dictionary<string, List<string>>();
            foreach(var kv in CodeMap)
            {
                newCodeMap.Add(kv.Key, new List<string>(kv.Value));
            }
            var newRelatedAsset = new Dictionary<int, List<TopologyRuleInfo>>();
            foreach (var kv in RelatedAsset)
            {
                var newList = new List<TopologyRuleInfo>();
                foreach (var rule in kv.Value)
                {
                    newList.Add(CloneRule(rule));
                }

                newRelatedAsset.Add(kv.Key, newList);
            }

            var objectRelated = new Dictionary<string, List<TopologyRuleInfo>>();
            foreach (var kv in RelatedObject)
            {
                var newList = new List<TopologyRuleInfo>();
                foreach (var rule in kv.Value)
                {
                    newList.Add(CloneRule(rule));
                }

                objectRelated.Add(kv.Key, newList);
            }

            var newListener = new TopologyListener(TopologyId, TopologyType, newCodeMap, newRelatedAsset, objectRelated, ParentTopologyId);
            return newListener;
        }

        private TopologyRuleInfo CloneRule(TopologyRuleInfo rule)
        {
            return new TopologyRuleInfo
            {
                RuleCode = rule.RuleCode,
                RuleType = rule.RuleType,
                AssetId = rule.AssetId,
                CreatedBy = rule.CreatedBy,
                CreatedTime = rule.CreatedTime,
                DataPoint = rule.DataPoint,
                FormatFunction = rule.FormatFunction,
                Id = rule.Id,
                ObjectName = rule.ObjectName,
                TargetIdentify = rule.TargetIdentify,
                TargetProperty = rule.TargetProperty,
                TopologyId = rule.TopologyId,
                UpdatedBy = rule.UpdatedBy,
                UpdatedTime = rule.UpdatedTime
            };
        }
    }
}
