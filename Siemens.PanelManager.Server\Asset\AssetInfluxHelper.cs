﻿using InfluxDB.Client;
using InfluxDB.Client.Api.Domain;
using InfluxDB.Client.Writes;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Common.Model;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.Config;

namespace Siemens.PanelManager.Server.Asset
{
    public class AssetInfluxHelper
    {
        private ILogger _logger;

        /// <summary>
        /// 实时数据Influxdb measurement名称
        /// </summary>
        public const string RealTimeMeasurementName = "archivedatarealtime";

        /// <summary>
        /// 15分钟数据Influxdb measurement名称
        /// </summary>
        public const string QuarterMeasurementName = "archivedataquarter";

        private readonly InfluxDBConfig _config;

        private InfluxDBClient? _client;
        private readonly IServiceProvider _provider;

        private InfluxDBClient InfluxDBClient
        {
            get 
            {
                if (_client == null)
                {
                    _client = new InfluxDBClient(_config?.Url, _config?.UserName, _config?.Password);
                }
                return _client;
            }
        }

        /// <summary>
        /// 构造函数初始化
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="configuration"></param>
        public AssetInfluxHelper(ILogger<AssetInfluxHelper> logger, IConfiguration configuration, IServiceProvider provider)
        {
            _logger = logger;
            var config = configuration.GetSection(InfluxDBConfig.Influxdb).Get<InfluxDBConfig>();
            if (config == null)
            {
                throw new Exception("InfluxDB config not found.");
            }
            _config = config;
            _provider = provider;
        }

        public void WriterData(int assetId,
            string objectId,
            DateTime time,
            Dictionary<string, string> datas,
            string tableName = RealTimeMeasurementName)
        {
            PointData pointData = PointData.Measurement(tableName);
            pointData = pointData
            .Tag("assetid", assetId.ToString())
            .Tag("objectid", objectId)
            .Timestamp(time, WritePrecision.Ms);

            foreach (var item in datas)
            {
                // 转换不成功的数据不插入
                if (double.TryParse(item.Value, out var tempValue))
                {
                    pointData = pointData.Field(item.Key.ToLower(), tempValue);
                }
            }

            var refObj = _provider.GetRequiredService<IInfluxDBRef>();
            refObj.AppendData(pointData);
        }

        /// <summary>
        ///  获取对应图表的x,y轴值
        /// </summary>
        /// <param name="assetId"></param>
        /// <param name="udcCode"></param>
        /// <param name="timeInterval"></param>
        /// <param name="isbit"></param>
        /// <returns></returns>
        public async Task<List<ChartEntity>> GetDataAsync(int assetId, string? udcCode, string? timeInterval, bool isbit)
        {
            try
            {
                var queryApi = InfluxDBClient.GetQueryApi();

                var flux = $@"from(bucket: ""{_config?.Bucket}"")
                              |> range(start: -24h, stop: now())
                              |> filter(fn: (r) => r[""_measurement""] == ""{RealTimeMeasurementName}"")
                              |> filter(fn: (r) => r[""assetid""] == ""{assetId}"")
                              |> filter(fn: (r) => r[""_field""] == ""{udcCode!.ToLower()}"")
                              |> aggregateWindow(every: {timeInterval}, fn: mean, createEmpty: false)
                              |> sort(columns: [""_time""],desc: false)
                              |> drop(columns: [""objectid"",""_start"",""_stop"",""assetid"",""_measurement"",""_field""])
                              |> yield()";

                var data = await queryApi.QueryAsync(flux, _config?.OrgName);

                var chartDatas = new List<ChartEntity>();

                if (data.Any())
                {
                    foreach (var item in data)
                    {
                        foreach (var _item in item.Records)
                        {
                            var xval = _item.GetTimeInDateTime();
                            //var xval = _item.Values.TryGetValue("_time", out object? xval);
                            _item.Values.TryGetValue("_value", out object? yval);


                            chartDatas.Add(new ChartEntity()
                            {
                                Xval = xval.ToString(),
                                Yval = isbit ? StringFunction.TenToBit(Convert.ToInt32(Convert.ToInt32(yval?.ToString() ?? "0"))) : Convert.ToDouble(yval).ToString("0.000")
                            });
                        }
                    }
                }

                return chartDatas;

            }
            catch (Exception ex)
            {
                _logger.LogError("AssetInfluxHelper_GetDataAsync:" + ex.Message);
                _client = null;
            }

            return new List<ChartEntity>();
        }

        /// <summary>
        /// 获取二进制点位数据
        /// </summary>
        /// <param name="assetId"></param>
        /// <param name="udcCode"></param>
        /// <param name="timeInterval"></param>
        /// <returns></returns>
        public async Task<List<ChartEntity>> GetIsBitDataAsync(int assetId, string? udcCode, string? timeInterval)
        {
            try
            {
                var queryApi = InfluxDBClient.GetQueryApi();

                var flux = $@"from(bucket: ""{_config?.Bucket}"")
                              |> range(start: -24h, stop: now())
                              |> filter(fn: (r) => r[""_measurement""] == ""{RealTimeMeasurementName}"")
                              |> filter(fn: (r) => r[""assetid""] == ""{assetId}"")
                              |> filter(fn: (r) => r[""_field""] == ""{udcCode!.ToLower()}"")
                              |> sort(columns: [""_time""],desc: false)
                              |> drop(columns: [""objectid"",""_start"",""_stop"",""assetid"",""_measurement"",""_field""])
                              |> last()";

                var data = await queryApi.QueryAsync(flux, _config?.OrgName);

                var chartDatas = new List<ChartEntity>();

                if (data.Any())
                {
                    foreach (var item in data)
                    {
                        foreach (var _item in item.Records)
                        {
                            var xval = _item.GetTimeInDateTime();
                            if (xval == null) continue;

                            xval = xval.Value.ToLocalTime();
                            // _item.Values.TryGetValue("_time", out object? xval);
                            _item.Values.TryGetValue("_value", out object? yval);

                            string val = StringFunction.TenToBit(Convert.ToInt32(Convert.ToInt32(yval!.ToString() ?? "0")));

                            chartDatas.Add(new ChartEntity()
                            {
                                Xval = xval.Value.ToString("HH:mm:ss"),
                                Yval = val
                            });
                        }
                    }
                }

                return chartDatas;

            }
            catch (Exception ex)
            {
                _logger.LogError("AssetInfluxHelper_GetDataAsync:" + ex.Message);
                _client = null;
            }

            return new List<ChartEntity>();
        }

        /// <summary>
        /// 获取抄表图表数据
        /// </summary>
        /// <param name="propertyList"></param>
        /// <param name="assetIds"></param>
        /// <param name="time"></param>
        /// <returns></returns>
        public async Task<List<TabChartDto>> GetTabChartDataAsync(List<string> propertyList, List<int> assetIds, string time)
        {
            var chartDatas = new List<TabChartDto>();

            try
            {
                if (assetIds.Any() && propertyList.Any())
                {
                    var queryApi = InfluxDBClient.GetQueryApi();

                    string sTime = string.IsNullOrEmpty(time) ? DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd") + " 23:00:00"
                                                       : Convert.ToDateTime(time).AddDays(-1).ToString("yyyy-MM-dd") + " 23:00:00";

                    string eTime = string.IsNullOrEmpty(time) ? DateTime.Now.ToString("yyyy-MM-dd HH:00:00")
                                   : DateTime.Now.ToString("yyyy-MM-dd") == Convert.ToDateTime(time).ToString("yyyy-MM-dd")
                                   ? DateTime.Now.ToString("yyyy-MM-dd HH:00:00") : Convert.ToDateTime(time).ToString("yyyy-MM-dd") + " 23:00:00";

                    string startTime = Convert.ToDateTime(sTime).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ");

                    string endTime = Convert.ToDateTime(eTime).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ");

                    var propertyByList = UniversalDeviceInfo.Instance._dicByProperty.Keys.ToList();

                    propertyByList.AddRange(propertyList);

                    var (strAssetIds, strPropertys) = GetQueryParam(propertyByList, assetIds);

                    var flux = $@"from(bucket: ""{_config?.Bucket}"")
                              |> range(start: {startTime}, stop: {endTime})
                              |> filter(fn: (r) => r[""_measurement""] == ""{RealTimeMeasurementName}"")
                              |> filter(fn: (r) => {strAssetIds})
                              |> filter(fn: (r) => {strPropertys})
                              |> aggregateWindow(every: 1h, fn: last, createEmpty: false)
                              |> sort(columns: [""_time""],desc: false)
                              |> drop(columns: [""objectid"",""_start"",""_stop"",""_measurement""])
                              |> group(columns: [""assetid""])
                              |> yield()";

                    var data = await queryApi.QueryAsync(flux, _config?.OrgName);

                    if (data.Any())
                    {
                        foreach (var item in data)
                        { 
                            foreach (var _item in item.Records)
                            {
                                var dateTime = _item.GetTimeInDateTime();
                                if (!dateTime.HasValue) continue;
                                dateTime = dateTime.Value.ToLocalTime();
                                //_item.Values.TryGetValue("_time", out object? dateTime);
                                _item.Values.TryGetValue("_value", out object? value);
                                _item.Values.TryGetValue("_field", out object? field);
                                _item.Values.TryGetValue("assetid", out object? assetId);

                                string dateByTime = dateTime.Value.ToString("HH:mm:ss");

                                //舍弃时间不为整点的数据，一般是最后一条数据
                                if (dateByTime.Contains(":00"))
                                {
                                    UniversalDeviceInfo.Instance._dicByProperty.TryGetValue(field!.ToString()!, out string? _filed);

                                    chartDatas.Add(new TabChartDto
                                    {
                                        AssetId = Convert.ToInt32(assetId),
                                        Field = _filed ?? Convert.ToString(field),
                                        DateTime = dateByTime,
                                        Val = Convert.ToDecimal(Convert.ToDecimal(value).ToString("0.00"))
                                    });
                                }
                            }
                        }
                    }
                    else
                    {
                        foreach (var firstItem in assetIds)
                        {
                            foreach (var seconditem in propertyList)
                            {
                                for (int i = 0; i <= 23; i++)
                                {
                                    var dateTime = i >= 10 ? $"{i}:00" : $"0{i}:00";

                                    chartDatas.Add(new TabChartDto
                                    {
                                        AssetId = firstItem,
                                        Field = seconditem,
                                        DateTime = dateTime,
                                        Val = Convert.ToDecimal("0.00")
                                    });
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("AssetInfluxHelper_GetTabChartDataAsync:" + ex.Message);
                _client = null;
            }

            return chartDatas;
        }

        /// <summary>
        /// 获取抄表图表实时数据
        /// </summary>
        /// <param name="propertyList"></param>
        /// <param name="assetIds"></param>
        /// <returns></returns>
        public async Task<List<TabChartDto>> GetRealTabChartDataAsync(List<string> propertyList, List<int> assetIds)
        {
            var chartDatas = new List<TabChartDto>();

            try
            {
                if (assetIds.Any() && propertyList.Any())
                {
                    var queryApi = InfluxDBClient.GetQueryApi();

                    var propertyByList = UniversalDeviceInfo.Instance._dicByProperty.Keys.ToList();

                    propertyByList.AddRange(propertyList);

                    var (strAssetIds, strPropertys) = GetQueryParam(propertyByList, assetIds);

                    var flux = $@"from(bucket: ""{_config?.Bucket}"")
                              |> range(start: -1h, stop: now())
                              |> filter(fn: (r) => r[""_measurement""] == ""{RealTimeMeasurementName}"")
                              |> filter(fn: (r) => {strAssetIds})
                              |> filter(fn: (r) => {strPropertys})
                              |> sort(columns: [""_time""],desc: false)
                              |> drop(columns: [""objectid"",""_start"",""_stop"",""_measurement""])
                              |> last()";

                    var data = await queryApi.QueryAsync(flux, _config?.OrgName);

                    if (data.Any())
                    {
                        foreach (var item in data)
                        {
                            foreach (var _item in item.Records)
                            {
                                var dateTime = _item.GetTimeInDateTime();
                                if (!dateTime.HasValue) continue;
                                dateTime = dateTime.Value.ToLocalTime();
                                //_item.Values.TryGetValue("_time", out object? dateTime);
                                _item.Values.TryGetValue("_value", out object? value);
                                _item.Values.TryGetValue("_field", out object? field);
                                _item.Values.TryGetValue("assetid", out object? assetId);

                                UniversalDeviceInfo.Instance._dicByProperty.TryGetValue(field!.ToString()!,out string? _filed);

                                var proField = _filed ?? Convert.ToString(field);
                                var decValue = Convert.ToDecimal(value);

                                chartDatas.Add(new TabChartDto
                                {
                                    AssetId = Convert.ToInt32(assetId),
                                    Field = proField,
                                    DateTime = dateTime.Value.ToString("HH:mm:ss"),
                                    Val = Convert.ToDecimal(decValue.ToString("0.00"))
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("AssetInfluxHelper_GetRealTabChartDataAsync:" + ex.Message);
                _client = null;
            }

            return chartDatas;
        }

        /// <summary>
        /// 返回查询条件
        /// </summary>
        /// <param name="propertyList"></param>
        /// <param name="assetIds"></param>
        /// <returns></returns>
        private (string, string) GetQueryParam(List<string> propertyList, List<int> assetIds)
        {
            var strAssetIdList = new List<string>();

            var strPropertyList = new List<string>();

            foreach (var item in assetIds)
            {
                strAssetIdList.Add($@"r[""assetid""] == ""{item}""");
            }

            foreach (var item in propertyList)
            {
                strPropertyList.Add($@"r[""_field""] == ""{item.ToLower()}""");
            }

            return (string.Join(" or ", strAssetIdList), string.Join(" or ", strPropertyList));
        }
    }

    /// <summary>
    /// 图表的值
    /// </summary>
    public class ChartEntity
    {
        /// <summary>
        ///  x轴
        /// </summary>
        public string? Xval { get; set; }

        /// <summary>
        /// y轴
        /// </summary>
        public string? Yval { get; set; }
    }

    /// <summary>
    /// 抄表临时表
    /// </summary>
    public class TabChartDto
    {
        /// <summary>
        ///  资产id
        /// </summary>
        public int AssetId { get; set; }

        /// <summary>
        /// 属性
        /// </summary>
        public string? Field { get; set; }

        /// <summary>
        /// 时间点
        /// </summary>
        public string? DateTime { get; set; }

        /// <summary>
        /// 值
        /// </summary>
        public decimal Val { get; set; }
    }
}
