﻿using Newtonsoft.Json;
using Siemens.PanelManager.Model.Topology;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.UploadExcel.Diagram.TemplateModel.NodeTemplate
{
    /// <summary>
    /// 变压器节点
    /// </summary>
    internal class TransformerNode : NodeData
    {
        public override NodeType NodeType => NodeType.Transformer;
        public TransformerNode()
        {
            TypeCode = "E";
            CloseStyle = "electrical24";
            SizeHight = 110;
            SizeWidth = 110;
            Category = "transformerNodeCategory";
            IsTransformer = true;
        }

    }
}
