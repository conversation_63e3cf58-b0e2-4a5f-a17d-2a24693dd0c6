﻿using InfluxDB.Client.Core;
using Siemens.InfluxDB.Helper.FluxModel;
using Siemens.InfluxDB.Helper.Interface;
using System.Collections.ObjectModel;

namespace Siemens.InfluxDB.Helper.Database
{
    internal class DataHelper<T> : DataHelperBase
        where T : IInfluxData
    {
        #region Init
        public DataHelper(string bucket) 
        {
            From = new FromModel(bucket);
            DataType = typeof(T);
            Measurement = DataType.Name;
            var measurement = DataType.GetCustomAttributes(typeof(Measurement), false).FirstOrDefault();
            if (measurement != null)
            {
                Measurement = ((Measurement)measurement).Name;
            }
            var measurmentFilter = $"r[\"_measurement\"] == \"{Measurement}\"";
            Filter = new Filter(measurmentFilter);
            var properties = DataType.GetProperties();
            var tags = new Dictionary<string, string>();
            var fields = new Dictionary<string, string>();
            foreach(var pro in properties) 
            {
                var firstAttr = pro.GetCustomAttributes(typeof(Column), false).FirstOrDefault();
                if(firstAttr==null)continue;
                Column attr = (Column)firstAttr;
                if (attr == null) continue;

                if (attr.IsTimestamp)
                {
                    TimeColumn = pro.Name;
                }
                else if (attr.IsTag)
                {
                    tags.TryAdd(pro.Name, attr.Name ?? pro.Name);
                }
                else
                {
                    fields.TryAdd(pro.Name, attr.Name ?? pro.Name);
                }
            }
            Tags = new ReadOnlyDictionary<string, string>(tags);
            Fields = new ReadOnlyDictionary<string, string>(fields);
            Sort.Columns.Add("_time");
            Sort.IsDesc = true;
        }
        #endregion

        /// <summary>
        /// 1: 存在
        /// 0：不存在
        /// 2: 时间主键
        /// </summary>
        /// <param name="columnName"></param>
        /// <param name="isLeft"></param>
        /// <param name="flux"></param>
        /// <returns></returns>
        public override int GetColumnFilter(string columnName, bool isLeft, out string flux)
        {
            flux = string.Empty;
            if (TimeColumn.Equals(columnName))
            {
                return 2;
            }
            if (Tags.TryGetValue(columnName, out string? dbColumnName))
            {
                flux = $"r[\"{dbColumnName ?? columnName}\"]";
                return 1;
            }

            if (Fields.TryGetValue(columnName, out dbColumnName))
            {
                if (isLeft)
                {
                    flux = $"r[\"_field\"] == \"{dbColumnName ?? columnName}\" and r[\"_value\"]";
                }
                else
                {
                    flux = $"r[\"_value\"] and r[\"_field\"] == \"{dbColumnName ?? columnName}\"";
                }
                return 1;
            }
            return 0;
        }

        public string? GetColumnName(string columnName)
        {
            if (TimeColumn.Equals(columnName))
            {
                return "_time";
            }
            else if (Tags.TryGetValue(columnName, out string? dbName))
            {
                return dbName;
            }
            else if (Fields.TryGetValue(columnName, out dbName))
            {
                return dbName;
            }
            return null;
        }
    }
}
