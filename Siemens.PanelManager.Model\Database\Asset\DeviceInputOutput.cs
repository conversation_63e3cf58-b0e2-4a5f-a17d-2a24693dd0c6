﻿using SqlSugar;

namespace Siemens.PanelManager.Model.Database.Asset
{
    [SugarTable("asset_device_input_output_config")]
    public class DeviceInputOutput : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "asset_id", IsNullable = false)]
        public int AssetId { get; set; }
        [SugarColumn(ColumnName = "name", IsNullable = false, Length = 256)]
        public string Name { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "code", IsNullable = false, Length = 256)]
        public string Code { get; set; } = string.Empty;

        [SugarColumn(IsIgnore = true)]
        public string? Value { get; set; }
    }
}
