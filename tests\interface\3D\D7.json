{"info": {"_postman_id": "8c56d8f1-610a-41ec-bcb3-ed907c788576", "name": "D7使用超级管理员账号进入panel manager组态编辑中的3D图编辑页面，点击修改地面尺寸icon修改地面尺寸", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 10", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "查看3D图列表 Copy 3", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"展示所有3D图表\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"id\");\r", "    pm.expect(pm.response.text()).to.include(\"name\");\r", "    pm.expect(pm.response.text()).to.include(\"code\");\r", "    pm.expect(pm.response.text()).to.include(\"description\");\r", "    pm.expect(pm.response.text()).to.include(\"time\");\r", "    pm.expect(pm.response.text()).to.include(\"owner\");\r", "    pm.expect(pm.response.text()).to.include(\"dashboardFirst\");\r", "});\r", " let D1 = pm.response.json().data[0].id\r", " pm.environment.set(\"D1\",D1)"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v2/topoplogy/3D", "host": ["{{baseUrl}}"], "path": ["api", "v2", "topoplogy", "3D"]}}, "response": []}, {"name": "更新3D图 Copy 2", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n\t\"code\": \"e2fd831e-e60e-3e91-9a38-c8f1c67c9e2e\",\r\n\t\"description\": \"test01\",\r\n\t\"name\": \"new 4D file\",\r\n\t\"owner\": \"test01\",\r\n\t\"time\": \"2023-05-17T10:55:48.773933\",\r\n\t\"nodes\": [{\r\n\t\t\"type\": \"ground\",\r\n\t\t\"uuid\": \"9fb7bf84-a528-4b73-aca0-77a181dc2493\",\r\n\t\t\"size\": {\r\n\t\t\t\"width\": 25000,\r\n\t\t\t\"depth\": 25000\r\n\t\t},\r\n\t\t\"userData\": {\r\n\t\t\t\"type\": \"ground\",\r\n\t\t\t\"control\": {\r\n\t\t\t\t\"selectable\": false,\r\n\t\t\t\t\"draggable\": false,\r\n\t\t\t\t\"transparent\": false,\r\n\t\t\t\t\"showPanel\": false\r\n\t\t\t}\r\n\t\t}\r\n\t}, {\r\n\t\t\"type\": \"panel\",\r\n\t\t\"name\": \"box1\",\r\n\t\t\"uuid\": \"da4e6f5f-900b-4a4e-96d1-009a5f39ca7b\",\r\n\t\t\"position\": {\r\n\t\t\t\"x\": -2362,\r\n\t\t\t\"y\": 0,\r\n\t\t\t\"z\": 778\r\n\t\t},\r\n\t\t\"rotation\": {\r\n\t\t\t\"x\": 0,\r\n\t\t\t\"y\": 0,\r\n\t\t\t\"z\": 0\r\n\t\t},\r\n\t\t\"size\": {\r\n\t\t\t\"width\": 1000,\r\n\t\t\t\"height\": 1000,\r\n\t\t\t\"depth\": 1000\r\n\t\t},\r\n\t\t\"userData\": {\r\n\t\t\t\"type\": \"panel\",\r\n\t\t\t\"panelType\": \"Sivecon8PT\",\r\n\t\t\t\"assetId\": 1,\r\n\t\t\t\"circuitList\": [{\r\n\t\t\t\t\"assetId\": 0,\r\n\t\t\t\t\"assetName\": \"\",\r\n\t\t\t\t\"circuitName\": \"\",\r\n\t\t\t\t\"assetNumber\": \"C-11\",\r\n\t\t\t\t\"assetType\": \"Feeder\",\r\n\t\t\t\t\"assetStructure\": \"Other\",\r\n\t\t\t\t\"useScene\": \"Lighting\",\r\n\t\t\t\t\"description\": \"\",\r\n\t\t\t\t\"width\": \"1\",\r\n\t\t\t\t\"height\": 200\r\n\t\t\t}],\r\n\t\t\t\"control\": {\r\n\t\t\t\t\"selectable\": true,\r\n\t\t\t\t\"draggable\": true,\r\n\t\t\t\t\"transparent\": false,\r\n\t\t\t\t\"showPanel\": true,\r\n\t\t\t\t\"groupable\": true,\r\n\t\t\t\t\"showDetail\": true\r\n\t\t\t}\r\n\t\t}\r\n\t}], \r\n\t\"id\": {{D1}}\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v2/topoplogy/3D", "host": ["{{baseUrl}}"], "path": ["api", "v2", "topoplogy", "3D"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}]}