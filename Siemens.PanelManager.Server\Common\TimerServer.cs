﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Siemens.PanelManager.Server.Alarm;
using Timer = System.Timers.Timer;

namespace Siemens.PanelManager.Server.Common
{
    public class TimerServer : BackgroundService
    {
        private readonly IServiceProvider _provider;

        /// <summary>
        /// 定时器1
        /// </summary>
        private static Timer? _timer1;

        /// <summary>
        /// 定时器2
        /// </summary>
        private static Timer? _timer2;

        /// <summary>
        /// 初始化构造函数
        /// </summary>
        /// <param name="provider"></param>
        public TimerServer(IServiceProvider provider)
        {
            _provider = provider;
        }

        /// <summary>
        /// 初始化执行函数
        /// </summary>
        /// <param name="stoppingToken"></param>
        /// <returns></returns>
        protected override Task ExecuteAsync(CancellationToken stoppingToken)
        {
            StartTimer1();
            StartTimer2();
            return Task.CompletedTask;
        }

        /// <summary>
        /// 启动定时器1
        /// </summary>
        private void StartTimer1()
        {   
            _timer1 = new Timer(1000);
            _timer1.Elapsed += SetCacheBitConfigs;
            _timer1.AutoReset = true;
            _timer1.Enabled = true;
            _timer1.Start();
        }

        /// <summary>
        /// 启动定时器1
        /// </summary>
        private void StartTimer2()
        {
            //_timer2 = new Timer(1000);
            //_timer2.Elapsed += SetCacheAlarmByDeviceNum;
            //_timer2.AutoReset = true;
            //_timer2.Enabled = true;
            //_timer2.Start();
        }

        /// <summary>
        /// 定时设置资产的二级制位信息缓存
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        /// <exception cref="NotImplementedException"></exception>
        private void SetCacheBitConfigs(object? sender, System.Timers.ElapsedEventArgs e)
        {
            if (_timer1 != null)//如果是第一次执行
            {
                _timer1.Enabled = false;

                _timer1.Interval = 1000 * 60 * 100; //100 分钟将缓存重置。

                var alarmLogServer = _provider.GetRequiredService<AlarmLogServer>();

                if (alarmLogServer != null)
                {
                    // 定时设置缓存
                    alarmLogServer.SetCacheBitConfigs();
                }

                _timer1.Enabled = true;
            }
        }
    }
}
