import { Ix<PERSON><PERSON>on, Modal } from "@siemens/ix-react";
import { useRef } from "react";

export default function MessageModal(prop) {
  const modalRef = useRef();

  const okFunc = () => {
    modalRef.current?.dismiss();
  };
  return (
    <Modal ref={modalRef}>
      <div className="modal-header">{prop.title}</div>
      <div className="modal-body">{prop.message}</div>
      <div className="modal-footer">
        <IxButton onClick={() => okFunc()}>确认</IxButton>
      </div>
    </Modal>
  );
}
