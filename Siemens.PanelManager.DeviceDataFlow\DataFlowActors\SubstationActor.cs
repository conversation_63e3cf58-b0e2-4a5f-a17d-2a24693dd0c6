﻿using Akka.Actor;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Quartz.Util;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.Log;
using Siemens.PanelManager.DeviceDataFlow.MessageBus;
using Siemens.PanelManager.DeviceDataFlow.StaticData;
using Siemens.PanelManager.Model.Alarm;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.DataFlow;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.Server.Common;
using Siemens.PanelManager.Server.DataPoint;
using SqlSugar;
using System.Diagnostics;

namespace Siemens.PanelManager.DeviceDataFlow.DataFlowActors
{
    internal class SubstationActor : AssetActorBase
    {

        private readonly DataPointServer _dataPointServer;

        public SubstationActor(ILogger<SubstationActor> logger, IServiceProvider provider, AssetSimpleInfo simpleInfo, SiemensCache cache)
            : base(simpleInfo, provider, logger, cache)
        {
            _dataPointServer = provider.GetRequiredService<DataPointServer>();
            LoggerName = "SubstationActor";
        }

        protected override async Task ChangeDataFunc(AssetChangeData changeData)
        {
            if (changeData.ChangeDatas.TryGetValue("HaveAlarm", out _))
            {
                UpdateAlarmStatus();
                changeData.ChangeDatas.Remove("HaveAlarm");
            }
            if (changeData.ChangeDatas.Count == 0) return;

            await UpdateData(changeData.AssetId, changeData.ChangeDatas);
        }

        /// <summary>
        /// 更新数据
        /// </summary>
        /// <param name="assetId"></param>
        /// <param name="changeDatas"></param>
        /// <returns></returns>
        private async Task UpdateData(int assetId, Dictionary<string, string> changeDatas)
        {
            var logger = AkkaRuntimeLoggerManager.GetLogger(LoggerName);
            var k = logger?.Start();
            var count = CountManagerTools.GetCount($"SubstationActor-1-{AssetSimpleInfo.AssetId}");
            count.Count++;
            var data = new Dictionary<string, string>();
            // 给配电房上信息
            var assetUpwardServer = Provider.GetService<AssetUpwardServer>();

            var configs = await _dataPointServer.GetSubstationDataPointConfigDetailsByCache(AssetSimpleInfo.AssetId);
            var keys = changeDatas.Keys.ToList();

            if (configs.Any(c => keys.Contains(c.DataPointCode) && c.ShowType == LayoutFormat.Realtime))
            {
                var hitConfigs = configs.Where(c => keys.Contains(c.DataPointCode) && c.ShowType == LayoutFormat.Realtime).ToList();
                foreach (var hitConfig in hitConfigs)
                {
                    data.TryAdd(hitConfig.DataPointCode, changeDatas[hitConfig.DataPointCode]);
                }
            }

            // 配电房的属性数据
            if (assetUpwardServer != null)
            {
                var subStationInfo = await assetUpwardServer!.GetSubStationInfo(AssetSimpleInfo.AssetId);
                if (k.HasValue)
                {
                    logger?.Mark(k.Value, $"assetUpwardServer - [GetSubStationInfo] AssetId({AssetSimpleInfo.AssetId})");
                }
                if (subStationInfo != null)
                {
                    data.Add("Health_Status", subStationInfo.HealthStatus ?? "");
                    data.Add("SubStation_Loss", subStationInfo.SubStationLoss ?? "");
                    data.Add("SubStation_Percentage", subStationInfo.SubStationPercentage ?? "");
                    data.Add("SubStation_Safety_Scope", subStationInfo.SubStationSafetyScope ?? "");
                    data.Add("SubStation_Day_Efficiency", subStationInfo.SubStationDayEfficiency.ToString() ?? "");

                    var alarmServer = Provider.GetService<AlarmLogServer>();

                    // 告警的信息数量

                    if (!subStationInfo.AssetName.IsNullOrWhiteSpace())
                    {
                        int num = alarmServer!.GetAlarmNum(new AlarmInfoParam() { SubStationName = subStationInfo.AssetName, AssetId = AssetSimpleInfo.AssetId });
                        if (k.HasValue)
                        {
                            logger?.Mark(k.Value, $"assetUpwardServer - [GetAlarmNum] AssetId({AssetSimpleInfo.AssetId})");
                        }

                        if (data.ContainsKey("HaveAlarm"))
                        {
                            data["HaveAlarm"] = num > 0 ? "1" : "0";
                        }
                        else
                        {
                            data.Add("HaveAlarm", num > 0 ? "1" : "0");
                        }
                    }
                }
            }
            if (k.HasValue)
            {
                logger?.Mark(k.Value, $"assetUpwardServer AssetId({AssetSimpleInfo.AssetId})");
            }

            if (changeDatas.ContainsKey("P") || changeDatas.ContainsKey("Q")
                || changeDatas.ContainsKey("ForwardActivePower")
                || changeDatas.ContainsKey("ActiveEnergy"))
            {
                var count2 = CountManagerTools.GetCount($"SubstationActor-2-{AssetSimpleInfo.AssetId}");
                count2.Count++;
                var ids = await GetDeviceInfo(logger, k);

                if (ids.Any(a => a == assetId))
                {
                    var totalP = 0m;
                    var totalQ = 0m;
                    var totalActiveEnergy = 0m;
                    foreach (var id in ids)
                    {
                        var cacheKey = string.Format(Constant.AssetCurrentDataCacheKey, id);
                        var currently = Cache.GetHashData(cacheKey, new string[] { "P", "Q", "ForwardActivePower", "ActiveEnergy" });
                        if (currently.TryGetValue("P", out var devicePStr) && decimal.TryParse(devicePStr, out decimal deviceP))
                        {
                            totalP += deviceP;
                        }

                        if (currently.TryGetValue("Q", out var deviceQStr) && decimal.TryParse(deviceQStr, out decimal deviceQ))
                        {
                            totalQ += deviceQ;
                        }

                        if (currently.TryGetValue("ForwardActivePower", out var activeEnergyStr) && decimal.TryParse(activeEnergyStr, out decimal activeEnergy))
                        {
                            totalActiveEnergy += activeEnergy;
                        }
                        else if (currently.TryGetValue("ActiveEnergy", out activeEnergyStr) && decimal.TryParse(activeEnergyStr, out activeEnergy))
                        {
                            totalActiveEnergy += activeEnergy;
                        }
                    }

                    if (changeDatas.ContainsKey("P"))
                    {
                        data.Add("Total_P", totalP.ToString());
                    }

                    if (changeDatas.ContainsKey("Q"))
                    {
                        data.Add("Total_Q", totalQ.ToString());
                    }

                    if (changeDatas.ContainsKey("ForwardActivePower") || changeDatas.ContainsKey("ActiveEnergy"))
                    {
                        data.Add("Total_ActiveEnergy", totalActiveEnergy.ToString());
                    }
                }
            }

            if (data.Any())
            {
                var actorManager = ActorManager.GetActorManagerNoException();

                if (actorManager != null)
                {
                    var changeData = new AssetChangeData()
                    {
                        AssetId = AssetSimpleInfo.AssetId,
                        AssetLevel = AssetSimpleInfo.AssetLevel,
                        AssetModel = AssetSimpleInfo.AssetModel,
                        AssetName = AssetSimpleInfo.AssetName,
                        AssetType = AssetSimpleInfo.AssetType,
                        ChangeDatas = data,
                        ChangeTime = DateTime.Now,
                    };
                    actorManager.DataPointRef.Tell(changeData);
                }

                var substationCacheKey = string.Format(Constant.AssetCurrentDataCacheKey, assetId);
                Cache.SetHashData(substationCacheKey, new Dictionary<string, string>(data));
            }

        }

        /// <summary>
        /// 查询配电房内的关口计量的设备
        /// </summary>
        /// <returns></returns>
        private async Task<int[]> GetDeviceInfo(RunTimeLogger? logger, int? key)
        {
            var client = Provider.GetRequiredService<SqlSugarScope>();

            var children = await client.Queryable<AssetRelation>()
                .WithCache($"SubstationGateway-{AssetSimpleInfo.AssetId}", (int)TimeSpan.FromMinutes(1).TotalSeconds)
                .ToChildListAsync(ar => ar.ParentId, AssetSimpleInfo.AssetId);
            var assetIds = children.Select(c => c.ChildId);

            if (key.HasValue)
            {
                logger?.Mark(key.Value, $"SubstationGateway AssetId({AssetSimpleInfo.AssetId}) Count[{assetIds.Count()}]");
            }

            var gatewayAssetIds = Cache.GetOrCreate<int[]>($"GatewayAssetIds", () =>
            {
                var gateway = MeasurementType.Gateway.ToString();

                var assetIds = client.Queryable<AssetInfo>()
                     .Where(a => a.AssetLevel == AssetLevel.Device && a.MeterType == gateway)
                     .Select(a => a.Id)
                     .ToArray();
                return assetIds;
            }, TimeSpan.FromMinutes(5));

            var filterIds = gatewayAssetIds.Where(id => assetIds.Contains(id)).ToArray();

            if (key.HasValue)
            {
                logger?.Mark(key.Value, $"GatewayAssetIds AssetId({AssetSimpleInfo.AssetId}) Count[{filterIds.Length}]");
            }

            return filterIds;
        }

        protected override async Task InputDataFunc(AssetInputData inputData)
        {
            var count = CountManagerTools.GetCount($"SubstationActor-{AssetSimpleInfo.AssetId}");
            count.Count++;
            if (inputData.Datas.TryGetValue("HaveAlarm", out _))
            {
                UpdateAlarmStatus();
                inputData.Datas.Remove("HaveAlarm");
            }
            if (inputData.Datas.Count == 0) return;

            await UpdateData(inputData.ParentId ?? inputData.AssetId ?? 0, inputData.Datas);
        }

        private void UpdateAlarmStatus()
        {
            var alarmLogService = Provider.GetRequiredService<AlarmLogServer>();

            var countModel = alarmLogService.GetAlarmCount(PanelManager.Model.Database.Asset.AssetLevel.Substation, AssetSimpleInfo.AssetName);
            if (countModel != null)
            {
                var cacheKey = string.Format(Constant.AssetCurrentDataCacheKey, AssetSimpleInfo.AssetId);
                Cache.SetHashData(cacheKey, new Dictionary<string, string>
                {
                    ["HaveAlarm"] = (countModel.HighCount + countModel.MiddleCount + countModel.LowCount) > 0 ? "1" : "0",
                    ["HighCount"] = countModel.HighCount.ToString(),
                    ["MiddleCount"] = countModel.MiddleCount.ToString(),
                    ["LowCount"] = countModel.LowCount.ToString(),
                });
            }
        }
    }
}
