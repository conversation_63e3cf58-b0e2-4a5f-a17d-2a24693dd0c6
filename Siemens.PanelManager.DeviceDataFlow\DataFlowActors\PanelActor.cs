﻿using Akka.Actor;
using Akka.Util.Internal;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.DeviceDataFlow.Model;
using Siemens.PanelManager.DeviceDataFlow.StaticData;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.Alarm;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.DataFlow;
using Siemens.PanelManager.Server.Alarm;
using Siemens.PanelManager.Server.Common;
using Siemens.PanelManager.Server.DataPoint;

namespace Siemens.PanelManager.DeviceDataFlow.DataFlowActors
{
    internal class PanelActor : AssetActorBase
    {
        private readonly DataPointServer _service;
        public PanelActor(ILogger<PanelActor> logger, IServiceProvider provider, AssetSimpleInfo simpleInfo,SiemensCache cache, DataPointServer server)
            : base(simpleInfo, provider, logger, cache)
        {
            _service = server;
        }

        protected override async Task ChangeDataFunc(AssetChangeData changeData)
        {
            if (changeData.AssetId != AssetSimpleInfo.AssetId) return;

            if (changeData.ChangeDatas.TryGetValue("HaveAlarm", out _))
            {
                UpdateAlarmStatus();
                changeData.ChangeDatas.Remove("HaveAlarm");
            }
            if (changeData.ChangeDatas.Count == 0) return;

            await SaveFunc(changeData.ChangeDatas, changeData.ChangeTime, true);
        }

        protected override async Task InputDataFunc(AssetInputData inputData)
        {
            if (inputData.AssetId != AssetSimpleInfo.AssetId && inputData.ParentId != AssetSimpleInfo.AssetId) return;
            if (inputData.Datas.TryGetValue("HaveAlarm", out _))
            {
                UpdateAlarmStatus();
                inputData.Datas.Remove("HaveAlarm");
            }
            if (inputData.Datas.Count == 0) return;

            await SaveFunc(inputData.Datas, inputData.InputTime, false);
        }

        /// <summary>
        /// 将部分数据进行数据存储
        /// </summary>
        /// <param name="originalDatas"></param>
        /// <param name="time"></param>
        /// <returns></returns>
        private async Task SaveFunc(Dictionary<string, string> originalDatas, DateTime time, bool isChangeData)
        {
            var cacheKey = string.Format(Constant.AssetCurrentDataCacheKey, AssetSimpleInfo.AssetId);
            var hasChanged = false;
            var changeData = new AssetChangeData()
            {
                AssetId = AssetSimpleInfo.AssetId,
                AssetLevel = AssetSimpleInfo.AssetLevel,
                AssetName = AssetSimpleInfo.AssetName,
                AssetModel = AssetSimpleInfo.AssetModel,
                AssetType = AssetSimpleInfo.AssetType,
                ChangeTime = time,
            };
            var needSaveDatas = new Dictionary<string, Dictionary<string, string>>();
            var datas = await ClearDataPoint(originalDatas, needSaveDatas);

            var currentStatus = Cache.GetHashData(cacheKey, datas.Keys.ToArray());
            foreach (var data in datas)
            {
                if (currentStatus.ContainsKey(data.Key))
                {
                    if (currentStatus[data.Key] != data.Value)
                    {
                        hasChanged = true;
                        changeData.ChangeDatas.Add(data.Key, data.Value);
                    }
                }
                else
                {
                    hasChanged = true;
                    changeData.ChangeDatas.Add(data.Key, data.Value);
                }
            }

            var alarmServer = Provider.GetService<AlarmLogServer>();

            //配电柜告警信息
            //if (alarmServer != null)
            //{
            //    var alarmInfo = alarmServer!.GetAlarmInfo(new AlarmInfoParam() { PanelName = changeData.AssetName, AssetId = changeData.AssetId });

            //    if (alarmInfo != null)
            //    {
            //        changeData.ChangeDatas.Add("Alarm_Severity", alarmInfo.AlarmSeverity.ToString());
            //        changeData.ChangeDatas.Add("Alarm_Status", alarmInfo.AlarmStatus.ToString());
            //        changeData.ChangeDatas.Add("Alarm_Path", alarmInfo.AlarmPath ?? "");
            //        changeData.ChangeDatas.Add("Alarm_Info", alarmInfo.AlarmInfo ?? "");
            //        changeData.ChangeDatas.Add("Alarm_Time", alarmInfo.AlarmTime ?? "");
            //    }

            //    int num = alarmServer!.GetAlarmNum(new AlarmInfoParam() { PanelName = changeData.AssetName,AssetId= changeData.AssetId});

            //    if (changeData.ChangeDatas.ContainsKey("HaveAlarm")) {

            //        changeData.ChangeDatas["HaveAlarm"] = num > 0 ? "1" : "0";
            //    }
            //    else
            //    {
            //        changeData.ChangeDatas.Add("HaveAlarm", num > 0 ? "1" : "0");
            //    }
            //}

            var assetUpwardServer = Provider.GetService<AssetUpwardServer>();

            // 配电房的属性数据
            if (assetUpwardServer != null)
            {
                var cabinetHealthInfo = await assetUpwardServer!.GetCabinetHealthInfo(changeData.AssetId);

                if (cabinetHealthInfo != null)
                {
                    changeData.ChangeDatas.Add("HealthScore", cabinetHealthInfo.HealthScore.ToString());
                    changeData.ChangeDatas.Add("Health_Grade", cabinetHealthInfo.HealthGrade ?? "");
                    changeData.ChangeDatas.Add("Abnormal_Count", cabinetHealthInfo.AbnormalCount.ToString());
                    changeData.ChangeDatas.Add("Alarm_Status_Count", cabinetHealthInfo.AlarmStatusCount.ToString());
                    changeData.ChangeDatas.Add("Max_Temperature", cabinetHealthInfo.MaxTemperature.ToString());
                    changeData.ChangeDatas.Add("Max_Temperature_Status", cabinetHealthInfo.MaxTemperatureStatus ?? "");
                    changeData.ChangeDatas.Add("Max_Electricity", cabinetHealthInfo.MaxElectricity.ToString());
                    changeData.ChangeDatas.Add("Max_Electricity_Status", cabinetHealthInfo.MaxElectricityStatus ?? "");
                    changeData.ChangeDatas.Add("Remaining_Life_Percentage", cabinetHealthInfo.RemainingLifePercentage.ToString());
                    changeData.ChangeDatas.Add("Remaining_Life_Status", cabinetHealthInfo.RemainingLifeStatus ?? "");
                    changeData.ChangeDatas.Add("Panel_Custom_Indicators", cabinetHealthInfo.PanelCustomIndicators ?? "");
                }
            }
            Cache.SetHashData(cacheKey, new Dictionary<string, string>(changeData.ChangeDatas));
            if (hasChanged)
            {
                var actorManager = ActorManager.GetActorManagerNoException();

                if (actorManager != null)
                {
                    actorManager.DataPointRef.Tell(changeData);
                }

                if (needSaveDatas.Count >= 0)
                {
                    foreach (var kv in needSaveDatas)
                    {
                        ActorManager.GetActorManager().AssetStatusSaveRef.Tell(new AssetSaveParam
                        {
                            AssetId = AssetSimpleInfo.AssetId,
                            ObjectId = AssetSimpleInfo.ObjectId ?? string.Empty,
                            Time = changeData.ChangeTime,
                            Datas = kv.Value,
                            TableName = kv.Key
                        });
                    }
                }

                if (AssetSimpleInfo.SubstationSimpleInfo != null)
                {
                    var oroxyRef = Provider.GetRequiredService<IAssetDataProxyRef>();

                    var panelInputData = new AssetInputData()
                    {
                        ParentId = AssetSimpleInfo.SubstationSimpleInfo.AssetId,
                        AssetId = AssetSimpleInfo.AssetId,
                        Datas = changeData.ChangeDatas,
                        InputTime = changeData.ChangeTime
                    };

                    oroxyRef.InputData(panelInputData);
                }
            }
        }

        /// <summary>
        /// 清除非开关柜的数据点位
        /// </summary>
        /// <param name="datas"></param>
        /// <param name="needSaveDatas"></param>
        /// <returns></returns>
        private async Task<Dictionary<string, string>> ClearDataPoint(Dictionary<string, string> datas, 
            Dictionary<string, Dictionary<string, string>> needSaveDatas)
        {
            var dataPoints = await _service.GetDataPointInfos(AssetSimpleInfo.AssetLevel, AssetSimpleInfo.AssetType, AssetSimpleInfo.AssetModel);
            var result = new Dictionary<string, string>();
            foreach (var kv in datas)
            {
                var dataPoint = dataPoints.FirstOrDefault(d => d.Code == kv.Key);
                if (dataPoint != null)
                {
                    if (!string.IsNullOrEmpty(dataPoint.SaveToTable))
                    {
                        if (!needSaveDatas.TryGetValue(dataPoint.SaveToTable, out var needSaves))
                        {
                            needSaves = new Dictionary<string, string>();
                            needSaveDatas.TryAdd(dataPoint.SaveToTable, needSaves);
                        }

                        needSaves.AddOrSet(kv.Key, kv.Value);
                    }

                    result.Add(kv.Key, kv.Value);
                }
            }
            return result;
        }

        private void UpdateAlarmStatus()
        {
            var alarmLogService = Provider.GetRequiredService<AlarmLogServer>();

            var countModel = alarmLogService.GetAlarmCount(PanelManager.Model.Database.Asset.AssetLevel.Panel, AssetSimpleInfo.AssetName);
            if (countModel != null)
            {
                var cacheKey = string.Format(Constant.AssetCurrentDataCacheKey, AssetSimpleInfo.AssetId);
                Cache.SetHashData(cacheKey, new Dictionary<string, string>
                {
                    ["HaveAlarm"] = (countModel.HighCount + countModel.MiddleCount + countModel.LowCount) > 0 ? "1" : "0",
                    ["HighCount"] = countModel.HighCount.ToString(),
                    ["MiddleCount"] = countModel.MiddleCount.ToString(),
                    ["LowCount"] = countModel.LowCount.ToString(),
                });
            }
        }
    }
}
