﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics.Contracts;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Tools.HttpService.Model
{
    internal class ResponseUpgrateStatus
    {
        [JsonProperty(PropertyName = "status")]
        public int Status { get; set; }
        [JsonProperty(PropertyName = "currentStatus")]
        public IReadOnlyDictionary<int, string> CurrentStatus { get; set; } = new Dictionary<int, string>();
    }
}
