﻿using CsvHelper.Configuration.Attributes;
using Newtonsoft.Json;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.WorkOrder
{
    [SugarTable("work_order_info")]
    public class WorkOrderInfo : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [SugarColumn(ColumnName = "work_order_code", IsNullable = false, Length = 256)]
        public string WorkOrderCode { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "work_order_name", IsNullable = true, Length = 256)]
        public string WorkOrderName { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "asset_type", IsNullable = true, Length = 256)]
        public string AssetType { get; set; } = string.Empty;

        [Uniqueness]
        [SugarColumn(ColumnName = "device", IsNullable = false, Length = 256)]
        public string Device { get; set; } = string.Empty;

        [Uniqueness]
        [SugarColumn(ColumnName = "work_order_type", IsNullable = false, Length = 256)]
        public string WorkOrderType { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "processing_time", IsNullable = false)]
        public DateTime ProcessingTime { get; set; }

        [SugarColumn(ColumnName = "processing_end_time", IsNullable = true)]
        public DateTime ProcessingEndTime { get; set; }

        [SugarColumn(ColumnName = "status", IsNullable = false, Length = 256)]
        public string Status { get; set; } = string.Empty;

        [SugarColumn(IsIgnore = true)]
        public string? DeviceName { get; set; }

        [SugarColumn(IsIgnore = true)]
        public List<WorkOrderContent>? Contents { get; set; }
    }
}
