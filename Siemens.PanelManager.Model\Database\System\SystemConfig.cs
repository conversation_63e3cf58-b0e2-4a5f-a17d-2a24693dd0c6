﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.System
{
    [SugarTable("sys_config")]
    public class SystemConfig : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [SugarColumn(ColumnName = "config_type", IsNullable = true, Length = 256)]
        [Uniqueness]
        public string Type { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "config_name", IsNullable = true, Length = 256)]
        [Uniqueness]
        public string Name { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "config_value", IsNullable = true, ColumnDataType = "varchar(10240)")]
        public string Value { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "config_extend", IsNullable = true, Length = 256)]
        public string? Extend { get; set; }

        [SugarColumn(ColumnName = "sort", IsNullable = false)]
        public int Sort { get; set; }
    }
}
