﻿using Microsoft.AspNetCore.Mvc;
using Siemens.PanelManager.Model.Database.WorkOrder;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class SnapshotTableQueryParam
    {
        /// <summary>
        /// 快照或者报告（report）id，默认可以不传入参数
        /// </summary>
        [FromQuery(Name = "reportId")]
        public string? ReportId { get; set; }

        [FromQuery(Name = "page")]
        public int Page { get; set; } = 1;

        [FromQuery(Name = "pageSize")]
        public int PageSize { get; set; } = 5;

        /// <summary>
        /// 表格数据类型，1: Order; 2: Panel; 3: Breaker
        /// </summary>
        [FromQuery(Name = "eventType")]
        public SnapshotReportType ReportType { get; set; }
    }
}
