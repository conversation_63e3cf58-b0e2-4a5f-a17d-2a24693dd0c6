﻿using Quartz;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Common.Job
{
    internal class RunOnceJobInfo
    {
        public RunOnceJobInfo(JobKey jobKey) 
        {
            Key = jobKey;
            var match = Regex.Match(jobKey.Name, "-([\\w]+)$");
            if (match.Success)
            {
                Name = match.Groups[1].Value;
            }
        }

        public RunOnceJobInfo(string name)
        {
            Name = name;
        }

        public RunOnceJobInfo(<PERSON>Key jobKey, IReadOnlyDictionary<string, string> parameters)
            :this(jobKey)
        {
            Parameters = parameters;
        }

        public RunOnceJobInfo(string name, IReadOnlyDictionary<string, string> parameters)
            : this(name)
        {
            Parameters = parameters;
        }

        public JobKey? Key { get; private set; }
        public string Name { get; private set; } = string.Empty;
        public IReadOnlyDictionary<string, string>? Parameters { get; private set; }
    }
}
