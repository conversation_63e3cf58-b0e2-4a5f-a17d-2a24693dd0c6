﻿using Newtonsoft.Json;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.WorkOrder
{
    [SugarTable("work_order_content")]
    public class WorkOrderContent : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [SugarColumn(ColumnName = "work_order_id", IsNullable = true)]
        public int? WorkOrderId { get; set; }

        [SugarColumn(ColumnName = "reserve_id", IsNullable = true)]
        public int? ReserveId { get; set; }

        [SugarColumn(ColumnName = "tag", IsNullable = true, Length = 1024)]
        public string? Tag { get; set; }

        [SugarColumn(ColumnName = "content", IsNullable = true, Length = 1024)]
        public string? Content { get; set; }

        [SugarColumn(ColumnName = "measure", IsNullable = true, Length = 1024)]
        public string? Measure { get; set; }

        [SugarColumn(ColumnName = "add_time", IsNullable = false)]
        public DateTime AddTime { get; set; }

        [SugarColumn(ColumnName = "attachment_ids", IsNullable = true, Length = 256)]
        public string? AttachmentIdsStr { get; set; }

        [SugarColumn(IsIgnore = true)]
        public int[]? AttachmentIds
        {
            get
            {
                if (string.IsNullOrEmpty(AttachmentIdsStr)) return null;
                return JsonConvert.DeserializeObject<int[]>(AttachmentIdsStr);
            }
            set
            {
                if (value == null)
                {
                    AttachmentIdsStr = null;
                    return;
                }
                AttachmentIdsStr = JsonConvert.SerializeObject(value);
            }
        }
    }
}
