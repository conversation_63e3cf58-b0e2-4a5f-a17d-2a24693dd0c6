﻿using Siemens.PanelManager.Common.Log;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.DeviceDataFlow.StaticData
{
    static class AkkaRuntimeLoggerManager
    {
        private static ConcurrentDictionary<string, RunTimeLogger> _loggerManager = new ConcurrentDictionary<string, RunTimeLogger>();
        static AkkaRuntimeLoggerManager()
        {
            _loggerManager.TryAdd("DeviceActor", new RunTimeLogger("DeviceActor", 30));
            _loggerManager.TryAdd("MessageBusActor", new RunTimeLogger("MessageBusActor"));
            _loggerManager.TryAdd("TopologyActor", new RunTimeLogger("TopologyActor"));
            _loggerManager.TryAdd("CircuitActor", new RunTimeLogger("CircuitActor"));
            _loggerManager.TryAdd("SubstationActor", new RunTimeLogger("SubstationActor"));
            _loggerManager.TryAdd("AlarmActor", new RunTimeLogger("AlarmActor"));
        }

        public static RunTimeLogger? GetLogger(string name)
        {
            if (_loggerManager.TryGetValue(name, out var logger))
            {
                return logger;
            }
            return null;
        }
    }
}
