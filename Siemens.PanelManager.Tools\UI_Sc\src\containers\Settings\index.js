import {
  IxButton,
  IxTabItem,
  IxTabs,
  showModal,
  showToast,
} from "@siemens/ix-react";
import React, { useState, useRef } from "react";
import { getSettings, updateSettings } from "../../api/settingsApi";
import { useNavigate } from "react-router-dom";
import { LoadingModal } from "../../components";

export default function SettingUI() {
  const navigate = useNavigate();
  const [selectedTab, setSelectedTab] = useState(0);
  const [settingData, setSettings] = useState(null);
  const [validationObj, setValidation] = useState(null);
  if (settingData === null) {
    getSettings(setSettings);
  }
  let [uiData, setUiData] = useState(null);
  if (uiData === null) {
    uiData = settingData;
  }
  const formRef = useRef();
  const handSubmit = () => {
    var tempSettingObj = {};
    let ip = uiData.ip;
    if (ip === "") {
      tempSettingObj.ip = "is-invalid";
    } else if (!isValidIP(ip)) {
      tempSettingObj.ip = "is-invalid";
    } else {
      updateSettings({ ip: ip }, (resp) => {
        showToast({
          message: "保存成功",
        });
        showModal({
          content: (
            <LoadingModal
              title="等待连接服务器"
              finishFuction={() => {
                let isConnected = sessionStorage.getItem("isConnected");
                return isConnected === "1";
              }}
            />
          ),
        }).then((m) => {
          m.onClose.listeners[0] = (r) => {
            if (r) {
              navigate(0);
            }
          };
        });
      });
    }
    setValidation(tempSettingObj);
  };

  const changeTab = (tabId) => setSelectedTab(tabId);

  return (
    <div
      style={{
        display: "block",
        position: "relative",
        width: "100%",
      }}
    >
      <IxTabs selected={selectedTab}>
        <IxTabItem onClick={() => changeTab(0)}>网络连接配置</IxTabItem>
      </IxTabs>
      {selectedTab === 0 ? (
        <div>
          <form
            className={"settings-table needs-validation"}
            noValidate
            ref={formRef}
            onSubmit={handSubmit}
          >
            <div className="settings-row" style={{ height: "10px" }}></div>

            <div className="settings-row">
              <scan className="text-default-title">服务器地址</scan>
              <input
                type="text"
                className={`form-control ${
                  validationObj?.ip ? "is-invalid" : ""
                }`}
                aria-describedby="inputGroupPrepend"
                id=""
                style={{ width: "200px" }}
                value={uiData?.ip}
                onChange={(input) => {
                  setUiData({ ip: input.target.value });
                }}
              ></input>
              <div className="invalid-feedback">IP 格式不正确</div>
            </div>
            <div className="settings-row" style={{ height: "10px" }}></div>
            <div className="settings-row">
              <IxButton type="submit">保存</IxButton>
            </div>
          </form>
        </div>
      ) : null}
    </div>
  );
}

function isValidIP(ip) {
  var reg =
    /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
  return reg.test(ip);
}
