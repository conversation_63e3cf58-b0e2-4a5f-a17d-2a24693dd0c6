# Panel Manager


## Getting started

To make it easy for you to get started with GitLab, here's a list of recommended next steps.

Already a pro? Just edit this README.md and make it your own. Want to make it easy? [Use the template at the bottom](#editing-this-readme)!

## Essentials

- Visual Studio 2022 17.4.1, C# 10
  - .NET Desktop Development (Individual components - .NET Framework 4.7.2 SDK)
  - ASP.NET and Web Development
  - Node.js Development Environment
  - Cross-Platform Development with .NET Core
- .NET Core SDK 6.0<br />
  https://dotnet.microsoft.com/download/dotnet/6.0

## Source code control
See https://code.siemens.com/si-lp-cea-rd-plm-project/edge/panel-manager/panel-manager-standard-product/server/panel-server/-/tree/dev.

Briefly:
- Start `git bash`
- Create ssh key `ssh-keygen`
- Copy content of `id_rsa.pub` to your profile settings on `code.siemens.com`

## How to run

dotnet Siemens.PanelManager.WebApi.dll

## Dependence

- SqlSugarCore 5.1.3.32
- Quartz 3.5.0
- MiniExcel 1.29.0
- log4net 2.0.15
- Akka 1.5.1
- Microsoft.Extensions.Caching.Memory 7.0.0
- Newtonsoft.Json 13.0.1
- Microsoft.AspNetCore.Authentication.JwtBearer 6.0.11
- Swashbuckle.AspNetCore 6.4.0
- Swashbuckle.AspNetCore.Annotations 6.4.0
- MQTTnet 4.2.1.781
- TouchSocket 1.3.0
- InfluxDB.Client 4.1.1.0
- CliWrap 3.6.0

## Code Cover Tools

AltCover & ReportGenerator

### How to run code cover

Step:
- dotnet publish into the binaries folder interferes with your downstream process
- altcover -o </NewDllPath/> -i . -r </ReportPath/>/coverage.xml
- altcover runner -x </NewDllPath/>/Siemens.PanelManager.WebApi.exe -o </ReportPath/> -r </NewDllPath/>
- run api test
- close Siemens.PanelManager.WebApi
- altcover runner --collect -r </NewDllPath/>
- ReportGenerator.exe -reports:</ReportPath/>/coverage.xml -targetdir:</ReportOutput/>

## TODO List

- Unit tests
- Api permission
- Language packs missing