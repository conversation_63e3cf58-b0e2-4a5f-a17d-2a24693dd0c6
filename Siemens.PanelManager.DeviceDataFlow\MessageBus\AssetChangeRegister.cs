﻿using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.DataFlow;
using System.Collections.Concurrent;

namespace Siemens.PanelManager.DeviceDataFlow.MessageBus
{
    public class AssetChangeRegister : IAssetChangeRegister
    {
        private ConcurrentDictionary<string, Action<AssetInfoOptionParam>> _registerInfo = new ConcurrentDictionary<string, Action<AssetInfoOptionParam>>();
        public void OnAssetChanged(string serviceName, Action<AssetInfoOptionParam> action)
        {
            Action<AssetInfoOptionParam> localFunc = (p) =>
            {
                try { action(p); }
                catch { }
            };
            _registerInfo.AddOrUpdate(serviceName, (k) => localFunc, (k, old) => localFunc);
        }

        public void RemoveRegister(string serviceName) 
        {
            _registerInfo.TryRemove(serviceName, out _);
        }

        public void InputAsset(AssetInfoOptionParam param) 
        {
            var actions = _registerInfo.Values;
            Task.Factory.StartNew(() =>
            {
                foreach (var a in actions)
                {
                    a(param);
                }
            });
        }
    }
}
