{"name": "sysloss_hour", "status": "active", "flux": "import \"date\"\n\noption task = {\n    name: \"sysloss_hour\",\n    every: 1h,\n}\n\nfrom(bucket: \"panel\")\n    |> range(start: -1h, stop: 0h)\n    |> filter(fn: (r) => r[\"_measurement\"] == \"sys_loss\")\n    |> sum()\n    |> map(fn: (r) => ({r with _measurement: \"sysloss_hour\", _time: date.truncate(t: -1h, unit: 1h)}))\n    |> pivot(rowKey: [\"_time\"], columnKey: [\"_field\"], valueColumn: \"_value\")\n    |> to(\n        bucket: \"panel\",\n        tagColumns: [\"topology_id\"],\n        fieldFn: (r) => ({\n            \"sum_fc\": r.line_loss_fc,\n            \"sum_breakerloss\": r.breaker_loss,\n            \"sum_powersource\": r.power_source,\n            \"sum_powerloss\": r.power_loss,\n            \"sum_powerload\": r.power_load,\n            \"sum_energy_loss_base_ref\": r.energy_loss_base_ref,\n            \"sum_pred_lower\": r.loss_pred_lower,\n            \"sum_pred_upper\": r.loss_pred_upper,\n            \"sum_breaker_loss_fee\": r.breaker_loss_fee,\n            \"sum_breaker_loss_fc_fee\": r.breaker_loss_fc_fee,\n            \"sum_pred_upper_fee\": r.loss_pred_upper_fee,\n            \"sum_energy_loss_base_ref_fee\": r.energy_loss_base_ref_fee,\n            \"sum_energy_loss_fee\": r.energy_loss_fee,\n        }),\n    )", "every": "1h"}