﻿using System.Runtime.CompilerServices;

namespace Siemens.PanelManager.Common
{
    public static class TimeFunction
    {
        private static readonly DateTime UinxTime = new DateTime(1970, 1, 1, 0, 0, 0);
        public static long GetTimestampForSec(this DateTime time)
        {
            return (long)(time.ToUniversalTime() - UinxTime).TotalSeconds;
        }

        public static long GetTimestampForMS(this DateTime time)
        {
            return (long)(time.ToUniversalTime() - UinxTime).TotalMilliseconds;
        }

        public static DateTime GetDateTimeBySec(this long sec)
        {
            return UinxTime.AddSeconds(sec).ToLocalTime();
        }

        public static DateTime GetDateTimeByMS(this long ms)
        {
            return UinxTime.AddMilliseconds(ms).ToLocalTime();
        }
        public static DateTime GetUinxTime()
        {
            return new DateTime(UinxTime.Ticks);
        }   
    }
}
