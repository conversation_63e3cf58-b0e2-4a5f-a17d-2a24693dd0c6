﻿using Microsoft.Extensions.Logging;
using Quartz;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.SyncService.Sqlite;


namespace Siemens.PanelManager.Job.SyncJob
{
    [DisallowConcurrentExecution]
    public class UdcSqliteSyncArchiveDataJob : JobBase
    {
        public override string Name => "UdcSqliteSyncArchiveDataJob";

        private ILogger<UdcSqliteSyncArchiveDataJob> _logger;

        private readonly IEnumerable<ISyncArchiveDataHandle> _syncHandles;

        /// <summary>
        /// 构造函数初始化
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="syncHandles"></param>
        public UdcSqliteSyncArchiveDataJob(ILogger<UdcSqliteSyncArchiveDataJob> logger, IEnumerable<ISyncArchiveDataHandle> syncHandles)
        {
            _logger = logger;
            _syncHandles = syncHandles;
        }

        public override async Task Execute()
        {
            foreach (var syncType in _syncHandles)
            {
                try
                {
                    await syncType.SyncWorkAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError($"UdcSqliteSyncArchiveDataJob_Execute", ex.Message);
                }
            }
        }
    }
}
