﻿namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class AcbStatusOverview
    {
        public AcbUnitOverview Overview { get; set; }
        public double ResistanceA { get; set; }
        public double ResistanceB { get; set; }
        public double ResistanceC { get; set; }

        public List<AcbUnitDeviceInfo> Devices { get; set; } = new List<AcbUnitDeviceInfo>();

    }

    public enum AcbUnitOverview
    {
        Excellent = 0,
        Good,
        Medium,
        Bad
    }

    public enum AcbUnitDeviceType
    {
        Up = 0,
        Down,
    }

    public class AcbUnitDeviceInfo
    {
        public AcbUnitDeviceType AcbUnitDeviceType { get; set; }

        public int Id { get; set; }

        public string? Name { get; set; }

        public string? Model { get; set; }

        public string? Type { get; set; }

        public string? Location { get; set; }
    }
}
