﻿using Siemens.PanelManager.Model.Database.Asset;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.UploadExcel
{
    internal class TransformerModel : IPanelModel
    {
        public TransformerModel(AssetInfo assetInfo, int index)
        {
            AssetInfo = assetInfo;
            Index = index;
        }
        public int Index { get; private set; }
        public AssetInfo AssetInfo { get; set; }
        public int Height { get; set; } = 2200;
        public int Width { get; set; } = 800;
        public int LineNo { get; set; } = 1;
        public int RowNo { get; set; } = 1;
        public string? BusbarStructure { get; set; }
        public AssetInfo? ParentAsset { get; set; }
        //连接的配电柜名称
        public string RelationPanelName { get;set; } = string.Empty;

        public string[] BusBars
        {
            get
            {
                if (string.IsNullOrEmpty(BusBarStr)) return new string[0];
                return BusBarStr.Split(',');
            }
        }
    }
     
}
