﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Model.Chart;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.DataPoint;
using Siemens.PanelManager.WebApi.StaticContent;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class Output_SubstationDataPointConfig
    {
        public Output_SubstationDataPointConfig(SubstationDataPointConfigDetails details, MessageContext messageContext)
        {
            Id = details.Id;
            ShowName = details.ShowName;
            ShowType = details.ShowType.ToString();
            BindAssetId = details.BindAssetId;
            BindAssetName = details.BindAssetName;
            BindDataPoint = details.BindDataPoint;
            if (details.BindDataPointName.StartsWith("||"))
            {
                BindDataPointName = details.BindDataPointName.Substring(2);
            }
            else
            {
                BindDataPointName = messageContext.GetDataPointName(details.BindDataPoint);
            }
            BindDataPointUnit = details.BindDataPointUnit;
            BindAssetModel = details.BindAssetModel;
            BindAssetType = details.BindAssetType;
            DataPointCode = details.DataPointCode;
        }
        public int Id { get; set; }
        public string ShowName { get; set; } = string.Empty;
        public string ShowType { get; set; } = string.Empty;
        public int BindAssetId { get; set; }
        public string BindAssetName { get; set; } = string.Empty;
        public string BindDataPoint { get; set; } = string.Empty;
        public string BindDataPointName { get; set; } = string.Empty;
        public string BindDataPointUnit { get; set; } = string.Empty;
        public string DataPointCode { get; set; } = string.Empty;

        public string? BindAssetModel { get; set; }
        public string? BindAssetType { get; set; }
    }

    public class Update_SubstationDataPointConfig
    {
        public int Id { get; set; }
        public string ShowName { get; set; } = string.Empty;
        public string ShowType { get; set; } = string.Empty;
        public int BindAssetId { get; set; }
        public string BindDataPoint { get; set; } = string.Empty;
        public string DataPointCode { get; set; } = string.Empty;
        public SubstationDataPointConfigDetails GetDetails()
        {
            return new SubstationDataPointConfigDetails
            {
                Id = Id,
                ShowName = ShowName,
                ShowType = Enum.Parse<LayoutFormat>(ShowType),
                BindAssetId = BindAssetId,
                BindDataPoint = BindDataPoint,
                DataPointCode = DataPointCode
            };
        }
    }

    public class Add_SubstationDataPointConfig
    {
        public string ShowName { get; set; } = string.Empty;
        public string ShowType { get; set; } = string.Empty;
        public int BindAssetId { get; set; }
        public string BindDataPoint { get; set; } = string.Empty;
        public string DataPointCode { get; set; } = string.Empty;

        public SubstationDataPointConfigDetails GetDetails()
        {
            return new SubstationDataPointConfigDetails
            {
                Id = 0,
                ShowName = ShowName,
                ShowType = Enum.Parse<LayoutFormat>(ShowType),
                BindAssetId = BindAssetId,
                BindDataPoint = BindDataPoint,
                DataPointCode= DataPointCode
            };
        }
    }

    public class Chart_SubstationDataPoint
    {
        public string ShowName { get; set; } = string.Empty;
        public string DataPointUnit { get; set; } = string.Empty;
        public long EndTime { get; set; }
        public LineChartModel? Data { get; set; }
    }
}
