[{"ConfigName": "panelCount", "DbType": "pgsql", "Sql": "SELECT count(1) as Value, asset_type as Name FROM public.asset_info where asset_level = 30 group by asset_type;", "Extend": "{\"TypeNameMappings\":{\"StaticKey\":\"StaticModel_PANELTYPE\"}}", "DashboardType": "Pie"}, {"ConfigName": "transformerCount", "DbType": "pgsql", "Sql": "SELECT count(1) as Value, asset_type as Name FROM public.asset_info where asset_level = 31 group by asset_type;", "Extend": "{\"TypeNameMappings\":{\"StaticKey\":\"StaticModel_TRANSFORMERTYPE\"}}", "DashboardType": "Pie"}, {"ConfigName": "circuitCount", "DbType": "pgsql", "Sql": "SELECT count(1) as Y1, asset_type as X FROM public.asset_info where asset_level = 40 group by asset_type;", "Extend": "{\"TypeNameMappings\":{\"StaticKey\":\"StaticModel_CIRCUITTYPE\"}}", "DashboardType": "Line"}, {"ConfigName": "deviceTypeCount", "DbType": "pgsql", "Sql": "SELECT count(1) as Value, asset_type as Name FROM public.asset_info where asset_level = 50 group by asset_type;", "Extend": "{\"TypeNameMappings\":{\"StaticKey\":\"StaticModel_DEVICETYPE\"}}", "DashboardType": "Pie"}, {"ConfigName": "deviceModelCount", "DbType": "pgsql", "Sql": "SELECT count(1) as Y1, asset_model as X, asset_type as XTitles FROM public.asset_info where asset_level = 50 group by asset_model, asset_type;", "Extend": "{\"TypeNameMappings\":{\"StaticKey\":\"StaticModel_DEVICEMODEL\"}}", "DashboardType": "Line"}, {"ConfigName": "alarmCount", "DbType": "pgsql", "Sql": "select count(1) as Value, al.severity as Name from public.alarm_log al where al.status >= 0 and al.status <= 10 and al.event_type != 2 and al.event_type != 9 [[QueryTime]] group by al.severity;", "Extend": "{\"TypeNameMappings\":{\"0\":\"Alarm_Severity_Low\",\"10\":\"Alarm_Severity_Middle\",\"20\":\"Alarm_Severity_High\"}}", "DashboardType": "Pie"}, {"ConfigName": "alarmCount_query", "DbType": "pgsql", "Sql": "select count(1) as Value, al.severity as Name from public.alarm_log al where al.status >= 0 and al.status <= 10 and al.event_type != 2 and al.event_type != 9 and [[Query]] group by al.severity;", "Extend": "{\"TypeNameMappings\":{\"0\":\"Alarm_Severity_Low\",\"10\":\"Alarm_Severity_Middle\",\"20\":\"Alarm_Severity_High\"}}", "DashboardType": "Pie"}, {"ConfigName": "assetReplacementPart", "DbType": "pgsql", "Sql": "select sum(t.value) as Value, t.name from (SELECT count(1) as value, case ad.health_level when 'attention' then '1' when 'maintain' then '1' when 'rushRepair' then '2' else '3' end as name FROM public.asset_info ai left join public.asset_device_details ad on ai.id = ad.asset_id left join public.asset_relation ar1 on ar1.child_id=ai.id left join public.asset_relation ar2 on ar2.child_id= ar1.parent_id left join public.asset_relation ar3 on ar3.child_id= ar2.parent_id  where ai.asset_level = 50 and ad.health_level is not null and ar3.parent_id in([[AssetId]]) and ad.health_level != 'normal' group by ad.health_level ) as t group by t.name;", "Extend": "{\"TypeNameMappings\":{\"1\":\"ReplacementPart_Delay\",\"2\":\"ReplacementPart_Urgent\"}}", "DashboardType": "Pie"}, {"ConfigName": "assetHealth", "DbType": "pgsql", "Sql": "select t.column1 as X, t.column2 as Y1, t.column3 as Y2, t.column4 as Y3, t.column5 as Y5 from (values ('MCCB', 10, 1, 0, 0),('MCB', 1, 0, 0, 0),('MotorProtector', 0, 1, 0, 0),('ACB', 10, 0, 0, 1),('GeneralDevice', 1, 0, 0, 0),('Meter', 15, 0, 0, 0)) as t;", "Extend": "{\"YColumns\":[\"Normal\",\"Warning\",\"Hint\",\"Repair\"]}", "DashboardType": "Line"}, {"ConfigName": "assetCount", "DbType": "pgsql", "Sql": "select ai.asset_level as X, count(1) as Y1  from asset_info ai group by ai.asset_level order by ai.asset_level;", "Extend": "{\"TypeNameMappings\":{\"10\":\"Area\",\"20\":\"Substation\",\"30\":\"Panel\",\"31\":\"Transformer\",\"40\":\"Circuit\",\"50\":\"Device\"},\"X\":[\"10\",\"20\",\"30\",\"31\",\"40\",\"50\"]}", "DashboardType": "Line"}, {"ConfigName": "panelHealth", "DbType": "pgsql", "Sql": "SELECT count(1) as Value, coalesce(ad.health_level, 'unknown') as Name FROM public.asset_info ai inner join public.asset_device_details ad on ai.id = ad.asset_id  where ai.asset_level = 30 and ad.health_level is not null group by ad.health_level;", "Extend": "{\"TypeNameMappings\":{\"StaticKey\":\"PanelHealth\"}}", "DashboardType": "Pie"}, {"ConfigName": "breakerHealth", "DbType": "pgsql", "Sql": "SELECT count(1) as Value, coalesce(re.health_level, 'unknown') as Name FROM (select health_level FROM public.asset_info ai inner join public.asset_device_details ad on ai.id = ad.asset_id left join public.asset_relation ar1 on ar1.child_id=ai.id left join public.asset_relation ar2 on ar2.child_id= ar1.parent_id left join public.asset_relation ar3 on ar3.child_id= ar2.parent_id  where ai.asset_level = 50 and ad.health_level is not null and ar3.parent_id in ([[AssetId]]) Union ALL select health_level FROM public.asset_info ai inner join public.asset_device_details ad on ai.id = ad.asset_id left join public.asset_relation ar1 on ar1.child_id=ai.id left join public.asset_relation ar2 on ar2.child_id= ar1.parent_id where ai.asset_level = 50 and ad.health_level is not null and ar2.parent_id in ([[AssetId]]) Union ALL select health_level FROM public.asset_info ai inner join public.asset_device_details ad on ai.id = ad.asset_id left join public.asset_relation ar1 on ar1.child_id=ai.id where ai.asset_level = 50 and ad.health_level is not null and ar1.parent_id in ([[AssetId]])) as re group by re.health_level;", "Extend": "{\"TypeNameMappings\":{\"StaticKey\":\"BreakerHealth\"}}", "DashboardType": "Pie"}, {"ConfigName": "deviceHarmonicInfo", "DbType": "pgsql", "Sql": "SELECT \"timestamp\" as X, coalesce(ua_[[FrequencyCount]], 0.0) as Y1, coalesce(ub_[[FrequencyCount]], 0.0) as Y2, coalesce(uc_[[FrequencyCount]], 0.0) as Y3 FROM public.asset_device_harmonic_history where asset_id = [[AssetId]] and \"timestamp\" < [[End]] and \"timestamp\" >= [[Start]] order by \"timestamp\" asc;", "DashboardType": "Line"}, {"ConfigName": "alarmEventTypeCount", "DbType": "pgsql", "Sql": "select count(1) as Value, al.event_type as Name from public.alarm_log al where al.status >= 0 and al.status < 20 and al.event_type != 2 and al.event_type != 9 and al.event_type != 8 [[QueryTime]] group by al.event_type;", "DashboardType": "Pie"}]