[{"ConfigName": "Asset_Power", "Sql": "import \"math\"\r\nquery1= from(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and (r[\"assetid\"] == \"[[AssetId]]\"))\r\n|> filter(fn: (r) => r[\"_field\"] == \"p\" or r[\"_field\"] == \"active_power\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value:((if exists r.p then r.p else 0.0)  + (if exists r.active_power then r.active_power else 0.0))\r\n})) |> drop(columns:[\"assetid\",\"objectid\"])\r\n|> aggregateWindow(every: [[Cycle]], fn: sum, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"p\")\r\n|> group(columns: [\"_field\"])\r\nquery2= from(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and (r[\"assetid\"] == \"[[AssetId]]\"))\r\n|> filter(fn: (r) => r[\"_field\"] == \"q\" or r[\"_field\"] == \"collective_reactive_power\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value:((if exists r.q then r.q else 0.0)  + (if exists r.collective_reactive_power then r.collective_reactive_power else 0.0))\r\n})) |> drop(columns:[\"assetid\",\"objectid\"])\r\n|> aggregateWindow(every: [[Cycle]], fn: sum, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"q\")\r\n|> group(columns: [\"_field\"])\r\nquery3= from(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and (r[\"assetid\"] == \"[[AssetId]]\"))\r\n|> filter(fn: (r) => r[\"_field\"] == \"s\" or r[\"_field\"] == \"apparent_power\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value:((if exists r.s then r.s else 0.0)  + (if exists r.apparent_power then r.apparent_power else 0.0))\r\n})) |> drop(columns:[\"assetid\",\"objectid\"])\r\n|> aggregateWindow(every: [[Cycle]], fn: sum, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"s\")\r\n|> group(columns: [\"_field\"])\r\nresult = union(tables: [query1, query2,query3])\r\nresult", "DashboardType": "Line", "DbType": "influxdb", "Extend": "{\"YColumns\":[\"P\",\"Q\",\"S\"],\"YColumnNameGroup\":[\"DataPoint_P\",\"DataPoint_Q\",\"DataPoint_S\"]}"}, {"ConfigName": "ASSET_VOLTAGE", "Sql": "import \"math\"\r\nquery1= from(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and (r[\"assetid\"] == \"[[AssetId]]\"))\r\n|> filter(fn: (r) => r[\"_field\"] == \"ua\" or r[\"_field\"] == \"voltage_ph_n_l1\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value:((if exists r.ua then r.ua else 0.0)  + (if exists r.voltage_ph_n_l1 then r.voltage_ph_n_l1 else 0.0))\r\n})) |> drop(columns:[\"assetid\",\"objectid\"])\r\n|> aggregateWindow(every: [[Cycle]], fn: sum, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"ua\")\r\n|> group(columns: [\"_field\"])\r\nquery2= from(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and (r[\"assetid\"] == \"[[AssetId]]\"))\r\n|> filter(fn: (r) => r[\"_field\"] == \"ub\" or r[\"_field\"] == \"voltage_ph_n_l2\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value:((if exists r.ub then r.ub else 0.0)  + (if exists r.voltage_ph_n_l2 then r.voltage_ph_n_l2 else 0.0))\r\n})) |> drop(columns:[\"assetid\",\"objectid\"])\r\n|> aggregateWindow(every: [[Cycle]], fn: sum, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"ub\")\r\n|> group(columns: [\"_field\"])\r\nquery3= from(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and (r[\"assetid\"] == \"[[AssetId]]\"))\r\n|> filter(fn: (r) => r[\"_field\"] == \"uc\" or r[\"_field\"] == \"voltage_ph_n_l3\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value:((if exists r.uc then r.uc else 0.0)  + (if exists r.voltage_ph_n_l3 then r.voltage_ph_n_l3 else 0.0))\r\n})) |> drop(columns:[\"assetid\",\"objectid\"])\r\n|> aggregateWindow(every: [[Cycle]], fn: sum, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"uc\")\r\n|> group(columns: [\"_field\"])\r\nresult = union(tables: [query1, query2,query3])\r\nresult", "DashboardType": "Line", "DbType": "influxdb", "Extend": "{\"YColumns\":[\"Ua\",\"Ub\",\"Uc\"],\"YColumnNameGroup\":[\"DataPoint_Ua\",\"DataPoint_Ub\",\"DataPoint_Uc\"]}"}, {"ConfigName": "ASSET_CURRENTS", "Sql": "import \"math\"\r\nquery1= from(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and (r[\"assetid\"] == \"[[AssetId]]\"))\r\n|> filter(fn: (r) => r[\"_field\"] == \"ia\" or r[\"_field\"] == \"current_l1\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value:((if exists r.ia then r.ia else 0.0)  + (if exists r.current_l1 then r.current_l1 else 0.0))\r\n})) |> drop(columns:[\"assetid\",\"objectid\"])\r\n|> aggregateWindow(every: [[Cycle]], fn: sum, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"ia\")\r\n|> group(columns: [\"_field\"])\r\nquery2= from(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and (r[\"assetid\"] == \"[[AssetId]]\"))\r\n|> filter(fn: (r) => r[\"_field\"] == \"ib\" or r[\"_field\"] == \"current_l2\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value:((if exists r.ib then r.ib else 0.0)  + (if exists r.current_l2 then r.current_l2 else 0.0))\r\n})) |> drop(columns:[\"assetid\",\"objectid\"])\r\n|> aggregateWindow(every: [[Cycle]], fn: sum, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"ib\")\r\n|> group(columns: [\"_field\"])\r\nquery3= from(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and (r[\"assetid\"] == \"[[AssetId]]\"))\r\n|> filter(fn: (r) => r[\"_field\"] == \"ic\" or r[\"_field\"] == \"current_l3\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value:((if exists r.ic then r.ic else 0.0)  + (if exists r.current_l3 then r.current_l3 else 0.0))\r\n})) |> drop(columns:[\"assetid\",\"objectid\"])\r\n|> aggregateWindow(every: [[Cycle]], fn: sum, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"ic\")\r\n|> group(columns: [\"_field\"])\r\nresult = union(tables: [query1, query2,query3])\r\nresult", "DashboardType": "Line", "DbType": "influxdb", "Extend": "{\"YColumns\":[\"Ia\",\"Ib\",\"Ic\"],\"YColumnNameGroup\":[\"DataPoint_Ia\",\"DataPoint_Ib\",\"DataPoint_Ic\"]}"}, {"ConfigName": "Asset_PowerFactory", "Sql": "import \"math\"\r\nquery1 = from(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"powfactor\" or r[\"_field\"] ==\"power_factor\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value: ((if exists r.powfactor then r.powfactor else 0.0)  + (if exists r.power_factor then r.power_factor else 0.0))}))\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"powfactor\")\r\n|> drop(columns: [\"powfactor\",\"power_factor\"])\r\n\t\tquery2 = from(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"powfactor_a\"\r\n or r[\"_field\"] == \"powfactor_b\"\r\n  or r[\"_field\"] == \"powfactor_c\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\nresult = union(tables: [query1, query2])\r\nresult", "DashboardType": "Line", "DbType": "influxdb", "Extend": "{\"YColumns\":[\"PowFactor\",\"PowFactor_A\",\"PowFactor_B\",\"PowFactor_C\"],\"YColumnNameGroup\":[\"DataPoint_PowFactor\",\"DataPoint_PowFactor_A\",\"DataPoint_PowFactor_B\",\"DataPoint_PowFactor_C\"]}"}, {"ConfigName": "Asset_F", "Sql": "import \"math\"\r\nfrom(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"f\" or r[\"_field\"] == \"frequency\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> set(key: \"_field\", value: \"f\")\r\n|> map(fn: (r) => ({ r with\r\n_value: ((if exists r.f then r.f else 0.0)  + (if exists r.frequency  then r.frequency  else 0.0))\r\n}))\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> drop(columns: [\"frequency \",\"f\"])", "DashboardType": "Line", "DbType": "influxdb", "Extend": "{\"YColumns\":[\"F\"],\"YColumnNameGroup\":[\"DataPoint_F\"]}"}, {"ConfigName": "Asset_THD", "Sql": "import \"math\"\r\nquery1 = from(bucket: \"panel\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"archivedatarealtime\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"thd_ua\" or r[\"_field\"] ==\"thd_voltage_l1\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value: ((if exists r.thd_ua then r.thd_ua else 0.0)  + (if exists r.thd_voltage_l1 then r.thd_voltage_l1 else 0.0))}))\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"thd_ua\")\r\n|> group(columns: [\"_field\"])\r\n|> drop(columns: [\"thd_ua\",\"thd_voltage_l1\"])\r\nquery2 = from(bucket: \"panel\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"archivedatarealtime\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"thd_ub\" or r[\"_field\"] ==\"thd_voltage_l2\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value: ((if exists r.thd_ub then r.thd_ub else 0.0)  + (if exists r.thd_voltage_l2 then r.thd_voltage_l2 else 0.0))}))\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"thd_ub\")\r\n|> group(columns: [\"_field\"])\r\n|> drop(columns: [\"thd_ub\",\"thd_voltage_l2\"])\r\n\t query3 = from(bucket: \"panel\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"archivedatarealtime\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"thd_uc\" or r[\"_field\"] ==\"thd_voltage_l3\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value: ((if exists r.thd_uc then r.thd_uc else 0.0)  + (if exists r.thd_voltage_l3 then r.thd_voltage_l3 else 0.0))}))\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"thd_uc\")\r\n|> group(columns: [\"_field\"])\r\n|> drop(columns: [\"thd_uc\",\"thd_voltage_l3\"])\r\nquery4 = from(bucket: \"panel\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"archivedatarealtime\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"thd_u\" or r[\"_field\"] ==\"THD_voltage\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value: ((if exists r.thd_u then r.thd_u else 0.0)  + (if exists r.THD_voltage then r.THD_voltage else 0.0))}))\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"thd_u\")\r\n|> group(columns: [\"_field\"])\r\n|> drop(columns: [\"thd_u\",\"THD_voltage\"])\r\nquery5 = from(bucket: \"panel\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"archivedatarealtime\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"thd_i\" or r[\"_field\"] ==\"THD_current\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value: ((if exists r.thd_i then r.thd_i else 0.0)  + (if exists r.THD_current then r.THD_current else 0.0))}))\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"thd_i\")\r\n|> group(columns: [\"_field\"])\r\n|> drop(columns: [\"thd_i\",\"THD_current\"])\r\nquery6 = from(bucket: \"panel\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"archivedatarealtime\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"thd_ia\" or r[\"_field\"] ==\"thd_current_l1\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value: ((if exists r.thd_ia then r.thd_ia else 0.0)  + (if exists r.thd_current_l1 then r.thd_current_l1 else 0.0))}))\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"thd_ia\")\r\n|> group(columns: [\"_field\"])\r\n|> drop(columns: [\"thd_ia\",\"thd_current_l1\"])\r\nquery7 = from(bucket: \"panel\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"archivedatarealtime\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"thd_ib\" or r[\"_field\"] ==\"thd_current_l2\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value: ((if exists r.thd_ib then r.thd_ib else 0.0)  + (if exists r.thd_current_l2 then r.thd_current_l2 else 0.0))}))\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"thd_ib\")\r\n|> group(columns: [\"_field\"])\r\n|> drop(columns: [\"thd_ib\",\"thd_current_l2\"])\r\nquery8 = from(bucket: \"panel\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"archivedatarealtime\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"thd_ic\" or r[\"_field\"] ==\"thd_current_l3\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value: ((if exists r.thd_ic then r.thd_ic else 0.0)  + (if exists r.thd_current_l3 then r.thd_current_l3 else 0.0))}))\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"thd_ic\")\r\n|> group(columns: [\"_field\"])\r\n|> drop(columns: [\"thd_ic\",\"thd_current_l3\"])\r\nresult = union(tables: [query1, query2,query3,query4,query5, query6,query7,query8])\r\nresult", "DashboardType": "Line", "DbType": "influxdb", "Extend": "{\"YColumns\":[\"THD_Ua\",\"THD_Ub\",\"THD_Uc\",\"THD_U\",\"THD_Ia\",\"THD_Ib\",\"THD_Ic\",\"THD_I\"],\"YColumnNameGroup\":[\"DataPoint_THD_Ua\",\"DataPoint_THD_Ub\",\"DataPoint_THD_Uc\",\"DataPoint_THD_U\",\"DataPoint_THD_Ia\",\"DataPoint_THD_Ib\",\"DataPoint_THD_Ic\",\"DataPoint_THD_I\"]}"}, {"ConfigName": "Asset_THD_U", "Sql": "import \"math\"\r\nquery1 = from(bucket: \"panel\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"archivedatarealtime\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"thd_ua\" or r[\"_field\"] ==\"thd_voltage_l1\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value: ((if exists r.thd_ua then r.thd_ua else 0.0)  + (if exists r.thd_voltage_l1 then r.thd_voltage_l1 else 0.0))}))\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"thd_ua\")\r\n|> group(columns: [\"_field\"])\r\n|> drop(columns: [\"thd_ua\",\"thd_voltage_l1\"])\r\nquery2 = from(bucket: \"panel\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"archivedatarealtime\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"thd_ub\" or r[\"_field\"] ==\"thd_voltage_l2\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value: ((if exists r.thd_ub then r.thd_ub else 0.0)  + (if exists r.thd_voltage_l2 then r.thd_voltage_l2 else 0.0))}))\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"thd_ub\")\r\n|> group(columns: [\"_field\"])\r\n|> drop(columns: [\"thd_ub\",\"thd_voltage_l2\"])\r\n\t query3 = from(bucket: \"panel\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"archivedatarealtime\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"thd_uc\" or r[\"_field\"] ==\"thd_voltage_l3\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value: ((if exists r.thd_uc then r.thd_uc else 0.0)  + (if exists r.thd_voltage_l3 then r.thd_voltage_l3 else 0.0))}))\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"thd_uc\")\r\n|> group(columns: [\"_field\"])\r\n|> drop(columns: [\"thd_uc\",\"thd_voltage_l3\"])\r\nquery4 = from(bucket: \"panel\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"archivedatarealtime\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"thd_u\" or r[\"_field\"] ==\"THD_voltage\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value: ((if exists r.thd_u then r.thd_u else 0.0)  + (if exists r.THD_voltage then r.THD_voltage else 0.0))}))\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"thd_u\")\r\n|> group(columns: [\"_field\"])\r\n|> drop(columns: [\"thd_u\",\"THD_voltage\"])\r\nresult = union(tables: [query1, query2,query3,query4])\r\nresult", "DashboardType": "Line", "DbType": "influxdb", "Extend": "{\"YColumns\":[\"THD_Ua\",\"THD_Ub\",\"THD_Uc\",\"THD_U\"],\"YColumnNameGroup\":[\"DataPoint_THD_Ua\",\"DataPoint_THD_Ub\",\"DataPoint_THD_Uc\",\"DataPoint_THD_U\"]}"}, {"ConfigName": "Asset_THD_I", "Sql": "from(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"thd_ia\" or r[\"_field\"] == \"thd_ib\" or r[\"_field\"] == \"thd_ic\" or r[\"_field\"] == \"thd_i\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> fill(value: 0.0)\r\n", "DashboardType": "Line", "DbType": "influxdb", "Extend": "{\"YColumns\":[\"THD_Ia\",\"THD_Ib\",\"THD_Ic\",\"THD_I\"],\"YColumnNameGroup\":[\"DataPoint_THD_Ia\",\"DataPoint_THD_Ib\",\"DataPoint_THD_Ic\",\"DataPoint_THD_I\"]}"}, {"ConfigName": "Asset_BusbarTempCustomer", "Sql": "import \"math\"\r\nfrom(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"[[aconnectpoint]]\" or r[\"_field\"] == \"[[bconnectpoint]]\" or r[\"_field\"] == \"[[cconnectpoint]]\" or r[\"_field\"] == \"[[nconnectpoint]]\" )\r\n |> filter(fn: (r) => r._value >= -50 and r._value <= 125)\r\n |> fill(value: 0.0)\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 10.0) / 10.0}))\r\n", "DashboardType": "Line", "DbType": "influxdb", "Extend": "{\"YColumns\":[[Extend]],\"YColumnNameGroup\":[\"DataPoint_AConnectPoint_1\",\"DataPoint_BConnectPoint_1\",\"DataPoint_CConnectPoint_1\",\"DataPoint_NConnectPoint_1\",\"DataPoint_AConnectPoint_2\",\"DataPoint_BConnectPoint_2\",\"DataPoint_CConnectPoint_2\",\"DataPoint_NConnectPoint_2\"]}"}, {"ConfigName": "Asset_BusbarTemp", "Sql": "import \"math\"\r\nfrom(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"aconnectpoint_1\" or r[\"_field\"] == \"bconnectpoint_1\" or r[\"_field\"] == \"cconnectpoint_1\" or r[\"_field\"] == \"nconnectpoint_1\" or r[\"_field\"] == \"aconnectpoint_2\" or r[\"_field\"] == \"bconnectpoint_2\" or r[\"_field\"] == \"cconnectpoint_2\" or r[\"_field\"] == \"nconnectpoint_2\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n", "DashboardType": "Line", "DbType": "influxdb", "Extend": "{\"YColumns\":[\"AConnectPoint_1\",\"BConnectPoint_1\",\"CConnectPoint_1\",\"NConnectPoint_1\",\"AConnectPoint_2\",\"BConnectPoint_2\",\"CConnectPoint_2\",\"NConnectPoint_2\"],\"YColumnNameGroup\":[\"DataPoint_AConnectPoint_1\",\"DataPoint_BConnectPoint_1\",\"DataPoint_CConnectPoint_1\",\"DataPoint_NConnectPoint_1\",\"DataPoint_AConnectPoint_2\",\"DataPoint_BConnectPoint_2\",\"DataPoint_CConnectPoint_2\",\"DataPoint_NConnectPoint_2\"]}"}, {"ConfigName": "Asset_BusbarTemp_1", "Sql": "import \"math\"\r\nfrom(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"aconnectpoint_1\" or r[\"_field\"] == \"bconnectpoint_1\" or r[\"_field\"] == \"cconnectpoint_1\" or r[\"_field\"] == \"nconnectpoint_1\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n", "DashboardType": "Line", "DbType": "influxdb", "Extend": "{\"YColumns\":[\"AConnectPoint_1\",\"BConnectPoint_1\",\"CConnectPoint_1\",\"NConnectPoint_1\"],\"YColumnNameGroup\":[\"DataPoint_AConnectPoint_1\",\"DataPoint_BConnectPoint_1\",\"DataPoint_CConnectPoint_1\",\"DataPoint_NConnectPoint_1\"]}"}, {"ConfigName": "Asset_BusbarTemp_2", "Sql": "import \"math\"\r\nfrom(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"aconnectpoint_2\" or r[\"_field\"] == \"bconnectpoint_2\" or r[\"_field\"] == \"cconnectpoint_2\" or r[\"_field\"] == \"nconnectpoint_2\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n", "DashboardType": "Line", "DbType": "influxdb", "Extend": "{\"YColumns\":[\"AConnectPoint_2\",\"BConnectPoint_2\",\"CConnectPoint_2\",\"NConnectPoint_2\"],\"YColumnNameGroup\":[\"DataPoint_AConnectPoint_2\",\"DataPoint_BConnectPoint_2\",\"DataPoint_CConnectPoint_2\",\"DataPoint_NConnectPoint_2\"]}"}, {"ConfigName": "LoadRate", "Sql": "import \"math\"\r\nfrom(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"i_agv\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: false)\r\n|> filter(fn: (r) => exists r[\"_value\"])\r\n|> fill(value: 0.0)\r\n|> map(fn: (r) => ({ r with _value: math.round(x: r._value / [[RatedCurrent]].0 * 10000.0) / 100.0 }))\r\n", "DashboardType": "Line", "DbType": "influxdb", "Extend": "{\"YColumns\":[\"I_Agv\"],\"YColumnNameGroup\":null}"}, {"ConfigName": "LoadRate_ThirdPart", "Sql": "import \"math\"\r\n\r\nfrom(bucket: \"[[DBName]]\")\r\n  |> range(start: [[StartDate]], stop: [[EndDate]])\r\n  |> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\")\r\n  |> filter(fn: (r) => r[\"_field\"] == \"current_l1\" or r[\"_field\"] == \"current_l2\" or r[\"_field\"] == \"current_l3\")\r\n  |> filter(fn: (r) => r[\"assetid\"] == \"[[AssetId]]\")\r\n  |> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: false)\r\n  |> fill(value: 0.0)\r\n  |> pivot(rowKey:[\"_time\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n  |> map(fn: (r) => ({ r with _value: math.round(x: math.mMax(x: math.mMax(x: r[\"current_l1\"], y: r[\"current_l2\"]), y: r[\"current_l3\"]) / [[RatedCurrent]].0 * 10000.0) / 100.0}))\r\n  |> drop(columns: [\"_start\",\"_stop\",\"_measurement\",\"assetid\",\"objectid\",\"current_l1\",\"current_l2\",\"current_l3\"])", "DashboardType": "Line", "DbType": "influxdb", "Extend": "{\"YColumns\":[\"\"],\"YColumnNameGroup\":null}"}, {"ConfigName": "CapacityAnalysis", "Sql": "import \"math\"\r\nfrom(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"s\" or r[\"_field\"] == \"apparent_power\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> filter(fn: (r) => exists r[\"_value\"])\r\n|> fill(value: 0.0)\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"s\")", "DashboardType": "Line", "DbType": "influxdb", "Extend": "{\"YColumns\":[\"S\"],\"YColumnNameGroup\":null}"}, {"ConfigName": "Voltage", "Sql": "import \"math\"\r\nfrom(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"ua\" or r[\"_field\"] == \"ub\" or r[\"_field\"] == \"uc\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n", "DashboardType": "Line", "DbType": "influxdb", "Extend": "{\"YColumns\":[\"Ua\",\"Ub\",\"Uc\"],\"YColumnNameGroup\":[\"DataPoint_Ua\",\"DataPoint_Ub\",\"DataPoint_Uc\"]}"}, {"ConfigName": "ElectricCurrent", "Sql": "import \"math\"\r\nfrom(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"ia\" or r[\"_field\"] == \"ib\" or r[\"_field\"] == \"ic\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n", "DashboardType": "Line", "DbType": "influxdb", "Extend": "{\"YColumns\":[\"Ia\",\"Ib\",\"Ic\"],\"YColumnNameGroup\":[\"DataPoint_Ia\",\"DataPoint_Ib\",\"DataPoint_Ic\"]}"}, {"ConfigName": "PhaseShiftActivePower", "Sql": "import \"math\"\r\nfrom(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"pa\" or r[\"_field\"] == \"pb\" or r[\"_field\"] == \"pc\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n", "DashboardType": "Line", "DbType": "influxdb", "Extend": "{\"YColumns\":[\"Pa\",\"Pb\",\"Pc\"],\"YColumnNameGroup\":[\"DataPoint_Pa\",\"DataPoint_Pb\",\"DataPoint_Pc\"]}"}, {"ConfigName": "PhaseShiftReactivePower", "Sql": "import \"math\"\r\nfrom(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and r[\"assetid\"] == \"[[AssetId]]\")\r\n|> filter(fn: (r) => r[\"_field\"] == \"qa\" or r[\"_field\"] == \"qb\" or r[\"_field\"] == \"qc\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n", "DashboardType": "Line", "DbType": "influxdb", "Extend": "{\"YColumns\":[\"Qa\",\"Qb\",\"Qc\"],\"YColumnNameGroup\":[\"DataPoint_Qa\",\"DataPoint_Qb\",\"DataPoint_Qc\"]}"}, {"ConfigName": "Asset_TotalPower", "Sql": "import \"math\"\r\nquery1= from(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and ([[AssetIdList]]))\r\n|> filter(fn: (r) => r[\"_field\"] == \"p\" or r[\"_field\"] == \"active_power\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value:((if exists r.p then r.p else 0.0)  + (if exists r.active_power then r.active_power else 0.0))\r\n})) |> drop(columns:[\"assetid\",\"objectid\"])\r\n|> aggregateWindow(every: [[Cycle]], fn: sum, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"p\")\r\n|> group(columns: [\"_field\"])\r\nquery2= from(bucket: \"[[DBName]]\")\r\n|> range(start: [[StartDate]], stop: [[EndDate]])\r\n|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and ([[AssetIdList]]))\r\n|> filter(fn: (r) => r[\"_field\"] == \"q\" or r[\"_field\"] == \"collective_reactive_power\")\r\n|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r\n|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r\n|> map(fn: (r) => ({ r with _value:((if exists r.q then r.q else 0.0)  + (if exists r.collective_reactive_power then r.collective_reactive_power else 0.0))\r\n})) |> drop(columns:[\"assetid\",\"objectid\"])\r\n|> aggregateWindow(every: [[Cycle]], fn: sum, createEmpty: true)\r\n|> fill(value: 0.0)\r\n|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / 100.0}))\r\n|> set(key: \"_field\", value: \"q\")\r\n|> group(columns: [\"_field\"])\r\nresult = union(tables: [query1, query2])\r\nresult", "DashboardType": "Line", "DbType": "influxdb", "Extend": "{\"YColumns\":[\"P\",\"Q\"],\"YColumnNameGroup\":[\"DataPoint_Total_P\",\"DataPoint_Total_Q\"]}"}, {"ConfigName": "Energy_Summary", "Sql": "from(bucket: \"[[DBName]]\")\r    |> range(start: [[StartDate]], stop: [[EndDate]])\r    |> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and ([[AssetIdList]]))\r    |> filter(fn: (r) => r[\"_field\"] == \"forwardactivepower_tariff1\" or r[\"_field\"] == \"forwardactivepower_tariff2\" or r[\"_field\"] == \"activeenergy\")\r    |> aggregateWindow(every: 1h, fn: max, createEmpty: false)\r    |> drop(columns: [\"objectid\",\"_start\",\"_stop\"])\r    |> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r    |> map(fn: (r) => ({ r with\r        _value: ((if exists r.forwardactivepower_tariff1 then r.forwardactivepower_tariff1 else 0.0)  + (if exists r.forwardactivepower_tariff2 then r.forwardactivepower_tariff2 else 0.0) + (if exists r.activeenergy then r.activeenergy else 0.0))\r        }))\r    |> drop(columns: [\"forwardactivepower_tariff1\",\"forwardactivepower_tariff2\",\"activeenergy\"])", "DashboardType": "Line", "DbType": "influxdb", "Extend": ""}, {"ConfigName": "Energy_Interval_Summary", "Sql": "from(bucket: \"[[DBName]]\")\r    |> range(start: [[StartDate]], stop: [[EndDate]])\r    |> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and ([[AssetIdList]]))\r    |> filter(fn: (r) => r[\"_field\"] == \"forwardactivepower_tariff1\" or r[\"_field\"] == \"forwardactivepower_tariff2\" or r[\"_field\"] == \"activeenergy\" or r[\"_field\"] == \"active_energy_import\")\r    |> aggregateWindow(every: [[TimeInterval]], fn: max, createEmpty: false)\r    |> drop(columns: [\"objectid\",\"_start\",\"_stop\"])\r    |> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r    |> map(fn: (r) => ({ r with\r        _value: ((if exists r.forwardactivepower_tariff1 then r.forwardactivepower_tariff1 else 0.0)  + (if exists r.forwardactivepower_tariff2 then r.forwardactivepower_tariff2 else 0.0) + (if exists r.activeenergy then r.activeenergy else 0.0) + (if exists r.active_energy_import then r.active_energy_import else 0.0))\r        }))\r    |> drop(columns: [\"forwardactivepower_tariff1\",\"forwardactivepower_tariff2\",\"activeenergy\",\"active_energy_import\"])", "DashboardType": "Line", "DbType": "influxdb", "Extend": ""}, {"ConfigName": "Job_Energy_Summary", "Sql": "from(bucket: \"[[DBName]]\")\r    |> range(start: [[StartDate]], stop: [[EndDate]])\r    |> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and r[\"assetid\"]==\"[[AssetId]]\")\r    |> filter(fn: (r) => r[\"_field\"] == \"forwardactivepower_tariff1\" or r[\"_field\"] == \"forwardactivepower_tariff2\" or r[\"_field\"] == \"activeenergy\" or r[\"_field\"] == \"active_energy_import\")\r    |> aggregateWindow(every: 1h, fn: max, createEmpty: false)\r    |> drop(columns: [\"objectid\",\"_start\",\"_stop\"])\r    |> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r    |> map(fn: (r) => ({ r with\r        _value: ((if exists r.forwardactivepower_tariff1 then r.forwardactivepower_tariff1 else 0.0)  + (if exists r.forwardactivepower_tariff2 then r.forwardactivepower_tariff2 else 0.0) + (if exists r.activeenergy then r.activeenergy else 0.0) + (if exists r.active_energy_import then r.active_energy_import else 0.0))\r        }))\r    |> drop(columns: [\"forwardactivepower_tariff1\",\"forwardactivepower_tariff2\",\"activeenergy\",\"active_energy_import\"])", "DashboardType": "Line", "DbType": "influxdb", "Extend": ""}, {"ConfigName": "Job_Energy_Sum", "Sql": "data =  from(bucket: \"[[DBName]]\")\r|> range(start: [[StartDate]], stop: [[EndDate]])\r|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and ([[AssetIdList]]))\r|> filter(fn: (r) => r[\"_field\"] == \"forwardactivepower_tariff1\" or r[\"_field\"] == \"forwardactivepower_tariff2\" or r[\"_field\"] == \"activeenergy\" or r[\"_field\"] == \"active_energy_import\")\r|> aggregateWindow(every: 1h, fn: max, createEmpty: false)\r|> drop(columns: [\"objectid\",\"_start\",\"_stop\"])\r|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r|> map(fn: (r) => ({ r with\r_value: ((if exists r.forwardactivepower_tariff1 then r.forwardactivepower_tariff1 else 0.0)  + \r(if exists r.forwardactivepower_tariff2 then r.forwardactivepower_tariff2 else 0.0) + \r(if exists r.activeenergy then r.activeenergy else 0.0) + \r(if exists r.active_energy_import then r.active_energy_import else 0.0))\r}))\r|> drop(columns: [\"forwardactivepower_tariff1\",\"forwardactivepower_tariff2\",\"activeenergy\",\"active_energy_import\"])\rfirst_data = data\r|> first()\rlast_data = data\r|> last()\rdata_difference = join(tables: {first: first_data, last: last_data}, on: [\"_measurement\", \"assetid\"], method: \"inner\")\r|> map(fn: (r) => ({\rr with \r_value: r._value_last - r._value_first \r}))\rdata_difference", "DashboardType": "Line", "DbType": "influxdb", "Extend": ""}, {"ConfigName": "Energy_ThreePower", "Sql": "import \"math\"\rimport \"experimental\"\rfrom(bucket: \"[[DBName]]\")\r |> range(start: [[StartDate]], stop: [[EndDate]])\r|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and ([[AssetIdList]]))\r|> filter(fn: (r) => [[FieldFilter]])\r|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r |> map(fn: (r) => ({ r with _time:experimental.subDuration(d: 1m, from: r._time),_value:[[MapSum]]\r})) |> drop(columns:[\"assetid\",\"objectid\"]) \r|> aggregateWindow(every: [[Cycle]], fn: sum, createEmpty: true)\r|> fill(value: 0.0)\r|> map(fn: (r)=> ({r with _value: math.round(x: r._value * 100.0) / ([[Coefficient]] * 100.0)}))\r|> set(key: \"_field\", value: [[powerTypeQuery]])", "DashboardType": "Line", "DbType": "influxdb", "Extend": ""}, {"ConfigName": "Energy_ThreePower_Export", "Sql": "import \"math\"\rfrom(bucket: \"[[DBName]]\")\r|> range(start: [[StartDate]], stop: [[EndDate]])\r|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and ([[AssetIdList]]))\r|> filter(fn: (r) => r[\"_field\"] == \"p\" or r[\"_field\"] == \"active_power\" or r[\"_field\"] == \"q\" or r[\"_field\"] == \"collective_reactive_power\" or r[\"_field\"] == \"s\" or r[\"_field\"] == \"apparent_power\")\r|> aggregateWindow(every: [[Cycle]], fn: mean, createEmpty: true)\r|> pivot(rowKey:[\"_time\",\"assetid\"], columnKey: [\"_field\"], valueColumn: \"_value\")\r|> map(fn: (r) => ({\r        r with \r        total_p: math.round(x: ((if exists r.p then r.p else 0.0) + (if exists r.active_power then r.active_power else 0.0)) * 100.0 / [[Coefficient]]) / 100.0,\r       total_q: math.round(x: ((if exists r.q then r.q else 0.0) + (if exists r.collective_reactive_power then r.collective_reactive_power else 0.0)) * 100.0 / [[Coefficient]]) / 100.0,\r        total_s: math.round(x: ((if exists r.s then r.s else 0.0) + (if exists r.apparent_power then r.apparent_power else 0.0)) * 100.0 / [[Coefficient]]) / 100.0\r    }))\r|> drop(columns:[\"objectid\",\"p\",\"q\",\"s\",\"active_power\",\"collective_reactive_power\",\"apparent_power\",\"_measurement\",\"_start\",\"_stop\"]) \r|> group()", "DashboardType": "Line", "DbType": "influxdb", "Extend": ""}, {"ConfigName": "JOB_HARMONICS_VOLTAGE", "Sql": "from(bucket: \"[[DBName]]\") \r|> range(start: [[StartDate]], stop: [[EndDate]]) \r|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and r[\"assetid\"] == \"[[AssetId]]\") \r|> filter(fn: (r) => r._field =~ /harmonics_voltage_ph_n/) \r|> aggregateWindow(every: 5m, fn: mean, createEmpty: false) \r|> yield(name: \"mean\")", "DashboardType": "Line", "DbType": "influxdb", "Extend": ""}, {"ConfigName": "Health_Substation", "Sql": "from(bucket: \"[[DBName]]\")\r|> range(start: [[StartDate]], stop: [[EndDate]])\r|>filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\")|> filter(fn: (r) => r[\"_field\"] == \"y_result\" and r[\"topology_id\"]==\"[[topologyId]]\")\r |>last()", "DashboardType": "Line", "DbType": "influxdb", "Extend": ""}, {"ConfigName": "ACB_IN_USE_OVERVIEW", "Sql": "from(bucket: \"[[DBName]]\")\r|> range(start: [[StartDate]], stop: [[EndDate]])\r|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and r[\"asset_id\"] == \"[[AssetId]]\" and r[\"_field\"] != \"Ia\" and r[\"_field\"] != \"Ib\" and r[\"_field\"] != \"Ic\") \r|> drop(columns: [\"_start\", \"_stop\", \"_time\", \"asset_id\" ,\"_measurement\"]) \r|> last()", "DashboardType": "Line", "DbType": "influxdb", "Extend": ""}, {"ConfigName": "ACB_IN_USE_RESISTANCE", "Sql": "from(bucket: \"[[DBName]]\")\r|> range(start: [[StartDate]], stop: [[EndDate]])\r|> filter(fn: (r) => r[\"_measurement\"] == \"[[TableName]]\" and r[\"asset_id\"] == \"[[AssetId]]\" and r[\"_field\"] != \"Ia\" and r[\"_field\"] != \"Ib\" and r[\"_field\"] != \"Ic\" and r[\"_field\"] != \"down_pac_asset_id\" and r[\"_field\"] != \"up_pac_asset_id\") \r|> drop(columns: [\"_start\", \"_stop\", \"asset_id\" ,\"_measurement\"])", "DashboardType": "Line", "DbType": "influxdb", "Extend": ""}]