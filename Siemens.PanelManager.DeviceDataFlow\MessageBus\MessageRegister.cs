﻿using Akka.Actor;
using Siemens.PanelManager.DeviceDataFlow.Model;
using Siemens.PanelManager.DeviceDataFlow.StaticData;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.DataFlow;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.DeviceDataFlow.MessageBus
{
    public class MessageRegister : IMessageRegister
    {
        private ConcurrentDictionary<string, Action<AssetInputData>> _registerInfo = new ConcurrentDictionary<string, Action<AssetInputData>>();
        private ConcurrentDictionary<string, IActorRef> _registerRef = new ConcurrentDictionary<string, IActorRef>();

        public ConcurrentQueue<AssetInputData> RegisterMessageQueue(string serviceName)
        {
            var queue = new ConcurrentQueue<AssetInputData>();
            var action = new Action<AssetInputData>((p) => 
            {
                queue.Enqueue(p);
            });

            bool isOK = false;
            try
            {
                var actorManager = ActorManager.GetActorManager();
                if (actorManager != null)
                {
                    var registerRef = actorManager.ActorSystem.ActorOf<DataConduitActor>(serviceName);
                    if (registerRef != null)
                    {
                        registerRef.Tell((serviceName, action));
                        _registerRef.AddOrUpdate(serviceName, registerRef, (k, v) => registerRef);

                        isOK = true;
                    }
                }
            }
            catch (Exception ex)
            {
            }

            if (!isOK)
            {
                _registerInfo.TryAdd(serviceName, action);
            }
            return queue;
        }

        public void OutputMessage(AssetInputData data)
        {
            var refs = _registerRef.Values;

            foreach (var r in refs)
            {
                var inputData = new AssetInputData(data);
                r.Tell(inputData);
            }
        }

        public void RegisterMessageAction(string serviceName, Action<AssetInputData> action)
        {
            bool isOK = false;
            try
            {
                var actorManager = ActorManager.GetActorManager();
                if (actorManager != null)
                {
                    var registerRef = actorManager.ActorSystem.ActorOf<DataConduitActor>(serviceName);
                    if (registerRef != null)
                    {
                        registerRef.Tell((serviceName, action));
                        _registerRef.AddOrUpdate(serviceName, registerRef, (k, v) => registerRef);

                        isOK = true;
                    }
                }
            }
            catch (Exception ex)
            {
            }

            if (!isOK)
            {
                _registerInfo.TryAdd(serviceName, action);
            }
        }

        public void InitMessageAction()
        {
            ActorManager manager = ActorManager.GetActorManager();
            foreach (var item in _registerInfo)
            {
                var registerRef = manager.ActorSystem.ActorOf<DataConduitActor>(item.Key);
                if (registerRef != null)
                {
                    registerRef.Tell((item.Key, item.Value));
                    _registerRef.AddOrUpdate(item.Key, registerRef, (k, v) => registerRef);
                }
            }
        }
    }
}
