﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Quartz;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.Http;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.Common.mqtt;
using Siemens.PanelManager.Interface.Extend;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.UDC;
using Siemens.PanelManager.Server.Asset;
using SqlSugar;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TouchSocket.Core;

namespace Siemens.PanelManager.Job.Mqtt
{
    public class ExternalMqttAction : IExtendAction
    {
        private readonly ILogger _logger;
        private ExternalMqttServer? _server;
        private CancellationTokenSource? _tokenSource;
        private ConcurrentDictionary<int, string> _jobDictionary = new ConcurrentDictionary<int, string>();
        private IServiceProvider _provider;
        private readonly AssetExtendServer _assetExtendServer;

        public ExternalMqttAction(ILogger<ExternalMqttAction> logger, IServiceProvider provider)
        {
            _assetExtendServer = provider.GetRequiredService<AssetExtendServer>();
            _logger = logger;
            _provider = provider;
        }

        public void Init(ExternalMqttServer externalMqttServer)
        {
            _server = externalMqttServer;
        }

        public async Task<ResultDto> StartMqtt(MqttConfig config)
        {
            if (_server == null) return new ResultDto()
            {
                Code = 50000,
                IsSuccess = false,
                Msg = "Mqtt服务未初始化"
            };

            if (_tokenSource == null)
            {
                _tokenSource = new CancellationTokenSource();
            }

            var result = await _server.CheckMqttClient(config);

            if(result.IsSuccess)
            {
                await _server.StartAsync(_tokenSource.Token);

                await InitMqttConfigs();
            }

            return result;
        }

        public CancellationToken GetCancellationToken()
        {
            if (_tokenSource == null)
            {
                _tokenSource = new CancellationTokenSource();
            }

            return _tokenSource.Token;
        }

        public async Task StopMqtt()
        {
            if (_server == null) return;

            if (_tokenSource != null)
            {
                await _server.StopAsync(_tokenSource.Token);
                _tokenSource.Cancel();
            }
            else
            {
                await _server.StopAsync(CancellationToken.None);
            }

            _tokenSource = null;

            var keys = _jobDictionary.Keys.ToArray();

            foreach (var key in keys)
            {
                await RemoveCycleJob(key);
            }
        }

        public async Task RestartMqtt()
        {
            if (_server == null) return;

            if (_tokenSource != null)
            {
                await _server.StopAsync(_tokenSource.Token);
                _tokenSource.Cancel();
            }
            else
            {
                await _server.StopAsync(CancellationToken.None);
            }

            _tokenSource = new CancellationTokenSource();

            await Task.Delay(200);
            await _server.StartAsync(_tokenSource.Token);

            await InitMqttConfigs();
        }

        public void SendDataAsync(string topic, string data)
        {
            if (_server == null) return;

            _server.SendData(topic, data);
        }

        public async Task TriggerCycleJob(int cycle)
        {
            if (_server == null) return;

            if (!_jobDictionary.ContainsKey(cycle))
            {
                var jobManager = _provider.GetRequiredService<JobManager>();
                var cron = GetCronStr(cycle);
                var result = await jobManager.TriggerCronJob<ExternalMqttJob>($"ExternalMqtt-{cycle}", cron, new Dictionary<string, string> 
                {
                    ["Cycle"] = cycle.ToString(),
                });

                if (!string.IsNullOrEmpty(result))
                {
                    _jobDictionary.TryAdd(cycle, result);
                }
            }
        }

        /// <summary>
        /// 获取Mqtt服务状态
        /// </summary>
        /// <returns>1: 运行中, 2: 连接中, 0: 未启动</returns>
        public int GetMqttServerStatus()
        {
            if(_server == null) return 0;

            return _server.GetMqttServerStatus();
        }

        public async Task RemoveCycleJob(int cycle)
        {
            if (_jobDictionary.TryRemove(cycle, out var jobKey))
            {
                var jobManager = _provider.GetRequiredService<JobManager>();
                await jobManager.DeleteCronJob(jobKey);
            }
        }

        private const string MqttConfigCycleKey = "MqttDataConfig-{0}";
        /// <summary>
        /// 初始化 Mqtt Config
        /// </summary>
        /// <returns></returns>
        public async Task InitMqttConfigs()
        {
            if (_server == null) return;

            var cache = _provider.GetRequiredService<SiemensCache>();
            using (var client = _provider.GetRequiredService<ISqlSugarClient>())
            {
                var groups = await client.Queryable<AssetMqttDataPointConfig>()
                    .InnerJoin<AssetInfo>((c, a) => a.Id == c.AssetId)
                    .Where((c, a) => c.ConfigType == GroupConfigType.Group && c.SamplingPeriod > 0)
                    .Select((c, a) => c)
                    .ToListAsync();

                var jobManager = _provider.GetRequiredService<JobManager>();
                var periods = groups.Select(c => c.SamplingPeriod).Distinct().ToList();

                foreach (var period in periods)
                {
                    if (!_jobDictionary.TryGetValue(period, out var job))
                    {
                        var assetIds = groups.Where(c => c.SamplingPeriod == period).Select(c => c.AssetId).Distinct().ToList();
                        foreach (var assetId in assetIds)
                        {
                            cache.SetHashData<string>(string.Format(MqttConfigCycleKey, period), assetId.ToString(), string.Empty);
                        }
                        await TriggerCycleJob(period);
                    }
                }
            }
        }

        /// <summary>
        /// 检查Mqtt联通性
        /// </summary>
        /// <param name="mqttConfig"></param>
        /// <returns></returns>
        public Task<ResultDto> CheckMqttConfig(MqttConfig mqttConfig)
        {
            if (_server == null || mqttConfig == null)
            {
                return Task.FromResult(new ResultDto()
                {
                    Code = 50000,
                    IsSuccess = false
                });
            }

            return _server.CheckMqttClient(mqttConfig);
        }


        public async Task PublishAlarmAsync(AlarmLog log)
        {
            var cache = _provider.GetRequiredService<SiemensCache>();
            var assetSimpleInfo = cache.Get<AssetSimpleInfo>(string.Format("Asset:SimpleInfo-{0}", log.AssetId));
            if (assetSimpleInfo == null)
            {
                return;
            }

            if (!await _assetExtendServer.GetAssetEnableStatus(assetSimpleInfo.AssetId)) return;

            var sendData = new AlarmResult
            {
                ItemId = assetSimpleInfo.AssetId.ToString(),
                ItemName = assetSimpleInfo.AssetName,
                TimeStamp = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:sszzz"),
                DataType = "Alarm",
            };

            if (log.EventType == AlarmEventType.Alarm || log.EventType == AlarmEventType.UdcAlarm || log.EventType == AlarmEventType.BreakerTrip)
            {
                sendData.Embedded.Items.Add(new AlarmItem
                {
                    InternalName = "Alarm/Value/AlarmId",
                    Name = "Alarm_Id",
                    Value = $"{log.Id}"
                });

                sendData.Embedded.Items.Add(new AlarmItem
                {
                    InternalName = "Alarm/Value/AlarmPath",
                    Name = "Alarm_Path",
                    Value = $"{log.SubstationName ?? "-"}/{log.PanelName ?? "-"}/{log.CircuitName ?? "-"}/{log.DeviceName ?? "-"}"
                });

                sendData.Embedded.Items.Add(new AlarmItem
                {
                    InternalName = "Alarm/Value/EventType",
                    Name = "Event_Type",
                    Value = log.EventTypeStr
                });


                sendData.Embedded.Items.Add(new AlarmItem
                {
                    InternalName = "Alarm/Value/AlarmInfo",
                    Name = "Alarm_Info",
                    Value = log.Message
                });

                sendData.Embedded.Items.Add(new AlarmItem
                {
                    InternalName = "Alarm/Value/AlarmStatus",
                    Name = "Alarm_Status",
                    Value = log.StatusStr
                });

                sendData.Embedded.Items.Add(new AlarmItem
                {
                    InternalName = "Alarm/Value/AlarmSeverity",
                    Name = "Alarm_Severity",
                    Value = log.SeverityStr
                });

                sendData.Embedded.Items.Add(new AlarmItem
                {
                    InternalName = "Alarm/Value/AlarmTime",
                    Name = "Alarm_Time",
                    Value = log.UpdatedTime.ToString("yyyy-MM-ddTHH:mm:ss.fffzzz")
                });

                sendData.Count = 7;
                sendData.Total = 7;
            }
            else if (log.EventType == AlarmEventType.DeviceLog || log.EventType == AlarmEventType.CommunicationAlarm)
            {
                sendData.Embedded.Items.Add(new AlarmItem
                {
                    InternalName = "Alarm/Value/MsgId",
                    Name = "Msg_Id",
                    Value = $"{log.Id}"
                });

                sendData.Embedded.Items.Add(new AlarmItem
                {
                    InternalName = "Alarm/Value/MsgPath",
                    Name = "Msg_Path",
                    Value = $"{log.SubstationName ?? "-"}/{log.PanelName ?? "-"}/{log.CircuitName ?? "-"}/{log.DeviceName ?? "-"}"
                });

                sendData.Embedded.Items.Add(new AlarmItem
                {
                    InternalName = "Alarm/Value/EventType",
                    Name = "Event_Type",
                    Value = log.EventTypeStr
                });


                sendData.Embedded.Items.Add(new AlarmItem
                {
                    InternalName = "Alarm/Value/MsgInfo",
                    Name = "Msg_Info",
                    Value = log.Message
                });

                sendData.Embedded.Items.Add(new AlarmItem
                {
                    InternalName = "Alarm/Value/MsgSeverity",
                    Name = "Msg_Severity",
                    Value = log.SeverityStr
                });

                sendData.Embedded.Items.Add(new AlarmItem
                {
                    InternalName = "Alarm/Value/MsgTime",
                    Name = "Msg_Time",
                    Value = log.UpdatedTime.ToString("yyyy-MM-ddTHH:mm:sszzz")
                });

                sendData.Count = 6;
                sendData.Total = 6;
            }

            if (sendData.Count > 0)
            {
                var data = JsonConvert.SerializeObject(sendData);
                _server?.SendData("Alarm", data);
            }
        }


        /// <summary>
        /// 获取Cron表达式
        /// </summary>
        /// <param name="cycle"></param>
        /// <returns></returns>
        private string GetCronStr(int cycle)
        {
            string cron = string.Empty;
            var s = DateTime.Now.Second;
            var m = DateTime.Now.Minute;
            switch (cycle)
            {
                case 1:
                    cron = "0/1 * * * * ? *";
                    break;
                case 5:
                case 15:
                case 30:
                    {
                        var remainder = (s + 1) % cycle;
                        cron = $"{remainder}/{cycle} * * * * ? *";
                    }
                    break;
                case 60:
                    {
                        s = s + 1;
                        if (s == 60)
                        {
                            s = 0;
                        }

                        cron = $"{s} 0/1 * * * ? *";
                    }
                    break;
                case 300:
                case 900:
                    {
                        s = s + 1;
                        if (s == 60)
                        {
                            s = 0;
                        }

                        var mCycle = cycle / 60;
                        var remainder = m % mCycle;
                        cron = $"{s} {remainder}/{mCycle} * * * ? *";
                    }
                    break;
                default:
                    break;
            }

            return cron;
        }
    }
}
