﻿using Newtonsoft.Json;
using SqlSugar;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class AssetScanResult
    {
        public int Id { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public int? AssetId { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? AssetName { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? AssetType { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? AssetModel { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? AssetNumber { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? AssetIpAddress { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? ObjectId { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? UdcNetworkInterface { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? TypeName { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? TypeDisplayName { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? IpAddress { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public int? Port { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? Netmask { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? Gateway { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? MacAddress { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? PlantIdentifier { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? OrderNumber { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? FirmwareVersion { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? BootloaderVersion { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? ItemId { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public string? InterfaceName { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public bool IsGateway { get; set; }

        /// <summary>
        /// AssetIpAddress 界面显示内容 包含了Ip 端口 SlaveId/UnitId
        /// IpAddress 当前和数据库中 ip_address对应，此字段仅仅服务西门子设备
        /// 为了避免逻辑影响 新开字段
        /// </summary>
        [JsonIgnore]
        public string? Ip { get; set; }
        [JsonIgnore]
        public int? SlaveId { get; set; }
        [JsonIgnore]
        public string? DevicePort { get; set; }
        [JsonIgnore]
        public bool DeviceIsGateway { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public int? UnitId { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public int? ImportStatus { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Include)]
        public List<AssetScanResult> Childs { get; set; } = new List<AssetScanResult>();
    }
}
