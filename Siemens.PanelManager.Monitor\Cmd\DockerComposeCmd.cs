﻿using CliWrap;
using System;

namespace Siemens.PanelManager.Monitor.Cmd
{
    static class DockerComposeCmd
    {
        public static async Task Up(string yamlFile, ILogger logger)
        {
            Func<string, Task> func = (message) =>
            {
                logger.LogInformation(message);
                return Task.CompletedTask;
            };
            var result = await Cli.Wrap("docker")
                .WithArguments(new string[] { "compose", "-f", yamlFile, "up", "-d" })
                .WithStandardOutputPipe(PipeTarget.ToDelegate(func))
                .WithStandardErrorPipe(PipeTarget.ToDelegate(func))
                .WithValidation(CommandResultValidation.None)
                .ExecuteAsync();
        }

        public static async Task Down(string yamlFile, ILogger logger) 
        {
            if (string.IsNullOrEmpty(yamlFile)) return;
            Func<string, Task> func = (message) =>
            {
                logger.LogInformation(message);
                return Task.CompletedTask;
            };
            var result = await Cli.Wrap("docker")
                .WithArguments(new string[] { "compose", "-f", yamlFile, "down" })
                .WithStandardOutputPipe(PipeTarget.ToDelegate(func))
                .WithStandardErrorPipe(PipeTarget.ToDelegate(func))
                .WithValidation(CommandResultValidation.None)
                .ExecuteAsync();
        }

        public static async Task Logs(string yamlFile, ILogger logger)
        {
            if (string.IsNullOrEmpty(yamlFile)) return;
            Func<string, Task> func = (message) =>
            {
                logger.LogInformation(message);
                return Task.CompletedTask;
            };
            var result = await Cli.Wrap("docker")
                .WithArguments(new string[] { "compose", "-f", yamlFile, "logs" })
                .WithStandardOutputPipe(PipeTarget.ToDelegate(func))
                .WithStandardErrorPipe(PipeTarget.ToDelegate(func))
                .WithValidation(CommandResultValidation.None)
                .ExecuteAsync();
        }
    }
}
