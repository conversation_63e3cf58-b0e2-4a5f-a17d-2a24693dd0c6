﻿using log4net;
using log4net.Core;
using log4net.Repository;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Common.Log
{
    public class SiemensLog : Microsoft.Extensions.Logging.ILogger, IDisposable
    {
        private ILog _log;
        internal SiemensLog(string name)
        {
            _log= LogHelper.GetLog(name);
        }

        public IDisposable? BeginScope<TState>(TState state) where TState : notnull
        {
            return this;
        }

        public void Dispose()
        {

        }

        public bool IsEnabled(LogLevel logLevel)
        {
            return true;
        }

        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
        {
            var message = state?.ToString();
            switch (logLevel)
            {
                case LogLevel.Debug:
                    _log.Debug(message);
                    break;
                case LogLevel.Information:
                    _log.Info(message);
                    break;
                case LogLevel.Warning:
                    _log.Warn(message);
                    break;
                case LogLevel.Error:
                    if (exception != null)
                    {
                        _log.Error(message, exception);
                    }
                    else
                    {
                        _log.Fatal(message);
                    }
                    break;
                default: break;
            }
        }
    }
}
