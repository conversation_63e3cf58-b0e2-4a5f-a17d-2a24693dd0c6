﻿using Newtonsoft.Json.Linq;
using OracleInternal.Secure.Network;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.WebApi.StaticContent;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class AssetStaticModelResult
    {
        public AssetStaticModelResult() { }

        public AssetStaticModelResult(SystemStaticModel model, MessageContext messageContext)
        {
            if (!string.IsNullOrEmpty(model.Extend))
            {
                Extend = JObject.Parse(model.Extend);
            }
            Name = messageContext.GetStaticModelName(model);
            Code = model.Code;
        }

        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public dynamic? Extend { get; set; }
    }
}
