﻿using Microsoft.AspNetCore.Mvc;
using System.Xml.Linq;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class WorkOrderQueryParam
    {
        [FromQuery(Name = "page")]
        public int Page { get; set; } = 1;

        [FromQuery(Name = "pageSize")]
        public int PageSize { get; set; } = 10;

        [FromQuery(Name = "workOrderCode")]
        public string? WorkOrderCode { get; set; }

        [FromQuery(Name = "workOrderName")]
        public string? WorkOrderName { get;set; }

        [FromQuery(Name = "workOrderType")]
        public string? WorkOrderType { get; set; }

        [FromQuery(Name = "assetType")]
        public string? AssetType { get; set; }

        [FromQuery(Name = "device")]
        public string? Device { get; set; }

        [FromQuery(Name = "status")]
        public string? Status { get; set; }

        [FromQuery(Name = "measure")]
        public string? Measure { get; set; }

        [FromQuery(Name = "content")]
        public string? Content { get; set; }
    }
}
