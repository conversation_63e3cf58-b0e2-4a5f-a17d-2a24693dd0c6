﻿using Microsoft.AspNetCore.SignalR.Client;
using System;
using System.Threading.Tasks;

namespace Siemens.PanelManager.HubModel.Client
{
    public class MontorClient
    {
        private const string PUBLICKEY = "MIIBCgKCAQEAvAHhQHAGn4BpKaA+BLjfkk+n+Oc/3pz7DfFBgvoyzy44J+m2AHf+KbW91tF9uyBEOJSx7Ua09g0aPKZkIiHQsHxhI6GnaChhniYi/rZl796PqnIsEk4HmP9QlxBaCD1Nje74iPynovr9HWWz++fhatybSDIQiE9F8MgSATWXqMqRbXJynM/dV0Ph+fYAHJAgT8rjRX/flHXs6SK64s69jNuk1pFQwW2Ldcb7q5t030+vKS4phCCAXppNEpK0OW17h8yYBEc0xuJzJtTVZz+LVCd4ZE0NRGy0C1ed/tjVf0cFTGz4R5KIv41vCVJqlUJohuGFpHYV7n5EjV+R/ddUEwIDAQAB";
        private string _url;
        private IHubConnectionBuilder _builder;
        private HubConnection _connection;
        private Func<MonitorModel, Task> _monitorFunc = (m) => Task.CompletedTask;
        private Func<LogModel, Task> _logFunc = (m) => Task.CompletedTask;
        private Func<bool, Task> _finish = (r) => Task.CompletedTask;
        private Func<Exception, Task> _disconnectFunc = (ex) => Task.CompletedTask;
        private bool _isConnecting = false;
        public MontorClient(string url)
        {
            _url = url;
            _builder = new HubConnectionBuilder()
                .WithUrl(url);
        }

        public MontorClient(string ip, int port)
        {
            _url = $"ws://{ip}:{port}/Monitor";
            _builder = new HubConnectionBuilder()
                .WithUrl(_url);
        }


        public bool IsConneted
        {
            get
            {
                return _connection?.State == HubConnectionState.Connected;
            }
        }

        public async Task<bool> Start(string userName, string password)
        {
            if (!_isConnecting)
            {
                try
                {
                    _isConnecting = true;
                    _connection = _builder.Build();
                    _connection.Closed += Disconnect;
                    _connection.On("MonitorInfo", _monitorFunc);
                    _connection.On("LogInfo", _logFunc);
                    _connection.On("FinishUpgrade", _finish);
                    await _connection.StartAsync();
                    var auth = new AuthLoginModel(userName, password, PUBLICKEY);
                    var result = await _connection.InvokeAsync<bool>("login", auth.EncryptCode, auth.RandomCode);
                    if (!result)
                    {
                        _connection = null;
                    }
                    return result;
                }
                finally
                {
                    _isConnecting = false;
                }
            }
            return false;
        }

        private async Task Disconnect(Exception ex)
        {
            _connection = null;
            await _disconnectFunc(ex);
        }

        public async Task Stop()
        {
            if (_connection != null)
            {
                await _connection.StopAsync();
            }
        }

        #region Monitor Service
        public async Task BeginMonitor()
        {
            if (_connection == null) throw new ApplicationException("链接尚未开始");
            await _connection.SendAsync("StartMonitor");
        }
        public async Task<MonitorModel> GetMonitor()
        {
            if (_connection == null) throw new ApplicationException("链接尚未开始");
            return await _connection.InvokeAsync<MonitorModel>("GetMonitor");
        }
        public async Task StopMonitor()
        {
            if (_connection == null) throw new ApplicationException("链接尚未开始");
            await _connection.SendAsync("StopMonitor");
        }

        public async Task<MonitorModel[]> GetMonitorHistory()
        {
            if (_connection == null) throw new ApplicationException("链接尚未开始");
            return await _connection.InvokeAsync<MonitorModel[]>("GetMonitorHistory");
        }
        #endregion

        #region Upgrade
        public async Task<string> BeginUpgrade()
        {
            if (_connection == null) throw new ApplicationException("链接尚未开始");
            return await _connection.InvokeAsync<string>("BeginUpgrade");
        }

        public async Task<bool> CancelUpgrade()
        {
            if (_connection == null) throw new ApplicationException("链接尚未开始");
            return await _connection.InvokeAsync<bool>("CancelUpgrade");
        }

        public async Task<string[]> GetUpgradeStep()
        {
            if (_connection == null) throw new ApplicationException("链接尚未开始");
            return await _connection.InvokeAsync<string[]>("GetUpgradeStep");
        }
        #endregion

        #region Update Port
        public async Task UpdatePort(string func)
        {
            if (_connection == null) throw new ApplicationException("链接尚未开始");
            await _connection.SendAsync("UpdatePort", func);
        }
        #endregion

        #region Client Reader

        public void OnMonitor(Func<MonitorModel, Task> func)
        {
            _monitorFunc += func;
        }

        public void OnLogInfo(Func<LogModel, Task> func)
        {
            _logFunc += func;
        }

        public void OnUpgradeFinish(Func<bool, Task> func)
        {
            _finish += func;
        }

        public void OnDisconnect(Func<Exception ,Task> func)
        {
            _disconnectFunc += func;
        }
        #endregion
    }
}
