﻿using System.Security.Cryptography;
using System.Text;

namespace Siemens.PanelManager.Monitor.Function
{
    internal class LocalSecurityFunc
    {
        private readonly IConfiguration _configuration;
        public LocalSecurityFunc(IConfiguration configuration) 
        {
            _configuration = configuration;
        }

        public bool CheckSign(string key, string sign)
        { 
            var publicKey = _configuration.GetValue<string>("LocalPublicKey");
            if(string.IsNullOrEmpty(publicKey)) return false;

            RSA rsa = RSA.Create();
            rsa.ImportRSAPublicKey(Convert.FromBase64String(publicKey), out _);

            return rsa.VerifyData(Encoding.UTF8.GetBytes($"Siemens-{key}"), Convert.FromBase64String(sign), HashAlgorithmName.SHA512, RSASignaturePadding.Pkcs1);
        }
    }
}
