﻿namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class EnergyExportConsumptionResult
    {
        public string? Loop { get; set; }
        public string? Device { get; set; }
        public string? DateRange { get; set; }
        public string CreateDate { get; set; } = DateTime.Now.ToString("yyyy-MM-dd");
        public decimal? MaxValue { get; set; }
        public decimal? MinValue { get; set; }
        public decimal? AvgValue { get; set; }
    }

    public class ConsumptionExcelData
    {
        public string? CircuitName {  get; set; }
        public string? DateTime { get; set; }
        public decimal? Value { get; set; }
    }
}
