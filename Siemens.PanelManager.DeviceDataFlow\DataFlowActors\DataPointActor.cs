﻿using Akka.Actor;
using Microsoft.Extensions.Logging;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.DeviceDataFlow.Model;
using Siemens.PanelManager.DeviceDataFlow.StaticData;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.DataFlow;
using Siemens.PanelManager.Server.DataPoint;

namespace Siemens.PanelManager.DeviceDataFlow.DataFlowActors
{
    /// <summary>
    /// 非第三方设备根据数据点配置，处理数据
    /// </summary>
    public class DataPointActor : ReceiveActor
    {
        private readonly DataPointServer _dataPointServer;
        private readonly ILogger _logger;
        private readonly SiemensCache _cache;

        public DataPointActor(ILogger<DataPointActor> logger, DataPointServer server, SiemensCache cache)
        {
            _dataPointServer = server;
            _logger = logger;
            _cache = cache;

            ReceiveAsync<AssetInputData>(AssetInputData);
            ReceiveAsync<AssetChangeData>(AssetChangeData);
            ReceiveAsync<AssetDetailsData>(AssetDetailsData);
        }

        /// <summary>
        /// 资产输入数据
        /// 1. 保存数据 （根据 DataPoint中 CanChart确认是否需要保存至 Influx DB）
        /// 2. 发送给 AssetProxyActor （必定）
        /// 3. 添加至全局实时数据中 （根据CanReportedData 是否发送给 AssetAllDataActor）
        /// 
        /// 第三方设备此流程都执行
        /// </summary>
        /// <param name="inputData"></param>
        /// <returns></returns>
        private async Task AssetInputData(AssetInputData inputData)
        {
            if (inputData.AssetId.HasValue)
            {
                var assetInfo = _cache.Get<AssetSimpleInfo>(string.Format(Constant.AssetSimpleInfoCacheKey, inputData.AssetId));
                if (assetInfo == null) return;

                var dataPoints = await _dataPointServer.GetDataPointInfos(assetInfo.AssetLevel, assetInfo.AssetType, assetInfo.AssetModel);
                var actorManager = ActorManager.GetActorManagerNoException();

                if (dataPoints == null || dataPoints.Count == 0)
                {
                    if (actorManager != null)
                    {
                        actorManager.AssetProxyRef.Tell(inputData);
                        actorManager.AssetStatusSaveRef.Tell(new AssetSaveParam
                        {
                            AssetId = inputData.AssetId.Value,
                            ObjectId = inputData.ObjectId ?? string.Empty,
                            TableName = Constant.RealTimeMeasurementName,
                            Datas = new Dictionary<string, string>(inputData.Datas),
                            Time = inputData.InputTime
                        });

                        actorManager.AllDataRef.Tell(new AssetInputData(inputData));
                    }
                    return;
                }

                var saveData = new Dictionary<string, string>();
                var allDatas = new Dictionary<string, string>();
                foreach (var kv in inputData.Datas)
                {
                    var dataPoint = dataPoints.FirstOrDefault(x => kv.Key.Equals(x.Code, StringComparison.OrdinalIgnoreCase));

                    if (dataPoint?.CanChart ?? false)
                    {
                        saveData.Add(kv.Key, kv.Value);
                    }

                    if (dataPoint?.CanReportedData ?? false)
                    {
                        allDatas.Add(kv.Key, kv.Value);
                    }
                }

                if (actorManager != null)
                {
                    actorManager.AssetProxyRef.Tell(inputData);
                    
                    if (saveData.Count > 0)
                    {
                        actorManager.AssetStatusSaveRef.Tell(new AssetSaveParam
                        {
                            AssetId = inputData.AssetId.Value,
                            ObjectId = inputData.ObjectId ?? string.Empty,
                            TableName = Constant.RealTimeMeasurementName,
                            Datas = saveData,
                            Time = inputData.InputTime
                        });
                    }

                    if (allDatas.Count > 0)
                    {
                        actorManager.AllDataRef.Tell(new AssetInputData
                        {
                            AssetId = inputData.AssetId,
                            AssetName = inputData.AssetName,
                            Datas = allDatas,
                            InputTime = inputData.InputTime
                        });
                    }
                }
            }
        }

        /// <summary>
        /// 资产变更数据
        /// 1. 发送给 TopologyActor （根据 DataPoint中 CanPrint确认是否需要发送）
        /// 2. 发送给 AlarmActor （根据 DataPoint中 CanAlarmListen确认是否需要发送）
        /// 3. Circuit Panel Substation 添加至全局实时数据中 （根据CanReportedData 是否发送给 AssetAllDataActor）
        /// 
        /// 第三方设备此流程都执行
        /// </summary>
        /// <param name="changeData"></param>
        /// <returns></returns>
        private async Task AssetChangeData(AssetChangeData changeData)
        {
            var assetInfo = _cache.Get<AssetSimpleInfo>(string.Format(Constant.AssetSimpleInfoCacheKey, changeData.AssetId));
            if (assetInfo == null) return;

            var dataPoints = await _dataPointServer.GetDataPointInfos(assetInfo.AssetLevel, assetInfo.AssetType, assetInfo.AssetModel);
            var actorManager = ActorManager.GetActorManagerNoException();

            if (dataPoints == null || dataPoints.Count == 0)
            {
                if (actorManager != null)
                {
                    actorManager.TopologyRef.Tell(changeData);

                    actorManager.AlarmRef.Tell(changeData);
                }
                return;
            }

            var printData = new Dictionary<string, string>();
            var alarmData = new Dictionary<string, string>();
            var allDatas = new Dictionary<string, string>();

            foreach (var kv in changeData.ChangeDatas)
            {
                var dataPoint = dataPoints.FirstOrDefault(x => kv.Key.Equals(x.Code, StringComparison.OrdinalIgnoreCase));
                if (dataPoint?.CanPrint ?? false)
                {
                    printData.Add(kv.Key, kv.Value);
                }

                if (dataPoint?.CanAlarmListen ?? false)
                {
                    alarmData.Add(kv.Key, kv.Value);
                }

                if (dataPoint?.CanReportedData ?? false)
                {
                    allDatas.Add(kv.Key, kv.Value);
                }
            }

            if (actorManager != null)
            {
                if (printData.Count > 0)
                {
                    actorManager.TopologyRef.Tell(new AssetChangeData
                    {
                        AssetId = changeData.AssetId,
                        ChangeDatas = printData,
                        ChangeTime = changeData.ChangeTime,
                        AssetLevel = changeData.AssetLevel,
                        AssetModel = changeData.AssetModel,
                        AssetName = changeData.AssetName,
                        AssetType = changeData.AssetType
                    });
                }

                if (alarmData.Count > 0)
                {
                    actorManager.AlarmRef.Tell(new AssetChangeData
                    {
                        AssetId = changeData.AssetId,
                        ChangeDatas = alarmData,
                        ChangeTime = changeData.ChangeTime,
                        AssetLevel = changeData.AssetLevel,
                        AssetModel = changeData.AssetModel,
                        AssetName = changeData.AssetName,
                        AssetType = changeData.AssetType
                    });
                }

                if (assetInfo.AssetLevel == AssetLevel.Panel
                    || assetInfo.AssetLevel == AssetLevel.Substation
                    || assetInfo.AssetLevel == AssetLevel.Circuit)
                {
                    actorManager.AllDataRef.Tell(new AssetChangeData
                    {
                        AssetId = changeData.AssetId,
                        ChangeDatas = allDatas,
                        ChangeTime = changeData.ChangeTime,
                        AssetLevel = changeData.AssetLevel,
                        AssetModel = changeData.AssetModel,
                        AssetName = changeData.AssetName,
                        AssetType = changeData.AssetType
                    });
                }
            }

        }

        /// <summary>
        /// 资产详情数据
        /// 1. 保存数据 （根据 DataPoint中 CanChart确认是否需要保存至 Influx DB）
        /// 2. 发送给 TopologyActor （根据 DataPoint中 CanPrint确认是否需要发送）
        /// 3. 发送给 AlarmActor （根据 DataPoint中 CanAlarmListen确认是否需要发送）
        /// 4. 添加至全局实时数据中 （根据CanReportedData 是否发送给 AssetAllDataActor）
        /// 
        /// 此流程都不执行
        /// </summary>
        /// <param name="detailsData"></param>
        /// <returns></returns>
        private async Task AssetDetailsData(AssetDetailsData detailsData)
        {
            var assetInfo = _cache.Get<AssetSimpleInfo>(string.Format(Constant.AssetSimpleInfoCacheKey, detailsData.AssetId));
            if (assetInfo == null) return;

            var dataPoints = await _dataPointServer.GetDataPointInfos(assetInfo.AssetLevel, assetInfo.AssetType, assetInfo.AssetModel);
            if (dataPoints == null || dataPoints.Count == 0) return;

            var printData = new Dictionary<string, string>();
            var alarmData = new Dictionary<string, string>();
            var saveData = new Dictionary<string, string>();
            var allDatas = new Dictionary<string, string>();

            foreach (var kv in detailsData.Details)
            {
                var dataPoint = dataPoints.FirstOrDefault(x => kv.Key.Equals(x.Code, StringComparison.OrdinalIgnoreCase));

                if (dataPoint?.CanPrint ?? false)
                {
                    printData.Add(kv.Key, kv.Value);
                }

                if (dataPoint?.CanAlarmListen ?? false)
                {
                    alarmData.Add(kv.Key, kv.Value);
                }

                if (dataPoint?.CanChart ?? false)
                {
                    saveData.Add(kv.Key, kv.Value);
                }

                if (dataPoint?.CanReportedData ?? false)
                {
                    allDatas.Add(kv.Key, kv.Value);
                }
            }

            var actorManager = ActorManager.GetActorManagerNoException();
            if (actorManager != null)
            {
                if (saveData.Count > 0)
                {
                    actorManager.AssetStatusSaveRef.Tell(new AssetSaveParam
                    {
                        AssetId = detailsData.AssetId,
                        ObjectId = assetInfo.ObjectId ?? string.Empty,
                        TableName = Constant.RealTimeMeasurementName,
                        Datas = saveData,
                        Time = detailsData.ChangeTime
                    });
                }

                if (printData.Count > 0)
                {
                    actorManager.TopologyRef.Tell(new AssetChangeData
                    {
                        AssetId = detailsData.AssetId,
                        ChangeDatas = printData,
                        ChangeTime = detailsData.ChangeTime,
                        AssetLevel = assetInfo.AssetLevel,
                        AssetModel = assetInfo.AssetModel,
                        AssetName = assetInfo.AssetName,
                        AssetType = assetInfo.AssetType
                    });
                }

                if (alarmData.Count > 0)
                {
                    actorManager.AlarmRef.Tell(new AssetChangeData
                    {
                        AssetId = assetInfo.AssetId,
                        ChangeDatas = alarmData,
                        ChangeTime = detailsData.ChangeTime,
                        AssetLevel = assetInfo.AssetLevel,
                        AssetModel = assetInfo.AssetModel,
                        AssetName = assetInfo.AssetName,
                        AssetType = assetInfo.AssetType
                    });
                }

                if (allDatas.Count > 0)
                {
                    actorManager.AllDataRef.Tell(new AssetDetailsData
                    {
                        AssetId = detailsData.AssetId,
                        Details = allDatas,
                        ChangeTime = detailsData.ChangeTime,
                        AssetLevel = detailsData.AssetLevel,
                        AssetModel = detailsData.AssetModel,
                        AssetName = detailsData.AssetName,
                        AssetType = detailsData.AssetType
                    });
                }
            }
        }
    }
}
