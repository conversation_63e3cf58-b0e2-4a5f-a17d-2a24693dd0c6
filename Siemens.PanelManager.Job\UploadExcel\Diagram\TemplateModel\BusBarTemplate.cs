﻿using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Job.UploadExcel.Diagram.TemplateModel.NodeTemplate;
using Siemens.PanelManager.Model.Topology;
using System.Text;

namespace Siemens.PanelManager.Job.UploadExcel.Diagram.TemplateModel
{
    /// <summary>
    /// 母线模板
    /// </summary>
    internal class BusBarTemplate : LineTemplateModelBase
    {
        private const int MinLenght = 100;
        private const int MinInterval = 30;
        private List<LineData> _lineDatas = new List<LineData>();
        private List<NodeData> _nodeDatas = new List<NodeData>();
        private string _busBarName;
        private int _busBarId;
        private int _begin = 0;
        private int _nextLength = 0;
        private int _lastPointId = 0;
        public BusBarTemplate(int busBarId, int x, int y, StringBuilder message)
            : base(x, y, message)
        {
            _busBarName = RomanNumberHelper.GetString(busBarId);
            _busBarId = busBarId;

            _lastPointId = 0;

            _nodeDatas.Add(new LabelNode()
            {
                LocationY = y - 30,
                LocationX = x,
                Text = $"{_busBarName}段母线",
                Key = -99
            });
        }

        public string BusBarName => _busBarName;

        public override NodeData[] NodeDatas => _nodeDatas.ToArray();

        public override LineData[] LineDatas => _lineDatas.ToArray();
        public override int SetKey(int beforeKey)
        {
            _lastPointId += beforeKey;
            _begin = beforeKey;
            return base.SetKey(beforeKey);
        }
        /// <summary>
        /// index:
        /// 0 自动向后添加节点
        /// 其他 在指定位置添加节点
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public NodeData? CreateNode(int length, int index = 0, int offset = 0)
        {
            _nextLength = length;
            if (length < MinLenght)
            {
                _nextLength = MinLenght;
            }

            NodeData? point = null;

            switch (index)
            {
                case 0:
                    {
                        var nextId = GetNewId();
                        var lastPoint = new PointNode()
                        {
                            Key = nextId,
                            LocationY = Y,
                            LocationX = X + offset,
                            Index = _busBarId,
                            Flag = "busPoint",
                        };
                        _nodeDatas.Add(lastPoint);
                        if (_lastPointId > _begin)
                        {
                            var line = new LineData()
                            {
                                Key = GetNewId(),
                                From = _lastPointId,
                                To = nextId,
                                Category = "busBarlink",
                                BusBarId = _busBarName
                            };
                            _lineDatas.Add(line);
                        }

                        X += _nextLength + offset;
                        _lastPointId = nextId;
                        point = lastPoint;
                    }
                    break;
                default:
                    {
                        if (index < 0)
                        {
                            throw new CreateTemplateException($"错误的坐标, {_busBarName}无法生成新的点");
                        }

                        var betweenPoints = new List<NodeData>();
                        NodeData? previous = null;
                        NodeData? next = null;
                        var pointList = _nodeDatas.OfType<PointNode>();
                        foreach (var n in pointList)
                        {
                            if (!n.Key.HasValue) continue;
                            if (n.LocationX > index)
                            {
                                if (next == null || next.LocationX < n.LocationX)
                                {
                                    next = n;
                                    continue;
                                }
                            }
                            if (n.LocationX < index)
                            {
                                if (previous == null || previous.LocationX > n.LocationX)
                                {
                                    previous = n;
                                    continue;
                                }
                            }
                            previous = n;
                            break;
                        }

                        var locationX = index;
                        if (previous != null && (index - previous.LocationX) < MinInterval)
                        {
                            locationX = previous.LocationX + MinInterval;
                        }
                        else if (next != null && (next.LocationX - index) < MinInterval)
                        {
                            locationX = next.LocationX - MinInterval;
                        }

                        point = new PointNode()
                        {
                            Key = GetNewId(),
                            LocationY = Y,
                            LocationX = locationX,
                            Index = _busBarId,
                            Flag = "busPoint"
                        };

                        if (previous != null && next != null && previous.Key.HasValue && next.Key.HasValue)
                        {
                            var line = _lineDatas.FirstOrDefault(l => (l.From == previous.Key && l.To == next.Key) || (l.From == next.Key && l.To == previous.Key));
                            if (line != null)
                            {
                                line.From = previous.Key.Value;
                                line.To = point.Key.Value;
                                var newLine = new LineData()
                                {
                                    Key = GetNewId(),
                                    From = point.Key.Value,
                                    To = next.Key.Value,
                                    Category = "busBarlink",
                                    BusBarId = _busBarName
                                };
                                _lineDatas.Add(newLine);
                                _nodeDatas.Add(point);
                            }
                            else
                            {
                                throw new CreateTemplateException($"错误的坐标, {_busBarName}无法生成新的点");
                            }
                        }
                        else if (previous != null && previous.Key.HasValue)
                        {
                            var newLine = new LineData()
                            {
                                Key = GetNewId(),
                                From = point.Key.Value,
                                To = previous.Key.Value,
                                Category = "busBarlink",
                                BusBarId = _busBarName
                            };
                            _lineDatas.Add(newLine);
                            _nodeDatas.Add(point);
                        }
                        else if (next != null && next.Key.HasValue)
                        {
                            var newLine = new LineData()
                            {
                                Key = GetNewId(),
                                From = next.Key.Value,
                                To = point.Key.Value,
                                Category = "busBarlink",
                                BusBarId = _busBarName
                            };
                            _lineDatas.Add(newLine);
                            _nodeDatas.Add(point);
                        }
                        else
                        {
                            throw new CreateTemplateException($"错误的坐标, {_busBarName}无法生成新的点");
                        }

                        break;
                    }
            }

            return point;
        }

        public void Finish(int assetId)
        {
            #region
            var points = _nodeDatas.OfType<PointNode>().Where(n => n.Flag == "busPoint" && n.Index == _busBarId).ToArray();
            var locXList = points.Select(x => x.LocationX).ToArray();
            var leftX = locXList.Min();
            var rightX = locXList.Max();
            var y = points.Select(n => n.LocationY).Max();
            var length = rightX - leftX;
            var begin = leftX;
            if (length > 450)
            {
                begin += length / 2 - 225;
            }

            var dataPointId = GetNewId();
            _nodeDatas.Add(new DataPointNode()
            {
                Key = dataPointId,
                LocationX = begin,
                LocationY = y - 30,
                ElectricalType = "Voltage",
                ElectricalName = "Ua",
                BindAssetId = assetId,
                BindPointName = "Ua"
            });

            begin += 150;
            dataPointId = GetNewId();
            _nodeDatas.Add(new DataPointNode()
            {
                Key = dataPointId,
                LocationX = begin,
                LocationY = y - 30,
                ElectricalType = "Voltage",
                ElectricalName = "Ub",
                BindAssetId = assetId,
                BindPointName = "Ub"
            });

            begin += 150;
            dataPointId = GetNewId();
            _nodeDatas.Add(new DataPointNode()
            {
                Key = dataPointId,
                LocationX = begin,
                LocationY = y - 30,
                ElectricalType = "Voltage",
                ElectricalName = "Uc",
                BindAssetId = assetId,
                BindPointName = "Uc"
            });
            #endregion
        }
    }
}
