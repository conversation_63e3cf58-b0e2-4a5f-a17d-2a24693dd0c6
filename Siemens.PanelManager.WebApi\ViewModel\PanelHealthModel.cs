﻿namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class PanelHealthModel
    {
        public int? Id { get; set; }
        public string? Name { get; set; }
        public decimal? Value { get; set; }
        public string? Code { get; set; }
        /// <summary>
        /// 0: 10分制
        /// 1: 100分制
        /// </summary>
        public int? LimitOpt { get; set; }
        public int[]? Limit { get; set; }
        public decimal? Weight { get; set; }
        public string? Score { get; set; }
        public string? Message { get; set;}
        public string? Status { get; set; } = string.Empty;
        public bool IsSystem { get; set; } = false;
        public bool IsEnable { get; set; } = false;
        public List<PanelHealthModel>? Children { get; set; }

    }
}
