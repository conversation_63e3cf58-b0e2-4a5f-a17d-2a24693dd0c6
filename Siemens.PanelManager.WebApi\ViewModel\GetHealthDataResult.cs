﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Siemens.PanelManager.Model.Chart;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class GetHealthDataResult
    {
        public GetHealthDataResult()
        {
            ReportTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            BranchCurrentResult = new HealthDataDetails(0m);
            CBsResult = new HealthDataDetails(0m);
            TemperatureResult = new HealthDataDetails(0m,"");
            EvaluationResult = new HealthDataDetails(0);
            AlarmResult = new HealthDataDetails(0,"");
            BasicsResult = new LineChartModel();
            alarmVuale = new Dictionary<string, decimal>();
        }
        public string ReportTime { get; set; }
        [JsonProperty("CBsResult")]
        public HealthDataDetails CBsResult { get; set; }
        public HealthDataDetails BranchCurrentResult { get; set; }
        public HealthDataDetails TemperatureResult { get;set; }
        public HealthDataDetails AlarmResult { get; set; }
        public HealthDataDetails EvaluationResult { get; set; }
        public JObject topology { get; set; }

        public static implicit operator JObject(GetHealthDataResult v)
        {
            throw new NotImplementedException();
        }
        public LineChartModel BasicsResult { get; set; }
        public string maxLine_id { get; set; }
        public Dictionary<string, decimal> alarmVuale { get; set; }
        public List<string> suggestion { get;set; }
        public decimal score { get;set; }
    }
    public class HealthDataDetails
    {
        public HealthDataDetails(decimal value)
        {
            Value = value;
        }
        public HealthDataDetails(decimal value, string abnormals)
            :this(value)
        {

            Abnormal = abnormals;
            
        }
        public decimal Value { get; set; }
        public string Abnormal { get; set; }
    }
}
