﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Newtonsoft.Json;
using Siemens.PanelManager.Model.Algorithm;
using Siemens.PanelManager.Model.Chart;

namespace Siemens.PanelManager.WebApi.ViewModel
{
    public class CustomizeHealthResult
    {
        public string familyMsg { get; set; }
        public string enviromentMsg { get; set; }
        public string cabinetMsg { get; set; }
        public string meterMsg { get; set; }
        public string lampMsg { get; set; }
        public string isolatedMsg { get; set; }
        public string protectionMsg { get; set; }
        public string unwindingMethodMsg { get; set; }
        public string modelSelectionMsg { get; set; }
        public string transformerConfigurationMsg { get; set; }
        public string airSwitchBreakingCapacityMsg { get; set; }
        public string cabinetAppearanceMsg { get; set; }
        public string cabinetConnectionMsg { get; set; }
        public string cabinetGroundingMsg { get; set; }
        public string cabinetLockingMsg { get; set; }
        public string airSwitchAppearanceMsg { get; set; }
        public string airSwitchOperationMsg { get; set; }
        public string airSwitchPositionMsg { get; set; }
        public string contactorSuctionConditionMsg { get; set; }
        public string contactorInsulationComponentsMsg { get; set; }
        public string contactorAuxiliaryContactsMsg { get; set; }
        public string contactorExtinguishingCoverMsg { get; set; }
        public string contactorClosingCoilMsg { get; set; }
        public string lightningArresterAppearanceMsg { get; set; }
        public string voiceMsg { get; set; }
        public string operationTestMsg { get; set; }
        public string infraredTestMsg { get; set; }
        public string contactorActionTestMsg { get; set; }
        public string lightningArresterTestMsg { get; set; }
        public evaluationResult evaluationResult { get; set; } = new evaluationResult();
        public LineChartModel CustomResult { get; set; } = new LineChartModel();
    }
}
