[{"Code": "TR_High_Ua", "Name": "TR_High_Ua", "GroupName": "Measurement", "AssetLevel": 31, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "V", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 1, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "TR_High_Ub", "Name": "TR_High_Ub", "GroupName": "Measurement", "AssetLevel": 31, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "V", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 2, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "TR_High_Uc", "Name": "TR_High_Uc", "GroupName": "Measurement", "AssetLevel": 31, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "V", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 3, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "TR_Low_Ua", "Name": "TR_Low_Ua", "GroupName": "Measurement", "AssetLevel": 31, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "V", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 4, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "TR_Low_Ub", "Name": "TR_Low_Ub", "GroupName": "Measurement", "AssetLevel": 31, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "V", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 5, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "TR_Low_Uc", "Name": "TR_Low_Uc", "GroupName": "Measurement", "AssetLevel": 31, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "V", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 6, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "TR_High_Ia", "Name": "TR_High_Ia", "GroupName": "Measurement", "AssetLevel": 31, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 7, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "TR_High_Ib", "Name": "TR_High_Ib", "GroupName": "Measurement", "AssetLevel": 31, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 8, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "TR_High_Ic", "Name": "TR_High_Ic", "GroupName": "Measurement", "AssetLevel": 31, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 9, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "TR_Low_Ia", "Name": "TR_Low_Ia", "GroupName": "Measurement", "AssetLevel": 31, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 10, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "TR_Low_Ib", "Name": "TR_Low_Ib", "GroupName": "Measurement", "AssetLevel": 31, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 11, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "TR_Low_Ic", "Name": "TR_Low_Ic", "GroupName": "Measurement", "AssetLevel": 31, "AssetType": null, "AssetModel": null, "FilterIds": "", "UdcCode": "", "Unit": "A", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": null, "SaveFunc": null, "Extend": "", "Sort": 12, "ParentName": "Measurement", "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "temperature_tr_phaseA", "Name": "temperature_tr_phaseA", "GroupName": "Temperature", "AssetLevel": 31, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "", "SaveFunc": null, "Extend": "", "Sort": 13, "ParentName": "Temperature", "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "temperature_tr_phaseB", "Name": "temperature_tr_phaseB", "GroupName": "Temperature", "AssetLevel": 31, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "", "SaveFunc": null, "Extend": "", "Sort": 14, "ParentName": "Temperature", "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}, {"Code": "temperature_tr_phaseC", "Name": "temperature_tr_phaseC", "GroupName": "Temperature", "AssetLevel": 31, "AssetType": null, "AssetModel": null, "FilterIds": "[T]", "UdcCode": "", "Unit": "℃", "CollectMode": "", "IsSystemStatus": false, "CanListen": true, "SaveToTable": "", "SaveFunc": null, "Extend": "", "Sort": 15, "ParentName": "Temperature", "CanAlarmListen": true, "CanChart": false, "CanPrint": true, "CanReportedData": true}]