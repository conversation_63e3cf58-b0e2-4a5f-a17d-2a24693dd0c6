﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Common.Job;
using Siemens.PanelManager.Common.UdcService;
using Siemens.PanelManager.Job.DeviceScan;
using Siemens.PanelManager.Model.Algorithm;
using Siemens.PanelManager.Model.Chart;
using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.Auth;
using Siemens.PanelManager.Model.Database.WorkOrder;
using Siemens.PanelManager.Model.WorkOrder;
using Siemens.PanelManager.Server.Algorithm;
using Siemens.PanelManager.Server.DataPoint;
using SqlSugar;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Job.MaintenanceReport
{
    public class MaintenanceReportJob : JobBase
    {
        private readonly ILogger<MaintenanceReportJob> _logger;
        private readonly IConfiguration _configuration;
        private readonly ISqlSugarClient _client;
        private readonly IServiceProvider _provider;

        private bool breakerNotHealth;

        public override string Name { get; } = "MaintenanceReportJob";

        public MaintenanceReportJob(ILogger<MaintenanceReportJob> logger,
            IConfiguration configuration,
            ISqlSugarClient client,
            IServiceProvider provider)
        {
            _logger = logger;
            _configuration = configuration;
            _client = client;
            _provider = provider;
        }

        public override async Task Execute()
        {
            try
            {
               var areaAndSubs = await _client.Queryable<AssetInfo>().Where(a => a.AssetLevel == AssetLevel.Area || a.AssetLevel == AssetLevel.Substation).ToListAsync();

                var customer = areaAndSubs.FirstOrDefault(a => a.AssetLevel == AssetLevel.Area);
                var subs = areaAndSubs.Where(a => a.AssetLevel == AssetLevel.Substation).ToList();
                foreach (var sub in subs) {
                    if (customer == null || sub == null)
                    {
                        return;
                    }

                    MaintenanceReportSnapshot reportSnapshot = new MaintenanceReportSnapshot();

                    reportSnapshot.CustomerName = customer.AssetName;
                    reportSnapshot.SubstationName = sub.AssetName;
                    reportSnapshot.GeneratedTime = DateTime.Now;
                    reportSnapshot.ReportName = $"{customer.AssetName}-{sub.AssetName}-{DateTime.Now.ToString("yyyy-MM-dd")}";

                    // 查询最后 alarm id
                    var alarmLog = await _client.Queryable<AlarmLog>().Where(a=>a.SubstationName==sub.AssetName).OrderByDescending(a => a.Id).FirstAsync();

                    reportSnapshot.LastAlarmId = alarmLog?.Id ?? 0;

                    var alarmOverview = await GetAlarmOverviewAsync(sub.AssetName);
                    reportSnapshot.AlarmOverview = alarmOverview;

                    await InitPieChartDataAsync(reportSnapshot,sub.Id);

                    var reportId = await _client.Insertable(reportSnapshot).ExecuteReturnIdentityAsync();

                    // 订货号列表和断路器健康
                    await AddOrdersAndBreakerHealthAsync(reportId);

                    await AddAlgorithmDataAsync(reportId,sub.Id);

                }

               
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "MaintenanceReportJob Execute Error.");
            }
        }

        private async Task<string> GetAlarmOverviewAsync(string subName)
        {
            var query = _client.Queryable<AlarmLog>()
                .Where(a => a.Status == AlarmLogStatus.New&&a.SubstationName==subName)
                .Where(a => a.EventType == AlarmEventType.Alarm || a.EventType == AlarmEventType.BreakerTrip || a.EventType == AlarmEventType.UdcAlarm);

            var tempQuery = query.Clone();
            var result = await tempQuery.GroupBy(l => l.Severity)
                .Select(l => new
                {
                    GroupColumn = l.Severity.ToString(),
                    TotalCount = SqlFunc.AggregateCount(l.Severity)
                }).ToArrayAsync();

            int lowCount = 0, middleCount = 0, highCount = 0;

            foreach (var item in result)
            {
                switch (item.GroupColumn)
                {
                    /*
                     *         Low = 0,
                     *         Middle = 10,
                     *         Hight = 20,
                     */
                    case "0":
                    case "Low":
                        lowCount = item.TotalCount;
                        break;
                    case "10":
                    case "Middle":
                        middleCount = item.TotalCount;
                        break;
                    case "20":
                    case "High":
                        highCount = item.TotalCount;
                        break;
                }
            }

            int totalCount = lowCount + middleCount + highCount;
            int newCount = 0, inProcessCount = 0, finishCount = 0, ignoreCount = 0;

            result = await query.GroupBy(l => l.Status)
                .Select(l => new
                {
                    GroupColumn = l.Status.ToString(),
                    TotalCount = SqlFunc.AggregateCount(l.Status)
                }).ToArrayAsync();

            foreach (var item in result)
            {
                switch (item.GroupColumn)
                {
                    /*
                     *  New = 0,
                     *  InProcess = 10,
                     *  Finish = 20,
                     *  Error = 99,
                     *  Ignore = -1
                     */
                    case "0":
                    case "New":
                        newCount = item.TotalCount;
                        break;
                    case "10":
                    case "InProcess":
                        inProcessCount = item.TotalCount;
                        break;
                    case "20":
                    case "Finish":
                        finishCount = item.TotalCount;
                        break;
                    case "-1":
                    case "Ignore":
                        ignoreCount = item.TotalCount;
                        break;
                }
            }

            return JsonConvert.SerializeObject(new { lowCount, middleCount, highCount, totalCount, newCount, inProcessCount, finishCount, ignoreCount });
        }

        private async Task InitPieChartDataAsync(MaintenanceReportSnapshot reportSnapshot,int subId)
        {
            var factory = _provider.GetRequiredService<IMessageContextFactory>();
            var messageContext = factory.GetMessageContext(factory.GetDefaultLanguage());

            var viewNames = new List<string>()
            {
                "ASSETREPLACEMENTPART",
                "BREAKERHEALTH"
            };

            var configs = await _client.Queryable<AssetDashboardConfig>().Where(c => viewNames.Contains(c.ConfigName)).ToArrayAsync();
            foreach (var config in configs)
            {
                if (config == null) continue;
                config.Sql = config.Sql.Replace("[[AssetId]]",subId.ToString());
                var data = await GetPieInfoAsync(config, messageContext);

                if (data == null) continue;

                switch (config.ConfigName)
                {
                    case "ASSETREPLACEMENTPART":
                        reportSnapshot.ReplacementPartChart = JsonConvert.SerializeObject(data);
                        break;
                    case "BREAKERHEALTH":
                        reportSnapshot.BreakerHealthChart = JsonConvert.SerializeObject(data);
                        break;
                }
            }
        }

        private async Task<List<PieInfo>> GetPieInfoAsync(AssetDashboardConfig config, IMessageContext messageContext)
        {
            if (string.IsNullOrEmpty(config.Sql)) return new List<PieInfo>();
            JObject extendObj;
            if (string.IsNullOrEmpty(config.Extend))
            {
                extendObj = new JObject();
            }
            else
            {
                try
                {
                    extendObj = JObject.Parse(config.Extend);
                }
                catch
                {
                    extendObj = new JObject();
                }
            }

            var pieList = await _client.Ado.SqlQueryAsync<PieInfo>(config.Sql);
            JToken? value;

            Func<string, string> typeNameMappings = (s) => s;
            if (extendObj.TryGetValue("TypeNameMappings", out value) && value != null && value is JObject valueObj)
            {
                try
                {
                    if (valueObj.ContainsKey("StaticKey"))
                    {
                        if (valueObj.TryGetValue("StaticKey", out var staticKeyToken) && staticKeyToken != null)
                        {
                            var staticKey = staticKeyToken.ToString();
                            typeNameMappings = (s) => messageContext.GetString($"{staticKey}_{s}") ?? $"{staticKey}_{s}";
                        }
                    }
                    else
                    {
                        var dicObj = valueObj.ToObject<Dictionary<string, string>>();

                        if (dicObj != null)
                        {
                            typeNameMappings = (s) => messageContext.GetString(dicObj[s]) ?? s;
                        }
                    }
                }
                catch
                {

                }
            }

            if (pieList == null) return new List<PieInfo>();
            var sum = pieList.Sum(t => t.Value.HasValue ? t.Value.Value : 0m);
            decimal pSum = 0m;

            for (var i = 0; i < pieList.Count; i++)
            {
                var pieInfo = pieList[i];
                if (!pieInfo.Value.HasValue)
                {
                    pieInfo.Value = 0m;
                    pieInfo.Percentage = 0m;
                    continue;
                }
                var p = Math.Round(pieInfo.Value.Value / sum, 4);

                if (pSum + p > 1)
                {
                    p = 1 - pSum;
                    pSum = 1;
                }
                else
                {
                    if (i == pieList.Count - 1)
                    {
                        p = 1 - pSum;
                    }
                    pSum += p;
                }

                pieInfo.Percentage = p * 100;
                pieInfo.Code = pieInfo.Name ?? string.Empty;
                pieInfo.Name = typeNameMappings(pieInfo.Name ?? string.Empty);

            }

            return pieList;
        }

        private async Task AddOrdersAndBreakerHealthAsync(int reportId)
        {
            var healthLevel = new List<string>
            {
                "attention",
                "maintain",
                "rushRepair"
            };

            var query = _client.Queryable<AssetInfo>()
                .InnerJoin<DeviceDetails>((a, ad) => a.Id == ad.AssetId)
                .Where((a, ad) => a.AssetLevel == AssetLevel.Device);

            var breakerHealth = await query.OrderBy((a, ad) => a.Id)
                .Select((a, ad) => new ReportSnapshotDetail
                {
                    AssetId = a.Id,
                    AssetName = a.AssetName,
                    AssetNumber = a.AssetNumber,
                    BuyNo = ad.MLFB,
                    Position = a.Location ?? string.Empty,
                    ReportId = reportId,
                    ReportType = SnapshotReportType.Breaker,
                    IndicatorInfo = ad.HealthLevel ?? string.Empty,
                }).ToListAsync();

            breakerNotHealth = breakerHealth != null && breakerHealth.Any(a => healthLevel.Contains(a.IndicatorInfo));

            var orderResult = await query.Where((a, ad) => a.MLFB != null && ad.HealthLevel != null && healthLevel.Contains(ad.HealthLevel))
                .OrderBy((a, ad) => a.Id)
                .Select((a, ad) => new ReportSnapshotDetail
                {
                    AssetId = a.Id,
                    AssetName = a.AssetName,
                    AssetNumber = a.AssetNumber,
                    BuyNo = ad.MLFB,
                    Position = a.Location ?? string.Empty,
                    ReportId = reportId,
                    ReportType = SnapshotReportType.Order
                }).ToListAsync();

            if (breakerHealth != null && breakerHealth.Any())
            {
                await _client.Insertable(breakerHealth).ExecuteCommandAsync();
            }

            if (orderResult != null && orderResult.Any())
            {
                await _client.Insertable(orderResult).ExecuteCommandAsync();
            }
        }

        private async Task AddAlgorithmDataAsync(int reportId,int subId)
        {
            try
            {
                var factory = _provider.GetRequiredService<IMessageContextFactory>();
                var messageContext = factory.GetMessageContext(factory.GetDefaultLanguage());

                int topoId = 0;
                var assetInfo = await _client.Queryable<AssetInfo>().Where(a =>a.Id== subId && a.AssetLevel == AssetLevel.Substation).FirstAsync();
                if (assetInfo != null && assetInfo.TopologyId.HasValue)
                {
                    topoId = (int)assetInfo.TopologyId;
                }

                var currentDateTime = DateTime.Now;
                var dateTime = new DateTime(currentDateTime.Year,
                    currentDateTime.Month,
                    currentDateTime.Day,
                    currentDateTime.Hour,
                    (currentDateTime.Minute / 15) * 15,
                    0).ToString("yyyy-MM-dd HH:mm:ss");

                Dictionary<string, object> requestBody = new Dictionary<string, object> { { "searchTime", dateTime }, { "topo", topoId } };

                LossDiagnose _lossDiagnose = _provider.GetRequiredService<LossDiagnose>();

                var indicatorList = new List<IndicatorType>
                    {
                        new IndicatorType
                        {
                            Level = "substation",
                            Value =  "normal"
                        },
                        new IndicatorType
                        {
                            Level = "panel",
                            Value = "normal"
                        },
                        new IndicatorType
                        {
                            Level = "circuit",
                            Value = "normal"
                        },
                        new IndicatorType
                        {
                            Level = "device",
                            Value = breakerNotHealth ? "exceptional" : "normal"
                        }
                    };

                try
                {
                    var response = await _lossDiagnose.GetSysLossdataApi(requestBody, "AlgorithmEvaluationApiPath", "searchCabinet");
                    var dataAPI = JsonConvert.DeserializeObject<CabinetHealthStatistics>(response);

                    if (dataAPI != null)
                    {
                        var toBeRepairedList = await _client.Queryable<AssetInfo>().Where(a => dataAPI.badAssetList.Contains(a.Id)).ToListAsync();
                        PieInfo pie1 = new PieInfo
                        {
                            Code = "excellent",
                            Name = messageContext.GetString("PanelHealth_excellent"),
                            Value = dataAPI.excellent,
                            Percentage = Math.Round(dataAPI.excellent / dataAPI.total, 2, MidpointRounding.AwayFromZero)
                        };
                        PieInfo pie2 = new PieInfo
                        {
                            Code = "good",
                            Name = messageContext.GetString("PanelHealth_good"),
                            Value = dataAPI.good,
                            Percentage = Math.Round(dataAPI.good / dataAPI.total, 2, MidpointRounding.AwayFromZero)
                        };
                        PieInfo pie3 = new PieInfo
                        {
                            Code = "medium",
                            Name = messageContext.GetString("PanelHealth_medium"),
                            Value = dataAPI.medium,
                            Percentage = Math.Round(dataAPI.medium / dataAPI.total, 2, MidpointRounding.AwayFromZero)
                        };
                        PieInfo pie4 = new PieInfo
                        {
                            Code = "bad",
                            Name = messageContext.GetString("PanelHealth_bad"),
                            Value = dataAPI.bad,
                            Percentage = Math.Round(dataAPI.bad / dataAPI.total, 2, MidpointRounding.AwayFromZero)
                        };

                        var pieInfos = new PieInfo[] { pie1, pie2, pie3, pie4 };

                        var panelNotHealth = toBeRepairedList != null && toBeRepairedList.Any();

                        foreach (var item in indicatorList)
                        {
                            if ((item.Level == "substation" || item.Level == "panel")
                                && panelNotHealth)
                            {
                                item.Value = "exceptional";
                            }
                        }

                        await _client.Updateable(new MaintenanceReportSnapshot
                        {
                            Id = reportId,
                            PanelHealthChart = JsonConvert.SerializeObject(pieInfos)
                        }).UpdateColumns(a => new { a.PanelHealthChart })
                        .ExecuteCommandAsync();

                        var panelHealth = toBeRepairedList.Select(a => new ReportSnapshotDetail
                        {
                            AssetId = a.Id,
                            AssetName = a.AssetName,
                            AssetNumber = a.AssetNumber,
                            Position = a.Location ?? string.Empty,
                            ReportId = reportId,
                            ReportType = SnapshotReportType.Panel,
                            IndicatorInfo = ""
                        }).ToList();

                        if (panelHealth != null && panelHealth.Any())
                        {
                            await _client.Insertable(panelHealth).ExecuteCommandAsync();
                        }
                    }
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "MaintenanceReportJob AddAlgorithmDataAsync GetSysLossdataApi Error.");
                }

                await _client.Updateable(new MaintenanceReportSnapshot
                {
                    Id = reportId,
                    IndicatorOverview = JsonConvert.SerializeObject(indicatorList),
                }).UpdateColumns(a => new { a.IndicatorOverview })
                .ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "MaintenanceReportJob AddAlgorithmDataAsync Error.");
            }
        }
    }
}
