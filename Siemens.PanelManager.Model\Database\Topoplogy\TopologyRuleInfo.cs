﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siemens.PanelManager.Model.Database.Topology
{
    [SugarTable("topoplogy_rule_info")]
    public class TopologyRuleInfo : LogicDataBase
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        [SugarColumn(ColumnName = "topology_id",IsNullable = false)]
        public int TopologyId { get; set; }
        [SugarColumn(ColumnName = "rule_type", IsNullable = true, Length = 50)]
        public string? RuleType { get; set; }
        [SugarColumn(ColumnName = "rule_code", IsNullable = false, Length = 50)]
        public string RuleCode { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "asset_id", IsNullable = true)]
        public int? AssetId { get; set; }
        [SugarColumn(ColumnName = "object_name", IsNullable = true, Length = 256)]
        public string? ObjectName { get; set; }
        [SugarColumn(ColumnName = "data_points", IsNullable = true)]
        public string DataPoint { get; set; }= string.Empty;
        [SugarColumn(ColumnName = "target_identify")]
        public string TargetIdentify { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "target_property")]
        public string TargetProperty { get; set; } = string.Empty;
        [SugarColumn(ColumnName = "format_function")]
        public string? FormatFunction { get; set; } = string.Empty;
    }
}
