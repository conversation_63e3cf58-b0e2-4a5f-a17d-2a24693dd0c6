﻿using InfluxDB.Client.Core;
using InfluxDB.Client.Core.Flux.Domain;
using Siemens.InfluxDB.Helper.Enum;
using Siemens.InfluxDB.Helper.Interface;
using System.Collections.ObjectModel;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;

namespace Siemens.InfluxDB.Helper.Database
{
    internal class SearchResultHelper<T> 
        where T : IInfluxData
    {
        private const string TimeColumnName = "_time";

        #region Init
        public SearchResultHelper() 
        {
            DataType = typeof(T);
            var properties = DataType.GetProperties();
            Columns = new Dictionary<string, Func<List<FluxTable>, int, object?>?>();
            var tags = new Dictionary<string, string>();
            var fields = new Dictionary<string, string>();

            foreach (var property in properties )
            {
                var attr = property.GetCustomAttribute<Column>();
                if (attr != null && attr.IsTimestamp)
                {
                    TimeColumn = property.Name;
                    Columns.Add(property.Name, (fluxs, index) => 
                    {
                        var flux = fluxs.FirstOrDefault();
                        if (flux != null) 
                        {
                            if (flux.Records.Count > index)
                            {
                                var record = flux.Records[index];
                                var value = record.GetValueByKey("_time");
                                return GetDbValue(value);
                            }
                        }
                        return null;
                    });
                }
                else if (attr != null && attr.IsTag)
                {
                    var dbName = attr.Name ?? property.Name;
                    tags.TryAdd(property.Name, dbName);
                    Columns.Add(property.Name, (fluxs, index) =>
                    {
                        var flux = fluxs.FirstOrDefault();
                        if (flux != null)
                        {
                            if (flux.Records.Count > index)
                            {
                                var record = flux.Records[index];
                                return GetDbValue(record.GetValueByKey(dbName));
                            }
                        }
                        return null;
                    });
                }
                else
                {
                    if (attr != null)
                    {
                        fields.TryAdd(property.Name, attr.Name ?? property.Name);
                    }
                    Columns.Add(property.Name, null);
                }
            }

            Tags = new ReadOnlyDictionary<string, string>(tags);
            Fields = new ReadOnlyDictionary<string, string>(fields);
        }

        private object GetDbValue(object value)
        {
            if (value is NodaTime.Instant instant)
            {
                return instant.ToDateTimeUtc().ToLocalTime();
            }
            return value;
        }

        public string TimeColumn { get; private set; } = "Time";
        public ReadOnlyDictionary<string, string> Tags { get; private set; }
        public ReadOnlyDictionary<string, string> Fields { get; private set; }
        public Type DataType { get; private set; }

        private Dictionary<string, Func<List<FluxTable>, int, object?>?> Columns { get; set; }
        #endregion

        public void SetColumnFunc(string dataName, GroupFunctionEnum groupFunction, string fieldName, object? defaultValue = null)
        {
            var funcName = groupFunction.GetGroupFunctionName();

            if (Columns.TryGetValue(dataName, out Func<List<FluxTable>, int, object?>? func))
            {
                func = (fluxs, index) =>
                {
                    var func = string.Empty;
                    foreach (var flux in fluxs)
                    {
                        if (flux.Records.Count > index)
                        {
                            var record = flux.Records[index];
                            var resultName = record.GetValueByKey("result");
                            if (!EnumExtend.DefaultResultCode.Equals(funcName) && !funcName.Equals(resultName)) continue;

                            resultName = record.GetValueByKey("_field");
                            if (!fieldName.Equals(resultName)) continue;

                            var value = record.GetValueByKey("_value");
                            value = GetDbValue(value);
                            return value ?? defaultValue;
                        }
                    }
                    return null;
                };

                Columns[dataName] = func;
            }
        }

        public List<T> GetValues(List<FluxTable> fluxTables)
        {
            #region Get Tag Columns
            FluxColumn[] tagColumns;
            int rowCount = 0;
            {
                var firstFlux = fluxTables.FirstOrDefault();
                if (firstFlux == null) 
                {
                    return new List<T>();
                }
                tagColumns = firstFlux.Columns.Where(c => !IsSystemColumn(c)).ToArray();
                rowCount = firstFlux.Records.Count;
            }
            #endregion

            var md5 = MD5.Create();
            Dictionary<string, List<FluxTable>> groupList = new Dictionary<string, List<FluxTable>>();
            {
                foreach (var fluxTable in fluxTables)
                {
                    var firstRecourd = fluxTable.Records.FirstOrDefault();
                    if (firstRecourd == null)
                    {
                        continue;
                    }
                    StringBuilder tagsValue = new StringBuilder();
                    foreach (var tag in tagColumns)
                    {
                        tagsValue.Append(firstRecourd.Row[tag.Index]);
                    }

                    var hash = Convert.ToBase64String(md5.ComputeHash(Encoding.UTF8.GetBytes(tagsValue.ToString())));
                    if (groupList.TryGetValue(hash, out List<FluxTable>? group))
                    {
                        group ??= new List<FluxTable>();
                        group.Add(fluxTable);
                        groupList[hash] = group;
                    }
                    else
                    {
                        groupList.Add(hash, new List<FluxTable>() { fluxTable });
                    }
                }
            }

            var results = new List<T>();
            Assembly assembly = DataType.Assembly;
            var properties = DataType.GetProperties();

            foreach (var key in groupList.Keys) 
            {
                var group = groupList[key];
                for (var i = 0; i < rowCount; i++)
                {
                    if (DataType.FullName == null) throw new InfluxDBHelperException($"{DataType.Name} 创建对象失败");
                    var obj = assembly.CreateInstance(DataType.FullName);
                    if (obj == null) throw new InfluxDBHelperException($"{DataType.Name} 创建对象失败");
                    foreach (var p in properties)
                    {
                        if (Columns.TryGetValue(p.Name, out var function) && function != null)
                        {
                            var valueObj = function(group, i);
                            if (valueObj != null)
                            {
                                p.SetValue(obj, valueObj);
                            }
                        }
                    }
                    results.Add((T)obj);
                }


            }

            return results;
        }

        private bool IsSystemColumn(FluxColumn column)
        {
            return column.Label == "result"
                || column.Label == "table"
                || column.Label == "_start"
                || column.Label == "_stop"
                || column.Label == "_field"
                || column.Label == "_measurement"
                || column.Label == "_value"
                || column.Label == TimeColumnName;
        }
    }
}
