{"info": {"_postman_id": "8e3f491a-4011-4065-8b56-9fa0b9a4651c", "name": "08使用管理员账号进入panel manager资产管理中的资产列表菜单，点击资产列表右边设备导出按钮", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "导出资产数据 Copy", "event": [{"listen": "test", "script": {"exec": ["console.log (postman.getResponseHeader(\"Content-Disposition\"))\r", "\r", "\r", "// pm.test(\"Content-Type is present\", function () {\r", "//     pm.response.to.have.header(\"Content-Disposition\");\r", "// });\r", "\r", "\r", "pm.test(\"文件以xlsx格式输出\", function () {\r", "    var test = postman.getResponseHeader(\"Content-Disposition\");\r", "    pm.expect(test).to.include(\"filename=____.xlsx\");\r", "});\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Asset/DownloadExcel", "host": ["{{baseUrl}}"], "path": ["api", "v1", "<PERSON><PERSON>", "DownloadExcel"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}]}