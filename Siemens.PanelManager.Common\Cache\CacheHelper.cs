﻿using Microsoft.Extensions.Caching.Memory;
using Quartz.Util;
using Siemens.PanelManager.Common.Log;
using System.Collections.Concurrent;
using System.Data;
using System.Diagnostics.CodeAnalysis;

namespace Siemens.PanelManager.Common.Cache
{
    static class CacheHelper
    {
        private static StringConverter _stringConverter = new StringConverter();
        private static object _keyLock = new object();
        private static IMemoryCache _cache = new MemoryCache(new MemoryCacheOptions());
        private static ConcurrentQueue<string> _keys = new ConcurrentQueue<string>();

        private static ConcurrentDictionary<string, ConcurrentQueue<string>> _hashKeys = new ConcurrentDictionary<string, ConcurrentQueue<string>>();

        private static RunTimeLogger _runTimeLogger = new RunTimeLogger("CacheHelper", 100);

        #region Simple Key
        /// <summary>
        /// Action:
        ///     0 不处理
        ///     1 添加
        ///     2 删除
        /// </summary>
        /// <param name="key"></param>
        /// <param name="action"></param>
        /// <returns></returns>
        private static string SimpleKey(string key, int action = 1)
        {
            string simpleKey = $"S[{key.ToLower()}]";
            //switch (action)
            //{

            //    case 1:
            //        lock (_keyLock)
            //        {
            //            if (!_keys.Contains(key))
            //            {
            //                _keys.Enqueue(key);
            //            }
            //        }
            //        break;
            //    case 2:
            //        lock (_keyLock)
            //        {
            //            if (_keys.Contains(key))
            //            {
            //                while (_keys.TryDequeue(out var existsKey))
            //                {
            //                    if (key.Equals(existsKey))
            //                    {
            //                        break;
            //                    }
            //                    else
            //                    {
            //                        _keys.Enqueue(existsKey);
            //                    }
            //                }
            //            }
            //        }
            //        break;
            //    case 0:
            //    default:
            //        break;
            //}

            return simpleKey;
        }

        public static T? Get<T>([DisallowNull] string key)
        {
            var k = _runTimeLogger.Start();
            key = SimpleKey(key);
            _cache.TryGetValue<T>(key, out T? value);
            _runTimeLogger.Stop(k);

            return value;
        }

        public static bool Set<T>([DisallowNull] string key, T value, TimeSpan? timeSpan = null)
        {
            if (value == null) return false;
            var k = _runTimeLogger.Start();
            key = SimpleKey(key);
            if (timeSpan == null)
            {
                _cache.Set(key, value);
            }
            else
            {
                _cache.Set(key, value,timeSpan.Value);
            }
            _runTimeLogger.Stop(k);

            return true;
        }

        public static bool SetBySlip<T>([DisallowNull] string key, T value, TimeSpan timeSpan)
        {
            if (value == null) return false;
            var k = _runTimeLogger.Start();
            key = SimpleKey(key);
            _cache.Set(key, value, new MemoryCacheEntryOptions()
            {
                SlidingExpiration = timeSpan
            });
            _runTimeLogger.Stop(k);

            return true;
        }

        #region Key Manager

        public static string[] GetSimpleKeys()
        {
            return _keys.ToArray();
        }
        #endregion
        public static void Remove([DisallowNull] string key)
        {
            var k = _runTimeLogger.Start();
            key = SimpleKey(key, 2);
            _cache.Remove(key);
            _runTimeLogger.Stop(k);
        }

        public static void Remove<T>([DisallowNull] string key, out T? data)
        {
            var k = _runTimeLogger.Start();
            key = SimpleKey(key, 2);
            if (_cache.TryGetValue<T>(key, out data))
            {
                _cache.Remove(key);
            }
            _runTimeLogger.Stop(k);
        }

        public static bool ContainsKey([DisallowNull] string key)
        {
            key = SimpleKey(key, 0);
            return _cache.TryGetValue(key, out _);
        }

        public static T GetOrCreate<T>([DisallowNull] string key, Func<T> createFunc, bool needAddKey = false, TimeSpan? timeSpan = null)
        {
            var k = _runTimeLogger.Start();
            var dataKey = SimpleKey(key);

            var value = _cache.GetOrCreate<T>(dataKey, (entry) =>
            {
                var item = createFunc();
                if (item != null)
                {
                    entry = entry.SetValue(item);
                    if (timeSpan != null)
                    {
                        entry.SetAbsoluteExpiration(timeSpan.Value);
                    }
                }

                return item;
            });
            _runTimeLogger.Stop(k);

            return value;
        }

        public static async Task<T> GetOrCreateAsync<T>([DisallowNull] string key, Func<Task<T>> createFunc, bool needAddKey = false, TimeSpan? timeSpan = null)
        {
            var k = _runTimeLogger.Start();
            var dataKey = SimpleKey(key);

            var value = await _cache.GetOrCreateAsync<T>(dataKey, async (entry) =>
            {
                var item = await createFunc();
                if (item != null)
                {
                    entry = entry.SetValue(item);
                    if (timeSpan != null)
                    {
                        entry.SetAbsoluteExpiration(timeSpan.Value);
                    }
                }

                return item;
            });
            _runTimeLogger.Stop(k);

            return value;
        }
        #endregion

        #region Hash Table

        private static string GetMainKey(string key)
        { 
            return $"H[{key.ToLower()}]";
        }

        /// <summary>
        /// Action:
        ///     0 不处理
        ///     1 添加
        ///     2 删除
        /// </summary>
        /// <param name="key"></param>
        /// <param name="subKey"></param>
        /// <param name="action"></param>
        /// <returns></returns>
        private static string HashTableKey(string key, string subKey, int action = 1)
        {
            string mainKey = GetMainKey(key);
            string hashKey = $"{mainKey}[{subKey}]";

            switch (action)
            {
                case 0:
                    break;
                case 1:
                    #region 添加
                    {
                        if (_hashKeys.TryGetValue(key, out var subKeys))
                        {
                            if (!subKeys.Contains(subKey))
                            {
                                subKeys.Enqueue(subKey);
                            }
                        }
                        else
                        {
                            subKeys = new ConcurrentQueue<string>();
                            subKeys.Enqueue(subKey);
                            _hashKeys.TryAdd(key, subKeys);
                        }
                    }
                    #endregion
                    break;
                case 2:
                    #region 删除
                    {
                        if (_hashKeys.TryGetValue(key, out var subKeys))
                        {
                            if (subKeys.Contains(subKey))
                            {
                                while (subKeys.TryDequeue(out var existsKey))
                                {
                                    if (subKey.Equals(existsKey))
                                    {
                                        break;
                                    }
                                    else
                                    {
                                        subKeys.Enqueue(existsKey);
                                    }
                                }

                                if (subKeys.Count == 0)
                                {
                                    _hashKeys.TryRemove(key, out _);
                                }
                            }
                        }
                    }
                    #endregion
                    break;
                default: break;
            }

            return hashKey;
        }

        /// <summary>
        /// 获取Hash Keys
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public static string[] GetHashKeys(string key)
        {
            var k = _runTimeLogger.Start();
            var r = _hashKeys.Keys.ToArray();
            _runTimeLogger.Stop(k);
            return r;
        }

        /// <summary>
        /// 验证 Hash 键
        /// </summary>
        /// <param name="key"></param>
        /// <param name="hashKey"></param>
        /// <returns></returns>
        public static bool CheckHashKey(string key, string hashKey)
        {
            var dataKey = HashTableKey(key, hashKey, 0);
            if (_cache.TryGetValue(dataKey, out _))
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 获取 Hash Table所有值
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public static Dictionary<string, T> GetHashAll<T>(string key)
        {
            var k = _runTimeLogger.Start();
            var result = new Dictionary<string, T>();
            if (_hashKeys.TryGetValue(key, out var subKeys) && subKeys != null)
            {
                var keyList = subKeys.ToArray();
                foreach (var subKey in keyList)
                {
                    var dataKey = HashTableKey(key, subKey, 0);
                    var dataValue = _cache.Get<T>(dataKey);
                    if (dataValue != null)
                    {
                        result.Add(subKey, dataValue);
                    }
                }
            }

            _runTimeLogger.Stop(k);

            return result;
        }

        public static Dictionary<string, T> GetHashValues<T>(string key, string[] hashKeys)
        {
            var k = _runTimeLogger.Start();
            var result = new Dictionary<string, T>();
            foreach (var subKey in hashKeys)
            {
                var dataKey = HashTableKey(key, subKey, 0);
                var dataValue = _cache.Get<T>(dataKey);
                if (dataValue != null)
                {
                    result.Add(subKey, dataValue);
                }
            }

            _runTimeLogger.Stop(k);

            return result;
        }

        /// <summary>
        /// 添加过期时间
        /// </summary>
        /// <param name="key"></param>
        /// <param name="values"></param>
        /// <param name="times"></param>
        public static void SetHashValues<T>(string key, Dictionary<string, T> values, double times = 0)
        {
            var k = _runTimeLogger.Start();
            foreach (var item in values)
            {
                var dataKey = HashTableKey(key, item.Key, 1);
                if (times == 0)
                {
                    _cache.Set(dataKey, item.Value);
                }
                else
                {
                    _cache.Set(dataKey, item.Value, TimeSpan.FromSeconds(times));
                }
            }

            _runTimeLogger.Stop(k);
        }

        public static void RemoveHashData(string key, string hashKey)
        {
            var k = _runTimeLogger.Start();
            var dataKey = HashTableKey(key, hashKey, 2);
            _cache.Remove(dataKey);

            _runTimeLogger.Stop(k);
        }

        public static void RemoveHashAllData(string key)
        {
            if (_hashKeys.TryRemove(key, out var subKeys) && subKeys != null)
            {
                while (subKeys.TryDequeue(out var subKey))
                {
                    var dataKey = HashTableKey(key, subKey, 0);
                    _cache.Remove(dataKey);
                }
            }
        }
        #endregion
    }

    internal class StringConverter : IEqualityComparer<string>
    {
        public bool Equals(string? x, string? y)
        {
            return string.Equals(x, y, StringComparison.OrdinalIgnoreCase);
        }

        public int GetHashCode([DisallowNull] string obj)
        {
            return obj.GetHashCode();
        }
    }
}
