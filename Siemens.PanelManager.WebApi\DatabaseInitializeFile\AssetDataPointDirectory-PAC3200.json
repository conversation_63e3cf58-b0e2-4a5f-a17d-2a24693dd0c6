[{"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "VoltageL-N", "ParentName": "", "LanguageKey": "VoltageL-N", "Sort": 1}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "VoltageL-N_ActualInstantaneousMeasurementValues", "ParentName": "VoltageL-N", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 2}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "VoltageL-N_GreatestMeasuredValues", "ParentName": "VoltageL-N", "LanguageKey": "GreatestMeasuredValues", "Sort": 3}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "VoltageL-N_LowestMeasuredValues", "ParentName": "VoltageL-N", "LanguageKey": "LowestMeasuredValues", "Sort": 4}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "VoltageL-L", "ParentName": "", "LanguageKey": "VoltageL-L", "Sort": 5}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "VoltageL-L_ActualInstantaneousMeasurementValues", "ParentName": "VoltageL-L", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 6}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "VoltageL-L_GreatestMeasuredValues", "ParentName": "VoltageL-L", "LanguageKey": "GreatestMeasuredValues", "Sort": 7}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "VoltageL-L_LowestMeasuredValues", "ParentName": "VoltageL-L", "LanguageKey": "LowestMeasuredValues", "Sort": 8}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "Current", "ParentName": "", "LanguageKey": "Current", "Sort": 9}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "Current_ActualInstantaneousMeasurementValues", "ParentName": "Current", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 10}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "Current_GreatestMeasuredValues", "ParentName": "Current", "LanguageKey": "GreatestMeasuredValues", "Sort": 11}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "Current_LowestMeasuredValues", "ParentName": "Current", "LanguageKey": "LowestMeasuredValues", "Sort": 12}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "Power", "ParentName": "", "LanguageKey": "Power", "Sort": 13}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "ActivePower", "ParentName": "Power", "LanguageKey": "ActivePower", "Sort": 14}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "InstantaneousValuesActivePower", "ParentName": "ActivePower", "LanguageKey": "InstantaneousValuesActivePower", "Sort": 15}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "InstantaneousValuesActivePower_ActualInstantaneousMeasurementValues", "ParentName": "InstantaneousValuesActivePower", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 16}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "InstantaneousValuesActivePower_GreatestMeasuredValues", "ParentName": "InstantaneousValuesActivePower", "LanguageKey": "GreatestMeasuredValues", "Sort": 17}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "InstantaneousValuesActivePower_LowestMeasuredValues", "ParentName": "InstantaneousValuesActivePower", "LanguageKey": "LowestMeasuredValues", "Sort": 18}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "MeanValuesActivePower", "ParentName": "ActivePower", "LanguageKey": "MeanValuesActivePower", "Sort": 19}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "MeanValuesActivePower_MaximumValuesInMeasuringPeriod", "ParentName": "MeanValuesActivePower", "LanguageKey": "MaximumValuesInMeasuringPeriod", "Sort": 20}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "MeanValuesActivePower_MinimumValuesInMeasuringPeriod", "ParentName": "MeanValuesActivePower", "LanguageKey": "MinimumValuesInMeasuringPeriod", "Sort": 21}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "CumulatedActivePower", "ParentName": "ActivePower", "LanguageKey": "CumulatedActivePower", "Sort": 22}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "CumulatedActivePowerImport", "ParentName": "CumulatedActivePower", "LanguageKey": "CumulatedActivePowerImport", "Sort": 23}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "CumulatedActivePowerExport", "ParentName": "CumulatedActivePower", "LanguageKey": "CumulatedActivePowerExport", "Sort": 24}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "ReactivePower", "ParentName": "Power", "LanguageKey": "ReactivePower", "Sort": 25}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "MeasuringMethodVARn", "ParentName": "ReactivePower", "LanguageKey": "MeasuringMethodVARn", "Sort": 26}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "MeasuringMethodVARn_ActualInstantaneousMeasurementValues", "ParentName": "MeasuringMethodVARn", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 27}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "MeasuringMethodVARn_GreatestMeasuredValues", "ParentName": "MeasuringMethodVARn", "LanguageKey": "GreatestMeasuredValues", "Sort": 28}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "MeasuringMethodVARn_LowestMeasuredValues", "ParentName": "MeasuringMethodVARn", "LanguageKey": "LowestMeasuredValues", "Sort": 29}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "MeanValuesReactivePower", "ParentName": "ReactivePower", "LanguageKey": "MeanValuesReactivePower", "Sort": 30}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "MeanValuesReactivePower_MaximumValuesInMeasuringPeriod", "ParentName": "MeanValuesReactivePower", "LanguageKey": "MaximumValuesInMeasuringPeriod", "Sort": 31}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "MeanValuesReactivePower_MinimumValuesInMeasuringPeriod", "ParentName": "MeanValuesReactivePower", "LanguageKey": "MinimumValuesInMeasuringPeriod", "Sort": 32}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "CumulatedReactivePower", "ParentName": "ReactivePower", "LanguageKey": "CumulatedReactivePower", "Sort": 33}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "CumulatedReactivePowerImport", "ParentName": "CumulatedReactivePower", "LanguageKey": "CumulatedReactivePowerImport", "Sort": 34}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "CumulatedReactivePowerExport", "ParentName": "CumulatedReactivePower", "LanguageKey": "CumulatedReactivePowerExport", "Sort": 35}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ParentName": "Power", "LanguageKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Sort": 36}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "ApparentPower_ActualInstantaneousMeasurementValues", "ParentName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 37}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "ApparentPower_GreatestMeasuredValues", "ParentName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LanguageKey": "GreatestMeasuredValues", "Sort": 38}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "ApparentPower_LowestMeasuredValues", "ParentName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LanguageKey": "LowestMeasuredValues", "Sort": 39}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "LoadManagement", "ParentName": "Power", "LanguageKey": "LoadManagement", "Sort": 40}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "CommonDataPowerMeasurementOfTheLastPeriod", "ParentName": "Power", "LanguageKey": "CommonDataPowerMeasurementOfTheLastPeriod", "Sort": 41}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "PowerFactor", "ParentName": "Power", "LanguageKey": "PowerFactor", "Sort": 42}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "PowerFactor_ActualInstantaneousMeasurementValues", "ParentName": "PowerFactor", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 43}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "PowerFactor_GreatestMeasuredValues", "ParentName": "PowerFactor", "LanguageKey": "GreatestMeasuredValues", "Sort": 44}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "PowerFactor_LowestMeasuredValues", "ParentName": "PowerFactor", "LanguageKey": "LowestMeasuredValues", "Sort": 45}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "Energy", "ParentName": "", "LanguageKey": "Energy", "Sort": 46}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "ActiveEnergy", "ParentName": "Energy", "LanguageKey": "ActiveEnergy", "Sort": 47}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "ActiveEnergy_Import", "ParentName": "ActiveEnergy", "LanguageKey": "Import", "Sort": 48}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "ActiveEnergyImportTariff1", "ParentName": "ActiveEnergy_Import", "LanguageKey": "ActiveEnergyImportTariff1", "Sort": 49}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "ActiveEnergyImportTariff2", "ParentName": "ActiveEnergy_Import", "LanguageKey": "ActiveEnergyImportTariff2", "Sort": 50}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "ActiveEnergy_Export", "ParentName": "ActiveEnergy", "LanguageKey": "Export", "Sort": 51}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "ActiveEnergyExportTariff1", "ParentName": "ActiveEnergy_Export", "LanguageKey": "ActiveEnergyExportTariff1", "Sort": 52}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "ActiveEnergyExportTariff2", "ParentName": "ActiveEnergy_Export", "LanguageKey": "ActiveEnergyExportTariff2", "Sort": 53}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "ReactiveEnergy", "ParentName": "Energy", "LanguageKey": "ReactiveEnergy", "Sort": 54}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "ReactiveEnergy_Import", "ParentName": "ReactiveEnergy", "LanguageKey": "Import", "Sort": 55}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "TotalReactiveEnergyImportTariff1", "ParentName": "ReactiveEnergy_Import", "LanguageKey": "TotalReactiveEnergyImportTariff1", "Sort": 56}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "TotalReactiveEnergyImportTariff2", "ParentName": "ReactiveEnergy_Import", "LanguageKey": "TotalReactiveEnergyImportTariff2", "Sort": 57}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "ReactiveEnergy_Export", "ParentName": "ReactiveEnergy", "LanguageKey": "Export", "Sort": 58}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "TotalReactiveEnergyExportTariff1", "ParentName": "ReactiveEnergy_Export", "LanguageKey": "TotalReactiveEnergyExportTariff1", "Sort": 59}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "TotalReactiveEnergyExportTariff2", "ParentName": "ReactiveEnergy_Export", "LanguageKey": "TotalReactiveEnergyExportTariff2", "Sort": 60}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "ApparentEnergy", "ParentName": "Energy", "LanguageKey": "ApparentEnergy", "Sort": 61}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "ApparentEnergyTariff1", "ParentName": "ApparentEnergy", "LanguageKey": "ApparentEnergyTariff1", "Sort": 62}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "ApparentEnergyTariff2", "ParentName": "ApparentEnergy", "LanguageKey": "ApparentEnergyTariff2", "Sort": 63}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "GlobalValues", "ParentName": "", "LanguageKey": "GlobalValues", "Sort": 64}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "GlobalValues_ActualInstantaneousMeasurementValues", "ParentName": "GlobalValues", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 65}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "GlobalValues_GreatestMeasuredValues", "ParentName": "GlobalValues", "LanguageKey": "GreatestMeasuredValues", "Sort": 66}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "GlobalValues_LowestMeasuredValues", "ParentName": "GlobalValues", "LanguageKey": "LowestMeasuredValues", "Sort": 67}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "ThreePhaseSystem", "ParentName": "", "LanguageKey": "ThreePhaseSystem", "Sort": 68}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "Counter", "ParentName": "", "LanguageKey": "Counter", "Sort": 69}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "CostManagement", "ParentName": "", "LanguageKey": "CostManagement", "Sort": 70}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "HarmonicDistortion", "ParentName": "", "LanguageKey": "HarmonicDistortion", "Sort": 71}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "THDCurrent", "ParentName": "HarmonicDistortion", "LanguageKey": "THDCurrent", "Sort": 72}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "THDCurrent_ActualInstantaneousMeasurementValues", "ParentName": "THDCurrent", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 73}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "THDCurrent_GreatestMeasuredValues", "ParentName": "THDCurrent", "LanguageKey": "GreatestMeasuredValues", "Sort": 74}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "THDVoltageL-N", "ParentName": "HarmonicDistortion", "LanguageKey": "THDVoltageL-N", "Sort": 75}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "THDVoltageL-N_ActualInstantaneousMeasurementValues", "ParentName": "THDVoltageL-N", "LanguageKey": "ActualInstantaneousMeasurementValues", "Sort": 76}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "THDVoltageL-N_GreatestMeasuredValues", "ParentName": "THDVoltageL-N", "LanguageKey": "GreatestMeasuredValues", "Sort": 77}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "GlobalDeviceDiagnostics", "ParentName": "", "LanguageKey": "GlobalDeviceDiagnostics", "Sort": 78}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "LocalDeviceStatus", "ParentName": "", "LanguageKey": "LocalDeviceStatus", "Sort": 79}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "GlobalDeviceStatus", "ParentName": "", "LanguageKey": "GlobalDeviceStatus", "Sort": 80}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "InputStatus", "ParentName": "", "LanguageKey": "InputStatus", "Sort": 81}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "OutputStatus", "ParentName": "", "LanguageKey": "OutputStatus", "Sort": 82}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "LimitMonitoring", "ParentName": "", "LanguageKey": "LimitMonitoring", "Sort": 83}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "Warnings", "ParentName": "", "LanguageKey": "Warnings", "Sort": 84}, {"AssetLevel": 50, "AssetModel": "PAC3200", "Name": "Others", "ParentName": "", "LanguageKey": "Others", "Sort": 85}]