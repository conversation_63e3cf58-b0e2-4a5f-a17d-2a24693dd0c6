{"info": {"_postman_id": "2120c2b7-dd7a-4acb-b894-6e38225a5c5d", "name": "W05使用超级管理员账号进入panel manager运维工单页面，点击修改工单，修改设备、工单类型、处理时间、处理措施、故障原因、工单内容", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26190232"}, "item": [{"name": "用户登录 Copy 7", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"响应体包含token\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"token\");\r", "});//响应体是否包含token\r", "\r", "let token = responseBody.match('\"token\":\"(.*?)\"')//获取token\r", "pm.environment.set('token',token[1])//把token保存到全局变量中\r", "console.log (token)\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userName\": \"admin\",\r\n  \"password\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "login"]}}, "response": []}, {"name": "获取所有资产详情 Copy 8", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "let Deviceid2 = pm.response.json().data[0].children[0].children[0].children[0].children[0].id//获取Areaid\r", "pm.environment.set('Deviceid2',Deviceid2)//把id保存到全局变量中\r", "\r", "pm.test(\"获取所有资产详情\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"id\",\"assetName\",\"assetNumber\",\"level\",\"location\",\"children\",\"type\",\"model\");\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/Asset", "host": ["{{baseUrl}}"], "path": ["api", "v1", "<PERSON><PERSON>"]}}, "response": []}, {"name": "获取工单列表详情 Copy 4", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为20000\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"展示工单列表详情\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"workOrderCode\");\r", "    pm.expect(pm.response.text()).to.include(\"deviceName\");\r", "    pm.expect(pm.response.text()).to.include(\"workOrderTypeName\");\r", "    pm.expect(pm.response.text()).to.include(\"content\");\r", "    pm.expect(pm.response.text()).to.include(\"processingTime\");\r", "    pm.expect(pm.response.text()).to.include(\"measureName\");\r", "    pm.expect(pm.response.text()).to.include(\"statusName\");\r", "    pm.expect(pm.response.text()).to.include(\"createdTime\");\r", "});\r", "\r", "let w1 = pm.response.json().items[0].id\r", "pm.environment.set(\"w1\", w1);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/workOrder/getWorkOrderList?pageSize=10&page=1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "workOrder", "getWorkOrderList"], "query": [{"key": "pageSize", "value": "10"}, {"key": "page", "value": "1"}]}}, "response": []}, {"name": "编辑工单Copy Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"code码为40300\", function () {\r", "    var jsonData = pm.response.json();\r", "    pm.expect(jsonData[\"code\"]).to.eql(20000);\r", "});\r", "\r", "pm.test(\"编辑工单成功\", function () {\r", "    pm.expect(pm.response.text()).to.include(\"success\");\r", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"id\": {{w1}},\r\n  \"device\": \"{{Deviceid2}}\",\r\n  \"workOrderType\": \"General\",\r\n  \"content\": \"string\",\r\n  \"faultCause\": \"string\",\r\n  \"processingTime\": \"2023-05-09T07:32:16.796Z\",\r\n  \"measure\": \"Check\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/workOrder/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "workOrder", ""]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});//状态码是否为200", "pm.test(\"Response time is less than 500ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(500);", "});"]}}]}