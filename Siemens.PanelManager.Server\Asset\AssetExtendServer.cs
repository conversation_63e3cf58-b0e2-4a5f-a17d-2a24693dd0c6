﻿using InfluxDB.Client.Api.Domain;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using Siemens.PanelManager.Common.Cache;
using Siemens.PanelManager.Common.General;
using Siemens.PanelManager.Interface.ActorRefs;
using Siemens.PanelManager.Model.CacheModel;
using Siemens.PanelManager.Model.Database.Alarm;
using Siemens.PanelManager.Model.Database.Asset;
using Siemens.PanelManager.Model.Database.System;
using Siemens.PanelManager.Model.Database.Topology;
using Siemens.PanelManager.Server.Asset.Model;
using Siemens.PanelManager.Server.DataPoint;
using Siemens.PanelManager.Server.Topology;
using SqlSugar;
using System.Text.RegularExpressions;

namespace Siemens.PanelManager.Server.Asset
{
    public class AssetExtendServer
    {

        private readonly ILogger _logger;
        private readonly IServiceProvider _provider;
        private const string AssetCurrentStatusCacheKey = "AssetStatus:Currently-{0}";
        private const string AssetEnableMqttCacheKey = "Asset:EnableMqtt-{0}";

        public AssetExtendServer(IServiceProvider provider, ILogger<AssetExtendServer> logger)
        {
            _logger = logger;
            _provider = provider;
        }

        public async Task<bool> GetAssetEnableStatus(int assetId)
        {
            var time = Random.Shared.Next(1200, 3000);
            var cache = _provider.GetRequiredService<SiemensCache>();
            var enable = await cache.GetOrCreateAsync<bool>(string.Format(AssetEnableMqttCacheKey, assetId), async () =>
            {
                using (var client = _provider.GetRequiredService<ISqlSugarClient>())
                {
                    var enableMqtt = await client.Queryable<AssetInfo>().Where(a => a.Id == assetId).Select(a => a.EnableMqtt).FirstAsync() ?? false;
                    return enableMqtt;
                }
            }, TimeSpan.FromSeconds(time));

            return enable;
        }

        public async Task UpdateAssetEnableMqtt(int assetId, bool enable, string user)
        {
            using (var client = _provider.GetRequiredService<ISqlSugarClient>())
            {
                var asset = await client.Queryable<AssetInfo>().FirstAsync(a => a.Id == assetId);
                if (asset == null) return;

                await UpdateAssetEnableMqtt(asset, enable, user, client);
            }
        }

        public async Task UpdateAssetEnableMqtt(AssetInfo asset, bool enable, string user, ISqlSugarClient? client = null)
        {
            var time = Random.Shared.Next(1200, 3000);

            if (client == null)
            {
                client = _provider.GetRequiredService<ISqlSugarClient>();
            }

            if (asset.EnableMqtt != enable)
            {
                asset.EnableMqtt = enable;
                asset.UpdatedBy = user;
                asset.UpdatedTime = DateTime.Now;
                await client.Updateable(asset).UpdateColumns(a=>
                new 
                {
                    a.UpdatedBy,
                    a.UpdatedTime,
                    a.EnableMqtt
                }).ExecuteCommandAsync();
            }

            var cache = _provider.GetRequiredService<SiemensCache>();
            cache.Set(string.Format(AssetEnableMqttCacheKey, asset.Id), enable, TimeSpan.FromSeconds(time));
        }

        public void RemoveAssetEnableMqttCache(int[] assetIds)
        {
            var cache = _provider.GetRequiredService<SiemensCache>();
            foreach (var assetId in assetIds)
            {
                cache.Clear(string.Format(AssetEnableMqttCacheKey, assetId));
            }
        }

        public async Task AddPannelSystemHealthCheckList(AssetInfo assetInfo, ISqlSugarClient client)
        {

            if (assetInfo == null || assetInfo.Id <= 0 || assetInfo.AssetLevel != AssetLevel.Panel)
                return;

            var panelHealthCheckList = await client.Queryable<PanelHealth>().Where(p => p.AssetId == assetInfo.Id && p.IsSystem).ToArrayAsync();

            var insertList = new List<PanelHealth>();
            insertList.Add(new PanelHealth()
            {
                Name = "本柜最高温度测点(℃)",
                Code = "MaxTemperature",
                AssetId = assetInfo.Id,
                Limit = "[0,100]",
                Value = 100,
                Weight = 60,
                IsSystem = true,
                CreatedBy = assetInfo.UpdatedBy,
                CreatedTime = DateTime.Now,
                UpdatedBy = assetInfo.UpdatedBy,
                UpdatedTime = DateTime.Now,
            });
            insertList.Add(new PanelHealth()
            {
                Name = "本柜最高电流测点(A)",
                Code = "MaxElectricity",
                AssetId = assetInfo.Id,
                Limit = "[0,100]",
                Value = 100,
                Weight = 60,
                IsSystem = true,
                CreatedBy = assetInfo.UpdatedBy,
                CreatedTime = DateTime.Now,
                UpdatedBy = assetInfo.UpdatedBy,
                UpdatedTime = DateTime.Now,
            });
            insertList.Add(new PanelHealth()
            {
                Name = "本柜断路器剩余寿命(%)",
                Code = "RemainingLife",
                AssetId = assetInfo.Id,
                Limit = "[0,100]",
                Value = 100,
                Weight = 60,
                IsSystem = true,
                CreatedBy = assetInfo.UpdatedBy,
                CreatedTime = DateTime.Now,
                UpdatedBy = assetInfo.UpdatedBy,
                UpdatedTime = DateTime.Now,
            });
            insertList.Add(new PanelHealth()
            {
                Name = "本柜告警次数",
                Code = "AlarmCount",
                AssetId = assetInfo.Id,
                Limit = "[0,100]",
                Value = 100,
                Weight = 60,
                IsSystem = true,
                CreatedBy = assetInfo.UpdatedBy,
                CreatedTime = DateTime.Now,
                UpdatedBy = assetInfo.UpdatedBy,
                UpdatedTime = DateTime.Now,
            });
            insertList.Add(new PanelHealth()
            {
                Name = "本柜高级告警次数",
                Code = "HighLevelAlarmCount",
                AssetId = assetInfo.Id,
                Limit = "[0,100]",
                Value = 100,
                Weight = 60,
                ParentCode = "AlarmCount",
                IsSystem = true,
                CreatedBy = assetInfo.UpdatedBy,
                CreatedTime = DateTime.Now,
                UpdatedBy = assetInfo.UpdatedBy,
                UpdatedTime = DateTime.Now,
            });
            insertList.Add(new PanelHealth()
            {
                Name = "本柜中级告警次数",
                Code = "MiddleLevelAlarmCount",
                AssetId = assetInfo.Id,
                Limit = "[0,100]",
                Value = 100,
                Weight = 60,
                ParentCode = "AlarmCount",
                IsSystem = true,
                CreatedBy = assetInfo.UpdatedBy,
                CreatedTime = DateTime.Now,
                UpdatedBy = assetInfo.UpdatedBy,
                UpdatedTime = DateTime.Now,
            });
            insertList.Add(new PanelHealth()
            {
                Name = "本柜低级告警次数",
                Code = "LowLevelAlarmCount",
                AssetId = assetInfo.Id,
                Limit = "[0,100]",
                Value = 100,
                Weight = 60,
                ParentCode = "AlarmCount",
                IsSystem = true,
                CreatedBy = assetInfo.UpdatedBy,
                CreatedTime = DateTime.Now,
                UpdatedBy = assetInfo.UpdatedBy,
                UpdatedTime = DateTime.Now,
            });
            foreach (var panelHealth in panelHealthCheckList)
            {
                var item = insertList.FirstOrDefault(p => p.Code == panelHealth.Code);
                if (item != null)
                {
                    insertList.Remove(item);
                }
            }

            if (insertList.Count > 0)
            {
                await client.Insertable(insertList).ExecuteCommandAsync();
            }
        }

        public async Task AddDeviceInputOutputList(AssetInfo assetInfo, ISqlSugarClient client)
        {
            if (assetInfo == null || assetInfo.Id <= 0 || assetInfo.AssetLevel != AssetLevel.Device || string.IsNullOrEmpty(assetInfo.AssetModel) || string.IsNullOrEmpty(assetInfo.AssetType)) return;
            if (await client.Queryable<DeviceInputOutput>().AnyAsync(d => d.AssetId == assetInfo.Id)) return;
            var server = _provider.GetRequiredService<DataPointServer>();

            var dataPoints = await server.GetDataPointInfos(AssetLevel.Device, assetInfo.AssetType, assetInfo.AssetModel);

            var dataPointlist = dataPoints.Where(d => d.GroupName == "DigitalInputOutput" || d.GroupName == "LimitMonitoring" || d.GroupName == "LogicFunction").ToArray();
            if (dataPointlist == null || dataPointlist.Length == 0) return;

            var factory = _provider.GetRequiredService<IMessageContextFactory>();
            var messageContext = factory.GetMessageContext(factory.GetDefaultLanguage());
            var list = new List<DeviceInputOutput>();

            #region 数字输入输出
            {
                var didoList = dataPointlist.Where(d => d.GroupName == "DigitalInputOutput").OrderBy(d => d.Code).ToArray();
                if (didoList.Length > 0)
                {
                    foreach (var point in didoList)
                    {
                        list.Add(new DeviceInputOutput
                        {
                            AssetId = assetInfo.Id,
                            Code = point.Code,
                            Name = messageContext.GetString($"DataPoint_{point.Code}") ?? point.Code,
                            UpdatedBy = assetInfo.UpdatedBy,
                            CreatedBy = assetInfo.CreatedBy,
                            CreatedTime = DateTime.Now,
                            UpdatedTime = DateTime.Now,
                        });
                    }
                }
            }
            #endregion

            #region 限值
            {
                var limitMonitoringList = dataPointlist.Where(d => d.GroupName == "LimitMonitoring").OrderBy(d => d.Code).ToArray();
                if (limitMonitoringList.Length > 0)
                {
                    foreach (var point in limitMonitoringList)
                    {
                        list.Add(new DeviceInputOutput
                        {
                            AssetId = assetInfo.Id,
                            Code = point.Code,
                            Name = messageContext.GetString($"DataPoint_{point.Code}") ?? point.Code,
                            UpdatedBy = assetInfo.UpdatedBy,
                            CreatedBy = assetInfo.CreatedBy,
                            CreatedTime = DateTime.Now,
                            UpdatedTime = DateTime.Now,
                        });
                    }
                }
            }
            #endregion

            #region 逻辑函数
            {
                var logicFunctionList = dataPointlist.Where(d => d.GroupName == "LogicFunction").OrderBy(d => d.Code).ToList();
                if (logicFunctionList.Count > 0)
                {
                    var loginResult = logicFunctionList.FirstOrDefault(d => d.Code == "LogicResult");
                    if (loginResult != null)
                    {
                        list.Add(new DeviceInputOutput
                        {
                            AssetId = assetInfo.Id,
                            Code = loginResult.Code,
                            Name = messageContext.GetString($"DataPoint_{loginResult.Code}") ?? loginResult.Code,
                            UpdatedBy = assetInfo.UpdatedBy,
                            CreatedBy = assetInfo.CreatedBy,
                            CreatedTime = DateTime.Now,
                            UpdatedTime = DateTime.Now,
                        });

                        logicFunctionList.Remove(loginResult);
                    }

                    foreach (var point in logicFunctionList)
                    {
                        list.Add(new DeviceInputOutput
                        {
                            AssetId = assetInfo.Id,
                            Code = point.Code,
                            Name = messageContext.GetString($"DataPoint_{point.Code}") ?? point.Code,
                            UpdatedBy = assetInfo.UpdatedBy,
                            CreatedBy = assetInfo.CreatedBy,
                            CreatedTime = DateTime.Now,
                            UpdatedTime = DateTime.Now,
                        });
                    }
                }
            }
            #endregion

            await client.Insertable(list).ExecuteCommandAsync();
        }

        public async Task<bool> VerifyAssetRelation(AssetRelation assetRelation,
            AssetRelation? parentRetaion = null,
            AssetRelation[]? children = null,
            ISqlSugarClient? client = null)
        {
            if (assetRelation.ChildId == 0)
            {
                assetRelation.ChildId = -1;
            }
            if (assetRelation.ParentId != 0)
            {
                if (parentRetaion == null)
                {
                    if (client == null) return false;
                    parentRetaion = await client.Queryable<AssetRelation>().FirstAsync(ar => ar.ChildId == assetRelation.ParentId);
                }

                if (parentRetaion == null) return false;
                if (assetRelation.AssetLevel <= parentRetaion.AssetLevel)
                    return false;
                if (assetRelation.AssetLevel == AssetLevel.Transformer&& parentRetaion.AssetLevel>= AssetLevel.Panel)
                {
                    return false;
                }
            }

            AssetLevel childLevel;
            if (children != null)
            {
                if (children.Length == 0) return true;
                childLevel = children.Max(a => a.AssetLevel);
            }
            else
            {
                if (client == null) return false;
                childLevel = await client.Queryable<AssetRelation>().Where(ar => ar.ParentId == assetRelation.ChildId).Select(ar => SqlFunc.AggregateMax(ar.AssetLevel)).FirstAsync();
            }

            if (childLevel != 0 && assetRelation.AssetLevel >= childLevel)
                return false;
            return true;
        }

        public async Task<bool> VerifyAssetInfo(AssetInfo assetInfo, List<string> errorCode, ISqlSugarClient client)
        {
            if (assetInfo == null) return false;
            CheckAssetInfoStaticRules(assetInfo, errorCode);

            if (await client.Queryable<AssetInfo>().AnyAsync(a => a.Id != assetInfo.Id && a.AssetName == assetInfo.AssetName))
            {
                errorCode.Add("Asset_AssetNameIsExists");
            }

            if (!string.IsNullOrEmpty(assetInfo.AssetNumber)
                && await client.Queryable<AssetInfo>().AnyAsync(a => a.Id != assetInfo.Id && a.AssetNumber == assetInfo.AssetNumber))
            {
                errorCode.Add("Asset_AssetNumberIsExists");
            }

            return errorCode.Count == 0;
        }

        private void CheckAssetInfoStaticRules(AssetInfo assetInfo, List<string> errorCode)
        {
            if (string.IsNullOrEmpty(assetInfo.AssetName)
                || assetInfo.AssetName.MustCharAndNotOnlyNumberOrSymbol(30))
            {
                errorCode.Add("Asset_AssetNameVerifyError");
            }

            if (assetInfo.Principal.CanNullAndNotOnlyNumberOrSymbol(30, 2)
                || Regex.IsMatch(assetInfo.Principal ?? string.Empty, "[\\$|#|!|@|\\^|\\*|\\(|\\)|\\||%|\\\\|/|<|>|;|\\[|\\]|\\{|\\}|\\?]+"))
            {
                errorCode.Add("Asset_PrincipalVerifyError");
            }

            if (assetInfo.Location.CanNullAndNotOnlySymbol(100))
            {
                errorCode.Add("Asset_LocationVerifyError");
            }

            if (assetInfo.Description.CanNullAndNotOnlyNumberOrSymbol(100))
            {
                errorCode.Add("Asset_DescriptionVerifyError");
            }

            if (assetInfo.AssetMaker.CanNullAndNotOnlyNumberOrSymbol(30))
            {
                errorCode.Add("Asset_AssetMakerVerifyError");
            }

            if (!string.IsNullOrEmpty(assetInfo.Telephone)
                && !Regex.IsMatch(assetInfo.Telephone, "^[\\d]{7,11}$"))
            {
                errorCode.Add("Asset_TelVerifyError");
            }
        }

        public bool VerifyAssetInfo(AssetInfo assetInfo, List<string> errorCode, List<AssetInfo> assetInfoes)
        {
            if (assetInfo == null) return false;
            CheckAssetInfoStaticRules(assetInfo, errorCode);
            if (assetInfoes.Any(a => a != assetInfo && a.AssetName == assetInfo.AssetName))
            {
                errorCode.Add("Asset_AssetNameIsExists");
            }

            if (!string.IsNullOrEmpty(assetInfo.AssetNumber)
                && assetInfoes.Any(a => a != assetInfo && a.AssetNumber == assetInfo.AssetNumber))
            {
                errorCode.Add("Asset_AssetNumberIsExists");
            }
            return errorCode.Count == 0;
        }

        public async Task DeleteAsset(AssetInfo assetInfo, ISqlSugarClient client, string user)
        {
            await DeleteAsset(assetInfo.Id, client, user);
        }

        public async Task DeleteAsset(int assetId, ISqlSugarClient client, string user)
        {
            var ar = await client.Queryable<AssetRelation>().ToChildListAsync(a => a.ParentId, assetId);
            var assetIds = new List<int>();
            assetIds.Add(assetId);
            foreach (var asset in ar)
            {
                assetIds.Add(asset.ChildId);
            }
            await DeleteAsset(assetIds.ToArray(), client, user);
        }


        public async Task DeleteAsset(int[] assetIds, ISqlSugarClient client, string user)
        {
            var relevances = await client.Queryable<AssetInfo>()
                .Where(a => assetIds.Contains(a.Id))
                .Select(a => new AssetRelevance()
                {
                    DrawingFileIdsStr = a.DrawingFileIdsStr,
                    ImageIdsStr = a.ImageIdsStr,
                    Topology3DId = a.Topology3DId,
                    TopologyId = a.TopologyId,
                }).ToArrayAsync();
            if (relevances.Length > 0)
            {
                var fileIds = new List<int>();
                var topologyIds = new List<int>();
                foreach (var r in relevances)
                {
                    if (!string.IsNullOrEmpty(r.ImageIdsStr))
                    {
                        try
                        {
                            var fileIdList = JsonConvert.DeserializeObject<int[]>(r.ImageIdsStr);
                            if (fileIdList != null)
                            {
                                fileIds.AddRange(fileIdList);
                            }
                        }
                        catch
                        {

                        }
                    }

                    if (!string.IsNullOrEmpty(r.DrawingFileIdsStr))
                    {
                        try
                        {
                            var fileIdList = JsonConvert.DeserializeObject<int[]>(r.DrawingFileIdsStr);
                            if (fileIdList != null)
                            {
                                fileIds.AddRange(fileIdList);
                            }
                        }
                        catch
                        {

                        }
                    }

                    if (r.TopologyId.HasValue)
                    {
                        topologyIds.Add(r.TopologyId.Value);
                    }

                    if (r.Topology3DId.HasValue)
                    {
                        topologyIds.Add(r.Topology3DId.Value);
                    }
                }

                if (fileIds.Count > 0)
                {
                    await client.Deleteable<FileManager>().Where(f => fileIds.Contains(f.Id) && !f.IsSystemFile).ExecuteCommandAsync();
                }

                if (topologyIds.Count > 0)
                {
                    var server = _provider.GetRequiredService<TopologyExtendFunc>();
                    await server.DeleteTopologyByTopologyIds(topologyIds.ToArray(), client);
                }
            }

            var thirdCodes = await client.Queryable<AssetInfo>()
                .Where(p => assetIds.Contains(p.Id) && !string.IsNullOrEmpty(p.ThirdPartCode))
                .Distinct()
                .Select(p => p.ThirdPartCode)
                .ToListAsync();

            if (thirdCodes != null && thirdCodes.Any())
            {

                //await client.Deleteable<ThirdModelConfig>().Where(p => thirdCodes.Contains(p.Code)).ExecuteCommandAsync();
                var universalDeviceConfigs = await client.Queryable<UniversalDeviceConfig>().Where(p => assetIds.Contains(p.AssetId)).ToListAsync();
                var universalDeviceConfigIds = universalDeviceConfigs.Select(p => p.Id).ToList();
                if (universalDeviceConfigIds.Any())
                {
                    await client.Deleteable<UniversalDeviceConfig>(universalDeviceConfigs).ExecuteCommandAsync();
                    await client.Deleteable<BitConfig>().Where(p => universalDeviceConfigIds.Contains(p.UniversalDeviceConfigId)).ExecuteCommandAsync();
                }
            }

            await client.Deleteable<AssetInfo>().Where(a => assetIds.Contains(a.Id)).ExecuteCommandAsync();
            await client.Deleteable<AssetRelation>().Where(a => assetIds.Contains(a.ChildId)).ExecuteCommandAsync();
            await client.Deleteable<PanelHealth>().Where(a => assetIds.Contains(a.AssetId)).ExecuteCommandAsync();
            await client.Deleteable<TemperatureMonitorInfo>().Where(a => assetIds.Contains(a.AssetId)).ExecuteCommandAsync();
            await client.Deleteable<DeviceInputOutput>().Where(a => assetIds.Contains(a.AssetId)).ExecuteCommandAsync();
            await client.Deleteable<DeviceDetails>().Where(d => assetIds.Contains(d.AssetId)).ExecuteCommandAsync();
            await client.Deleteable<BreakerProtectionSetting>().Where(p => assetIds.Contains(p.AssetId)).ExecuteCommandAsync();
            await client.Deleteable<AssetGeneralDataPoints>().Where(p => assetIds.Contains(p.AssetId)).ExecuteCommandAsync();
            await client.Deleteable<AssetMessagesConfig>().Where(p => assetIds.Contains(p.AssetId)).ExecuteCommandAsync();
            await client.Deleteable<DeviceHarmonicHistory>().Where(p => assetIds.Contains(p.AssetId)).ExecuteCommandAsync();
            await client.Deleteable<TopologyRuleInfo>().Where(p => p.AssetId.HasValue && assetIds.Contains(p.AssetId.Value)).ExecuteCommandAsync();
            await client.Deleteable<AssetMqttDataPointConfig>().Where(c => assetIds.Contains(c.AssetId)).ExecuteCommandAsync();

            #region 删除缓存
            var cache = _provider.GetRequiredService<SiemensCache>();
            foreach (var assetId in assetIds)
            {
                cache.RemoveHashData(string.Format(AssetCurrentStatusCacheKey, assetId));
                cache.Clear(string.Format("DataPoint_Device_{0}", assetId));
                #region MqttDataConfig
                {
                    cache.Clear(string.Format("AssetMqttConfig-{0}", assetId));
                    // 1s
                    cache.RemoveHashData("MqttDataConfig-1", new string[] { assetId.ToString() });
                    // 5s
                    cache.RemoveHashData("MqttDataConfig-5", new string[] { assetId.ToString() });
                    // 15s
                    cache.RemoveHashData("MqttDataConfig-15", new string[] { assetId.ToString() });
                    // 30s
                    cache.RemoveHashData("MqttDataConfig-30", new string[] { assetId.ToString() });
                    // 1min
                    cache.RemoveHashData("MqttDataConfig-60", new string[] { assetId.ToString() });
                    // 5min
                    cache.RemoveHashData("MqttDataConfig-300", new string[] { assetId.ToString() });
                    // 15min
                    cache.RemoveHashData("MqttDataConfig-900", new string[] { assetId.ToString() });
                }
                #endregion
            }
            #endregion

            #region 确认被删除资产的告警信息
            {
                int page = 1;
                int count = 100;

                while (true)
                {
                    var logHistories = new List<AlarmLogChangeLog>();

                    var alarmLogs = await client.Queryable<AlarmLog>()
                        .Where(a => a.AssetId.HasValue
                            && assetIds.Contains(a.AssetId ?? 0)
                            && (a.Status == AlarmLogStatus.New || a.Status == AlarmLogStatus.InProcess))
                        .OrderBy(a => a.Id)
                        .ToPageListAsync(page, count);

                    foreach (var alarmLog in alarmLogs)
                    {
                        alarmLog.UpdatedBy = user;
                        alarmLog.UpdatedTime = DateTime.Now;
                        alarmLog.Status = AlarmLogStatus.Finish;

                        logHistories.Add(new AlarmLogChangeLog
                        {
                            LogId = alarmLog.Id,
                            Remark = "The alarm is completed because the asset has been deleted.",
                            Source = AlarmChangeSource.Person,
                            ChangedBy = user,
                            ChangeTime = DateTime.Now,
                        });
                    }

                    try
                    {
                        await client.Ado.BeginTranAsync();
                        await client.Updateable(alarmLogs)
                            .UpdateColumns(c => new
                            {
                                c.UpdatedBy,
                                c.UpdatedTime,
                                c.Status,
                            })
                            .ExecuteCommandAsync();

                        await client.Insertable(logHistories)
                            .ExecuteCommandAsync();

                        await client.Ado.CommitTranAsync();
                    }
                    catch (Exception ex)
                    {
                        await client.Ado.RollbackTranAsync();
                        _logger.LogError(ex, "Finish alarm log error");
                        break;
                    }

                    if (alarmLogs.Count < count)
                    {
                        break;
                    }
                }
            }
            #endregion

            var refObj = _provider.GetRequiredService<IAssetManagerRef>();
            refObj.RemoveAsset(assetIds);
        }

        public async Task DeleteAllAsset(ISqlSugarClient client, string user)
        {
            var assetIds = await client.Queryable<AssetInfo>().Select(a => a.Id).ToArrayAsync();
            await DeleteAsset(assetIds, client, user);
        }

        public bool IsGeneralEquipment(string assetModel, string assetType)
        {
            return "Other".Equals(assetModel, StringComparison.OrdinalIgnoreCase)
                        || "GeneralDevice".Equals(assetType, StringComparison.OrdinalIgnoreCase)
                        || ("Modbus".Equals(assetModel, StringComparison.OrdinalIgnoreCase)
                        && "Gateway".Equals(assetType, StringComparison.OrdinalIgnoreCase));
        }
    }
}
